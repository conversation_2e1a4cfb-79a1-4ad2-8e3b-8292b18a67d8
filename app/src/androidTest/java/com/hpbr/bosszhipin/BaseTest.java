package com.hpbr.bosszhipin;

import androidx.test.espresso.action.ViewActions;

import org.junit.After;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;

//import android.support.test.uiautomator.UiDevice;
//import android.support.test.uiautomator.UiObject;
//import android.support.test.uiautomator.UiSelector;

/**
 * Created by yuchao<PERSON><PERSON> on 16/11/7.
 */

public class BaseTest {
    private static final String APP_NAME = "Boss直聘";

//    private UiDevice mUiDevice;

    public void setUp() throws Exception{
//        mUiDevice = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation());
    }
//
//    public UiDevice getUiDevice() {
//        return mUiDevice;
//    }

    /**
     * 切换到桌面
     */
    protected void goLauncher(){
//        mUiDevice.pressHome();
        waitSecond(2);
    }

    /**
     * 启动App
     */
    protected void startApp() throws Exception {
//        UiObject app = mUiDevice.findObject(new UiSelector().text(APP_NAME));
//        app.clickAndWaitForNewWindow();
    }

    /**
     * 登出
     */
    protected boolean logout(){
        boolean ret = false;
        try {
            onView(withId(R.id.ll_tab_4)).perform(click());
            onView(withId(R.id.iv_general_settings)).perform(click());
            onView(withId(R.id.tv_logout)).perform(click());
            onView(withText("确定")).perform(click());
            ret=true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * 登陆
     * @return
     */
    protected boolean login(){
        boolean ret = false;
        try {
            onView(withId(R.id.tv_password_login)).perform(click());
            onView(withId(R.id.et_phone)).perform(ViewActions.clearText());
            onView(withId(R.id.et_phone)).perform(typeText("14000001001"), closeSoftKeyboard());
            onView(withId(R.id.et_password)).perform(ViewActions.clearText());
            onView(withId(R.id.et_password)).perform(typeText("111111"), closeSoftKeyboard());
            onView(withId(R.id.tv_login)).perform(click());
            ret=true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }

    @After
    public void tearDown() throws Exception {
//        super.tearDown();
    }

    /**
     * 等待
     * @param second
     */
    protected void waitSecond(int second){
        try {
            int millis = second * 1000;
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
