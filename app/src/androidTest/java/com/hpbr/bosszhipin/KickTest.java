package com.hpbr.bosszhipin;

import androidx.test.rule.ActivityTestRule;

import com.hpbr.bosszhipin.module.main.activity.MainActivity;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/7.
 * 踢人测试，需要两台手机一起
 */

public class KickTest extends BaseTest{
    @Rule
    public ActivityTestRule mActivityRule = new ActivityTestRule<>(
            MainActivity.class);

    @Before
    @Override
    public void setUp() throws Exception{
        super.setUp();
    }

    @Test
    public void kickTest(){
        logout();
        waitSecond(2);
        for (int i = 0; i < 20; i++) {
            login();
            waitSecond(10);
        }
    }
}
