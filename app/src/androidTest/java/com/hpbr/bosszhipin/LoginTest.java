package com.hpbr.bosszhipin;

import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.hpbr.bosszhipin.module.login.activity.GetStartedActivity;
import com.hpbr.bosszhipin.module.login.activity.GetStartedActivity2;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/11/7.
 */
@RunWith(AndroidJUnit4.class)
public class LoginTest extends BaseTest{

    @Rule
    public ActivityTestRule mActivityRule = new ActivityTestRule<>(
            GetStartedActivity2.class);

    @Before
    @Override
    public void setUp() throws Exception{
        super.setUp();
    }

    @Test
    public void testLogin() throws Exception {
        login();
        waitSecond(2000);
    }


}
