package com.hpbr.bosszhipin;

import androidx.test.espresso.UiController;
import androidx.test.espresso.ViewAction;
import androidx.test.espresso.action.AdapterViewProtocol;
import androidx.test.espresso.core.deps.guava.base.Optional;
import androidx.test.espresso.matcher.BoundedMatcher;
import androidx.test.filters.LargeTest;
import androidx.test.filters.RequiresDevice;
import androidx.test.internal.util.Checks;
import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import android.util.Log;
import android.view.View;
import android.widget.Adapter;
import android.widget.AdapterView;
import android.widget.ExpandableListView;
import android.widget.ListView;
import android.widget.TextView;

import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.views.MTextView;

import org.hamcrest.Description;
import org.hamcrest.Matcher;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.LinkedList;

import static androidx.test.espresso.Espresso.onData;
import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.AdapterViewProtocols.standardProtocol;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.matcher.ViewMatchers.isAssignableFrom;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;
import static org.hamcrest.Matchers.allOf;
import static org.hamcrest.Matchers.anything;
import static org.hamcrest.Matchers.instanceOf;


/**
 * Created by yuchaofei on 16/10/27.
 */
@RunWith(AndroidJUnit4.class)
@LargeTest
public class MMSTest {

    @Rule
    public ActivityTestRule<MainActivity> mActivityRule = new ActivityTestRule<>(
            MainActivity.class);

    @Test
    @RequiresDevice
    public void myTest() {

        for (int i = 0; i < 2000; i++) {
            try {
                Thread.sleep(10000);
                contacts();
                position();
            } catch (Throwable throwable) {
                throwable.printStackTrace();
                String cause = throwable.getMessage();
                System.out.println(cause);
            }
        }
        try {
            position();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    public void contacts() throws Throwable {

        onView(withId(R.id.ll_tab_3)).perform(click());//点击消息tab
//        onView(withId(R.id.tvTitle)).perform(typeText(STRING_TO_BE_TYPED), closeSoftKeyboard()); //line 1
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }

        onView(withText("聊天")).perform(click()); //点击联系人tab

        Thread.sleep(2000);

        for (int i = 0; i < 2000; i++) {
            sendMessage(i);//发送消息
        }
        Thread.sleep(2000);

//        onView(isRoot()).perform(ViewActions.pressBack());
//
//        onView(isRoot()).perform(ViewActions.pressKey(KeyEvent.KEYCODE_HOME));
////        onView(withText("聊天")).perform(pressKey(KeyEvent.KEYCODE_HOME));
////        mActivityRule.getActivity().onKeyDown(KeyEvent.KEYCODE_HOME, new KeyEvent(KeyEvent.ACTION_DOWN,KeyEvent.KEYCODE_HOME));
//
//        Thread.sleep(10000);
//
//        startApp();
//        String expectedText = "Hello, " + STRING_TO_BE_TYPED + "!";
//        for (int i = 0; i < 1000; i++) {
//            onView(withText(R.string.btn_text)).perform(click());
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        }
//        onView(withId(R.id.btn_test)).check(matches(withText(expectedText))); //line 3
    }

    private void sendMessage(int index) throws Throwable {
//        DataInteraction dataInteraction = onData(hasToString(containsString(String.format("%s", ""))));
//        dataInteraction = dataInteraction.inAdapterView(allOf(withId(R.id.contact_tab_list),isDescendantOfA(Matchers.<View>instanceOf(ExpandableListView.class))));
//        dataInteraction.perform(click());
//        onView(allOf(withId(R.id.tv_name),isDescendantOfA(allOf(withId(R.id.contact_tab_list),withText("xyz"))))).perform(click());
//
//        onData(anything())
//                .inAdapterView(Matchers.<View>allOf(instanceOf(ExpandableListView.class)))
//                .atPosition(0).perform(click());
//        Matcher<View> ancestorMatcher = isDescendantOfA(allOf(withId(R.id.contact_tab_list), instanceOf(ExpandableListView.class)));
//        DataInteraction dataInteraction = onData(anything()).inAdapterView(ancestorMatcher);
//        dataInteraction.perform(click());
        onData(anything()).inAdapterView(allOf(withId(R.id.contact_tab_list), instanceOf(ExpandableListView.class)))
                .atPosition(1).perform(click()); //点击listview第二行

//        onView(allOf(withId(R.id.contact_tab_list), instanceOf(ExpandableListView.class))).perform(click());
//        onData(allOf(is(instanceOf(Map.class)),hasEntry(equalTo("周小游"), is(str))));
//        onView(withText("周小游")).perform(click());
//        onView(withText("李大嘴")).perform(click());


//        Thread.sleep(1000);

        for (int i = 0; i < 10; i++) {
            onView(withId(R.id.et_content)).perform(typeText(index + ":" + i), closeSoftKeyboard());//输入消息
            onView(withId(R.id.tv_send)).perform(click());//发送

            try {
                onData(anything()).inAdapterView(allOf(withId(R.id.lv_chat), instanceOf(ListView.class))).atPosition(0)
                        .usingAdapterViewProtocol(new ReverseProdocol())
                        .onChildView(withId(R.id.tv_content_status)).perform(new ViewAction() {
                    @Override
                    public Matcher<View> getConstraints() {
                        return isAssignableFrom(MTextView.class);
                    }

                    @Override
                    public String getDescription() {
                        return "text";
                    }

                    @Override
                    public void perform(UiController uiController, View view) {
                        TextView textView = (TextView) view;
                        String text = textView.getText().toString();
                        if ("失败".equals(text)) {
                            Log.e(text, "perform() called with: text = [" + text + "]");//判断发送状态
                        }
                    }
                });
            } catch (Exception e) {
            }

//            Thread.sleep(2000);
//            onData(allOf(is(new BoundedMatcher<Object, Person>(Person.class) {
//                @Override
//                public void describeTo(Description description) {
//                }
//                @Override
//                protected boolean matchesSafely(Person obj) {
//        /* TODO Implement comparison logic */
//                    return false;
//                }
//            }))).inAdapterView(withId(R.id.lv_chat)).atPosition(10).perform(click());
//            onView(withId(R.id.tv_content_status))
//                    .check(matches(withText("失败")));

        }

//        onView(withId(R.id.tv_exchange_phone)).perform(click());
//        Thread.sleep(1000);
//
//        onView(withId(R.id.tv_exchange_wechat)).perform(click());
//        Thread.sleep(1000);

        onView(withId(R.id.iv_back)).perform(click());

//        Thread.sleep(1000);
//        ExpandableListView list = (ExpandableListView) withId(R.id.contact_tab_list).;
//        list.smoothScrollToPosition(0);
////        onView(withId(R.id.contact_tab_list)).perform(swipeDown());
////        onView(withId(R.id.contact_tab_list)).perform(swipeDown());
////        onView(withId(R.id.contact_tab_list)).perform(swipeDown());
//        Thread.sleep(1000);
//        list.smoothScrollToPosition(0);
//        onView(withId(R.id.contact_tab_list)).perform(ViewActions.scrollTo());
//        pressKey(KeyEvent.KEYCODE_HOME);

    }

    public static String getText(final Matcher<View> matcher) {
        final String[] text = {null};
        onView(matcher).perform(new ViewAction() {
            //识别所操作的对象类型
            @Override
            public Matcher<View> getConstraints() {
                return isAssignableFrom(TextView.class);
            }

            //视图操作的一个描述
            @Override
            public String getDescription() {
                return "getting text from a TextView";
            }

            //实际的一个操作，在之类我们就可以获取到操作的对象了。
            @Override
            public void perform(UiController uiController, View view) {
                TextView textView = (TextView) view;
                text[0] = textView.getText().toString();
            }
        });
        return text[0];
    }


    public static Matcher<View> stateWithText(final Matcher<String> stringMatcher) {
        return new BoundedMatcher<View, TextView>(TextView.class) {
            @Override
            public void describeTo(Description description) {
                description.appendText("with text: ");
                stringMatcher.describeTo(description);
            }

            @Override
            public boolean matchesSafely(TextView textView) {
                return stringMatcher.matches(textView.getText().toString());
            }
        };
    }

    public static Matcher<Object> withYourListItemText(final Matcher<String> yourListItemText) {
        Checks.checkNotNull(yourListItemText);
        return new BoundedMatcher<Object, MTextView>(MTextView.class) {
            @Override
            public boolean matchesSafely(MTextView item) {
                return yourListItemText.matches(item.getText());
            }

            @Override
            public void describeTo(Description description) {
                description.appendText("with text: " + yourListItemText.toString());
                yourListItemText.describeTo(description);
            }
        };
    }


    public void position() throws Throwable {
        onView(withId(R.id.ll_tab_1)).perform(click());
        Thread.sleep(2000);
        onView(withId(R.id.ll_tab_4)).perform(click());
        Thread.sleep(2000);
        onView(withId(R.id.ll_tab_2)).perform(click());
        Thread.sleep(2000);
    }

    private void startApp() {
        System.out.println("====startApp====");
        mActivityRule.launchActivity(null);
        System.out.println("====end startApp====");
    }

    private static class ReverseProdocol implements AdapterViewProtocol {
        private final AdapterViewProtocol delegate = standardProtocol();

        @Override
        public Iterable<AdaptedData> getDataInAdapterView(
                AdapterView<? extends Adapter> adapterView) {
            LinkedList<AdaptedData> result = new LinkedList<>();
            for (AdaptedData data : delegate.getDataInAdapterView(adapterView)) {
                result.addFirst(data);
            }
            return result;
        }

        @Override
        public Optional<AdaptedData> getDataRenderedByView(
                AdapterView<? extends Adapter> adapterView, View view) {
            return delegate.getDataRenderedByView(adapterView, view);
        }

        @Override
        public void makeDataRenderedWithinAdapterView(AdapterView<? extends Adapter> adapterView, AdaptedData data) {

        }

        @Override
        public boolean isDataRenderedWithinAdapterView(AdapterView<? extends Adapter> adapterView, AdaptedData adaptedData) {
            return true;
        }

        // Similarly delegate to the other two methods
        // ...
    }

}
