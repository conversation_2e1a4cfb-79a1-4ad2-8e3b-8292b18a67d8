package com.hpbr.bosszhipin;

import androidx.test.espresso.action.ViewActions;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.matcher.ViewMatchers.withId;

/**
 * Created by y<PERSON><PERSON><PERSON><PERSON> on 16/12/8.
 */
@RunWith(AndroidJUnit4.class)
public class MessageChecker extends BaseTest{

    @Test
    public void messageCheck(){

    }

    private void sendMessage(){
        logout();
        onView(withId(R.id.et_phone)).perform(ViewActions.clearText());
        onView(withId(R.id.tv_send_code)).perform(typeText("17020000669"), closeSoftKeyboard());
        onView(withId(R.id.tv_send_code)).perform(click());
        waitSecond(60);

    }

    private void readMessage(){

    }
}
