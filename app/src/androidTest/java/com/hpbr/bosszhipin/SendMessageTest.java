package com.hpbr.bosszhipin;

import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;

import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.clearText;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.action.ViewActions.closeSoftKeyboard;
import static androidx.test.espresso.action.ViewActions.typeText;
import static androidx.test.espresso.matcher.ViewMatchers.withId;
import static androidx.test.espresso.matcher.ViewMatchers.withText;

/**
 * Created by monch on 2017/6/12.
 */
@RunWith(AndroidJUnit4.class)
public class SendMessageTest {

    public ActivityTestRule mActivityRule =
            new ActivityTestRule(WelcomeActivity.class);

    @Test
    public void testSendMessage() {
        waitSecond(5);
        clickText("消息");
        waitSecond(3);
        clickText("陈磊");
        waitSecond(3);
        int index = 0;
        while (true) {
            onView(withId(R.id.et_content)).perform(click());
            waitSecond(0.5f);
            onView(withId(R.id.et_content)).perform(clearText());
            waitSecond(0.5f);
            onView(withId(R.id.et_content)).perform(typeText("test" + index++), closeSoftKeyboard());
            waitSecond(0.5f);
            onView(withId(R.id.tv_send)).perform(click());
            waitSecond(1);
        }
    }

    private void clickText(String text) {
        onView(withText(text)).perform(click());
    }

    private void waitSecond(float second){
        try {
            float millis = second * 1000;
            Thread.sleep((int)millis);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
