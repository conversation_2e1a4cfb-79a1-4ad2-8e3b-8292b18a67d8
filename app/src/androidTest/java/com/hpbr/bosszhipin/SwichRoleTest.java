package com.hpbr.bosszhipin;

import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.hpbr.bosszhipin.module.main.activity.MainActivity;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import static androidx.test.espresso.Espresso.onView;
import static androidx.test.espresso.action.ViewActions.click;
import static androidx.test.espresso.matcher.ViewMatchers.withId;

/**
 * Created by y<PERSON><PERSON><PERSON><PERSON> on 16/11/7.
 */
@RunWith(AndroidJUnit4.class)
public class SwichRoleTest extends BaseTest {
    @Rule
    public ActivityTestRule mActivityRule = new ActivityTestRule<>(
            MainActivity.class);

    @Before
    @Override
    public void setUp() throws Exception{
        super.setUp();
    }

    @Test
    public void swichRoleTest() {
        logout();
        login();
        for (int i = 0; i < 100; i++) {
            if (i % 2 == 0) {
                swichBoss();
            } else {
                swichGeek();
            }
        }
    }

    public void goSwich() {
        try {
            onView(withId(R.id.ll_tab_4)).perform(click());
            onView(withId(R.id.iv_general_settings)).perform(click());
            onView(withId(R.id.rl_change_identity)).perform(click());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void swichBoss() {
        goSwich();
//        onView(withId( R.id.ll_binc_boss)).perform(click());

    }

    public void swichGeek() {
        goSwich();
//        onView(withId( R.id.ll_binc_geek)).perform(click());
    }
}
