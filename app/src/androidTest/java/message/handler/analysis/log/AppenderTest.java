package message.handler.analysis.log;

import androidx.test.rule.ActivityTestRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.hpbr.bosszhipin.BaseTest;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.twl.mms.utils.ThreadManager;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/25.
 */
@RunWith(AndroidJUnit4.class)
public class AppenderTest extends BaseTest{
    private static final String TAG = "AppenderTest";

    @Rule
    public ActivityTestRule mActivityRule = new ActivityTestRule<>(
            MainActivity.class);

    @Before
    @Override
    public void setUp() throws Exception{
        super.setUp();
    }

    @Test
    public void testNext(){
//        String str = FileUtils.read(new File("/sdcard/techwolf/report/report_2018-01-26.info"));
//        Log.e(TAG, "testNext: " + str);
        for (int i = 0; i < 1000; i++) {
            ThreadManager.getSubThreadHandler().postDelayed(Appender.getInstance(), 0);
        }
        for (int i = 0; i < 5; i++) {
            new Thread(){
                @Override
                public void run() {
                    for (int i = 0; i < 10000; i++) {
                        long id = System.currentTimeMillis();
                        Appender.getInstance().log2File("s|" + id + "|" + i+"\n");
                        Appender.getInstance().log2File("r|" + id + "|" + i + "\n");
                    }
                }
            }.start();
        }


        try {
            Thread.sleep(40000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
