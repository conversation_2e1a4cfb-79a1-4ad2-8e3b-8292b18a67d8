{"bossFilterConfig": [{"code": 200, "name": "学历要求", "tip": "", "paramName": "degree", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 209, "name": "初中及以下", "tip": ""}, {"code": 208, "name": "中专/中技", "tip": ""}, {"code": 206, "name": "高中", "tip": ""}, {"code": 202, "name": "大专", "tip": ""}, {"code": 203, "name": "本科", "tip": ""}, {"code": 204, "name": "硕士", "tip": ""}, {"code": 205, "name": "博士", "tip": ""}]}, {"code": 400, "name": "薪资待遇", "tip": "", "paramName": "salary", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 402, "name": "3K以下", "tip": ""}, {"code": 403, "name": "3-5K", "tip": ""}, {"code": 404, "name": "5-10K", "tip": ""}, {"code": 405, "name": "10-20K", "tip": ""}, {"code": 406, "name": "20-50K", "tip": ""}, {"code": 407, "name": "50K以上", "tip": ""}]}, {"code": 100, "name": "经验要求", "tip": "", "paramName": "experience", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 109, "name": "在校/应届", "tip": ""}, {"code": 103, "name": "1年以内", "tip": ""}, {"code": 104, "name": "1-3年", "tip": ""}, {"code": 105, "name": "3-5年", "tip": ""}, {"code": 106, "name": "5-10年", "tip": ""}, {"code": 107, "name": "10年以上", "tip": ""}]}, {"code": 700, "name": "求职状态", "tip": "", "paramName": "intention", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 701, "name": "离职-随时到岗", "tip": ""}, {"code": 702, "name": "在职-暂不考虑", "tip": ""}, {"code": 703, "name": "在职-考虑机会", "tip": ""}, {"code": 704, "name": "在职-月内到岗", "tip": ""}]}, {"code": 1100, "name": "院校", "tip": "", "paramName": "school", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 1104, "name": "985", "tip": ""}, {"code": 1103, "name": "211", "tip": ""}, {"code": 1102, "name": "双一流院校", "tip": "首批双一流建设高校共计137所"}, {"code": 1105, "name": "留学", "tip": "有出国留学或在港澳台地区学习经历"}, {"code": 1106, "name": "国内外名校", "tip": "QS排名前500的院校"}]}, {"code": 1200, "name": "跳槽频率", "tip": "", "paramName": "switchJobFrequency", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 1201, "name": "5年少于3份", "tip": "根据在线简历填写内容筛选"}, {"code": 1202, "name": "平均每份工作大于1年", "tip": "在线简历中平均在职大于1年"}]}, {"code": 1300, "name": "是否与同事交换简历", "tip": "", "paramName": "exchangeResumeWithColleague", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": ""}, {"code": 1301, "name": "近一个月没有", "tip": "未与公司同事交换附件简历"}]}, {"code": 1400, "name": "名企", "tip": "", "paramName": "bigCompany", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": ""}, {"code": 1401, "name": "名企优先", "tip": "名企根据平台内用户，对企业的搜索/关注等行为定义"}]}, {"code": 1500, "name": "求职意向", "tip": "", "paramName": "intentionStrength", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": ""}, {"code": 1501, "name": "强", "tip": ""}, {"code": 1502, "name": "较强", "tip": ""}, {"code": 1503, "name": "一般", "tip": ""}]}, {"code": 2200, "name": "性别", "tip": "", "paramName": "gender", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": ""}, {"code": 2201, "name": "男", "tip": ""}, {"code": 2202, "name": "女", "tip": ""}]}, {"code": 2300, "name": "近期没有看过", "tip": "", "paramName": "recentNotView", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": ""}, {"code": 2301, "name": "近14天没有", "tip": "近14天您未浏览在线简历的牛人"}]}, {"code": 2400, "name": "可拨打电话", "tip": "", "paramName": "callPhone", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 2401, "name": "可拨打", "tip": ""}]}, {"code": 2500, "name": "活跃度", "tip": "", "paramName": "activation", "subFilterConfigModel": [{"code": 0, "name": "全部", "tip": ""}, {"code": 2501, "name": "刚刚活跃", "tip": "刚刚在平台活跃的牛人"}, {"code": 2502, "name": "今日活跃", "tip": "今日在平台活跃的牛人"}, {"code": 2503, "name": "3日内活跃", "tip": "三日内在平台活跃过的牛人"}, {"code": 2504, "name": "本周活跃", "tip": "本周在平台活跃过的牛人"}, {"code": 2505, "name": "本月活跃", "tip": "本月在平台活跃过的牛人"}]}, {"code": 5000, "name": "资格证书", "tip": "多选时牛人拥有任一证书既符合", "paramName": "certificate", "subFilterConfigModel": [{"code": 0, "name": "不限", "tip": "多选时牛人拥有任一证书既符合"}, {"code": 5001, "name": "健身教练国家职业资格证", "tip": "多选时牛人拥有任一证书既符合"}, {"code": 5002, "name": "IFBB证书", "tip": "多选时牛人拥有任一证书既符合"}, {"code": 5003, "name": "CBBA证书", "tip": "多选时牛人拥有任一证书既符合"}]}], "dataVersion": 1.0}