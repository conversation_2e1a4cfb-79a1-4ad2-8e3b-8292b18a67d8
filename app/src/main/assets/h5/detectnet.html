<!DOCTYPE html>

<head>
    <title>网络检测</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport"
          content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <style>
        body, div, img, p { margin:0; padding:0}
        body {
            background-color:#fff;
            padding:10px
        }
        p { border-bottom: 1px dotted #ddd;padding:10px 0}
        .green{ color:green}
        .red {color:red}
        @media (prefers-color-scheme: dark) and (max-device-width: 1024px) {
            body {
                background: #222;
                color: #e5e5e5;
            }
            p {border-bottom: 1px dotted #666;}
        }
    </style>
</head>

<body>
<p><strong>手机时间：</strong><span id="time">正在检测中...</span></p>
<p><strong>IP 地 址：</strong><span id="ip">正在检测中...</span></p>
<p><strong>网络状态：</strong><span id="net">正在检测中...</span></p>
<p><strong>搜索状态：</strong><span id="bd">正在检测中...</span></p>
<p><strong>服务状态：</strong><span id="ali">正在检测中...</span></p>
<p><strong>主站状态：</strong><span id="zp">正在检测中...</span></p>
<p><strong>CDN状态：</strong><span id="cdn">正在检测中...</span></p>
<p><strong>下载速度：</strong><span id="speed">正在检测中...</span></p>
<script type="text/javascript">
    const matchFn = e => {
<!--            if (e.matches) {-->
<!--                onlyHeadBgColor ? window.wst.modifyNavbgColor(C2) : window.wst.modifyNavbgColor(C2, C4, C6);-->
<!--            } else {-->
<!--                onlyHeadBgColor ? window.wst.modifyNavbgColor(C1) : window.wst.modifyNavbgColor(C1, C3, C5);-->
<!--            }-->
    };
    matchFn(window.matchMedia('(prefers-color-scheme: dark)'));
    var visibleCallBack = () => {
        matchFn(window.matchMedia('(prefers-color-scheme: dark)'));
    };
    // ios控制中心快捷切换暗黑模式
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', matchFn);
    // 页面从后台回到前台
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') { // visible , hidden , prerender
            setTimeout(() => {
                visibleCallBack();
            }, 50);
        }
    });
    var detectData = {
        time:'',
        ip:'',
        isOnline: false, // 0 未连接网络， 1 已连接网络
        netType:'',
        netSpeed: 0,
        netSpeedK: 0,
        netRtt:0,
        searchState: false,
        serviceState: false,
        mainSiteState: false,
        CDNState: false,
        downLoadSpeed: 0,
    };
    function addZero(num) {
        if (num.toString().length == 1) {
            return num < 10 ? '0' + num : num;
        }
        return num;
    }
    // Object.assign(detectData, data);
    // 本地时间
    function detectTime() {
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = addZero(currentDate.getMonth() + 1); // 月份从0开始要加1
        var day = addZero(currentDate.getDate());
        var hour = addZero(currentDate.getHours());
        var minute = addZero(currentDate.getMinutes());
        var second = addZero(currentDate.getSeconds());
        var time = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
        const ele = document.getElementById("time");
        detectData.time = time;
        ele.innerHTML = time;
    }

    // IP地址
    function detectIPAddress() {
        const ele = document.getElementById("ip");
        var apiUrl = 'https://api.ipify.org?format=json';
        fetch(apiUrl).then(response => response.json()).then((data) => {ele.innerHTML = data.ip;detectData.ip = data.ip;} ).catch(error => ele.innerHTML = error);
    }

    // 检查网络连接状态
    function detectConnectStatus() {
        if (typeof navigator == 'object') {
            var str = '';
            var isOnline = navigator.onLine; // false时，你可以认为它是离线的，但你不能认为true一定意味着浏览器可以上网
            str += isOnline ? '<b class="green">已连接</b>' : '<b class="red">未连接</b>';
            detectData.isOnline = isOnline;
            if (navigator.connection) {
                console.log(navigator.connection)
                const { downlink, effectiveType, rtt, saveData } = navigator.connection;
                var netType = effectiveType;
                var netSpeed = downlink;
                var netSpeedK = downlink * 1024 / 8;
                var netRtt = rtt;
                str += '<br>网络类型：' + netType + '<br>网络速度：' + netSpeed + ' M/s 或 ' + netSpeedK  + ' KB/s<br>网络延迟：' + netRtt +' ms';
                detectData.netType = netType;
                detectData.netSpeed = netSpeed;
                detectData.netSpeedK = netSpeedK;
                detectData.netRtt = netRtt;
            }
            const ele = document.getElementById("net");
            ele.innerHTML = str;
        } else {
            return detectData;
        }
    }

    // 搜索
    function detectBDNet() {
        var img = new Image();
        img.onload = function () {
            const ele = document.getElementById("bd");
            ele.innerHTML = '<b class="green">已通过 ✅</b>';
            detectData.searchState = true;
        };
        img.onerror = function () {
            const ele = document.getElementById("bd");
            ele.innerHTML = '<b class="red">未通过</b>';
            detectData.searchState = false;
        };
        img.src = 'https://www.baidu.com/favicon.ico?_t=' + (+new Date());
    }

    // 服务
    function detectALYNet() {
        var img = new Image();
        img.onload = function () {
            const ele = document.getElementById("ali");
            ele.innerHTML = '<b class="green">已通过 ✅</b>';
            detectData.serviceState = true;
        };
        img.onerror = function () {
            const ele = document.getElementById("ali");
            ele.innerHTML = '<b class="red">未通过</b>';
            detectData.serviceState = false;
        };
        img.src = 'https://www.aliyun.com/favicon.ico?_t=' + (+new Date());
    }

    // boss主站
    function detectZPNet() {
        var img = new Image();
        img.onload = function () {
            const ele = document.getElementById("zp");
            ele.innerHTML = '<b class="green">已通过 ✅</b>';
            detectData.mainSiteState = true;
        };
        img.onerror = function () {
            const ele = document.getElementById("zp");
            ele.innerHTML = '<b class="red">未通过</b>';
            detectData.mainSiteState = false;
        };
        img.src = 'https://www.zhipin.com/favicon.ico?_t=' + (+new Date());
    }

    // CDN
    function detectCDNNet() {
        var img = new Image();
        img.onload = function () {
            const ele = document.getElementById("cdn");
            ele.innerHTML = '<b class="green">已通过 ✅</b>';
            detectData.CDNState = true;
        };
        img.onerror = function () {
            const ele = document.getElementById("cdn");
            ele.innerHTML = '<b class="red">未通过</b>';
            detectData.CDNState = false;
        };
        img.src = 'https://img.bosszhipin.com/boss/avatar/avatar_1.png?_t=' + (+new Date());
    }

    //网速
    // function detectSpeed() {
    //     var img = new Image(),
    //         startTime;
    //     img.onload = function () {
    //         var endTime = +new Date(),
    //             period = (endTime - startTime) / 1000,
    //             speed = 748 / period;
    //         const ele = document.getElementById("speed");
    //         ele.innerHTML = speed.toFixed(2) + 'KB/s';
    //     };
    //     img.onerror = function () {
    //         const ele = document.getElementById("speed");
    //         ele.innerHTML = '<b class="red">未通过</b>';
    //     };
    //     startTime = +new Date();
    //     img.src = 'https://static.zhipin.com/v2/web/geek/images/logo.png?_t=' + (+new Date());

    // }

    function measureBW(fn, time) {
        time = time || 1;
        var startTime, endTime, fileSize;
        var count = time;
        function measureBWSimple () {
            var xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () => {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    if(!fileSize){
                        fileSize = xhr.responseText.length;
                    }
                    count --;
                    if(count <= 0){
                        endTime = Date.now();
                        var speed = fileSize * time  / ((endTime - startTime)/1000) / 1024;
                        fn && fn(Math.floor(speed));
                    }
                }
            }
            // xhr.open("GET", "https://static.zhipin.com/v2/web/geek/images/logo.png?_t=" + Math.random(), true);
            xhr.open("GET", "https://z.zhipin.com/H5/images/activity/poster_propstoc.png?_t=" + Math.random(), true);
            xhr.send();
        }
        startTime = Date.now();
        for(var x = time; x > 0; x--) {
            measureBWSimple()
        }
    }

    // function reportDetectData(reportedData) {
    //     const domain = 'https://apm-fe.zhipin.com/wapi/zpApm/actionLog/fe/common.json';
    //     fetch(domain, {
    //             method: 'POST',
    //             headers: { 'Content-Type': 'application/json;charset=UTF-8', 'report-source':'detectNetworkStates' },
    //             mode: 'cors',
    //             credentials: 'include',
    //             type: 'report-data',
    //             body: JSON.stringify(reportedData),
    //             // keepalive: true, // chromium 70的bug，暂时不开启
    //         })
    //         .then( response => response.json())
    //         .then(responseData => {
    //             if (typeof callback == 'function') {
    //                 responseData.code === 0 && callback();
    //             }
    //         })
    //         .catch(err => console.log('Request Failed', err))
    // }
    var reportedData = {
        identity: 1,
        clientInfo: {
            model: navigator.userAgent,
        },
        items: {},
    };
    window.onload = function () {
        detectConnectStatus();
        detectTime();
        detectIPAddress();
        detectBDNet();
        detectALYNet();
        detectZPNet();
        detectCDNNet();
        // detectSpeed();
        if (detectData.isOnline) {
            measureBW((speed) => {
                console.log(speed + " KB/sec");  //913 KB/sec
                const ele = document.getElementById("speed");
                ele.innerHTML = speed + " KB/sec";
                detectData.downLoadSpeed = speed;
                reportedData.items = detectData;
                console.log('detectData', detectData);
                // reportDetectData(reportedData);
            }, 10);
        } else {
            const ele = document.getElementById("speed");
            ele.innerHTML = 0 + " KB/sec";
        }
    };

</script>
</body>

</html>
