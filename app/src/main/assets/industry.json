{"dataVersion": 2.0, "industryConfig2": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100020, "positionType": 0, "name": "互联网", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100001, "positionType": 0, "name": "电子商务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100021, "positionType": 0, "name": "计算机软件", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100007, "positionType": 0, "name": "生活服务(O2O)", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100015, "positionType": 0, "name": "企业服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100006, "positionType": 0, "name": "医疗健康", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100002, "positionType": 0, "name": "游戏", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100003, "positionType": 0, "name": "社交网络与媒体", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100028, "positionType": 0, "name": "人工智能", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100029, "positionType": 0, "name": "云计算", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100012, "positionType": 0, "name": "在线教育", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100023, "positionType": 0, "name": "计算机服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100005, "positionType": 0, "name": "大数据", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100004, "positionType": 0, "name": "广告营销", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100030, "positionType": 0, "name": "物联网", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100017, "positionType": 0, "name": "新零售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100016, "positionType": 0, "name": "信息安全", "rank": 0, "mark": 0}], "positionType": 0, "name": "互联网/AI", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101400, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101405, "positionType": 0, "name": "半导体/芯片", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101406, "positionType": 0, "name": "电子/硬件开发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101402, "positionType": 0, "name": "通信/网络设备", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101401, "positionType": 0, "name": "智能硬件/消费电子", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101403, "positionType": 0, "name": "运营商/增值服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101404, "positionType": 0, "name": "计算机硬件", "rank": 0, "mark": 0}], "positionType": 0, "name": "电子/通信/半导体", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101100, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101101, "positionType": 0, "name": "餐饮", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101111, "positionType": 0, "name": "美容", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101112, "positionType": 0, "name": "美发", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101102, "positionType": 0, "name": "酒店/民宿", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101107, "positionType": 0, "name": "休闲/娱乐", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101113, "positionType": 0, "name": "运动/健身", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101114, "positionType": 0, "name": "保健/养生", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101109, "positionType": 0, "name": "家政服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101103, "positionType": 0, "name": "旅游/景区", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101105, "positionType": 0, "name": "婚庆/摄影", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101110, "positionType": 0, "name": "宠物服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101108, "positionType": 0, "name": "回收/维修", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101106, "positionType": 0, "name": "其他生活服务", "rank": 0, "mark": 0}], "positionType": 0, "name": "服务业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101000, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101011, "positionType": 0, "name": "批发/零售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101012, "positionType": 0, "name": "进出口贸易", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101001, "positionType": 0, "name": "食品/饮料/烟酒", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101003, "positionType": 0, "name": "服装/纺织", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101009, "positionType": 0, "name": "家具/家居", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101010, "positionType": 0, "name": "家用电器", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101002, "positionType": 0, "name": "日化", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101006, "positionType": 0, "name": "珠宝/首饰", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101013, "positionType": 0, "name": "其他消费品", "rank": 0, "mark": 0}], "positionType": 0, "name": "消费品/批发/零售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100700, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100704, "positionType": 0, "name": "装修装饰", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100708, "positionType": 0, "name": "房屋建筑工程", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100709, "positionType": 0, "name": "土木工程", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100710, "positionType": 0, "name": "机电工程", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100707, "positionType": 0, "name": "物业管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100706, "positionType": 0, "name": "房地产中介/租赁", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100705, "positionType": 0, "name": "建筑材料", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100701, "positionType": 0, "name": "房地产开发经营", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100703, "positionType": 0, "name": "建筑设计", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100711, "positionType": 0, "name": "建筑工程咨询服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100712, "positionType": 0, "name": "土地与公共设施管理", "rank": 0, "mark": 0}], "positionType": 0, "name": "房地产/建筑", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100300, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100303, "positionType": 0, "name": "培训/辅导机构", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100305, "positionType": 0, "name": "职业培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100301, "positionType": 0, "name": "学前教育", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100302, "positionType": 0, "name": "学校/学历教育", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100304, "positionType": 0, "name": "学术/科研", "rank": 0, "mark": 0}], "positionType": 0, "name": "教育培训", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100100, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100104, "positionType": 0, "name": "文化艺术/娱乐", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100105, "positionType": 0, "name": "体育", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100101, "positionType": 0, "name": "广告/公关/会展", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100103, "positionType": 0, "name": "广播/影视", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100102, "positionType": 0, "name": "新闻/出版", "rank": 0, "mark": 0}], "positionType": 0, "name": "广告/传媒/文化/体育", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100900, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100906, "positionType": 0, "name": "通用设备", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100907, "positionType": 0, "name": "专用设备", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100908, "positionType": 0, "name": "电气机械/器材", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100909, "positionType": 0, "name": "金属制品", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100910, "positionType": 0, "name": "非金属矿物制品", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100911, "positionType": 0, "name": "橡胶/塑料制品", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100912, "positionType": 0, "name": "化学原料/化学制品", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100913, "positionType": 0, "name": "仪器仪表", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100914, "positionType": 0, "name": "自动化设备", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100904, "positionType": 0, "name": "印刷/包装/造纸", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100905, "positionType": 0, "name": "铁路/船舶/航空/航天制造", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100915, "positionType": 0, "name": "计算机/通信/其他电子设备", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100916, "positionType": 0, "name": "新材料", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100917, "positionType": 0, "name": "其他制造业", "rank": 0, "mark": 0}], "positionType": 0, "name": "制造业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100600, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100601, "positionType": 0, "name": "咨询", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100605, "positionType": 0, "name": "财务/审计/税务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100604, "positionType": 0, "name": "人力资源服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100602, "positionType": 0, "name": "法律", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100609, "positionType": 0, "name": "检测/认证/知识产权", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100603, "positionType": 0, "name": "翻译", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100608, "positionType": 0, "name": "其他专业服务", "rank": 0, "mark": 0}], "positionType": 0, "name": "专业服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100400, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100402, "positionType": 0, "name": "医疗服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100404, "positionType": 0, "name": "医美服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100403, "positionType": 0, "name": "医疗器械", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100405, "positionType": 0, "name": "IVD", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100401, "positionType": 0, "name": "生物/制药", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100406, "positionType": 0, "name": "医药批发零售", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100407, "positionType": 0, "name": "医疗研发外包", "rank": 0, "mark": 0}], "positionType": 0, "name": "制药/医疗", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100800, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100804, "positionType": 0, "name": "新能源汽车", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100805, "positionType": 0, "name": "汽车智能网联", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100806, "positionType": 0, "name": "汽车经销商", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100807, "positionType": 0, "name": "汽车后市场", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100801, "positionType": 0, "name": "汽车研发/制造", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100802, "positionType": 0, "name": "汽车零部件", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100808, "positionType": 0, "name": "摩托车/自行车制造", "rank": 0, "mark": 0}], "positionType": 0, "name": "汽车", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100500, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100505, "positionType": 0, "name": "即时配送", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100506, "positionType": 0, "name": "快递", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100507, "positionType": 0, "name": "公路物流", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100508, "positionType": 0, "name": "同城货运", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100509, "positionType": 0, "name": "跨境物流", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100510, "positionType": 0, "name": "装卸搬运和仓储业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100511, "positionType": 0, "name": "客运服务", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100512, "positionType": 0, "name": "港口/铁路/公路/机场", "rank": 0, "mark": 0}], "positionType": 0, "name": "交通运输/物流", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101200, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101208, "positionType": 0, "name": "光伏", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101209, "positionType": 0, "name": "储能", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101210, "positionType": 0, "name": "动力电池", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101211, "positionType": 0, "name": "风电", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101212, "positionType": 0, "name": "其他新能源", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101207, "positionType": 0, "name": "环保", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101202, "positionType": 0, "name": "化工", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101205, "positionType": 0, "name": "电力/热力/燃气/水利", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101201, "positionType": 0, "name": "石油/石化", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101203, "positionType": 0, "name": "矿产/地质", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101204, "positionType": 0, "name": "采掘/冶炼", "rank": 0, "mark": 0}], "positionType": 0, "name": "能源/化工/环保", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100200, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 100206, "positionType": 0, "name": "互联网金融", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100201, "positionType": 0, "name": "银行", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100207, "positionType": 0, "name": "投资/融资", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100203, "positionType": 0, "name": "证券/期货", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100204, "positionType": 0, "name": "基金", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100202, "positionType": 0, "name": "保险", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100208, "positionType": 0, "name": "租赁/拍卖/典当/担保", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100205, "positionType": 0, "name": "信托", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100209, "positionType": 0, "name": "财富管理", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 100210, "positionType": 0, "name": "其他金融业", "rank": 0, "mark": 0}], "positionType": 0, "name": "金融", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101300, "subLevelModelList": [{"cityType": 0, "capital": 0, "regionCode": 0, "code": 101303, "positionType": 0, "name": "农/林/牧/渔", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101302, "positionType": 0, "name": "非盈利机构", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101301, "positionType": 0, "name": "政府/公共事业", "rank": 0, "mark": 0}, {"cityType": 0, "capital": 0, "regionCode": 0, "code": 101304, "positionType": 0, "name": "其他行业", "rank": 0, "mark": 0}], "positionType": 0, "name": "政府/非盈利机构/其他", "rank": 0, "mark": 0}]}