{"v": "5.7.3", "fr": 60, "ip": 0, "op": 120, "w": 40, "h": 30, "nm": "get-tab", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "“图层 2/get-tab-2”轮廓", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [7], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [7], "e": [0]}, {"t": 66}], "ix": 10}, "p": {"a": 0, "k": [19.933, 26.756, 0], "ix": 2}, "a": {"a": 0, "k": [12.413, 20.741, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.997, 0.353], [0.175, 0.006], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.635, -1.212], [-0.175, -0.062], [-0.934, -0.034], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [2.196, -1.678], [0.748, 1.084], [2.196, -1.678], [1.654, -4.026], [1.129, -4.129], [-1.654, -1.84]], "c": true}], "e": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [1.353, 1.458], [0.242, -0.085], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.635, -1.212], [-0.079, -0.085], [-1.295, 0.456], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.977, -1.477], [0.748, 1.084], [2.709, -3.167], [5.175, -8.308], [4.679, -8.297], [-0.768, -3.688]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [1.353, 1.458], [0.242, -0.085], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.635, -1.212], [-0.079, -0.085], [-1.295, 0.456], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.977, -1.477], [0.748, 1.084], [2.709, -3.167], [5.175, -8.308], [4.679, -8.297], [-0.768, -3.688]], "c": true}], "e": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.568, 2.202], [0.215, 0.009], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.473, -0.869], [-0.042, -0.087], [-1.147, -0.048], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.986, -1.485], [0.748, 1.084], [2.688, -3.108], [4.873, -6], [4.474, -6.143], [-0.803, -3.614]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.568, 2.202], [0.215, 0.009], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.473, -0.869], [-0.042, -0.087], [-1.147, -0.048], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.986, -1.485], [0.748, 1.084], [2.688, -3.108], [4.873, -6], [4.474, -6.143], [-0.803, -3.614]], "c": true}], "e": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.964, 1.89], [0.221, 0.031], [0.573, -0.528]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.248, -0.692], [-0.081, -0.136], [-1.179, -0.165], [-0.681, 0.627]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.995, -1.493], [0.748, 1.084], [2.745, -3.06], [5.093, -6.018], [4.628, -6.262], [0.063, -4.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.964, 1.89], [0.221, 0.031], [0.573, -0.528]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.248, -0.692], [-0.081, -0.136], [-1.179, -0.165], [-0.681, 0.627]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [1.995, -1.493], [0.748, 1.084], [2.745, -3.06], [5.093, -6.018], [4.628, -6.262], [0.063, -4.75]], "c": true}], "e": [{"i": [[0, 0], [-0.437, 0.954], [1.098, 0.336], [-1.282, -4.453], [-2.829, 2.023], [2.369, 0.076], [0.474, -0.645], [0, 0], [0, 0], [0.767, 0.329], [0.675, -0.853], [0, 0], [0, 0], [0.997, 0.353], [0.175, 0.006], [0.922, -1.559]], "o": [[1.316, -2.625], [1.334, -2.921], [-2.452, -0.75], [1.281, 4.454], [2.329, -1.665], [-0.452, -0.015], [0, 0], [0, 0], [0.326, -1.1], [-0.768, -0.328], [0, 0], [0, 0], [0.635, -1.212], [-0.175, -0.062], [-0.934, -0.034], [0, 0]], "v": [[-2.659, 0.22], [-0.031, -5.148], [0.531, -9.887], [-7.498, 5.432], [5.952, 8.614], [6.411, -1.266], [5.022, -0.322], [3.752, 2.286], [5.022, -0.322], [4.36, -2.465], [2.196, -1.678], [0.748, 1.084], [2.196, -1.678], [1.654, -4.026], [1.129, -4.129], [-1.654, -1.84]], "c": true}]}, {"t": 66}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.33300000359, 0.33300000359, 0.33300000359, 1], "e": [0.33300000359, 0.33300000359, 0.33300000359, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0.33300000359, 0.33300000359, 0.33300000359, 1], "e": [1, 1, 1, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [1, 1, 1, 1], "e": [1, 1, 1, 1]}, {"t": 66}], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [1.4], "e": [0.4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [0.4], "e": [1]}, {"t": 66}], "ix": 5}, "lc": 1, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.48, 11.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[0, 0], [-0.392, 0.852], [-2.057, -1.585]], "o": [[-1.363, -2.721], [0.391, -0.852], [0, 0]], "v": [[-0.182, 3.105], [-1.64, -2.253], [2.032, -1.154]], "c": false}], "e": [{"i": [[0, 0], [-0.933, -0.095], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[6.808, 3.34], [11.166, -2.246], [11.387, 2.478]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [-0.933, -0.095], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[6.808, 3.34], [11.166, -2.246], [11.387, 2.478]], "c": false}], "e": [{"i": [[0, 0], [-0.933, -0.095], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[7.079, 3.692], [11.437, -1.894], [11.658, 2.83]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [-0.933, -0.095], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[7.079, 3.692], [11.437, -1.894], [11.658, 2.83]], "c": false}], "e": [{"i": [[0, 0], [-1.869, 0.016], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[7.079, 3.692], [11.116, -1.854], [11.239, 3.663]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [{"i": [[0, 0], [-1.869, 0.016], [0.842, -1.395]], "o": [[1.644, -2.419], [1.703, 0.607], [0, 0]], "v": [[7.079, 3.692], [11.116, -1.854], [11.239, 3.663]], "c": false}], "e": [{"i": [[0, 0], [-0.392, 0.852], [-2.057, -1.585]], "o": [[-1.363, -2.721], [0.391, -0.852], [0, 0]], "v": [[-0.182, 3.105], [-1.64, -2.253], [2.032, -1.154]], "c": false}]}, {"t": 66}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.541000007181, 0.541000007181, 0.541000007181, 1], "e": [0.541000007181, 0.541000007181, 0.541000007181, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0.541000007181, 0.541000007181, 0.541000007181, 1], "e": [1, 1, 1, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [1, 1, 1, 1], "e": [1, 1, 1, 1]}, {"t": 66}], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [1.4], "e": [0.4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 64, "s": [0.4], "e": [1]}, {"t": 66}], "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.156, 6.844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "“图层 1/无描边”轮廓", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 4, "s": [0], "e": [100]}, {"t": 5}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 11, "s": [7], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [7], "e": [0]}, {"t": 66}], "ix": 10}, "p": {"a": 0, "k": [20.088, 28.004, 0], "ix": 2}, "a": {"a": 0, "k": [12.088, 22.902, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "路径 1 - 内容 - Smoothness", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Blend Precision", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 25, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Blend On/Off", "np": 3, "mn": "ADBE Checkbox Control", "ix": 3, "en": 1, "ef": [{"ty": 7, "nm": "复选框", "mn": "ADBE Checkbox Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Smoothness", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Blend Precision", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 25, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Blend On/Off", "np": 3, "mn": "ADBE Checkbox Control", "ix": 6, "en": 1, "ef": [{"ty": 7, "nm": "复选框", "mn": "ADBE Checkbox Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 10, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 11, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 12, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 13, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 14, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 15, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 16, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 17, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 18, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.737, -0.338], [0.05, -0.502], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [0.365, 0.168], [-0.08, 0.802], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [-0.585, -3.345], [1.872, -4.487], [2.553, -3.447], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}], "e": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.863, -0.433], [0.158, -0.468], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [0.909, 0.444], [-0.636, 1.445], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [-0.081, -4.363], [3.668, -6.947], [2.872, -3.509], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.863, -0.433], [0.158, -0.468], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [0.909, 0.444], [-0.636, 1.445], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [-0.081, -4.363], [3.668, -6.947], [2.872, -3.509], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}], "e": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-1.455, -0.868], [0.27, -0.426], [0.562, -0.877], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.391, 0.83], [-1.134, 1.79], [-0.524, 0.818], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.106, -8.914], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-1.455, -0.868], [0.27, -0.426], [0.562, -0.877], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.391, 0.83], [-1.134, 1.79], [-0.524, 0.818], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.106, -8.914], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}], "e": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.767, -0.333], [0.244, -0.442], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.783, 0.71], [-1.082, 1.959], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.031, -7.478], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.767, -0.333], [0.244, -0.442], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.783, 0.71], [-1.082, 1.959], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.031, -7.478], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}], "e": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.923, -0.756], [0.244, -0.442], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.13, 0.925], [-1.082, 1.959], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.189, -6.867], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.923, -0.756], [0.244, -0.442], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [1.13, 0.925], [-1.082, 1.959], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [0.323, -5.178], [5.189, -6.867], [3.127, -3.559], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}], "e": [{"i": [[-0.463, 1.679], [-2.414, 0.526], [0.539, -1.127], [-0.111, -0.565], [-0.635, 1.494], [-0.787, 0.94], [-0.737, -0.338], [0.05, -0.502], [0.444, -0.887], [0.059, -0.176], [-0.555, 1.029], [-1.867, -0.491], [0.457, -0.943], [-0.441, 1.235], [-1.02, 0], [0.083, -1.828], [1.91, -1.792], [1.53, -0.162], [1.044, 2.16]], "o": [[0.464, -1.679], [2.414, -0.527], [-0.538, 1.127], [0.111, 0.566], [0.294, -0.69], [0.741, -0.886], [0.365, 0.168], [-0.08, 0.802], [-0.435, 0.869], [-0.157, 0.468], [0.555, -1.029], [1.869, 0.492], [-0.457, 0.944], [0.44, -1.236], [1.018, 0], [-0.06, 1.299], [-0.776, 0.728], [-5.297, 0.56], [-1.044, -2.161]], "v": [[-7.91, 0.424], [-0.958, -10.64], [0.176, -7.698], [-3.559, 0.168], [-2.26, -0.486], [-0.585, -3.345], [1.872, -4.487], [2.553, -3.447], [1.292, -0.655], [0.439, 1.008], [1.592, 0.908], [4.887, -2.657], [4.059, 2.113], [5.128, 2.297], [7.501, -1.126], [8.872, 3.146], [6.954, 9.366], [3.49, 10.607], [-7.91, 7.104]], "c": true}]}, {"t": 55}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.33300000359, 0.33300000359, 0.33300000359, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.263, 12.196], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[-1.468, -1.4], [0.22, -0.439], [0.399, -0.891], [-0.653, 0.763]], "o": [[-0.477, 0.849], [-0.221, 0.438], [-1.457, -2.492], [0.652, -0.762]], "v": [[1.742, -1.283], [0.696, 0.649], [-0.233, 2.643], [-1.439, -2.239]], "c": true}], "e": [{"i": [[-1.367, -1.393], [0.243, -0.489], [0.399, -0.891], [-0.69, 0.692]], "o": [[-0.399, 0.87], [-0.221, 0.438], [-1.213, -2.693], [0.729, -0.675]], "v": [[2.656, -0.796], [1.379, 1.741], [0.583, 3.771], [-0.693, -2.151]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[-1.367, -1.393], [0.243, -0.489], [0.399, -0.891], [-0.69, 0.692]], "o": [[-0.399, 0.87], [-0.221, 0.438], [-1.213, -2.693], [0.729, -0.675]], "v": [[2.656, -0.796], [1.379, 1.741], [0.583, 3.771], [-0.693, -2.151]], "c": true}], "e": [{"i": [[-0.961, -1.366], [0.335, -0.69], [0.399, -0.891], [-0.836, 0.406]], "o": [[-0.087, 0.956], [-0.221, 0.438], [-0.237, -3.499], [1.036, -0.325]], "v": [[4.522, 0.311], [4.111, 6.11], [1.275, 4.407], [2.288, -1.799]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[-0.961, -1.366], [0.335, -0.69], [0.399, -0.891], [-0.836, 0.406]], "o": [[-0.087, 0.956], [-0.221, 0.438], [-0.237, -3.499], [1.036, -0.325]], "v": [[4.522, 0.311], [4.111, 6.11], [1.275, 4.407], [2.288, -1.799]], "c": true}], "e": [{"i": [[-0.758, -1.352], [0.382, -0.79], [0.399, -0.891], [-0.91, 0.263]], "o": [[0.068, 0.999], [-0.221, 0.438], [0.251, -3.902], [1.189, -0.15]], "v": [[5.634, 0.949], [5.756, 6.908], [1.878, 5.113], [3.779, -1.623]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[-0.758, -1.352], [0.382, -0.79], [0.399, -0.891], [-0.91, 0.263]], "o": [[0.068, 0.999], [-0.221, 0.438], [0.251, -3.902], [1.189, -0.15]], "v": [[5.634, 0.949], [5.756, 6.908], [1.878, 5.113], [3.779, -1.623]], "c": true}], "e": [{"i": [[-0.006, -1.239], [0.328, -0.673], [0.399, -0.891], [-0.946, 0.191]], "o": [[-0.048, 0.936], [-0.221, 0.438], [0.495, -4.103], [1.266, -0.063]], "v": [[5.984, 1.332], [5.352, 6.742], [2.18, 5.466], [4.525, -1.535]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[-0.006, -1.239], [0.328, -0.673], [0.399, -0.891], [-0.946, 0.191]], "o": [[-0.048, 0.936], [-0.221, 0.438], [0.495, -4.103], [1.266, -0.063]], "v": [[5.984, 1.332], [5.352, 6.742], [2.18, 5.466], [4.525, -1.535]], "c": true}], "e": [{"i": [[-0.048, -1.64], [0.274, -0.556], [0.399, -0.891], [-0.983, 0.12]], "o": [[-0.259, 1.369], [-0.221, 0.438], [0.739, -4.305], [1.343, 0.024]], "v": [[6.642, 1.696], [4.948, 6.575], [2.481, 5.819], [5.27, -1.447]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[-0.048, -1.64], [0.274, -0.556], [0.399, -0.891], [-0.983, 0.12]], "o": [[-0.259, 1.369], [-0.221, 0.438], [0.739, -4.305], [1.343, 0.024]], "v": [[6.642, 1.696], [4.948, 6.575], [2.481, 5.819], [5.27, -1.447]], "c": true}], "e": [{"i": [[0.257, -1.426], [0.22, -0.439], [0.399, -0.891], [-1.02, 0.048]], "o": [[-0.477, 0.849], [-0.221, 0.438], [0.983, -4.506], [1.419, 0.112]], "v": [[7.166, 1.922], [4.544, 6.408], [2.783, 6.172], [6.016, -1.359]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0.257, -1.426], [0.22, -0.439], [0.399, -0.891], [-1.02, 0.048]], "o": [[-0.477, 0.849], [-0.221, 0.438], [0.983, -4.506], [1.419, 0.112]], "v": [[7.166, 1.922], [4.544, 6.408], [2.783, 6.172], [6.016, -1.359]], "c": true}], "e": [{"i": [[0.359, -1.278], [0.22, -0.439], [0.399, -0.891], [-1.313, -0.524]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.935, -6.117], [2.033, 0.811]], "v": [[11.849, 4.444], [7.334, 9.237], [5.732, 7.116], [11.979, -0.654]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0.359, -1.278], [0.22, -0.439], [0.399, -0.891], [-1.313, -0.524]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.935, -6.117], [2.033, 0.811]], "v": [[11.849, 4.444], [7.334, 9.237], [5.732, 7.116], [11.979, -0.654]], "c": true}], "e": [{"i": [[0.359, -1.278], [0.22, -0.439], [0.399, -0.891], [-1.313, -0.524]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.935, -6.117], [2.033, 0.811]], "v": [[12.454, 4.244], [7.939, 9.037], [6.337, 6.916], [12.584, -0.855]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0.359, -1.278], [0.22, -0.439], [0.399, -0.891], [-1.313, -0.524]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.935, -6.117], [2.033, 0.811]], "v": [[12.454, 4.244], [7.939, 9.037], [6.337, 6.916], [12.584, -0.855]], "c": true}], "e": [{"i": [[-0.007, -1.302], [0.22, -0.439], [0.399, -0.891], [-1.181, -0.266]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.057, -5.392], [1.757, 0.496]], "v": [[10.068, 3.052], [6.287, 7.752], [4.648, 6.32], [9.78, -1.132]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [{"i": [[-0.007, -1.302], [0.22, -0.439], [0.399, -0.891], [-1.181, -0.266]], "o": [[-0.477, 0.849], [-0.221, 0.438], [2.057, -5.392], [1.757, 0.496]], "v": [[10.068, 3.052], [6.287, 7.752], [4.648, 6.32], [9.78, -1.132]], "c": true}], "e": [{"i": [[-0.372, -1.327], [0.22, -0.439], [0.399, -0.891], [-1.049, -0.009]], "o": [[-0.477, 0.849], [-0.221, 0.438], [1.178, -4.667], [1.481, 0.182]], "v": [[8.169, 2.033], [4.635, 6.468], [2.959, 5.725], [6.975, -1.409]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [{"i": [[-0.372, -1.327], [0.22, -0.439], [0.399, -0.891], [-1.049, -0.009]], "o": [[-0.477, 0.849], [-0.221, 0.438], [1.178, -4.667], [1.481, 0.182]], "v": [[8.169, 2.033], [4.635, 6.468], [2.959, 5.725], [6.975, -1.409]], "c": true}], "e": [{"i": [[0.107, -1.784], [0.22, -0.439], [0.399, -0.891], [-0.917, 0.248]], "o": [[-0.406, 1.301], [-0.221, 0.438], [0.3, -3.942], [1.205, -0.133]], "v": [[5.416, 1.003], [3.322, 4.528], [1.895, 4.697], [4.17, -1.685]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [{"i": [[0.107, -1.784], [0.22, -0.439], [0.399, -0.891], [-0.917, 0.248]], "o": [[-0.406, 1.301], [-0.221, 0.438], [0.3, -3.942], [1.205, -0.133]], "v": [[5.416, 1.003], [3.322, 4.528], [1.895, 4.697], [4.17, -1.685]], "c": true}], "e": [{"i": [[-1.468, -1.4], [0.22, -0.439], [0.399, -0.891], [-0.653, 0.763]], "o": [[-0.477, 0.849], [-0.221, 0.438], [-1.457, -2.492], [0.652, -0.762]], "v": [[1.742, -1.283], [0.696, 0.649], [-0.233, 2.643], [-1.439, -2.239]], "c": true}]}, {"t": 55}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.33300000359, 0.33300000359, 0.33300000359, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.774, 6.975], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [80], "e": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [50], "e": [50]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [50], "e": [100]}, {"t": 52}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}], "markers": []}