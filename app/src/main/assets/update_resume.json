{"v": "5.5.4", "fr": 30, "ip": 0, "op": 90, "w": 285, "h": 315, "nm": "黑色", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "“welcome/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [101.875, 134.875, 0], "e": [101.875, 134.875, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.083}, "t": 33, "s": [101.875, 134.875, 0], "e": [104.975, 136.925, 0], "to": [0.517, 0.342, 0], "ti": [-1.034, -0.684, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [104.975, 136.925, 0], "e": [108.077, 138.976, 0], "to": [1.034, 0.684, 0], "ti": [-1.033, -0.683, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [108.077, 138.976, 0], "e": [111.176, 141.026, 0], "to": [1.033, 0.683, 0], "ti": [-1.033, -0.683, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [111.176, 141.026, 0], "e": [114.278, 143.077, 0], "to": [1.033, 0.683, 0], "ti": [-1.033, -0.683, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [114.278, 143.077, 0], "e": [117.373, 145.123, 0], "to": [1.033, 0.683, 0], "ti": [-1.032, -0.682, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [117.373, 145.123, 0], "e": [120.468, 147.171, 0], "to": [1.032, 0.682, 0], "ti": [-1.033, -0.683, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.166}, "t": 39, "s": [120.468, 147.171, 0], "e": [123.573, 149.223, 0], "to": [1.033, 0.683, 0], "ti": [-1.034, -0.684, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [123.573, 149.223, 0], "e": [126.674, 151.274, 0], "to": [1.034, 0.684, 0], "ti": [-1.034, -0.684, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [126.674, 151.274, 0], "e": [129.775, 153.325, 0], "to": [1.034, 0.684, 0], "ti": [-1.034, -0.683, 0]}, {"i": {"x": 0.833, "y": 0.905}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [129.775, 153.325, 0], "e": [132.875, 155.375, 0], "to": [1.034, 0.683, 0], "ti": [-0.585, -0.387, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.698}, "t": 43, "s": [132.875, 155.375, 0], "e": [133.285, 155.646, 0], "to": [0.585, 0.387, 0], "ti": [-0.131, -0.087, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.176}, "t": 44, "s": [133.285, 155.646, 0], "e": [133.664, 155.897, 0], "to": [0.131, 0.087, 0], "ti": [-0.121, -0.08, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 45, "s": [133.664, 155.897, 0], "e": [134.009, 156.125, 0], "to": [0.121, 0.08, 0], "ti": [-0.109, -0.072, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 46, "s": [134.009, 156.125, 0], "e": [134.318, 156.329, 0], "to": [0.109, 0.072, 0], "ti": [-0.097, -0.064, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 47, "s": [134.318, 156.329, 0], "e": [134.591, 156.51, 0], "to": [0.097, 0.064, 0], "ti": [-0.085, -0.056, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 48, "s": [134.591, 156.51, 0], "e": [134.826, 156.665, 0], "to": [0.085, 0.056, 0], "ti": [-0.072, -0.048, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 49, "s": [134.826, 156.665, 0], "e": [135.024, 156.796, 0], "to": [0.072, 0.048, 0], "ti": [-0.059, -0.039, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 50, "s": [135.024, 156.796, 0], "e": [135.183, 156.901, 0], "to": [0.059, 0.039, 0], "ti": [-0.047, -0.031, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.192}, "t": 51, "s": [135.183, 156.901, 0], "e": [135.305, 156.982, 0], "to": [0.047, 0.031, 0], "ti": [-0.035, -0.023, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.202}, "t": 52, "s": [135.305, 156.982, 0], "e": [135.391, 157.039, 0], "to": [0.035, 0.023, 0], "ti": [-0.023, -0.015, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.224}, "t": 53, "s": [135.391, 157.039, 0], "e": [135.442, 157.073, 0], "to": [0.023, 0.015, 0], "ti": [-0.011, -0.008, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.322}, "t": 54, "s": [135.442, 157.073, 0], "e": [135.46, 157.084, 0], "to": [0.011, 0.008, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.658}, "o": {"x": 0.167, "y": 0.191}, "t": 55, "s": [135.46, 157.084, 0], "e": [135.446, 157.075, 0], "to": [0.001, 0, 0], "ti": [0.009, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.11}, "t": 56, "s": [135.446, 157.075, 0], "e": [135.404, 157.047, 0], "to": [-0.009, -0.006, 0], "ti": [0.019, 0.012, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.135}, "t": 57, "s": [135.404, 157.047, 0], "e": [135.334, 157.001, 0], "to": [-0.019, -0.012, 0], "ti": [0.027, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.814}, "o": {"x": 0.167, "y": 0.145}, "t": 58, "s": [135.334, 157.001, 0], "e": [135.241, 156.939, 0], "to": [-0.027, -0.018, 0], "ti": [0.035, 0.023, 0]}, {"i": {"x": 0.833, "y": 0.82}, "o": {"x": 0.167, "y": 0.151}, "t": 59, "s": [135.241, 156.939, 0], "e": [135.125, 156.863, 0], "to": [-0.035, -0.023, 0], "ti": [0.042, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.823}, "o": {"x": 0.167, "y": 0.155}, "t": 60, "s": [135.125, 156.863, 0], "e": [134.991, 156.775, 0], "to": [-0.042, -0.027, 0], "ti": [0.047, 0.031, 0]}, {"i": {"x": 0.833, "y": 0.826}, "o": {"x": 0.167, "y": 0.158}, "t": 61, "s": [134.991, 156.775, 0], "e": [134.841, 156.675, 0], "to": [-0.047, -0.031, 0], "ti": [0.052, 0.035, 0]}, {"i": {"x": 0.833, "y": 0.828}, "o": {"x": 0.167, "y": 0.16}, "t": 62, "s": [134.841, 156.675, 0], "e": [134.678, 156.567, 0], "to": [-0.052, -0.035, 0], "ti": [0.056, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.83}, "o": {"x": 0.167, "y": 0.162}, "t": 63, "s": [134.678, 156.567, 0], "e": [134.504, 156.452, 0], "to": [-0.056, -0.037, 0], "ti": [0.059, 0.039, 0]}, {"i": {"x": 0.833, "y": 0.831}, "o": {"x": 0.167, "y": 0.163}, "t": 64, "s": [134.504, 156.452, 0], "e": [134.322, 156.332, 0], "to": [-0.059, -0.039, 0], "ti": [0.061, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.832}, "o": {"x": 0.167, "y": 0.164}, "t": 65, "s": [134.322, 156.332, 0], "e": [134.135, 156.209, 0], "to": [-0.061, -0.041, 0], "ti": [0.063, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.165}, "t": 66, "s": [134.135, 156.209, 0], "e": [133.946, 156.083, 0], "to": [-0.063, -0.041, 0], "ti": [0.063, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.834}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [133.946, 156.083, 0], "e": [133.756, 155.958, 0], "to": [-0.063, -0.042, 0], "ti": [0.063, 0.042, 0]}, {"i": {"x": 0.833, "y": 0.835}, "o": {"x": 0.167, "y": 0.168}, "t": 68, "s": [133.756, 155.958, 0], "e": [133.568, 155.833, 0], "to": [-0.063, -0.042, 0], "ti": [0.062, 0.041, 0]}, {"i": {"x": 0.833, "y": 0.836}, "o": {"x": 0.167, "y": 0.168}, "t": 69, "s": [133.568, 155.833, 0], "e": [133.384, 155.712, 0], "to": [-0.062, -0.041, 0], "ti": [0.06, 0.04, 0]}, {"i": {"x": 0.833, "y": 0.837}, "o": {"x": 0.167, "y": 0.169}, "t": 70, "s": [133.384, 155.712, 0], "e": [133.207, 155.594, 0], "to": [-0.06, -0.04, 0], "ti": [0.058, 0.038, 0]}, {"i": {"x": 0.833, "y": 0.838}, "o": {"x": 0.167, "y": 0.17}, "t": 71, "s": [133.207, 155.594, 0], "e": [133.036, 155.482, 0], "to": [-0.058, -0.038, 0], "ti": [0.055, 0.037, 0]}, {"i": {"x": 0.833, "y": 0.839}, "o": {"x": 0.167, "y": 0.171}, "t": 72, "s": [133.036, 155.482, 0], "e": [132.875, 155.375, 0], "to": [-0.055, -0.037, 0], "ti": [0.052, 0.034, 0]}, {"i": {"x": 0.833, "y": 0.84}, "o": {"x": 0.167, "y": 0.172}, "t": 73, "s": [132.875, 155.375, 0], "e": [132.724, 155.275, 0], "to": [-0.052, -0.034, 0], "ti": [0.048, 0.032, 0]}, {"i": {"x": 0.833, "y": 0.841}, "o": {"x": 0.167, "y": 0.174}, "t": 74, "s": [132.724, 155.275, 0], "e": [132.585, 155.183, 0], "to": [-0.048, -0.032, 0], "ti": [0.044, 0.029, 0]}, {"i": {"x": 0.833, "y": 0.842}, "o": {"x": 0.167, "y": 0.175}, "t": 75, "s": [132.585, 155.183, 0], "e": [132.458, 155.099, 0], "to": [-0.044, -0.029, 0], "ti": [0.04, 0.027, 0]}, {"i": {"x": 0.833, "y": 0.843}, "o": {"x": 0.167, "y": 0.176}, "t": 76, "s": [132.458, 155.099, 0], "e": [132.344, 155.024, 0], "to": [-0.04, -0.027, 0], "ti": [0.036, 0.024, 0]}, {"i": {"x": 0.833, "y": 0.845}, "o": {"x": 0.167, "y": 0.178}, "t": 77, "s": [132.344, 155.024, 0], "e": [132.244, 154.958, 0], "to": [-0.036, -0.024, 0], "ti": [0.031, 0.021, 0]}, {"i": {"x": 0.833, "y": 0.847}, "o": {"x": 0.167, "y": 0.18}, "t": 78, "s": [132.244, 154.958, 0], "e": [132.157, 154.9, 0], "to": [-0.031, -0.021, 0], "ti": [0.027, 0.018, 0]}, {"i": {"x": 0.833, "y": 0.849}, "o": {"x": 0.167, "y": 0.183}, "t": 79, "s": [132.157, 154.9, 0], "e": [132.085, 154.852, 0], "to": [-0.027, -0.018, 0], "ti": [0.022, 0.014, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.186}, "t": 80, "s": [132.085, 154.852, 0], "e": [132.026, 154.814, 0], "to": [-0.022, -0.014, 0], "ti": [0.017, 0.011, 0]}, {"i": {"x": 0.833, "y": 0.858}, "o": {"x": 0.167, "y": 0.192}, "t": 81, "s": [132.026, 154.814, 0], "e": [131.981, 154.784, 0], "to": [-0.017, -0.011, 0], "ti": [0.013, 0.008, 0]}, {"i": {"x": 0.833, "y": 0.867}, "o": {"x": 0.167, "y": 0.202}, "t": 82, "s": [131.981, 154.784, 0], "e": [131.949, 154.763, 0], "to": [-0.013, -0.008, 0], "ti": [0.008, 0.006, 0]}, {"i": {"x": 0.833, "y": 0.888}, "o": {"x": 0.167, "y": 0.224}, "t": 83, "s": [131.949, 154.763, 0], "e": [131.931, 154.75, 0], "to": [-0.008, -0.006, 0], "ti": [0.004, 0.003, 0]}, {"i": {"x": 0.833, "y": 0.853}, "o": {"x": 0.167, "y": 0.322}, "t": 84, "s": [131.931, 154.75, 0], "e": [131.924, 154.746, 0], "to": [-0.004, -0.003, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.653}, "o": {"x": 0.167, "y": 0.194}, "t": 85, "s": [131.924, 154.746, 0], "e": [131.929, 154.749, 0], "to": [0, 0, 0], "ti": [-0.003, -0.002, 0]}, {"i": {"x": 0.833, "y": 0.781}, "o": {"x": 0.167, "y": 0.11}, "t": 86, "s": [131.929, 154.749, 0], "e": [131.945, 154.76, 0], "to": [0.003, 0.002, 0], "ti": [-0.007, -0.005, 0]}, {"i": {"x": 0.833, "y": 0.804}, "o": {"x": 0.167, "y": 0.135}, "t": 87, "s": [131.945, 154.76, 0], "e": [131.97, 154.777, 0], "to": [0.007, 0.005, 0], "ti": [-0.01, -0.007, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.145}, "t": 88, "s": [131.97, 154.777, 0], "e": [132.005, 154.799, 0], "to": [0.01, 0.007, 0], "ti": [-0.006, -0.004, 0]}, {"t": 89}], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 1, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 2, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 3, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 4, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 5, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 6, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 7, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 8, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 9, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 10, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 11, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 12, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 13, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 14, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 15, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 16, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 17, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 19, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 21, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 22, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 23, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 24, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 25, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 26, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 27, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 28, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 30, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 31, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.167, 0.167, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 32, "s": [0, 0, 100], "e": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.083, 0.083, 0]}, "t": 33, "s": [0, 0, 100], "e": [10, 10, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 34, "s": [10, 10, 100], "e": [20, 20, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 35, "s": [20, 20, 100], "e": [30, 30, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 36, "s": [30, 30, 100], "e": [40, 40, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 37, "s": [40, 40, 100], "e": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 38, "s": [50, 50, 100], "e": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 39, "s": [60, 60, 100], "e": [70, 70, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 40, "s": [70, 70, 100], "e": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 41, "s": [80, 80, 100], "e": [90, 90, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.907, 0.907, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 42, "s": [90, 90, 100], "e": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.849, 0.849, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.838, 0.838, 0]}, "t": 43, "s": [100, 100, 100], "e": [101.105, 101.105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.852, 0.852, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.186, 0.186, 0]}, "t": 44, "s": [101.105, 101.105, 100], "e": [102.004, 102.004, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.857, 0.857, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.191, 0.191, 0]}, "t": 45, "s": [102.004, 102.004, 100], "e": [102.701, 102.701, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.863, 0.863, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.199, 0.199, 0]}, "t": 46, "s": [102.701, 102.701, 100], "e": [103.205, 103.205, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.875, 0.875, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.213, 0.213, 0]}, "t": 47, "s": [103.205, 103.205, 100], "e": [103.527, 103.527, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.908, 0.908, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.252, 0.252, 0]}, "t": 48, "s": [103.527, 103.527, 100], "e": [103.687, 103.687, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1.44, 1.44, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.88, 0.88, 0]}, "t": 49, "s": [103.687, 103.687, 100], "e": [103.704, 103.704, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.754, 0.754, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.07, 0.07, 0]}, "t": 50, "s": [103.704, 103.704, 100], "e": [103.599, 103.599, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.802, 0.802, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.126, 0.126, 0]}, "t": 51, "s": [103.599, 103.599, 100], "e": [103.394, 103.394, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.817, 0.817, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.144, 0.144, 0]}, "t": 52, "s": [103.394, 103.394, 100], "e": [103.112, 103.112, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.824, 0.824, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.153, 0.153, 0]}, "t": 53, "s": [103.112, 103.112, 100], "e": [102.773, 102.773, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.829, 0.829, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.158, 0.158, 0]}, "t": 54, "s": [102.773, 102.773, 100], "e": [102.397, 102.397, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 55, "s": [102.397, 102.397, 100], "e": [102.001, 102.001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.835, 0.835, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.166, 0.166, 0]}, "t": 56, "s": [102.001, 102.001, 100], "e": [101.602, 101.602, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 57, "s": [101.602, 101.602, 100], "e": [101.213, 101.213, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.84, 0.84, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.171, 0.171, 0]}, "t": 58, "s": [101.213, 101.213, 100], "e": [100.844, 100.844, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.842, 0.842, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.174, 0.174, 0]}, "t": 59, "s": [100.844, 100.844, 100], "e": [100.505, 100.505, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.844, 0.844, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 60, "s": [100.505, 100.505, 100], "e": [100.201, 100.201, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.179, 0.179, 0]}, "t": 61, "s": [100.201, 100.201, 100], "e": [99.938, 99.938, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.85, 0.85, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.183, 0.183, 0]}, "t": 62, "s": [99.938, 99.938, 100], "e": [99.717, 99.717, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.853, 0.853, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.187, 0.187, 0]}, "t": 63, "s": [99.717, 99.717, 100], "e": [99.539, 99.539, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.858, 0.858, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.193, 0.193, 0]}, "t": 64, "s": [99.539, 99.539, 100], "e": [99.404, 99.404, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.866, 0.866, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.202, 0.202, 0]}, "t": 65, "s": [99.404, 99.404, 100], "e": [99.308, 99.308, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.88, 0.88, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.219, 0.219, 0]}, "t": 66, "s": [99.308, 99.308, 100], "e": [99.249, 99.249, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.927, 0.927, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.275, 0.275, 0]}, "t": 67, "s": [99.249, 99.249, 100], "e": [99.224, 99.224, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.219, 0.219, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.562, -0.562, 0]}, "t": 68, "s": [99.224, 99.224, 100], "e": [99.227, 99.227, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.774, 0.774, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.093, 0.093, 0]}, "t": 69, "s": [99.227, 99.227, 100], "e": [99.255, 99.255, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.807, 0.807, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.132, 0.132, 0]}, "t": 70, "s": [99.255, 99.255, 100], "e": [99.302, 99.302, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.819, 0.819, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.146, 0.146, 0]}, "t": 71, "s": [99.302, 99.302, 100], "e": [99.365, 99.365, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.826, 0.826, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.154, 0.154, 0]}, "t": 72, "s": [99.365, 99.365, 100], "e": [99.438, 99.438, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.83, 0.83, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.16, 0.16, 0]}, "t": 73, "s": [99.438, 99.438, 100], "e": [99.518, 99.518, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.163, 0.163, 0]}, "t": 74, "s": [99.518, 99.518, 100], "e": [99.602, 99.602, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.836, 0.836, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0]}, "t": 75, "s": [99.602, 99.602, 100], "e": [99.685, 99.685, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.838, 0.838, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.169, 0.169, 0]}, "t": 76, "s": [99.685, 99.685, 100], "e": [99.766, 99.766, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.841, 0.841, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.172, 0.172, 0]}, "t": 77, "s": [99.766, 99.766, 100], "e": [99.842, 99.842, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.843, 0.843, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.175, 0.175, 0]}, "t": 78, "s": [99.842, 99.842, 100], "e": [99.911, 99.911, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.845, 0.845, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.177, 0.177, 0]}, "t": 79, "s": [99.911, 99.911, 100], "e": [99.972, 99.972, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.847, 0.847, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.18, 0.18, 0]}, "t": 80, "s": [99.972, 99.972, 100], "e": [100.025, 100.025, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.85, 0.85, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.184, 0.184, 0]}, "t": 81, "s": [100.025, 100.025, 100], "e": [100.069, 100.069, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.854, 0.854, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.188, 0.188, 0]}, "t": 82, "s": [100.069, 100.069, 100], "e": [100.104, 100.104, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.859, 0.859, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.194, 0.194, 0]}, "t": 83, "s": [100.104, 100.104, 100], "e": [100.131, 100.131, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.868, 0.868, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.205, 0.205, 0]}, "t": 84, "s": [100.131, 100.131, 100], "e": [100.149, 100.149, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.887, 0.887, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.227, 0.227, 0]}, "t": 85, "s": [100.149, 100.149, 100], "e": [100.159, 100.159, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.962, 0.962, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.316, 0.316, 0]}, "t": 86, "s": [100.159, 100.159, 100], "e": [100.163, 100.163, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.637, 0.637, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [-0.069, -0.069, 0]}, "t": 87, "s": [100.163, 100.163, 100], "e": [100.161, 100.161, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.108, 0.108, 0]}, "t": 88, "s": [100.161, 100.161, 100], "e": [100.154, 100.154, 100]}, {"t": 89}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "缩放 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 6, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "缩放 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 16, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "缩放 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "位置 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 11, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "位置 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 10, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "位置 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.392, -0.42], [0.071, -0.715], [0, 0], [1.373, 0]], "o": [[-0.392, 0.42], [0, 0], [-0.14, -1.555], [-0.644, 0]], "v": [[-1.562, -2.248], [-2.249, -0.553], [2.276, -0.553], [-0.007, -2.879]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.588, -0.784], [-0.028, -1.26], [0, 0], [-0.392, -0.448], [-0.687, 0], [-0.365, 0.294], [-0.182, 0.504], [0, 0], [0.491, -0.448], [0.938, 0], [0.616, 0.672], [0, 1.177], [-0.617, 0.715], [-1.051, 0]], "o": [[0.532, 0.702], [0, 0], [0.056, 0.813], [0.392, 0.448], [0.588, 0], [0.308, -0.252], [0, 0], [-0.169, 0.7], [-0.602, 0.532], [-1.037, 0], [-0.645, -0.7], [0, -1.064], [0.616, -0.755], [1.162, 0]], "v": [[2.598, -2.641], [3.439, 0.315], [-2.276, 0.315], [-1.618, 2.206], [0.007, 2.879], [1.45, 2.431], [2.178, 1.296], [3.299, 1.296], [2.318, 3.005], [0.007, 3.817], [-2.486, 2.795], [-3.439, -0.007], [-2.528, -2.683], [-0.035, -3.817]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [104.799, 82.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.406, -0.925], [-0.392, 0.21], [-0.504, 0], [-0.477, -0.477], [0, -0.826], [0, 0], [0, 0], [0, 0], [0.266, 0.308], [0.616, 0], [0.308, -0.364], [0, -0.589], [0, 0], [0, 0], [0, 0], [1.106, 0], [0.336, -0.42], [0, -0.574], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.813, 0]], "o": [[0.28, -0.477], [0.35, -0.225], [0.757, 0], [0.448, 0.49], [0, 0], [0, 0], [0, 0], [0, -0.645], [-0.28, -0.336], [-0.476, 0], [-0.337, 0.365], [0, 0], [0, 0], [0, 0], [0, -1.289], [-0.518, 0], [-0.336, 0.393], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.546, -0.784], [1.106, 0]], "v": [[0.315, -2.346], [1.324, -3.382], [2.598, -3.719], [4.434, -3.004], [5.12, -1.03], [5.12, 3.719], [3.999, 3.719], [3.999, -0.847], [3.593, -2.276], [2.248, -2.766], [1.058, -2.234], [0.567, -0.819], [0.567, 3.719], [-0.553, 3.719], [-0.553, -0.847], [-2.22, -2.766], [-3.495, -2.15], [-3.999, -0.693], [-3.999, 3.719], [-5.12, 3.719], [-5.12, -3.523], [-3.999, -3.523], [-3.999, -2.556], [-1.954, -3.719]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.93, 82.281], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.42, -0.588], [0, -0.813], [-0.392, -0.532], [-0.77, 0], [-0.448, 0.589], [0, 0.785], [0.379, 0.532], [0.757, 0]], "o": [[-0.392, 0.532], [0, 0.799], [0.42, 0.589], [0.757, 0], [0.379, -0.532], [0, -0.813], [-0.448, -0.588], [-0.77, 0]], "v": [[-1.793, -1.996], [-2.367, 0.007], [-1.793, 1.996], [0, 2.879], [1.807, 1.996], [2.381, 0.007], [1.807, -1.996], [0, -2.879]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.645, -0.757], [0, -1.093], [0.603, -0.7], [1.079, 0], [0.644, 0.757], [0, 1.079], [-0.617, 0.714], [-1.092, 0]], "o": [[0.616, 0.714], [0, 1.079], [-0.658, 0.757], [-1.092, 0], [-0.617, -0.7], [0, -1.093], [0.644, -0.757], [1.079, 0]], "v": [[2.592, -2.696], [3.516, 0.007], [2.605, 2.683], [0, 3.817], [-2.591, 2.683], [-3.516, 0.007], [-2.591, -2.696], [0, -3.817]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.841, 82.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 6, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.546, -0.42], [-0.126, -0.882], [0, 0], [0.35, 0.28], [0.574, 0], [0.406, -0.532], [0, -0.883], [-0.392, -0.505], [-0.729, 0], [-0.168, 1.247], [0, 0], [0.56, -0.477], [0.896, 0], [0.603, 0.729], [0, 1.106], [-0.574, 0.7], [-1.079, 0]], "o": [[0.56, 0.435], [0, 0], [-0.098, -0.56], [-0.365, -0.279], [-0.7, 0], [-0.392, 0.519], [0, 0.882], [0.378, 0.504], [1.233, 0], [0, 0], [-0.168, 0.924], [-0.547, 0.462], [-1.093, 0], [-0.574, -0.686], [0, -1.107], [0.617, -0.771], [0.896, 0]], "v": [[2.249, -3.187], [3.271, -1.212], [2.165, -1.212], [1.493, -2.473], [0.092, -2.878], [-1.576, -2.081], [-2.165, 0.021], [-1.589, 2.109], [0.077, 2.878], [2.193, 1.001], [3.312, 1.001], [2.234, 3.117], [0.077, 3.818], [-2.458, 2.725], [-3.312, 0.035], [-2.458, -2.667], [0.092, -3.818]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.98, 82.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0.553, 5.099], [-0.553, 5.099], [-0.553, -5.099], [0.553, -5.099]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.436, 80.901], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.392, -0.42], [0.071, -0.715], [0, 0], [1.373, 0]], "o": [[-0.392, 0.42], [0, 0], [-0.14, -1.555], [-0.644, 0]], "v": [[-1.562, -2.248], [-2.249, -0.553], [2.276, -0.553], [-0.007, -2.879]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.588, -0.784], [-0.028, -1.26], [0, 0], [-0.392, -0.448], [-0.687, 0], [-0.365, 0.294], [-0.182, 0.504], [0, 0], [0.491, -0.448], [0.938, 0], [0.616, 0.672], [0, 1.177], [-0.617, 0.715], [-1.051, 0]], "o": [[0.532, 0.702], [0, 0], [0.056, 0.813], [0.392, 0.448], [0.588, 0], [0.308, -0.252], [0, 0], [-0.169, 0.7], [-0.602, 0.532], [-1.037, 0], [-0.645, -0.7], [0, -1.064], [0.616, -0.755], [1.162, 0]], "v": [[2.598, -2.641], [3.439, 0.315], [-2.276, 0.315], [-1.618, 2.206], [0.007, 2.879], [1.45, 2.431], [2.178, 1.296], [3.299, 1.296], [2.318, 3.005], [0.007, 3.817], [-2.486, 2.795], [-3.439, -0.007], [-2.528, -2.683], [-0.035, -3.817]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [65.907, 82.379], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 6, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.154, -5.001], [-2.956, 3.39], [-2.9, 3.39], [-0.617, -5.001], [0.631, -5.001], [2.9, 3.39], [2.956, 3.39], [5.156, -5.001], [6.443, -5.001], [3.572, 5.001], [2.298, 5.001], [0.029, -3.335], [-0.014, -3.335], [-2.298, 5.001], [-3.572, 5.001], [-6.443, -5.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.537254929543, 0.537254929543, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 70, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [55.513, 80.999], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[4, -2.5], [0.021, 2.5], [-4, -2.5]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.478431373835, 0.486274510622, 0.627451002598, 1], "ix": 4}, "o": {"a": 0, "k": 15, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [100, 98.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.988, 0], [0, 0], [0, -1.988], [0, 0], [1.988, 0], [0, 0], [0, 0], [0, 0], [0, 1.988], [0, 0]], "o": [[0, 0], [1.988, 0], [0, 0], [0, 1.988], [0, 0], [0, 0], [0, 0], [-1.988, 0], [0, 0], [0, -1.988]], "v": [[-36.4, -14.5], [36.4, -14.5], [40, -10.9], [40, 10.9], [36.4, 14.5], [-18.8, 14.5], [-28, 14.5], [-36.4, 14.5], [-40, 10.9], [-40, -10.9]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.478431373835, 0.486274510622, 0.627451002598, 1], "ix": 4}, "o": {"a": 0, "k": 15, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [79, 81.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 9", "np": 4, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "“礼花/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 5, "s": [0], "e": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 9, "s": [100], "e": [0]}, {"t": 46}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [129.5, -71.5, 0], "e": [129.5, 120.5, 0], "to": [0, 32, 0], "ti": [0, -37, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [129.5, 120.5, 0], "e": [129.5, 150.5, 0], "to": [0, 37, 0], "ti": [0, -5, 0]}, {"t": 28}], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [11, 11, 100], "e": [97, 97, 100]}, {"t": 10}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "组 6 - 内容 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "组 6 - 内容 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 60, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "组 6 - 内容 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 4, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 5, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 1 - 内容 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 6, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 7, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 8, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "路径 - 路径 1 - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 9, "en": 1, "ef": [{"ty": 0, "nm": "滑块", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-8.528, 2.125], [0, 0], [3.716, 0.784]], "o": [[0, 0], [0, 0], [-3.6, -3.53], [0, 0], [-3.715, -0.785]], "v": [[-9.706, -4.858], [-4.812, 0.852], [9.706, 2.733], [4.491, -2.551], [-3.473, -3.069]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.666999966491, 0.195999998205, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [192.706, 4.858], "e": [209.978, 8.031], "to": [2.879, 0.529], "ti": [-8.766, -7.297]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [209.978, 8.031], "e": [245.303, 48.641], "to": [8.766, 7.297], "ti": [-5.888, -6.768]}, {"t": 66}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.635, -1.027], [-1.751, -0.813], [-0.894, 1.119], [0, 0], [0, 0], [0.108, -3.089]], "o": [[2.087, 1.027], [3.373, -2.107], [0.894, -1.118], [0, 0], [0, 0], [-0.108, 3.088]], "v": [[-6.651, 4.033], [-0.44, 6.999], [3.194, -2.051], [6.651, -4.391], [-0.144, -6.999], [-3.308, -2.259]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.325, 0.791999966491, 0.764999988032, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [254.507, 75.99], "e": [275.471, 89.811], "to": [3.494, 2.303], "ti": [-1.624, -3.05]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [275.471, 89.811], "e": [264.25, 94.289], "to": [1.624, 3.05], "ti": [-4.268, -2.817]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [264.25, 94.289], "e": [301.077, 106.71], "to": [4.268, 2.817], "ti": [-2.064, -3.566]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [301.077, 106.71], "e": [276.635, 115.687], "to": [2.064, 3.566], "ti": [4.074, -1.496]}, {"t": 103}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-7.002, -3.452], [5.365, -6.126]], "o": [[0, 0], [-4.34, -2.56], [5.063, 0.826]], "v": [[-1.777, 3.846], [9.5, -0.047], [-9.5, 2.28]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.294000004787, 0.528999956916, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [282.897, 39.068], "e": [265.066, 63.33], "to": [-2.972, 4.044], "ti": [2.972, -4.044]}, {"t": 62}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.887, -0.081], [0, 0], [-0.512, -0.373]], "o": [[0, 0], [0, 0], [0, 0], [0.886, 0.081], [0, 0], [0.513, 0.374]], "v": [[5.486, 1.04], [-2.514, 1.01], [-5.486, -0.96], [0.34, -0.96], [2.992, -0.462], [4.887, 0.2]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.642999985639, 0.764999988032, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [288.039, 38.812], "e": [269.316, 62.853], "to": [-3.121, 4.007], "ti": [3.121, -4.007]}, {"t": 62}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.345, 8.073], [0, 0], [-6.958, -3.856], [0, 0]], "o": [[3.925, -0.413], [0, 0], [0, 0], [0, 0]], "v": [[-9.578, -5.015], [-0.044, -6.017], [9.578, 5.039], [0.272, 6.017]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152999997606, 0.161000001197, 0.286000001197, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [82.226, 91.75], "e": [32.144, 122.07], "to": [-0.087, -0.014], "ti": [0.005, 0.021]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [32.144, 122.07], "e": [53.783, 143.368], "to": [-0.005, -0.021], "ti": [8.573, -3.413]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [53.783, 143.368], "e": [10.901, 182.883], "to": [-8.576, 3.414], "ti": [3.43, -1.366]}, {"t": 167}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[17.892, 3.186], [13.01, 8.16], [21.01, 9.186]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[15.83, 45.454], [8.113, 41.407], [16.113, 42.433]], "c": true}]}, {"t": 60}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('\\u8def\\u5f84 - \\u8def\\u5f84 1 - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.352999997606, 0.372999991623, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [112.045, 48.967], "e": [112, 56], "to": [-0.008, 1.172], "ti": [-0.306, -5.827]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [112, 56], "e": [113.879, 83.931], "to": [0.306, 5.827], "ti": [-0.313, -4.655]}, {"t": 120}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.301, 0.524], [0, 0], [0.582, 5.034]], "o": [[0, 0], [-5.682, 0.898], [0, 0], [0, 0]], "v": [[-23.515, 23.296], [-16.244, 28.441], [-24.507, 28.911], [-30.244, 23.013]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.294000004787, 0.705999995213, 0.681999954523, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [59.253, 53.77], "e": [36.847, 79.39], "to": [-3.734, 4.27], "ti": [3.734, -4.27]}, {"t": 60}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[-4.688, -0.33], [0, 0], [0, 0], [2.164, 0]], "o": [[4.688, 0.33], [0, 0], [0, 0], [0.159, 0]], "v": [[-27.384, 23.834], [-21.304, 17.847], [-29.295, 17.847], [-32.304, 21.184]], "c": true}], "e": [{"i": [[-4.688, -0.33], [0, 0], [0, 0], [2.164, 0]], "o": [[4.688, 0.33], [0, 0], [0, 0], [0.159, 0]], "v": [[-50.838, 50.122], [-44.758, 44.135], [-52.749, 44.135], [-55.758, 47.472]], "c": true}]}, {"t": 60}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.325, 0.791999966491, 0.764999988032, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [68.494, 57.98], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 8", "np": 4, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "“桌子/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142.5, 157.5, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.359, 0], [0, 0], [0, -1.36], [-1.36, 0], [0, 0], [0, 1.359]], "o": [[0, 0], [-1.36, 0], [0, 1.359], [0, 0], [1.359, 0], [0, -1.36]], "v": [[72.21, -2.462], [-72.211, -2.462], [-74.673, 0.001], [-72.211, 2.462], [72.21, 2.462], [74.672, 0.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 14, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [130.672, 156.402], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.946, 0], [0, -1.947], [-1.947, 0], [0, 1.947]], "o": [[-1.947, 0], [0, 1.947], [1.946, 0], [0, -1.947]], "v": [[0.001, -3.525], [-3.525, 0.001], [0.001, 3.525], [3.525, 0.001]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 20, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [101.821, 136.033], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[19.738, -1.41], [-19.739, -1.41], [-19.051, 1.41], [19.738, 1.41]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 14, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150.382, 152.508], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.297, 0], [0, 0], [0, -1.557], [-0.045, -0.203], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.557, 0], [0, 0.207], [0, 0], [0, 0], [0, 0], [-0.311, -1.26]], "v": [[17.556, -18.471], [-26.059, -18.471], [-28.878, -15.65], [-28.81, -15.033], [-21.29, 18.471], [28.878, 18.471], [20.294, -16.326]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 14, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [102.5, 135.471], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[15.275, -72.225], [10.332, -72.236], [-15.4, 73.161], [-10.551, 74.017]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 14, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [74.722, 231.062], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.515, -0.436], [1.102, -0.809]], "o": [[0, 0], [0, 0], [-0.767, 0.777], [-0.514, 0.438], [0, 0]], "v": [[-3.175, -5.398], [1.768, -5.394], [3.331, 3.294], [1.407, 5.117], [-1.018, 6.988]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.466666668653, 0.466666668653, 0.635294139385, 1], "ix": 4}, "o": {"a": 0, "k": 14, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [179.026, 164.226], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": -28, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "“头/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 2, "s": [0], "e": [-11]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [-11], "e": [0]}, {"t": 63}], "ix": 10}, "p": {"a": 0, "k": [204, 93.5, 0], "ix": 2}, "a": {"a": 0, "k": [204, 93.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.648, 0], [0, 1.656], [1.657, 0], [0.287, -0.091]], "o": [[0.49, 0.351], [1.657, 0], [0, -1.657], [-0.317, 0], [0, 0]], "v": [[-2.37, 2.443], [-0.63, 3], [2.37, 0], [-0.63, -3], [-1.54, -2.86]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0.866999966491, 0.816000007181, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [213.377, 77.208], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[-2.344, 3.22], [-2.529, -7.37], [-0.213, -1.108], [1.547, 0.462], [0.089, 0.413], [0.11, 1.082], [-0.357, 0.43], [-0.913, 1.973], [0, 0.432], [0.054, 3.028], [-0.444, 2.368]], "o": [[3.855, 17.06], [0.715, 2.082], [-3.923, -1.564], [-4.179, -1.248], [-0.088, -0.413], [-0.252, -2.469], [0.863, -1.043], [0.58, -1.256], [0, -0.432], [-0.044, -2.534], [0.265, -1.41]], "v": [[-1.421, -20.715], [8.156, 15.93], [9.548, 20.715], [1.343, 17.676], [-8.752, 14.905], [-9.296, 11.631], [-8.954, 7.815], [-6.508, 3.926], [-5.529, 0.036], [-5.334, -6.014], [-5.334, -13.771]], "c": true}], "e": [{"i": [[-2.344, 3.22], [-2.529, -7.37], [-0.213, -1.108], [3.662, 1.363], [0.089, 0.413], [0.11, 1.082], [-0.357, 0.43], [-0.913, 1.973], [0, 0.432], [0.054, 3.028], [-0.444, 2.368]], "o": [[3.855, 17.06], [0.715, 2.082], [-3.923, -1.564], [-4.087, -1.522], [-0.088, -0.413], [-0.252, -2.469], [0.863, -1.043], [0.58, -1.256], [0, -0.432], [-0.044, -2.534], [0.265, -1.41]], "v": [[-1.421, -20.715], [8.156, 15.93], [12.227, 26.584], [0.321, 19.005], [-8.752, 14.905], [-9.296, 11.631], [-8.954, 7.815], [-6.508, 3.926], [-5.529, 0.036], [-5.334, -6.014], [-5.334, -13.771]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[-2.344, 3.22], [-2.529, -7.37], [-0.213, -1.108], [3.662, 1.363], [0.089, 0.413], [0.11, 1.082], [-0.357, 0.43], [-0.913, 1.973], [0, 0.432], [0.054, 3.028], [-0.444, 2.368]], "o": [[3.855, 17.06], [0.715, 2.082], [-3.923, -1.564], [-4.087, -1.522], [-0.088, -0.413], [-0.252, -2.469], [0.863, -1.043], [0.58, -1.256], [0, -0.432], [-0.044, -2.534], [0.265, -1.41]], "v": [[-1.421, -20.715], [8.156, 15.93], [12.227, 26.584], [0.321, 19.005], [-8.752, 14.905], [-9.296, 11.631], [-8.954, 7.815], [-6.508, 3.926], [-5.529, 0.036], [-5.334, -6.014], [-5.334, -13.771]], "c": true}], "e": [{"i": [[-2.344, 3.22], [-2.529, -7.37], [-0.213, -1.108], [1.547, 0.462], [0.089, 0.413], [0.11, 1.082], [-0.357, 0.43], [-0.913, 1.973], [0, 0.432], [0.054, 3.028], [-0.444, 2.368]], "o": [[3.855, 17.06], [0.715, 2.082], [-3.923, -1.564], [-4.179, -1.248], [-0.088, -0.413], [-0.252, -2.469], [0.863, -1.043], [0.58, -1.256], [0, -0.432], [-0.044, -2.534], [0.265, -1.41]], "v": [[-1.421, -20.715], [8.156, 15.93], [9.548, 20.715], [1.343, 17.676], [-8.752, 14.905], [-9.296, 11.631], [-8.954, 7.815], [-6.508, 3.926], [-5.529, 0.036], [-5.334, -6.014], [-5.334, -13.771]], "c": true}]}, {"t": 63}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.250980407, 0.258823543787, 0.305882364511, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [217.148, 76.716], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.417, 2.19], [3.113, -11.399], [0.979, -2.442], [1.553, -14.079], [1.936, -0.763], [-2.082, -6.738], [-8.371, 3.534], [-1.558, 2.591], [-0.519, 2.591], [1.966, 3.267], [0.291, 6.434], [-2.342, 4.206]], "o": [[-4.417, -2.19], [-3.115, 11.399], [-0.978, 2.442], [-0.545, 4.935], [-4.678, 1.843], [2.27, 7.343], [2.308, -0.974], [1.559, -2.59], [0.518, -2.591], [-2.023, -3.363], [9.85, -0.014], [3.511, -6.309]], "v": [[19.623, -30.801], [0.092, -22.237], [-3.022, -11.874], [-12.892, 3.15], [-20.167, 9.075], [-24.839, 22.838], [-0.924, 30.103], [7.91, 23.888], [10.507, 18.189], [8.594, 4.364], [5.124, -10.333], [23.411, -16.663]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.250980407, 0.258823543787, 0.305882364511, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [190.945, 79.881], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.275], [-5.719, -6.219], [-3.613, 2.557], [-0.815, 1.479], [-0.424, 1.568], [-0.231, 2.046], [1.267, 1.29]], "o": [[0, 0.519], [2.388, 2.597], [0.748, -0.53], [0.292, -0.531], [0.201, -0.74], [1.96, -5.619], [-7.501, -7.642]], "v": [[-7.911, -14.04], [-6.351, 10.834], [5.085, 11.87], [8.164, 10.834], [9.117, 7.758], [9.764, 3.579], [10.803, -6.785]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.816000007181, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [202.596, 70.13], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.271, -1.5], [0, 0], [-0.238, -0.856], [-3.205, -1.861], [-3.015, 2.677], [0.673, 4.721], [1.223, 0.09]], "o": [[0, 0], [0.936, 3.908], [0.505, 1.816], [3.205, 1.86], [2.011, -1.784], [-0.361, -8.962], [-1.222, -0.091]], "v": [[-8.645, -10.847], [-7.082, -4.714], [-5.321, 2.432], [-5.321, 11.128], [5.965, 10.375], [7.972, 0.617], [5.596, -12.961]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.816000007181, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [203.645, 88.055], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "“上身/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142.5, 157.5, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0.038, 0.694], [-0.657, 0.679], [-1.36, 1.544], [0.834, -0.028], [-0.099, 0.705], [-0.05, 0.076], [-0.515, 1.227], [0.015, 0.253], [5.646, -2.778], [0.365, -0.47], [0.583, -0.646], [0.459, -0.727], [0.337, -0.885], [0.055, -0.721], [-0.079, -1.392], [0.103, -1.047], [0, 0]], "o": [[0, 0], [0.018, -0.431], [1.177, -0.937], [0.468, -0.758], [1.359, -1.544], [-0.791, -0.372], [0.038, -0.072], [1.362, -2.051], [0.172, -0.41], [-0.254, -0.878], [-1.781, 0.876], [-0.029, 0.046], [-0.588, 0.701], [-0.853, 1.35], [-0.507, 1.927], [-0.007, 1.185], [-0.036, 0.607], [0, 0], [0, 0]], "v": [[4.469, 11.244], [4.634, 8.424], [4.645, 7.08], [6.184, 5.783], [13.704, -2.162], [14.318, -4.229], [10.626, -3.649], [12.822, -6.206], [14.73, -9.156], [14.527, -10.65], [7.256, -8.51], [4.193, -6.256], [2.475, -4.361], [1.036, -2.264], [-0.654, 1.066], [-1.278, 3.964], [-1.437, 7.479], [-1.576, 9.634], [-2.465, 12.515]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-1.36, 1.544], [3.363, -2.951], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [1.359, -1.544], [-3.363, 2.952], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.719, 11.244], [4.196, 9.143], [4.895, 7.08], [6.84, 3.345], [9.86, -3.912], [7.63, -5.604], [2.063, -2.618], [1.915, -2.831], [-5.333, -11.781], [-6.317, -11.369], [-7.994, -12.01], [-8.495, -10.443], [-9.588, -9.705], [-9.277, -7.076], [-10.404, -6.309], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-1.36, 1.544], [3.363, -2.951], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [1.359, -1.544], [-3.363, 2.952], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.719, 11.244], [4.196, 9.143], [4.895, 7.08], [6.84, 3.345], [9.86, -3.912], [7.63, -5.604], [2.063, -2.618], [1.915, -2.831], [-5.333, -11.781], [-6.317, -11.369], [-7.994, -12.01], [-8.495, -10.443], [-9.588, -9.705], [-9.277, -7.076], [-10.404, -6.309], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-0.617, 0.491], [-0.121, 1.553], [0.041, 2.362], [0.037, -1.895], [1.73, 2.481], [0.051, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.376, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [-0.054, -3.133], [-0.088, 4.474], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[1.088, 7.524], [0.565, 5.423], [1.264, 3.36], [3.209, -0.375], [1.457, -8.435], [-0.166, -8.75], [-1.568, -6.338], [-1.716, -6.551], [-8.964, -15.501], [-9.948, -15.089], [-11.625, -15.73], [-12.126, -14.163], [-13.219, -13.425], [-13.166, -11.292], [-14.035, -10.029], [-11.222, -2.318], [-6.645, 6.452], [-6.378, 7.075], [-4.273, 11.946]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [-0.617, 0.491], [-0.121, 1.553], [0.041, 2.362], [0.037, -1.895], [1.73, 2.481], [0.051, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.376, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [-0.054, -3.133], [-0.088, 4.474], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[1.088, 7.524], [0.565, 5.423], [1.264, 3.36], [3.209, -0.375], [1.457, -8.435], [-0.166, -8.75], [-1.568, -6.338], [-1.716, -6.551], [-8.964, -15.501], [-9.948, -15.089], [-11.625, -15.73], [-12.126, -14.163], [-13.219, -13.425], [-13.166, -11.292], [-14.035, -10.029], [-11.222, -2.318], [-6.645, 6.452], [-6.378, 7.075], [-4.273, 11.946]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-0.402, 2.018], [0.465, -2.665], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [0.31, -1.557], [-0.769, 4.408], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.72, 11.244], [4.196, 9.143], [4.896, 7.08], [6.84, 3.345], [5.718, -5.815], [4.059, -5.605], [2.063, -2.618], [1.915, -2.831], [-5.931, -11.387], [-6.915, -10.975], [-8.592, -11.616], [-9.093, -10.049], [-10.186, -9.311], [-9.875, -6.682], [-11.002, -5.915], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-0.402, 2.018], [0.465, -2.665], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [0.31, -1.557], [-0.769, 4.408], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.72, 11.244], [4.196, 9.143], [4.896, 7.08], [6.84, 3.345], [5.718, -5.815], [4.059, -5.605], [2.063, -2.618], [1.915, -2.831], [-5.931, -11.387], [-6.915, -10.975], [-8.592, -11.616], [-9.093, -10.049], [-10.186, -9.311], [-9.875, -6.682], [-11.002, -5.915], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-0.617, 0.491], [-0.121, 1.553], [0.041, 2.362], [0.037, -1.895], [1.73, 2.481], [0.051, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.376, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [-0.054, -3.133], [-0.088, 4.474], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[1.088, 7.524], [0.565, 5.423], [1.264, 3.36], [3.209, -0.375], [1.457, -8.435], [-0.166, -8.75], [-1.568, -6.338], [-1.716, -6.551], [-8.964, -15.501], [-9.948, -15.089], [-11.625, -15.73], [-12.126, -14.163], [-13.219, -13.425], [-13.166, -11.292], [-14.035, -10.029], [-11.222, -2.318], [-6.645, 6.452], [-6.378, 7.075], [-4.273, 11.946]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[0, 0], [0, 0], [-0.617, 0.491], [-0.121, 1.553], [0.041, 2.362], [0.037, -1.895], [1.73, 2.481], [0.051, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.376, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [-0.054, -3.133], [-0.088, 4.474], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[1.088, 7.524], [0.565, 5.423], [1.264, 3.36], [3.209, -0.375], [1.457, -8.435], [-0.166, -8.75], [-1.568, -6.338], [-1.716, -6.551], [-8.964, -15.501], [-9.948, -15.089], [-11.625, -15.73], [-12.126, -14.163], [-13.219, -13.425], [-13.166, -11.292], [-14.035, -10.029], [-11.222, -2.318], [-6.645, 6.452], [-6.378, 7.075], [-4.273, 11.946]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-0.402, 2.018], [0.465, -2.665], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [0.31, -1.557], [-0.769, 4.408], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.72, 11.244], [4.197, 9.143], [4.896, 7.08], [6.84, 3.345], [5.718, -5.815], [4.059, -5.605], [2.063, -2.618], [1.915, -2.831], [-5.931, -11.387], [-6.915, -10.975], [-8.592, -11.616], [-9.093, -10.049], [-10.186, -9.311], [-9.875, -6.682], [-11.002, -5.915], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [-0.616, 0.492], [-0.12, 1.553], [-0.402, 2.018], [0.465, -2.665], [1.73, 2.481], [0.052, 0.075], [1.939, 0.531], [0.159, -0.207], [0.965, -0.505], [0.156, -0.344], [0.187, -0.895], [0.133, -0.85], [0.375, -0.866], [-1.74, -3.406], [-1.532, -1.654], [-0.014, -0.232], [0, 0]], "o": [[0, 0], [-0.191, -0.765], [1.177, -0.937], [0.229, -2.947], [0.31, -1.557], [-0.769, 4.408], [-0.047, -0.067], [-1.605, -2.351], [-0.429, -0.118], [-0.329, 0.426], [-0.964, 0.504], [-0.022, 0.05], [-0.186, 0.896], [-0.134, 0.849], [-0.816, 1.881], [1.757, 3.439], [0.158, 0.171], [0, 0], [0, 0]], "v": [[4.72, 11.244], [4.197, 9.143], [4.896, 7.08], [6.84, 3.345], [5.718, -5.815], [4.059, -5.605], [2.063, -2.618], [1.915, -2.831], [-5.931, -11.387], [-6.915, -10.975], [-8.592, -11.616], [-9.093, -10.049], [-10.186, -9.311], [-9.875, -6.682], [-11.002, -5.915], [-7.591, 1.402], [-2.656, 9.042], [-2.389, 9.665], [-2.215, 12.515]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [0.038, 0.694], [-0.657, 0.679], [-1.36, 1.544], [0.834, -0.028], [-0.099, 0.705], [-0.05, 0.076], [-0.515, 1.227], [0.015, 0.253], [5.646, -2.778], [0.365, -0.47], [0.583, -0.646], [0.459, -0.727], [0.337, -0.885], [0.055, -0.721], [-0.079, -1.392], [0.103, -1.047], [0, 0]], "o": [[0, 0], [0.018, -0.431], [1.177, -0.937], [0.468, -0.758], [1.359, -1.544], [-0.791, -0.372], [0.038, -0.072], [1.362, -2.051], [0.172, -0.41], [-0.254, -0.878], [-1.781, 0.876], [-0.029, 0.046], [-0.588, 0.701], [-0.853, 1.35], [-0.507, 1.927], [-0.007, 1.185], [-0.036, 0.607], [0, 0], [0, 0]], "v": [[4.469, 11.244], [4.634, 8.424], [4.645, 7.08], [6.184, 5.783], [13.704, -2.162], [14.318, -4.229], [10.626, -3.649], [12.822, -6.206], [14.73, -9.156], [14.527, -10.65], [7.256, -8.51], [4.193, -6.256], [2.475, -4.361], [1.036, -2.264], [-0.654, 1.066], [-1.278, 3.964], [-1.437, 7.479], [-1.576, 9.634], [-2.465, 12.515]], "c": true}]}, {"t": 53}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.816000007181, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [164.973, 87.163], "e": [154.596, 86.975], "to": [-1.729, -0.031], "ti": [-1.149, -1.373]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [154.596, 86.975], "e": [165.555, 91.868], "to": [0.601, 0.718], "ti": [-4.402, -1.974]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [165.555, 91.868], "e": [171.867, 95.402], "to": [4.011, 1.799], "ti": [0.228, -0.126]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [171.867, 95.402], "e": [161.322, 91.625], "to": [-0.26, 0.143], "ti": [5.358, 1.887]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [161.322, 91.625], "e": [151.723, 88.559], "to": [-4.5, -1.585], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [151.723, 88.559], "e": [163.914, 92.361], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [163.914, 92.361], "e": [171.867, 95.402], "to": [0, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [171.867, 95.402], "e": [161.323, 91.85], "to": [0, 0], "ti": [5.515, 1.78]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [161.323, 91.85], "e": [151.723, 88.559], "to": [-4.173, -1.347], "ti": [0.495, 0.592]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [151.723, 88.559], "e": [159.169, 87.375], "to": [-0.661, -0.79], "ti": [-3.728, 0.128]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [159.169, 87.375], "e": [164.973, 87.163], "to": [2.753, -0.094], "ti": [-0.938, 0.099]}, {"t": 53}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0], "e": [54]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 15, "s": [54], "e": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [4], "e": [54]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [54], "e": [4]}, {"t": 38}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.599, 0.266], [-2.116, -15.717], [0.171, -5.604], [10.757, -4.023], [-0.054, -1.862], [0, 0], [0, 0], [-0.434, 1.179], [-0.853, 7.17], [1.575, 7.112]], "o": [[-2.599, -0.266], [1.41, 10.479], [-16.772, 2.308], [-5.195, 1.942], [0, 0], [0, 0], [1.256, 0], [1.395, -3.786], [0.864, -7.26], [-1.575, -7.111]], "v": [[23.715, -28.066], [13.793, -10.997], [15.652, 13.127], [-25.642, 22.625], [-33.354, 28.332], [-28.679, 28.332], [25.105, 28.332], [27.92, 26.37], [31.292, 9.937], [31.833, -17.878]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.505999982357, 0.885999977589, 0.875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [204.249, 125.645], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.205, 3.417], [-2.739, -1.05], [0.526, -11.542], [3.217, -9.261], [-2.044, -11.765], [5.741, 0], [4.276, 0.711], [0.016, 0.934], [0.772, 2.972], [-2.886, 5.156], [-5.334, 7.965], [-3.556, -0.264]], "o": [[6.18, 1.414], [9.857, 3.777], [-0.294, 6.444], [-2.044, 5.884], [-9.585, 2.834], [-5.743, 0], [0.639, -1.605], [-0.178, -10.444], [-2.393, -9.222], [3.721, -6.646], [1.141, 1.787], [3.556, 0.265]], "v": [[2.415, -36.29], [15.794, -32.594], [27.714, -15.262], [15.794, 5.566], [15.794, 32.038], [-7.195, 36.29], [-22.222, 35.223], [-21.287, 31.413], [-22.961, 10.667], [-25.354, -12.723], [-11.772, -34.637], [-4.726, -31.561]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.430999995213, 0.823999980852, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [207.763, 128.154], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-7.575, 3.519], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-13.046, 36.606], [7.573, -3.519], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-3.895, 0.747], [-3.965, -27.551], [-11.098, -27.457], [-9.635, 27.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-7.575, 3.519], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [7.573, -3.519], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 6.497], [-12.965, -27.301], [-20.848, -25.957], [-9.635, 27.696], [14.257, 1.497]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-7.575, 3.519], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [7.573, -3.519], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 6.497], [-12.965, -27.301], [-20.848, -25.957], [-9.635, 27.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-8.583, 26.44], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 5.747], [-5.715, -25.051], [-13.848, -26.707], [-12.385, 26.696], [14.257, 1.497]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-8.583, 26.44], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 5.747], [-5.715, -25.051], [-13.848, -26.707], [-12.385, 26.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-11.395, 8.247], [-17.215, -25.801], [-24.598, -25.207], [-12.385, 26.696], [14.257, 1.497]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-11.395, 8.247], [-17.215, -25.801], [-24.598, -25.207], [-12.385, 26.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-8.583, 26.44], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 5.747], [-5.715, -25.051], [-13.848, -26.707], [-12.385, 26.696], [14.257, 1.497]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-8.583, 26.44], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-8.145, 5.747], [-5.715, -25.051], [-13.848, -26.707], [-12.385, 26.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-11.395, 8.247], [-17.215, -25.801], [-24.598, -25.207], [-12.385, 26.696], [14.257, 1.497]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-12.295, 1.037], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-2.939, 36.441], [8.321, -0.702], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-11.395, 8.247], [-17.215, -25.801], [-24.598, -25.207], [-12.385, 26.696], [14.257, 1.497]], "c": true}], "e": [{"i": [[0, 0], [3.446, -6.411], [3.76, -6.709], [0, 0], [0, 0], [-7.575, 3.519], [-8.355, 13.947]], "o": [[-9.011, 2.276], [-3.446, 6.412], [0, 0], [0, 0], [-13.046, 36.606], [7.573, -3.519], [0, 0]], "v": [[22.348, -31.215], [3.663, -18.184], [-3.895, 0.747], [-3.965, -27.551], [-11.098, -27.457], [-9.635, 27.696], [14.257, 1.497]], "c": true}]}, {"t": 53}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.344999998808, 0.760999977589, 0.749000012875, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [173.036, 125.266], "e": [173.181, 125.267], "to": [0.024, 0], "ti": [-0.024, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [173.181, 125.267], "e": [173.181, 125.267], "to": [0, 0], "ti": [0, 0]}, {"t": 31}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 7", "np": 4, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "“下身/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142.5, 157.5, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.355, -2.844], [-6.535, -21.931], [-20.313, -41.171], [0, 0], [1.805, 8.018], [1.522, 1.863], [-4.033, 8.663], [3.89, 6.274], [2.108, 3.386]], "o": [[2.355, 10.96], [-3.533, 4.266], [2.856, 9.583], [0, 0], [-4.582, -24.903], [-2.81, -12.483], [34.734, -3.473], [6.048, -12.996], [-1.158, -1.87], [0, 0]], "v": [[10.185, -66.66], [10.185, -45.954], [-44.348, -9.471], [-9.596, 66.66], [2.764, 64.318], [-6.816, 14.938], [-13.314, -6.581], [44.836, -24.786], [40.945, -54.559], [36.046, -62.441]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.305882364511, 0.317647069693, 0.517647087574, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [187.535, 222.66], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.059, -0.818], [4.796, 2.313], [3.094, -0.647], [-6.166, -3.619], [-5.682, 1.664], [5.566, 5.841]], "o": [[-4.059, 0.817], [-0.669, 0.136], [-4.64, 0.969], [6.168, 3.619], [3.79, -1.11], [-1.15, 2.285]], "v": [[7.473, -2.368], [-5.81, -4.611], [-11.452, -3.436], [-15.576, 3.404], [17.951, 3.404], [15.286, -7.023]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.368627458811, 0.376470595598, 0.517647087574, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [176.669, 300.568], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.072, -1.048], [0.604, -0.036], [-2.893, 1.835], [-1.695, 2.186], [0, 0]], "o": [[1.516, 3.14], [-4.072, 1.048], [9.147, 4.608], [2.892, -1.834], [0, 0], [0, 0]], "v": [[-25.369, -5.405], [-29.33, 2.251], [-36.344, 3.877], [-18.286, 8.037], [-11.406, 2.008], [-13.134, -6.372]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.816000007181, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [203.432, 293.224], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 4, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.931, -1.076], [-1.961, 1.435], [2.065, -0.715], [4.195, 1.818], [2.031, 2.509], [-3.918, -2.047]], "o": [[3.954, 0.718], [1.293, 6.098], [-3.097, 1.072], [-7.656, -3.32], [-2.97, -3.666], [3.918, 2.046]], "v": [[7.668, -0.257], [16.54, -1.332], [15.382, 8.888], [1.088, 5.217], [-14.863, -4.628], [-6.941, -7.914]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.368627458811, 0.376470595598, 0.517647087574, 1], "e": [0.368627458811, 0.376470595598, 0.517647087574, 1]}, {"t": 89}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [115.47, 292.342], "e": [116.345, 292.342], "to": [0.146, 0], "ti": [0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [116.345, 292.342], "e": [115.47, 292.342], "to": [0, 0], "ti": [0.146, 0]}, {"t": 89}], "ix": 2}, "a": {"a": 0, "k": [14.333, 3.892], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0], "e": [-9]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [-9], "e": [0]}, {"t": 89}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 4, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [3.671, 0.433], [2.539, 0.499], [-3.357, 0.676], [-2.299, 1.46], [0, 0]], "o": [[0, 2.698], [-3.67, -0.432], [7.06, 7.853], [3.357, -0.677], [0, 0], [0, 0]], "v": [[1.941, -6.722], [-3.565, -3.323], [-12.879, -4.72], [2.747, 6.046], [11.231, 2.841], [12.479, -3.884]], "c": true}], "e": [{"i": [[0, 0], [3.671, 0.433], [2.539, 0.499], [-3.357, 0.676], [-2.299, 1.46], [0, 0]], "o": [[0, 2.698], [-3.67, -0.432], [7.06, 7.853], [3.357, -0.677], [0, 0], [0, 0]], "v": [[2.515, -7.012], [-3.565, -3.323], [-12.879, -4.72], [2.747, 6.046], [11.231, 2.841], [13.021, -3.363]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [3.671, 0.433], [2.539, 0.499], [-3.357, 0.676], [-2.299, 1.46], [0, 0]], "o": [[0, 2.698], [-3.67, -0.432], [7.06, 7.853], [3.357, -0.677], [0, 0], [0, 0]], "v": [[2.515, -7.012], [-3.565, -3.323], [-12.879, -4.72], [2.747, 6.046], [11.231, 2.841], [13.021, -3.363]], "c": true}], "e": [{"i": [[0, 0], [3.671, 0.433], [2.539, 0.499], [-3.357, 0.676], [-2.299, 1.46], [0, 0]], "o": [[0, 2.698], [-3.67, -0.432], [7.06, 7.853], [3.357, -0.677], [0, 0], [0, 0]], "v": [[1.941, -6.722], [-3.565, -3.323], [-12.879, -4.72], [2.747, 6.046], [11.231, 2.841], [12.479, -3.884]], "c": true}]}, {"t": 89}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976000019148, 0.816000007181, 0.811999990426, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [112.595, 288.014], "ix": 2}, "a": {"a": 0, "k": [6.859, 0.199], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [7], "e": [-2.222]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 40, "s": [-2.222], "e": [7]}, {"t": 89}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 4, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [12.113, -5.718], [8.02, -6.176], [2.153, -9.145], [4.333, -28.679], [0, 0], [-3.613, 7.696], [-1.916, 1.641], [-4.494, 22.565], [0.722, 3.834]], "o": [[-0.539, 5.491], [-12.207, 5.765], [-4.763, 3.667], [-2.96, 12.572], [0, 0], [11.376, -23.443], [6, -12.778], [5.47, -4.687], [2.995, -15.042], [0, 0]], "v": [[24.006, -62.208], [5.027, -45.393], [-33.899, -27.275], [-44.218, -3.717], [-55.158, 58.159], [-44.218, 62.207], [-21.734, 15.497], [-11.085, -7.688], [51.027, -33.893], [54.436, -62.208]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.188235297799, 0.364705890417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [162.833, 222.933], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "“椅子/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142.5, 157.5, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.336], [-18.077, 0], [0, 3.337], [18.076, 0]], "o": [[0, 3.337], [18.076, 0], [0, -3.336], [-18.077, 0]], "v": [[-32.73, -0.001], [0, 6.042], [32.729, -0.001], [0, -6.042]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.556862771511, 0.564705908298, 0.733333349228, 1], "ix": 4}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [210.271, 208.645], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-17.34, -12.326], [17.34, 12.326]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.556862771511, 0.564705908298, 0.733333349228, 1], "ix": 3}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 1.423, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [209.547, 243.551], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[17.497, -12.396], [-17.497, 12.396]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.556862771511, 0.564705908298, 0.733333349228, 1], "ix": 3}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 1.423, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [206.091, 243.407], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[6.546, -43.808], [-6.546, 43.807]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.556862771511, 0.564705908298, 0.733333349228, 1], "ix": 3}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 1.423, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [188.618, 256.382], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-6.042, -45.318], [6.042, 45.318]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.556862771511, 0.564705908298, 0.733333349228, 1], "ix": 3}, "o": {"a": 0, "k": 50, "ix": 4}, "w": {"a": 0, "k": 1.423, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [227.39, 256.885], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "“底部阴影/欢迎回来-图”轮廓 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [142.5, 157.5, 0], "ix": 2}, "a": {"a": 0, "k": [142.5, 157.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-78.701, 0], [0, -6.627], [78.7, 0], [0, 6.627]], "o": [[78.7, 0], [0, 6.627], [-78.701, 0], [0, -6.627]], "v": [[0, -12], [142.5, 0], [0, 12], [-142.5, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.392156869173, 0.392156869173, 0.713725507259, 1], "ix": 4}, "o": {"a": 0, "k": 12, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [142.5, 303], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90, "st": 0, "bm": 0}], "markers": []}