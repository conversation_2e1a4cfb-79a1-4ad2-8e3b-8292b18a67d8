package com.hpbr.bosszhipin.base;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.database.sqlite.SQLiteDatabase;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.apm.lifecycle.AppLifecycleCaptor;
import com.hpbr.bosszhipin.BuildKey;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.base.hook.HookManager;
import com.hpbr.bosszhipin.common.AppTrimMemoryCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.helper.LifeCycleLogHelper;
import com.hpbr.bosszhipin.config.DBConfig;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.config.NightModeConfig;
import com.hpbr.bosszhipin.config.custom.UrlConfigManager;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.get.export.publish.bean.PostVideoDraftBean;
import com.hpbr.bosszhipin.module.contacts.entity.PostVideoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.MessageBean;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.patch.PatchHelper;
import com.hpbr.bosszhipin.router.AppCommonRouter;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;
import com.hpbr.bosszhipin.startup.process.Processer;
import com.hpbr.bosszhipin.utils.PrivacyAnalytics;
import com.hpbr.bosszhipin.utils.io.file.FileSystemSpaceChecker;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.AppBuildConfig;
import com.monch.lbase.LBase;
import com.monch.lbase.orm.LiteOrm;
import com.monch.lbase.orm.db.DataBase;
import com.monch.lbase.orm.db.DataBaseConfig;
import com.monch.lbase.orm.db.TableManager;
import com.monch.lbase.orm.db.impl.SQLiteHelper;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.daemon.PowerManager;
import com.twl.mms.client.MMSServiceSDK;
import com.twl.signer.Signer;
import com.twl.utils.DBUtils;
import com.twl.utils.GsonUtils;
import com.twl.utils.process.ProcessUtil;

import net.bosszhipin.base.SimpleApiRequest;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

import message.handler.dao.MessageDaoFactory;

/**
 * Created by monch on 15/3/30.
 */
@SuppressLint("BZL-Log")
public class App extends BaseApplication {

    private static final String TAG = "SimpleApplication";

    /**
     * App唯一实例
     */
    @SuppressLint("StaticFieldLeak")
    private static App app;
    @SuppressLint("StaticFieldLeak")
    private static Context sContext;


    private static ExecutorService mDbExecutor = Executors.newFixedThreadPool(1, new ThreadFactory() {
        @Override
        public Thread newThread(@NonNull Runnable r) {
            return new Thread(r, "db executor");
        }
    });

    @Override
    protected void attachBaseContext(Context base) {
        try {
            super.attachBaseContext(base);
            // 安装tinker
            PatchHelper.init(base);
            Utils.init(this);//安全模式和安全气垫都在用，方便其他业务场景提前使用，初始化提前
            //线上容灾防护
            CrashProtectInitHelper.init(base, this);
        } catch (Throwable throwable) {
            onAppCrash("action_startup", "attach_base_context", throwable);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // initialization
        App.app = this;
        App.sContext = this;
        try {
            LifeCycleLogHelper.init(this);

            initLBase();
            //隐私上传配置
            PrivacyAnalytics.init(new ReportListener());
            HookManager.init(this);
            PrivacyDelayInitializer.initialize(this);
            // url的配置，必须是第一位的！！！
            UrlConfigManager.initConfig(app);
            Signer.initialize(this.getApplicationContext());

            //这个位置比较合适
            initDeveloperTools();

            // 初始化进程数据
            Processer.initProcessId(this);// 【重要！！！重要！！！重要！！！】其它业务初始化必须都在它之后

            samsungMemoryLeakVersionCompat();

            disableSystemHideAPIDialog();

            initConnectivityManager();
            NightModeConfig.applySetting();
        } catch (Throwable throwable) {
            //避免TLog没有初始化导致，日志没有输入的问题，补了一个Log.e()
            Log.e(TAG, String.format("App onCreate crash , reason = %s", throwable.getMessage()), throwable);
            TLog.error(TAG, throwable, "App onCreate crash , reason = %s", throwable.getMessage());
            onAppCrash("action_startup", "crash",throwable);
            throw throwable;
        }
    }

    public void initDeveloperTools() {
        if (!BuildInfoUtils.isDebug()) {
            return;
        }
        AppCommonRouter.initDeveloperTools(this);
    }

    private void onAppCrash(String action,String type,Throwable throwable) {
        try {
            ApmAnalyzer.create()
                    .action(action, type)
                    .p2(ProcessUtil.getCurProcessName(this))
                    .p3(throwable.getMessage())
                    .p4(getStackTraceString(throwable))
                    .debug()
                    .reportNow();
        } catch (Throwable t) {
            Log.e(TAG, String.format("onAppCreateCrash , reason = %s", t.getMessage()), t);
        }
    }


    private void initConnectivityManager() {
        // https://stackoverflow.com/questions/41431409/connectivitymanager-leaking-not-sure-how-to-resolve
        // https://github.com/square/leakcanary/issues/393
        // https://issuetracker.google.com/issues/37077692
        ConnectivityManager manager = (ConnectivityManager) getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
        L.debug(TAG, String.valueOf(manager));
    }

    /**
     * 解决华为9.0手机 反射调用@Hide API时候系统弹窗
     * https://blog.csdn.net/zpjsmalltime/article/details/86577058
     * <p>
     * APP内部场景
     * F3 VIP续费页面--->打开webView界面--->返回就会复现
     * <p>
     * 弹窗的具体位置
     * {@link android.app.Activity.performStart()#isAppDebuggable||isDlwarningEnabled}
     */
    private void disableSystemHideAPIDialog() {
        if (Build.VERSION.SDK_INT < 28) return;
        try {
            Class clazz = Class.forName("android.app.ActivityThread");
            Method currentActivityThread = clazz.getDeclaredMethod("currentActivityThread");
            currentActivityThread.setAccessible(true);
            Object activityThread = currentActivityThread.invoke(null);
            Field mHiddenApiWarningShown = clazz.getDeclaredField("mHiddenApiWarningShown");
            mHiddenApiWarningShown.setAccessible(true);
            mHiddenApiWarningShown.setBoolean(activityThread, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initLBase() {
        AppBuildConfig appBuildConfig = new AppBuildConfig();
        appBuildConfig.CONFIG = BuildInfoUtils.getInt(BuildKey.CONFIG);
        appBuildConfig.DEBUG = BuildInfoUtils.isDebug();
        TwlAppProvider twlAppProvider = new TwlAppProvider();
        LBase.init(this, this, appBuildConfig, twlAppProvider);
    }

    public static Context getAppContext() {
        return sContext;
    }

    @Override
    public boolean isDebug() {
        return BuildInfoUtils.isDebug();
    }

    /**
     * 获取APP实例，此处为保证APP内只有一个实例，故使用单例
     *
     * @return
     */
    public static App get() {
        return app;
    }


    public static ExecutorService getDbExecutor() {
        return mDbExecutor;
    }

    /**
     * 数据库实例
     */
    private DataBase mDataBase;

    @Override
    public DataBase db() {
        if (mDataBase == null) {
            ContactDoctorFactory.checkNewInstallEnvironment(this);
            mDataBase = LiteOrm.newInstance(new DataBaseConfig(getContext(),
                    DBConfig.DB_NAME, DBConfig.DB_VERSION, new SQLiteHelper.OnUpdateListener() {
                long startTime = System.currentTimeMillis();

                @Override
                public void onCreate(SQLiteDatabase db) {
                    try {
                        new TableManager().checkOrCreateTable(db, MessageBean.class);
                        db.execSQL("create index index_msg_main on MessageTable(myUserId, myRole, mid)");
                        new TableManager().checkOrCreateTable(db, ContactBean.class);
                        db.execSQL("create index index_contact_mian on Contact(friendId, myId, myRole)");

                        createPostVideoTaskTable(db);

                        createPostVideoDraftTable(db);

                        LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.DATA_DB).put("dbCreate", DBConfig.DB_VERSION).info();
                        long maxMId = MessageDaoFactory.getMaxMessageId();
                        if (maxMId > 0) {
                            MessageDaoFactory.setDdbRepair(); //标记是否DB损坏
                            String threadName = Thread.currentThread().getName();
                            long delayTime = System.currentTimeMillis() - startTime;
                            //apm有可能没有初始化，延迟两秒上报
                            AppThreadFactory.POOL.schedule(new Runnable() {
                                @Override
                                public void run() {
                                    ApmAnalyzer.create()
                                            .action(ApmAnalyticsAction.ACTION_SQ_LITE_UPDATE,
                                                    ApmAnalyticsAction.TYPE_SQ_LITE_CORRUPTION)
                                            .p2(String.valueOf(maxMId))
                                            .p3(threadName)
                                            .p4(String.valueOf(delayTime > 5_000))
                                            .report();
                                }
                            }, 2, TimeUnit.SECONDS);
                            LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.DATA_DB).put("dbCorruption", true).info();
                        }
                        SpManager.get().global().edit().putLong("db#create", System.currentTimeMillis()).apply();
                    } catch (Throwable e) {
                        FileSystemSpaceChecker.crashCheckInternalSpace(FileSystemSpaceChecker.CRASH_BY_DEF_DB, e);
                        TLog.info(TAG, "onCreate failed : %s ", e.getMessage());
                        CrashReport.postCatchedException(e);
                    }

                }

                @Override
                public void onUpdate(SQLiteDatabase db, int oldVersion, int newVersion) {
                    try {
                        long time = SystemClock.elapsedRealtime();
                        onDatabaseUpdate(db, oldVersion, newVersion);
                        long elspedTime = SystemClock.elapsedRealtime() - time;
                        TLog.error(TAG, "onUpdate: [%d]", elspedTime);
                        if (elspedTime > 500) {
                            ApmAnalyzer.create()
                                    .action(ApmAnalyticsAction.ACTION_SQ_LITE_UPDATE,
                                            ApmAnalyticsAction.TYPE_SQ_LITE_UPDATE_TOO_LONG)
                                    .param("p2", String.valueOf(elspedTime))
                                    .report();
                        }
                        LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.DATA_DB).put("dbUpdate", oldVersion + "," + newVersion).put("time", elspedTime).info();
                    } catch (Exception e) {
                        e.printStackTrace();
                        FileSystemSpaceChecker.crashCheckInternalSpace(FileSystemSpaceChecker.CRASH_BY_DEF_DB, e);
                        TLog.info(TAG, "onUpdate failed : %s ", e.getMessage());
                        ApmAnalyzer.create()
                                .action(ApmAnalyticsAction.ACTION_SQ_LITE_UPDATE,
                                        ApmAnalyticsAction.TYPE_SQ_LITE_UPDATE_EXCEPTION)
                                .param("p2", e.toString())
                                .report();
                    }
                }

                Exception lastException;

                @Override
                public void onError(Exception e) {
                    TLog.error(TAG, e, "onError failed  ");
                    //控制频率
                    if (lastException == null || (e != null && !TextUtils.equals(e.toString(), lastException.toString()))) {
                        ApmAnalyzer.create()
                                .action("action_db", "exception")
                                .param("p2", String.valueOf(e))
                                .param("p3", Log.getStackTraceString(e))
                                .reportNow();
                    }
                    lastException = e;
                }
            }));
        }
        return mDataBase;
    }

    /**
     * 本地数据库更新回调
     */
    protected void onDatabaseUpdate(SQLiteDatabase db, int oldVersion, int newVersion) {
        L.d("sql", "=====old:" + oldVersion + " new:" + newVersion);
//      db.execSQL();
        switch (oldVersion) {
            case 8:
                //这次升级主要是为了清空Chat数据库中msgId为时间戳的脏数据（5.38）
//                try {
//                    String[] args = {String.valueOf(NotifyUtils.TIME_LAST)};
//                    db.delete("Chat", "msgId > ?", args);
//                } catch (Exception e) {
//
//                }
            case 9:
                if (DBUtils.isTableExist(db, "Contact")) {
                    db.execSQL("alter table Contact add column isNeedComplete BOOLEAN default 'false'");
                    db.execSQL("alter table Contact add column lastRefreshTime INTEGER");
                    db.execSQL("alter table Contact add column securityId TEXT");
                }
            case 10:// 7.09添加 蓝白融合
                db.execSQL("alter table MessageTable add column friendSource INTEGER default 0");
                db.execSQL("create index index_msg_main on MessageTable(myUserId, myRole, mid)");
                db.execSQL("create index index_contact_mian on Contact(friendId, myId, myRole)");
            case 11://7.17版本 get功能 要求更新GET联系人的ID
                //修改联系人的id
                db.execSQL("update Contact set friendId=" + MqttConfig.CHAT_GET_CIRCLE1 + " where friendId=" + MqttConfig.CHAT_GET_CIRCLE + "");
                //修改消息的id
                db.execSQL("update MessageTable set fromUid=" + MqttConfig.CHAT_GET_CIRCLE1 + " where fromUid=" + MqttConfig.CHAT_GET_CIRCLE + "");
            case 12:
                createPostVideoTaskTable(db);
                break;
            case 13:
                db.execSQL("alter table MessageTable add column smid INTEGER NOT NULL DEFAULT 0");
                db.execSQL("alter table Contact add column lastMsgId INTEGER NOT NULL DEFAULT 0");
                break;
            case 14:
                //社区职位视频发布增加草稿
                createPostVideoDraftTable(db);
                break;
        }
    }

    private void createPostVideoTaskTable(@NonNull SQLiteDatabase db) {
        // 821.605【L】视频发布流程优化
        // 发布视频支持后台发布，发布任务需要持久化
        try {
            new TableManager().checkOrCreateTable(db, PostVideoBean.class);
        } catch (Exception e) {
            FileSystemSpaceChecker.crashCheckInternalSpace(FileSystemSpaceChecker.CRASH_BY_DEF_DB, e);
            CrashReport.postCatchedException(e);
        }
    }

    private void createPostVideoDraftTable(@NonNull SQLiteDatabase db) {
        // 发布视频支持后台发布，发布任务需要持久化
        try {
            new TableManager().checkOrCreateTable(db, PostVideoDraftBean.class);
        } catch (Exception e) {
            FileSystemSpaceChecker.crashCheckInternalSpace(FileSystemSpaceChecker.CRASH_BY_DEF_DB, e);
            CrashReport.postCatchedException(e);
        }
    }

    public void exit() {
        exit(false);
    }

    /**
     * 退出进程
     *
     * @param isForce 是否强制退出进程
     */
    public void exit(boolean isForce) {
        finishAll();

        AppLifecycleCaptor.INSTANCE.clear();

        if (isForce
                || (PowerManager.isDaemonEnable(this)
                && MMSServiceSDK.get().isMultiModel())) {
            getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    killProcess(App.getAppContext());
                    System.exit(0);
                }
            }, 300);
        }
    }

    /**
     * 模拟重启
     */
    public static void restartApp() {
        get().finishAll();
        Intent intent = new Intent(App.get(), WelcomeActivity.class);
        AppUtil.startActivity(App.get(), intent, ActivityAnimType.NONE);
    }

    /**
     * 上传隐私 & 非登录 不上传
     */
    class ReportListener implements PrivacyAnalytics.ReportListener {
        @Override
        public void onReporter(PrivacyAnalytics privacyAnalytics) {
            if (AccountHelper.isCurrentLoginStatus()) {
                SimpleApiRequest.POST(ChatUrlConfig.URL_ZPCHAT_COMPLIANCE_REPORT)
                        .addParam("content", GsonUtils.toJson(privacyAnalytics.getData())).execute();
            }
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        AppTrimMemoryCommon.release(level);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        TLog.close();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    /**
     * 代码从Util里面拷过来，不直接调用是不想触发其他类的类加载
     */
    @NonNull
    public static String getStackTraceString(@NonNull Throwable e) {
        String stackTraceString;
        try {
            if (e instanceof StackOverflowError) {
                final StackTraceElement[] stackTrace = e.getStackTrace();
                StringBuilder builder = new StringBuilder();
                builder.append(e.getMessage());
                builder.append("\n");
                int len = Math.min(stackTrace.length, 20); // 最多获取前20帧
                for (int i = 0; i < len; i++) {
                    builder.append(stackTrace[i].toString());
                    builder.append("\n");
                }
                stackTraceString = builder.toString();
            } else {
                stackTraceString = Log.getStackTraceString(e);
            }
        } catch (Throwable ignored) {
            stackTraceString = e.getMessage();
        }

        return stackTraceString == null ? "" : stackTraceString;
    }

}
