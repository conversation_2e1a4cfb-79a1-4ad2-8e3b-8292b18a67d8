package com.hpbr.bosszhipin.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDelegate;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.DialogTaskCommon;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.utils.AppStatusUtil;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.window.FloatWindowManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.activity.LActivity;
import com.monch.lbase.util.L;
import com.twl.anti.AccessibilityChecker;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 15/3/30.
 */
public abstract class BaseActivity extends LActivity {

    private static final String LOG_TAG = BaseActivity.class.getSimpleName();
    public boolean isDestroy = false;
    /**
     * 全局设置 是否是深色模式
     */
    protected boolean darkMode;
    protected boolean isBlackBackendRun = false;

    private int uiMode = -1;
    private boolean isResumed;
    private static boolean isReportUiMode = false;
    @Override
    public Resources getResources() {
        Resources resources = super.getResources();
        if (isResumed && NightUtil.isSupportDark() && uiMode > 0 && AndroidDataStarGray.getInstance().fixDarkTheme() > 0) { //目前测试没啥用了，case 部分手机小米：直播切换横竖屏退出之后异常
            Configuration configuration = resources.getConfiguration();
            int mode = configuration.uiMode & Configuration.UI_MODE_NIGHT_MASK;
            if (uiMode != mode) {
                if (!isReportUiMode) {
                    isReportUiMode = true;
                    ApmAnalyzer.create().action("changeTheme", "uiMode").p3(this.getClass().getName()).report();
                }
                L.e("activity", "=====getResources===:" + this.getClass().getSimpleName() + " resources=" + mode + "   isDarkMode=" + (uiMode == Configuration.UI_MODE_NIGHT_YES));
                uiMode = mode;
                Utils.runOnUiThreadDelayed(() -> getDelegate().onConfigurationChanged(configuration), 100);
            }
        }
        return resources;
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        if (NightUtil.isSupportDark()) {
            boolean shouldUserDarkMode = shouldUserDarkMode();
            this.darkMode = shouldUserDarkMode;
            this.uiMode = shouldUserDarkMode ? Configuration.UI_MODE_NIGHT_YES : Configuration.UI_MODE_NIGHT_NO;
            if (shouldUserDarkMode != NightUtil.isAppDarkMode()) {
                getDelegate().setLocalNightMode(shouldUserDarkMode ? AppCompatDelegate.MODE_NIGHT_YES : AppCompatDelegate.MODE_NIGHT_NO);
            }
            L.i("activity", "=====attachBaseContext===:" + this.getClass().getSimpleName() + "   shouldUserDarkMode=" + shouldUserDarkMode);
        }
        super.attachBaseContext(newBase);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        L.i("activity", "=====onCreate===:" + this.getClass().getSimpleName() + "   isDarkMode=" + NightUtil.isDarkMode(this) + "   isAppDarkMode=" + NightUtil.isDarkMode(Utils.getApp()));
        setNavigationBar();
        // ... 注册用户按HOME键的广播接收器
        registerHomeKeyEventReceiver(this);
        //查询如果可以开启硬件加速，则直接打开硬件加速
        //811版本注：此处老版本限制OPPO手机开启硬件加速的代码已去除：@See: MobileUtil.isSupportHardwareAccelerate()
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED);
    }


    protected boolean shouldUserDarkMode() {
        return NightUtil.isAppDarkMode();
    }

    public void setNavigationBar() {
        if (darkMode) {
            setLightNavigationBar();
        } else {
            setDarkNavigationBar();
        }
    }

    /**
     * 快速切换页面
     * 解决选择跟随系统， 系统切换模式
     */
    protected void resetNightMode() {
        if (NightUtil.isSupportDark()) {
            if (AppCompatDelegate.getDefaultNightMode() == AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM) {//跟随模式的情况下， 切换系统暗黑模式， 不会回调 onNightModeChanged
                boolean darkMode = NightUtil.isDarkMode(this);
                L.i("activity", "=====resetLocalNightMode===:" + this.getClass().getSimpleName() + " Resource#isDarkMode=" + darkMode + " Activity#isDarkMode=" + this.darkMode);
                if (darkMode != this.darkMode) {
                    if (this instanceof MainActivity) { // recreate 黑屏问题:https://zhishu.zhipin.com/docs/oBzxtjcpan2
                        finish();
                        overridePendingTransition(0, 0);
                        Intent intent = getIntent();
                        startActivity(intent);
                        overridePendingTransition(0, 0);
                    } else {
                        recreate();
                    }
                }
            }
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        // 清空所有通知消息
        if (isLoginPage()) NotifyUtils.clearNotifications();
        // 判断当前是否为从后台进入前台的标记
        activityOnResume(this);
        // 全局判断是否为从设置页面回来
        DialogTaskCommon.handlerSettingBack(this);

        if (!isForbidWindow()) {
            FloatWindowManager.getInstance().onResumeWindow(this);
        }
        isResumed = true;
    }


    @Override
    protected void onPause() {
        isResumed = false;
        super.onPause();
        if (!isForbidWindow()) {
            FloatWindowManager.getInstance().onPauseWindow(this);
        }

    }

    @Override
    protected void onNightModeChanged(int mode) {
        super.onNightModeChanged(mode);
        if (NightUtil.isSupportDark()) {
            boolean darkMode = mode == AppCompatDelegate.MODE_NIGHT_YES;
            L.i("activity", "=====onNightModeChanged===:" + this.getClass().getSimpleName() + " mode = " + mode + "     Resource#isDarkMode=" + NightUtil.isDarkMode(this) + "    Activity#isDarkMode=" + this.darkMode);
            if (darkMode != this.darkMode) {
                recreate();
            }
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (NightUtil.isSupportDark()) {
            L.i("activity", "=====onConfigurationChanged===:" + this.getClass().getSimpleName() + "   isDarkMode=" + darkMode + "   isDarkMode=" + NightUtil.isDarkMode(this));
            resetNightMode(); //用于跟随系统时 切换系统暗黑，监听变化
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterHomeKeyEventReceiver(this);
        isDestroy = true;
        L.i("activity", "=====onDestroy===:" + this.getClass().getSimpleName());
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        AccessibilityChecker.checkKeyEvent(this, keyCode, event);
        if (keyCode == KeyEvent.KEYCODE_BACK && baseMonitorBackPress()) {
            AppUtil.finishActivity(this);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 点击物理返回键时，是否调用BaseActivity#onKeyDown中的finishActivity方法
     */
    public boolean baseMonitorBackPress() {
        return true;
    }

    public boolean isLoginPage() {
        return AccountHelper.isCurrentLoginStatus();
    }

    @Override
    public void unregisterReceiver(BroadcastReceiver receiver) {
        try {
            super.unregisterReceiver(receiver);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
    }

    /**
     * 检测app是否处于后台运行的标识
     */
    private static boolean isBackendRun = false;

    private void registerHomeKeyEventReceiver(Context context) {
        // 注册用户按HOME键的广播接收器
        ReceiverUtils.registerSystem(context,
                new IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS), mHomeKeyEventReceiver);
    }

    private void unregisterHomeKeyEventReceiver(Context context) {
        // 解除注册用户按HOME键的广播接收器
        ReceiverUtils.unregisterSystem(context, mHomeKeyEventReceiver);
    }

    private void activityOnResume(Context context) {
        //黑名单 过滤
        if (isBlackBackendRun) return;
        if (isBackendRun) {
            // ... 此处为APP从后台进入前台的处理
            isBackendRun = false;
            AppStatusUtil.onAppStatusIsForeground(context);
        }
    }

    /**
     * 该广播用于接收用户按下HOME键的监听
     */
    private BroadcastReceiver mHomeKeyEventReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (isBackendRun) return;
            String action = intent.getAction();
            if (action.equals(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)) {
                String reason = intent.getStringExtra("reason");
                if (TextUtils.equals(reason, "homekey")) {
                    //表示按了home键,程序到了后台
                    isBackendRun = true;
                    AppStatusUtil.onAppStatusIsBackground();
                }
            }
        }
    };

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        AccessibilityChecker.checkMotionEvent(event, this);
        if (!isForbidWindow()) {
            FloatWindowManager.getInstance().dispatchTouchEvent(event);
        }
        boolean result = false;
        try {
            result = super.dispatchTouchEvent(event);
        } catch (Exception e) {
            L.e(LOG_TAG, e.getMessage());
        }
        return result;
    }

    /**
     * 是否禁止窗口
     */
    protected boolean isForbidWindow() {
        return false;
    }

    //activity自己页面处理了返回事件，自己的view嵌套比较深,拦截view
    public final List<IKeyDownCallback> keyDownList = new ArrayList<>();

    public void registerKeyDownCallBack(@NonNull IKeyDownCallback callback) {
        if (!keyDownList.contains(callback)) {
            keyDownList.add(callback);
        }
    }

    public void unRegisterKeyDownCallBack(@NonNull IKeyDownCallback callback) {
        keyDownList.remove(callback);
    }


    public interface IKeyDownCallback {
        boolean onKeyDown(int keyCode, KeyEvent event);
    }
}