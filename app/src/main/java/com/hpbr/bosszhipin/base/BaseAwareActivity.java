package com.hpbr.bosszhipin.base;

import android.app.Activity;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.LayoutRes;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

/**
 * @param <M>
 * <AUTHOR>
 * BaseAwareActivity
 */
public abstract class BaseAwareActivity<M extends BaseViewModel> extends BaseActivity2 {
    protected M mViewModel;
    protected ViewModelProvider mProvider;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        onBeforeCreate(savedInstanceState);
        super.onCreate(savedInstanceState);
        mProvider = getProvider();
        createViewModel(mProvider);
        setContentView(contentLayout());
        registerLoading();
        registerError();
        onAfterCreate(savedInstanceState);
    }

    protected void registerLoading() {
        if (mViewModel != null) {
            mViewModel.mLoading.observe(this, new Observer<String>() {
                @Override
                public void onChanged(String s) {
                    if (s == null) {
                        dismissProgressDialog();
                    } else {
                        showProgressDialog(s);
                    }
                }
            });
        }
    }

    protected void registerError() {
        if (mViewModel != null) {
            mViewModel.mError.observe(this, this::onError);
        }
    }

    protected void onError(ErrorReason errorReason) {

    }

    protected ViewModelProvider getProvider() {
        return new ViewModelProvider(this);
    }

    protected void createViewModel(ViewModelProvider provider) {
        Class temp = getClass();
        Class<M> z = null;
        while (z == null && null != temp) {
            z = ReflectionUtils.getInstancedGenericClass(temp);
            temp = temp.getSuperclass();
        }
        if (z != null) {
            mViewModel = provider.get(z);
        } else {
            TLog.error("BaseAwareActivity", "mViewModel is null");
        }

    }

    protected abstract @LayoutRes
    int contentLayout();

    /**
     * 发生在 setContentView() 之后
     *
     * @param savedInstanceState
     */
    protected abstract void onAfterCreate(Bundle savedInstanceState);


    protected void onBeforeCreate(Bundle savedInstanceState) {

    }

    public BaseAwareActivity<M> getThis() {
        return this;
    }


    /**
     * 设置Activity中不使用刘海区域
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_DEFAULT：默认情况下，全屏窗口不会使用到刘海区域，非全屏窗口可正常使用刘海区域。
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS：窗口声明使用刘海区域
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_NEVER：窗口声明不使用刘海区域
     */
    protected void setCutOutMode(Activity activity) {
        if (!ActivityUtils.isValid(activity)) return;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // 不使用刘海区域
            WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
            lp.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_NEVER;
            activity.getWindow().setAttributes(lp);
        }
    }

    /**
     * 设置StatusBar和NavigationBar的样式
     */
    protected void setStatusBarAndNavigationBarDarkStyle() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            clearLightStatusBarFlag();
            getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.color_balck));
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setNavigationBarColor(Color.BLACK);
        }
    }

    public M getViewModel() {
        return mViewModel;
    }
}