package com.hpbr.bosszhipin.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.hpbr.bosszhipin.views.BaseBottomDialogFragment;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.error.ErrorReason;

public abstract class BaseAwareBottomDialogFragment<M extends BaseViewModel> extends BaseBottomDialogFragment {
    protected M mViewModel;
    protected ViewModelProvider mProvider;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        mProvider = getProvider();
        createViewModel(mProvider);
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        registerLoading();
        registerError();
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    protected ViewModelProvider getProvider() {
        return new ViewModelProvider(getThis());
    }

    protected void registerLoading() {
        if (mViewModel != null) {
            mViewModel.mLoading.observe(this, this::showLoading);
        }
    }

    protected void showLoading(String s) {
        if (s == null) {
            dismissProgressDialog();
        } else {
            showProgressDialog(s);
        }
    }

    protected void registerError() {
        if (mViewModel != null) {
            mViewModel.mError.observe(this, this::onError);
        }
    }

    protected void onError(ErrorReason errorReason) {

    }


    public BaseAwareBottomDialogFragment<M> getThis() {
        return this;
    }

    protected void createViewModel(ViewModelProvider provider) {
        Class temp = getClass();
        Class<M> z = null;
        while (z == null && null != temp) {
            z = ReflectionUtils.getInstancedGenericClass(temp);
            temp = temp.getSuperclass();
        }
        if (z != null) {
            mViewModel = provider.get(z);
        } else {
            TLog.error("BaseAwareFragment", "mViewModel is null");
        }
    }

}
