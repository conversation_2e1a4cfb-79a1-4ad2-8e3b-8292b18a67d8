package com.hpbr.bosszhipin.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.error.ErrorReason;


/**
 * @param <M>
 * <AUTHOR>
 * BaseAwareFragment
 */
public abstract class BaseAwareFragment<M extends BaseViewModel> extends LazyLoadFragment {

    protected M mViewModel;
    protected ViewModelProvider mProvider;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        mProvider = getProvider();
        createViewModel(mProvider);
        super.onCreate(savedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        registerLoading();
        registerError();
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    protected ViewModelProvider getProvider() {
        return new ViewModelProvider(getThis());
    }

    protected void registerLoading() {
        if (mViewModel != null) {
            mViewModel.mLoading.observe(this, s -> {
                showLoading(s);
            });
        }
    }

    protected void showLoading(String s) {
        if (s == null) {
            dismissProgressDialog();
        } else {
            showProgressDialog(s);
        }
    }

    protected void registerError() {
        if (mViewModel != null) {
            mViewModel.mError.observe(this, errorReason -> {
                onError(errorReason);
            });
        }
    }

    protected void onError(ErrorReason errorReason) {

    }


    public BaseAwareFragment<M> getThis() {
        return this;
    }

    public <T extends BaseViewModel> T getActivityViewModel(@NonNull Class<T> modelClass) {
        return new ViewModelProvider((FragmentActivity) activity).get(modelClass);
    }

    public <T extends BaseViewModel> T getFragmentViewModel(@NonNull Class<T> modelClass) {
        return new ViewModelProvider(this).get(modelClass);
    }

    protected void createViewModel(ViewModelProvider provider) {
        Class temp = getClass();
        Class<M> z = null;
        while (z == null && null != temp) {
            z = ReflectionUtils.getInstancedGenericClass(temp);
            temp = temp.getSuperclass();
        }
        if (z != null) {
            mViewModel = provider.get(z);
        } else {
            TLog.error("BaseAwareFragment", "mViewModel is null");
        }
    }

    @Override
    protected void onViewHidden(boolean isVisibleToUser) {

    }

    @Override
    protected void requestLoading() {

    }

    public void initData() {

    }

}
