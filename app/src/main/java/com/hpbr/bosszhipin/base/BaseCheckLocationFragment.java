package com.hpbr.bosszhipin.base;

import android.Manifest;

import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;

/**
 * Author: <PERSON>
 * Date: 2018/10/08.
 */
public abstract class BaseCheckLocationFragment extends BaseFragment {

    /***
     *
     * 检查是否已经获取定位权限
     * ***/
    public boolean isLocationPermissionGrant() {
        return PermissionManager.checkAllSelfPermissions(App.getAppContext(),
                Manifest.permission.ACCESS_FINE_LOCATION);
    }


    protected abstract void onRejectLocationPermission();

    public void bgAction(int isOpen) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_EDIT_JOB_LOCATION_GPS)
                .param("p", String.valueOf(isOpen))
                .build();
    }

    protected abstract void onGrantLocationPermission();

    public void requestLocation(LocationService.OnLocationCallback locationCallback) {
        final LocationService location = new LocationService(activity);
        location.setOnLocationCallback(locationCallback);
        location.start();
    }
}
