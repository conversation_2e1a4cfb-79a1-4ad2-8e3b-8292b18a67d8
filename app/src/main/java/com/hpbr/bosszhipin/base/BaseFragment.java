package com.hpbr.bosszhipin.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.view.View;

import com.monch.lbase.activity.fragment.LFragment;

/**
 * Created by monch on 15/3/30.
 */
public abstract class BaseFragment extends LFragment {

    @Override
    public void onDestroy() {
        destroy();
        super.onDestroy();
    }

    /**
     * 销毁Fragment时调用该方法
     */
    public void destroy() {
    }

    public void unregisterReceiver(Context context, BroadcastReceiver receiver) {
        try {
            context.unregisterReceiver(receiver);
        } catch (Exception e) {
        }
    }

    public <T extends View> T find(View view, int viewID) {
        return (T) view.findViewById(viewID);
    }

//    /**
//     * 解决方法: http://stackoverflow.com/questions/15207305/getting-the-error-java-lang-illegalstateexception-activity-has-been-destroyed
//     * This seems to be a bug in the newly added support for nested fragments.
//     * Basically, the child FragmentManager ends up with a broken internal state when it is detached from the activity.
//     * A short-term workaround that fixed it for me is to add the following to onDetach() of every Fragment which you call getChildFragmentManager()
//     */
//    @Override
//    public void onDetach() {
//        super.onDetach();
//        try {
//            Field childFragmentManager = Fragment.class.getDeclaredField("mChildFragmentManager");
//            childFragmentManager.setAccessible(true);
//            childFragmentManager.set(this, null);
//        } catch (NoSuchFieldException e) {
//            throw new RuntimeException(e);
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
//    }

    public String getPageTitle() {
        return "";
    }
}
