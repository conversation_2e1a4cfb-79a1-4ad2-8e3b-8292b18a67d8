package com.hpbr.bosszhipin.base;

import android.app.Activity;
import android.os.Build;
import android.view.WindowManager;

import com.twl.utils.ActivityUtils;

/**
 * 直播基类，所有直播都基础此类，用来判断是否在直播界面的问题
 */
public class BaseLiveActivity extends BaseActivity {

    /**
     * 设置Activity中不使用刘海区域
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_DEFAULT：默认情况下，全屏窗口不会使用到刘海区域，非全屏窗口可正常使用刘海区域。
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_ALWAYS：窗口声明使用刘海区域
     * LAYOUT_IN_DISPLAY_CUTOUT_MODE_NEVER：窗口声明不使用刘海区域
     *
     * @param activity
     */
    protected void setCutOutMode(Activity activity) {
        if (!ActivityUtils.isValid(activity)) return;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            // 不使用刘海区域
            WindowManager.LayoutParams lp = activity.getWindow().getAttributes();
            lp.layoutInDisplayCutoutMode = WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_NEVER;
            activity.getWindow().setAttributes(lp);
        }
    }

}
