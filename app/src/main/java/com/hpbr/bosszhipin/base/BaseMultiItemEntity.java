package com.hpbr.bosszhipin.base;

import com.chad.library.adapter.base.entity.MultiItemEntity;

/**
 * @ClassName ：BaseMultiItemEntity
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/8/9  9:10 下午
 */
public class BaseMultiItemEntity<T> implements MultiItemEntity {

    /*条目Type*/
    private int itemType;
    /*数据*/
    private T data;

    public BaseMultiItemEntity(int itemType, T data) {
        this.itemType = itemType;
        this.data = data;
    }

    @Override
    public int getItemType() {
        return itemType;
    }

    public void setItemType(int itemType) {
        this.itemType = itemType;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
