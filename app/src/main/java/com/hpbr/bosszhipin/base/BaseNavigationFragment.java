package com.hpbr.bosszhipin.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * @ClassName ：BaseNavigationFragment
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  9:22 PM
 */
public abstract class BaseNavigationFragment<M extends BaseViewModel> extends BaseAwareFragment<M> {
    private View rootView = null;
    private boolean isFragmentViewInit = false;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (rootView == null) {
            rootView = super.onCreateView(inflater, container, savedInstanceState);
        } else {
            ViewGroup parent = (ViewGroup) rootView.getParent();
            if (parent != null) parent.removeView(rootView);
        }
        return rootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        if (!isFragmentViewInit) {
            super.onViewCreated(view, savedInstanceState);
            requireActivity().getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
                @Override
                public void handleOnBackPressed() {
                    onBackPressed();
                }
            });
            isFragmentViewInit = true;
        }
    }


    protected void onBackPressed() {

    }
}
