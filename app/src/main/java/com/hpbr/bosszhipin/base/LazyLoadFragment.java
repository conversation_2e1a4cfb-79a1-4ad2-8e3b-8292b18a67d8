package com.hpbr.bosszhipin.base;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: Zhou<PERSON><PERSON>
 * Date: 2017/9/12.
 */
public abstract class LazyLoadFragment extends BaseFragment {

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(getLayoutResId(), container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews(view);
        getData();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        onViewHidden(isVisibleToUser);
        getData();
    }

    @LayoutRes
    protected abstract int getLayoutResId();

    protected abstract void initViews(View view);

    protected abstract void onViewHidden(boolean isVisibleToUser);

    public void getData() {
        if (isAdded() && getUserVisibleHint()) {
            requestLoading();
        }
    }

    protected abstract void requestLoading();
}
