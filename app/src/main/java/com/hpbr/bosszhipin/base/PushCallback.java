package com.hpbr.bosszhipin.base;

import android.content.Intent;

import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.push.IPushCallback;
import com.hpbr.bosszhipin.push.PushSdkManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.monch.lbase.util.LText;
import com.twl.mms.service.AppStatus;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class PushCallback implements IPushCallback {


    @Override
    public void onReceiveMessage(int type, String message) {
        String pushMethods = getPushMethods(type);
        handlerPushMessage(message, pushMethods);
    }

    @Override
    public void onSystemNotificationClicked(int type, Map<String, String> message) {
        Intent intent = new Intent(App.get(), WelcomeActivity.class);
        String pushUrl = null;
        long fromUserId = 0;
        if (message != null && !message.isEmpty()) {
            if (message.containsKey("msgId")) {
                intent.putExtra(Constants.MAIN_MSG_ID_KEY, message.get("msgId"));
            }
            if (message.containsKey("fromId")) {
                fromUserId = LText.getLong(message.get("fromId"));
                intent.putExtra(Constants.MAIN_FROM_ID_KEY, message.get("fromId"));
            }

            if (message.containsKey(Constants.MAIN_MSG_SERVER_PUSH_TYPE)) {
                intent.putExtra(Constants.MAIN_MSG_SERVER_PUSH_TYPE, message.get(Constants.MAIN_MSG_SERVER_PUSH_TYPE));
            }

            if (message.containsKey("landing")) {
                pushUrl = message.get("landing");
            }
            intent.putExtra(Constants.NOTIFY_STRING_URL, ZPManager.getPushDefaultUrl(fromUserId, pushUrl));
            intent.putExtra(Constants.MAIN_MSG_PUSH_MOTHED, getPushMethods(type));
            intent.putExtra(Constants.MAIN_MSG_PUSH_BIZID, message.get("bizId"));
            intent.putExtra(Constants.MAIN_MSG_PUSH_BIZTYPE, message.get("bizType"));
            intent.putExtra(Constants.MAIN_MSG_PUSH_SYSTEM, 1);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
//            App.get().startActivity(intent);
            AppUtil.startActivity(App.get(), intent);

            /***
             * 系统push点击打点
             * ***/
            AnalyticsFactory.create()
                    .action(AnalyticsAction.ACTION_PUSH_CLICK)
                    .param("p2", message.get("msgId"))
                    .param("p3", AppStatus.isForeground() ? "foreground" : "background")
                    .param("p4", getPushMethods(type))
                    .param("p8", message.get(Constants.MAIN_MSG_SERVER_PUSH_TYPE))
                    .param("p9", message.get("bizId"))
                    .param("p12", message.get("bizType"))
                    .param("p13", message.get("bizId"))
                    .param("p14", message.get("uuid"))
                    .build();
        }
    }

    private String getPushMethods(int type) {
        String pushMethods;
        if (type == PushSdkManager.PUSH_TYPE_MI) {
            pushMethods = "xiaomi-sys";
        } else if (type == PushSdkManager.PUSH_TYPE_HW) {
            pushMethods = "huawei-sys";
        } else if (type == PushSdkManager.PUSH_TYPE_HONOR) {
            pushMethods = "honor-sys";
        } else if (type == PushSdkManager.PUSH_TYPE_HW_PASS_THOUGH) {
            pushMethods = "huawei-sdk";
        } else if (type == PushSdkManager.PUSH_TYPE_OPPO) {
            pushMethods = "oppo-sys";
        } else if (type == PushSdkManager.PUSH_TYPE_VIVO) {
            pushMethods = "vivo-sys";
        }else if (type == PushSdkManager.PUSH_TYPE_MEIZU) {
            pushMethods = "meizu-sys";
        }else {
            pushMethods = "xiaomi-sdk";
        }
        return pushMethods;
    }

    private void handlerPushMessage(String string, String pushMethods) {
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(string);
        } catch (JSONException e) {
            jsonObject = null;
            MException.printError("第三方push消息异常", e);
        }
        if (jsonObject == null) return;
        String content = jsonObject.optString("content");
        String pushUrl = jsonObject.optString("landing");
        long msgId = jsonObject.optLong("msgId");
        long fromUserId = jsonObject.optLong("fromId");
//        String uuid = jsonObject.optString("uuid");

        if (fromUserId == UserManager.getUID()) {
            // 如果这一条Push的发送者是当前登录用户的话，直接返回
            return;
        }

        String serverPushType = jsonObject.optString(Constants.MAIN_MSG_SERVER_PUSH_TYPE);

        long taskId = jsonObject.optLong("taskId");
        String soundUrl = jsonObject.optString("soundUri");
        ContactManager.getInstance().refreshContacts();        //这个对方有一次联系人更新
        NotifyUtils.handleArrivedMessageNotification(App.get(), pushMethods, content, pushUrl, msgId, fromUserId, soundUrl, serverPushType);
    }
}
