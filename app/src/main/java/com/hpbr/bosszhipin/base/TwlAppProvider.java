package com.hpbr.bosszhipin.base;


import android.Manifest;
import android.content.Context;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.config.custom.UrlConfigManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;
import com.hpbr.bosszhipin.startup.process.Processer;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.PreloadAppFlagUtil;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.hpbr.bosszhipin.utils.status.AppFlagUtil;
import com.monch.lbase.HttpCommonParams;
import com.monch.lbase.LBase;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.Inspector;
import com.twl.http.HttpTraceIdGenerator;
import com.twl.provider.ITwlAppProvider;
import com.twl.utils.NetworkUtils;

import net.bosszhipin.base.Constant;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by wangtian on 2017/8/26.
 */

public class TwlAppProvider implements ITwlAppProvider {

    /**************************************************/
    @Override
    public String getPackageName() {
        return PackageConfigContants.PACKAGE_NAME;
    }

    @Override
    public String getSecretKey() {
        return UserManager.getSecretKey();
    }

    @Override
    public Context getContext() {
        return App.get().getContext();
    }

    @Override
    public String getFullUserAgent() {
        return MobileUtil.getFullUserAgent();
    }

    @Override
    public boolean isMainProcess() {
        return Processer.isMainProcess();
    }

    @Override
    public HttpCommonParams getHttpCommonParams(boolean isNeedLocationInfo) {
        HttpCommonParams httpCommonParams = new HttpCommonParams();

        httpCommonParams.v = MobileUtil.getVersion();
        httpCommonParams.curidentity = UserManager.getUserRole().get() + "";
        httpCommonParams.app_id = PackageConfigContants.PACKAGE_APP_ID + "";
        httpCommonParams.version = Build.VERSION.RELEASE;
        httpCommonParams.channel = MobileUtil.getChannel();
        httpCommonParams.preload_app_flag = PreloadAppFlagUtil.getPreloadAppFlag();
        httpCommonParams.tinker_id = AppFlagUtil.getTinkerId();
        httpCommonParams.tinker_new_id = AppFlagUtil.getTinkerNewId();
        httpCommonParams.model = MobileUtil.getDeviceType();

        //定位权限是否开启
        httpCommonParams.hasLocPer = getLocPer();
        httpCommonParams.isNeedLocationInfo = isNeedLocationInfo; // 业务需要定位相关信息
        httpCommonParams.isNeedDeviceInfo = canDeviceInfoStatus(); // 业务需要设备相关信息
        httpCommonParams.uniqid = MobileUtil.getUniqId(App.getAppContext());
        if (httpCommonParams.isNeedDeviceInfo) {
//            httpCommonParams.imei = MobileUtil.getRealImei(App.get().getContext());
            httpCommonParams.oaid = MobileUtil.getOAID();
            httpCommonParams.oaid_honor = MobileUtil.getHMOAID();
            httpCommonParams.did = MobileUtil.getDid();
//            httpCommonParams.umid = MobileUtil.getUmid();
        }

        if (1 == httpCommonParams.hasLocPer) {
            if (httpCommonParams.isNeedLocationInfo) {
                TLog.debug("HttpCommonParams", "case %d add", 2);
                if (AndroidDataStarGray.getInstance().isUploadWifiInfo()) {
                     httpCommonParams.ssid = NetworkUtils.getWifiSSID(LBase.getContext());
                    httpCommonParams.bssid = NetworkUtils.getWifiBSSID(LBase.getContext());
                }
                httpCommonParams.longitude = LocationService.getLongitude();
                httpCommonParams.latitude = LocationService.getLatitude();
            }
        }

        httpCommonParams.dzt = Inspector.getInstance(App.getAppContext()).getDeviceType();
        httpCommonParams.sub_source = MobileUtil.SUB_SOURCE;
        httpCommonParams.is_bg_req = App.get().isForeground()?0:1;
        httpCommonParams.tourist = CommonConfigManager.getInstance().isTourist();
        return httpCommonParams;
    }


    private static int getLocPer() {
        if (LocationPermissionHelper.isLocPer == -1) {
            refreshLocPer();
        }
        return LocationPermissionHelper.isLocPer;
    }

    public static void refreshLocPer() {
        LocationPermissionHelper.isLocPer = PermissionManager.checkAllSelfPermissions(App.getAppContext(), Manifest.permission.ACCESS_FINE_LOCATION) ? 1 : 0;
    }


    @Override
    public void printError(Throwable t) {
        MException.printError(t);
    }

    @Override
    public void printError(String tag, Throwable t) {
        MException.printError(tag, t);
    }

    @Override
    public boolean isCurrentLoginStatus() {
        return UserManager.isCurrentLoginStatus();
    }

    @Override
    @Nullable
    public LevelBean getCapital(@NonNull String provinceName) {
        return VersionAndDatasCommon.getInstance().getCapital(provinceName);
    }

    @Override
    public String getCityCode(String city) {
        return VersionAndDatasCommon.getInstance().getCityCode(city);
    }

    @Override
    public String getCityCode(String city, String district) {
        if (TextUtils.isEmpty(district)) {
            return getCityCode(city);
        }
        String cityCode = VersionAndDatasCommon.getInstance().getCityCode2(city);
        if ((cityCode == null || "0".equals(cityCode))) {
            TLog.info("getCityCode", "getCityCode city = %s  district = %s cityCode = %s", city, district, cityCode);
            return getCityCode(district);
        }
        return cityCode;
    }

    @Override
    public String getSubCityCode(String city, String subCity, int type) {
        return VersionAndDatasCommon.getInstance().getSubCityCode(city, subCity, type);
    }


    @Override
    public String getVersion() {
        return MobileUtil.getVersion();
    }

    @Override
    public Map<String, String> getExtraHeader() {
        Map<String, String> extraHeader = new HashMap<>();
        String traceIdStr = HttpTraceIdGenerator.generate();
        extraHeader.put(Constant.ReqHeader.HEADER_TRACE_ID, traceIdStr);
        extraHeader.put(Constant.ReqHeader.HEADER_ZP_TAG, UrlConfigManager.getZPTagText(App.get()));
        extraHeader.put(Constant.ReqHeader.HEADER_T2, UserManager.getToken());
        return extraHeader;
    }

    boolean canDeviceInfoStatus() {
        return PrivacyDelayInitializer.getInstance().isCompleted();
    }
}