package com.hpbr.bosszhipin.base.helper.sync;

import java.io.Serializable;
import java.util.Map;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2024/7/12 14:16
 *     desc   :
 *     version:
 * </pre>
 */
public class ApmConfigBean implements Serializable {
    private static final long serialVersionUID = 5792892564516800486L;

    public String pub_config_json_string;
    public String pri_config_json_string;
    public Map<String, Object> disposable_config;

    @Override
    public String toString() {
        return "ConfigBean{" +
                "pub_config_json_string='" + pub_config_json_string + '\'' +
                ", pri_config_json_string='" + pri_config_json_string + '\'' +
                ", disposable_config=" + disposable_config +
                '}';
    }
}
