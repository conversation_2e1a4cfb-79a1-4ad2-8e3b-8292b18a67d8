package com.hpbr.bosszhipin.base.helper.sync;

import androidx.annotation.Nullable;

import com.bzl.platform.BuildKey;
import com.bzl.platform.log.Logger;
import com.bzl.platform.provider.BuildInfoProvider;
import com.hpbr.apm.common.utils.AESOperator;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 直接移到到apm算了，当做 APMConfigLite
 */
public class ApmConfigMan {
    private static final String TAG = "ApmConfigMan";
    private static final String HOST = "https://apm-and.zhipin.com/api/zpApm/";
    private static final String URL_GET_CONFIG = HOST + "user/config/get.json";

    @Nullable
    public static ApmConfigBean getConfigSync(String userId, String did) {
        String apmKey = BuildInfoProvider.isDebuggable() ? BuildInfoProvider.getString(BuildKey.APM_KEY_DEBUG, "") : BuildInfoProvider.getString(BuildKey.APM_KEY_RELEASE, "");
        String iv = BuildInfoProvider.isDebuggable() ? BuildInfoProvider.getString(BuildKey.APM_IV_DEBUG, "") : BuildInfoProvider.getString(BuildKey.APM_IV_RELEASE, "");
        Map<String, String> params = new HashMap<>();
        params.put("appKey", apmKey);
        params.put("userId", encUserId(userId, apmKey, iv));
        params.put("did", encDId(did, apmKey, iv));
        return getConfigSync(params);
    }

    public static String encUserId(String userId, String apmKey, String iv) {
        try {
            return AESOperator.encrypt(
                    userId,
                    apmKey,
                    iv
            );
        } catch (Exception e) {
            Logger.e(TAG, "get userid", e);
        }
        return "";
    }

    private static String encDId(String did, String apmKey, String iv) {
        try {
            return AESOperator.encrypt(
                    did,
                    apmKey,
                    iv
            );
        } catch (Exception e) {
            Logger.e(TAG, "get userid", e);
        }
        return "";
    }

    public static ApmConfigBean getConfigSync(Map<String, String> params) {
        ApmConfigNetworkHelper.Result zpData = ApmConfigNetworkHelper.getSync(URL_GET_CONFIG, params);
        if (zpData.throwable != null) {
            Logger.e(TAG, "config get fail", zpData.throwable);
            return null;
        }

        if (zpData.resp == null) {
            Logger.e(TAG, "config resp null");
            return null;
        }


        try {
            String content = zpData.resp.optString("content");

            JSONObject contentJson = new JSONObject(content);
            JSONObject pubConfig = contentJson.optJSONObject("pub_config");
            JSONObject priConfig = contentJson.optJSONObject("pri_config");
            JSONObject disposableConfig = contentJson.optJSONObject("disposable_config");

            String pub_config_json_string = pubConfig != null ? pubConfig.optString("jsonObjectString") : null;
            String pri_config_json_string = priConfig != null ? priConfig.optString("jsonObjectString") : null;
            Map<String, Object> disposable_config = ApmConfigNetworkHelper.jsonToMap(disposableConfig);

            ApmConfigBean result = new ApmConfigBean();
            result.pub_config_json_string = pub_config_json_string;
            result.pri_config_json_string = pri_config_json_string;
            result.disposable_config = disposable_config;
            return result;
        } catch (Throwable t) {
            Logger.e(TAG, "config get fail", t);
        }
        return null;
    }
}
