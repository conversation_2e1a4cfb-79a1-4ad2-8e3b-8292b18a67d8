package com.hpbr.bosszhipin.base.helper.sync;

import androidx.annotation.Nullable;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
public class ApmConfigNetworkHelper {
    public interface Callback {
        void onResponse(JSONObject zpData);
        void onError(Exception e);
    }

    // Result类表示网络请求的结果
    public static class Result {
        public final boolean isSuccess;
        @Nullable
        public Throwable throwable;
        @Nullable
        public JSONObject resp;

        public Result() {
            this.isSuccess = true;
        }

        public Result(@Nullable JSONObject resp) {
            this.resp = resp;
            this.isSuccess = true;
        }
        public Result(@Nullable Throwable t) {
            this.throwable = t;
            this.isSuccess = false;
        }
    }

    public static Result getSync(String requestUrl, Map<String, String> params) {
        HttpURLConnection urlConnection = null;
        JSONObject jsonResponse = null;
        Exception exception = null;

        try {
            // Build the URL with parameters if provided
            StringBuilder urlWithParams = new StringBuilder(requestUrl);
            if (params != null && !params.isEmpty()) {
                urlWithParams.append("?");
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (urlWithParams.length() > 0 && !urlWithParams.toString().endsWith("?")) {
                        urlWithParams.append("&");
                    }
                    urlWithParams.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                }
            }

            URL url = new URL(urlWithParams.toString());
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("GET");
            urlConnection.setConnectTimeout(5000);
            urlConnection.setReadTimeout(5000);

            // Get the response
            int responseCode = urlConnection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                jsonResponse = new JSONObject(response.toString());

                int code = jsonResponse.optInt("code", -1);
                String message = jsonResponse.optString("message", "");
                if (code == 0) {
                    JSONObject zpData = new JSONObject(jsonResponse.optString("zpData", "{}"));
                    return new Result(zpData); // Success response
                } else {
                    return new Result(new IOException(String.format("code: %s, message: %s", code, message))); // Error response
                }
            } else {
                throw new IOException("HTTP error code: " + responseCode);
            }
        } catch (Exception e) {
            exception = e;
            return new Result(exception); // Error response
        } finally {
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }
    }

    // 将 JSONObject 转换为 Map<String, Object>
    public static Map<String, Object> jsonToMap(JSONObject json) throws JSONException {
        if (json == null) {
            return null;
        }
        Map<String, Object> retMap = new HashMap<>();

        if (json != JSONObject.NULL) {
            retMap = toMap(json);
        }
        return retMap;
    }

    public static Map<String, Object> toMap(JSONObject object) throws JSONException {
        Map<String, Object> map = new HashMap<>();

        Iterator<String> keysItr = object.keys();
        while (keysItr.hasNext()) {
            String key = keysItr.next();
            Object value = object.get(key);

            if (value instanceof JSONObject) {
                value = toMap((JSONObject) value);
            }
            map.put(key, value);
        }
        return map;
    }
}
