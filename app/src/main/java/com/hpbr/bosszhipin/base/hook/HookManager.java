package com.hpbr.bosszhipin.base.hook;

import android.content.Context;
import android.os.Build;

import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.monch.lbase.util.LText;
import com.twl.utils.process.ProcessUtil;

import java.util.concurrent.atomic.AtomicInteger;

public final class HookManager {
    private static final String TAG = "HookManager2";
    public static AtomicInteger gApiCallCount = new AtomicInteger(0);

    public static void init(Context context) {
        //只针对Android Q以及以上hook
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            return;
        }

        //非华为手机不hook
        if (!isHuawei()) {
            return;
        }

        //子进程不hook
        if (!ProcessUtil.isMainProcess(context)) {
            return;
        }

        //如果服务下发关闭不hook
        if (CommonConfigManager.getInstance().isCloseHookLocationApi()) {
            return;
        }

        WifiManagerHook.init(context);
        //callback较多就不hook了
//        LocationManagerHook.init(context);
//        TelephonyHook.init(context);
//        TelephonyRegistyHook.init(context);
    }

    private static boolean isHuawei() {
        String brand = Build.BRAND;
        String manu = Build.MANUFACTURER;
        if (!LText.empty(brand)) brand = brand.toUpperCase();
        if (!LText.empty(manu)) manu = manu.toUpperCase();
        if (LText.equal(brand, "HONOR") || LText.equal(manu, "HUAWEI")) {
            return true;
        }
        return false;
    }
}
