package com.hpbr.bosszhipin.base.hook;

import android.content.Context;
import android.location.LocationManager;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

final class LocationManagerHook {
    private static Class iLocationManager;
    private static Field serviceField;
    public static void init(Context context) {
        try {
            iLocationManager = Class.forName("android.location.ILocationManager");
            serviceField = LocationManager.class.getDeclaredField("mService");
            serviceField.setAccessible(true);
            hook(context);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static void log(String format, Object... objects) {
        LogUtils.log("LocationManagerHook", format, objects);
    }

    private static void hook(Context context) throws IllegalAccessException {
        LocationManager wifi = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        Object realIwm = serviceField.get(wifi);
        ILMInvocationHandler iwmInvocationHandler = new ILMInvocationHandler(realIwm);
        serviceField.set(wifi,
                Proxy.newProxyInstance(iLocationManager.getClassLoader(), new Class[]{iLocationManager},
                        iwmInvocationHandler));
    }

    private static class ILMInvocationHandler implements InvocationHandler {
        private Object real;
        private Object lastLocation;

        public ILMInvocationHandler(Object real) {
            this.real = real;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            log("invoke: %s : [%d]", methodName, HookManager.gApiCallCount.get());
            switch (methodName) {
                case "requestLocationUpdates": {
                    log("requestLocationUpdates invoke: %d", HookManager.gApiCallCount.incrementAndGet());
                    new Exception().printStackTrace();
//                    return null;
                }
                break;
                case "addGpsStatusListener": {
                    log("addGpsStatusListener invoke: %d", HookManager.gApiCallCount.incrementAndGet());
//                    return null;
                }
                break;
                case "getLastLocation": {
                    log("getLastLocation invoke: %d", HookManager.gApiCallCount.incrementAndGet());
//                    if (lastLocation == null) {
//                        log("getLastLocation invoke: %d", HookManager.gApiCallCount.incrementAndGet());
//                        lastLocation = method.invoke(real, args);
//                        return lastLocation;
//                    }
//                    return lastLocation;
                    break;
                }
            }
            return method.invoke(real, args);
        }
    }
}
