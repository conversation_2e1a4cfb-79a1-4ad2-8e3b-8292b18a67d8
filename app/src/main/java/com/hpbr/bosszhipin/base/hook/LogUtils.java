package com.hpbr.bosszhipin.base.hook;

import android.util.Log;

import com.hpbr.bosszhipin.base.App;


final class LogUtils {
    private static final String TAG = "HookManager";

    public static void log(String tag, String format, Object... objects) {
        if (App.get().isDebug()) {
            String log = objects == null ? format : String.format(format, objects);
            Log.e(TAG, String.format("%s: %s", tag, log));
        }
    }
}
