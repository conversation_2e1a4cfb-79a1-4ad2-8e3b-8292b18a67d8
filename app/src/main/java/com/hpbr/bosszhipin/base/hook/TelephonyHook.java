package com.hpbr.bosszhipin.base.hook;

import android.content.Context;
import android.os.IBinder;
import android.os.IInterface;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedDeque;

public final class TelephonyHook {

    public static void init(Context context) {
        try {
            Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            Method getService = serviceManager.getDeclaredMethod("getService", String.class);
            IBinder rawBinder = (IBinder) getService.invoke(null, Context.TELEPHONY_SERVICE);
            IBinder hookedBinder = (IBinder) Proxy.newProxyInstance(serviceManager.getClassLoader(),
                    new Class<?>[]{IBinder.class},
                    new ITelephonyProxyInvocationHandler(rawBinder));

            Field cacheField = serviceManager.getDeclaredField("sCache");
            cacheField.setAccessible(true);
            Map<String, IBinder> cache = (Map) cacheField.get(null);
            cache.put(Context.TELEPHONY_SERVICE, hookedBinder);
        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    private static void log(String format, Object... objects) {
        LogUtils.log("Telephony", format, objects);
    }


    static class ITelephonyBinderInvocationHandler implements InvocationHandler {
        ConcurrentLinkedDeque<String> callList = new ConcurrentLinkedDeque<>();
        Object allCellInfo = null;
        Object mCellLocation;
        Object base;

        private ITelephonyBinderInvocationHandler(IBinder base, Class<?> stubClass) {
            try {
                Method asInterfaceMethod = stubClass.getDeclaredMethod("asInterface", IBinder.class);
                this.base = asInterfaceMethod.invoke(null, base);
            } catch (Exception e) {
                throw new RuntimeException("hooked failed!");
            }
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            log("invoke: %s : [%d]", methodName, HookManager.gApiCallCount.get());
            switch (methodName) {
                case "getAllCellInfo": {
                    if (allCellInfo == null && !callList.contains(methodName)) {
                        log("getAllCellInfo invoke: %d", HookManager.gApiCallCount.incrementAndGet());
                        callList.add(methodName);
                        allCellInfo = method.invoke(base, args);

                    }
                    return allCellInfo;
                }
                case "getCellLocation": {
                    if (mCellLocation == null && !callList.contains(methodName)) {
                        log("getCellLocation invoke: %d", HookManager.gApiCallCount.incrementAndGet());
                        callList.add(methodName);
                        mCellLocation = method.invoke(base, args);
                    }
                    return mCellLocation;
                }
            }
            return method.invoke(base, args);
        }


    }

    static class ITelephonyProxyInvocationHandler implements InvocationHandler {
        IBinder base;
        Class<?> stub;
        Class<?> iinterface;
        Object mObject;

        public ITelephonyProxyInvocationHandler(IBinder base) {
            this.base = base;
            try {
                this.stub = Class.forName("com.android.internal.telephony.ITelephony$Stub");
                this.iinterface = Class.forName("com.android.internal.telephony.ITelephony");
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if ("queryLocalInterface".equals(method.getName())) {
                if (mObject == null) {
                    mObject = Proxy.newProxyInstance(proxy.getClass().getClassLoader(),

                            // asInterface 的时候会检测是否是特定类型的接口然后进行强制转换
                            // 因此这里的动态代理生成的类型信息的类型必须是正确的
                            new Class[]{IBinder.class, IInterface.class, this.iinterface},
                            new ITelephonyBinderInvocationHandler(base, stub));
                }
                return mObject;
            }
            return method.invoke(base, args);
        }
    }
}
