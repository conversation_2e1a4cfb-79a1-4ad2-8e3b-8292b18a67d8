package com.hpbr.bosszhipin.base.hook;

import android.content.Context;
import android.os.IBinder;
import android.os.IInterface;
import android.telephony.CellLocation;
import android.telephony.PhoneStateListener;
import android.telephony.ServiceState;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;

import java.lang.ref.WeakReference;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Iterator;
import java.util.Map;
import java.util.WeakHashMap;

public final class TelephonyRegistyHook {
    private static final String SERVER_NAME = "telephony.registry";
    private static Context gContext;

    private static TwlPhoneStateListener sTwlPhoneStateListener;

    public static void init(Context context) {
        try {
            gContext = context;
            Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            Method getService = serviceManager.getDeclaredMethod("getService", String.class);
            IBinder rawBinder = (IBinder) getService.invoke(null, SERVER_NAME);
            IBinder hookedBinder = (IBinder) Proxy.newProxyInstance(serviceManager.getClassLoader(),
                    new Class<?>[]{IBinder.class},
                    new ITelephonyProxyInvocationHandler(rawBinder));

            Field cacheField = serviceManager.getDeclaredField("sCache");
            cacheField.setAccessible(true);
            Map<String, IBinder> cache = (Map) cacheField.get(null);
            cache.put(SERVER_NAME, hookedBinder);

            sTwlPhoneStateListener = new TwlPhoneStateListener();
            TelephonyManager telephonyManager = (TelephonyManager) gContext.getSystemService(Context.TELEPHONY_SERVICE);
            telephonyManager.listen(sTwlPhoneStateListener, 273);
        } catch (Throwable e) {
            e.printStackTrace();
            log("init error : %s", e.getMessage());
        }

    }

    private static void log(String format, Object... objects) {
        LogUtils.log("TelephonyRegisty", format, objects);
    }


    static class ITelephonyBinderInvocationHandler implements InvocationHandler {
        Object base;
        private int mEvents = 0;

        private ITelephonyBinderInvocationHandler(IBinder base, Class<?> stubClass) {
            try {
                Method asInterfaceMethod = stubClass.getDeclaredMethod("asInterface", IBinder.class);
                this.base = asInterfaceMethod.invoke(null, base);
                log("ITelephonyBinderInvocationHandler:%s", this.base);
            } catch (Exception e) {
                throw new RuntimeException("hooked failed!");
            }
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            log("invoke: %s : [%d]", methodName, HookManager.gApiCallCount.get());
            switch (methodName) {
                case "listenForSubscriber": {
                    Object stub = null;
                    int events = -1;
                    int index = 3;
                    while (index < args.length) {
                        if (args[index] instanceof Integer) {
                            stub = args[index - 1];
                            events = (int) args[index];
                            break;
                        }
                        index++;
                    }
                    if (stub != null) {
                        mEvents |= events;
                        log("listenForSubscriber(%x) %x", events, mEvents);

                        if (sTwlPhoneStateListener.isTwlPhoneStateListener(stub)) {
                            log("real invoke:"+ HookManager.gApiCallCount.addAndGet(3));
                            return method.invoke(base, args);
                        }
                        if (events == 0) {
                            sTwlPhoneStateListener.removePhoneStateListener(stub);
                        } else {
                            sTwlPhoneStateListener.addPhoneStateListener(stub, events);
                        }
                        return null;
                    } else {
                        return method.invoke(base, args);
                    }

                }
            }
            return method.invoke(base, args);
        }
    }

    static class ITelephonyProxyInvocationHandler implements InvocationHandler {
        IBinder base;
        Class<?> stub;
        Class<?> iinterface;
        Object mObject;

        public ITelephonyProxyInvocationHandler(IBinder base) {
            this.base = base;
            try {
                this.stub = Class.forName("com.android.internal.telephony.ITelephonyRegistry$Stub");
                this.iinterface = Class.forName("com.android.internal.telephony.ITelephonyRegistry");
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            }
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if ("queryLocalInterface".equals(method.getName())) {
                if (mObject == null) {
                    mObject = Proxy.newProxyInstance(proxy.getClass().getClassLoader(),

                            // asInterface 的时候会检测是否是特定类型的接口然后进行强制转换
                            // 因此这里的动态代理生成的类型信息的类型必须是正确的
                            new Class[]{IBinder.class, IInterface.class, this.iinterface},
                            new ITelephonyBinderInvocationHandler(base, stub));
                }
                return mObject;
            }
            return method.invoke(base, args);
        }
    }


    /**
     * 自定义PhoneStateListener，由当前类通知其他注册类
     */
    public static class TwlPhoneStateListener extends PhoneStateListener {
        private static final int METHOD_CELL_LOCATION_CHANGED = PhoneStateListener.LISTEN_CELL_LOCATION;
        private static final int METHOD_SIGNAL_STRENGTHS_CHANGED = PhoneStateListener.LISTEN_SIGNAL_STRENGTHS;
        private static final int METHOD_SERVICE_STATE_CHANGED = PhoneStateListener.LISTEN_SERVICE_STATE;

        private Field mPhoneStateListenerField;
        WeakHashMap<PhoneStateListener, Integer> mStateListenerWeakHashMap = new WeakHashMap<>();
        private CellLocation mCellLocation;
        private SignalStrength mSignalStrength;
        private ServiceState mServiceState;

        private TwlPhoneStateListener() {
        }

        @Override
        public void onCellLocationChanged(CellLocation location) {
            super.onCellLocationChanged(location);
            log("onCellInfoChanged: ");
            super.onCellLocationChanged(location);
            mCellLocation = location;
            doCallback(METHOD_CELL_LOCATION_CHANGED, mCellLocation);
        }

        @Override
        public void onSignalStrengthsChanged(SignalStrength signalStrength) {
            log("onSignalStrengthsChanged: ");
            super.onSignalStrengthsChanged(signalStrength);
            mSignalStrength = signalStrength;
            doCallback(METHOD_SIGNAL_STRENGTHS_CHANGED, signalStrength);
        }

        @Override
        public void onServiceStateChanged(ServiceState serviceState) {
            log("onServiceStateChanged: ");
            super.onServiceStateChanged(serviceState);
            mServiceState = serviceState;
            doCallback(METHOD_SERVICE_STATE_CHANGED, serviceState);
        }

        private void doCallback(int method, Object arg) {
            Iterator<PhoneStateListener> iterator = mStateListenerWeakHashMap.keySet().iterator();
            while (iterator.hasNext()) {
                PhoneStateListener phoneStateListener = iterator.next();
                Integer events = mStateListenerWeakHashMap.get(phoneStateListener);
                if (events == null) {
                    continue;
                }
                //如果命中event更新方法
                if ((events & method) == method) {
                    invoke(phoneStateListener, method);
                }
            }
        }

        private void invoke(PhoneStateListener phoneStateListener, int method) {
            if (phoneStateListener != null) {
                if ((method & METHOD_CELL_LOCATION_CHANGED) == METHOD_CELL_LOCATION_CHANGED) {
                    try {
                        phoneStateListener.onCellLocationChanged(mCellLocation);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }

                if ((method & METHOD_SIGNAL_STRENGTHS_CHANGED) == METHOD_SIGNAL_STRENGTHS_CHANGED) {
                    try {
                        phoneStateListener.onSignalStrengthsChanged(mSignalStrength);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }

                if ((method & METHOD_SERVICE_STATE_CHANGED) == METHOD_SERVICE_STATE_CHANGED) {
                    try {
                        phoneStateListener.onServiceStateChanged(mServiceState);
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        public void addPhoneStateListener(Object stub, int events) {
            try {
                PhoneStateListener phoneStateListener = getPhoneStateListener(stub);
                addPhoneStateListenerInner(phoneStateListener, events);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        private void addPhoneStateListenerInner(PhoneStateListener phoneStateListener, int events) {
            if (phoneStateListener != null) {
                mStateListenerWeakHashMap.put(phoneStateListener, events);
                invoke(phoneStateListener, events);
            }
            log("addPhoneStateListener size=[%d], [%s]", mStateListenerWeakHashMap.size(), phoneStateListener);

        }

        public void removePhoneStateListener(Object stub) {
            try {
                PhoneStateListener phoneStateListener = getPhoneStateListener(stub);
                if (phoneStateListener != null) {
                    mStateListenerWeakHashMap.remove(phoneStateListener);
                }
                log("removePhoneStateListener size=[%d]", mStateListenerWeakHashMap.size());
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        boolean isTwlPhoneStateListener(Object stub) {
            try {
                PhoneStateListener phoneStateListener = getPhoneStateListener(stub);
                return phoneStateListener instanceof TwlPhoneStateListener;
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return false;
        }

        private PhoneStateListener getPhoneStateListener(Object stub) throws ClassNotFoundException, NoSuchFieldException, IllegalAccessException {
            if (mPhoneStateListenerField == null) {
                Class stubClass = Class.forName("android.telephony.PhoneStateListener$IPhoneStateListenerStub");
                mPhoneStateListenerField = stubClass.getDeclaredField("mPhoneStateListenerWeakRef");
                mPhoneStateListenerField.setAccessible(true);
            }
            WeakReference<PhoneStateListener> phoneStateListenerWeakRef = (WeakReference<PhoneStateListener>) mPhoneStateListenerField.get(stub);
            if (phoneStateListenerWeakRef != null) {
                PhoneStateListener phoneStateListener = phoneStateListenerWeakRef.get();
                return phoneStateListener;

            }
            return null;
        }
    }
}
