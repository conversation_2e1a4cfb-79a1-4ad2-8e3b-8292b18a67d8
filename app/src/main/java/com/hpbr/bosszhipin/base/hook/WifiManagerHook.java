package com.hpbr.bosszhipin.base.hook;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

public final class WifiManagerHook {

    private static Class iWifiManager;
    private static Field serviceField;

    public static void init(Context context) {
        try {
            iWifiManager = Class.forName("android.net.wifi.IWifiManager");
            serviceField = WifiManager.class.getDeclaredField("mService");
            serviceField.setAccessible(true);
            hook(context);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static void log(String format, Object... objects) {
        LogUtils.log("WifiManager", format, objects);
    }

    private static void hook(Context context) throws IllegalAccessException {
        WifiManager wifi = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        Object realIwm = serviceField.get(wifi);
        IWMInvocationHandler iwmInvocationHandler = new IWMInvocationHandler(realIwm);
        serviceField.set(wifi,
                Proxy.newProxyInstance(iWifiManager.getClassLoader(), new Class[]{iWifiManager},
                        iwmInvocationHandler));
        registerReceiver(context, iwmInvocationHandler);
    }

    private static void registerReceiver(Context context, IWMInvocationHandler iwmInvocationHandler) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        filter.addAction(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        context.registerReceiver(new NetReceiver(iwmInvocationHandler), filter);
    }

    private static class NetReceiver extends BroadcastReceiver {
        private IWMInvocationHandler mIWMInvocationHandler;
        private boolean isReset = false;

        private NetReceiver(IWMInvocationHandler IWMInvocationHandler) {
            mIWMInvocationHandler = IWMInvocationHandler;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent != null ? intent.getAction() : null;
            log("onReceive: %s", action );
            if (action != null) {
                switch (intent.getAction()) {
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                        if (isReset) {
                            mIWMInvocationHandler.mWifiInfo = null;
                        } else {
                            //第一次不用重置
                            isReset = true;
                        }
                        break;
                    case WifiManager.SCAN_RESULTS_AVAILABLE_ACTION:
                        mIWMInvocationHandler.mScanResults = null;
                        break;
                }
            }
        }
    }

    private static class IWMInvocationHandler implements InvocationHandler {
        private Object real;
        private Object mWifiInfo;
        private Object mScanResults;
        private Object mNetworks;

        public IWMInvocationHandler(Object real) {
            this.real = real;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            log("invoke: %s : [%d]", methodName, HookManager.gApiCallCount.get());
            switch (methodName) {
                case "getConnectionInfo": {
                    if (mWifiInfo == null) {
                        log("getConnectionInfo invoke: %d", HookManager.gApiCallCount.incrementAndGet());
                        mWifiInfo = method.invoke(real, args);
                    }
                    return mWifiInfo;
                }
//                case "getScanResults": {
//                    if (mScanResults == null) {
//                        log("getScanResults invoke: %d", HookManager.gApiCallCount.incrementAndGet());
//                        mScanResults =  method.invoke(real, args);
//                    }
//                    return mScanResults;
//                }
//                case "getConfiguredNetworks":{
//                    if (mNetworks == null) {
//                        log("getConfiguredNetworks invoke: %d", HookManager.gApiCallCount.incrementAndGet());
//                        mNetworks = method.invoke(real, args);
//                    }
//                    return mNetworks;
//                }
            }
            return method.invoke(real, args);
        }
    }
}
