package com.hpbr.bosszhipin.callback;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.chat.export.router.SingleRouter;

/**
 * @ClassName ：PageLifecycleCallback
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/3/24  5:05 PM
 */
public class PageLifecycleCallback implements Application.ActivityLifecycleCallbacks {

    private final EventListener eventListener;

    /*是否到达过单聊页面（包含普通聊天页、蓝白）*/
    private boolean arriveSingleChat = false;
    private Activity activityInstance;

    public PageLifecycleCallback(Activity activityInstance, EventListener eventListener) {
        this.activityInstance = activityInstance;
        this.eventListener = eventListener;
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        if (SingleRouter.checkIsChatActivityBossUser(activity) || SingleRouter.checkIsChatActivityDZUser(activity)) {/*到达过单聊页面（包含普通聊天页、蓝白）*/
            arriveSingleChat = true;
        }
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        if (activity == activityInstance) {
            if (eventListener != null) {
                if (arriveSingleChat) {
                    eventListener.onArriveSingleChat();
                }
            }
            arriveSingleChat = false;
        }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }

    public interface EventListener {
        /**
         * 到达过单聊页面后返回
         */
        default void onArriveSingleChat() {
        }
    }
}
