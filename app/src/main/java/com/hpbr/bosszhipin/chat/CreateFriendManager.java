package com.hpbr.bosszhipin.chat;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.RejectDrawContactManager;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.BossCreateFriendRequest;
import net.bosszhipin.api.BossCreateFriendResponse;
import net.bosszhipin.api.GeekCreateFriendRequest;
import net.bosszhipin.api.GeekCreateFriendResponse;
import net.bosszhipin.api.bean.ServerAddFriendBean;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Created by guofeng
 * on 2018/12/28.
 */

public class CreateFriendManager {


    private final Activity mActivity;

    public CreateFriendManager(Activity activity) {
        mActivity = activity;
    }

    public void createFriend(FriendParams friendParams, OnCreateFriendCallBack onCreateFriendCallBack) {

        if (UserManager.isGeekRole()) {
            geekExecuteAddFriend(friendParams, onCreateFriendCallBack);
            return;
        }

        if (UserManager.isBossRole()) {
            bossExecuteAddFriend(friendParams, onCreateFriendCallBack);
            return;
        }

    }

    private void bossExecuteAddFriend(FriendParams friendParams, OnCreateFriendCallBack onCreateFriendCallBack) {
        BossCreateFriendRequest request = new BossCreateFriendRequest(new ApiRequestCallback<BossCreateFriendResponse>() {

            @Override
            public void onStart() {
                super.onStart();
               showProgress();
            }

            @Override
            public void onSuccess(ApiData<BossCreateFriendResponse> data) {
                BossCreateFriendResponse resp = data.resp;
                if (!TextUtils.isEmpty(resp.vipChatToast)) {
                    T.ss(resp.vipChatToast);
                }

                if (!TextUtils.isEmpty(resp.rightsUseTip)) {
                    T.ss(resp.rightsUseTip);
                }

                ServerAddFriendBean relation = data.resp.relation;
                if (relation != null) {

                    ContactBean contactBean = getContactBean(friendParams);
                    contactBean.fromServerBossAddFriendBean(relation, UserManager.getUID(), UserManager.getUserRole().get());
                    contactBean.isNeedComplete = false;

                    ContactManager.getInstance().insertOrUpdateServerField(contactBean, UserManager.getUserRole().get());

                    Intent intent = new Intent(Constants.RECEIVER_CREATE_FRIEND_ACTION);
                    intent.putExtra(Constants.DATA_ID, LText.getLong(friendParams.friendId));
                    intent.putExtra(Constants.DATA_SECURITY_ID, relation.getSecurityId());
                    intent.putExtra(Constants.DATA_OLD_SECURITY_ID, friendParams.securityId);
                    ReceiverUtils.sendBroadcast(mActivity, intent);

                    //添加日志,排查securityId为空的异常
                    addSecurityIdLogcat(relation, contactBean);

                    if (onCreateFriendCallBack != null) {
                        onCreateFriendCallBack.onSuccessListener(contactBean);
                    }

                    //用户主动添加好友，移除拒绝列表
                    RejectDrawContactManager.getInstance().removeReject(contactBean);

                }


                //807.36 查看/开聊权益消耗提示  增加notice信息
                if (null != resp.notice && resp.notice.need && !TextUtils.isEmpty(resp.notice.msg)) {
                    ToastUtils.showText(resp.notice.msg);
                }
            }

            @Override
            public void onComplete() {
               dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {


                if (onCreateFriendCallBack != null) {
                    onCreateFriendCallBack.onFailedListener(reason);
                }
            }
        }, friendParams.fromAiRecommend);
        request.friendId = friendParams.friendId;
        request.jobId = friendParams.jobId;
        request.expectId = friendParams.expectId;
        request.securityId = friendParams.securityId;
        request.lid = friendParams.lid;
        request.from = friendParams.from;
        request.entrance = friendParams.entrance;
        request.greet = friendParams.greet;
        // 1225.66【B/C】口语测试提效：通过口语测试建立好友关系时，新增 languageId 接口入参
        request.languageId = friendParams.speakTestLanguageId;
        HttpExecutor.execute(request);
    }

    /**
     * 添加日志,排查securityId为空的异常
     *
     * @param relation
     * @param contactBean
     */
    private void addSecurityIdLogcat(ServerAddFriendBean relation, ContactBean contactBean) {
        try {
            String serverSecurityId = relation.getSecurityId();
            String parserSecurity = contactBean.securityId;

            ContactBean cacheBean = ContactManager.getInstance()
                    .queryContactByFriendId(contactBean.friendId, UserManager.getUserRole().get(), contactBean.friendSource);

            TLog.info(TAG, "add friend complete serverSecurityId=" + serverSecurityId + "parserSecurity=" + parserSecurity + "cacheSecurityId=" + (cacheBean != null ? cacheBean.securityId : ""));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static final String TAG = "CreateFriendManager";

    private ContactBean getContactBean(FriendParams friendParams) {
        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(LText.getLong(friendParams.friendId), UserManager.getUserRole().get(), friendParams.friendSource);

        if (contactBean == null) {
            contactBean = new ContactBean();
            contactBean.friendSource = friendParams.friendSource;
        }
        return contactBean;
    }


    private void showProgress() {
        if (mActivity instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) mActivity;
            activity.showProgressDialog("建立好友关系");
        }
    }

    private void dismissProgress(){
        if (mActivity instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) mActivity;
            activity.dismissProgressDialog();
        }
    }


    private void geekExecuteAddFriend(FriendParams friendParams, OnCreateFriendCallBack onCreateFriendCallBack) {
        GeekCreateFriendRequest request = new GeekCreateFriendRequest(new ApiRequestCallback<GeekCreateFriendResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                showProgress();
            }

            @Override
            public void onSuccess(ApiData<GeekCreateFriendResponse> data) {

                ServerAddFriendBean relation = data.resp.relation;
                if (relation != null) {
                    ContactBean contactBean = getContactBean(friendParams);
                    contactBean.fromServerGeekAddFriendBean(relation, UserManager.getUID(), UserManager.getUserRole().get());
                    contactBean.isNeedComplete = false;
                    ContactManager.getInstance().insertOrUpdateServerField(contactBean, UserManager.getUserRole().get());

                    Intent intent = new Intent(Constants.RECEIVER_CREATE_FRIEND_ACTION);
                    intent.putExtra(Constants.DATA_ID, LText.getLong(friendParams.friendId));
                    intent.putExtra(Constants.DATA_SECURITY_ID, relation.getSecurityId());
                    intent.putExtra(Constants.DATA_OLD_SECURITY_ID, friendParams.securityId);
                    ReceiverUtils.sendBroadcast(mActivity, intent);

                    if (onCreateFriendCallBack != null) {
                        onCreateFriendCallBack.onSuccessListener(contactBean);
                    }

                    //用户主动添加好友，移除拒绝列表
                    RejectDrawContactManager.getInstance().removeReject(contactBean);

                }


            }

            @Override
            public void onComplete() {
                dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {


                if (reason.getErrCode() == 1049) { //  //牛人加好友的,该code的作用用来关闭本页面并且刷新前一个详情页面
                    Intent intent = new Intent(Constants.RECEIVER_REFRESH_JOB_DETAIL_ACTION);
                    intent.putExtra(Constants.DATA_JOB_ID, friendParams.jobId);
                    ReceiverUtils.sendBroadcast(mActivity, intent);
                }

                if (onCreateFriendCallBack != null) {
                    onCreateFriendCallBack.onFailedListener(reason);
                }
            }
        }, friendParams.fromAiRecommend);
        request.friendId = friendParams.friendId;
        request.jobId = friendParams.jobId;
        request.expectId = friendParams.expectId;
        request.lid = friendParams.lid;
        request.similarPosition = friendParams.similarPosition;
        request.securityId = friendParams.securityId;
        request.entrance = friendParams.entrance;
        request.jobAddressId = friendParams.jobAddressId;
        request.applyJobDirectly = friendParams.applyJobDirectly;
        request.startChatProcessExpGroup = friendParams.startChatProcessExpGroup;
        HttpExecutor.execute(request);
    }


    public interface OnCreateFriendCallBack {

        void onSuccessListener(ContactBean mContactBean);

        void onFailedListener(ErrorReason reason);

    }


    public static class FriendParams extends BaseEntity {

        private static final long serialVersionUID = -8115654144821150467L;

        private String friendId;
        private String jobId;
        private String expectId;
        private String lid;
        private String similarPosition;
        private String from;
        private String securityId;
        private int friendSource;
        // Boss开聊 1.立即沟通 2.询问工作经历 3.询问项目经历 4.获取作品集
        // Geek开聊 1.公司圈 9-互动列表
        private int entrance;

        private String greet;

        private String jobAddressId;//907职位加密id

        private int applyJobDirectly;

        // 1225.66【B/C】口语测试提效：通过口语测试建立好友关系时，新增 languageId 接口入参
        private String speakTestLanguageId;
        private int startChatProcessExpGroup;
        private boolean fromAiRecommend;

        /**
         * 1225.66【B/C】口语测试提效：通过口语测试建立好友关系时，新增 languageId 接口入参
         *
         * @param speakTestLanguageId 口语测试数据返回的 languageId
         */
        public void setSpeakTestLanguageId(String speakTestLanguageId) {
            this.speakTestLanguageId = speakTestLanguageId;
        }

        public void setApplyJobDirectly(int applyJobDirectly) {
            this.applyJobDirectly = applyJobDirectly;
        }

        public void setFromAiRecommend(boolean fromAiRecommend) {
            this.fromAiRecommend = fromAiRecommend;
        }

        public void setJobAddressId(String jobAddressId) {
            this.jobAddressId = jobAddressId;
        }

        public void setFriendSource(int friendSource) {
            this.friendSource = friendSource;
        }

        public void setFriendId(String friendId) {
            this.friendId = friendId;
        }

        public void setJobId(String jobId) {
            this.jobId = jobId;
        }

        public void setExpectId(String expectId) {
            this.expectId = expectId;
        }

        public void setLid(String lid) {
            this.lid = lid;
        }

        public void setSimilarPosition(String similarPosition) {
            this.similarPosition = similarPosition;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public void setSecurityId(String securityId) {
            this.securityId = securityId;
        }

        public void setEntrance(int entrance) {
            this.entrance = entrance;
        }

        public void setGreet(String greet) {
            this.greet = greet;
        }

        public void setStartChatProcessExpGroup(int startChatProcessExpGroup) {
            this.startChatProcessExpGroup = startChatProcessExpGroup;
        }
    }


}
