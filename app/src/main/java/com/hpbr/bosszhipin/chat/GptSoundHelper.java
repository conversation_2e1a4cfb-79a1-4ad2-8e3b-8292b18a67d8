package com.hpbr.bosszhipin.chat;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;

import com.hpbr.bosszhipin.module.contacts.sounds.AudioUtil;
import com.hpbr.bosszhipin.module.contacts.sounds.ChatSoundPlayer;

/**
 * create by guofeng
 * date on 2023/5/25
 */

public class GptSoundHelper {


    private GptSoundHelper() {
    }

    private static final GptSoundHelper instance = new GptSoundHelper();

    public static GptSoundHelper getInstance() {
        return instance;
    }


    private final ITextToAudio iTextToAudio = new VTeamSDKTextToVideoImp();


    public void vSdkPlaySound(AppCompatActivity activity,
                              long msgId,
                              String text,
                              @NonNull TextToAudioCallBack callBack) {
        /*暂停正在播放的声音*/
        AudioUtil.requestAudioFocus(activity, true);

        /*注册生命周期*/
        activity.getLifecycle().removeObserver(observer);
        activity.getLifecycle().addObserver(observer);

        /*开始播放*/
        iTextToAudio.createTextToAudio(activity, msgId, text, callBack);
    }


    public void stopPlay() {
        iTextToAudio.onPause();
    }

    private final LifecycleObserver observer = (LifecycleEventObserver) (source, event) -> {

        if (event == Lifecycle.Event.ON_DESTROY) {
            iTextToAudio.onDestroy();
        }
        if (event == Lifecycle.Event.ON_PAUSE) {
            iTextToAudio.onPause();
        }

    };

}