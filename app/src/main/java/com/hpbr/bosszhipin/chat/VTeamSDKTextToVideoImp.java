package com.hpbr.bosszhipin.chat;

import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;

import com.bzl.sdk.voice.TextToAudioManager;
import com.bzl.sdk.voice.interfaces.AbsText2AudioPlayCallback;
import com.bzl.sdk.voice.internal.net.bean.TextToAudioSendBean;
import com.bzl.sdk.voice.internal.net.bean.TtsStartConfig;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.ActivityUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2023/9/26
 */

public class VTeamSDKTextToVideoImp implements ITextToAudio {

    private static final String TAG = "VTeamSDKTextToVideoImp";


    public VTeamSDKTextToVideoImp() {

    }


    private ProgressDialog progressDialog;

    private Activity topActivity;

    private void showProgress() {
        App.get().getMainHandler().post(() -> {
            if (progressDialog == null) {
                topActivity = ForegroundUtils.get().getTopActivity();
                progressDialog = new ProgressDialog(topActivity);
            }
            if (ActivityUtils.isValid(topActivity)) {
                progressDialog.show();
            }
        });

    }

    private void dismissProgress() {
        App.get().getMainHandler().post(() -> {
            if (progressDialog == null) return;
            if (ActivityUtils.isValid(topActivity)) {
                progressDialog.dismiss();
            }
        });
    }

    @Override
    public void createTextToAudio(Context context,
                                  long msgId, String text,
                                  @NonNull TextToAudioCallBack callBack) {

        //把任务添加到队列里面
        queue.add(new TaskLineExecuteRunnable(context, text, String.valueOf(msgId), callBack));

        /*暂停当前播放的*/
        TextToAudioManager.get().stop();

        //开始播放
        checkCanPlay();
    }


    private TaskLineExecuteRunnable currentPlaying;


    private void checkCanPlay() {
        //还没有开始播放
        if (currentPlaying == null) {
            if (!LList.isEmpty(queue)) {
                //显示loading
                showProgress();
                currentPlaying = queue.remove(0);
                currentPlaying.run();
            }
        }
    }


    private void playNextInQueue() {
        //播放下一个
        if (!LList.isEmpty(queue)) {
            //显示loading
            showProgress();
            currentPlaying = queue.remove(0);
            currentPlaying.run();
        }
    }

    private final List<TaskLineExecuteRunnable> queue = new ArrayList<>();


    private class TaskLineExecuteRunnable implements Runnable {

        private final Context context;
        private final String text;
        private final String msgId;
        private final TextToAudioCallBack callBack;


        public TaskLineExecuteRunnable(Context context, String text, String msgId, TextToAudioCallBack callBack) {
            this.context = context;
            this.text = text;
            this.msgId = msgId;
            this.callBack = callBack;
        }

        @Override
        public void run() {

            /*组装数据*/
            TextToAudioSendBean textToAudioSendBean = new TextToAudioSendBean();
            textToAudioSendBean.text = text;
            textToAudioSendBean.session_id = String.valueOf(msgId);

            /*开始播放*/
            TextToAudioManager.get().start(context, textToAudioSendBean, TtsStartConfig.get().setSignalName("zhipin-gpt").setOnline(HostConfig.CONFIG == HostConfig.Addr.ONLINE),
                    new AbsText2AudioPlayCallback() {
                        @Override
                        public void onPlayStart(String sessionId) {
                            dismissProgress();
                            L.d(VTeamSDKTextToVideoImp.TAG, "onPlayStart sessionId=" + sessionId);
                            callBack.onPlayStart(LText.getLong(sessionId));
                        }

                        @Override
                        public void onPlayError(String sessionId, int code, String msg) {
                            dismissProgress();
                            L.d(VTeamSDKTextToVideoImp.TAG, "onPlayError sessionId=" + sessionId);
                            callBack.onPlayError(LText.getLong(sessionId), code, msg);

                            //情况当前播放对象数据
                            if (currentPlaying != null && LText.equal(currentPlaying.msgId, sessionId)) {
                                currentPlaying = null;
                            }

                            //前一个socket关闭成功，在执行播放下一个
                            playNextInQueue();
                        }


                        @Override
                        public void onPlayCompleted(String sessionId, int result) {
                            L.d(VTeamSDKTextToVideoImp.TAG, "onPlayCompleted sessionId=" + sessionId);
//                            callBack.onPlayCompleted(LText.getLong(sessionId), result);
                        }
                    });

        }

    }


    @Override
    public void onPause() {
        /*暂停当前播放的*/
        TextToAudioManager.get().stop();
    }

    @Override
    public void onDestroy() {
        /*页面关闭释放资源*/
        TextToAudioManager.get().onDestroy();
    }

}