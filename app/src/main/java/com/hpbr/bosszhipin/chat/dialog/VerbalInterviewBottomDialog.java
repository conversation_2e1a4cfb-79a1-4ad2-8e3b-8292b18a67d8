package com.hpbr.bosszhipin.chat.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.chat.export.constant.ChatAnalyticsAction;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.interviews.export.InterviewsRouter;
import com.hpbr.bosszhipin.listener.Callback;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.module.interview.entity.SelectContactParamBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.network.VerbalInterviewMapResponse;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.BubbleMiddleLayout;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.VerbalInterviewResponse;
import net.bosszhipin.api.bean.ServerTranslatedPoiAddressBean;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * author: dubojun
 * date: 2024/8/21
 * description:
 **/
public class VerbalInterviewBottomDialog {
    private static final String TAG = "VerbalInterviewDialog";
    public static final int PAGE_FROM_CHAT = 1;
    public static final int PAGE_FROM_INTERVIEW = 2;
    private Activity mActivity;

    private VerbalInterviewResponse mVerbalInterviewData;

    private BottomView mBottomView;

    private boolean mIsSelected;

    private OnSelectAddress mOnSelectAddress;

    private SimpleDraweeView mMapView;
    private TextView mAddressBubble;
    private TextView mContactPhoneView;
    private TextView mContactNameView;
    private BubbleMiddleLayout mBubbleMiddleLayout;
    private ConstraintLayout mContactInfoContainer;
    private ZPUIRoundButton mBtnSend;

    private ServerTranslatedPoiAddressBean mPoiAddressBean;
    private String mContactName;
    private String mContactPhone;
    private String mContactId;

    // 是否修改过弹窗信息
    private boolean mIsModified;
    private int source; // 0 - 默认 1 - 气泡场景。协议verbalInterview返回
    private Runnable onSendSuccessCallback;
    private int pageFrom;

    public VerbalInterviewBottomDialog(Activity mActivity, VerbalInterviewResponse verbalInterviewData, int source, OnSelectAddress onSelectAddress) {
        this.mActivity = mActivity;
        this.mVerbalInterviewData = verbalInterviewData;
        this.source = source;
        this.mOnSelectAddress = onSelectAddress;
    }

    public void show() {
        View view = createView();
        if (view == null) {
            TLog.info(TAG, "create verbal interview dialog failed.");
            return;
        }

        mBottomView = new BottomView(mActivity, R.style.BottomViewTheme_Transparent, view);
        mBottomView.setAnimation(R.style.BottomToTopAnim);
        mBottomView.showBottomView(false);

        if (mVerbalInterviewData != null) {
            statsBossVerbalInterviewPV(mVerbalInterviewData.geekId, mVerbalInterviewData.jobId, mVerbalInterviewData.address, mVerbalInterviewData.sendStatus == 0 ? 1 : 2, pageFrom);
        }
    }

    @Nullable
    private View createView() {
        if (mVerbalInterviewData == null) {
            TLog.info(TAG, "data is null, init dialog view fail.");
            return null;
        }

        View view = LayoutInflater.from(mActivity).inflate(R.layout.view_dialog_verbal_interview, null);
        if (view == null) {
            TLog.info(TAG, "view is null");
            return null;
        }

        mMapView = view.findViewById(R.id.iv_map);
        mAddressBubble = view.findViewById(R.id.tv_location);
        mBubbleMiddleLayout = view.findViewById(R.id.bl_location_view);
        mContactPhoneView = view.findViewById(R.id.tv_contact_content);
        mContactNameView = view.findViewById(R.id.tv_contact_name);
        mContactInfoContainer = view.findViewById(R.id.cl_contact_info_container);

        setTitleView(view);
        setInterviewAddress(view);
        setContactView(view);
        setButtonView(view);

        return view;
    }

    private void setTitleView(View view) {
        ImageView ivCloseDialog = view.findViewById(R.id.iv_close_verbal_interview_dialog);
        ivCloseDialog.setOnClickListener(v -> {
            if (mBottomView != null) {
                mBottomView.dismissBottomView();
            }
            statsBossVerbalInterviewClick(mVerbalInterviewData.geekId, mVerbalInterviewData.jobId, 5, "", "");
        });

        TextView titleView = view.findViewById(R.id.tv_verbal_interview_title);
        titleView.setText(mVerbalInterviewData.isConfirmSend() ? R.string.chat_verbal_interview_title : R.string.chat_verbal_interview_title_already_send);
    }

    private void setInterviewAddress(@NonNull View rootView) {
        ImageView chooseAddress = rootView.findViewById(R.id.iv_choose_interview_address);
        chooseAddress.setOnClickListener(v -> {
            openMapPage();
        });

        // 首次打开接口返回的是职位上的地图url，职位地图是百度就返回百度。所以不传baiduMapUrl
        setMapView(mVerbalInterviewData.mapUrl, mVerbalInterviewData.address, mVerbalInterviewData.baiduMapUrl);
    }

    private void setMapView(String mapUrl, String address, String baiduMapUrl) {
        String showMapUrl = getMapUrl(mapUrl, baiduMapUrl);
        mMapView.setImageURI(StringUtil.getNetworkUri(showMapUrl));
        mMapView.setOnClickListener(v -> {
            openMapPage();
        });

        if (TextUtils.isEmpty(address)) {
            mBubbleMiddleLayout.setVisibility(View.GONE);
        } else {
            mBubbleMiddleLayout.setVisibility(View.VISIBLE);
            mAddressBubble.setText(address);
        }
    }

    private void openMapPage() {
        statsBossVerbalInterviewClick(mVerbalInterviewData.geekId, mVerbalInterviewData.jobId, 3, "", "");
        JobBean jobBean = BossJobListProvider.getInstance().findJobById(mVerbalInterviewData.jobId);
        if (jobBean == null) {
            ToastUtils.showText("沟通职位不在线，无法使用");
            TLog.info(TAG, "没有找到有效的职位数据，jobId = %d", mVerbalInterviewData.jobId);
            return;
        }

        if (mOnSelectAddress != null) {
            mOnSelectAddress.onSelectAddress(jobBean);
        }
    }

    private void setContactView(@NonNull View rootView) {
        mIsSelected = mVerbalInterviewData.isSelected();
        mContactInfoContainer.setVisibility(mIsSelected ? View.VISIBLE : View.GONE);
        ImageView sendContact = rootView.findViewById(R.id.iv_send_contact);
        sendContact.setImageResource(mIsSelected ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
        sendContact.setOnClickListener(v -> {
            mIsSelected = !mIsSelected;
            sendContact.setImageResource(mIsSelected ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
            mContactInfoContainer.setVisibility(mIsSelected ? View.VISIBLE : View.GONE);

            mIsModified = true;
            updateButtonViewBg();
        });

        mContactId = mVerbalInterviewData.encContactId;
        mContactName = mVerbalInterviewData.showName;
        mContactPhone = mVerbalInterviewData.contact;
        String phone = buildPhoneText(mContactPhone);
        mContactPhoneView.setText(phone);
        mContactNameView.setText(mContactName);

        ImageView chooseContact = rootView.findViewById(R.id.iv_choose_contacts);

        // 选择联系人后回显信息
        SelectContactParamBean selectContactParamBean = new SelectContactParamBean();
        Callback<SelectContactParamBean.BossContactBean> callback = (bossContactBean) -> {
            if (bossContactBean == null) {
                TLog.info(TAG, "contactName and contactPhone is null, update contact info fails.");
                return;
            }

            mIsModified = isContactModified(bossContactBean.contactName, bossContactBean.contactPhone);

            mContactId = bossContactBean.contactId;
            mContactName = bossContactBean.contactName;
            mContactPhone = bossContactBean.contactPhone;
            String text = buildPhoneText(mContactPhone);
            mContactPhoneView.setText(text);
            mContactNameView.setText(mContactName);
            updateButtonViewBg();
        };

        chooseContact.setOnClickListener(v -> {
            SelectContactParamBean.BossContactBean bossContactBean = new SelectContactParamBean.BossContactBean(mContactId, mContactName, mContactPhone);
            selectContactParamBean.callback = callback;
            selectContactParamBean.contactBean = bossContactBean;
            InterviewsRouter.showBossSelectContactDialog(mActivity, mVerbalInterviewData.jobId, selectContactParamBean);
            statsBossVerbalInterviewClick(mVerbalInterviewData.geekId, mVerbalInterviewData.jobId, 4, "", "");
        });
    }

    public void updateContactView(SelectContactParamBean.BossContactBean bossContactBean) {
        if (bossContactBean == null) {
            TLog.info(TAG, "bossContactBean is null, update contact info fails.");
            return;
        }

        mIsModified = isContactModified(bossContactBean.contactName, bossContactBean.contactPhone);

        mContactId = bossContactBean.contactId;
        mContactName = bossContactBean.contactName;
        mContactPhone = bossContactBean.contactPhone;

        String phone = buildPhoneText(mContactPhone);
        mContactPhoneView.setText(phone);
        mContactNameView.setText(mContactName);
        updateButtonViewBg();
    }

    private void setButtonView(@NonNull View rootView) {
        mBtnSend = rootView.findViewById(R.id.btn_send_verbal_interview);
        mBtnSend.setText(mVerbalInterviewData.isConfirmSend() ? R.string.chat_verbal_interview_confirm_send : R.string.chat_verbal_interview_resend);
        mBtnSend.setOnClickListener(v -> {
            sendVerbalInterview();
        });

        updateButtonViewBg();
    }

    private void updateButtonViewBg() {
        // 确认发送不调整
        if (mVerbalInterviewData.isConfirmSend()) {
            return;
        }

        // 重新发送且内容有修改时设置
        mBtnSend.setEnabled(mIsModified);
    }

    private void sendVerbalInterview() {
        String interviewLongitude = mPoiAddressBean != null ? String.valueOf(mPoiAddressBean.poiLongitude) : String.valueOf(mVerbalInterviewData.interviewLongitude);
        String interviewLatitude = mPoiAddressBean != null ? String.valueOf(mPoiAddressBean.poiLatitude) : String.valueOf(mVerbalInterviewData.interviewLatitude);
        String interviewArea = mPoiAddressBean != null ? mPoiAddressBean.poiStreet : mVerbalInterviewData.interviewArea;
        String interviewCity = mPoiAddressBean != null ? mPoiAddressBean.poiCity : mVerbalInterviewData.interviewCity;
        String interviewLocationCode = mPoiAddressBean != null ? String.valueOf(mPoiAddressBean.poiCityCode) : String.valueOf(mVerbalInterviewData.interviewLocationCode);
        String interviewPoiId = mPoiAddressBean != null ? mPoiAddressBean.geoId : mVerbalInterviewData.interviewPoiId;
        String interviewPoiTitle = mPoiAddressBean != null ? mPoiAddressBean.poiTitle : mVerbalInterviewData.interviewPoiTitle;
        String interviewProvince = mPoiAddressBean != null ? mPoiAddressBean.poiProvince : mVerbalInterviewData.interviewProvince;
        String cityCode = mPoiAddressBean != null ? String.valueOf(mPoiAddressBean.poiCityCode) : "";
        String interviewRoomNumber = mPoiAddressBean != null ? mPoiAddressBean.roomInfo : mVerbalInterviewData.interviewRoomNumber;
        String address = mPoiAddressBean != null ? mPoiAddressBean.poiTitle : mVerbalInterviewData.address;
        String encContactId = !TextUtils.isEmpty(mContactId) ? mContactId : mVerbalInterviewData.encContactId;

        SimpleApiRequest.POST(ChatUrlConfig.URL_VERBAL_INTERVIEW_SEND)
                .addParam("securityId", mVerbalInterviewData.securityId)
                .addParam("selected", mIsSelected ? 1 : 0)
                .addParam("encContactId", encContactId)
                .addParam("interviewArea", interviewArea)
                .addParam("interviewCity", interviewCity)
                .addParam("interviewLatitude", interviewLatitude)
                .addParam("interviewLocationCode", interviewLocationCode)
                .addParam("interviewLongitude", interviewLongitude)
                .addParam("interviewPoiId", interviewPoiId)
                .addParam("interviewPoiTitle", interviewPoiTitle)
                .addParam("interviewProvince", interviewProvince)
                .addParam("interviewRoomNumber", interviewRoomNumber)
                .addParam("jobId", mVerbalInterviewData.jobId)
                .addParam("cityCode", cityCode)
                .addParam("contact", mContactPhone)
                .addParam("showName", mContactName)
                .addParam("address", address)
                .addParam("source", source)
                .setRequestCallback(new SimpleCommonApiRequestCallback<EmptyResponse>() {
                    @Override
                    public void onSuccess(ApiData<EmptyResponse> data) {
                        super.onSuccess(data);
                        if (onSendSuccessCallback != null) {
                            onSendSuccessCallback.run();
                        }
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        mBottomView.dismissBottomView();
                    }
                })
                .execute();
        statsBossVerbalInterviewClick(mVerbalInterviewData.geekId, mVerbalInterviewData.jobId, mVerbalInterviewData.isConfirmSend() ? 1 : 2,
                mVerbalInterviewData.geekId, interviewPoiTitle);

    }

    public void updateAddress(ServerTranslatedPoiAddressBean poiAddressBean) {
        if (poiAddressBean == null || mVerbalInterviewData == null) {
            TLog.info(TAG, "poiAddressBean is null");
            return;
        }
        mPoiAddressBean = poiAddressBean;

        SimpleApiRequest.GET(ChatUrlConfig.URL_GENERATE_VERBAL_INTERVIEW_MAP)
                .addParam("securityId", mVerbalInterviewData.securityId)
                .addParam("jobAddrLng", poiAddressBean.poiLongitude)
                .addParam("jobAddrLat", poiAddressBean.poiLatitude)
                .setRequestCallback(new SimpleCommonApiRequestCallback<VerbalInterviewMapResponse>() {
                    @Override
                    public void onSuccess(ApiData<VerbalInterviewMapResponse> data) {
                        if (data.resp != null) {
                            setMapView(data.resp.mapUrl, poiAddressBean.poiTitle, data.resp.baiduMapUrl);
                            mIsModified = true;
                            updateButtonViewBg();
                        }
                    }
                })
                .execute();
    }

    private String buildPhoneText(String phone) {
        return "·" + phone;
    }

    private boolean isContactModified(String name, String phone) {
        return !TextUtils.equals(mContactName, name) || !TextUtils.equals(mContactPhone, phone);
    }

    public void setPageFrom(int pageFrom) {
        this.pageFrom = pageFrom;
    }

    public interface OnSelectAddress {
        void onSelectAddress(JobBean jobBean);
    }

    public void setOnSendSuccessCallback(Runnable onSendSuccessCallback) {
        this.onSendSuccessCallback = onSendSuccessCallback;
    }

    public void statsBossVerbalInterviewPV(String geekId, long jobId, String address, int sendStatus, int pageFrom) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_INTERVIEW_ADDRESS_SHOW)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", address)
                .param("p4", sendStatus)
                .param("p5", pageFrom)
                .debug()
                .build();
    }

    public void statsBossVerbalInterviewClick(String geekId, long jobId, int clickPosition, String geekIdList, String address) {
        AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_BOSS_INTERVIEW_ADDRESS_CLICK)
                .param("p", geekId)
                .param("p2", jobId)
                .param("p3", clickPosition)
                .param("p4", geekIdList)
                .param("p5", address)
                .debug()
                .build();
    }

    private String getMapUrl(String gaodeMap, String baiduMap) {
        if (!MapViewCompat.isForceAMap() && AndroidDataStarGray.getInstance().isUserBMapTypeV3()) {
            return LText.notEmpty(baiduMap) ? baiduMap : gaodeMap;
        } else {
            return gaodeMap;
        }
    }
}
