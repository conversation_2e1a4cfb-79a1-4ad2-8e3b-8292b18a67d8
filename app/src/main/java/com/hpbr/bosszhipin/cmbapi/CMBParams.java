package com.hpbr.bosszhipin.cmbapi;

import android.text.TextUtils;
import org.json.JSONObject;

import java.io.Serializable;

/**
 * 招行支付入参
 * <p>
 * http://openhome.cmbchina.com/PayNew/pay/doc/cell/app/AndroidInterface
 */
public class CMBParams implements Serializable {

    private static final long serialVersionUID = 1399029561082370732L;

    public String requestData;

    public String cmbJumpAppUrl;

    public String cmbH5JumpUrl;

    public String method;

    public String cmbAppId;

    public boolean isShowNavigationBar = true;

    public static CMBParams obtain(String requestData, String cmbJumpAppUrl,
                                   String cmbH5JumpUrl, String method, String cmbAppId) {
        CMBParams cmbParams = new CMBParams();
        cmbParams.requestData = requestData;
        cmbParams.cmbJumpAppUrl = cmbJumpAppUrl;
        cmbParams.cmbH5JumpUrl = cmbH5JumpUrl;
        cmbParams.method = method;
        cmbParams.cmbAppId = cmbAppId;
        return cmbParams;
    }

    public static CMBParams obtainMock(boolean isDebug) {
        CMBParams cmbParams = new CMBParams();

        cmbParams.requestData = "jsonRequestData=%7B%22charset%22%3A%22UTF-8%22%2C%22reqData%22%3A%7B%22amount%22%3A%220.01%22%2C%22branchNo%22%3A%220010%22%2C%22date%22%3A%2*********%22%2C%22dateTime%22%3A%****************%22%2C%22expireTimeSpan%22%3A%221440%22%2C%22merchantNo%22%3A%********%22%2C%22orderNo%22%3A%2*********1121370001%22%2C%22payNoticeUrl%22%3A%22https%3A%2F%2Fquality-assurance-callback.zhipin.com%2Fqa%2Fwapi%2Fzpp%2Fpay%2Fcmb%2Fnotify%22%7D%2C%22sign%22%3A%223239d932da5bf6ee167ebca42212be139a18adfdad8536d395ce4a21e3aa6921%22%2C%22signType%22%3A%22SHA-256%22%2C%22version%22%3A%221.0%22%7D";

        cmbParams.cmbJumpAppUrl = isDebug ?
                "cmbmobilebank://CMBLS/FunctionJump?action=gofuncid&funcid=200013&serverid=CMBEUserPay&requesttype=post&cmb_app_trans_parms_start=here"
                : "cmbmobilebank://CMBLS/FunctionJump?action=gofuncid&funcid=200013&serverid=CMBEUserPay&requesttype=post&cmb_app_trans_parms_start=here";
        cmbParams.cmbH5JumpUrl = isDebug ?
                "http://*************:801/netpayment/BaseHttp.dll?H5PayJsonSDK "
                : "https://netpay.cmbchina.com/netpayment/BaseHttp.dll?H5PayJsonSDK ";
        cmbParams.method = "pay";
        cmbParams.isShowNavigationBar = true;
        return cmbParams;
    }

    public void parseJson(JSONObject jsonObject) {
        requestData = jsonObject.optString("requestData");
        cmbJumpAppUrl = jsonObject.optString("cmbJumpAppUrl");
        cmbH5JumpUrl = jsonObject.optString("cmbH5JumpUrl");
        method = jsonObject.optString("method");
        cmbAppId = jsonObject.optString("cmbAppId");
    }

    /**
     * 招行一网通跳转参数校验
     */
    public boolean isCMBParamsValid() {
        return !TextUtils.isEmpty(cmbAppId)
                && !TextUtils.isEmpty(requestData)
                && (!TextUtils.isEmpty(cmbJumpAppUrl)
                || !TextUtils.isEmpty(cmbH5JumpUrl));
    }

    @Override
    public String toString() {
        return "CMBParams{" +
                "requestData='" + requestData + '\'' +
                ", cmbJumpAppUrl='" + cmbJumpAppUrl + '\'' +
                ", cmbH5JumpUrl='" + cmbH5JumpUrl + '\'' +
                ", method='" + method + '\'' +
                ", cmbAppId='" + cmbAppId + '\'' +
                ", isShowNavigationBar=" + isShowNavigationBar +
                '}';
    }
}
