package com.hpbr.bosszhipin.cmbapi;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import cmbapi.CMBApi;
import cmbapi.CMBApiFactory;
import cmbapi.CMBEventHandler;
import cmbapi.CMBResponse;

import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

/**
 * Author: zhouyou
 * Date: 2022/1/5
 */
public class CMBPayAppEntryActivity extends BaseActivity2 implements CMBEventHandler {

    protected static final String TAG = "CMBPayAppEntryActivity1";

    protected CMBApi mCmbApi;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        TLog.print(TAG, "onCreate() ......");
        /*CMBPayUtil->CMBApiFactory.createCMBAPI创建缓存过一次*/
        mCmbApi = CMBApiFactory.getCMBApi();
        if (null == mCmbApi) {
            String lastCMBAppId = CMBPayUtil.getCMBAppId();
            if (!TextUtils.isEmpty(lastCMBAppId)) {
                mCmbApi = CMBApiFactory.createCMBAPI(this, lastCMBAppId);
            }
        }
        handleCMBIntent(mCmbApi, getIntent(), this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        TLog.print(TAG, "onNewIntent() ......");
        handleCMBIntent(mCmbApi, intent, this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        TLog.print(TAG, "onActivityResult() ......");
        handleCMBIntent(mCmbApi, data, this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CMBPayUtil.destroyCMBAPI();
    }

    public void handleCMBIntent(CMBApi cmbApi, Intent intent, CMBEventHandler eventHandler) {
        boolean result = false;
        if (null != cmbApi) {
            try {
                result = mCmbApi.handleIntent(intent, eventHandler);
            } catch (Exception e) {
                TLog.print(TAG, "handleCMBIntent Exception...... %s", LText.toString(e.toString()));
            }
        }
        if (!result) {
            TLog.print(TAG, "handleCMBIntent error......");
            finishSelf();
        }
    }

    @Override
    public void onResp(CMBResponse resp) {
        TLog.print(TAG, "onResp......");
        if (null != resp) {
            if (LText.isEmptyOrNull(resp.respMsg)) {
                resp.respMsg = "";
            }
            TLog.print(TAG, "respCode: %d, respMsg: %s", resp.respCode, resp.respMsg);
            CMBPayUtil.sendCMBPayResultBroadcast(resp.respCode, resp.respMsg, false);
        }
        finishSelf();
    }

    public void finishSelf() {
        AppUtil.finishActivity(this);
    }

}
