package com.hpbr.bosszhipin.cmbapi;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import cmbapi.CMBApi;
import cmbapi.CMBApiFactory;
import cmbapi.CMBRequest;
import cmbapi.CMBResponse;
import cmbapi.CMBWebViewListener;
import cmbapi.CMBWebview;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

/**
 * Author: zhouyou
 * Date: 2022/1/5
 */
public class CMBPayH5EntryActivity extends BaseActivity2 {

    protected static final String TAG = "CMBPayH5EntryActivity1";

    /*Title*/
    protected AppTitleView mTitleView;
    protected CMBWebview mWebView;

    /*请求入参*/
    private String mStrUrl = "";
    private String mStrMethod = "";
    private String mStrRequestData = "";

    /*返回值*/
    private int mRespCode;
    private String mRespString = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_cmb_pay_h5_entry);
        TLog.print(TAG, "onCreate() ......");
        initParams();
        initViews();
        initData();
    }

    private void initParams() {
        Intent intent = getIntent();
        if (intent == null) {
            return;
        }
        mStrUrl = intent.getStringExtra(CMBConstant.CMB_DATA_H5_JUMP_URL);
        mStrMethod = intent.getStringExtra(CMBConstant.CMB_DATA_METHOD);
        mStrRequestData = intent.getStringExtra(CMBConstant.CMB_DATA_REQUEST_DATA);
    }

    private void initViews() {
        mWebView = findViewById(R.id.webview);
        mTitleView = findViewById(R.id.title_view);
        mTitleView.setDividerInvisible();
        mTitleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                onBackPressed();
            }
        });
    }

    private void initData() {

        cacheCMBApi();

        CMBRequest request = new CMBRequest();
        request.CMBH5Url = mStrUrl;
        request.method = mStrMethod;
        request.requestData = mStrRequestData;

        try {
            mWebView.sendRequest(request, new CMBWebViewListener() {
                @Override
                public void onClosed(int respCode, String respString) {
                    mRespCode = respCode;
                    mRespString = respString;
                    handleRespMessage(true);
                }

                @Override
                public void onTitleChanged(String title) {
                    mTitleView.setTitle(title);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            TLog.print(TAG, "mWebView.sendRequest error ... %s", e.toString());
            ToastUtils.showText("一网通购买失败，参数错误0x6~");
            finishSelf();
        }
    }

    private void cacheCMBApi() {
        /*CMBPayUtil->CMBApiFactory.createCMBAPI创建缓存过一次*/
        CMBApi cmbApi = CMBApiFactory.getCMBApi();
        if (null == cmbApi) {
            String lastCMBAppId = CMBPayUtil.getCMBAppId();
            if (!TextUtils.isEmpty(lastCMBAppId)) {
                CMBApiFactory.createCMBAPI(this, lastCMBAppId);
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

//    @Override
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            onBackPressed();
//            return true;
//        }
//        return super.onKeyDown(keyCode, event);
//    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CMBPayUtil.destroyCMBAPI();
    }

    @Override
    public void onBackPressed() {
        if (null != mWebView) {
            CMBResponse response = mWebView.getCMBResponse();
            mRespCode = response.respCode;
            mRespString = response.respMsg;
            handleRespMessage(false);
        }
    }

    private void handleRespMessage(boolean isOnClosed) {
        if (LText.isEmptyOrNull(mRespString)) {
            mRespString = "";
        }

        TLog.print(TAG, "H5 handleRespMessage isOnClosed: %b | respCode: %d | respMessage: %s",
                isOnClosed, mRespCode, mRespString);

        CMBPayUtil.sendCMBPayResultBroadcast(mRespCode, mRespString, false);

        finishSelf();
    }

    public void finishSelf() {
        AppUtil.finishActivity(this);
    }

}
