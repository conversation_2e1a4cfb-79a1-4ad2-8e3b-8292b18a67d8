package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.module.launcher.ProtocolWebviewActivity;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;

import net.bosszhipin.api.bean.ServerHighlightListBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/5/21
 */
public class AppFirstUseDialogUtil {

    public static final String SP_KEY = "show_first_use_dialog";

    private Activity activity;

    private DialogUtils.Builder firstUseDialogBuilder;

    private View.OnClickListener onConfirmListener,onTouristListener;

    public AppFirstUseDialogUtil(Activity activity, View.OnClickListener onConfirmListener) {
        this.activity = activity;
        this.onConfirmListener = onConfirmListener;
        firstUseDialogBuilder = getFirstUseAppDialog();
    }

    public void setTouristListener(View.OnClickListener onTouristListener) {
        this.onTouristListener = onTouristListener;
    }
    /**
     * 进入APP后弹框提示
     */
    private DialogUtils.Builder getFirstUseAppDialog() {
        List<CharSequence> descList = new ArrayList<>();
        descList.add(activity.getString(R.string.string_app_first_use_statement_1));
        descList.add(activity.getString(R.string.string_app_first_use_statement_2));

        String protocolContent = activity.getString(R.string.string_app_first_dialog_protocol_content);
        SpannableStringBuilder protocolBuilder = getProtocolSpanBuilder(protocolContent, getProtocolHighlightList(protocolContent));
        descList.add(protocolBuilder);

        DialogUtils.Builder builder = new DialogUtils.Builder(activity);
        builder.setDoubleButton();
        builder.setCancelable(false);
        builder.setTitle("欢迎使用BOSS直聘");
        builder.setDesc(descList);
        builder.setNegativeAction("拒绝", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                AnalyticsFactory.create().action(AnalyticsAction.ACTION_ACTIVE_LOGIN_AGREEMENT_HWPA)
//                        .param("p", 0)
//                        .build();
                showAfterCancelingUseAppDialog();
            }
        });
        builder.setPositiveAction("同意", onConfirmListener);
        return builder;
    }

    private SpannableStringBuilder getProtocolSpanBuilder(String content, List<ServerHighlightListBean> indexList) {
        int length = content.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(content);
        if (indexList != null) {
            int size = indexList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightListBean bean = indexList.get(i);
                if (bean == null) continue;
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                if (startIndex < 0 || endIndex <= startIndex || endIndex > length) continue;
                builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.app_green_dark)), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                builder.setSpan(new ClickableSpan() {
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                        ds.setUnderlineText(false);
                    }

                    @Override
                    public void onClick(@NonNull View widget) {
                        ((TextView) widget).setHighlightColor(Color.TRANSPARENT);
                        ProtocolWebviewActivity.startActivity(activity, bean.subUrl);
                    }
                }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    private List<ServerHighlightListBean> getProtocolHighlightList(String content) {
        String protocol11String = activity.getString(R.string.string_app_first_dialog_protocol_11);
        String protocol12String = activity.getString(R.string.string_app_first_dialog_protocol_12);

        List<ServerHighlightListBean> indexList = new ArrayList<>();
        // 《用户协议》
        ServerHighlightListBean protocol11 = new ServerHighlightListBean();
        protocol11.startIndex = getIndexes(protocol11String, content)[0];
        protocol11.endIndex = getIndexes(protocol11String, content)[1];
        protocol11.subUrl = URLConfig.WEB_URL_REGISTER_PROTOCOL;
        // 《隐私政策》
        ServerHighlightListBean protocol12 = new ServerHighlightListBean();
        protocol12.startIndex = getIndexes(protocol12String, content)[0];
        protocol12.endIndex = getIndexes(protocol12String, content)[1];
        protocol12.subUrl = URLConfig.WEB_URL_PRIVACY_PROTOCOL;

        indexList.add(protocol11);
        indexList.add(protocol12);
        return indexList;
    }

    private int[] getIndexes(@NonNull String target, @NonNull String total) {
        int[] idx = new int[2];
        idx[0] = total.indexOf(target);
        idx[1] = idx[0] + target.length();
        return idx;
    }

    public void showFirstUseAppDialog() {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        if (firstUseDialogBuilder != null) {
            firstUseDialogBuilder.build().show();
        }
        CommonConfigManager.getInstance().setTourist(false);
    }

    private DialogUtils.Builder getCancelingDialogBuilder() {
        DialogUtils.Builder builder = new DialogUtils.Builder(activity);
        builder.setDoubleButton();
        builder.setCancelable(false);
        builder.setTitle(R.string.warm_prompt);
        builder.setDesc("我们非常重视对您个人信息的保护，值得您的信赖。如不同意该政策，很遗憾我们将无法为您提供完整版服务。\n如果您愿意打开网络等基础功能开关，我们可为您提供游客模式。");
        builder.setNegativeAction("我再想想", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showFirstUseAppDialog();
            }
        });
        builder.setPositiveAction("游客模式",onTouristListener);
        return builder;
    }

    private void showAfterCancelingUseAppDialog() {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        DialogUtils.Builder cancelingDialogBuilder = getCancelingDialogBuilder();
        cancelingDialogBuilder.build().show();
    }

    public static void showPrivacyDialog(Activity activity) {
        DialogUtils d = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle("隐私提示")
                .setDesc("同意隐私政策后您可体验该服务，我们将妥善保护您的隐私。")
                .setNegativeAction("取消")
                .setPositiveAction("去授权", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        App.get().finishAll();
                        Intent intent = new Intent(activity, WelcomeActivity.class);
                        AppUtil.startActivity(activity, intent, ActivityAnimType.NONE);
                        CommonConfigManager.getInstance().setTourist(false);
                    }
                })
                .build();
        d.show();
    }

    public static void showPrivacyDialogBoss(Activity activity) {
        DialogUtils d = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle("隐私提示")
                .setDesc("我们暂未对Boss身份提供游客模式。同意隐私政策后您可体验App正常服务，我们将妥善保护您的隐私。")
                .setNegativeAction("取消")
                .setPositiveAction("去授权", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        App.get().finishAll();
                        Intent intent = new Intent(activity, WelcomeActivity.class);
                        AppUtil.startActivity(activity, intent, ActivityAnimType.NONE);
                        CommonConfigManager.getInstance().setTourist(false);
                    }
                })
                .build();
        d.show();
    }

    public static void showToLoginDialog(Activity activity) {
        DialogUtils d = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle("提示")
                .setDesc("登录后即可体验该服务。")
                .setNegativeAction("取消")
                .setPositiveAction("去登录", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        App.get().finishAll();
                        CommonConfigManager.getInstance().setTourist(false);
                        LoginRouter.startLoginActivity(activity, ROLE.GEEK.get(), false);
                    }
                })
                .build();
        d.show();
    }
}
