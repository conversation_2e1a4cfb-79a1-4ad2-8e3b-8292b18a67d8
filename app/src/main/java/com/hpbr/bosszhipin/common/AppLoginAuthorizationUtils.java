package com.hpbr.bosszhipin.common;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.BottomView;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import zpui.lib.ui.popup.cookie.ZPUICookieBar;

/**
 * Author: zhouyou
 * Date: 2021/4/23
 * App注册登录流程授权信息弹框
 */
public class AppLoginAuthorizationUtils {


    private final FragmentActivity activity;
    private final OnActionCallback callback;
    private final int popType;

    private BottomView bottomView;

    private ZPUICookieBar cookieBar;

    class PostNotificationCallback implements OnActionCallback {
        OnActionCallback callback;

        public PostNotificationCallback(OnActionCallback callback) {
            this.callback = callback;
        }

        @Override
        public void executeAction() {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // android 13以上请求通知权限
                PermissionHelper.getPostNotificationHelperLite(activity).setPermissionCallback((yes, permission) -> {
                    if (callback != null) {
                        callback.executeAction();
                    }
                }).requestPermission();
            } else {//走老逻辑
                if (callback != null) {
                    callback.executeAction();
                }
            }
        }
    }
    public AppLoginAuthorizationUtils(FragmentActivity activity, int popType, OnActionCallback callback) {
        this.activity = activity;
        this.popType = popType;
        this.callback = new PostNotificationCallback(callback);
    }

    public void dispatch() {
        if (isNewUser()) {//无论有没有权限以及是否弹出过权限申请，针对新用户都需要弹隐私说明弹框
            showNewUserDialog();
        } else {
            if (callback != null) {
                callback.executeAction();
            }
        }
    }

    /**
     * 新用户显示查看用户协议
     */
    private void showNewUserDialog() {
        if (!ActivityUtils.isValid(activity)) {
            return;
        }
        bottomView = getNewUserDialog();
        bottomView.showBottomView(true);

        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_PRIVACY_AGREEMENT)
                .build();
    }

    /**
     * 获取新用户弹框
     *
     * @return
     */
    private BottomView getNewUserDialog() {
        @SuppressLint("InflateParams") View view = LayoutInflater.from(activity).inflate(R.layout.dialog_agree_then_continue, null);
        TextView tv_see_agreement = view.findViewById(R.id.see_agreement);
        tv_see_agreement.setMovementMethod(LinkMovementMethod.getInstance());
        tv_see_agreement.setText(getNewUserProtocolString(), TextView.BufferType.SPANNABLE);
        view.findViewById(R.id.btn_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissBottomView();
                AnalyticsFactory.create()
                        .action(AnalyticsAction.ACTION_PRIVACY_AGREEMENT_CLICK)
                        .param("p", "0")
                        .param("p2", 0)
                        .build();
            }
        });
        view.findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissBottomView();
                AnalyticsFactory.create()
                        .action(AnalyticsAction.ACTION_PRIVACY_AGREEMENT_CLICK)
                        .param("p", "1")
                        .param("p2", 0)
                        .build();

                if (callback != null) {
                    callback.executeAction();
                }
            }
        });
        BottomView bottomView = new BottomView(activity, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        return bottomView;
    }

    private void dismissBottomView() {
        if (bottomView != null && bottomView.isShowing()) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
        if (cookieBar != null) {
            cookieBar.dismiss();
        }
    }

    /**
     * 隐私协议字符串
     *
     * @return
     */
    private SpannableStringBuilder getNewUserProtocolString() {
        SpannableStringBuilder ssb = new SpannableStringBuilder("《用户协议》《隐私政策》");

        ssb.setSpan(new ClickableSpan() {

            @Override
            public void onClick(View widget) {
                ((TextView) widget).setHighlightColor(Color.TRANSPARENT);
                Intent intent = new Intent(activity, WebViewActivity.class);
                intent.putExtra(Constants.DATA_URL, URLConfig.WEB_URL_REGISTER_PROTOCOL);
                AppUtil.startActivity(activity, intent);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(ContextCompat.getColor(activity, R.color.app_green));
                ds.setUnderlineText(false);
            }

        }, 0, 6, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        ssb.setSpan(new ClickableSpan() {

            @Override
            public void onClick(View widget) {
                ((TextView) widget).setHighlightColor(Color.TRANSPARENT);
                Intent intent = new Intent(activity, WebViewActivity.class);
                intent.putExtra(Constants.DATA_URL, URLConfig.WEB_URL_PRIVACY_PROTOCOL);
                AppUtil.startActivity(activity, intent);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(ContextCompat.getColor(activity, R.color.app_green));
                ds.setUnderlineText(false);
            }
        }, 6, 6 + 6, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        return ssb;
    }

    private boolean isNewUser() {
        return popType == PopType.POP_NEW_USER;
    }

    public interface PopType {
        int POP_DEFAULT = 1; // 老用户默认弹框弹出样式
        int POP_NEW_USER = 2; // 新用户弹框弹出样式
    }

    public interface OnActionCallback {
        void executeAction();
    }
}
