package com.hpbr.bosszhipin.common;

import android.content.ComponentCallbacks2;

import com.facebook.common.memory.MemoryTrimType;
import com.facebook.common.memory.MemoryTrimmable;
import com.facebook.common.memory.MemoryTrimmableRegistry;
import com.hpbr.bosszhipin.common.helper.PreloadWebViewTool;
import com.techwolf.lib.tlog.TLog;

import java.util.Iterator;
import java.util.Vector;

/**
 * Created by monch on 2016/11/16.
 */

public class AppTrimMemoryCommon {

    public static void release(int level) {
        releaseFrescoTrimmableSet(level);
        // 此处调用GC会有部分引发Crash
//        System.gc();
        releasePreWebView(level);
    }

    public static FrescoTrimmableRegister getFrescoTrimmableRegister() {
        return new FrescoTrimmableRegister();
    }

    // Fresco的内存引用
    private static Vector<MemoryTrimmable> mFrescoTrimmableSet = new Vector<>();

    private static void releaseFrescoTrimmableSet(int level) {
        try {
            Iterator<MemoryTrimmable> iterator = mFrescoTrimmableSet.iterator();
            while (iterator.hasNext()) {
                if (level >= ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
                    iterator.next().trim(MemoryTrimType.OnAppBackgrounded);
                } else {
                    iterator.next().trim(MemoryTrimType.OnCloseToDalvikHeapLimit);
                }
            }
        }
        //如果缓存中加载了fresco图片，切后台这里会崩溃
        //fresco目前已知bug，未解决「https://github.com/facebook/fresco/issues/2676」
        catch (Exception e) {
            TLog.info("AppTrimMemoryCommon", "fresco release image catch");
        }
    }

    private static class FrescoTrimmableRegister implements MemoryTrimmableRegistry {

        @Override
        public void registerMemoryTrimmable(MemoryTrimmable trimmable) {
            mFrescoTrimmableSet.add(trimmable);
        }

        @Override
        public void unregisterMemoryTrimmable(MemoryTrimmable trimmable) {
            mFrescoTrimmableSet.remove(trimmable);
        }
    }

    private static void releasePreWebView(int level) {
        if (level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE ||
                level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW ||
                level == ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL) {
            PreloadWebViewTool.getInstance().releaseWebView();
        }
    }


}
