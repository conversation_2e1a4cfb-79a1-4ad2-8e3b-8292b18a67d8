package com.hpbr.bosszhipin.common;

import android.content.Intent;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.ReceiverUtils;

/**
 * Author: <PERSON>
 * Date: 2019/01/04.
 * Boss F2互动列表变更发生的广播
 */
public class BossInteractTabChangedUtils {

    public static final String SHOW_F2_I_SEE_ENTRANCE = Constants.PREFIX + ".SHOW_F2_I_SEE_ENTRANCE";

    /**
     * @param isShowISee 是否展示我看过入口
     */
    public static void sendBroadcast(boolean isShowISee) {
        //新旧两次Boss受阻断的状态不同，则需要添加或移出‘F2的我看过’
        Intent intent = new Intent(Constants.REFRESH_F2_INTERACT_TAB_CHANGE_ACTION);
        intent.putExtra(SHOW_F2_I_SEE_ENTRANCE, isShowISee);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }
}
