package com.hpbr.bosszhipin.common;

import android.Manifest;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.provider.CalendarContract;
import android.provider.CalendarContract.Calendars;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LText;

import java.util.Map;
import java.util.TimeZone;

/**
 * Created by monch on 16/7/20.
 */
public class CalendarCommon {

    private static final String TAG = "CalendarCommon";

    private static final String DEFAULT_NAME = "BOSS直聘";
    private static final String DEFAULT_ACCOUNT_NAME = "<EMAIL>";
    private static final String DEFAULT_ACCOUNT_TYPE = CalendarContract.ACCOUNT_TYPE_LOCAL;

    // 帐户ID 静态存储
    private static long accountId;

    // 上下文
    private Context context;

    // 构造
    public CalendarCommon(Context context) {
        this.context = context;
    }

    /**
     * 初始化帐户
     */
    private void initAccount() {
        if (accountId <= 0) {
            accountId = addAccountId();
        }
    }

    /**
     * 添加日历帐户
     *
     * @return 帐户ID
     */
    private long addAccountId() {
        ContentResolver contentResolver = context.getContentResolver();
        TimeZone timeZone = TimeZone.getDefault();
        ContentValues value = new ContentValues();
        value.put(Calendars.NAME, DEFAULT_NAME);    // 日历的名称
        value.put(Calendars.ACCOUNT_NAME, DEFAULT_ACCOUNT_NAME);    // 帐户名称
        value.put(Calendars.ACCOUNT_TYPE, DEFAULT_ACCOUNT_TYPE);    // 帐户类型
        value.put(Calendars.CALENDAR_DISPLAY_NAME, DEFAULT_NAME);   // 日历显示的名称
        value.put(Calendars.VISIBLE, 1);    // 日历关联的事件显示
        value.put(Calendars.CALENDAR_COLOR, ContextCompat.getColor(context, R.color.app_green));  // 日历颜色
        value.put(Calendars.CALENDAR_ACCESS_LEVEL, Calendars.CAL_ACCESS_OWNER); // 帐户级别 权限
        value.put(Calendars.SYNC_EVENTS, 1);    // 同步事件，1显示，0不显示
        value.put(Calendars.CALENDAR_TIME_ZONE, timeZone.getID());  // 时区
        value.put(Calendars.OWNER_ACCOUNT, DEFAULT_ACCOUNT_NAME);
        Uri calendarUri = Calendars.CONTENT_URI;
        calendarUri = calendarUri.buildUpon()
                .appendQueryParameter(CalendarContract.CALLER_IS_SYNCADAPTER, "true")
                .appendQueryParameter(Calendars.ACCOUNT_NAME, DEFAULT_ACCOUNT_NAME)
                .appendQueryParameter(Calendars.ACCOUNT_TYPE, DEFAULT_ACCOUNT_TYPE)
                .build();
        Uri uri = contentResolver.insert(calendarUri, value);
        if (uri != null) {
            return Long.parseLong(uri.getLastPathSegment());
        }
        return 0;
    }

    /**
     * 添加事件通知
     *
     * @param title
     * @param description
     * @param startTimeMillis
     * @param endTimeMillis
     * @param remindMinutes
     * @return
     */
    public long addEvent(String title,
                         String description,
                         long startTimeMillis,
                         long endTimeMillis,
                         int remindMinutes, long id) {
        return addEvent(title, description, startTimeMillis, endTimeMillis, remindMinutes, String.valueOf(id));
    }

    public long addEvent(String title,
                         String description,
                         long startTimeMillis,
                         long endTimeMillis,
                         int remindMinutes, String id) {
        if (!checkPermission(context, Manifest.permission.WRITE_CALENDAR)) return 0;
        initAccount();
        if (accountId <= 0) return 0;
        ContentResolver contentResolver = context.getContentResolver();
        // 添加
        TimeZone timeZone = TimeZone.getDefault();
        ContentValues eventValue = new ContentValues();
        eventValue.put(CalendarContract.Events.CALENDAR_ID, accountId);    // 日历的ID
        eventValue.put(CalendarContract.Events.TITLE, title);  // 事件的标题
        eventValue.put(CalendarContract.Events.DESCRIPTION, description);   // 事件的描述
        eventValue.put(CalendarContract.Events.DTSTART, startTimeMillis);   // 事件启动时间
        eventValue.put(CalendarContract.Events.DTEND, endTimeMillis); // 事件结束时间
        eventValue.put(CalendarContract.Events.EVENT_TIMEZONE, timeZone.getID());   // 事件的时区
        eventValue.put(CalendarContract.Events.ALL_DAY, 0); // 1代表全天，0为普通
        long eventId = 0L;
        try {
            Uri eventUri = contentResolver.insert(CalendarContract.Events.CONTENT_URI, eventValue);
            if (eventUri == null) {
                return 0;
            }
            eventId = LText.getLong(eventUri.getLastPathSegment());
            ContentValues reminderValue = new ContentValues();
            reminderValue.put(CalendarContract.Reminders.EVENT_ID, eventId);
            reminderValue.put(CalendarContract.Reminders.MINUTES, remindMinutes);
            reminderValue.put(CalendarContract.Reminders.METHOD, CalendarContract.Reminders.METHOD_ALERT);
            contentResolver.insert(CalendarContract.Reminders.CONTENT_URI, reminderValue);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            SpManager.get().user(TAG).edit().putLong(id, eventId).apply();
            NotifyUtils.allowedWriteAgendaToCalendar(true);//添加完日程，自动打开日历开关（设置-隐私设置-权限管理）
        }
        return eventId;
    }

    public void removeEvent(long id) {
        removeEvent(String.valueOf(id));
    }

    public void removeEvent(String id) {
        if (!checkPermission(context, Manifest.permission.WRITE_CALENDAR)) return;
        initAccount();
        if (accountId <= 0) return;
        ContentResolver contentResolver = context.getContentResolver();

        long eventId = SpManager.get().user(TAG).getLong(id, 0L);
        if (eventId > 0) {
            Uri deleteUri = ContentUris.withAppendedId(CalendarContract.Events.CONTENT_URI, eventId);
            try {
                int result = contentResolver.delete(deleteUri, null, null);
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            } finally {
                SpManager.get().user(TAG).edit().remove(id).apply();
            }
        }
    }


    public static boolean checkPermission(Context context, String permission) {
        return ContextCompat.checkSelfPermission(context, permission) ==
                PackageManager.PERMISSION_GRANTED;
    }

    public static long getInterviewIdWithUrl(String url) {
        long id = -1;
        Map<String, String> interviewParams = ZPManager.UrlHandler.getParams(url);
        if (!TextUtils.isEmpty(interviewParams.get("interviewid"))) {
            try {
                id = Long.valueOf(interviewParams.get("interviewid"));
            } catch (Exception e) {

            }
        }
        return id;
    }

}
