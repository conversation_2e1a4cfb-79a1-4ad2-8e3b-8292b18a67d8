package com.hpbr.bosszhipin.common;

import android.app.Activity;

import com.hpbr.bosszhipin.common.dialog.CustomWeCallStepOneDialog;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.report.AutoReport;
import com.hpbr.bosszhipin.utils.StringUtil;

/**
 * Author: zhouyou
 * Date: 2022/6/10
 */
public class CustomWebCallUtils {

    private final String phoneNumber;
    private final String source;

    private final Activity activity;
    private CustomWeCallStepOneDialog stepOneDialog;

    public CustomWebCallUtils(Activity activity, String phoneNumber, String source) {
        this.activity = activity;
        this.phoneNumber = phoneNumber;
        this.source = source;
    }


    public void show() {
        showStepOneDialog();
    }

    private void showStepOneDialog() {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BOSS_REQUIRE_CONSULT_DETAIL)
                .param("p2", source)
                .build();
        stepOneDialog = new CustomWeCallStepOneDialog(activity, phoneNumber, new CustomWeCallStepOneDialog.ActionListener() {

            @Override
            public void onHotLineConnect(String mPhoneNumber) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BOSS_REQUIRE_CONSULT_DETAIL)
                        .param("p2", source)
                        .param("p3", 2)
                        .build();
                new AutoReport(AutoReport.CALL_HOT_LINE).report();
                StringUtil.dial(activity, mPhoneNumber);
            }
        });
        stepOneDialog.show();
    }
}
