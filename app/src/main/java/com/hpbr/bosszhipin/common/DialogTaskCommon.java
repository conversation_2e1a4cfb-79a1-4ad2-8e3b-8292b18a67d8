package com.hpbr.bosszhipin.common;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatDialogTaskBean;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 2017/6/13.
 */

public class DialogTaskCommon {
    public static final String KEY = "ChatDialogTask";

    public static void handler(ChatDialogTaskBean bean) {
        if (App.get().isForeground()) {
            // 处理前台，直接处理
            handler.postDelayed(new HandlerRunnable(bean), 2000);
        } else {
            // 处理后台，保存待处理
            saveDialogTask(bean);
        }
    }

    private static void saveDialogTask(ChatDialogTaskBean bean) {
        String json_str = SpManager.get().user().getString(KEY, "");
        List<ChatDialogTaskBean> list = null;
        if (!TextUtils.isEmpty(json_str)) {
            list = new ArrayList<>(GsonUtils.fromJson(json_str, new TypeToken<List<ChatDialogTaskBean>>() {
            }.getType()));
        }
        if (list == null) list = new ArrayList<>();
        list.add(bean);
        SpManager.get().user().edit().putString(KEY, GsonUtils.toJson(list)).apply();
    }

    private static final Handler handler = new Handler(Looper.getMainLooper());

    public static void handlerAll() {
        AppThreadFactory.POOL.submit(queryRunnable);
    }

    private static final Runnable queryRunnable = new Runnable() {
        @Override
        public void run() {
            String json_str = SpManager.get().user().getString(KEY, "");
            if (TextUtils.isEmpty(json_str)) return;
            List<ChatDialogTaskBean> list = GsonUtils.fromJson(json_str, new TypeToken<List<ChatDialogTaskBean>>() {
            }.getType());
            if (LList.isEmpty(list)) return;

            for (ChatDialogTaskBean bean : list) {
                if (bean != null && bean.type == 9) {
                    if (bean.endTime > System.currentTimeMillis()) {
                        handler.postDelayed(new HandlerRunnable(bean), 2000);
                        break;
                    }
                }
            }
            for (ChatDialogTaskBean bean : list) {
                if (bean != null && bean.type == 10) {
                    if (bean.endTime > System.currentTimeMillis()) {
                        handler.postDelayed(new HandlerRunnable(bean), 2000);
                        break;
                    }
                }
            }
            SpManager.get().user().edit().remove(KEY).apply();
        }
    };

    private static class HandlerRunnable implements Runnable {
        private ChatDialogTaskBean bean;

        HandlerRunnable(ChatDialogTaskBean bean) {
            this.bean = bean;
        }

        @Override
        public void run() {
            if (bean != null && bean.endTime > System.currentTimeMillis()) {
                Context context = App.get().getContext();
                if (context == null) {
                    Log.d("DialogTask", "Context === null");
                    return;
                }
                if (!(context instanceof Activity)) {
                    Log.d("DialogTask", "context is not Activity");
                    return;
                }

                final Activity activity = (Activity) context;
                if (bean.type == 9) {
                    handlerInterviewDialog(activity, bean);
                    maidian(bean.pointStatics, false);
                } else if (bean.type == 10) {
                    // 查看定位权限
                    handlerLocation(activity, bean);
                }
            }
        }
    }

    // 面试弹窗
    private static void handlerInterviewDialog(final Activity activity, final ChatDialogTaskBean bean) {
        DialogUtils dialogUtils = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle(bean.title)
                .setDesc(bean.desc)
                .setNegativeAction(bean.leftButton, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!TextUtils.isEmpty(bean.leftProtocol)) {
                            ZPManager manager = new ZPManager(activity, bean.leftProtocol);
                            manager.handler();
                        }
                    }
                })
                .setPositiveAction(bean.rightButton, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        new ZPManager(activity, bean.rightProtocol).handler();
                    }
                })
                .build();
        if (!activity.isFinishing()) {
            dialogUtils.show();
        }
    }

    // 处理定位
    private static void handlerLocation(final Activity activity, final ChatDialogTaskBean bean) {
        if (!PermissionManager.checkAllSelfPermissions(App.getAppContext(), Manifest.permission.ACCESS_FINE_LOCATION)) {
            showLocationDialog(bean);
        } else {
            locationHandler(activity, bean);
        }
    }

    private static void showLocationDialog(final ChatDialogTaskBean bean) {
        // 没有定位权限
        Context context = App.get().getContext();
        if (context == null || !(context instanceof Activity)) return;
        final Activity activity = (Activity) context;
        DialogUtils dialogUtils = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle(bean.title)
                .setDesc(bean.desc)
                .setNegativeAction(bean.leftButton, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (!TextUtils.isEmpty(bean.leftProtocol)) {
                            ZPManager manager = new ZPManager(activity, bean.leftProtocol);
                            manager.handler();
                        }
                    }
                })
                .setPositiveAction(bean.rightButton, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        isInSettingPage = true;
                        activity.startActivity(new Intent(Settings.ACTION_SETTINGS));
                    }
                })
                .build();
        if (!activity.isFinishing()) {
            dialogUtils.show();
        }
        maidian(bean.pointStatics, true);
    }

    private static void locationHandler(final Context context, final ChatDialogTaskBean bean) {
        final LocationService location = new LocationService(context);
        location.setOnLocationCallback(new LocationService.OnLocationCallback() {
            @Override
            public void onLocationCallback(boolean success, LocationService.LocationBean locationBean) {
                if (!success || locationBean == null) {
                    showLocationDialog(bean);
                }
            }
        });
        location.start();
    }

    private static boolean isInSettingPage = false;

    public static void handlerSettingBack(final Context context) {
        if (!isInSettingPage) return;
        isInSettingPage = false;
        if (!PermissionManager.checkAllSelfPermissions(App.getAppContext(), Manifest.permission.ACCESS_FINE_LOCATION))
            return;
        final LocationService location = new LocationService(context);
        location.setOnLocationCallback(new LocationService.OnLocationCallback() {
            @Override
            public void onLocationCallback(boolean success, LocationService.LocationBean locationBean) {
                if (!success || locationBean == null) return;
//                new UploadLocationCommon().upload(context, locationBean, true);
            }
        });
        location.start();
    }

    private static void maidian(String string, boolean isNotPermission) {
        if (TextUtils.isEmpty(string)) return;
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(string);
        } catch (JSONException e) {
            e.printStackTrace();
            return;
        }
        String action = jsonObject.optString("action");
        String p3 = jsonObject.optString("p3");
        String p5 = jsonObject.optString("p5");
        if (TextUtils.isEmpty(action)) return;
        AnalyticsFactory factory = AnalyticsFactory.create();
        factory.action(action);
        factory.param("p3", p3);
        if (isNotPermission) {
            factory.param("p5", "1");
        } else if (!TextUtils.isEmpty(p5)) {
            factory.param("p5", p5);
        }
        factory.build();
    }



}
