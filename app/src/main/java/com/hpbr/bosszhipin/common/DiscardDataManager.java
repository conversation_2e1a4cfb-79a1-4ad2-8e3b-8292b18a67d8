package com.hpbr.bosszhipin.common;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.DiscardLastMessageRequest;
import net.bosszhipin.api.ResultStringResponse;
import net.bosszhipin.base.ApiRequestCallback;

import message.handler.dao.MessageDaoFactory;

/**
 * Created by wang<PERSON> on 2017/8/12.
 */

public class DiscardDataManager {
    public static final String TAG = DiscardDataManager.class.getSimpleName();
    public static final String SP_NAME = "discard_message_info";
    private SharedPreferences sp;

    public static final long TIME_LIMIT = 12 * 60 * 60 * 1000;

    private static class SingleHolder {
        private static final DiscardDataManager mInstance = new DiscardDataManager();
    }

    private DiscardDataManager() {
        sp = App.getAppContext().getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
    }

    public static DiscardDataManager getInstance() {
        return SingleHolder.mInstance;
    }

    private void putLong(String key, long value) {
        SharedPreferences.Editor editor = sp.edit();
        editor.putLong(key, value);
        editor.commit();
    }

    //最大消息的id
    private static String makeMaxMsgIdKey() {
        return "message_id" + "_" + UserManager.getUserRole().get() + "_" + UserManager.getUID();
    }


    //更新的时间
    private static String makeUpdateTimeKey() {
        return "id_update_time" + "_" + UserManager.getUserRole().get() + "_" + UserManager.getUID();
    }


    /***
     *
     * 执行具体的删除操作
     * **/
    public synchronized void deleteDiscardMessage() {
        long messageId = sp.getLong(makeMaxMsgIdKey(), -1);
        TLog.info("discard", "local messageId："+messageId);
        if (messageId > 0) {
            int result = MessageDaoFactory.getMessageDao().deleteListByMaxId(messageId);
            TLog.info("discard", "====delete history result:" + result + " serverId:" + messageId);
            putLong(makeMaxMsgIdKey(), -1);
        }

        if (!UserManager.isCurrentLoginStatus()) {
            return;
        }

//        long lastUpdateTime = sp.getLong(makeUpdateTimeKey(), -1);
//        if (System.currentTimeMillis() - lastUpdateTime < TIME_LIMIT) {
//            L.info("discard", "deleteDiscardMessage local [%s]", "不需要请求最大id");
//            return;
//        }
        L.info("discard", "deleteDiscardMessage local [%s]", "开始请求最大id");

        DiscardLastMessageRequest request = new DiscardLastMessageRequest(new ApiRequestCallback<ResultStringResponse>() {
            @Override
            public void onSuccess(ApiData<ResultStringResponse> data) {

                String id = data.resp.result;
                TLog.info("discard", "deleteDiscardMessage id："+id);
                if (!TextUtils.isEmpty(id)) {
                    putLong(makeMaxMsgIdKey(), Long.valueOf(id));
//                    putLong(makeUpdateTimeKey(), System.currentTimeMillis());
                    L.info("discard", "message will discard and id = [%s]", id);
                } else {
                    L.info("discard", "deleteDiscardMessage net onComplete [%s]", "error");
                }

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.info("discard", "deleteDiscardMessage net [%s]", "error");
            }
        });
        HttpExecutor.execute(request);


    }
}
