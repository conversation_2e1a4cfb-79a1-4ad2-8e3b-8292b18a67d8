package com.hpbr.bosszhipin.common;

import android.graphics.Bitmap;

import androidx.annotation.NonNull;

import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;

/**
 * Created by monch on 15/5/22.
 */
public class DownloadPhotoCommon {

    public interface OnDownloadCallback {
        void onDownloadComplete(Bitmap bitmap);
        void onDownloadFailed();
    }

    private OnDownloadCallback callback;

    public void setOnDownloadCallback(OnDownloadCallback callback) {
        this.callback = callback;
    }

    public void onNewDownloadTask(String url) {
        ImageRequest imageRequest = ImageRequest.fromUri(url);
        ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>>
                dataSource = imagePipeline.fetchDecodedImage(imageRequest, null);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            @Override
            protected void onNewResultImpl(Bitmap bitmap) {
                if (callback != null) {
                    callback.onDownloadComplete(bitmap);
                }
            }

            @Override
            protected void onFailureImpl(@NonNull DataSource<CloseableReference<CloseableImage>> dataSource) {
                if (callback != null) {
                    callback.onDownloadFailed();
                }
            }
        }, AppThreadFactory.POOL);
    }

}
