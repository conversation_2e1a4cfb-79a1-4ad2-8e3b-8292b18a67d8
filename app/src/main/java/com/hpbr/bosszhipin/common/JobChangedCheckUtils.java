package com.hpbr.bosszhipin.common;

import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LText;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.utils.GsonUtils;
import com.twl.utils.MD5;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2017/12/18.
 * 影响F1职位列表刷新的工具
 */
public class JobChangedCheckUtils {

    /**
     * 1. 职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_LIST_CHANGED_TAG;
    /**
     * 2. 热招职位
     */
    private static String JOB_HOT_TAG;
    /**
     * 3. 未付费职位
     */
    private static String JOB_UNPAID_TAG;
    /**
     * 职位排序时，强行刷新逻辑
     */
    private static boolean FORCE_REFRESH;

    /**
     * 发送刷新F1的广播
     */
    public static void sendJobListChangeBroadcast() {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_JOB_LIST_CHANGED_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * 初始化职位字符串摘要
     */
    public static void init() {
        try {
            UserBean user = UserManager.getLoginUser();
            if (user != null && user.bossInfo != null) {
                List<JobBean> jobList = BossJobListProvider.getInstance().getJobListReadOnly();

                Gson gson = GsonUtils.getExcludeFieldsWithoutExposeGson();
                String jobString = gson.toJson(jobList);
                JOB_LIST_CHANGED_TAG = MD5.convert(jobString);
                JOB_HOT_TAG = user.hotJobIds;
                JOB_UNPAID_TAG = user.unpaidList;
            }
        } catch (Exception e) {
            CrashReport.postCatchedException(e);
        }
    }

    /**
     * 是否两者都有变化
     *
     * @return
     */
    public static boolean isChanged() {
        return isJobListChanged() || isJobListUnpaidChanged() || isJobListHotChanged();
    }

    /**
     * 判断本地职位是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isJobListChanged() {
        try {
            UserBean user = UserManager.getLoginUser();
            if (user != null && user.bossInfo != null) {
                List<JobBean> jobList = BossJobListProvider.getInstance().getJobListReadOnly();
                Gson gson = GsonUtils.getExcludeFieldsWithoutExposeGson();
                String jobString = gson.toJson(jobList);
                String newDigest = MD5.convert(jobString);
                if (!LText.equal(newDigest, JOB_LIST_CHANGED_TAG)) {
                    JOB_LIST_CHANGED_TAG = newDigest;
                    return true;
                }
            }
        } catch (Exception e) {
            CrashReport.postCatchedException(e);
            return true;
        }
        return false;
    }

    /**
     * 判断本地热招职位是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isJobListHotChanged() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && !TextUtils.equals(user.hotJobIds, JOB_HOT_TAG)) {
            JOB_HOT_TAG = user.hotJobIds;
            return true;
        }
        return false;
    }

    /**
     * 判断本地未付费职位是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isJobListUnpaidChanged() {
        UserBean user = UserManager.getLoginUser();
        if (user != null && !TextUtils.equals(user.unpaidList, JOB_UNPAID_TAG)) {
            JOB_UNPAID_TAG = user.unpaidList;
            return true;
        }
        return false;
    }

    public static void setForceRefresh(boolean forceRefresh) {
        FORCE_REFRESH = forceRefresh;
    }

    public static boolean isForceRefresh() {
        return FORCE_REFRESH;
    }
}
