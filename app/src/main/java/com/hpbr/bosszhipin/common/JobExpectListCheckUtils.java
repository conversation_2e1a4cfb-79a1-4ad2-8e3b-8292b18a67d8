package com.hpbr.bosszhipin.common;

import android.content.Intent;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;
import com.twl.utils.MD5;

import net.bosszhipin.api.bean.CodeNameFlagBean;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2017/12/19.
 */
public class JobExpectListCheckUtils {
    private static  final  String TAG = "JobExpectListCheckUtils";

    public static final  String STU_SELECTED_CODE="stu_selected_code";

    /**
     * 职场人职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_TAG;


    /**
     * 学生职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_TAG_STU;

    /**
     * 混合职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_MIXED_TAG;


    /**
     * 兼职期望职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_PARTIME_TAG;

    /**
     * 境外期望职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_OVERSEAS_TAG;

    /**
     * 零工
     */
    private static String JOB_CHANGED_ODD_JOB_TAG;


    /**
     * 学生：混合职位字符串摘要，用来判断职位因为增删改所带来的页面刷新
     */
    private static String JOB_CHANGED_MIXED_TAG_FOR_STU;

    /**
     * 期望类型
     */
    private static String JOB_EXPECT_TYPE_TAG;

    /**
     * 强行刷新逻辑
     */
    private static boolean FORCE_REFRESH;

    /**
     * 发送刷新F1的广播
     */
    public static void sendExpectListChangeBroadcast() {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_GEEK_EXPECT_LIST_CHANGED_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }


    public static void sendStuChangeBroadcast(long code ) {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_STUDENT_EXPECT_TYPE_CHANGE_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        intent.putExtra(STU_SELECTED_CODE,code);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }


    /**
     * 初始化职位字符串摘要
     */
    public static void init() {
        // 期望
        List<JobIntentBean> expectList = GeekExpectManager.getGeekProExpectList();
        if (!LList.isEmpty(expectList)) {
            String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectList);
            JOB_CHANGED_TAG = MD5.convert(expectString);
        }


        // 职场人混推期望
        JobIntentBean mixedExpect = GeekExpectManager.getGeekMixedExpect();
        String mixedString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(mixedExpect);
        JOB_CHANGED_MIXED_TAG = MD5.convert(mixedString);

        // 职场人兼职期望
        JobIntentBean partimeExpect = GeekExpectManager.getGeekProPartimeExpect();
        String partimeExpectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(partimeExpect);
        JOB_CHANGED_PARTIME_TAG = MD5.convert(partimeExpectString);

        // 职场人境外期望
        JobIntentBean overSeasExpect = GeekExpectManager.getOverseasExpect();
        String overSeasExpectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(overSeasExpect);
        JOB_CHANGED_OVERSEAS_TAG = MD5.convert(overSeasExpectString);


        // 零工
        JobIntentBean oddJobExpect = GeekExpectManager.getOddJobExpect();
        String oddJobExpectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(oddJobExpect);
        JOB_CHANGED_ODD_JOB_TAG = MD5.convert(oddJobExpectString);


    }

    public static void initStudent() {
        // 期望
        List<JobIntentBean> expectListstu = GeekExpectManager.getGeekStuExpectList();
        if (!LList.isEmpty(expectListstu)) {
            String expectStringStu = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectListstu);
            JOB_CHANGED_TAG_STU = MD5.convert(expectStringStu);
        }


        // 学生期望类型
        List<CodeNameFlagBean> expectTypeList = GeekExpectManager.getGeekExpectTypeList();
        if (!LList.isEmpty(expectTypeList)) {

            String expectTypeString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectTypeList);
            JOB_EXPECT_TYPE_TAG = MD5.convert(expectTypeString);

        }


        // 学生综合期望
        JobIntentBean mixedExpectStu = GeekExpectManager.getStuCombineRcmdExpect();
        String mixedExpectStuString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(mixedExpectStu);
        JOB_CHANGED_MIXED_TAG_FOR_STU = MD5.convert(mixedExpectStuString);

    }


    /**
     * 判断本地虚拟期望是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isGeekMixedExpectChanged() {

        JobIntentBean mixedExpect = GeekExpectManager.getGeekMixedExpect();
        String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(mixedExpect);
        String newDigest = MD5.convert(expectString);
        if (!LText.equal(newDigest, JOB_CHANGED_MIXED_TAG)) {
            JOB_CHANGED_MIXED_TAG = newDigest;
            return true;
        }
        return false;
    }


    /**
     * 判断本地兼职期望是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isGeekPartimeExpectChanged() {

        JobIntentBean mixedExpect = GeekExpectManager.getGeekProPartimeExpect();
        String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(mixedExpect);
        String newDigest = MD5.convert(expectString);
        if (!LText.equal(newDigest, JOB_CHANGED_PARTIME_TAG)) {
            JOB_CHANGED_PARTIME_TAG = newDigest;
            return true;
        }
        return false;
    }


    private static boolean isGeekOverseasChanged() {

        JobIntentBean overseasExpect = GeekExpectManager.getOverseasExpect();
        String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(overseasExpect);
        String newDigest = MD5.convert(expectString);
        if (!LText.equal(newDigest, JOB_CHANGED_OVERSEAS_TAG)) {
            JOB_CHANGED_OVERSEAS_TAG = newDigest;
            return true;
        }
        return false;
    }

    private static boolean isGeekOddJobChanged() {

        JobIntentBean oddJobExpect = GeekExpectManager.getOddJobExpect();
        String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(oddJobExpect);
        String newDigest = MD5.convert(expectString);
        if (!LText.equal(newDigest, JOB_CHANGED_ODD_JOB_TAG)) {
            JOB_CHANGED_ODD_JOB_TAG = newDigest;
            return true;
        }
        return false;
    }


    private static boolean isStuMixedExpectChanged() {

        JobIntentBean mixedExpectStu = GeekExpectManager.getStuCombineRcmdExpect();
        String expectStringStu = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(mixedExpectStu);
        String newDigestStu = MD5.convert(expectStringStu);
        if (!LText.equal(newDigestStu, JOB_CHANGED_MIXED_TAG_FOR_STU)) {
            JOB_CHANGED_MIXED_TAG_FOR_STU = newDigestStu;
            return true;
        }
        return false;
    }


    public static boolean isStuChanged() {
        return isStuExpectListChanged() || isStuMixedExpectChanged();
    }

    public static boolean isGeekproChanged() {

        boolean isExpectListChanged=isExpectListChanged();
        boolean isGeekMixedExpectChanged=isGeekMixedExpectChanged();
        boolean isGeekPartimeExpectChanged=isGeekPartimeExpectChanged();
        boolean isGeekOverseasChanged=isGeekOverseasChanged();
        boolean isGeekOddJobChanged=isGeekOddJobChanged();
        //原来的方式批量修改会造成 F1大刷变多，
        return isExpectListChanged || isGeekMixedExpectChanged || isGeekPartimeExpectChanged || isGeekOverseasChanged || isGeekOddJobChanged;
    }

    /**
     * 判断本地职位是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    private static boolean isExpectListChanged() {
        String newDigest = null;
        List<JobIntentBean> expectList = GeekExpectManager.getGeekProExpectList();
        if (!LList.isEmpty(expectList)) {
            String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectList);
            newDigest = MD5.convert(expectString);
        }

        if (!LText.equal(newDigest, JOB_CHANGED_TAG)) {
            JOB_CHANGED_TAG = newDigest;
            return true;
        }
        return false;
    }

    private static boolean isStuExpectListChanged() {
        String newDigest = null;
        List<JobIntentBean> expectList = GeekExpectManager.getGeekStuExpectList();
        if (!LList.isEmpty(expectList)) {
            String expectString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectList);
            newDigest = MD5.convert(expectString);

        }

        if (!LText.equal(newDigest, JOB_CHANGED_TAG_STU)) {
            JOB_CHANGED_TAG_STU = newDigest;
            return true;
        }


        return false;
    }


    /**
     * 判断本地期望类型是否有变更
     *
     * @return true - 变更 | false - 未变更
     */
    public static boolean isExpectTypeListChanged() {
        String newDigest = null;
        List<CodeNameFlagBean> expectTypeList = GeekExpectManager.getGeekExpectTypeList();
        if (!LList.isEmpty(expectTypeList)) {
            String expectTypeString = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(expectTypeList);
            newDigest = MD5.convert(expectTypeString);
        }

        if (!LText.equal(newDigest, JOB_EXPECT_TYPE_TAG)) {
            JOB_EXPECT_TYPE_TAG = newDigest;
            return true;
        }
        return false;
    }

    public static void setForceRefresh(boolean forceRefresh) {
        FORCE_REFRESH = forceRefresh;
    }

    public static boolean isForceRefresh() {
        return FORCE_REFRESH;
    }
}
