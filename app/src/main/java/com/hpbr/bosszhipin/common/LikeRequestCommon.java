package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.text.TextUtils;

import com.hpbr.bosszhipin.app.R;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.BossInterestPostRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * 作者：ZhouYou
 * 日期：2016/12/20.
 * 感兴趣和不感兴趣
 */
public class LikeRequestCommon {

    private final Activity activity;
    private final IBaseView view;

    public LikeRequestCommon(Activity activity, IBaseView view) {
        this.activity = activity;
        this.view = view;
    }

    /**
     * 对职位感兴趣
     *
     * @param jobId      职位id
     * @param expectId   牛人期望id
     * @param securityId
     * @param lid        埋点id
     */
    public void likeJob(long jobId, long expectId, String securityId, String lid, final OnRequestSuccessListener listener) {
        BossInterestPostRequest request = new BossInterestPostRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                view.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (listener == null) {
                    ToastUtils.showText("操作失败");
                } else {
                    ToastUtils.showText(R.string.interest_job_tip);
                    listener.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                view.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.jid = jobId;
        request.expectId = (expectId > 0) ? expectId : 0;
        request.statusFlag = 1;
        request.tag = 4;
        request.lid = TextUtils.isEmpty(lid) ? "" : lid;
        request.securityId=securityId;
        HttpExecutor.execute(request);
    }

    /**
     * 对职位不感兴趣
     *
     * @param jobId      职位id
     * @param expectId   牛人期望id
     * @param securityId
     * @param lid        埋点id
     */
    public void dislikeJob(long jobId, long expectId, String securityId, String lid, final OnRequestSuccessListener listener) {
        BossInterestPostRequest request = new BossInterestPostRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                view.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (listener == null) {
                    ToastUtils.showText("操作失败");
                } else {
                    ToastUtils.showText("已取消收藏");
                    listener.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                view.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.jid = jobId;
        request.expectId = (expectId > 0) ? expectId : 0;
        request.statusFlag = 0;
        request.tag = 4;
        request.lid = TextUtils.isEmpty(lid) ? "" : lid;
        request.securityId=securityId;
        HttpExecutor.execute(request);
    }

    public interface OnRequestSuccessListener {
        void onSuccess();
    }
}
