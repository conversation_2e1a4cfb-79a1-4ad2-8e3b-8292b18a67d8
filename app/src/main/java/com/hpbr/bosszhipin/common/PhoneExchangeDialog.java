package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;

import net.bosszhipin.api.ChangePhoneSwitchRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

public class PhoneExchangeDialog {
    PhoneExchangeListener onOpenExchangeCallback;
    Activity activity;
    long jobId;
    boolean isShow = true;
    int from;

    public PhoneExchangeDialog(Activity activity) {
        this.activity = activity;
    }

    public PhoneExchangeDialog(Activity activity, boolean isShow) {
        this.activity = activity;
        this.isShow = isShow;
    }

    public void showChatTip() {
        Uri uri = StringUtil.getResouceUri(R.mipmap.ic_phone_exchange_dialog_tip);
        DialogUtils.ImageParams imageParams = new DialogUtils.ImageParams(DialogUtils.ImageParams.TOP_BACKGROUND, uri, 0.5f);

        new DialogUtils.Builder(activity)
                .setTopBackground(imageParams)
                .setTitle("获得电话交换助手体验名额")
                .setDesc("开启电话交换助手，招聘效率提升100%")
                .setPositiveAction("立即体验", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showDialog();
                    }
                })
                .setNegativeAction("遗憾放弃", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                    }
                })
                .setDoubleButton()
                .build()
                .show();
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public void setOnOpenExchangeCallback(PhoneExchangeListener onOpenExchangeCallback) {
        this.onOpenExchangeCallback = new AnalyticsPhoneExchangeListener(onOpenExchangeCallback, from, jobId);
    }

    public void setJobId(long jobId) {
        this.jobId = jobId;
    }

    public void showDialog() {
        new DialogUtils.Builder(activity)
                .setTitle("电话交换助手")
                .setTitleRightIcon(isShow ? R.mipmap.ic_position_limit : 0)
                .setDesc("开启后，你主动沟通的牛人将可以通过账号绑定手机号（" + getEncodePhoneNumber() + "）直接联系你，招聘效果更棒哦～")
                .setPositiveAction("确定开启", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        if (onOpenExchangeCallback != null) {
                            onOpenExchangeCallback.onStartPhoneExchangeClick();
                        }
                        doOpenExchangeListener();

                    }
                })
                .setNegativeAction("取消", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onOpenExchangeCallback != null) {
                            onOpenExchangeCallback.onCancelPhoneExchangeClick();
                        }
                    }
                })
                .setDoubleButton()
                .build()
                .show();
    }

    /**
     * 获得加密的
     *
     * @return
     */
    private String getEncodePhoneNumber() {
        String phone = UserManager.getPhone();
        if (!LText.empty(phone)) {
            if (phone.length() == 11) {
                String start = phone.substring(0, 3);
                String end = phone.substring(7, 11);
                return start + "****" + end;
            }
        }
        return phone;
    }

    //打开交换电话助手
    public void doOpenExchangeListener() {
        ChangePhoneSwitchRequest request = new ChangePhoneSwitchRequest(new SimpleCommonApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {

                //显示对话框
                new DialogUtils.Builder(activity)
                        .setTitle("开启成功")
                        .setDesc("太打扰怎么办？可以在【职位管理】里面编辑职位进行关闭哦 ~")
                        .setSingleButton()
                        .setTitleRightIcon(isShow ? R.mipmap.ic_position_limit : 0)
                        .setPositiveAction("知道啦")
                        .build()
                        .show();

                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onOpenSuccess();
                }
            }
        });
        request.jobId = String.valueOf(jobId);
        request.switchStatus = ChangePhoneSwitchRequest.OPEN;
        request.execute();
    }

    public static class PhoneExchangeListener {

        public void onStartPhoneExchangeClick() {

        }

        public void onCancelPhoneExchangeClick() {

        }

        public void onOpenSuccess() {

        }
    }

    public static void sendPhoneExchangeTip(ChatBean chatBean) {
        final Intent intent = new Intent(Constants.RECEIVER_PHONE_EXCHANGE_DIALOG_GUIDE);
        intent.putExtra(Constants.DATA_ENTITY, chatBean);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    public class AnalyticsPhoneExchangeListener extends PhoneExchangeListener {

        PhoneExchangeListener phoneExchangeListener;
        int from;
        long jobId;

        public AnalyticsPhoneExchangeListener(PhoneExchangeListener phoneExchangeListener) {
            this.phoneExchangeListener = phoneExchangeListener;
        }

        public AnalyticsPhoneExchangeListener(PhoneExchangeListener phoneExchangeListener, int from, long jobId) {
            this.phoneExchangeListener = phoneExchangeListener;
            this.from = from;
            this.jobId = jobId;
        }

        @Override
        public void onStartPhoneExchangeClick() {
            if (this.phoneExchangeListener != null) {
                this.phoneExchangeListener.onStartPhoneExchangeClick();
            }
        }

        @Override
        public void onCancelPhoneExchangeClick() {
            if (this.phoneExchangeListener != null) {
                this.phoneExchangeListener.onCancelPhoneExchangeClick();
            }
        }

        @Override
        public void onOpenSuccess() {
            if (this.phoneExchangeListener != null) {
                this.phoneExchangeListener.onOpenSuccess();
            }
        }
    }
}
