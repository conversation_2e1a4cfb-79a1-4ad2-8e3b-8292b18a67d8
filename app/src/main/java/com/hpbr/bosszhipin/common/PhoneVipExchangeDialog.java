package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dialog.TitleRightIconDialog;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.twl.http.ApiData;
import com.twl.ui.ToastUtils;
import com.twl.utils.UrlUtils;

import net.bosszhipin.api.ChangePhoneSwitchRequest;
import net.bosszhipin.api.PhoneExchangeSwitchDetailResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.util.Collections;

public class PhoneVipExchangeDialog {
    PhoneExchangeListener onOpenExchangeCallback;
    Activity activity;
    int from;
    long jobId;
    long msgId;

    public PhoneVipExchangeDialog(Activity activity) {
        this.activity = activity;
    }


    public void setFrom(int from) {
        this.from = from;
    }

    public void setJobId(long jobId) {
        this.jobId = jobId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public void setOnOpenExchangeCallback(PhoneExchangeListener onOpenExchangeCallback) {
        this.onOpenExchangeCallback = onOpenExchangeCallback;
    }


    public void showDialog(PhoneExchangeSwitchDetailResponse.TransitionBean transition) {
        if (transition == null) {
            ToastUtils.showText("数据异常");
            return;
        }
        TitleRightIconDialog doubleDialog = new TitleRightIconDialog(activity);
        doubleDialog.setTitle(transition.title);
        doubleDialog.setTitleRightIcon(transition.icon);
        doubleDialog.setContent(Collections.singletonList(transition.desc));
        doubleDialog.setNegativeButton("取消", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onCancelPhoneExchangeClick();
                }
            }
        });
        doubleDialog.setPositiveButton("确认开启", new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onStartPhoneExchangeClick();
                }
                doOpenExchangeListener();
            }
        });
        doubleDialog.show();
    }


    public void showTipDialog(PhoneExchangeSwitchDetailResponse.IntroduceCardBean introduceCaseBean) {
        View view = LayoutInflater.from(activity).inflate(R.layout.buttom_view_phone_switch_vip_guide, null);
        TextView tv_title =view.findViewById(R.id.tv_title);
        TextView tv_content =view.findViewById(R.id.tv_content);
        SimpleDraweeView iv_right_icon =view.findViewById(R.id.iv_right_icon);
        tv_title.setText(introduceCaseBean.title);
        iv_right_icon.setImageURI(introduceCaseBean.icon);
        for (String introduce : introduceCaseBean.introduces) {
            tv_content.append(introduce);
            tv_content.append("\n");
        }
        BottomView  bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomView.dismissBottomView();
                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onCancelPhoneExchangeClick();
                }
            }
        });
        view.findViewById(R.id.btn_submit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomView.dismissBottomView();
                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onStartPhoneExchangeClick();
                }
                new ZPManager(activity, UrlUtils.addQueryParameter(introduceCaseBean.jumpUrl, "msgId", String.valueOf(msgId),"index","1")).handler();
            }
        });
        bottomView.showBottomView2(false);
    }


    //打开交换电话助手
    public void doOpenExchangeListener() {
        ChangePhoneSwitchRequest request = new ChangePhoneSwitchRequest(new SimpleCommonApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (onOpenExchangeCallback != null) {
                    onOpenExchangeCallback.onOpenSuccess();
                }
            }
        });
        request.jobId = String.valueOf(jobId);
        request.switchStatus = ChangePhoneSwitchRequest.OPEN;
        request.execute();
    }

    public static class PhoneExchangeListener {

        public void onStartPhoneExchangeClick() {

        }

        public void onCancelPhoneExchangeClick() {

        }

        public void onOpenSuccess() {

        }
    }

    public static void sendPhoneExchangeTip(ChatBean chatBean) {
        final Intent intent = new Intent(Constants.RECEIVER_PHONE_EXCHANGE_DIALOG_GUIDE);
        intent.putExtra(Constants.DATA_ENTITY, chatBean);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

//    public class AnalyticsPhoneExchangeListener extends PhoneExchangeListener {
//
//        PhoneExchangeListener phoneExchangeListener;
//        int from;
//        long jobId;
//
//        public AnalyticsPhoneExchangeListener(PhoneExchangeListener phoneExchangeListener) {
//            this.phoneExchangeListener = phoneExchangeListener;
//        }
//
//        public AnalyticsPhoneExchangeListener(PhoneExchangeListener phoneExchangeListener, int from, long jobId) {
//            this.phoneExchangeListener = phoneExchangeListener;
//            this.from = from;
//            this.jobId = jobId;
//        }
//
//        @Override
//        public void onStartPhoneExchangeClick() {
//            AnalyticsFactory.create()
//                    .action(AnalyticsAction.SYSTEM_NEWGUIDE_PHONEASSISTANT_RECONFIRMCLICK)
//                    .param("p", from)
//                    .param("p2", jobId)
//                    .param("p3", 1)
//                    .build();
//            if (this.phoneExchangeListener != null) {
//                this.phoneExchangeListener.onStartPhoneExchangeClick();
//            }
//        }
//
//        @Override
//        public void onCancelPhoneExchangeClick() {
//            AnalyticsFactory.create()
//                    .action(AnalyticsAction.SYSTEM_NEWGUIDE_PHONEASSISTANT_RECONFIRMCLICK)
//                    .param("p", from)
//                    .param("p2", jobId)
//                    .param("p3", 2)
//                    .build();
//            if (this.phoneExchangeListener != null) {
//                this.phoneExchangeListener.onCancelPhoneExchangeClick();
//            }
//        }
//
//        @Override
//        public void onOpenSuccess() {
//            if (this.phoneExchangeListener != null) {
//                this.phoneExchangeListener.onOpenSuccess();
//            }
//        }
//    }
}
