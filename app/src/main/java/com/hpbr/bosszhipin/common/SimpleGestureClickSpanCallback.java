package com.hpbr.bosszhipin.common;

import android.content.Context;
import android.view.MotionEvent;

import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.GestureMTextView;

public class SimpleGestureClickSpanCallback implements GestureMTextView.OnGestureClickSpanCallback {
    Context mContext;

    public SimpleGestureClickSpanCallback(Context context) {
        mContext = context;
    }

    @Override
    public void onClickSpanListener(GestureMTextView.InnerClickSpan innerClickSpan) {
        ZPManager manager = new ZPManager(mContext, innerClickSpan.getUrl());
        if (manager.isZPUrl() || manager.isWebUrl()) {
            manager.handler();
        } else {
            URLIntentCommon.dial(mContext, innerClickSpan.getUrl());
        }
    }

    public static GestureMTextView bindClickSpan(GestureMTextView gestureMTextView, MotionEvent event) {
        gestureMTextView.onSingleClick(event, new SimpleGestureClickSpanCallback(gestureMTextView.getContext()));
        return gestureMTextView;
    }
}
