package com.hpbr.bosszhipin.common;

import com.monch.lbase.util.SP;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetSwitchRequest;
import net.bosszhipin.api.GetSwitchResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.HashSet;
import java.util.Set;

public class SwitchCommon {

    public static final String SWITCH_LIST = "com.hpbr.bosszhipin.SWITCH_LIST";
    private static long number = -999;

    private static Set<ISwitchChangeCallBack> callBackSet = new HashSet<>();


    public interface ISwitchChangeCallBack {
        void onSwitchChangeCallBack();
    }

    public static void register(ISwitchChangeCallBack callBack) {
        synchronized (callBackSet) {
            callBackSet.add(callBack);
        }
    }

    public static void unRegister(ISwitchChangeCallBack callBack) {
        synchronized (callBackSet) {
            callBackSet.remove(callBack);
        }
    }

    private static void notifyCalback() {
        synchronized (callBackSet) {
            for (ISwitchChangeCallBack callBack : callBackSet) {
                callBack.onSwitchChangeCallBack();
            }
        }
    }

    public static void init() {
        requestSwitch();
    }

    private static void requestSwitch() {

        GetSwitchRequest request = new GetSwitchRequest(new ApiRequestCallback<GetSwitchResponse>() {
            @Override
            public void onSuccess(ApiData<GetSwitchResponse> data) {
                number = data.resp.switchCode;
                SP.get().putLong(SWITCH_LIST, number);
                notifyCalback();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    /**
     * n 需要小于63
     *
     * @param n
     * @return
     */
    private static long getNumber(int n) {
        if (number == -999) {
            number = SP.get().getLong(SWITCH_LIST, 0);
        }
        return 1l << n;
    }

//    public static boolean getOpenOrCloseResume() {
//        long a = getNumber(0);
//        return (number & a) == a;
//    }


    /**
     * 是否打开优惠券入口
     *
     * @return
     */
    public static boolean getCouponShow() {
        long a = getNumber(7);
        return (number & a) == a;
    }

    /**
     * 电量提示开关
     *
     * @return true显示 false隐藏
     */
    public static boolean getDaemonEnabled() {
        long a = getNumber(16);
        return (number & a) == a;
    }

    /**
     * 拒绝选理由功能是否可用
     *
     * @return true可用 false不可用
     */
    public static boolean isRejectReasonAvailable() {
        long a = getNumber(20);
        return (number & a) == a;
    }


    /**
     * mqtt qos是否设置为2
     *
     * @return true:2  false:1
     */
    public static boolean isMqttQosIsTwo() {
        long a = getNumber(24);
        return (number & a) == a;
    }

    /**
     * 提问加油卡的勋章标记
     *
     * @return true - 展示 | false - 不展示
     */
    public static boolean isShowBossQuestionMedal() {
        long a = getNumber(25);
        return (number & a) == a;
    }

    /**
     * 是否开启分享小程序
     */
    public static boolean isShareMiniProgram() {
        long a = getNumber(28);
        return (number & a) == a;
    }

    /**
     * 619 后台控制是否要打开“开启微信通知”相关入口
     *
     * @return true 打开
     */
    public static boolean isOpenWechatNotification() {
        long a = getNumber(35);
        return (number & a) == a;
    }

    /**
     * 群聊f2开口
     *
     * @return true - 展示 | false - 不展示
     */
    public static boolean isGroupChatOpen() {
        long a = getNumber(29);
        return (number & a) == a;
    }

    /**
     * 是否显示子弹短信
     *
     * @return
     */
    public static boolean isShowBulletView() {
        long a = getNumber(33);
        return (number & a) == a;
    }

    /**
     * 7.0全量撤回
     * 是否可以撤回消息
     *
     * @return
     */
    public static boolean isCanRevocationMessage() {
        long a = getNumber(34);
        return (number & a) == a;
    }

    /**
     * 是否展示短信通知的购买入口
     *
     * @return
     */
    public static boolean isShowSmsNotifyItem() {
        long a = getNumber(37);
        return (number & a) == a;
    }

    /**
     * 是否显示短信
     *
     * @return
     */
    public static boolean isShowVideoIcon() {
//        long a = getNumber(38);
//        return (number & a) == a;

        return true;

    }

    /**
     * 首善猎头是否可以看见我
     *
     * @return
     */
    public static boolean isShowFirstCompleteExpectHunter() {
        long a = getNumber(39);
        return (number & a) == a;
    }




    /**
     * 新招呼
     *
     * @return
     */
    public static boolean isNewCallMessage() {
        long a = getNumber(44);
        return (number & a) == a;
    }




    /**
     * 牛人是否显示视频通话icon
     *
     * @return
     */
    public static boolean geekShowVideoing() {
        long a = getNumber(48);
        return (number & a) == a;
    }


}