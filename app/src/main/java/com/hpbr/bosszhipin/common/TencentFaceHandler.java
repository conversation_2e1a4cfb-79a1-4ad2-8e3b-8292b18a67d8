package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.os.Bundle;

import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceContant;
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceVerifySdk;
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyLoginListener;
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyResultListener;
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceError;
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceVerifyResult;
import com.tencent.cloud.huiyansdkface.facelight.process.FaceVerifyStatus;
import com.tencent.cloud.huiyansdkface.wehttp2.WeLog;
import com.tencent.cloud.huiyansdkface.wehttp2.WeOkHttp;

/**
 * Created by wangtian on 2018/6/29.
 * 腾讯云人脸认证
 * <p>
 * 接入文档
 * https://cloud.tencent.com/document/product/655/14077
 */

public class TencentFaceHandler {
    private static final String TAG = "TencentFaceHandler";
//    public static final String APP_ID = "IDABBEP2";
//    public static final String LICENCE = "BQ5iCdDXjPesjFQLObI3jRooguRQq5fs86ZkTgJ3HIjOKeclJYbc0VxQUR57/+80WkFmwnFX0D6l8ZFE4lXRqUZoI/Tr6pt1FBmN2Qcm+oGYGn9YwuTMWWim26Pmkjiyak7tXXizIRFn4Ypc53tELnZF+mb7oDcS5PGHSXShgFY8UYsO6NdgAW6U0fVweFnIPxAxB6H8ZnzDDhTAw+qyM/2YaZLlZhq9thLckRJikXJeuFMPN+m/6CbV3192WnLAj9YEZrMNVFotu1kD655P7z4EwVyRTvaAFxR6fGY060Aac2p3MOYaw1EiXIE76WULweAsWlLk5zQb6FOd02z53w==";
    private TencentFaceParams params;
    private TencentFaceCallback callback;
    private Activity activity;

    //此处防重复点击时间间隔为demo模拟，合作方可根据自己情况确定间隔时间
    private static final long CLICK_GAP = 800;
    private long clickTime = 0;

    private WeOkHttp myOkHttp = new WeOkHttp();

    private void initHttp() {
        //拿到OkHttp的配置对象进行配置
        //WeHttp封装的配置
        myOkHttp.config()
                //配置超时,单位:s
                .timeout(20, 20, 20)
                //添加PIN
                .log(WeLog.Level.BODY);
    }

    public TencentFaceHandler(Activity activity, TencentFaceParams params, TencentFaceCallback callback) {
        this.params = params;
        this.callback = callback;
        this.activity = activity;
        initHttp();
    }

    public void startMain(){
        Utils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                start();
            }
        });
    }
    public void start() {
        // 升级 4.4.1.2、新版sdk不再支持由服务端配置人脸模式（动作、光线等），全部由腾讯sdk内定为动作+光线模式，后端与前端接口可先不作调整
//        FaceVerifyStatus.Mode mode;
//        if(this.params.faceMode == 0){
//            mode = FaceVerifyStatus.Mode.ACT;
//        }else if(this.params.faceMode == 2){
//            mode = FaceVerifyStatus.Mode.REFLECTION;
//        }else{
//            ToastUtils.showText("不支持的认证类型");
//            return;
//        }
//老版本需要的
//        if(com.webank.facelight.tools.e.d(App.get())){
//            callback.onTencentFaceResult(false, "-1");
//            return;
//        }
        if (System.currentTimeMillis() - clickTime < CLICK_GAP) {
            TLog.error(TAG, "duplicate click faceGradeFaceId!");
            return;
        }
        clickTime = System.currentTimeMillis();
        this.callback.onShowProgress(true);
        String appId = PackageConfigContants.TENCENT_FACE_APPID;
        if(HostConfig.CONFIG != HostConfig.Addr.ONLINE && HostConfig.CONFIG != HostConfig.Addr.PRE){//测试环境，6.17添加
            appId = "IDAEfIjp";
        }

        Bundle data = new Bundle();
        WbCloudFaceVerifySdk.InputData  inputData = new WbCloudFaceVerifySdk.InputData(
                params.faceId,
                params.agreementNo,
                appId,
                "1.0.0",
                params.openApiNonce,
                params.openApiUserId,
                params.openApiSign,
                FaceVerifyStatus.Mode.GRADE, PackageConfigContants.TENCENT_FACE_LICENCE
        );

        data.putSerializable(WbCloudFaceContant.INPUT_DATA, inputData);
//        //是否展示刷脸成功页面，默认展示
//        data.putBoolean(WbCloudFaceContant.SHOW_SUCCESS_PAGE, false);
//        //是否展示刷脸失败页面，默认展示
//        data.putBoolean(WbCloudFaceContant.SHOW_FAIL_PAGE, false);
        //日志
        data.putBoolean(WbCloudFaceContant.IS_ENABLE_LOG, BuildInfoUtils.isDebug());
        //设置选择的比对类型  默认为公安网纹图片对比
        //公安网纹图片比对 WbCloudFaceContant.ID_CRAD
        //仅活体检测  WbCloudFaceContant.NONE
        data.putString(WbCloudFaceContant.COMPARE_TYPE, WbCloudFaceContant.ID_CARD);
        //是否需要录制上传视频 默认不需要，此处设置为不需要
        data.putBoolean(WbCloudFaceContant.VIDEO_UPLOAD, true);
        //是否对录制视频进行检查，默认不检查，此处设置为不检查
        data.putBoolean(WbCloudFaceContant.VIDEO_CHECK, true);
        //【特别注意】请使用activity context拉起sdk
        //【特别注意】请在主线程拉起sdk！
        WbCloudFaceVerifySdk.getInstance().initAdvSdk(activity, data, new WbCloudFaceVerifyLoginListener() {
            @Override
            public void onLoginSuccess() {
                TLog.info(TAG, "onLoginSuccess");
                callback.onShowProgress(false);

                WbCloudFaceVerifySdk.getInstance().startWbFaceVerifySdk(activity, new WbCloudFaceVerifyResultListener() {
                    @Override
                    public void onFinish(WbFaceVerifyResult result) {
                         if (result != null) {
                            if (result.isSuccess()) {
                                TLog.debug(TAG, "刷脸成功! Sign=" + result.getSign() + "; liveRate=" + result.getLiveRate() +
                                        "; similarity=" + result.getSimilarity() + "userImageString=" + result.getUserImageString());
                                callback.onTencentFaceResult(true, "");
                            } else {
                                WbFaceError error = result.getError();
                                if (error != null) {
                                    TLog.debug(TAG, "刷脸失败！domain=" + error.getDomain() + " ;code= " + error.getCode()
                                            + " ;desc=" + error.getDesc() + ";reason=" + error.getReason());
                                    if (error.getDomain().equals(WbFaceError.WBFaceErrorDomainCompareServer)) {
                                        TLog.debug(TAG, "对比失败，liveRate=" + result.getLiveRate() +
                                                "; similarity=" + result.getSimilarity());
                                    }
                                    callback.onTencentFaceResult(false, error.getCode()+"");
                                } else {
                                    callback.onTencentFaceResult(false, "-9999");
                                    TLog.error(TAG, "sdk返回error为空！");
                                }
                            }
                        } else {
                            callback.onTencentFaceResult(false, "-9998");
                             TLog.error(TAG, "sdk返回结果为空！");
                        }
                        WbCloudFaceVerifySdk.getInstance().release();
                    }
                });
            }

            @Override
            public void onLoginFailed(WbFaceError error) {
                TLog.error(TAG,"onLoginFailed error = %s",error);
                callback.onShowProgress(false);
                callback.onTencentFaceResult(false, error.getCode());
            }
        });


//        WbCloudFaceVerifySdk.getInstance().initSdk(activity, data, new WbCloudFaceVeirfyLoginListner() {
//            @Override
//            public void onLoginSuccess() {
//                Log.i(TAG, "onLoginSuccess");
//                callback.onShowProgress(false);
//
//                WbCloudFaceVerifySdk.getInstance().startWbFaceVeirifySdk(activity, new WbCloudFaceVeirfyResultListener() {
//                    @Override
//                    public void onFinish(WbFaceVerifyResult result) {
//                        if (result != null) {
//                            if (result.isSuccess()) {
//                                Log.d(TAG, "刷脸成功! Sign=" + result.getSign() + "; liveRate=" + result.getLiveRate() +
//                                        "; similarity=" + result.getSimilarity() + "userImageString=" + result.getUserImageString());
//                                callback.onTencentFaceResult(true, "");
//                            } else {
//                                WbFaceError error = result.getError();
//                                if (error != null) {
//                                    Log.d(TAG, "刷脸失败！domain=" + error.getDomain() + " ;code= " + error.getCode()
//                                            + " ;desc=" + error.getDesc() + ";reason=" + error.getReason());
//                                    if (error.getDomain().equals(WbFaceError.WBFaceErrorDomainCompareServer)) {
//                                        Log.d(TAG, "对比失败，liveRate=" + result.getLiveRate() +
//                                                "; similarity=" + result.getSimilarity());
//                                    }
//                                    callback.onTencentFaceResult(false, error.getCode()+"");
//                                } else {
//                                    callback.onTencentFaceResult(false, "-9999");
//                                    Log.e(TAG, "sdk返回error为空！");
//                                }
//                            }
//                        } else {
//                            callback.onTencentFaceResult(false, "-9998");
//                            Log.e(TAG, "sdk返回结果为空！");
//                        }
//
//                    }
//                });
//            }
//
//            @Override
//            public void onLoginFailed(WbFaceError error) {
//                Log.i(TAG, "onLoginFailed!");
//                callback.onShowProgress(false);
//                callback.onTencentFaceResult(false, error.getCode());
//            }
//        });
    }
    public static class TencentFaceParams {
        public String faceId;
        public String agreementNo;  //订单号
        public String openApiNonce; //32位随机字符串
        public String openApiUserId; //user id
        public String openApiSign; //签名信息
        public int faceMode;//动作模式（WBFaceVerifyLivingType_Action、MIDDLE） =0； 闪光模式（WBFaceVerifyLivingType_Light、REFLECT） = 2；
    }

    public interface TencentFaceCallback {
        void onShowProgress(boolean isShow);

        void onTencentFaceResult(boolean result, String errorCode);
    }
}
