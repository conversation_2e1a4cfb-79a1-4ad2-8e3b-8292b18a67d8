package com.hpbr.bosszhipin.common;

import static com.hpbr.bosszhipin.report.ReportContextUtils.realUploadFile;

import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.report.McpTxApiRequestCallback;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;
import com.techwolf.lib.tlog.report.CallbackReporter;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.FileIOUtils;

import net.bosszhipin.api.FileUploadResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

/**
 * Created by monch on 2017/3/22.
 */

public class TwlReportHandler implements CallbackReporter.ReportHandler {

    private static final String TAG = "TwlReportHandler";

    @Override
    public void onHandler(File[] files) {
        if (files == null || files.length == 0) return;
        AppThreadFactory.POOL.submit(() -> {
            for (File file : files) {
                if (file != null && file.exists()) {
                    File temp = null;
                    try {
                        temp = FileIOUtils.getTempFile(file);
                    } catch (Exception e) {
                        CrashReport.postCatchedException(e);
                    }
                    if (temp != null) {
                        uploadFile(temp, true);
                    } else {
                        uploadFile(file, false);
                    }
                } else {
                    TLog.info(TAG, "file is not exists path = %s", file != null ? file.getAbsolutePath() : "null");
                }
            }
            CrashReport.postCatchedException(new Exception("Upload info file!"));
        });
    }

    private void uploadFile(final File file, final boolean delOnComplete) {
        L.info(TAG, "TwlReportHandler %s", "upload info file");
        String[] logFiles = file.getParentFile() != null ? file.getParentFile().list() : null;
        realUploadFile(file, new McpTxApiRequestCallback() {

            @Override
            public void handleInChildThread(ApiData<FileUploadResponse> data) {
                if (data != null && data.resp != null && data.resp.isSuccess()) {
                    reportApmUploadFileStatus(true, data.resp.url, "", StringUtil.connectTextWithChar(",",logFiles));
                } else {
                    reportApmUploadFileStatus(false, "", "response error",StringUtil.connectTextWithChar(",",logFiles));
                }
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                reportApmUploadFileStatus(false, "", reason.getErrReason(),StringUtil.connectTextWithChar(",",logFiles));
            }

            @Override
            public void onSuccess(ApiData<FileUploadResponse> data) {
            }

            @Override
            public void onComplete() {
                if (delOnComplete) {
                    boolean deleted = file.delete();
                    L.d(TAG, "file<" + file.getAbsolutePath() + "> deleted: " + deleted);
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
    }

    private void reportApmUploadFileStatus(boolean isSuccess, String url, String errorReason ,String fileParentFileNameList) {
        JSONObject object = new JSONObject();
        try {
            JSONArray a = new JSONArray();
            a.put(url);
            object.put(ApmAnalyticsAction.BizKey.KEY_LOG_ADDRESS, a);
            object.put(ApmAnalyticsAction.BizKey.KEY_ERROR_REASON, errorReason);
            object.put("log_exists_all", fileParentFileNameList);
        } catch (JSONException e) {
            e.printStackTrace();
        }
//        ApmAnalyzer.create()
//                .action(ApmAnalyticsAction.ACTION_T_LOG,
//                        isSuccess ? ApmAnalyticsAction.ACTION_T_LOG_SUCCESS : ApmAnalyticsAction.ACTION_T_LOG_FAILED)
//                .param("p2", object.toString())
//                .report();
    }


}
