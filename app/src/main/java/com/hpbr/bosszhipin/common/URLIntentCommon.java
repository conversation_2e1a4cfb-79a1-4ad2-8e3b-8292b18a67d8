package com.hpbr.bosszhipin.common;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.provider.ContactsContract;
import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.MultiItemDialog;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;

/**
 * Created by zonglv on 15/4/21.
 * 点击url并且跳转
 */
public class URLIntentCommon extends ClickableSpan {
    private Context activity;
    public String uri;

    public URLIntentCommon(Context activity, String uri) {
        super();
        this.activity = activity;
        this.uri = uri;
    }

    @Override
    public void updateDrawState(TextPaint ds) {
        super.updateDrawState(ds);
        ds.setUnderlineText(false);
    }

    @Override
    public void onClick(View v) {
        ZPManager manager = new ZPManager(activity, uri);
        if (manager.isZPUrl() || manager.isWebUrl()) {
            manager.handler();
        } else {
            dial(activity, uri);
        }
    }

    public static void dial(final Context activity, final String mobile) {
        String[] items = new String[]{"打电话", "发短信", "添加到通讯录", "复制到剪贴板"};;
        MultiItemDialog d = new MultiItemDialog(activity);
        d.show(items, new URLOnItemClickListener(activity, mobile));
    }

    /**
     * 针对Uri的点击事件操作
     * 【打电话】【发短信】
     */
    private static class URLOnItemClickListener implements View.OnClickListener {

        private Context activity;
        private String mobile;

        private URLOnItemClickListener(Context activity, String mobile) {
            this.activity = activity;
            this.mobile = mobile;
        }

        @Override
        public void onClick(View v) {
            int index = (Integer) v.getTag();
            Intent intent;
            String tel = mobile;
            if (tel.startsWith("tel:")) {
                tel = tel.substring("tel:".length(), tel.length());
            }
            switch (index) {
                case 0:
                    StringUtil.dial(activity, tel);
                    break;
                case 1:
                    Uri uri = Uri.parse("smsto:" + tel);
                    intent = new Intent(Intent.ACTION_SENDTO, uri);
                    try {
                        AppUtil.startActivity(activity, intent);
                    } catch (Exception ignored) {
                    }
                    break;
                case 2:
                    try {
                        insertFriend2(tel);
                    } catch (Exception e) {
                        MException.printError(URLIntentCommon.class.getSimpleName(), e);
                        T.ss("添加通讯录失败");
                    }
                    break;
                case 3:
                    copyText(tel);
                    T.ss("手机号已复制");
                    break;
                default:
                    break;
            }
        }

        private void insertFriend2(String number) throws Exception {
            Intent intent = new Intent(Intent.ACTION_INSERT,
                    Uri.withAppendedPath(Uri.parse("content://com.android.contacts"), "contacts"));
            intent.setType("vnd.android.cursor.dir/person");
            intent.putExtra(ContactsContract.Intents.Insert.PHONE, number);
            AppUtil.startActivity(activity, intent);
        }

        /**
         * 复制文字方法
         */
        private void copyText(String text) {
            if (activity == null) return;
            if (LText.empty(text)) return;
            ClipboardManager clip = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
            clip.setPrimaryClip(ClipData.newPlainText("copy", text));
        }

    }
}
