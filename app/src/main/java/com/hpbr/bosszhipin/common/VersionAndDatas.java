package com.hpbr.bosszhipin.common;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.monch.lbase.orm.db.TableManager;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.BuglyLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.mms.utils.ExceptionUtils;
import com.twl.mms.utils.TWLException;

import net.bosszhipin.api.bean.CityBean;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 从原VersionAndDatasCommon中抽取出的父类
 * <p>
 * Created by quzhiyong on 2018/10/17
 */
public abstract class VersionAndDatas {
    public static final String TAG = VersionAndDatas.class.getSimpleName();



    //1004拆分
    public static final String CITY_FILENAME = "city.json";
    public static final String SCALE_FILENAME = "scale.json";
    public static final String DEGREE_FILENAME = "degree.json";
    public static final String EXPERIENCE_FILENAME = "experience.json";
    public static final String GEEKFILTERCONFIG_FILENAME = "geekFilterConfig.json";
    public static final String BOSSFILTERCONFIG_FILENAME = "bossFilterConfig.json";
    public static final String INDUSTRY_FILENAME = "industry.json";
    public static final String INTERNPOSITION_FILENAME = "internPosition.json";
    public static final String SCHOOLSEARCH_FILENAME = "schoolSearch.json";
    public static final String STAGE_FILENAME = "stage.json";
    public static final String SALARY_FILENAME = "salary.json";


    protected static final String EXTRA_BEAN_FILENAME = "extraData.json"; //2.8 对应服务端 breakSilenceVersion

//    protected static final String SKILL_WORD_BEAN_FILENAME = "skillWord.json";//9.1 skillWordsVersion
    protected static final String POSITION_BEAN_FILENAME = "position.json";// 3.4 positionVersion

    protected static final String BUSINESS_DISTRICT = "business_district.json";
    protected static final String BUSINESS_DISTRICT_FOR_GEEK = "business_district_for_geek.json";
    protected static final String SUBWAY = "subway.json";
    protected static final String DISTANCE = "distanceFilter.json";

    /****************
     * 基础数据默认ID,持续加
     *********************/
    protected static final long POSITION_ID = 1;
    protected static final long SCALE_ID = 2;
    protected static final long HOT_CITY_ID = 3;
    protected static final long BOSS_FILTER_ID = 4;
    protected static final long CITY_ID = 5;//
    protected static final long DEGREE_ID = 6;
    protected static final long EXPERIENCE_ID = 7;
    protected static final long INDUSTRY_ID = 8;
    protected static final long GEEK_FILTER_ID = 9;
    /* 5.51新增，细分的行业字典 */
    protected static final long INDUSTRY_CONFIG2_ID = 10;
    /* 6.02新增，商圈数据 */
    protected static final long BUSINESS_DISTRICT_ID = 11;
    /* 6.02新增，地铁数据 */
    protected static final long SUBWAY_ID = 12;
    /* 6.06新增，附近距离数据 */
    protected static final long DISTANCE_ID = 13;
    /* 7.02新增，C端商圈 */
    protected static final long BUSINESS_DISTRICT_ID_FOR_GEEK = 14;
    /* 7.02新增，院校要求 */
    protected static final long SCHOOL_LEVEL = 15;
    /**
     * 7.04新增 实习生 求职期望
     */
    protected static final long INTERN_POSITION_ID = 16;

    protected static final String DELETE_CASCADE_SQL = " parentId IN (SELECT ID FROM %s WHERE parentId = %d ) OR parentId = %d ";//删除两级数据
    protected static final String DELETE_3_CASCADE_SQL = " parentId IN (Select id from %s where parentId IN (SELECT ID FROM %s WHERE parentId = %d)) OR parentId IN (SELECT ID FROM %s WHERE parentId = %d) OR parentId = %d";//删除3级数据
    protected static long DEF_ID = 1000;//ID 默认从1000开始,以下保留给筛选用

    protected volatile static AtomicLong mLevelBeanId = null;
    protected volatile static AtomicLong mFilterBeanId = null;

    protected static ReadWriteLock mReadWriteLock = new ReentrantReadWriteLock();

    protected volatile boolean mInitialize = false;

    protected String keyOfBd(@NonNull ROLE role) {
        return role == ROLE.BOSS ? BUSINESS_DISTRICT : BUSINESS_DISTRICT_FOR_GEEK;
    }

    protected long codeOfBd(@NonNull ROLE role) {
        return role == ROLE.BOSS ? BUSINESS_DISTRICT_ID : BUSINESS_DISTRICT_ID_FOR_GEEK;
    }

    protected String nameOfBd(@NonNull ROLE role) {
        return role == ROLE.BOSS ? BUSINESS_DISTRICT : BUSINESS_DISTRICT_FOR_GEEK;
    }

    protected int businessRoleOfBd(@NonNull ROLE role) {
        return role == ROLE.BOSS ? 1 : 0;
    }

    /**
     * 生成偏好key，需要两个参数
     *
     * @param params1 文件名
     * @param params2 类型
     * @return
     */
    protected String getKey(String params1, String params2) {
        StringBuffer stringBuffer = new StringBuffer("com.hpbr.bosszhipin.asset.");
        stringBuffer.append(params1).append(".").append(params2).append(".key");
        return stringBuffer.toString();
    }

    /**
     * 初始化默认ID
     *
     * @param clazz
     * @return
     */
    protected <T> T getMaxBean(Class clazz) {
        try {
            QueryBuilder queryBuilder = new QueryBuilder(clazz);
            String tableName = TableManager.getTableName(clazz);
            queryBuilder.where(" ID = (SELECT MAX(ID) FROM " + tableName + " )", new Object[0]);
            ArrayList<T> list = App.get().db().query(queryBuilder);
            if (!LList.isEmpty(list)) {
                return list.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 初始化FilterBeanid
     *
     * @return
     */
    protected long getLeverBeanId() {
        if (mLevelBeanId == null) {
            synchronized (VersionAndDatas.class) {
                if (mLevelBeanId == null) {
                    long id = DEF_ID;
                    LevelBean maxBean = getMaxBean(LevelBean.class);
                    if (maxBean != null) {
                        id += maxBean.id;
                    }
                    mLevelBeanId = new AtomicLong(id);
                }
            }
        }
        return mLevelBeanId.getAndIncrement();
    }

    /**
     * 初始化FilterBeanid
     *
     * @return
     */
    protected long getFilterBeanId() {
        if (mFilterBeanId == null) {
            synchronized (VersionAndDatas.class) {
                if (mFilterBeanId == null) {
                    long id = DEF_ID;
                    FilterBean maxBean = getMaxBean(FilterBean.class);
                    if (maxBean != null) {
                        id += maxBean.id;
                    }
                    mFilterBeanId = new AtomicLong(id);
                }
            }
        }
        return mFilterBeanId.getAndIncrement();
    }

    protected boolean saveLevelBean(List<LevelBean> list, long code, boolean is3Tier) {
        boolean ret = false;

        if (list != null && list.size() > 0) {
            int size = list.size();

            for (int i = 0; i < size; i++) {
                list.get(i).parentId = code;
            }

            String tableName = TableManager.getTableName(LevelBean.class);
            String where;
            if (is3Tier) {
                where = String.format(DELETE_3_CASCADE_SQL, tableName, tableName, code, tableName, code, code);//删除三级自关联数据
            } else {
                where = String.format(DELETE_CASCADE_SQL, tableName, code, code);//删除二级自关联数据
            }
            ret = resetBean(list, where);
        }

        return ret;
    }


    protected List<LevelBean> parseCityBean(List<CityBean> city) {
        List<LevelBean> list = null;

        if (city != null) {
            list = new ArrayList<>(city.size());
            for (CityBean bean : city) {
                list.add(getLevelBean(bean));
            }
        }

        return list;
    }

    protected LevelBean getLevelBean(CityBean cityBean) {
        LevelBean bean = new LevelBean();
        bean.code = cityBean.code;
        bean.name = cityBean.name;
        bean.firstChar = cityBean.firstChar;
        bean.mark = cityBean.mark;
        bean.positionType = cityBean.positionType;
        bean.capital = cityBean.capital;
        bean.id = getLeverBeanId();
        bean.color = cityBean.color;

        List<CityBean> subLevelModelList = cityBean.subLevelModelList;
        if (subLevelModelList != null) {
            int size = subLevelModelList.size();
            List<LevelBean> list = new ArrayList<>(size);
            for (int i = 0; i < size; i++) {
                CityBean subCityBean = subLevelModelList.get(i);
                LevelBean e = getLevelBean(subCityBean);
                e.parentId = bean.id;
                list.add(e);
            }
            bean.subLevelModeList = list;
        }
        return bean;
    }

    /**
     * 根据JSONOBJECT获取LevelBean
     *
     * @param jsonObject
     * @return
     */
    protected LevelBean getLevelBeanFromJson(JSONObject jsonObject) {
        LevelBean bean = new LevelBean();
        bean.code = jsonObject.optLong("code");
        bean.name = jsonObject.optString("name");
        bean.firstChar = jsonObject.optString("firstChar");
        bean.mark = jsonObject.optInt("mark");
        bean.positionType = jsonObject.optInt("positionType");
        bean.cityType = jsonObject.optInt("cityType");
        bean.capital = jsonObject.optInt("capital");
        bean.color = jsonObject.optString("color");
        bean.id = getLeverBeanId();
        JSONArray jaSubLevelModeList = jsonObject.optJSONArray("subLevelModelList");
        if (jaSubLevelModeList != null) {
            int count = jaSubLevelModeList.length();
            List<LevelBean> list = new ArrayList<>(count);
            for (int i = 0; i < count; i++) {
                JSONObject joSubLevelMode = jaSubLevelModeList.optJSONObject(i);
                LevelBean e = getLevelBeanFromJson(joSubLevelMode);
                e.parentId = bean.id;
                list.add(e);
            }
            bean.subLevelModeList = list;
        }
        return bean;
    }


    /**
     * 保存基础数据
     *
     * @param jsonArray
     * @param code
     */
    protected boolean saveLevelBean(JSONArray jsonArray, long code) {
        return saveLevelBean(jsonArray, code, false);
    }

    /**
     * 保存基础数据
     *
     * @param jsonArray
     * @param code
     * @param is3Tier   是否是三级结构数据
     */
    protected boolean saveLevelBean(JSONArray jsonArray, long code, boolean is3Tier) {
        boolean ret = false;
        if (jsonArray != null) {
            int count = jsonArray.length();
            if (count > 0) {
                List<LevelBean> list = new ArrayList<>(count);
                for (int i = 0; i < count; i++) {
                    JSONObject jo = jsonArray.optJSONObject(i);
                    if (jo == null) continue;
                    LevelBean bean = getLevelBeanFromJson(jo);
                    bean.parentId = code;
                    list.add(bean);
                }
                String tableName = TableManager.getTableName(LevelBean.class);
                String where;
                if (is3Tier) {
                    where = String.format(DELETE_3_CASCADE_SQL, tableName, tableName, code, tableName, code, code);//删除三级自关联数据
                } else {
                    where = String.format(DELETE_CASCADE_SQL, tableName, code, code);//删除二级自关联数据
                }
                ret = resetBean(list, where);
            }
        }
        return ret;
    }

    /**
     * 保存基础数据
     *
     * @param jsonArray
     * @param code
     */
    protected boolean saveFilterBean(JSONArray jsonArray, long code) {
        boolean ret = false;
        if (jsonArray != null) {
            int count = jsonArray.length();
            if (count > 0) {
                List<FilterBean> list = new ArrayList<>(count);
                for (int i = 0; i < count; i++) {
                    JSONObject jo = jsonArray.optJSONObject(i);
                    if (jo == null) continue;
                    FilterBean bean = new FilterBean();
                    bean.parentId = code;
                    parseJson2FilterBean(jo, bean);
                    list.add(bean);
                }
                String where = String.format(DELETE_CASCADE_SQL, TableManager.getTableName(FilterBean.class), code, code);//删除所有数据
                ret = resetBean(list, where);
            }
        }
        return ret;
    }

    /**
     * 更新数据
     *
     * @param list  新数据
     * @param where 删除条件1
     * @param <T>
     * @return
     */
    protected <T> boolean resetBean(List<T> list, String where) {
        boolean ret = false;
        if (list != null && list.size() > 0) {
            try {
                long time = System.currentTimeMillis();
                mReadWriteLock.writeLock().lock();
                if (where == null) {
                    App.get().db().deleteAll(list.get(0).getClass());
                } else {
                    int count = App.get().db().delete(list.get(0).getClass(), where);
                    TLog.info(TAG, "delete count = [%d], time = [%d]",
                            count, System.currentTimeMillis() - time);
                }
                int count = App.get().db().save(list);
                ret = count == list.size();
                TLog.info(TAG, "resetBean() called with: class = [%s], time = [%d], ret = [%b], count = [%d]",
                        list.get(0).getClass(), System.currentTimeMillis() - time, ret, list.size());
            } catch (Throwable e) {
                e.printStackTrace();
            } finally {
                mReadWriteLock.writeLock().unlock();
            }
        }
        return ret;
    }

    /**
     * 获取一个JsonObject
     *
     * @param json
     * @return
     */
    protected JSONObject getJsonObject(String json) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(json);
        } catch (JSONException e) {
            jsonObject = null;
        }
        return jsonObject;
    }
//
//    protected void saveToFile(String fileName, Object object) {
//        if (BuildInfoUtils.isDebug()) { // 测试环境写入本地，用于测试或替换工程中的基础数据
//            try {
//                String content = GsonUtils.getExcludeFieldsWithoutExposeGson().toJson(object);
//                File file = new File("/sdcard/bszp/data/");
//                if (!file.exists()) {
//                    file.mkdirs();
//                }
//                File saveFile = new File(file, fileName);
//                if (!saveFile.exists()) {
//                    saveFile.createNewFile();
//                }
//                FileIOUtils.writeFileFromString(saveFile, content);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }

    protected ArrayList<LevelBean> json2FilterBeans(String json) {
        ArrayList<LevelBean> list = null;
        try {
            JSONArray jsonArray = new JSONArray(json);
            int count = jsonArray.length();
            list = new ArrayList<>(count);
            for (int i = 0; i < count; i++) {
                JSONObject jo = jsonArray.getJSONObject(i);
                if (jo == null) continue;
                LevelBean bean = new LevelBean();
                bean.parseJson(jo);
                list.add(bean);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return list;
    }

    protected void parseJson2FilterBean(JSONObject jsonObject, FilterBean filterBean) {
        if (jsonObject == null) return;
        filterBean.code = jsonObject.optLong("code");
        filterBean.name = jsonObject.optString("name");
        filterBean.paramName = jsonObject.optString("paramName");
        filterBean.tip = jsonObject.optString("tip");
        filterBean.id = getFilterBeanId();
        JSONArray ja = jsonObject.optJSONArray("subFilterConfigModel");
        if (ja != null) {
            int size = ja.length();
            for (int i = 0; i < size; i++) {
                JSONObject jo = ja.optJSONObject(i);
                if (jo == null) continue;
                FilterBean item = new FilterBean();
                parseJson2FilterBean(jo, item);
                item.parentId = filterBean.id;
                filterBean.subFilterConfigModel.add(item);
            }
        }
    }

    /*****************************
     * 下面所有的方法为获取具体数据列表
     *****************************/
    protected <T> List<T> query(Class<T> clazz, long id) {
        List<T> list = null;
        try {
            mReadWriteLock.readLock().lock();
            long time = System.currentTimeMillis();
            list = App.get().db().query(new QueryBuilder(clazz).where("parentId = ?", new Object[]{id}));
            TLog.info(TAG, "query() called with:  id = [%d], time = [%d]", id, System.currentTimeMillis() - time);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkList(list, id, clazz);
        return list;
    }

    protected <T> List<T> query3TierCascade(Class<T> clazz, long id) {
        List<T> list = null;
        try {
            mReadWriteLock.readLock().lock();
            long time = System.currentTimeMillis();
            list = App.get().db().query3TierCascade(new QueryBuilder(clazz).where("parentId = ?", new Object[]{id}));
            TLog.info(TAG, "query() called with:  id = [%d], time = [%d]", id, System.currentTimeMillis() - time);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkList(list, id, clazz);
        return list;
    }

    protected <T> List<T> query3TierCascade(Class<T> clazz, String where, Object[] whereArgs) {
        List<T> list = null;
        try {
            mReadWriteLock.readLock().lock();
            long time = System.currentTimeMillis();
            list = App.get().db().query3TierCascade(new QueryBuilder(clazz).where(where, whereArgs));
            TLog.info(TAG, "query3TierCascade() called with:  where = [%s],whereArgs = [%s] time = [%d]", where, whereArgs[0], System.currentTimeMillis() - time);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkList(list, clazz, where, whereArgs);
        return list;
    }

    protected <T> List<T> queryCascade(Class<T> clazz, long id) {
        List<T> list = null;
        try {
            mReadWriteLock.readLock().lock();
            long time = System.currentTimeMillis();
            list = App.get().db().queryCascade(new QueryBuilder(clazz).where("parentId = ?", new Object[]{id}));
            TLog.info(TAG, "queryCascade() called with:  id = [%d], time = [%d]", id, System.currentTimeMillis() - time);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkList(list, id, clazz);
        return list;
    }

    protected <T> List<T> query(Class<T> clazz, String where, Object[] whereArgs) {
        List<T> list = null;
        try {
            mReadWriteLock.readLock().lock();
            long time = System.currentTimeMillis();
            list = App.get().db().query(new QueryBuilder(clazz).where(where, whereArgs));
            TLog.info(TAG, "queryCascade() called with:  where = [%s], time = [%d]", where, System.currentTimeMillis() - time);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkList(list, clazz, where, whereArgs);
        return list;
    }

    protected <T> T queryById(String key, Class<T> tClass) {
        T t = null;
        try {
            mReadWriteLock.readLock().lock();
            t = App.get().db().queryById(key, tClass);
            if (t == null) {
                Exception exception = new Exception("data is null, class = [" + tClass + "], key = [" + key + "]");
                ExceptionUtils.postCatchedException(new TWLException(TWLException.MAIN_APP, exception));
            }
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkValue(tClass, t, key);
        return t;
    }

    protected <T> T queryById(long key, Class<T> tClass) {
        T t = null;
        try {
            mReadWriteLock.readLock().lock();
            t = App.get().db().queryById(key, tClass);
        } catch (Throwable e) {
        } finally {
            mReadWriteLock.readLock().unlock();
        }
        checkValue(tClass, t, key);
        return t;
    }

    private void checkList(List list, long id, Class clazz) {
        if (mInitialize && (list == null || list.isEmpty())) {
            TLog.error(TAG, "checkList: clazz = [%s], id=[%d]", clazz, id);
            Exception exception = new Exception("list is null");
            ExceptionUtils.postCatchedException(new TWLException(TWLException.MAIN_APP, exception));
        }
    }

    private void checkList(List list, Class clazz, String where, Object[] whereArgs) {
        if (mInitialize && (list == null || list.isEmpty())) {
            try {
                if (whereArgs != null && whereArgs.length > 0) {
                    Object obj = whereArgs[0];
                    if (obj instanceof Long) {
                        long id = ((Long) obj).longValue();
                        if (id == SUBWAY_ID
                                || id == DISTANCE_ID
                                || id == BUSINESS_DISTRICT_ID
                                || id == BUSINESS_DISTRICT_ID_FOR_GEEK) {
                            return;
                        }
                    } else if (obj instanceof String) {//过滤州没有匹配的情况
                        String strKey = (String) obj;
                        if (strKey.contains("自治州") || strKey.endsWith("省") || strKey.endsWith("自治区")) {
                            return;
                        }
                        BuglyLog.e("VersionAndDatas", "strKey = " + strKey);
                    }
                }

                TLog.error(TAG, "checkList: clazz = [%s], where=[%s], args=[%s]", clazz, where, Arrays.toString(whereArgs));
                Exception exception = new Exception("list is null");
                CrashReport.postCatchedException(exception);
            } catch (Exception e) {
                CrashReport.postCatchedException(e);
            }
        }
    }

    private void checkValue(Class clazz, Object value, Object key) {
        if (value == null) {
            TLog.error(TAG, "checkList: clazz = [%s], args=[%s]", clazz, key);
            Exception exception = new Exception("data is null");
            CrashReport.postCatchedException(exception);
        }
    }
}
