package com.hpbr.bosszhipin.common;

import static com.basedata.core.ExceptionConstants.ACTION_BASEDATA_EXCEPTION;

import android.content.Context;
import android.content.res.AssetManager;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.basedata.businessdata.BossFilterDataFactory;
import com.basedata.businessdata.BusinessDistrctDataFactory;
import com.basedata.businessdata.CityDataFactory;
import com.basedata.businessdata.DegreeDataFactory;
import com.basedata.businessdata.DistanceDataFactory;
import com.basedata.businessdata.ExperienceDataFactory;
import com.basedata.businessdata.ExtraDataFactory;
import com.basedata.businessdata.GeekFilterDataFactory;
import com.basedata.businessdata.IndustryDataFactory;
import com.basedata.businessdata.InternalPositionlDataFactory;
import com.basedata.businessdata.PositionDataFactory;
import com.basedata.businessdata.ScaleDataFactory;
import com.basedata.businessdata.SchoolLevelDataFactory;
import com.basedata.businessdata.SubwayDataFactory;
import com.basedata.core.BasicDataActionHelper;
import com.basedata.core.BasicDataCompareUtils;
import com.basedata.core.BasicDataContainsChecker;
import com.basedata.core.BasicDataCore;
import com.basedata.core.BasicDataIO;
import com.basedata.core.BasicDataLog;
import com.basedata.core.BasicDataProcessAction;
import com.basedata.core.ExceptionConstants;
import com.google.gson.reflect.TypeToken;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.CommonConstants;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.basedata.ExtraDataBean;
import com.hpbr.bosszhipin.common.basedata.PositionWordsCategoryBean;
import com.hpbr.bosszhipin.common.basedata.WorkCategorywordsBean;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.BCitySortUtil;
import com.hpbr.bosszhipin.utils.CitySortUtil;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.GeekNormalCitySortUtil;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.techwolf.lib.tlog.TLog;
import com.twl.mms.utils.ExceptionUtils;
import com.twl.mms.utils.TWLException;
import com.twl.utils.GsonUtils;

import org.apache.commons.lang3.ArrayUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.ref.SoftReference;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Created by monch on 15/7/3.
 */
public class VersionAndDatasCommon extends VersionAndDatas {


    // 以下城市才有地铁，用于过滤埋点badCase
    public static final long[] subwayCitis = new long[]{
            101010100, 101020100, 101030100, 101040100,
            101050100, 101060100, 101070100, 101070200,
            101080100, 101090100, 101100100, 101110100,
            101120100, 101120200, 101130100, 101160100,
            101180100, 101190100, 101190200, 101190400,
            101190800, 101191100, 101200100, 101210100,
            101210400, 101210700, 101220100, 101230100,
            101230200, 101240100, 101250100, 101260100,
            101270100, 101280100, 101280600, 101280800,
            101281600, 101290100, 101300100, 101320300
    };


    public static final String TAG = "VersionAndDatasCommon";
    public static final int BASE_DATA_TYPE_MMKV = 1;
    public static final int BASE_DATA_TYPE_DEFAULT = 0;
    public static final String BASE_DATA_TYPE_TAG = "base_data_type";
    public boolean mmkvBasicData = true;
    private volatile static VersionAndDatasCommon instance;
    private final Context context;

    private final SP sp = SP.get();
    private SoftReference<List<LevelBean>> mIndustriyConfig2Reference;
    /**
     * 工作状态列表
     */
    private List<LevelBean> jobStatusList;
    private int chatCommonMaxCache;
    private int chatCommonMaxSameWords;
    /**
     * 红包最小值，最大值
     */
    private int redEnvelopeMinValue = -1;
    private int redEnvelopeMaxValue = -1;
    /**
     * 冲值最小值，最大值
     */
    private int rechargeMinValue = -1;
    private int rechargeMaxValue = -1;
    /**
     * 提现最小值，最大值
     */
    private int withdrawMinValue = -1;
    private int withdrawMaxValue = -1;

    private SoftReference<List<LevelBean>> mCNormalSortCityListReference;
    private SoftReference<List<LevelBean>> mSortCityListReference;
    private SoftReference<List<LevelBean>> mBSortCityListReference;
    private SoftReference<List<LevelBean>> mBSortCityWithClearListReference;
    private SoftReference<List<LevelBean>> mSortNormalCityListReference;
    private SoftReference<List<LevelBean>> cityListReference;
    private SoftReference<List<LevelBean>> positionListReference;
    private SoftReference<List<LevelBean>> positionPartTimeListReference;
    private SoftReference<List<LevelBean>> internPositionListReference;

    /**
     * 牛人筛选条件列表
     */
    private SoftReference<List<FilterBean>> geekFilterListReference;
    /**
     * BOSS筛选条件列表
     */
    private SoftReference<List<FilterBean>> bossFilterListReference;
    /**
     * 猎头擅长职类（不包含「其他」为前缀的职类）
     */
    private SoftReference<List<LevelBean>> hunterPositionListReference;
    private int readDataType = BASE_DATA_TYPE_MMKV;
    ;
    private List<LevelBean> list;
    private BasicDataCore basicDataCore;

    private VersionAndDatasCommon(Context context) {
        this.context = context;
    }

    public static VersionAndDatasCommon getInstance() {
        if (instance == null) {
            synchronized (VersionAndDatasCommon.class) {
                if (instance == null) {
                    instance = new VersionAndDatasCommon(App.get().getApplicationContext());
                }
            }
        }
        return instance;
    }

    /**
     * 1311.92372【基础数据】基础数据功能完善：添加基础数据版本号重置方法
     */
    public static void tryResetBasicData() {
        try {
            BasicDataActionHelper.onBasicFunctionExecute(0, null);
            // 清理本地所有业务数据版本号
            BasicDataIO.forceClearDataVersion();
            // 重新请求基础数据版本号，并更新本地 MMKV 中的基础数据
            VersionAndDatasCommon.getInstance().tryCheckVersionAndUpdate();
            // 执行完成上报
            BasicDataActionHelper.onBasicFunctionExecute(1, null);
        } catch (Exception e) {
            TLog.error(TAG, e.getMessage());
            // 执行出错上报
            BasicDataActionHelper.onBasicFunctionExecute(-1, e.getMessage());
        }
    }

    /**
     * 1311.92372【基础数据】基础数据功能完善：手动调用接口请求逻辑，更新本地基础数据
     */
    private void tryCheckVersionAndUpdate() {
        if (basicDataCore != null) {
            basicDataCore.requestMainVersion();
        } else {
            // 执行失败上报
            BasicDataActionHelper.onBasicFunctionExecute(-2, "basicDataCore is null");
        }
    }

    /**
     * 7.02 C端使用新的商圈数据
     */
    public void init() {

        synchronized (VersionAndDatasCommon.class) {
            if (mInitialize) return;
        }
        try {
            BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_PRE_INIT);

            basicDataCore = new BasicDataCore(App.getAppContext());
            basicDataCore.init();
            String wasUpdateDatasKey = "com.hpbr.bosszhipin.NewVersion.UpdateDatas.Key." + MobileUtil.getVersion();
            boolean wasUpdateDatas = sp.getBoolean(wasUpdateDatasKey, true);

            BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_START_CLEAR);

            if (wasUpdateDatas) {
                BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_NEW_APK);
                BasicDataIO.forceClearDataVersion();
                sp.putBoolean(wasUpdateDatasKey, false);
            }

            BasicDataProcessAction.getInstance().onMainProcessStep(BasicDataProcessAction.ProcessStep.ON_START_ASSETS);

            BasicDataActionHelper.onStartAssetsInit(SUBWAY);
            if (!isFinished(SUBWAY)) {
                boolean ret = handleSubway(getData(SUBWAY));
                sp.putBoolean(getLocalFinsihKey(SUBWAY), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(CITY_FILENAME);
            if (!isFinished(CITY_FILENAME)) {
                boolean ret = handleCityData(getData(CITY_FILENAME));
                sp.putBoolean(getLocalFinsihKey(CITY_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(POSITION_BEAN_FILENAME);
            if (!isFinished(POSITION_BEAN_FILENAME)) {
                boolean ret = handlePosition(getData(POSITION_BEAN_FILENAME));
                sp.putBoolean(getLocalFinsihKey(POSITION_BEAN_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(INTERNPOSITION_FILENAME);
            if (!isFinished(INTERNPOSITION_FILENAME)) {
                boolean ret = handleInternPosition(getData(INTERNPOSITION_FILENAME));
                sp.putBoolean(getLocalFinsihKey(INTERNPOSITION_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(BUSINESS_DISTRICT);
            if (!isFinished(BUSINESS_DISTRICT)) {
                boolean ret = handleBusinessDistrict(getData(BUSINESS_DISTRICT), ROLE.BOSS);
                sp.putBoolean(getLocalFinsihKey(BUSINESS_DISTRICT), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(BUSINESS_DISTRICT_FOR_GEEK);
            if (!isFinished(BUSINESS_DISTRICT_FOR_GEEK)) {
                boolean ret = handleBusinessDistrict(getData(BUSINESS_DISTRICT_FOR_GEEK), ROLE.GEEK);
                sp.putBoolean(getLocalFinsihKey(BUSINESS_DISTRICT_FOR_GEEK), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(SCALE_FILENAME);
            if (!isFinished(SCALE_FILENAME)) {
                boolean ret = handleScaleData(getData(SCALE_FILENAME));
                sp.putBoolean(getLocalFinsihKey(SCALE_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(DEGREE_FILENAME);
            if (!isFinished(DEGREE_FILENAME)) {
                boolean ret = handleDegreeData(getData(DEGREE_FILENAME));
                sp.putBoolean(getLocalFinsihKey(DEGREE_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(EXPERIENCE_FILENAME);
            if (!isFinished(EXPERIENCE_FILENAME)) {
                boolean ret = handleExperienceData(getData(EXPERIENCE_FILENAME));
                sp.putBoolean(getLocalFinsihKey(EXPERIENCE_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(GEEKFILTERCONFIG_FILENAME);
            if (!isFinished(GEEKFILTERCONFIG_FILENAME)) {
                boolean ret = handleGeekfilter(getData(GEEKFILTERCONFIG_FILENAME));
                sp.putBoolean(getLocalFinsihKey(GEEKFILTERCONFIG_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(BOSSFILTERCONFIG_FILENAME);
            if (!isFinished(BOSSFILTERCONFIG_FILENAME)) {
                boolean ret = handleBossFitler(getData(BOSSFILTERCONFIG_FILENAME));
                sp.putBoolean(getLocalFinsihKey(BOSSFILTERCONFIG_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(INDUSTRY_FILENAME);
            if (!isFinished(INDUSTRY_FILENAME)) {
                boolean ret = handleIndustryData(getData(INDUSTRY_FILENAME));
                sp.putBoolean(getLocalFinsihKey(INDUSTRY_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(SCHOOLSEARCH_FILENAME);
            if (!isFinished(SCHOOLSEARCH_FILENAME)) {
                boolean ret = handleSchoolSearch(getData(SCHOOLSEARCH_FILENAME));
                sp.putBoolean(getLocalFinsihKey(SCHOOLSEARCH_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(DISTANCE);
            if (!isFinished(DISTANCE)) {
                boolean ret = handleDistance(getData(DISTANCE));
                sp.putBoolean(getLocalFinsihKey(DISTANCE), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(SALARY_FILENAME);
            if (!isFinished(SALARY_FILENAME)) {
                boolean ret = handleSalary(getData(SALARY_FILENAME));
                sp.putBoolean(getLocalFinsihKey(SALARY_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(STAGE_FILENAME);
            if (!isFinished(STAGE_FILENAME)) {
                boolean ret = handleStage(getData(STAGE_FILENAME));
                sp.putBoolean(getLocalFinsihKey(STAGE_FILENAME), ret);
            }

            BasicDataActionHelper.onStartAssetsInit(EXTRA_BEAN_FILENAME);
            if (!isFinished(EXTRA_BEAN_FILENAME)) {
                boolean ret = handleExtra(getData(EXTRA_BEAN_FILENAME));
                sp.putBoolean(getLocalFinsihKey(EXTRA_BEAN_FILENAME), ret);
            }

            basicDataCore.requestMainVersion();

        } catch (Throwable e) {
            ExceptionUtils.postCatchedException(new TWLException(TWLException.MAIN_APP, e));//上报异常
        } finally {
            synchronized (VersionAndDatasCommon.class) {
                mInitialize = true;
            }
        }
    }

    public String getLocalFinsihKey(String key) {

        return "com.hpbr.bosszhipin.NewVersion.UpdateDatas.Key.localFinished" + key + MobileUtil.getVersion();
    }


    // 是否已经完成第一次本地加载
    public boolean isFinished(String key) {
        if (sp.getBoolean(getLocalFinsihKey(key), false)) {
            BasicDataActionHelper.onAssetsFinished(key);
            return true;
        } else {
            return false;
        }
    }


    /**
     * 将Assets中的DataBase数据保存入数据库
     *
     * @param jsonString
     */

    private boolean handleCityData(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleCity(jsonObject);
        }
        return false;
    }

    private boolean handleDegreeData(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleDegree(jsonObject);
        }
        return false;
    }

    private boolean handleExperienceData(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleExperience(jsonObject);
        }
        return false;
    }

    private boolean handleInternPosition(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleInternPosition(jsonObject);
        }
        return false;
    }

    private boolean handleIndustryData(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleIndustry(jsonObject);
        }
        return false;
    }

    private boolean handleSchoolSearch(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleSchoolSearch(jsonObject);
        }
        return false;

    }

    private boolean handleScaleData(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleScale(jsonObject);
        }
        return false;
    }


    private boolean handleGeekfilter(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleGeekFilter(jsonObject);
        }
        return false;
    }

    private boolean handleBossFitler(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleBossFilter(jsonObject);
        }
        return false;
    }

    private boolean handleDistance(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleDistance(jsonObject);
        }

        return false;
    }

    private boolean handleSalary(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleSalary(jsonObject);
        }
        return false;
    }

    private boolean handleStage(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleStage(jsonObject);
        }
        return false;
    }

    /**
     * 将DataBase(保存入数据库
     *
     * @param jsonObject
     */

    /**
     * 将Assets中的Extra数据保存入数据库
     *
     * @param jsonString
     */
    public boolean handleExtra(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            return BasicDataIO.handleExtra(jsonObject);
        }

        return false;
    }

    private boolean handleBusinessDistrict(String jsonString, ROLE role) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
            if (role == ROLE.BOSS) {
                return BasicDataIO.handleBossBusinessDistrict(jsonObject);
            } else {
                return BasicDataIO.handleGeekBusinessDistrict(jsonObject);
            }
        }

        return false;
    }

    private boolean handleSubway(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);

            return BasicDataIO.handleSubway(jsonObject);
        }

        return false;
    }


    /**
     * 将Assets中的SkillWord数据保存入数据库
     *
     * @param jsonString
     */
    private boolean handlePosition(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
//            if (handlePosition(jsonObject)) {
//                String keyBoolean = getKey(POSITION_BEAN_FILENAME, "boolean");
//                sp.putBoolean(keyBoolean, false);
//            }
            return BasicDataIO.handlePosition(jsonObject);
        }
        return false;
    }


    /**
     * 将Extra保存入数据库
     *
     * @param jsonObject
     */
    public boolean handleExtra(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            String stringDataValue = jsonObject.optString("dataValue");
            String key1 = getKey(EXTRA_BEAN_FILENAME, "float");
            float version = (float) jsonObject.optDouble("dataVersion");
            float localDataVersion = SP.get().getFloat(key1, -1);
            if (version > localDataVersion) {
                if (!LText.empty(stringDataValue)) {
                    JSONObject joDataValue = getJsonObject(stringDataValue);
                    if (joDataValue != null && joDataValue.length() > 0) {
                        Iterator<String> iterator = joDataValue.keys();
                        List<ExtraDataBean> list = new ArrayList<>(joDataValue.length());
                        while (iterator.hasNext()) {
                            String key = iterator.next();
                            ExtraDataBean extradata = new ExtraDataBean();
                            extradata.extKey = key;
                            Object object = joDataValue.opt(key);
                            if (object instanceof JSONArray) {
                                JSONArray jsonArray = (JSONArray) object;
                                boolean isStringArray = false;
                                if (jsonArray.length() > 0) {
                                    if (jsonArray.opt(0) instanceof String) {
                                        isStringArray = true;
                                    }
                                }
                                String string = jsonArray.toString();
                                if (isStringArray) {
                                    extradata.extValue = string.substring(1, string.length() - 1).replace("\"", "");
                                } else {
                                    extradata.extValue = string;
                                }

                            } else {
                                extradata.extValue = object + "";
                            }
                            list.add(extradata);
                        }
                        ret = resetBean(list, null);

                        if (ret) {
                            SP.get().putFloat(key1, version);
                        }
                    }
                }
            }
        }
        return ret;
    }


    private boolean handleDistance(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            String key1 = getKey(DISTANCE, "float");
            float version = (float) jsonObject.optDouble("dataVersion");
            float localDataVersion = sp.getFloat(key1, -1);
            JSONArray jaData = jsonObject.optJSONArray("distanceFilter");
            if (version > localDataVersion && jaData != null && jaData.length() > 0) {
                ret = saveLevelBean(jaData, DISTANCE_ID);
                if (ret) {
                    sp.putFloat(key1, version);
                }
            }
        }
        return ret;
    }


    private boolean handleBusinessDistrict(JSONObject jsonObject, ROLE role) {
        boolean ret = false;
        if (jsonObject != null) {
            String key1 = getKey(keyOfBd(role), "float");
            float version = (float) jsonObject.optDouble("dataVersion");
            float localDataVersion = sp.getFloat(key1, -1);
            JSONArray jaData = jsonObject.optJSONArray("businessDistrict");
            if (version > localDataVersion && jaData != null && jaData.length() > 0) {
                ret = saveLevelBean(jaData, codeOfBd(role));

                if (ret) {
                    sp.putFloat(key1, version);
                }
            }
        }
        return ret;
    }


    private boolean handleSubway(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            String key1 = getKey(SUBWAY, "float");
            float version = (float) jsonObject.optDouble("dataVersion");
            float localDataVersion = sp.getFloat(key1, -1);
            JSONArray jaData = jsonObject.optJSONArray("subway");
            if (version > localDataVersion && jaData != null && jaData.length() > 0) {
                ret = saveLevelBean(jaData, SUBWAY_ID);

                if (ret) {
                    sp.putFloat(key1, version);
                }
            }
        }
        return ret;
    }

    /**
     * 将Assets中的SkillWord数据保存入数据库
     *
     * @param jsonString
     */
    private void handleSkillWord(String jsonString) {
        if (!LText.empty(jsonString)) {
            JSONObject jsonObject = getJsonObject(jsonString);
//            handleSubway(jsonObject);
            BasicDataIO.handleSkillWord(jsonObject);
//            String keyBoolean = getKey(SUBWAY, "boolean");
//            sp.putBoolean(keyBoolean, false);
        }
    }


    /**
     * 将Skillword保存入数据库
     *
     * @param jsonObject
     */
//    private boolean handleSkillWord(JSONObject jsonObject) {
//        boolean ret = false;
//        if (jsonObject != null) {
//            String key1 = getKey(SKILL_WORD_BEAN_FILENAME, "float");
//            float version = (float) jsonObject.optDouble("dataVersion");
//            float localDataVersion = sp.getFloat(key1, -1);
//            if (version > localDataVersion) {
//                JSONObject workCategorywords = jsonObject.optJSONObject("workCategorywords");
//                ret = saveWorkCategoryWords(workCategorywords);
//                JSONObject positionWordsCategory = jsonObject.optJSONObject("positionWordsCategory");
//                ret &= savePositionWordsCategory(positionWordsCategory);
//                if (ret) {
//                    sp.putFloat(key1, version);
//                }
//            }
//        }
//        return ret;
//    }
    private boolean handlePosition(JSONObject jsonObject) {
        boolean ret = false;
        if (jsonObject != null) {
            String key1 = getKey(POSITION_BEAN_FILENAME, "float");
            float version = (float) jsonObject.optDouble("positionVersion");
            float localDataVersion = sp.getFloat(key1, -1);
            String city = jsonObject.optString("cityCode");
            String cityKey = getKey(POSITION_BEAN_FILENAME, "code");
            String localCity = sp.getString(cityKey, "");
            if (version > localDataVersion || !LText.equal(city, localCity)) {
                JSONArray position = jsonObject.optJSONArray("position");

                ret = saveLevelBean(position, POSITION_ID, true);
                if (ret) {
                    sp.putFloat(key1, version);
                    sp.putString(cityKey, city);
                }
            }
        }
        return ret;
    }

    /**
     * 保存工作范围词
     *
     * @param jsonObjectWords
     */
    private boolean saveWorkCategoryWords(JSONObject jsonObjectWords) {
        boolean ret = false;
        if (jsonObjectWords != null && jsonObjectWords.length() > 0) {
            Iterator<String> keys = jsonObjectWords.keys();
            List<WorkCategorywordsBean> list = new ArrayList<>(jsonObjectWords.length());
            while (keys.hasNext()) {
                String key = keys.next();
                JSONArray ja = jsonObjectWords.optJSONArray(key);
                if (ja != null && ja.length() > 0) {
                    WorkCategorywordsBean bean = new WorkCategorywordsBean();
                    bean.id = Long.parseLong(key);
                    int size = ja.length();
                    StringBuilder sb = new StringBuilder(ja.optString(0));
                    for (int i = 1; i < size; i++) {
                        String word = ja.optString(i);
                        if (TextUtils.isEmpty(word)) continue;
                        sb.append(",").append(word);
                    }
                    bean.words = sb.toString();
                    list.add(bean);
                }
            }
            ret = resetBean(list, null);
        }
        return ret;
    }

    private boolean savePositionWordsCategory(JSONObject jsonObjectWords) {
        boolean ret = false;
        if (jsonObjectWords != null && jsonObjectWords.length() > 0) {
            Iterator keys = jsonObjectWords.keys();
            List<PositionWordsCategoryBean> list = new ArrayList<>(jsonObjectWords.length());
            while (keys.hasNext()) {
                String key = (String) keys.next();
                PositionWordsCategoryBean bean = new PositionWordsCategoryBean();
                bean.positionId = LText.getLong(key);
                bean.categoryId = jsonObjectWords.optLong(key);
                list.add(bean);
            }
            ret = resetBean(list, null);
        }
        return ret;
    }

    /**
     * 将本地文件压入本地偏好
     *
     * @param fileName
     * @return
     */
    private String getData(String fileName) {
        BasicDataActionHelper.onAssetsStartGet(fileName);
        String json = getAssetFile(fileName);
        if (TextUtils.isEmpty(json)) {
            BasicDataActionHelper.onAssetsGetEmpty(fileName);
            return null;
        }
        BasicDataActionHelper.onAssetsStartSave(fileName);
        return json;
    }

    /**
     * 获取asset文件夹下的文件
     *
     * @param fileName
     */
    private String getAssetFile(String fileName) {
        AssetManager am = context.getAssets();
        InputStreamReader input = null;
        BufferedReader reader = null;
        StringBuffer result = null;
        try {
            InputStream inputStream = am.open(fileName);
            result = new StringBuffer(inputStream.available());
            input = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            reader = new BufferedReader(input);
            String line = "";
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception e) {
            } finally {
                if (input != null) {
                    try {
                        input.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
        return result.toString();
    }

    /**
     * 获取学历列表
     *
     * @return
     */
    public List<LevelBean> getDegreeList() {
        tryLogFunction("getDegreeList()");
        List<LevelBean> degreeList = new ArrayList<>();
        degreeList = DegreeDataFactory.getInstance().getDegreeList();
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            degreeList = DegreeDataFactory.getInstance().getDegreeList();
//            if (LList.isEmpty(degreeList)) {
//                degreeList = query(LevelBean.class, DEGREE_ID);
//            }
//        } else {
//            degreeList = query(LevelBean.class, DEGREE_ID);
//            if (LList.isEmpty(degreeList)) {
//                degreeList = DegreeDataFactory.getInstance().getDegreeList();
//            }
//        }

        return degreeList;
    }


    public List<LevelBean> getPositionListForPartTime() {
        List<LevelBean> positionList = new ArrayList<>();
        if (positionPartTimeListReference != null) {
            List<LevelBean> cachedList = positionPartTimeListReference.get();
            if (LList.getCount(cachedList) > 0) {
                positionList.addAll(cachedList);
                return positionList;
            }
        }
        List<LevelBean> originList = getPositionList();
        if (LList.getCount(originList) == 0) {
            return positionList;
        }

        for (LevelBean first : originList) {
            if (first == null || LList.getCount(first.subLevelModeList) == 0) {
                continue;
            }
            for (LevelBean second : first.subLevelModeList) {
                if (second == null || LList.getCount(second.subLevelModeList) == 0) {
                    continue;
                }
                LevelBean newFirstBean = null;
                for (LevelBean third : second.subLevelModeList) {
                    if (third == null) {
                        continue;
                    }
                    //809新增 判断是否是兼职类型 positionType 为2或3， 1:全职 2:兼职 3:全职&兼职
                    int positionType = third.positionType;
                    if (positionType != 2 && positionType != 3) {
                        continue;
                    }
                    if (newFirstBean == null) {
                        newFirstBean = new LevelBean(second.code, second.name);
                        positionList.add(newFirstBean);
                    }
                    if (newFirstBean.subLevelModeList == null) {
                        newFirstBean.subLevelModeList = new ArrayList<>();
                    }
                    LevelBean newThirdBean = new LevelBean(third.code, third.name);
                    newThirdBean.mark = third.mark;
                    newThirdBean.positionType = third.positionType;
                    newFirstBean.subLevelModeList.add(newThirdBean);
                }
            }
        }

        if (LList.getCount(positionList) > 0) {
            positionPartTimeListReference = new SoftReference<>(positionList);
        } else {
            TLog.print(TAG, "position-part-time list is empty");
        }
        return positionList;
    }

    /**
     * 获取职位列表
     *
     * @return
     */
    public List<LevelBean> getPositionList() {
        tryLogFunction("getPositionList()");
        List<LevelBean> positionList = new ArrayList<>();

        if (positionListReference != null) {
            positionList = positionListReference.get();
            if (LList.getCount(positionList) > 0) {
                return positionList;
            }
        }


        if (LList.getCount(positionList) == 0) {
            positionList = PositionDataFactory.getInstance().getPositionList();
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//                if (LList.isEmpty(positionList)) {
//                    positionList = query3TierCascade(LevelBean.class, POSITION_ID);
//                }
//            } else {
//                positionList = query3TierCascade(LevelBean.class, POSITION_ID);
//                if (LList.isEmpty(positionList)) {
//                    positionList = PositionDataFactory.getInstance().getPositionList();
//                }
//            }
//
//            if (LList.getCount(positionList) > 0) {
//                positionListReference = new SoftReference<>(positionList);
//            } else {
//                L.e(TAG, "position list is empty");
//            }
        }

        return positionList;
    }

    /**
     * 获得实习生求职期望类型
     *
     * @return
     */
    public List<LevelBean> getInternPositionList() {
        tryLogFunction("getInternPositionList()");
        List<LevelBean> internPositionList = null;

        if (internPositionListReference != null) {
            internPositionList = internPositionListReference.get();
            if (LList.getCount(internPositionList) > 0) {
                return internPositionList;
            }
        }

        if (LList.getCount(internPositionList) == 0) {
            internPositionList = InternalPositionlDataFactory.getInstance().getInternalPositions();
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                internPositionList = InternalPositionlDataFactory.getInstance().getInternalPositions();
//                if (LList.isEmpty(internPositionList)) {
//                    internPositionList = query3TierCascade(LevelBean.class, INTERN_POSITION_ID);
//                }
//            } else {
//                internPositionList = query3TierCascade(LevelBean.class, INTERN_POSITION_ID);
//                if (LList.isEmpty(internPositionList)) {
//                    internPositionList = InternalPositionlDataFactory.getInstance().getInternalPositions();
//                }
//            }

            if (LList.getCount(internPositionList) > 0) {
                internPositionListReference = new SoftReference<>(internPositionList);
            } else {
                L.e(TAG, "intern position list is empty");
            }
        }

        return internPositionList;
    }

    /**
     * 获取城市列表
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getCityList() {
        tryLogFunction("getCityList()");

        List<LevelBean> cityList = null;

        if (cityListReference != null) {
            cityList = cityListReference.get();
        }

        if (LList.getCount(cityList) == 0) {

            cityList = CityDataFactory.getInstance().getCityList();
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//                if (LList.isEmpty(cityList)) {
//                    cityList = query3TierCascade(LevelBean.class, CITY_ID);
//                }
//            } else {
//                cityList = query3TierCascade(LevelBean.class, CITY_ID);
//                if (LList.isEmpty(cityList)) {
//                    cityList = CityDataFactory.getInstance().getCityList();
//                }
//            }

        }

        if (LList.getCount(cityList) > 0) {
            cityListReference = new SoftReference<>(cityList);
        } else {
            L.e(TAG, "cityList is empty");
        }
        return cityList;
    }

    /**
     * 根据name在数据库中查找省的信息(包括其所有市和对应的区)
     *
     * @param name
     * @return
     */
    @Nullable
    public LevelBean getProvinceByName(@Nullable String name) {
        tryLogFunction("getProvinceByName()");
        LevelBean province;
        if (TextUtils.isEmpty(name)) {
            return null;
        }

        province = CityDataFactory.getInstance().getProvinceByName(context, name);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            province = CityDataFactory.getInstance().getProvinceByName(name);
//            if (province == null) {
//                province = getProvinceByNameDefault(name);
//            }
//        } else {
//            province = getProvinceByNameDefault(name);
//            if (province == null) {
//                province = CityDataFactory.getInstance().getProvinceByName(name);
//            }
//        }
        return province;
    }

    @Nullable
    public LevelBean getProvinceByNameDefault(@Nullable String name) {
        List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId = ? and name like ?", new Object[]{CITY_ID, name});
        if (LList.getCount(list) <= 0) {
            return null;
        } else {
            return list.get(0);
        }
    }


    /**
     * 所有省的信息
     *
     * @return
     */
    @Nullable
    public List<LevelBean> getProvinces() {
        List<LevelBean> list = new ArrayList<>();
        if (readDataType == BASE_DATA_TYPE_MMKV) {
            list = CityDataFactory.getInstance().getProvinces(context);
            if (LList.isEmpty(list)) {
                list = query(LevelBean.class, "parentId = ?", new Object[]{CITY_ID});
                if (!LList.isEmpty(list)) {
                    String erroInfo = "province , size : " + list.size();
                }
            }

        } else {
            list = query(LevelBean.class, "parentId = ?", new Object[]{CITY_ID});
            if (LList.isEmpty(list)) {
                list = CityDataFactory.getInstance().getProvinces(context);
            }
        }

        return list;
    }

    public List<LevelBean> getProvincesDefault() {
        tryLogFunction("getProvincesDefault()");
        List<LevelBean> list = new ArrayList<>();
        list = query(LevelBean.class, "parentId = ?", new Object[]{CITY_ID});
        return list;
    }


    /**
     * 所有市的信息
     *
     * @param id
     * @return
     */
    public List<LevelBean> getCitiesById(long id) {
        tryLogFunction("getCitiesById()");
        List<LevelBean> list = new ArrayList<>();
        list = query(LevelBean.class, "parentId = ?", new Object[]{id});
        return list;
    }

    /**
     * 所有区的信息
     *
     * @param id
     * @return
     */
    public List<LevelBean> getAreasById(long id) {
        tryLogFunction("getAreasById()");
        List<LevelBean> list = query(LevelBean.class, "parentId = ?", new Object[]{id});
        return list;
    }

    /**
     * 返回一个Map
     *
     * @param provinceName
     * @param cityName
     * @param areaName
     * @returnx
     */
    public Map<Integer, LevelBean> getProvinceCityArea(@Nullable String provinceName,
                                                       @Nullable String cityName,
                                                       @Nullable String areaName) {
        tryLogFunction("getProvinceCityArea()");
        Map<Integer, LevelBean> beans = new HashMap<>();

        beans = CityDataFactory.getInstance().getProvinceCityArea(context, provinceName, cityName, areaName);
        //---------------------------匹配省
//        beans = getProvinceCityAreaDefault(provinceName, cityName, areaName);
//        if (MapUtils.isEmpty(beans)) {
//            beans = CityDataFactory.getInstance().getProvinceCityArea(provinceName, cityName, areaName);
//        }
        return beans;
    }

    public Map<Integer, LevelBean> getProvinceCityAreaDefault(@Nullable String provinceName,
                                                              @Nullable String cityName,
                                                              @Nullable String areaName) {
        Map<Integer, LevelBean> beans = new HashMap<>();
        //---------------------------匹配省
        List<LevelBean> provinces = getProvincesDefault();
        if (provinces == null) {
            return beans;
        }
        LevelBean province = null;
        for (LevelBean bean : provinces) {
            if (provinceName != null && provinceName.contains(bean.name)) {
                province = bean;
                break;
            }
        }
        beans.put(0, province);
        if (province == null) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                    .param("p5", "getProvinceByname")
                    .param("p2", provinceName)
                    .param("p3", cityName)
                    .report();
            return beans;
        }

        //-------------------匹配市
        List<LevelBean> cities = getCitiesById(province.id);
        if (cities == null) {
            return beans;
        }
        LevelBean city = null;
        for (LevelBean ci : cities) {
            if (cityName != null && cityName.contains(ci.name)) {
                city = ci;
                break;
            }
        }
        beans.put(1, city);
        if (city == null) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                    .param("p5", "getCityByName")
                    .param("p2", provinceName)
                    .param("p3", cityName)
                    .report();
            return beans;
        }

        //------------------匹配区
        List<LevelBean> areas = getAreasById(city.id);
        if (areas == null) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                    .param("p5", "getAreasById")
                    .param("p2", provinceName)
                    .param("p3", cityName)
                    .param("p4", String.valueOf(city.id))
                    .report();
            return beans;
        }
        LevelBean area = null;
        for (LevelBean ar : areas) {
            if (areaName != null && areaName.contains(ar.name)) {
                area = ar;
                break;
            }
        }
        beans.put(2, area);

        return beans;
    }


    /**
     * 只获取省份+热门
     */
    public List<LevelBean> getOnlyProvinceList() {

        List<LevelBean> provinceList;
        provinceList = CityDataFactory.getInstance().getOnlyProvinceList(context);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            provinceList = CityDataFactory.getInstance().getOnlyProvinceList();
//            if (LList.isEmpty(provinceList)) {
//                provinceList = getOnlyProvinceListDefault();
        if (LList.isEmpty(provinceList)) {

            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getOnlyProvinceList")
                    .report();

        }
//            }
//        } else {
//            provinceList = getOnlyProvinceListDefault();
//            if (LList.isEmpty(provinceList)) {
//                provinceList = CityDataFactory.getInstance().getOnlyProvinceList();
//            }
//        }
        return provinceList;
    }

    public List<LevelBean> getOnlyProvinceListDefault() {

        List<LevelBean> provinceList = query(LevelBean.class, CITY_ID);
        List<LevelBean> hotList = getHotCityList();
        LevelBean hotBean = new LevelBean();
        hotBean.name = "推荐";
        hotBean.subLevelModeList = hotList;
        provinceList.add(0, hotBean);
        return provinceList;

    }

    /*废弃不要用这个方法*/
    @Deprecated
    public List<LevelBean> getCityListByCode(long id) {
        tryLogFunction("getCityListByCode()");

        List<LevelBean> list = new ArrayList<>();
        list = CityDataFactory.getInstance().getCitiesById(id);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            list = CityDataFactory.getInstance().getCitiesById(id);
//            if (LList.isEmpty(list)) {
//                list = query(LevelBean.class, "parentId=?", new Object[]{id});
//                if (!LList.isEmpty(list)) {
//                }
//            }
//        } else {
//            list = query(LevelBean.class, "parentId=?", new Object[]{id});
//            if (LList.isEmpty(list)) {
//                list = CityDataFactory.getInstance().getCitiesById(id);
//            }
//        }
        return list;
    }

    /**
     * 获取热门城市列表
     *
     * @return
     */
    public List<LevelBean> getHotCityList() {
        tryLogFunction("getHotCityList()");

        List<LevelBean> hotCityList = new ArrayList<>();
        hotCityList = CityDataFactory.getInstance().getHotCityList();
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            hotCityList = CityDataFactory.getInstance().getHotCityList();
//            if (LList.isEmpty(hotCityList)) {
//                hotCityList = query(LevelBean.class, HOT_CITY_ID);
//            }
//        } else {
//            hotCityList = query(LevelBean.class, HOT_CITY_ID);
//            if (LList.isEmpty(hotCityList)) {
//                hotCityList = CityDataFactory.getInstance().getHotCityList();
//            }
//        }

        return hotCityList;
    }

    /**
     * 获取BOSS经验列表
     *
     * @return
     */
    public List<LevelBean> getBossExperienceList() {
        tryLogFunction("getBossExperienceList()");

        List<LevelBean> bossExperienceList = new ArrayList<>();
        bossExperienceList = ExperienceDataFactory.getInstance().getExperienceList();
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            bossExperienceList = ExperienceDataFactory.getInstance().getExperienceList();
//            if (LList.isEmpty(bossExperienceList)) {
//                bossExperienceList = query(LevelBean.class, EXPERIENCE_ID);
//            }
//        } else {
//            bossExperienceList = query(LevelBean.class, EXPERIENCE_ID);
//            if (LList.isEmpty(bossExperienceList)) {
//                bossExperienceList = ExperienceDataFactory.getInstance().getExperienceList();
//            }
//        }
        return bossExperienceList;
    }

    /**
     * 获得经验要求
     * todo 这里需要调整下 INTERN_CODE不再硬编码
     *
     * @param isIntern           实习生
     * @param isDenyChangeIntern 点击了 切换实习生 否
     * @return
     */
    @Deprecated
    public List<LevelBean> getBossExperienceList(boolean isIntern, boolean isDenyChangeIntern) {
        tryLogFunction("getBossExperienceList()");
        if (isIntern) {
            List<LevelBean> levelBeans = new ArrayList<>();
            levelBeans.add(new LevelBean(CommonConstants.INTERN_CODE, "在校生"));
            levelBeans.add(new LevelBean(CommonConstants.SCHOOL_CODE, "应届生"));
            return levelBeans;
        }

        List<LevelBean> bossExperienceList = getBossExperienceList();

        //去掉在校生
        if (isDenyChangeIntern) {
            Iterator<LevelBean> iterator = bossExperienceList.iterator();
            while (iterator.hasNext()) {
                LevelBean next = iterator.next();
                if (next.code == CommonConstants.INTERN_CODE) {
                    iterator.remove();
                    break;
                }
            }
        }

        return bossExperienceList;
    }


    /**
     * 获取院校要求
     *
     * @return 院校列表
     */
    @NonNull
    public List<FilterBean> getSchoolLevelList() {
        tryLogFunction("getSchoolLevelList()");

        List<LevelBean> levelBeans = new ArrayList<>();
        levelBeans = SchoolLevelDataFactory.getInstance().getSchoolLevels();
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            levelBeans = SchoolLevelDataFactory.getInstance().getSchoolLevels();
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = query(LevelBean.class, SCHOOL_LEVEL);
//            }
//        } else {
//            levelBeans = query(LevelBean.class, SCHOOL_LEVEL);
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = SchoolLevelDataFactory.getInstance().getSchoolLevels();
//            }
//        }

        List<FilterBean> filterBeans = new ArrayList<>();
        if (!LList.isEmpty(levelBeans)) {
            for (LevelBean bean : levelBeans) {
                filterBeans.add(new FilterBean(bean.code, bean.name));
            }
        }
        return filterBeans;
    }

    /**
     * 获取某个城市下的指定商圈
     *
     * @param cityCode 城市码
     * @return
     */
    public LevelBean getBusinessDistrictOnSpecifiedCity(long cityCode) {
        tryLogFunction("getBusinessDistrictOnSpecifiedCity()");
        LevelBean district;
        district = BusinessDistrctDataFactory.getInstance().getBusinessDistrictOnSpecifiedCity(cityCode, businessRoleOfBd(UserManager.getUserRole()));
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            district = BusinessDistrctDataFactory.getInstance().getBusinessDistrictOnSpecifiedCity(cityCode, businessRoleOfBd(UserManager.getUserRole()));
//            if (district == null) {
//                List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{codeOfBd(UserManager.getUserRole()), cityCode});
//                district = LList.getElement(list, 0);
        if (district == null) {
            String erroInfo = String.format(Locale.getDefault(), "getBusinessDistrictOnSpecifiedCity : cityCode=[%d]", cityCode);
            boolean dataContainsCity = false;
            if (UserManager.isBossRole()) {
                dataContainsCity = BasicDataContainsChecker.containsBossDistrictCityCode(cityCode);
            } else if(UserManager.isGeekRole()) {
                dataContainsCity = BasicDataContainsChecker.containsGeekDistrictCityCode(cityCode);
            }
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBusinessDistrictOnSpecifiedCity")
                    .param("p2", erroInfo)
                    .param("p3", String.valueOf(cityCode))
                    .param("p4", String.valueOf(UserManager.getUserRole()))
                    .param("p7", String.valueOf(dataContainsCity ? 1 : 0))
                    .report();

        }
//            }
//        } else {
//            List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{codeOfBd(UserManager.getUserRole()), cityCode});
//            district = LList.getElement(list, 0);
////            if (district == null) {
////                district = BusinessDistrctDataFactory.getInstance().getBusinessDistrictOnSpecifiedCity(cityCode, businessRoleOfBd(UserManager.getUserRole()));
////            }
//        }
        return district;

    }

    /**
     * 获取某个城市下的地铁数据
     *
     * @param cityCode 城市码
     * @return
     */
    public LevelBean getSubwayOnSpecifiedCity(long cityCode) {
        tryLogFunction("getSubwayOnSpecifiedCity()");
        LevelBean subway;
        subway = SubwayDataFactory.getInstance().getSubwayOnSpecifiedCity(cityCode);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            subway = SubwayDataFactory.getInstance().getSubwayOnSpecifiedCity(cityCode);
//            if (subway == null) {
//                List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{SUBWAY_ID, cityCode});
//                subway = LList.getElement(list, 0);
        boolean dataContainsCity = BasicDataContainsChecker.containsSubwayCityCode(cityCode);
        if (subway == null && ArrayUtils.contains(subwayCitis, cityCode)) {
            String erroInfo = String.format(Locale.getDefault(), " getSubwayOnSpecifiedCity : cityCode=[%d] ", cityCode);
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getSubwayOnSpecifiedCity")
                    .param("p2", erroInfo)
                    .param("p3", String.valueOf(dataContainsCity ? 1 : 0))
                    .report();
        }
//            }
//        } else {
//            List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{SUBWAY_ID, cityCode});
//            subway = LList.getElement(list, 0);
////            if (subway == null) {
////                subway = SubwayDataFactory.getInstance().getSubwayOnSpecifiedCity(cityCode);
////            }
//        }
        return subway;
    }

    /**
     * 获取某个城市下的置顶周边距离数据
     *
     * @param cityCode 城市码
     * @return
     */
    public LevelBean getDistanceDataOnSpecifiedCity(long cityCode) {
        tryLogFunction("getDistanceDataOnSpecifiedCity()");
        LevelBean distance;
        distance = DistanceDataFactory.getInstance().getDistanceDataOnSpecifiedCity(cityCode);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            distance = DistanceDataFactory.getInstance().getDistanceDataOnSpecifiedCity(cityCode);
//            if (distance == null) {
//                List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{DISTANCE_ID, cityCode});
//                distance = LList.getElement(list, 0);
        if (distance == null && (cityCode == 101010100 || cityCode == 101020100)) { // 只有上海和北京才有距离筛选数据

            String erroInfo = String.format(Locale.getDefault(), "getDistanceDataOnSpecifiedCity : cityCode=[%d]", cityCode);

            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getDistanceDataOnSpecifiedCity")
                    .param("p2", erroInfo)
                    .report();

        }
//            }
//        } else {
//            List<LevelBean> list = query3TierCascade(LevelBean.class, "parentId=? and code=?", new Object[]{DISTANCE_ID, cityCode});
//            distance = LList.getElement(list, 0);
////            if (distance == null) {
////                distance = DistanceDataFactory.getInstance().getDistanceDataOnSpecifiedCity(cityCode);
////            }
//        }
        return distance;
    }

    /**
     * 获取规模列表
     *
     * @return
     */
    public List<LevelBean> getScaleList() {
        tryLogFunction("getScaleList()");
//        if (scaleList == null || scaleList.size() <= 0) {
        List<LevelBean> scaleList = new ArrayList<>();
        scaleList = ScaleDataFactory.getInstance().getScaleList();
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            scaleList = ScaleDataFactory.getInstance().getScaleList();
//            if (LList.isEmpty(scaleList)) {
//                scaleList = query(LevelBean.class, SCALE_ID);
//            }
//        } else {
//            scaleList = query(LevelBean.class, SCALE_ID);
//            if (LList.isEmpty(scaleList)) {
//                scaleList = ScaleDataFactory.getInstance().getScaleList();
//            }
//        }
        //App.get().db().query(new QueryBuilder(LevelBean.class).where("parentId = ?", new Object[]{SCALE_ID}));
//        }
        return scaleList;
    }

    /**
     * 获取二级职位对应的标签的CODE
     *
     * @param key
     * @return -1为未找到合适的code
     */
    public long getCategoryWordsCode(String key) {
        tryLogFunction("getCategoryWordsCode()");
        long result = -1;
        PositionWordsCategoryBean positionWordsCategoryBean = queryById(key, PositionWordsCategoryBean.class);
        if (positionWordsCategoryBean != null) {
            result = positionWordsCategoryBean.categoryId;
        }
        return result;
    }

    /**
     * 获取二级职位对应的技能要求/工作重点标签列表
     *
     * @param code
     * @return
     */
    public List<String> getCategoryWords(String code) {
        if (!LText.empty(code)) {
            long c = getCategoryWordsCode(code);
            if (c != -1) {
                WorkCategorywordsBean bean = queryById(c, WorkCategorywordsBean.class);
                if (bean != null && !TextUtils.isEmpty(bean.words)) {
                    String[] words = bean.words.split(",");
                    return Arrays.asList(words);
                }
            }
        }
        return new ArrayList<>(0);
    }

    /**
     * 清空二级职位对应标签的内存缓存
     */
    public void clearCategoryWords() {
//        if (categoryWordsMap != null) {
//            categoryWordsMap.clear();
//        }
//        if (categoryWordsCodeMap != null) {
//            categoryWordsCodeMap.clear();
//        }
    }

    /**
     * 获取行业列表
     *
     * @return
     */
//    public List<LevelBean> getIndustryList() {
//
//        List<LevelBean> industryList = new ArrayList<>();
//        industryList = IndustryDataFactory.getInstance().getIndustryList();
////        if (readDataType == BASE_DATA_TYPE_MMKV) {
////            industryList = IndustryDataFactory.getInstance().getIndustryList();
////            if (LList.isEmpty(industryList)) {
////                industryList = query(LevelBean.class, INDUSTRY_ID);
//        if (!LList.isEmpty(industryList)) {
//            String erroInfo = "getIndustryList size :" + industryList.size();
//
//            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
//                    .param("p5", "getIndustryList")
//                    .param("p2", erroInfo)
//                    .report();
//
//        }
////            }
////        } else {
////            industryList = query(LevelBean.class, INDUSTRY_ID);
////            if (LList.isEmpty(industryList)) {
////                industryList = IndustryDataFactory.getInstance().getIndustryList();
////            }
////        }
//
//        return industryList;
//    }

    /**
     * 获取新的行业字典数据列表
     *
     * @return 行业字典数据列表
     */
    public List<LevelBean> getIndustryConfig2List() {
        tryLogFunction("getIndustryConfig2List()");
        List<LevelBean> levelBeans = new ArrayList<>();
        if (mIndustriyConfig2Reference != null) {
            levelBeans = mIndustriyConfig2Reference.get();
        }

        levelBeans = IndustryDataFactory.getInstance().getIndustryConfig2List();
//        if (levelBeans == null || levelBeans.size() == 0) {
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                levelBeans = IndustryDataFactory.getInstance().getIndustryConfig2List();
//                if (LList.isEmpty(levelBeans)) {
//                    levelBeans = queryCascade(LevelBean.class, INDUSTRY_CONFIG2_ID);
        if (LList.isEmpty(levelBeans)) {

            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getIndustryConfig2List")
                    .report();
        }
//                }
//            } else {
//                levelBeans = queryCascade(LevelBean.class, INDUSTRY_CONFIG2_ID);
//                if (LList.isEmpty(levelBeans)) {
//                    levelBeans = IndustryDataFactory.getInstance().getIndustryConfig2List();
//                }
//            }
//        }

        if (levelBeans != null && levelBeans.size() > 0) {
            mIndustriyConfig2Reference = new SoftReference<>(levelBeans);
        } else {
            L.e("getIndustryConfig2List获取数据失败");
        }
        return levelBeans;
    }

    /**
     * 使用行业ID获取行业名称
     *
     * @param code
     * @return
     */
    public String getIndustryNameUseCode(long code) {
//        getIndustryList();
        String result = "";
        List<LevelBean> industryList = query(LevelBean.class, "parentId = ? and code = ?", new Object[]{INDUSTRY_ID, code});
        if (!LList.isEmpty(industryList)) {
            result = industryList.get(0).name;
        }
        return result;
    }

    /**
     * 获取工作状态列表
     *
     * @return
     */
    public List<LevelBean> getJobStatusList() {
        tryLogFunction("getJobStatusList()");
        if (jobStatusList == null || jobStatusList.size() <= 0) {
            jobStatusList = new ArrayList<>();
            String jsonString = "{\"jobType\":[ {\"code\":0,\"name\":\"离职-随时到岗\"},{\"code\":3,\"name\":\"在职-月内到岗\"},{\"code\":2,\"name\":\"在职-考虑机会\"},{\"code\":1,\"name\":\"在职-暂不考虑\"}]}";
            initListTypeData(jobStatusList, jsonString, "jobType");
        }
        return jobStatusList;
    }


    private List<LevelBean> studentWorkStatus;


    public List<LevelBean> getStudentWorkStatusList() {
        tryLogFunction("getStudentWorkStatusList()");
        if (studentWorkStatus == null || studentWorkStatus.size() <= 0) {
            studentWorkStatus = new ArrayList<>();
            String jsonString = "{\"jobType\":[ {\"code\":0,\"name\":\"离校-随时到岗\"},{\"code\":3,\"name\":\"在校-月内到岗\"},{\"code\":2,\"name\":\"在校-考虑机会\"},{\"code\":1,\"name\":\"在校-暂不考虑\"}]}";
            initListTypeData(studentWorkStatus, jsonString, "jobType");
        }
        return studentWorkStatus;
    }


    /**
     * 初始化基本类型列表数据
     *
     * @param list
     * @param json
     * @param jsonKey
     */
    public void initListTypeData(List<LevelBean> list, String json, String jsonKey) {
        String jsonString = json;
        if (LText.empty(jsonString)) {
            return;
        }
        JSONObject jsonObject = getJsonObject(jsonString);
        if (jsonObject == null) {
            return;
        }
        JSONArray ja = jsonObject.optJSONArray(jsonKey);
        if (ja == null) return;
        int count = ja.length();
        for (int i = 0; i < count; i++) {
            JSONObject jo = ja.optJSONObject(i);
            if (jo == null) continue;
            LevelBean bean = getLevelBeanFromJson(jo);
            list.add(bean);
        }
    }

    /**
     * 初始化基本类型MAP数据
     *
     * @param map
     * @param json
     * @param jsonKey
     */
    public void initMapTypeData(Map<String, Integer> map, String json, String jsonKey) {
        String jsonString = json;
        if (LText.empty(jsonString)) {
            return;
        }
        JSONObject jsonObject = getJsonObject(jsonString);
        if (jsonObject == null) {
            return;
        }
        JSONObject jo = jsonObject.optJSONObject(jsonKey);
        if (jo == null) return;
        Iterator keys = jo.keys();
        while (keys.hasNext()) {
            String key = (String) keys.next();
            int value = jo.optInt(key);
            map.put(key, value);
        }
    }

    /**
     * 获取BOSS亮点标签列表
     *
     * @return
     */
    public List<String> getBossLureKeywordList() {
        tryLogFunction("getBossLureKeywordList()");
        ExtraDataBean bean;
        bean = ExtraDataFactory.getInstance().getExtraObjectByKey("BossLurekeyword");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            bean = ExtraDataFactory.getInstance().getExtraObjectByKey("BossLurekeyword");
//            if (bean == null) {
//                bean = queryById("BossLurekeyword", ExtraDataBean.class);
//            }
//        } else {
//            bean = queryById("BossLurekeyword", ExtraDataBean.class);
//            if (bean == null) {
//                bean = ExtraDataFactory.getInstance().getExtraObjectByKey("BossLurekeyword");
//            }
//        }
        if (bean != null) {
            String[] strings = bean.extValue.split(",");
            return Arrays.asList(strings);
        }
        return new ArrayList<>(0);
    }

    /**
     * 获取牛人亮点标签列表
     *
     * @return
     */
    public List<String> getGeekLureKeywordList() {
        ExtraDataBean bean;
        if (readDataType == BASE_DATA_TYPE_MMKV) {
            bean = ExtraDataFactory.getInstance().getExtraObjectByKey("SeekerLurekeyword");
        } else {
            bean = queryById("SeekerLurekeyword", ExtraDataBean.class);
        }
        if (bean != null) {
            String[] strings = bean.extValue.split(",");
            return Arrays.asList(strings);
        }
        return new ArrayList<>(0);
    }

    public List<String> getBossHobbyList() {
        ExtraDataBean bean;
        bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//            if (bean == null) {
//                bean = queryById("labels", ExtraDataBean.class);
        if (bean == null) {

            String erroInfo = "getBossHobbyList";
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBossHobbyList")
                    .param("p2", erroInfo)
                    .report();

        }
//            }
//        } else {
//            bean = queryById("labels", ExtraDataBean.class);
//            if (bean == null) {
//                bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//            }
//        }
        if (bean != null) {
            try {
                JSONObject jsonObject = new JSONObject(bean.extValue);
                JSONArray ja = jsonObject.optJSONArray("hobby");
                if (ja != null) {
                    return GsonUtils.getGson().fromJson(ja.toString(),
                            new TypeToken<List<String>>() {
                            }.getType());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return new ArrayList<>(0);
    }


    // 获取客服电话
    public List<String> getHotLineCustomer() {
        tryLogFunction("getHotLineCustomer()");
        List<String> list = ExtraDataFactory.getInstance().getHotLineNumbers("customer");
        if (LList.isEmpty(list)) {
            list.add("400-065-5799");
        }
        return list;


    }

    // 获取老年人直连热线
    public List<String> getHotLineOldPerson() {
        tryLogFunction("getHotLineOldPerson()");
        List<String> list = ExtraDataFactory.getInstance().getHotLineNumbers("oldPeople");
        if (LList.isEmpty(list)) {
            list.add("400-661-6030");
        }
        return list;
    }

    // 朝阳人社局监督电话
    public List<String> getHotLinechaoyangSocial() {
        tryLogFunction("getHotLinechaoyangSocial()");
        List<String> list = ExtraDataFactory.getInstance().getHotLineNumbers("chaoyangSocial");
        if (LList.isEmpty(list)) {
            list.add("(010)57596212");
            list.add("(010)65099938");
        }
        return list;
    }

    // 推荐算法举报与未成年人举报
    public List<String> getHotLineRecommendAndMinority() {
        tryLogFunction("getHotLineRecommendAndMinority()");
        List<String> list = ExtraDataFactory.getInstance().getHotLineNumbers("recommendAndMinority");
        if (LList.isEmpty(list)) {
            list.add("400-065-5799");
        }
        return list;

    }

    public String beianName() {
        tryLogFunction("beianName()");
        String name = ExtraDataFactory.getInstance().getbeianInfo("name");
        if (TextUtils.isEmpty(name)) {
            return "京ICP备14013441号-23A";
        }
        return name;
    }

    public String beianUrl() {
        tryLogFunction("beianUrl()");
        String url = ExtraDataFactory.getInstance().getbeianInfo("url");
        if (TextUtils.isEmpty(url)) {
            return "https://beian.miit.gov.cn";
        }
        return url;
    }


    public List<String> getBossCharacterList() {

        ExtraDataBean bean;
        bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//            if (bean == null) {
//                bean = queryById("labels", ExtraDataBean.class);
        if (bean == null) {
            String erroInfo = "getBossCharacterList";
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBossCharacterList")
                    .param("p2", erroInfo)
                    .report();

        }
//            }
//        } else {
//            bean = queryById("labels", ExtraDataBean.class);
//            if (bean == null) {
//                bean = ExtraDataFactory.getInstance().getExtraObjectByKey("labels");
//            }
//        }
        if (bean != null) {
            try {
                JSONObject jsonObject = new JSONObject(bean.extValue);
                JSONArray ja = jsonObject.optJSONArray("character");
                if (ja != null) {
                    return GsonUtils.getGson().fromJson(ja.toString(),
                            new TypeToken<List<String>>() {
                            }.getType());
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return new ArrayList<>(0);
    }

    /**
     * 获取举报列表
     *
     * @return
     */
    public List<LevelBean> getReportList() {
        tryLogFunction("getReportList()");
        if (UserManager.getUserRole() == ROLE.BOSS) {
            return getBossReportList();
        } else {
            return getGeekReportList();
        }
    }

    /**
     * 获取牛人举报列表
     *
     * @return
     */
    public List<LevelBean> getGeekReportList() {
        tryLogFunction("getGeekReportList()");
        List<LevelBean> levelBeans = new ArrayList<>();
        levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("GeekReports");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("GeekReports");
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = getExtraDataLevelBeans("GeekReports");
        if (LList.isEmpty(levelBeans)) {

            String erroInfo = "getGeekReportList";
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getGeekReportList")
                    .param("p2", erroInfo)
                    .report();

        }
//            }
//        } else {
//            levelBeans = getExtraDataLevelBeans("GeekReports");
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("GeekReports");
//            }
//        }
        return levelBeans;
    }

    private List<LevelBean> getExtraDataLevelBeans(String key) {

        ExtraDataBean extraDataBean = queryById(key, ExtraDataBean.class);
        if (extraDataBean != null) {
            List<LevelBean> list = json2FilterBeans(extraDataBean.extValue);
            if (list != null) {
                return list;
            }
        }
        return new ArrayList<>(0);
    }

    /**
     * 获取牛人亮点标签列表
     *
     * @return
     */
    public List<LevelBean> getBossReportList() {
        tryLogFunction("getBossReportList()");
        List<LevelBean> levelBeans = new ArrayList<>();
        levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("BossReports");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("BossReports");
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = getExtraDataLevelBeans("BossReports");
        if (LList.isEmpty(levelBeans)) {
            String erroInfo = "getBossReportList";
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBossReportList")
                    .param("p2", erroInfo)
                    .report();
        }
//            }
//        } else {
//            levelBeans = getExtraDataLevelBeans("BossReports");
//            if (LList.isEmpty(levelBeans)) {
//                levelBeans = ExtraDataFactory.getInstance().getExtraDataLevelBeans("BossReports");
//            }
//        }
        return levelBeans;
    }

    public List<String> getInterviewCommentList(int code) {
        ExtraDataBean extraDataBean;
        if (UserManager.isBossRole()) {
            extraDataBean = queryById("BossInterviewCommentTag", ExtraDataBean.class);
        } else {
            extraDataBean = queryById("GeekInterviewCommentTag", ExtraDataBean.class);
        }
        List<String> data = null;
        if (extraDataBean != null) {
            try {
                JSONObject jsonObject = new JSONObject(extraDataBean.extValue);
                JSONArray ja = jsonObject.optJSONArray("label" + code);
                if (ja != null && ja.length() > 0) {
                    int size = ja.length();
                    data = new ArrayList<>(size);
                    for (int i = 0; i < size; i++) {
                        String value = ja.optString(i);
                        if (!TextUtils.isEmpty(value)) {
                            data.add(value);
                        }
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return data;
    }

    /**
     * 获得聊天页面 常用语缓存最大数量
     *
     * @return
     */
    public int chatCacheCommonWordMax() {
        tryLogFunction("chatCacheCommonWordMax()");
        if (chatCommonMaxCache == 0) {
            ExtraDataBean extraDataBean;
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("fastRepalySetting", ExtraDataBean.class);
            if (extraDataBean == null) {

                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "chatCacheCommonWordMax")
                        .param("p2", "read_mmkv_file_erro : chatCacheCommonWordMax")
                        .report();
            }
//                }
//            } else {
//                extraDataBean = queryById("fastRepalySetting", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//                }
//            }
            if (extraDataBean != null && !LText.empty(extraDataBean.extValue)) {
                try {
                    JSONObject jsonObject = new JSONObject(extraDataBean.extValue);
                    chatCommonMaxCache = jsonObject.optInt("mesNumToPrompt");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        if (chatCommonMaxCache == 0) {
            chatCommonMaxCache = 40;
        }
        return chatCommonMaxCache;
    }

    /**
     * 获得聊天页面 常用语缓存最大数量
     *
     * @return
     */
    public int chatCommonMaxSameWords() {
        tryLogFunction("chatCommonMaxSameWords()");
        if (chatCommonMaxSameWords == 0) {

            ExtraDataBean extraDataBean;
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("fastRepalySetting", ExtraDataBean.class);
            if (extraDataBean == null) {
                String erroInfo = "chatCommonMaxSameWords";
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "chatCommonMaxSameWords")
                        .param("p2", erroInfo)
                        .report();
            }
//                }
//            } else {
//                extraDataBean = queryById("fastRepalySetting", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("fastRepalySetting");
//                }
//            }
            if (extraDataBean != null && !LText.empty(extraDataBean.extValue)) {
                try {
                    JSONObject jsonObject = new JSONObject(extraDataBean.extValue);
                    chatCommonMaxSameWords = jsonObject.optInt("timesToPrompt");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        if (chatCommonMaxSameWords == 0) {
            chatCommonMaxSameWords = 3;
        }
        return chatCommonMaxSameWords;
    }

    public int getRedEnvelopeMinValue() {
        if (redEnvelopeMinValue == -1) {
            ExtraDataBean extraDataBean;
            if (readDataType == BASE_DATA_TYPE_MMKV) {
                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("redMin");
            } else {
                extraDataBean = queryById("redMin", ExtraDataBean.class);
            }
            if (extraDataBean != null) {
                redEnvelopeMinValue = Integer.parseInt(extraDataBean.extValue);
            }
            if (readDataType == BASE_DATA_TYPE_MMKV) {
                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("redMax");
            } else {
                extraDataBean = queryById("redMax", ExtraDataBean.class);
            }
            if (extraDataBean != null) {
                redEnvelopeMaxValue = Integer.parseInt(extraDataBean.extValue);
            }
        }
        if (redEnvelopeMinValue == -1) {
            redEnvelopeMinValue = 1;
        }
        return redEnvelopeMinValue;
    }

    public int getRedEnvelopeMaxValue() {
        if (redEnvelopeMaxValue == -1) {
            getRechargeMinValue();
        }
        if (redEnvelopeMaxValue == -1) {
            redEnvelopeMaxValue = 20000;
        }
        return redEnvelopeMaxValue;
    }

    public int getRechargeMinValue() {
        tryLogFunction("getRechargeMinValue()");
        if (rechargeMinValue == -1) {
            ExtraDataBean extraDataBean;
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("chargeMin");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("chargeMin");
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("chargeMin", ExtraDataBean.class);
            if (extraDataBean == null) {
                String erroInfo = "getRechargeMinValue";
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "getRechargeMinValue")
                        .param("p2", erroInfo)
                        .report();

            }
//                }
//            } else {
//                extraDataBean = queryById("chargeMin", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("chargeMin");
//                }
//            }
            if (extraDataBean != null) {
                rechargeMinValue = Integer.parseInt(extraDataBean.extValue);
            }
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("chargeMax");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("chargeMax", ExtraDataBean.class);
//                }
//            } else {
//                extraDataBean = queryById("chargeMax", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("chargeMax");
//                }
//            }
            if (extraDataBean != null) {
                rechargeMaxValue = Integer.parseInt(extraDataBean.extValue);
            }
        }
        if (rechargeMinValue == -1) {
            rechargeMinValue = 1;
        }
        return rechargeMinValue;
    }

    public int getRechargeMaxValue() {
        if (rechargeMaxValue == -1) {
            getRechargeMinValue();
        }
        if (rechargeMaxValue == -1) {
            rechargeMaxValue = 100000;
        }
        return rechargeMaxValue;
    }

    public int getWithdrawMinValue() {
        tryLogFunction("getWithdrawMinValue()");
        if (withdrawMinValue == -1) {
            ExtraDataBean extraDataBean;
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("withdrawMin");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("withdrawMin", ExtraDataBean.class);
            if (extraDataBean == null) {
                String erroInfo = "getWithdrawMinValue";
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "getWithdrawMinValue")
                        .param("p2", erroInfo)
                        .report();

            }
//                }
//            } else {
//                extraDataBean = queryById("withdrawMin", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("withdrawMin");
//                }
//            }
            if (extraDataBean != null) {
                withdrawMinValue = Integer.parseInt(extraDataBean.extValue);
            }
            extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("withdrawMax");
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//                if (extraDataBean == null) {
//                    extraDataBean = queryById("withdrawMax", ExtraDataBean.class);
            if (extraDataBean == null) {
                String erroInfo = "withdrawMax";
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "withdrawMax")
                        .param("p2", erroInfo)
                        .report();
            }
//                }
//            } else {
//                extraDataBean = queryById("withdrawMax", ExtraDataBean.class);
//                if (extraDataBean == null) {
//                    extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("withdrawMax");
//                }
//            }
            if (extraDataBean != null) {
                withdrawMaxValue = Integer.parseInt(extraDataBean.extValue);
            }
        }
        if (withdrawMinValue == -1) {
            withdrawMinValue = 10000;
        }
        return withdrawMinValue;
    }

    public int getWithdrawMaxValue() {
        tryLogFunction("getWithdrawMaxValue()");
        if (withdrawMaxValue == -1) {
            getWithdrawMinValue();
        }
        if (withdrawMaxValue == -1) {
            withdrawMaxValue = 1500000;
        }
        return withdrawMaxValue;
    }

    /**
     * 获取企业服务电话
     */
    public String getCompanyServiceTel() {
        tryLogFunction("getCompanyServiceTel()");
        String companyServiceTel = null;
        ExtraDataBean extraDataBean;
        extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("hotline");
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//            if (extraDataBean == null) {
//                extraDataBean = queryById("hotline", ExtraDataBean.class);
        if (extraDataBean == null) {
            String erroInfo = "getCompanyServiceTel";
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getCompanyServiceTel")
                    .param("p2", erroInfo)
                    .report();

        }
//
//            }
//        } else {
//            extraDataBean = queryById("hotline", ExtraDataBean.class);
//            if (extraDataBean == null) {
//                extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("hotline");
//            }
//        }
        if (extraDataBean != null) {
            companyServiceTel = extraDataBean.extValue;
        }

        return companyServiceTel;
    }

    /**
     * 获取网络客服电话
     */
    public String getCustomWebPhone() {
        tryLogFunction("getCustomWebPhone()");
        String customWebPhone = "";
        ExtraDataBean extraDataBean = ExtraDataFactory.getInstance().getExtraObjectByKey("customerPhone");
        if (extraDataBean != null) {
            customWebPhone = extraDataBean.extValue;
        }
        return customWebPhone;
    }

    public String getHotJobHint() {
        String string = null;
        ExtraDataBean extraDataBean = queryById("hotJobHint", ExtraDataBean.class);
        if (extraDataBean != null) {
            string = extraDataBean.extValue;
        }
        return string;
    }

    public String getHotJobServiceHint() {
        String string = null;
        ExtraDataBean extraDataBean = queryById("hotJobServiceHint", ExtraDataBean.class);
        if (extraDataBean != null) {
            string = extraDataBean.extValue;
        }
        return string;
    }

    /**
     * 获取排序后的城市列表 - B端
     * 热门城市 补了一个全国 用于清除
     *
     * @return
     */
    public List<LevelBean> getBSortCityWithClearList(boolean needLocationCity) {
        tryLogFunction("getBSortCityWithClearList()");
        List<LevelBean> sortCityList;
        if (mBSortCityWithClearListReference != null) {
            sortCityList = mBSortCityWithClearListReference.get();
            if (sortCityList != null && !sortCityList.isEmpty()) {
                return sortCityList;
            }
        }
        List<LevelBean> tempCityList;
        tempCityList = CityDataFactory.getInstance().getSortCityList(context);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//
//            if (LList.isEmpty(tempCityList)) {
//                tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
        if (LList.isEmpty(tempCityList)) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBSortCityWithClearList")
                    .report();

        }
//            }
//        } else {
//            tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
//            if (LList.isEmpty(tempCityList)) {
//                tempCityList = CityDataFactory.getInstance().getSortCityList();
//            }
//        }
        List<LevelBean> tempHotList = getHotCityList();
        LList.addElement(tempHotList, new LevelBean(0, "全国"), 0);
        sortCityList = BCitySortUtil.sortCityList(needLocationCity, tempCityList, tempHotList);
        mBSortCityWithClearListReference = new SoftReference(sortCityList);
        return sortCityList;
    }


    /**
     * 获取排序后的城市列表 - B端
     *
     * @return
     */
    public List<LevelBean> getBSortCityList(boolean needLocationCity) {
        tryLogFunction("getBSortCityList()");
        List<LevelBean> sortCityList;
        if (mBSortCityListReference != null) {
            sortCityList = mBSortCityListReference.get();
            if (sortCityList != null && !sortCityList.isEmpty()) {
                return sortCityList;
            }
        }
        List<LevelBean> tempCityList;
        tempCityList = CityDataFactory.getInstance().getSortCityList(context);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            tempCityList = CityDataFactory.getInstance().getSortCityList();
//            if (LList.isEmpty(tempCityList)) {
//                tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
        if (LList.isEmpty(tempCityList)) {

            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                    .param("p5", "getBSortCityList")
                    .report();
        }
//            }
//        } else {
//            tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
//            if (LList.isEmpty(tempCityList)) {
//                tempCityList = CityDataFactory.getInstance().getSortCityList();
//            }
//        }
        List<LevelBean> tempHotList = getHotCityList();
        sortCityList = BCitySortUtil.sortCityList(needLocationCity, tempCityList, tempHotList);
        mBSortCityListReference = new SoftReference(sortCityList);
        return sortCityList;
    }


    /**
     * 获取排序后的常规城市列表 - C端
     *
     * @return
     */

    @Nullable
    @WorkerThread
    public List<LevelBean> getCNormalCitySortList() {
        tryLogFunction("getCNormalCitySortList()");
        List<LevelBean> sortCityList;
        if (mCNormalSortCityListReference != null) {
            sortCityList = mCNormalSortCityListReference.get();
            if (sortCityList != null && !sortCityList.isEmpty()) {
                return sortCityList;
            }
        }

        List<LevelBean> cityList = new ArrayList<>();
        List<LevelBean> provinces = getCityList();
        if (provinces != null) {
            for (LevelBean bean : provinces) {   // 这是省级
                List<LevelBean> cities = bean.subLevelModeList;
                if (cities == null || cities.size() == 0) continue;

                // 这是市级
                cityList.addAll(cities);
            }
        }

        List<LevelBean> tempHotList = getHotCityList();

        sortCityList = GeekNormalCitySortUtil.sortCityList(cityList, tempHotList);
        mCNormalSortCityListReference = new SoftReference<>(sortCityList);

        return sortCityList;
    }


    /**
     * 获取排序后的常规城市列表 （包含县级市）- C端
     *
     * @return
     */
    @Nullable
    @WorkerThread
    public List<LevelBean> getSortCityList2() {
        tryLogFunction("getSortCityList2()");
        List<LevelBean> sortCityList;
        if (mSortCityListReference != null) {
            sortCityList = mSortCityListReference.get();
            if (sortCityList != null && !sortCityList.isEmpty()) {
                return sortCityList;
            }
        }

        List<LevelBean> cityList = new ArrayList<>();
        List<LevelBean> provinces = getCityList();
        if (provinces != null) {
            for (LevelBean bean : provinces) {   // 这是省级
                List<LevelBean> cities = bean.subLevelModeList;
                if (cities == null || cities.size() == 0) continue;

                // 这是市级
                cityList.addAll(cities);
            }
        }

        List<LevelBean> tempHotList = getHotCityList();

        sortCityList = CitySortUtil.sortCityList(cityList, tempHotList);
        mSortCityListReference = new SoftReference<>(sortCityList);

        return sortCityList;
    }

    /**
     * 获取正常排序后的城市列表
     *
     * @return
     */
    public List<LevelBean> getSorNormalCityList() {
        List<LevelBean> sortCityList;
        if (mSortNormalCityListReference != null) {
            sortCityList = mSortNormalCityListReference.get();
            if (sortCityList != null && !sortCityList.isEmpty()) {
                return sortCityList;
            }
        }
//        List<LevelBean> cityList = getCityList();//减少三级查询的使用
        List<LevelBean> tempCityList;
        if (readDataType == BASE_DATA_TYPE_MMKV) {
            tempCityList = CityDataFactory.getInstance().getSortCityList(context);
            if (LList.isEmpty(tempCityList)) {
                tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
                if (LList.isEmpty(tempCityList)) {

                    ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                            .param("p5", "getSorNormalCityList")
                            .report();

                }
            }
        } else {
            tempCityList = query(LevelBean.class, " parentId in (select id from LevelData where parentId = ?) order by firstChar", new Object[]{CITY_ID});
            if (LList.isEmpty(tempCityList)) {
                tempCityList = CityDataFactory.getInstance().getSortCityList(context);
            }
        }
//        for (LevelBean bean : cityList) {   // 这是省级
//            if (bean.subLevelModeList != null && bean.subLevelModeList.size() > 0) {
//                for (LevelBean beanSub : bean.subLevelModeList) {   // 这是市级
//                    if (beanSub != null)
//                        tempCityList.add(beanSub);
//                }
//            }
//        }
        Collections.sort(tempCityList, new Comparator<LevelBean>() {
            @Override
            public int compare(LevelBean o1, LevelBean o2) {
                return BasicDataCompareUtils.compare(o1, o2);
            }
        });
        mSortNormalCityListReference = new SoftReference(tempCityList);
        return tempCityList;
    }

    /**************************** 筛选条件 ********************************/

    @Nullable
    public LevelBean getCapital(@NonNull String provinceName) {
        tryLogFunction("getCapital()");
        LevelBean capital = null;
        LevelBean province = getProvinceByName(provinceName);
        List<LevelBean> cities = new ArrayList<>();

        if (province != null) {
            cities = CityDataFactory.getInstance().getCitiesById(province.code);
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                cities = CityDataFactory.getInstance().getCitiesById(province.code);
//                if (LList.isEmpty(cities)) {
//                    cities = getCitiesById(province.id);
//                }
//            } else {
//                cities = getCitiesById(province.id);
//                if (province == null) {
//                    cities = CityDataFactory.getInstance().getCitiesById(province.code);
//                }
//            }
            if (cities != null) {
                for (LevelBean levelBean : cities) {
                    if (levelBean.cityType == 0 && levelBean.capital == 1) {
                        capital = levelBean;
                        break;
                    }
                }
            }
        }
        return capital;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @return
     */
    public String getCityCode(String name) {
        tryLogFunction("getCityCode()");
        if (LText.empty(name)) return null;
        String code;
        String tempName = name;

        name = handleAmapCityName(name);
        code = CityDataFactory.getInstance().getCityCode(context, name);
        if ("0".equals(code)) {
            ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                    .param("p5", "getCityCode")
                    .param("p2", name)
                    .param("p3", tempName)
                    .report();
        }
        return code;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @return
     */
    public String getCityCode2(String name) {
        tryLogFunction("getCityCode2()");
        if (LText.empty(name)) return null;
        String code;

        name = handleAmapCityName(name);
        code = CityDataFactory.getInstance().getCityCode(context, name);
        return code;
    }

    private final static List<String> county_level_city = Arrays.asList("澄迈县", "临高县", "屯昌县", "定安县");
    private final static List<String> are_level_city = Arrays.asList("大堂区", "沙田区", "油尖旺区", "深水埗区", "元朗区", "中西区", "湾仔区", "九龙城区", "黄大仙区", "观塘区", "荃湾区", "屯门区");
    private final static List<String> district_city = Arrays.asList("香港特别行政区", "澳门特别行政区");
    private final static List<String> forest_are_city = Arrays.asList("神农架林区");
    private final static List<String> special_city = Arrays.asList("台湾省");
    //台湾
    private final static List<String> special_are_level_city = Arrays.asList("苗栗县", "新竹县","彰化县","南投县","云林县","嘉义县","屏东县","台东县","花莲县","宜兰县","澎湖县","金门县","连江县");

    /**
     * 处理高德返回的城市名
     * 过滤特殊城市
     */

    public String handleAmapCityName(String name) {

        if ((LText.empty(name))) {
            return null;
        }

        if(special_city.contains(name)){ // 去掉'省'字
            name = name.substring(0, name.length() - 1); // 去掉'省'字
        }

        if (name.endsWith("省") || name.endsWith("地区") || name.endsWith("自治州")
                || name.endsWith("旗") || name.endsWith("盟") || name.endsWith("特区")) {
            return name;
        }

        if (forest_are_city.contains(name)){ // 去掉'林区'字
            name = name.substring(0, name.length() - 2); // 去掉'林区'字
        }

        if (county_level_city.contains(name)) {
            name = name.substring(0, name.length() - 1); // 去掉'县'字
        }

        if (district_city.contains(name)) {
            name = name.substring(0, name.length() - 5); // 去掉'特别行政区'字
        }

        // 这种主要是处理有些带"市"有些不带"市"
        if (!name.contains("市")) {
            name += "市";
            return name;
        }
        return name;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @param list
     * @return
     */
    public String getCityCode(String name, List<LevelBean> list) {
        tryLogFunction("getCityCode()");
        if (list == null || list.size() <= 0) return "0";
        String code = "0";
        for (LevelBean bean : list) {
            String cityName = bean.name;
            if (LText.empty(cityName)) continue;
            cityName = handleAmapCityName(cityName);
            if (LText.equal(cityName, name)) {
                code = String.valueOf(bean.code);
            }
        }
        return code;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @return
     */
    public String getSubCityCode(String name, String subName, int type) {
        tryLogFunction("getSubCityCode()");
        if (LText.empty(name) || LText.empty(subName)) return null;

        String code;
        String tempName = name;

        name = handleAmapCityName(name);
        code = CityDataFactory.getInstance().getSubCityCode(context, name, subName, type);
//        if (readDataType == BASE_DATA_TYPE_MMKV) {
//            code = CityDataFactory.getInstance().getSubCityCode(name, subName, type);
//            if (TextUtils.isEmpty(code) || "0".equals(code)) {
//                List<LevelBean> levelBeans = query(LevelBean.class, "name like ? and parentId in (select id from LevelData where parentId = ?)", new Object[]{tempName, CITY_ID});
//                code = getSubCityCode(name, subName, levelBeans, type);
//                if ("0".equals(code)) {//这里基本上不会走到
//                    levelBeans = queryCascade(LevelBean.class, CITY_ID);
//                    code = getSubCityCode(name, subName, levelBeans, type);
//                    if (!"0".equals(code)) {
//
//                        String erroInfo = "getSubCityCode" + subName;
//
//                        ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
//                                .param("p5", "getSubCityCode")
//                                .param("p2", erroInfo)
//                                .report();
//
//                    }
//                }
//            }
//        } else {
//            List<LevelBean> levelBeans = query(LevelBean.class, "name like ? and parentId in (select id from LevelData where parentId = ?)", new Object[]{tempName, CITY_ID});
//            code = getSubCityCode(name, subName, levelBeans, type);
//            if ("0".equals(code)) {//这里基本上不会走到
//                levelBeans = queryCascade(LevelBean.class, CITY_ID);
//                code = getSubCityCode(name, subName, levelBeans, type);
//            }
//        }

        if (!TextUtils.isEmpty(name) && !TextUtils.isEmpty(subName) && !name.contains("自治州")) {
            // 查询不到县级市
            if ("0".equals(code) && type == 3 && !subName.contains("区") && !subName.contains("县")) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                        .param("p5", "getSubCityCode县级市")
                        .param("p2", name)
                        .param("p3", subName)
                        .param("p4", String.valueOf(type))
                        .param("p6", tempName)
                        .report();
            }

            // 查询不到县城
            if ("0".equals(code) && type == 2 && !subName.contains("区") && !subName.contains("市") && !name.contains("自治州") && subName.contains("县")) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_AMAP_NOTMACH)
                        .param("p5", "getSubCityCode县城")
                        .param("p2", name)
                        .param("p3", subName)
                        .param("p4", String.valueOf(type))
                        .param("p6", tempName)
                        .report();
            }
        }

        return code;
    }

    /**
     * 获取本地城市code
     *
     * @param name
     * @param list
     * @return
     */
    public String getSubCityCode(String name, String subName, List<LevelBean> list, int type) {
        if (list == null || list.size() <= 0) return "0";
        String code = "0";
        for (LevelBean bean : list) {
            String cityName = bean.name;
            if (LText.empty(cityName)) continue;

            cityName = handleAmapCityName(cityName);

            if (LText.equal(cityName, name)) {
                if (!LList.isEmpty(bean.subLevelModeList)) {
                    for (LevelBean subBean : bean.subLevelModeList) {
                        String subCityName = subBean.name;
                        if (LText.empty(subCityName)) continue;
                        if ((subCityName.contains(subName) || subName.contains(subCityName)) && subBean.cityType == type) {
                            code = String.valueOf(subBean.code);
                        }
                    }
                }
            }
        }
        return code;
    }

    /**
     * 在LevelBean中使用Code获取名称
     *
     * @param code
     * @param list
     * @return
     */
    private String useLevelBeanCodeGetName(String code, List<LevelBean> list) {
        if (list == null || list.size() <= 0) return "";
        for (LevelBean bean : list) {
            String cityCode = bean.code + "";
            String result = null;
            if (LText.equal(cityCode, code)) {
                result = bean.name;
            }
            if (result == null && LList.getCount(bean.subLevelModeList) > 0) {
                result = useLevelBeanCodeGetName(code, bean.subLevelModeList);
            }
            if (!LText.empty(result)) {
                return result;
            }
        }
        return null;
    }

    public List<FilterBean> getGeekFilterList() {
        tryLogFunction("getGeekFilterList()");
        List<FilterBean> geekFilterList = new ArrayList<>();
        if (geekFilterListReference != null) {
            geekFilterList = geekFilterListReference.get();
        }
        if (geekFilterList == null || geekFilterList.size() <= 0) {
            geekFilterList = GeekFilterDataFactory.getInstance().getGeekFilterList();
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                geekFilterList = GeekFilterDataFactory.getInstance().getGeekFilterList();
//                if (LList.isEmpty(geekFilterList)) {
//                    geekFilterList = queryCascade(FilterBean.class, GEEK_FILTER_ID);
            if (LList.isEmpty(geekFilterList)) {

                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "getGeekFilterList")
                        .report();

            }
//                }
//            } else {
//                geekFilterList = queryCascade(FilterBean.class, GEEK_FILTER_ID);
//                if (LList.isEmpty(geekFilterList)) {
//                    geekFilterList = GeekFilterDataFactory.getInstance().getGeekFilterList();
//                }//App.get().db().queryCascade(new QueryBuilder(FilterBean.class).where("parentId = ? ", new Object[]{GEEK_FILTER_ID}));
//            }
        }

        if (geekFilterList == null || geekFilterList.isEmpty()) {

        } else {
            geekFilterListReference = new SoftReference<List<FilterBean>>(geekFilterList);
        }
        return geekFilterList;
    }

    public List<LevelBean> getGeekFilterSalaryList() {
        tryLogFunction("getGeekFilterSalaryList()");
        List<FilterBean> filterList = VersionAndDatasCommon.getInstance().getGeekFilterList();

        if (LList.isEmpty(filterList)) {
            return null;
        } else {
            List<LevelBean> salaryList = new ArrayList<>();
            for (FilterBean filterBean : filterList) {
                if (filterBean.code == 400) {
                    if (!LList.isEmpty(filterBean.subFilterConfigModel)) {
                        for (FilterBean child : filterBean.subFilterConfigModel) {
                            LevelBean levelBean = new LevelBean(child.code, child.name);
                            salaryList.add(levelBean);
                        }
                    }
                    break;
                }
            }
            return salaryList;
        }
    }

    public List<LevelBean> getGeekFilterScaleList() {
        tryLogFunction("getGeekFilterScaleList()");
        List<FilterBean> filterList = VersionAndDatasCommon.getInstance().getGeekFilterList();

        if (LList.isEmpty(filterList)) {
            return null;
        } else {
            List<LevelBean> salaryList = new ArrayList<>();
            for (FilterBean filterBean : filterList) {
                if (filterBean.code == 300) {
                    if (!LList.isEmpty(filterBean.subFilterConfigModel)) {
                        for (FilterBean child : filterBean.subFilterConfigModel) {
                            LevelBean levelBean = new LevelBean(child.code, child.name);
                            salaryList.add(levelBean);
                        }
                    }
                    break;
                }
            }
            return salaryList;
        }
    }

    public List<LevelBean> getGeekFilterExperienceList() {
        tryLogFunction("getGeekFilterExperienceList()");
        List<FilterBean> filterList = VersionAndDatasCommon.getInstance().getGeekFilterList();

        if (LList.isEmpty(filterList)) {
            return null;
        } else {
            List<LevelBean> salaryList = new ArrayList<>();
            for (FilterBean filterBean : filterList) {
                if (filterBean.code == 100) {
                    if (!LList.isEmpty(filterBean.subFilterConfigModel)) {
                        for (FilterBean child : filterBean.subFilterConfigModel) {
                            LevelBean levelBean = new LevelBean(child.code, child.name);
                            salaryList.add(levelBean);
                        }
                    }
                    break;
                }
            }
            return salaryList;
        }
    }

    public List<FilterBean> getBossFilterList() {
        tryLogFunction("getBossFilterList()");
        List<FilterBean> bossFilterList = null;
        if (bossFilterListReference != null) {
            bossFilterList = bossFilterListReference.get();
        }
        if (bossFilterList == null || bossFilterList.size() <= 0) {
            bossFilterList = BossFilterDataFactory.getInstance().getBossFilterList();
//            if (readDataType == BASE_DATA_TYPE_MMKV) {
//                bossFilterList = BossFilterDataFactory.getInstance().getBossFilterList();
//                if (LList.isEmpty(bossFilterList)) {
//                    bossFilterList = queryCascade(FilterBean.class, BOSS_FILTER_ID);//App.get().db().queryCascade(new QueryBuilder(FilterBean.class).where("parentId = ? ", new Object[]{BOSS_FILTER_ID}));
            if (LList.isEmpty(bossFilterList)) {
                ApmAnalyzer.create().action(ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_EXCEPTION_MMKV_NO_DATA)
                        .param("p5", "getBossFilterList")
                        .report();

            }

//            } else {
//                bossFilterList = queryCascade(FilterBean.class, BOSS_FILTER_ID);//App.get().db().queryCascade(new QueryBuilder(FilterBean.class).where("parentId = ? ", new Object[]{BOSS_FILTER_ID}));
//                if (LList.isEmpty(bossFilterList)) {
//                    bossFilterList = BossFilterDataFactory.getInstance().getBossFilterList();
//                }
//            }
        }
        if (bossFilterList == null || bossFilterList.isEmpty()) {
        } else {
            bossFilterListReference = new SoftReference<List<FilterBean>>(bossFilterList);
        }
        return bossFilterList;
    }

    /**
     * 获取猎头职类列表
     * 二级职类且不包含「其他」为前缀的职类
     */
    public List<LevelBean> getHunterPositionList() {
        tryLogFunction("getHunterPositionList()");
        List<LevelBean> positionList = new ArrayList<>();
        if (hunterPositionListReference != null) {
            positionList = hunterPositionListReference.get();
            if (LList.getCount(positionList) > 0) {
                return positionList;
            }
        }
        // 原始的所有职类列表
        List<LevelBean> originList = getPositionList();
        if (LList.isEmpty(originList)) {
            return positionList;
        }
        for (LevelBean firstLevel : originList) {
            String filterPrefix = context.getString(R.string.string_others);
            if (firstLevel.code == 0 || firstLevel.name.contains(filterPrefix)) continue;
            List<LevelBean> secondLevels = new ArrayList<>();
            for (LevelBean secondLevel : firstLevel.subLevelModeList) {
                if (secondLevel.code == 0 || secondLevel.name.contains(filterPrefix)) continue;
                secondLevels.add(secondLevel);
            }
            if (LList.hasElement(secondLevels)) {
                LevelBean positionFirstLevel = new LevelBean(firstLevel.code, firstLevel.name);
                positionFirstLevel.subLevelModeList = secondLevels;
                positionList.add(positionFirstLevel);
            }
        }
        if (LList.isNotEmpty(positionList)) {
            hunterPositionListReference = new SoftReference<>(positionList);
        } else {
            TLog.print(TAG, "hunter position list is empty");
        }
        return positionList;
    }

    /**
     * 1313.92372:【基础数据】基础数据业务数据获取方法调用统计
     *
     * @param functionName 业务数据获取方法名称
     */
    private void tryLogFunction(@NonNull String functionName) {
        try {
            BasicDataLog.tryLogFunctionAndPageInfo(functionName, ForegroundUtils.get().getTopActivity(), UserManager.getUserRole().name());
        } catch (Exception e) {
            BasicDataLog.logError(e.getMessage());
        }
    }
}
