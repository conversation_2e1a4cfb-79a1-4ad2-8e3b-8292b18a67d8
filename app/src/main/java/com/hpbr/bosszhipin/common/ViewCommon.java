package com.hpbr.bosszhipin.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.view.SimpleDraweeView;
import com.fresco.lib.FrescoUtils;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.company.export.Company;
import com.hpbr.bosszhipin.company.export.CompanyConsts;
import com.hpbr.bosszhipin.company.export.CompanyRouter;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.block.utils.FakeBoldStyle;
import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteIndexBean;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.AxisBean;
import com.hpbr.bosszhipin.module.my.activity.boss.brand.bean.BrandInfoBean;
import com.hpbr.bosszhipin.module.my.activity.homepage.HomePageNotOpenActivity;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BackGroundSpanWithoutLineSpacing;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ZPUIAvatarDecorationView;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.bean.ServerHighlightIndexBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;

import java.util.List;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;
import zpui.lib.ui.statelayout.layout.LoadingSceneLayout;

/**
 * Created by zhouyou on 15/4/7.
 */
public class ViewCommon {
    public static final String TAG ="ViewCommon";

    private final static int[] TEMP_LOC = {0, 0};

    /**
     * 设置用户头像
     *
     * @param imageView   需要设置头像的View，必须是SimpleDrawweeView
     * @param avatarIndex 用户的靓图下标index
     * @param avatarUrl   用户的头像URL
     */
    public static void setAvatar(SimpleDraweeView imageView, int avatarIndex, String avatarUrl) {
        if (imageView == null) return;
        if (TextUtils.isEmpty(avatarUrl) && (avatarIndex > 0 && avatarIndex < 17)) {
            int resId = getDefaultAvatarId(avatarIndex);
            imageView.setImageURI(StringUtil.getResouceUri(resId));
        } else {
            FrescoUtils.loadGifFromNetwork(imageView, StringUtil.getNetworkUri(avatarUrl), false);
        }
    }

    /**
     * 设置用户头像 带挂件
     *
     * @param imageView
     * @param avatarIndex
     * @param avatarUrl
     * @param pendantUrl
     */
    public static void setAvatarWithPendant(ZPUIAvatarDecorationView imageView, int avatarIndex, String avatarUrl, String pendantUrl) {
        if (imageView == null) return;
        if (TextUtils.isEmpty(avatarUrl) && (avatarIndex > 0 && avatarIndex < 17)) {
            int resId = getDefaultAvatarId(avatarIndex);
            imageView.setAvatar(StringUtil.getResouceUri(resId));
        } else if (TextUtils.isEmpty(avatarUrl)) {
            imageView.setAvatar(StringUtil.getResouceUri(R.mipmap.ic_head_photo_default));
        } else {
            FrescoUtils.loadGifFromNetwork(imageView.getIvAvatar(), StringUtil.getNetworkUri(avatarUrl), false);
        }
        if (!TextUtils.isEmpty(pendantUrl)) {
            imageView.setDecoration(pendantUrl);
        }
    }

    /**
     * 设置用户头像，并展示为圆形，开启抗锯齿效果，防止图片失真变形
     */
    public static void setRoundAvatar(SimpleDraweeView sdvAvatar, int avatarIndex, @Nullable String avatarUrl) {
        if (sdvAvatar == null || sdvAvatar.getHierarchy() == null) {
            return;
        }
        setSdvRound(sdvAvatar);
        setAvatar(sdvAvatar, avatarIndex, avatarUrl);
    }

    /**
     * 动态设置SimpleDraweeView展示为圆形，并开启抗锯齿效果，防止图片变形失真
     */
    public static void setSdvRound(SimpleDraweeView sdvAvatar) {
        if (sdvAvatar == null || sdvAvatar.getHierarchy() == null) {
            return;
        }
        RoundingParams roundingParams = sdvAvatar.getHierarchy().getRoundingParams() == null ? RoundingParams.asCircle() : sdvAvatar.getHierarchy().getRoundingParams();
        roundingParams.setPaintFilterBitmap(true);
        sdvAvatar.getHierarchy().setRoundingParams(roundingParams);
    }

    public static void setFakeBoldText(TextView textView, boolean isFakeBold) {
        TextPaint textViewPaint = textView.getPaint();
        if (textViewPaint != null) {
            textViewPaint.setFakeBoldText(isFakeBold);
        }
    }

    /**
     * 根据EditText所在坐标和用户点击的坐标相对比，来判断是否隐藏键盘，因为当用户点击EditText时没必要隐藏
     *
     * @param v
     * @param event
     * @return
     */
    public static boolean isShouldHideInput(View v, MotionEvent event) {
        if ((v instanceof EditText)) {
            v.getLocationInWindow(TEMP_LOC);
            int left = TEMP_LOC[0], top = TEMP_LOC[1], bottom = top + v.getHeight(), right = left
                    + v.getWidth();
            return !(event.getX() > left && event.getX() < right
                    && event.getY() > top && event.getY() < bottom);
        }
        // 如果焦点不是EditText则忽略，这个发生在视图刚绘制完，第一个焦点不在EditView上，和用户用轨迹球选择其他的焦点
        return false;
    }

    /**
     * 任务列表设置浮层使用
     * 设置控件所在的位置YY，并且不改变宽高，
     * XY为绝对位置
     */
    public static void setGuideLayerLayout(View view, int x, int y, int w, int h) {
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) view.getLayoutParams();
        params.leftMargin = x;
        params.topMargin = y;
        params.width = w;
        params.height = h;
        view.setLayoutParams(params);
    }

    public static void setGuideLayerLayoutDes(View view, int x, int y, int w, int h) {
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) view.getLayoutParams();
        params.leftMargin = x - 2 * w;
        params.topMargin = y - (int) (h * 2.5);
        params.width = w * 3;
        params.height = (int) (h * 2.5);
        view.setLayoutParams(params);
    }

    public static SpannableStringBuilder setHighlightedFormatter(String text, List<AutoCompleteIndexBean> indexList, int highlightedColor) {
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        int size = indexList.size();
        int startIndex;
        int endIndex;
        for (int i = 0; i < size; i++) {
            AutoCompleteIndexBean bean = indexList.get(i);
            if (bean == null) continue;
            startIndex = bean.startIdx;
            endIndex = bean.endIdx;
            if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
            builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return builder;
    }

    @Nullable
    public static SpannableStringBuilder getHighlightText(@Nullable String content, @Nullable String highlightString, @ColorInt int highlightedColor) {
        if (LText.empty(content)) return null;
        if (LText.empty(highlightString)) {
            return new SpannableStringBuilder(content);
        }
        SpannableStringBuilder builder = new SpannableStringBuilder(content);
        int length = LText.len(content);
        int startIndex = LText.getStringIndex(content, highlightString);
        int endIndex = startIndex + LText.len(highlightString);
        if (startIndex >= 0 && endIndex > startIndex && endIndex <= length) {
            builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return builder;
    }

    @NonNull
    public static SpannableStringBuilder getCertainTextHighlighted(@Nullable String content, @Nullable String highlightString, @ColorInt int highlightedColor) {
        if (LText.empty(content)) {
            return new SpannableStringBuilder();
        } else if (LText.empty(highlightString)) {
            return new SpannableStringBuilder(content);
        }

        SpannableStringBuilder builder = new SpannableStringBuilder(content);
        int startIndex = 0;
        int highlightLength = highlightString.length();

        while (startIndex <= content.length() - highlightLength) {
            int index = content.indexOf(highlightString, startIndex);
            if (index == -1) break;

            int endIndex = index + highlightLength;

            // 应用高亮颜色
            builder.setSpan(
                    new ForegroundColorSpan(highlightedColor),
                    index,
                    endIndex,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            );

            startIndex = endIndex;
        }

        return builder;
    }



    public static SpannableStringBuilder setTextHighLight(String text, List<ServerHighlightListBean> indexList, int highlightedColor) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (indexList != null) {
            int size = indexList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightListBean bean = indexList.get(i);
                if (bean == null) continue;
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    public static SpannableStringBuilder setTextHighLightWithClick(Context context,String text, List<ServerHighlightListBean> highlightList, int highlightedColor) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (highlightList != null) {
            int size = highlightList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightListBean bean = highlightList.get(i);
                if (bean == null) continue;
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                builder.setSpan(new ClickableSpan() {
                    @Override
                    public void onClick(@NonNull View widget) {
                        String protocol =bean.subUrl;
                        if (TextUtils.isEmpty(protocol)){
                            protocol =bean.url;
                        }
                        new ZPManager(context,protocol).handler();
                    }

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        super.updateDrawState(ds);
                        ds.setColor(ContextCompat.getColor(context, R.color.app_green_dark));
                        ds.setUnderlineText(false);
                    }
                }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    @NonNull
    public static SpannableStringBuilder setTextHighLightWithClick(@Nullable String text, @Nullable List<ServerHighlightListBean> highlightList, int highlightedColor, @Nullable OnClickableBeanClickListener listener) {
        if (LText.empty(text)) {
            return new SpannableStringBuilder();
        }
        int length = LText.len(text);
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (highlightList == null || LList.isEmpty(highlightList)) {
            return builder;
        }
        int startIndex;
        int endIndex;
        for (ServerHighlightListBean bean : highlightList) {
            if (bean == null) {
                continue;
            }
            startIndex = bean.startIndex;
            endIndex = bean.endIndex;
            if (isSpanIndexInvalid(startIndex, endIndex, length)) {
                continue;
            }
            ClickableSpan span = new ClickableSpan() {
                @Override
                public void onClick(@NonNull View widget) {
                    if (listener != null) {
                        listener.onClick(bean);
                    }
                }

                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    ds.setColor(highlightedColor);
                    ds.setUnderlineText(false);
                }
            };
            builder.setSpan(span, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return builder;
    }

    @NonNull
    public static SpannableStringBuilder setTextHighLightWithClick(@Nullable String text, @Nullable String highlightString, int highlightedColor, @Nullable View.OnClickListener listener) {
        if (LText.empty(text)) {
            return new SpannableStringBuilder();
        } else if (LText.empty(highlightString)) {
            return new SpannableStringBuilder(text);
        }
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        int startIndex = LText.getStringIndex(text, highlightString);
        int endIndex = startIndex + LText.len(highlightString);
        int length = LText.len(text);
        if (isSpanIndexInvalid(startIndex, endIndex, length)) {
            return new SpannableStringBuilder(text);
        }
        ClickableSpan span = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                if (listener != null) {
                    listener.onClick(widget);
                }
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(highlightedColor);
                ds.setUnderlineText(false);
            }
        };
        builder.setSpan(span, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return builder;
    }


    public static SpannableStringBuilder getExchangedText(List<ServerHighlightListBean> list, String text, Context context, int defColor) {
        if (TextUtils.isEmpty(text)) {
            return new SpannableStringBuilder("");
        }
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(text);
        if (list == null || list.size() == 0) {
            return spannableStringBuilder;
        }
        try {
            for (ServerHighlightListBean offsets : list) {
                int start = offsets.startIndex;
                int end = text.length();
                if (offsets.endIndex < text.length()) {//服务端返回的结束位置如果比整个字符串的长度还大的话
                    end = offsets.endIndex;
                }
                if (start == end) {
                    break;
                }
                if (!TextUtils.isEmpty(offsets.url)) {
                    spannableStringBuilder.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View view) {
                            if (ActivityUtils.isInvalid(context)) {
                                T.ss("无效跳转，activity null");
                            } else {
                                String protocol = offsets.subUrl;
                                if (TextUtils.isEmpty(protocol)) {
                                    protocol = offsets.url;
                                }
                                new ZPManager(context, protocol).handler();
                            }
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint paint) {
                            int colorInt = defColor;
                            if (!TextUtils.isEmpty(offsets.color)) {
                                colorInt = offsets.getColorOfInt(defColor);
                            }
                            paint.setColor(colorInt);
                        }
                    }, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                } else {
                    int colorInt = defColor;
                    if (!TextUtils.isEmpty(offsets.color)) {
                        colorInt = offsets.getColorOfInt(defColor);
                    }
                    ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(colorInt);
                    spannableStringBuilder.setSpan(foregroundColorSpan, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
            }
            return spannableStringBuilder;
        } catch (Exception e) {
            //防止发生不可预测的异常 充分保证 哪怕不显示颜色 也要把正常的文案显示出来
            return spannableStringBuilder;
        }
    }




    /**
     * 服务返回颜色
     */
    public static SpannableStringBuilder setTextHighLightWithClickByDefColor(Context context,String text, List<ServerHighlightListBean> highlightList, int defColor) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        try{
            if (highlightList != null) {
                int size = highlightList.size();
                int startIndex;
                int endIndex;
                for (int i = 0; i < size; i++) {
                    ServerHighlightListBean bean = highlightList.get(i);
                    if (bean == null) continue;
                    startIndex = bean.startIndex;
                    endIndex = bean.endIndex;
                    if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                    int  colorInt =defColor;
                    if (!TextUtils.isEmpty(bean.color)){
                        colorInt =bean.getColorOfInt(defColor);
                    }

                    builder.setSpan(new ForegroundColorSpan(colorInt), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    builder.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            String protocol =bean.subUrl;
                            if (TextUtils.isEmpty(protocol)){
                                protocol =bean.url;
                            }
                            new ZPManager(context,protocol).handler();
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            super.updateDrawState(ds);
                            int  colorInt =defColor;
                            if (!TextUtils.isEmpty(bean.color)){
                                colorInt =bean.getColorOfInt(defColor);
                            }
                            ds.setColor(colorInt);
                            ds.setUnderlineText(false);
                            if (bean.bold){
                                ds.setFakeBoldText(true); // 设置加粗
                            }

                        }
                    }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    if (bean.bold){
                        StyleSpan boldSpan = new StyleSpan(android.graphics.Typeface.BOLD);
                        builder.setSpan(boldSpan, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                    }
                }
            }
        }catch(Exception e){
            TLog.info(TAG,e.toString());
        }

        return builder;
    }

    public static SpannableStringBuilder setTextBGHighLight(String text, float lineSpacing, List<ServerHighlightListBean> indexList, int highlightedColor) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (indexList != null) {
            int size = indexList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightListBean bean = indexList.get(i);
                if (bean == null) continue;
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                builder.setSpan(new BackGroundSpanWithoutLineSpacing(highlightedColor), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    public static SpannableStringBuilder getHighlightedTextBuilder(CharSequence text, List<ServerHighlightIndexBean> indexList, int highlightedColor) {
        return getHighlightedTextBuilder(text, indexList, highlightedColor, false);
    }

    public static SpannableStringBuilder getHighlightedTextBuilder(CharSequence text, List<ServerHighlightIndexBean> indexList, int highlightedColor, boolean isFakeBold) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (indexList != null) {
            int size = indexList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightIndexBean bean = indexList.get(i);
                if (bean == null) continue;
                startIndex = bean.start;
                endIndex = bean.end;
                if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                if (isFakeBold) {
                    builder.setSpan(new FakeBoldStyle(Typeface.NORMAL), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }
        return builder;
    }

    public static SpannableStringBuilder getFakeBoldText(CharSequence text, List<ServerHighlightListBean> indexList) {
        if (TextUtils.isEmpty(text)) return null;
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (indexList != null) {
            int size = indexList.size();
            int startIndex;
            int endIndex;
            for (int i = 0; i < size; i++) {
                ServerHighlightListBean bean = indexList.get(i);
                if (bean == null) continue;
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                if (isSpanIndexInvalid(startIndex, endIndex, length)) continue;
                builder.setSpan(new FakeBoldStyle(Typeface.NORMAL), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    @NonNull
    public static SpannableStringBuilder getFakeBoldHighlightText(@Nullable CharSequence text, @Nullable List<ServerHighlightListBean> indexList, int highlightedColor) {
        if (TextUtils.isEmpty(text)) {
            return new SpannableStringBuilder();
        }
        int length = text.length();
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        if (LList.isNotEmpty(indexList)) {
            int startIndex;
            int endIndex;
            for (int i = 0; i < LList.getCount(indexList); i++) {
                ServerHighlightListBean bean = LList.getElement(indexList, i);
                if (bean == null || isSpanIndexInvalid(bean.startIndex, bean.endIndex, length)) {
                    continue;
                }
                startIndex = bean.startIndex;
                endIndex = bean.endIndex;
                builder.setSpan(new FakeBoldStyle(Typeface.NORMAL), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                builder.setSpan(new ForegroundColorSpan(highlightedColor), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return builder;
    }

    /**
     * 获得头像的相对坐标
     *
     * @param activity
     * @param view
     * @return
     */
    public static AxisBean getAvatarAxis(Activity activity, View view) {
        if (activity == null || activity.isFinishing() || view == null) return null;
        int[] xyAxis = new int[2];
        view.getLocationOnScreen(xyAxis);
        int x = xyAxis[0];
        int y = xyAxis[1];
        Rect frame = new Rect();
        activity.getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        int statusBarHeight = frame.top;
        return new AxisBean(x, y - statusBarHeight, view.getMeasuredWidth(), view.getMeasuredHeight());
    }

    /**
     * Get view location in window.
     *
     * @param sharedView The view.
     * @return AxisBean of view specs.
     */
    public static AxisBean getSharedViewSpecsInWindow(@NonNull View sharedView) {
        int[] outLocation = new int[2];
        sharedView.getLocationInWindow(outLocation);
        return new AxisBean(outLocation[0], outLocation[1], sharedView.getMeasuredWidth(), sharedView.getMeasuredHeight());
    }

    /**
     * 给手机号设置隐私
     *
     * @param phone
     */
    public static String keepPhoneSecret(@Nullable String phone) {
        if (phone == null) return "";

        int length = phone.length();
        switch (length) {
            case 11:
                phone = phone.substring(0, 3) + " ****** " + phone.substring(9);
                break;
            case 10:
                phone = phone.substring(0, 3) + " ***** " + phone.substring(8);
                break;
            case 9:
                phone = phone.substring(0, 3) + " **** " + phone.substring(7);
                break;
            case 8:
                phone = phone.substring(0, 3) + " *** " + phone.substring(6);
                break;
            case 7:
                phone = phone.substring(0, 3) + " ** " + phone.substring(5);
                break;
            case 6:
                phone = phone.substring(0, 3) + " * " + phone.substring(4);
                break;
            default:
                break;
        }
        return phone;
    }

    /**
     * 打开公司主页
     *
     * @param activity
     */
    public static void openHomePage(Activity activity) {
        UserBean loginUser = UserManager.getLoginUser();
        BossInfoBean bossInfo = loginUser != null ? loginUser.bossInfo : null;
        if (bossInfo == null) return;
        Intent intent;
        if (UserManager.isBasicInfoCompleteBoss(loginUser)
                && bossInfo.certification == 3
                && LList.getElement(bossInfo.brandList, 0) != null) {
            BrandInfoBean brandInfoBean = bossInfo.brandList.get(0);
            ViewCommon.jumpToEditCompany(activity, brandInfoBean, bossInfo.companyActiveUrl);
        } else {
            intent = new Intent(activity, HomePageNotOpenActivity.class);
            AppUtil.startActivity(activity, intent);
        }
    }

    /**
     * 跳转到编辑公司页面
     *
     * @param activity
     * @param brandInfoBean 品牌信息
     * @param url           编辑公司url
     */
    public static void jumpToEditCompany(Activity activity, BrandInfoBean brandInfoBean, String url) {
        if (brandInfoBean.isDecorateComplete) {
            Company.builder()
                    .setFrom(CompanyConsts.From.FROM_NONE)
                    .build(activity)
                    .jumpToEdit();

        } else {
            CompanyRouter.jumpToEditCompany(activity);
        }
    }

    /**
     * 获取默认头像的资源ID
     *
     * @param index
     * @return
     */
    public static int getDefaultAvatarId(int index) {
        int id = 0;
        switch (index) {
            case 1:
                id = R.mipmap.avatar_1;
                break;
            case 2:
                id = R.mipmap.avatar_2;
                break;
            case 3:
                id = R.mipmap.avatar_3;
                break;
            case 4:
                id = R.mipmap.avatar_4;
                break;
            case 5:
                id = R.mipmap.avatar_5;
                break;
            case 6:
                id = R.mipmap.avatar_6;
                break;
            case 7:
                id = R.mipmap.avatar_7;
                break;
            case 8:
                id = R.mipmap.avatar_8;
                break;
            case 9:
                id = R.mipmap.avatar_9;
                break;
            case 10:
                id = R.mipmap.avatar_10;
                break;
            case 11:
                id = R.mipmap.avatar_11;
                break;
            case 12:
                id = R.mipmap.avatar_12;
                break;
            case 13:
                id = R.mipmap.avatar_13;
                break;
            case 14:
                id = R.mipmap.avatar_14;
                break;
            case 15:
                id = R.mipmap.avatar_15;
                break;
            case 16:
                id = R.mipmap.avatar_16;
                break;
            default:
                id = 0;
                break;
        }
        return id;
    }

    public static @DrawableRes int getGenderIcon(int gender) {
        int icon;
        switch (gender) {
            case 0:
                icon = R.mipmap.ic_gender_female_16;
                break;
            case 1:
                icon = R.mipmap.ic_gender_male_16;
                break;
            case 2:
                icon = R.mipmap.ic_gender_secret_16;
                break;
            default:
                icon = 0;
                break;
        }
        return icon;
    }

    public static int getInitMainTabSelection() {
        return 0;
    }

    public static void setImageTint(Context context, ImageView ivImage) {
        ivImage.setImageTintList(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.color_FF292929_FFD2D2D6)));
    }

    /**
     * 牛人端，获取列表预加载shimmer样式
     *
     * @param context
     * @param targetView
     * @return
     */
    public static ZPUIStateLayoutManager getJobListPreLoadingStyle(Context context, View targetView) {
        ZPUIStateLayoutManager stateLayoutManager = new ZPUIStateLayoutManager(context, targetView);
        View viewLoad = LayoutInflater.from(context).inflate(R.layout.twl_ui_shimmer_loading_view_for_job_list, null);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_CUSTOM)
                .setBackgroundColor(ContextCompat.getColor(context, R.color.color_FFF5F5F6_FF131314))
                .setCustomView(viewLoad);
        return stateLayoutManager;
    }

    /**
     * BOSS端，获取列表预加载shimmer样式
     *
     * @param context
     * @param targetView
     * @return
     */
    public static ZPUIStateLayoutManager getResumeListPreLoadingStyle(Context context, View targetView) {
        ZPUIStateLayoutManager stateLayoutManager = new ZPUIStateLayoutManager(context, targetView);
        View viewLoad = LayoutInflater.from(context).inflate(R.layout.twl_ui_shimmer_loading_view_for_resume_list, null);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_CUSTOM)
                .setBackgroundColor(ContextCompat.getColor(context, R.color.color_FFF5F5F6_FF131314))
                .setCustomView(viewLoad);
        return stateLayoutManager;
    }

    /**
     * BOSS端，获取列表预加载shimmer样式
     *
     * @param context
     * @param targetView
     * @return
     */
    public static ZPUIStateLayoutManager getAnonymousResumeListPreLoadingStyle(Context context, View targetView) {
        ZPUIStateLayoutManager stateLayoutManager = new ZPUIStateLayoutManager(context, targetView);
        View viewLoad = LayoutInflater.from(context).inflate(R.layout.twl_ui_shimmer_loading_view_for_anonymous_resume_list, null);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_CUSTOM)
                .setBackgroundColor(ContextCompat.getColor(context, R.color.color_FFF5F5F6_FF131314))
                .setCustomView(viewLoad);
        return stateLayoutManager;
    }

    public static class CutInfo {
        public int w;
        public int h;
    }

    public static void setBoldTextBuilder(@Nullable SpannableStringBuilder builder, @Nullable List<ServerHighlightListBean> indexList) {
        if (LList.isEmpty(indexList) || builder == null) {
            return;
        }
        int size = indexList.size();
        int length = builder.length();
        int startIndex;
        int endIndex;
        for (int i = 0; i < size; i++) {
            ServerHighlightListBean bean = LList.getElement(indexList, i);
            if (bean == null || isSpanIndexInvalid(bean.startIndex, bean.endIndex, length)) {
                continue;
            }
            startIndex = bean.startIndex;
            endIndex = bean.endIndex;
            builder.setSpan(new FakeBoldStyle(Typeface.NORMAL), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
    }

    private static boolean isSpanIndexInvalid(int startIndex, int endIndex, int length) {
        return startIndex < 0 || endIndex <= startIndex || endIndex > length;
    }

    public interface OnClickableBeanClickListener {
        void onClick(@NonNull ServerHighlightListBean bean);
    }
}
