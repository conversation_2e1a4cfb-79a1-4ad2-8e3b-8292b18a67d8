package com.hpbr.bosszhipin.common;

import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.RecordVipPrivilegeTipGuideRequest;
import net.bosszhipin.api.RecordVipPrivilegeTipGuideResponse;
import net.bosszhipin.api.bean.ServerVipPrivilegeTipGuideBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Author: zhouyou
 * Date: 2021/12/23
 */
public class VipPrivilegeTipGuideUtils {

    public static final int SCENE_F1_FILTER = 1;
    public static final int SCENE_F2_GEEK_INTENT = 2;
    public static final int SCENE_RESUME_GEEK_ANALYZER = 4;
    public static final int SCENE_RESUME_ASSISTANCE = 8;
    public static final int SCENE_F1_FILTER_POP_WINDOW = 16;
    public static final int SCENE_F1_FILTER_POP_WINDOW_32 = 32;
    //1111.290【商业】电话直拨-提升开启电话授权牛人筛选功能使用率-展示区域高亮
    public static final int SCENE_F1_FILTER_POP_WINDOW_64 = 64;

    //1207.92【驻外】驻外列表下信息调整
    public static final int SCENE_F1_FILTER_POP_WINDOW_128 = 128;

    private static final CopyOnWriteArrayList<ServerVipPrivilegeTipGuideBean> tipList = new CopyOnWriteArrayList<>();

    /**
     * 缓存数据
     */
    public static void cacheTipListData(List<ServerVipPrivilegeTipGuideBean> list) {
        tipList.clear();
        if (!LList.isEmpty(list)) {
            tipList.addAll(list);
        }
    }

    /**
     * 当前场景是否支持展示引导
     *
     * @param source
     * @return
     */
    public static boolean isExistScene(int source) {
        boolean isExist = false;
        if (!LList.isEmpty(tipList)) {
            for (ServerVipPrivilegeTipGuideBean item : tipList) {
                if (item != null && item.isShowGuide() && item.source == source) {
                    isExist = true;
                    break;
                }
            }
        }
        return isExist;
    }

    /**
     * 当前场景是否支持展示引导
     *
     * @param source
     * @return
     */
    public static ServerVipPrivilegeTipGuideBean isExistItemScene(int source) {
        ServerVipPrivilegeTipGuideBean guideBean = null;
        if (!LList.isEmpty(tipList)) {
            for (ServerVipPrivilegeTipGuideBean item : tipList) {
                if (item != null && item.isShowGuide() && item.source == source) {
                    guideBean = item;
                    break;
                }
            }
        }
        return guideBean;
    }

    public static void recordScene(int source, int bizSource) {
        recordScene(source, bizSource, 0);
    }


    public static void recordScene(int source, int bizSource, long jobId) {
        // 本地移除
        Iterator<ServerVipPrivilegeTipGuideBean> it = tipList.iterator();
        while (it.hasNext()) {
            ServerVipPrivilegeTipGuideBean item = it.next();
            if (item != null && item.source == source) {
                tipList.remove(item);
                break;
            }
        }
        // 上传网络
        RecordVipPrivilegeTipGuideRequest request = new RecordVipPrivilegeTipGuideRequest(new ApiRequestCallback<RecordVipPrivilegeTipGuideResponse>() {
            @Override
            public void onSuccess(ApiData<RecordVipPrivilegeTipGuideResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.source = source;
        request.bizSource = bizSource;
        if (jobId > 0) {
            request.jobId = jobId;
        }
        HttpExecutor.execute(request);
    }

    /*VIP4特权使用引导的触发，并区分特权。（919.259）*/
    /*特权编号：1高级筛选，2意向牛人畅聊，3牛人分析器，4简历助手，5同事沟通进度*/
    public static void baBizItemSearchListChat(int action) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_BLOCK_VIP4_TRIGUSEGUIDE)
                .param("p", String.valueOf(action))
                .debug()
                .build();
    }
}
