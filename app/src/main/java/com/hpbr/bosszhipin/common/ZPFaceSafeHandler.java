package com.hpbr.bosszhipin.common;

import android.content.Context;

import androidx.annotation.NonNull;

import com.bzl.security.verify.VerifyConfig;
import com.bzl.security.verify.VerifySdk;
import com.bzl.security.verify.annotations.ApiEnv;
import com.bzl.security.verify.interfaces.IIdentityCollectCallback;
import com.bzl.security.verify.interfaces.IdentityType;
import com.bzl.security.verify.interfaces.Supplier;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.webview.bean.ZpFaceCollectionMessage;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.kanzhun.safetyfacesdk.commondatastructure.FaceDetectParam;
import com.twl.ui.ToastUtils;

/**
 * created by tongxiaoyun.
 * date: 2022/10/20
 * time: 11:33 AM
 * description:人脸识别接入安全SDK的特征向量校验工具类
 */
public class ZPFaceSafeHandler {
    public static final String TAG = ZPFaceSafeHandler.class.getSimpleName();
    private final Context context;
    private final ZpFaceCollectionMessage message;


    public ZPFaceSafeHandler(Context context, String deviceData, IIdentityCollectCallback zpFaceVectorCallback, ZpFaceCollectionMessage message) {
        this.context = context;
        this.message = message;
        if (message == null) return;
        if (message.data == null) return;
        boolean debug = HostConfig.CONFIG != HostConfig.Addr.ONLINE && HostConfig.CONFIG != HostConfig.Addr.PRE;
        int apiEnv = ApiEnv.QA;
        String appKey = ZPFaceVectorHandler.DEBUG_APP_KEY;
        String secretKey = ZPFaceVectorHandler.DEBUG_SECRET_KEY;
        if (HostConfig.CONFIG == HostConfig.Addr.ONLINE) {
            appKey = ZPFaceVectorHandler.RELEASE_APP_KEY;
            secretKey = ZPFaceVectorHandler.RELEASE_SECRET_KEY;
            apiEnv = ApiEnv.RELEASE;
        } else if (HostConfig.CONFIG == HostConfig.Addr.PRE) {
            appKey = ZPFaceVectorHandler.PRE_APP_KEY;
            secretKey = ZPFaceVectorHandler.PRE_SECRET_KEY;
            apiEnv = ApiEnv.PRE;
        }

        VerifyConfig verifyConfig = VerifyConfig.newBuilder(apiEnv)
                .enableLog(debug)
                .setAppKey(appKey)
                .setSecretKey(secretKey)
                .deviceDataSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return deviceData;
                    }
                })
                .setFaceTicketSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return "";
                    }
                })
                .userIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return String.valueOf(UserManager.getUID());
                    }
                })
                .deviceIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return MobileUtil.getDid();
                    }
                })
                .needDetectPics(true)
                .enableLog(true)
                .useNewModel(message.data.useNewModel)
                .setIdentityCollectCallback(zpFaceVectorCallback)
                .build();
        VerifySdk.setShowProtocol(false);
        VerifySdk.init(context, verifyConfig);
    }

    public void start() {
        if (message == null) return;
        if (message.data == null) return;
        if (message.data.orderId == null || message.data.cardId == null || message.data.name == null) {
            ToastUtils.showText("数据异常");
            return;
        }
        FaceDetectParam param = FaceDetectParam.createDefault();
        param.setEnablePrepare(message.data.needPreRecord);
        boolean showTip = message.data.age > ZPFaceVectorHandler.AGE_ZERO &&
                (message.data.age > ZPFaceVectorHandler.MAX_AGE ||
                        message.data.age < ZPFaceVectorHandler.MIN_AGE);
        if (message.data.faceType == 3) {
            param.setNeedActionDetectAfterSilence(true);
            param.setActionDetectTypeAfterSilence(0);
        }

        param.setShowSecurityTip(showTip);
        VerifySdk.openPageForIdentityDetect(context, message.data.orderId, message.data.name, message.data.cardId,
                changeFaceType(), message.data.needPreRecord, param);
    }

    public int changeFaceType() {
        if (message.data.faceType == 2) {
            return IdentityType.DAZZLING;
        } else if (message.data.faceType == 1) {
            return IdentityType.ACTION;
        }
        return IdentityType.SILENCE;
    }

}
