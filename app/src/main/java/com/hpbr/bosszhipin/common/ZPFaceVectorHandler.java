package com.hpbr.bosszhipin.common;


import android.content.Context;

import androidx.annotation.NonNull;

import com.bzl.security.verify.VerifyConfig;
import com.bzl.security.verify.VerifySdk;
import com.bzl.security.verify.annotations.ApiEnv;
import com.bzl.security.verify.interfaces.ICallbackCode;
import com.bzl.security.verify.interfaces.IOpenFaceDetectCallback;
import com.bzl.security.verify.interfaces.Supplier;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.launcher.ProtocolWebviewActivity;
import com.hpbr.bosszhipin.module.webview.jsi.PostMessage;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.kanzhun.safetyfacesdk.commondatastructure.FaceDetectParam;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import java.util.Locale;

/**
 * created by tongxiaoyun.
 * date: 2022/10/20
 * time: 11:33 AM
 * description:人脸识别接入安全SDK的特征向量校验工具类
 */
public class ZPFaceVectorHandler {
    public static final String TAG = ZPFaceVectorHandler.class.getSimpleName();
    private final Context context;
    private final ZPFaceVectorCallback zpFaceVectorCallback;
    private final PostMessage.Params params;
    public final static String DEBUG_APP_KEY = "LdBsuyHG";
    public final static String RELEASE_APP_KEY = "smbP1eYZ";
    public final static String PRE_APP_KEY = "BMrdbdel";
    public final static String RELEASE_SECRET_KEY = "c33b054c680e40618df9aeb18cab28af";
    public final static String PRE_SECRET_KEY = "4e69087f854845f6a6ae4ca70464db52";
    public final static String DEBUG_SECRET_KEY = "03221d1bc96a44489fb21e46cada9f03";
    public final static int MAX_AGE = 60;
    public final static int MIN_AGE = 20;
    public final static int AGE_ZERO = 0;

    public ZPFaceVectorHandler(Context context, String cheatData, ZPFaceVectorCallback zpFaceVectorCallback, PostMessage.Params params) {
        this.context = context;
        this.zpFaceVectorCallback = zpFaceVectorCallback;
        this.params = params;
        boolean debug = HostConfig.CONFIG != HostConfig.Addr.ONLINE && HostConfig.CONFIG != HostConfig.Addr.PRE;
        int apiEnv = ApiEnv.QA;
        String appKey = DEBUG_APP_KEY;
        String secretKey = DEBUG_SECRET_KEY;
        if (HostConfig.CONFIG == HostConfig.Addr.ONLINE) {
            appKey = RELEASE_APP_KEY;
            secretKey = RELEASE_SECRET_KEY;
            apiEnv = ApiEnv.RELEASE;
        } else if (HostConfig.CONFIG == HostConfig.Addr.PRE) {
            appKey = PRE_APP_KEY;
            secretKey = PRE_SECRET_KEY;
            apiEnv = ApiEnv.PRE;
        }


        VerifyConfig verifyConfig = VerifyConfig.newBuilder(apiEnv)
                .enableLog(debug)
                .setAppKey(appKey)
                .setSecretKey(secretKey)
                .deviceDataSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return cheatData;
                    }
                })
                .setFaceTicketSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        if (params == null) return "";
                        return params.certificate;
                    }
                })
                .userIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return String.valueOf(UserManager.getUID());
                    }
                })
                .deviceIdSupplier(new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return MobileUtil.getDid();
                    }
                })
                .needDetectPics(true)
                .setOpenFaceDetectCallback(new IOpenFaceDetectCallback() {
                    @Override
                    public void onResult(int verifyCode, int detailcode, String message) {
                        TLog.info(TAG, "=======verifyCode：%d ========detailcode: %d ==== message: %s", verifyCode, detailcode, message);
                        pointApm("vector", message, String.valueOf(verifyCode), String.valueOf(detailcode));
                        if (detailcode == ICallbackCode.CODE_SUCCESS) {
                            if (zpFaceVectorCallback != null) {
                                zpFaceVectorCallback.onFaceVectorResult(String.format(Locale.getDefault(), "javascript:responsezpfacevectorResult('0','%d','%s')", detailcode, message == null ? "" : message));
                            }
                        } else {
                            if (zpFaceVectorCallback != null) {
                                zpFaceVectorCallback.onFaceVectorResult(String.format(Locale.getDefault(), "javascript:responsezpfacevectorResult('1','%d','%s')", detailcode, message == null ? "" : message));
                            }
                        }
                    }

                    @Override
                    public void onClickOpenAgreement() {
                        String url = String.format(Locale.getDefault(), "%smpa/html/mix/agreement-detail?agreementId=17b357e7f70341bcb3fc1ac73c05b376", URLConfig.getWebHost());
                        ProtocolWebviewActivity.startActivity(context, url);
                    }

                    @Override
                    public void onClickAgreementAndContinue() {
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_LIFECYCLE_FACE_CLICK_LAW)
                                .param("p", 2)
                                .debug()
                                .build();
                    }

                    @Override
                    public void onDetectStatistic(String s) {
                        if (LText.empty(s)) return;
                        TLog.info(TAG, "onDetectStatistic :%s", s);
                    }

                })
                .useNewModel(params.useNewModel)
                .build();
        VerifySdk.setShowProtocol(false);
        VerifySdk.init(context, verifyConfig);
    }

    public void start() {
        if (params == null) return;
        String orderId = params.orderId;
        if (orderId == null) {
            ToastUtils.showText("数据异常");
            return;
        }
        FaceDetectParam param = FaceDetectParam.createDefault();
        param.setEnablePrepare(false);
        boolean showTip = params.age > ZPFaceVectorHandler.AGE_ZERO &&
                (params.age > ZPFaceVectorHandler.MAX_AGE ||
                        params.age < ZPFaceVectorHandler.MIN_AGE);
        param.setShowSecurityTip(showTip);
        VerifySdk.openPageForOpenFaceDetect(context, orderId, param);
    }


    private void pointApm(String tag, String errorMessage, String verifyCode, String detailcode) {
        ApmAnalyzer.create().action("action_face_auth", tag)
                .param("p2", errorMessage)
                .param("p3", verifyCode)
                .param("p4", detailcode)
                .report();
    }

    public interface ZPFaceVectorCallback {
        void onFaceVectorResult(String js);
    }
}
