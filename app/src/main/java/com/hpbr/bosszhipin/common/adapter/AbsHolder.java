package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.CallSuper;
import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public abstract class AbsHolder<M extends ItemModel> extends RecyclerView.ViewHolder implements View.OnClickListener {

    private final LayoutInflater inflater;
    private final Context context;
    private M model;

    public M getModel() {
        return model;
    }

    public Context getContext() {
        return context;
    }

    public AbsHolder(@NonNull View itemView) {
        super(itemView);
        itemView.setOnClickListener(this);
        context = itemView.getContext();
        inflater = LayoutInflater.from(itemView.getContext());
    }

    @Override
    public void onClick(View v) {

    }

    @CallSuper
    public void bind(@NonNull M model) {
        this.model = model;
    }

    public View inflate(@LayoutRes int resId, ViewGroup parent, boolean attachToRoot) {
        return inflater.inflate(resId, parent, attachToRoot);
    }

    public <T extends View> T findViewById(@IdRes int id) {
        return itemView.findViewById(id);
    }

}
