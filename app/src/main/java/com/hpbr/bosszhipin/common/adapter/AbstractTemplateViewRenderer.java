package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;

import com.monch.lbase.util.L;


public abstract class AbstractTemplateViewRenderer<M extends ItemModel, VH extends AbsHolder<M>, I>
        extends AbstractViewRenderer<M, VH, I> {

    private static final String LOG_TAG = AbstractTemplateViewRenderer.class.getSimpleName();

    public AbstractTemplateViewRenderer(Context context, @NonNull I listener) {
        super(context, listener);
    }

    @NonNull
    @Override
    public VH createViewHolder(@NonNull ViewGroup parent) {
        View itemView = inflate(getTemplateLayoutResId(), parent, false);
        FrameLayout container = itemView.findViewById(getContainerId());
        final int varResId = getVarResId();
        if (varResId != 0 && container != null) {
            container.addView(inflate(varResId, container, false));
        } else {
            L.e(LOG_TAG, "varResId: " + varResId + ", container: " + container);
        }
        return newVH(itemView);
    }

    @LayoutRes
    protected abstract int getTemplateLayoutResId();

    @IdRes
    protected abstract int getContainerId();

    @LayoutRes
    protected abstract int getVarResId();

    @NonNull
    protected abstract VH newVH(@NonNull View itemView);

}
