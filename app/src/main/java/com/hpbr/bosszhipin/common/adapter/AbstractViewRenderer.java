package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;

import androidx.annotation.NonNull;

import com.monch.lbase.util.L;

/**
 * Created by zhangxiangdong on 2018/8/9 17:39.
 */
public abstract class AbstractViewRenderer<M extends ItemModel, VH extends AbsHolder<M>, I> extends ViewRenderer<M, VH> {

    private static final String LOG_TAG = AbstractViewRenderer.class.getSimpleName();

    @NonNull
    private final I mListener;

    public AbstractViewRenderer(Context context, @NonNull I listener) {
        super(context);
        mListener = listener;
    }

    @Override
    public void bindView(@NonNull M model, @NonNull VH holder) {
        try {
            holder.bind(model);
        } catch (Exception e) {
            L.e(LOG_TAG, e.getMessage());
        }
    }

    @NonNull
    public I getListener() {
        return mListener;
    }

}
