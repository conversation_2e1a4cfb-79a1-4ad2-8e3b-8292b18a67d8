package com.hpbr.bosszhipin.common.adapter;

import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.*;
import androidx.recyclerview.widget.RecyclerView;
import com.hpbr.bosszhipin.common.adapter.utils.HolderUtils;

public abstract class AbstractViewRenderer2<M extends ItemModel, C extends Callback>
        extends ViewRenderer<M, RecyclerView.ViewHolder> {

    @NonNull
    private final C callback;

    public AbstractViewRenderer2(@NonNull C callback) {
        super(callback.getContext());
        this.callback = callback;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder createViewHolder(@NonNull ViewGroup parent) {
        return new RecyclerView.ViewHolder(inflate(getItemLayoutResId(), parent, false)) {
            {
                onBeforeHolderInitialized(this);
            }
        };
    }

    @CallSuper
    protected void onBeforeHolderInitialized(@NonNull RecyclerView.ViewHolder holder) {

    }

    @LayoutRes
    public abstract int getItemLayoutResId();

    @NonNull
    protected <T extends View> T getViewById(@NonNull RecyclerView.ViewHolder holder, @IdRes int id) {
        return HolderUtils.getViewById(holder, id);
    }

    @CallSuper
    @Override
    public void bindView(@NonNull M model, @NonNull RecyclerView.ViewHolder holder) {
        holder.itemView.setTag(model);
    }

    @Nullable
    public final M getModel(@NonNull RecyclerView.ViewHolder holder) {
        Object tag = holder.itemView.getTag();
        if (tag instanceof ItemModel) {
            //noinspection unchecked
            return (M) tag;
        }
        return null;
    }

    @NonNull
    public final C getCallback() {
        return callback;
    }

}
