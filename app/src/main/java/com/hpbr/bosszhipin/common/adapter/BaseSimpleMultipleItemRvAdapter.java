package com.hpbr.bosszhipin.common.adapter;

import androidx.annotation.Nullable;
import com.hpbr.bosszhipin.base.BaseMultiItemEntity;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

import java.util.List;

/**
 * @ClassName ：BaseSimpleMultipleItemRvAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/1/6  10:32 上午
 */
public abstract class BaseSimpleMultipleItemRvAdapter<T extends BaseMultiItemEntity, V extends BaseViewHolder> extends BaseMultipleItemRvAdapter<T, V> {

    public BaseSimpleMultipleItemRvAdapter(@Nullable List<T> data) {
        super(data);
    }

    @Override
    protected int getViewType(List<T> data, int position) {
        T item = data.get(position);
        return item.getItemType();
    }

}
