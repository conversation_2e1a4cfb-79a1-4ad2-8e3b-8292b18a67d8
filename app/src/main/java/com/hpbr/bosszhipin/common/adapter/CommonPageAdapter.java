package com.hpbr.bosszhipin.common.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import com.hpbr.bosszhipin.base.BaseAwareFragment;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

/**
 * @ClassName ：CommonPageAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/8/21  8:45 上午
 */
public class CommonPageAdapter extends FragmentPagerAdapter {
    private List<Fragment> mFragmentList = new ArrayList<>();

    public CommonPageAdapter(FragmentManager fm, int behavior, @NonNull List<Fragment> fragmentList) {
        super(fm, behavior);
        for (Fragment fragment : fragmentList) {
            this.mFragmentList.add(fragment);
        }
    }

    public CommonPageAdapter(FragmentManager fm, int behavior, @NonNull TreeMap<Integer, BaseAwareFragment> fragments) {
        super(fm, behavior);
        mFragmentList.clear();
        if (fragments != null) {
            for (BaseAwareFragment baseAwareFragment : fragments.values()) {
                mFragmentList.add(baseAwareFragment);
            }
        }
    }

    public CommonPageAdapter(FragmentManager fm) {
        super(fm);
    }

    public List<Fragment> getFragmentList() {
        return mFragmentList;
    }

    public void addPage(int index, Fragment fragment){
        mFragmentList.add(index, fragment);
        notifyDataSetChanged();
    }

    public void addPage(Fragment fragment){
        mFragmentList.add(fragment);
        notifyDataSetChanged();
    }

    public void delPage(int index) {
        mFragmentList.remove(index);
        notifyDataSetChanged();
    }

    public void delPage(Fragment fragment) {
        mFragmentList.remove(fragment);
        notifyDataSetChanged();
    }

    public void updatePage(List<Fragment> fragmentList) {
        mFragmentList.clear();

        for(int i = 0; i < fragmentList.size(); i++){
            mFragmentList.add(fragmentList.get(i));
        }
        notifyDataSetChanged();
    }

    public void updatePage(TreeMap<Integer, BaseAwareFragment> fragments){
        mFragmentList.clear();
        if (fragments != null) {
            for (BaseAwareFragment baseAwareFragment : fragments.values()) {
                mFragmentList.add(baseAwareFragment);
            }
        }
        notifyDataSetChanged();
    }

    @Override
    public Fragment getItem(int position) {
        return mFragmentList.get(position);
    }

    @Override
    public int getCount() {
        return mFragmentList.size();
    }

    /**
     * 返回值有三种，
     * POSITION_UNCHANGED  默认值，位置没有改变
     * POSITION_NONE       item已经不存在
     * position            item新的位置
     * 当position发生改变时这个方法应该返回改变后的位置，以便页面刷新。
     */
    @Override
    public int getItemPosition(Object object) {
        if (object instanceof Fragment) {
            int index = mFragmentList.indexOf(object);
            if (index != -1) {
                return index;
            } else {
                return POSITION_NONE;
            }
        }
        return super.getItemPosition(object);
    }

    @Override
    public long getItemId(int position) {
        return mFragmentList.get(position).hashCode();
    }
}
