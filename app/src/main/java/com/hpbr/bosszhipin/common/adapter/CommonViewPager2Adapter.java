package com.hpbr.bosszhipin.common.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ：CommonViewPager2Adapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/6/22  11:58 上午
 */
public class CommonViewPager2Adapter extends FragmentStateAdapter {

    private List<Fragment> fragmentList = new ArrayList<>();

    public CommonViewPager2Adapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    public CommonViewPager2Adapter(@NonNull Fragment fragment) {
        super(fragment);
    }

    public void setNewFragments(List<Fragment> fragments) {
        fragmentList.clear();
        if (fragments != null) {
            fragmentList.addAll(fragments);
        }
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        Fragment f = LList.getElement(fragmentList, position);
        if (f == null) {
            f = new Fragment();
        }
        return f;
    }

    @Override
    public int getItemCount() {
        return fragmentList.size();
    }

    @Override
    public long getItemId(int position) {
        Fragment fragment = LList.getElement(fragmentList, position);
        if (fragment != null) {
            return fragment.hashCode();
        }
        return position;
    }

    @Override
    public boolean containsItem(long itemId) {
        for (int i = 0; i < fragmentList.size(); i++) {
            long itemId1 = getItemId(i);
            if (itemId1 == itemId) {
                return true;
            }
        }
        return super.containsItem(itemId);
    }
}
