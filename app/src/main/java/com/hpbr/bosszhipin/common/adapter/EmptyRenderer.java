package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/2/28.
 */
public class EmptyRenderer extends ViewRenderer {

    EmptyRenderer(Context context) {
        super(context);
    }

    @Override
    public void bindView(@NonNull ItemModel model, @NonNull RecyclerView.ViewHolder holder) {

    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder createViewHolder(@NonNull ViewGroup parent) {
        return new RecyclerView.ViewHolder(inflate(R.layout.empty, parent, false)) {
        };
    }

    @Override
    public boolean canUseThisRenderer(ItemModel model) {
        return true;
    }

}
