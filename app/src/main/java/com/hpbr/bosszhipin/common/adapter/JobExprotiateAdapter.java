package com.hpbr.bosszhipin.common.adapter;

import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.AgentProxyExpatriateTipBottomDialog;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-20
 * @description Ctb JD页 中介派遣弹窗banner
 */
public class JobExprotiateAdapter extends BaseQuickAdapter<AgentProxyExpatriateTipBottomDialog.DialogBean, BaseViewHolder> {
    public JobExprotiateAdapter(@Nullable List<AgentProxyExpatriateTipBottomDialog.DialogBean> data) {
        super(R.layout.geek_job_detail_exportrait_banner, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, AgentProxyExpatriateTipBottomDialog.DialogBean item) {
        SimpleDraweeView ivPic = helper.getView(R.id.iv_banner_pic);
        MTextView tvDesc = helper.getView(R.id.tv_desc);
        MTextView tvTips = helper.getView(R.id.tv_tips);
        ivPic.setImageResource(item.pidSourceId);
        tvDesc.setText(item.desc, View.GONE);
        if (!TextUtils.isEmpty(item.tips)) {
            tvTips.setText(item.tips);
            tvTips.setVisibility(View.VISIBLE);
        } else {
            tvTips.setVisibility(View.GONE);
        }
    }
}
