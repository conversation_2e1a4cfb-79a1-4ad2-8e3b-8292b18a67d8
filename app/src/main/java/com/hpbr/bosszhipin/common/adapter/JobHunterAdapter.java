package com.hpbr.bosszhipin.common.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.HunterRecruitTipBottomDialog;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-09-13
 * @description Ctb JD页 猎头弹窗banner
 */
public class JobHunterAdapter extends BaseQuickAdapter<HunterRecruitTipBottomDialog.DialogBean, BaseViewHolder> {
    public JobHunterAdapter(@Nullable List<HunterRecruitTipBottomDialog.DialogBean> data) {
        super(R.layout.geek_job_detail_hunter_banner, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, HunterRecruitTipBottomDialog.DialogBean item) {
        ImageView ivPic = helper.getView(R.id.iv_banner_pic);
        MTextView tvDesc = helper.getView(R.id.tv_desc);
        MTextView tvTips = helper.getView(R.id.tv_tips);
        ivPic.setImageResource(item.pidSourceId);
        tvDesc.setText(item.desc, View.GONE);

        if (!TextUtils.isEmpty(item.tips)) {
            tvTips.setText(item.tips);
            tvTips.setVisibility(View.VISIBLE);
        } else {
            tvTips.setVisibility(View.GONE);
        }
    }


}
