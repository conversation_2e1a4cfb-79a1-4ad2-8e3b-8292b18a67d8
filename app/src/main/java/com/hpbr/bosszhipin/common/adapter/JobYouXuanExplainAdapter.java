package com.hpbr.bosszhipin.common.adapter;

import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.views.MTextView;
import net.bosszhipin.api.bean.ServerSafeTipOverlayBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-20
 * @description Ctb JD页 中介派遣弹窗banner
 */
public class JobYouXuanExplainAdapter extends BaseQuickAdapter<ServerSafeTipOverlayBean, BaseViewHolder> {
    public boolean isSelectNoMore = false;
    public JobYouXuanExplainAdapter(@Nullable List<ServerSafeTipOverlayBean> data) {
        super(R.layout.geek_job_detail_youxuan_banner, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, ServerSafeTipOverlayBean item) {
        SimpleDraweeView ivPic = helper.getView(R.id.iv_banner_pic);
        MTextView tvDesc = helper.getView(R.id.tv_desc);
        MTextView tvTips = helper.getView(R.id.tv_tips);
        ivPic.setImageURI(item.picUrl);
        tvDesc.setText(item.subTitle, View.GONE);
        if (!TextUtils.isEmpty(item.content)) {
            SpannableStringBuilder builder = ViewCommon.setTextHighLight(item.content, item.contentHighlightIndex, ContextCompat.getColor(mContext, R.color.color_FF3072F6));
            tvTips.setText(builder);
        } else {
            tvTips.setVisibility(View.GONE);
        }
    }
}
