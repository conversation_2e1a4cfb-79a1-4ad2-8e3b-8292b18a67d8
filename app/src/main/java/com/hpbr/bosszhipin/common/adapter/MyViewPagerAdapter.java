package com.hpbr.bosszhipin.common.adapter;

import com.hpbr.bosszhipin.base.BaseAwareFragment;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

/**
 * @ClassName ：MyViewPagerAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/8/3  3:08 下午
 */
public class MyViewPagerAdapter extends FragmentPagerAdapter {
    private final List<Fragment> mFragments = new ArrayList<>();

    public MyViewPagerAdapter(@NonNull FragmentManager fm, List<BaseAwareFragment> fragments) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        mFragments.clear();
        if (!LList.isEmpty(fragments)) {
            mFragments.addAll(fragments);
        }
    }

    public MyViewPagerAdapter(@NonNull FragmentManager fm, TreeMap<Integer, BaseAwareFragment> fragments) {
        super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
        updateFragments(fragments);
    }

    public void updateFragments(TreeMap<Integer, BaseAwareFragment> fragments){
        mFragments.clear();
        if (fragments != null) {
            for (BaseAwareFragment baseAwareFragment : fragments.values()) {
                mFragments.add(baseAwareFragment);
            }
        }
    }

    @NonNull
    @Override
    public Fragment getItem(int position) {
        Fragment fragment = LList.getElement(mFragments, position);
        if (fragment == null) {
            return new Fragment();
        }
        return fragment;
    }

    @Override
    public int getCount() {
        return LList.getCount(mFragments);
    }

}
