package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;
import android.util.SparseArray;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import com.hpbr.bosszhipin.utils.functions.NonNullConsumer;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;

import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangxiangdong on 2018/1/30.
 * <p>
 * https://android.jlelse.eu/a-recyclerview-with-multiple-item-types-dfba3979050
 */
public class RendererRecyclerViewAdapter<M extends ItemModel> extends RecyclerView.Adapter {

    private static final String LOG_TAG = RendererRecyclerViewAdapter.class.getSimpleName();

    private final SparseArray<ViewRenderer> mUsefulRenderers = new SparseArray<>();
    private final ViewRenderer mEmptyRenderer;
    private List<M> mItems = new ArrayList<>();
    @NonNull
    private final Context context;

    public RendererRecyclerViewAdapter(@NonNull Context context) {
        this(null, context);
    }

    public RendererRecyclerViewAdapter(@Nullable List<M> items, @NonNull Context context) {
        if (items != null && items.size() > 0) {
            mItems.addAll(items);
        }
        this.context = context;
        mEmptyRenderer = new EmptyRenderer(context);
    }

    @NonNull
    public Context getContext() {
        return context;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull final ViewGroup parent, final int viewType) {
        @NonNull RecyclerView.ViewHolder holder;
        try {
            @NonNull ViewRenderer renderer = getRenderer(viewType);
            holder = renderer.createViewHolder(parent);
        } catch (Exception e) {
            if (BuildInfoUtils.isDebug()) {
                throw e;
            } else {
                L.e(LOG_TAG, String.valueOf(e));
                e.printStackTrace();
                holder = mEmptyRenderer.createViewHolder(parent);
            }
        }
        return holder;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        final ItemModel model = getItem(position);
        final ViewRenderer renderer = getRenderer(model);
        if (model != null) {
            try {
                renderer.bindView(model, holder);
            } catch (Exception e) {
                if (BuildInfoUtils.isDebug()) {
                    throw e;
                } else {
                    L.e(LOG_TAG, e.getMessage());
                    e.printStackTrace();
                }
            }
        } else {
            final String msg = "数据项为null, position: " + position;
            if (BuildInfoUtils.isDebug()) {
                throw new IllegalArgumentException(msg);
            } else {
                L.e(LOG_TAG, msg);
            }
        }
    }

    @NonNull
    public ViewRenderer getRenderer(@Nullable ItemModel model) {
        /* 根据单条数据源从已注册的Renderer集合中查找到对应的Renderer */
        for (int i = 0; i < mUsefulRenderers.size(); i++) {
            ViewRenderer renderer = mUsefulRenderers.valueAt(i);
            if (renderer.canUseThisRenderer(model)) {
                return renderer;
            }
        }
        // 对应的单条数据没有找对对应的Renderer，返回空的占位Renderer
        return mEmptyRenderer;
    }

    @NonNull
    private ViewRenderer getRenderer(int viewType) {
        ViewRenderer renderer = mUsefulRenderers.get(viewType);
        if (renderer == null) {
            renderer = mEmptyRenderer;
        }
        return renderer;
    }

    @Override
    public int getItemViewType(final int position) {
        return getRenderer(getItem(position)).getType();
    }

    @Override
    public int getItemCount() {
        return mItems.size();
    }

    public void clearData() {
        mItems.clear();
    }

    public void setDataByQuote(@NonNull List<M> data) {
        if (data == null) return;
        mItems = data;
    }

    public void setItems(@Nullable List<M> items) {
        if (items == null) return;

        clearData();
        mItems.addAll(items);
    }

    public void addItems(@Nullable List<M> items) {
        if (items == null || items.size() == 0) return;

        mItems.addAll(items);
    }

    public void addItem(@Nullable M item) {
        if (item == null) return;

        mItems.add(item);
    }

    public void addItem(int index, @Nullable M item) {
        if (item == null) return;
        if (index < 0 || index > mItems.size()) return;
        mItems.add(index, item);
    }

    public void addItems(int index, @Nullable List<M> items) {
        if (items == null || items.size() == 0) return;
        if (index < 0 || index > mItems.size()) return;

        mItems.addAll(index, items);
    }

    @NonNull
    public List<M> getItems() {
        return mItems;
    }

    @Nullable
    public M getItem(final int position) {
        return LList.getElement(mItems, position);
    }

    @Nullable
    public M removeItem(int position) {
        return LList.delElement(mItems, position);
    }

    @SuppressWarnings("UnusedReturnValue")
    @Nullable
    public M removeItem(M item) {
        return LList.delElement(mItems, item);
    }

    @NonNull
    public RendererRecyclerViewAdapter registerRenderers(@NonNull ViewRenderer... renderers) {
        if (renderers.length > 0) {
            for (ViewRenderer renderer : renderers) {
                registerRenderer(renderer);
            }
        }
        return this;
    }

    @NonNull
    public RendererRecyclerViewAdapter registerRenderer(@NonNull ViewRenderer renderer) {
        final int type = renderer.getType();
        if (mUsefulRenderers.get(type) == null
                || renderer.getType() == ViewRenderer.TYPE_UNREGISTER) { // 未注册
            renderer.setType(mUsefulRenderers.size()); // 使用已注册的Renderer数量作为下一个待注册的Renderer的类型

            int finalType = renderer.getType();
            L.d(LOG_TAG, "Register renderer type: " + finalType);
            mUsefulRenderers.put(finalType, renderer);
        } else {
            final String msg = "ViewRenderer already exist with this type: " + type;
            if (BuildInfoUtils.isDebug()) {
                throw new RuntimeException(msg);
            } else {
                L.e(LOG_TAG, msg);
            }
        }
        return this;
    }

    @Nullable
    public <R extends ViewRenderer> R getRenderer(@NonNull Class<? extends ViewRenderer> rendererClass) {
        R instance = null;
        int rendererCount = mUsefulRenderers.size();
        for (int i = 0; i < rendererCount; i++) {
            ViewRenderer viewRenderer = mUsefulRenderers.get(mUsefulRenderers.keyAt(i));
            if (viewRenderer.getClass().isAssignableFrom(rendererClass)) {
                //noinspection unchecked
                instance = (R) viewRenderer;
                break;
            }
        }
        return instance;
    }

    public <R extends ViewRenderer> void getRenderer(@NonNull NonNullConsumer<R> rendererConsumer) {
        Class<R> type = null;
        try {
            //noinspection unchecked
            type =
                    (Class<R>) ((ParameterizedType) rendererConsumer
                            .getClass()
                            .getGenericInterfaces()[0])
                            .getActualTypeArguments()[0];
        } catch (Exception e) {
            L.e(LOG_TAG, e.getMessage());
        }

        if (type != null) {
            R renderer = getRenderer(type);
            if (renderer != null) {
                // 只有找到Renderer，调用方才能消费
                rendererConsumer.accept(renderer);
            }
        }
    }

}
