package com.hpbr.bosszhipin.common.adapter;

import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.SelectImageQualityDialog;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;


import androidx.annotation.NonNull;


public class SelectImageQualityAdapter extends BaseRvAdapter<SelectImageQualityDialog.SelectImageQualityItemBean, SelectImageQualityAdapter.ViewHolder> {

    public SelectImageQualityAdapter() {
        super(R.layout.geek_dialog_item_image_quality_select);
    }

    @Override
    protected void convert(@NonNull ViewHolder helper, SelectImageQualityDialog.SelectImageQualityItemBean item) {

        helper.setData(item);
    }

    static class ViewHolder extends BaseViewHolder {

        private final ImageView ivSelected;
        private final MTextView tvTitle;

        public ViewHolder(View view) {
            super(view);
            ivSelected = view.findViewById(R.id.ivSelected);
            tvTitle = view.findViewById(R.id.tvTitle);
        }

        void setData(SelectImageQualityDialog.SelectImageQualityItemBean data) {
            tvTitle.setText(data.title);
            ivSelected.setImageResource(data.isSelected ? R.mipmap.geek_f1_dialog_common_template_selected : R.mipmap.ic_round_o);
        }
    }
}
