package com.hpbr.bosszhipin.common.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.*;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.common.adapter.utils.HolderUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class SimpleAdapter<M, C> extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    @NonNull
    private final List<M> models = new ArrayList<>();
    private final @NonNull
    C callback;

    public SimpleAdapter(@NonNull List<M> models, @NonNull C callback) {
        this.models.addAll(models);
        this.callback = callback;
    }

    @NonNull
    public final C getCallback() {
        return callback;
    }

    @NonNull
    public final List<M> getModels() {
        return models;
    }

    public final void setModels(@NonNull List<M> models) {
        this.models.clear();
        this.models.addAll(models);
    }

    public final void addModels(@NonNull List<M> models) {
        this.models.addAll(models);
    }

    @NonNull
    @Override
    public final RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new RecyclerView.ViewHolder(LayoutInflater.from(parent.getContext()).inflate(getLayoutResId(), parent, false)) {
            {
                onBeforeHolderInitialized(this);
            }
        };
    }

    @Override
    public final void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        bindView(holder, getItem(position));
    }

    @NonNull
    public final M getItem(int position) {
        return models.get(position);
    }

    @Override
    public final int getItemCount() {
        return models.size();
    }

    @NonNull
    protected final <T extends View> T getViewById(@NonNull RecyclerView.ViewHolder holder, @IdRes int id) {
        return HolderUtils.getViewById(holder, id);
    }

    @LayoutRes
    protected abstract int getLayoutResId();

    @CallSuper
    protected void bindView(@NonNull RecyclerView.ViewHolder holder, @NonNull M model) {
    }

    @CallSuper
    protected void onBeforeHolderInitialized(@NonNull RecyclerView.ViewHolder holder) {
    }

}
