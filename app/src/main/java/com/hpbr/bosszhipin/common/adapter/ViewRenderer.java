package com.hpbr.bosszhipin.common.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.monch.lbase.util.L;

import java.lang.reflect.ParameterizedType;

/**
 * Created by zhangxiangdong on 2018/1/30.
 * <p>
 * https://android.jlelse.eu/a-recyclerview-with-multiple-item-types-dfba3979050
 */
public abstract class ViewRenderer<M extends ItemModel, VH extends RecyclerView.ViewHolder> {

    private static final String LOG_TAG = ViewRenderer.class.getSimpleName();

    static final int TYPE_UNREGISTER = -1; // 未注册的卡片类型

    private int type = TYPE_UNREGISTER;
    @NonNull
    private final LayoutInflater inflater;
    @NonNull
    private final Context context;

    public ViewRenderer(@NonNull Context context) {
        this.context = context;
        this.inflater = LayoutInflater.from(context);
    }

    public abstract void bindView(@NonNull M model, @NonNull VH holder);

    @NonNull
    public abstract VH createViewHolder(@NonNull ViewGroup parent);

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public View inflate(int resource, @NonNull ViewGroup root, boolean attachToRoot) {
        return inflater.inflate(resource, root, attachToRoot);
    }

    @NonNull
    public Context getContext() {
        return context;
    }

    /**
     * 根据传入的单条数据判断当前的Renderer是否合适
     *
     * @param model 单条数据源
     * @return 当前Renderer是否是该数据源对应的
     */
    protected boolean canUseThisRenderer(@Nullable ItemModel model) {
        Class<M> type = null;

        try {
            ParameterizedType parameterizedType = (ParameterizedType) getClass().getGenericSuperclass();
            if (parameterizedType != null) {
                //noinspection unchecked
                type = (Class<M>) parameterizedType.getActualTypeArguments()[0];
            }
        } catch (Exception e) {
            L.e(LOG_TAG, e.getMessage());
        }

        if (type != null) {
            return type.isInstance(model);
        }

        return false;
    }

}
