package com.hpbr.bosszhipin.common.adapter.utils;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

import com.hpbr.bosszhipin.utils.functions.NonNullConsumer;
import com.hpbr.bosszhipin.utils.functions.NonNullSupplier;

public class AdapterUtils {

    public static <A extends Adapter<RecyclerView.ViewHolder>> void bindAdapter(@NonNull RecyclerView recyclerView,
                                                                                @NonNull Class<A> adapterCls,
                                                                                @NonNull NonNullSupplier<A> adapterSupplier,
                                                                                @NonNull NonNullConsumer<A> adapterConsumer) {
        if (recyclerView.getAdapter() != null
                && adapterCls.isInstance(recyclerView.getAdapter())) {
            //noinspection unchecked
            adapterConsumer.accept((A) recyclerView.getAdapter());
        } else {
            recyclerView.setAdapter(adapterSupplier.get());
        }
    }

    public static void addMiddleDivider(@NonNull RecyclerView recyclerView, @RecyclerView.Orientation int orientation, int dividerInPx) {
        recyclerView.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);

                if (recyclerView.getAdapter() != null) {
                    final int childAdapterPosition = parent.getChildAdapterPosition(view);
                    final int setDivider = childAdapterPosition < recyclerView.getAdapter().getItemCount() - 1 ? dividerInPx : 0;

                    if (orientation == RecyclerView.VERTICAL) {
                        outRect.bottom = setDivider;
                    } else {
                        outRect.right = setDivider;
                    }
                }
            }
        });
    }

}
