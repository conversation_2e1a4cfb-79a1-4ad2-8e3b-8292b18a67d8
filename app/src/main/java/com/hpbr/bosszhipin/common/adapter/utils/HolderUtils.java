package com.hpbr.bosszhipin.common.adapter.utils;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

public class HolderUtils {

    @NonNull // 调用方需要确保 viewId 对应的 View 存在 holder 中的 itemView 中
    public static <T extends View> T getViewById(@NonNull ViewHolder holder, @IdRes int viewId) {
        View itemView = holder.itemView;
        Object viewById = itemView.getTag(viewId);
        if (viewById == null) {
            viewById = itemView.findViewById(viewId);
            itemView.setTag(viewId, viewById);
        }
        //noinspection unchecked
        return (T) viewById;
    }

    public static void initialize(@NonNull ViewHolder holder) {
        View itemView = holder.itemView;
        initialize0(holder, itemView);
    }

    private static void initialize0(@NonNull ViewHolder holder, @NonNull View view) {
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View child = viewGroup.getChildAt(i);
                initialize0(holder, child);
            }
        } else {
            getViewById(holder, view.getId());
        }
    }

}
