package com.hpbr.bosszhipin.common.app;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bzl.safe.crashprotect.internal.enums.BZLCrashType;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.startup.process.Processer;
import com.techwolf.lib.tlog.TLog;

import java.util.Arrays;
import java.util.List;

/**
 * Created by monch on 2017/7/18.
 */

public class ActivityThreadHook {

    private static final String TAG = "ActivityThreadHook";
    private static Handler mH = new Handler(Looper.getMainLooper());

    public static void hookActivityThread() {
        mH.postAtFrontOfQueue(new MonitorRunnable());
    }

    private static class MonitorRunnable implements Runnable {
        @Override
        public void run() {
            try {
                Log.i(TAG, "In loop.");
                Looper.loop();
                Log.i(TAG, "Loop error.");
            } catch (Throwable t) {
                TLog.error(TAG, t, "Receiver error.");
                if (isIgnoreThrowable(t)) {
                    Log.i(TAG, "Retry loop.");
                    hookActivityThread();
                } else {
                    Log.i(TAG, "throw error.", t);
                    CrashProtectInitHelper.tryRescueBeforeCrash(BZLCrashType.TYPE_JAVA, t, null, null);
                    throw t;
                }
            }
        }
    }

    private static final List<String> MMS_IGNORES = Arrays.asList("notification", "MMSReceiver", "onGetTokenComplete");
    private static final List<String> MAIN_IGNORES = Arrays.asList(
            "android.widget.Toast$TN",
            "io.rong.imlib.ConnectChangeReceiver",
            "android.hardware.SystemSensorManager$SensorEventQueue.dispatchSensorEvent_",
            "com.tencent.wcdb.database.SQLiteConnection.nativePrepareStatement",
            "android.widget.Editor$ActionPinnedPopupWindow.computeLocalPosition",
            "android.view.ViewGroup.resetCancelNextUpFlag",
            "android.widget.TextView.canPasteAsPlainText",
            "android.app.RemoteServiceException$CannotDeliverBroadcastException",
            "can't deliver broadcast",
            "Activity client record must not be null to execute transaction item"
    );


    private static boolean isIgnoreThrowable(Throwable t) {
        boolean ignore = false;
        try {
            final String string = Log.getStackTraceString(t);
            if (string != null) {
                List<String> ignoreList = null;
                if (Processer.isMMSProcess()) {
                    ignoreList = MMS_IGNORES;
                } else if (Processer.isMainProcess()) {
                    ignoreList = MAIN_IGNORES;
                }
                ignore = isIgnore(string, ignoreList);
                if (ignore) { //忽略的异常上报到bugly 的主动异常
                    TLog.info(TAG, "%s", string);
                    onIgnoreCatch(t);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ignore;
    }

    private static boolean isIgnore(String stacks, List<String> ignoreList){
        if (ignoreList != null) {
            for (String s : ignoreList) {
                if (stacks.contains(s)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static void onIgnoreCatch(Throwable throwable) {
        try {
            ApmAnalyzer.create()
                    .action("action_thread_hook", "ignore")
                    .p3(throwable.getMessage())
                    .p4(Log.getStackTraceString(throwable))
                    .debug()
                    .report();
        } catch (Throwable t) {
            TLog.error(TAG, t, "thread hook catch");
        }
    }
}
