package com.hpbr.bosszhipin.common.app.crash;

import com.hpbr.bosszhipin.common.app.crash.natives.WebViewCacheCrashRepair;
import com.tencent.bugly.BuglyStrategy;

import java.util.ArrayList;
import java.util.List;

public final class CrashRepairMan {
    private static final List<ICrashRepair> sNativeList = new ArrayList<>();

    static {
        sNativeList.add(new WebViewCacheCrashRepair());
    }

    public static void onCatch(int crashType, String errorType, String errorMessage, String errorStack) {
        if (errorStack == null || errorStack.isEmpty()) {
            return;
        }
        if (crashType == BuglyStrategy.a.CRASHTYPE_NATIVE) {
            for (ICrashRepair crashHandle : sNativeList) {
                if (crashHandle.shouldRepair(errorStack)) {
                    crashHandle.tryRepair();
                    break;
                }
            }
        }
    }

}