package com.hpbr.bosszhipin.common.app.crash.natives;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.common.app.crash.ICrashRepair;
import com.hpbr.bosszhipin.common.helper.WebViewCrashFixer;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;

public class WebViewCacheCrashRepair implements ICrashRepair {
    private static final String KEY_WORD = "libllvm-glnext.so";
    private static final String SP_KEY_WEB_VIEW_CACHE_HANDLE = "SP_KEY_WEB_VIEW_CACHE_HANDLE";

    @Override
    public boolean shouldRepair(@NonNull String stack) {
        return stack.contains(KEY_WORD);
    }

    @Override
    public void tryRepair() {
        int currentVersionCode = BuildInfoUtils.getVersionCode();
        int lastVersionCode = SpManager.get().global().getInt(SP_KEY_WEB_VIEW_CACHE_HANDLE, 0);
        if (currentVersionCode == lastVersionCode) {
            return;//相同版本已处理过一次，直接跳过
        }
        SpManager.get().global().edit().putInt(SP_KEY_WEB_VIEW_CACHE_HANDLE, currentVersionCode).apply();

        WebViewCrashFixer.deleteWebViewCache(Utils.getApp(), true);
    }
}
