package com.hpbr.bosszhipin.common.decoration;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：GridDecoration
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2020/11/16  11:22 AM
 */
public class GridDecoration extends RecyclerView.ItemDecoration {
    /*上下文对象*/
    private Context context;
    /*水平方向的间距(单位为：dp)*/
    private int horizontalSpace;
    /*顶部的间距(单位为：dp)*/
    private int topSpace;
    /*底部的间距(单位为：dp)*/
    private int bottomSpace;

    public GridDecoration(Context context, int horizontalSpace, int topSpace, int bottomSpace) {
        this.context = context;
        this.horizontalSpace = horizontalSpace;
        this.topSpace = topSpace;
        this.bottomSpace = bottomSpace;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.left = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
        outRect.right = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
        outRect.top = ZPUIDisplayHelper.dp2px(context, topSpace);
        outRect.bottom = ZPUIDisplayHelper.dp2px(context, bottomSpace);
    }
}
