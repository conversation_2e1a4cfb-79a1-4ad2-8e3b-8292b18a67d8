package com.hpbr.bosszhipin.common.decoration;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.annotation.IntRange;
import androidx.recyclerview.widget.RecyclerView;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：GridDecoration2
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/10/28  10:34 上午
 */
public class GridDecoration2 extends RecyclerView.ItemDecoration {

    /*上下文对象*/
    private Context context;
    /*一行有多少个Item*/
    private int spanCount;
    /*水平方向的间距(单位为：dp)*/
    private int horizontalSpace;
    /*顶部的间距(单位为：dp)*/
    private int topSpace;
    /*底部的间距(单位为：dp)*/
    private int bottomSpace;


    public GridDecoration2(Context context, @IntRange(from = 1) int spanCount, int horizontalSpace, int topSpace, int bottomSpace) {
        this.context = context;
        this.spanCount = spanCount;
        this.horizontalSpace = horizontalSpace;
        this.topSpace = topSpace;
        this.bottomSpace = bottomSpace;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.top = ZPUIDisplayHelper.dp2px(context, topSpace);
        outRect.bottom = ZPUIDisplayHelper.dp2px(context, bottomSpace);

        if (spanCount <= 0) return;
        int index = parent.getChildAdapterPosition(view);
        if ((index + 1) % spanCount == 1) {
            outRect.right = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
        } else if ((index + 1) % spanCount == 0) {
            outRect.left = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
        } else {
            outRect.left = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
            outRect.right = ZPUIDisplayHelper.dp2px(context, horizontalSpace / 2);
        }
    }
}
