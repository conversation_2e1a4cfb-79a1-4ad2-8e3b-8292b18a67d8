package com.hpbr.bosszhipin.common.decoration;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * @author: 冯智健
 * @date: 2025年03月27日 13:58
 * @description:
 */
public class HeadTailSpacesItemDecoration extends RecyclerView.ItemDecoration {
    private final int top;
    private final int bottom;

    public HeadTailSpacesItemDecoration(int top, int bottom) {
        this.top = top;
        this.bottom = bottom;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int itemCount = state.getItemCount();
        // 设置顶部间距
        if (position == 0) {
            outRect.top = top;
        }
        // 设置底部间距
        if (position == itemCount - 1) {
            outRect.bottom = bottom;
        }
    }
}
