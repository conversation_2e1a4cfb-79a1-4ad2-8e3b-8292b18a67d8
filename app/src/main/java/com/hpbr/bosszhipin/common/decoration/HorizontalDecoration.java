package com.hpbr.bosszhipin.common.decoration;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import androidx.annotation.IdRes;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * @ClassName ：HorizontalDecoration
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2020/12/23  10:03 AM
 */
public class HorizontalDecoration extends RecyclerView.ItemDecoration {

    private Context mContext;

    private int space;
    /*最后一个Item是否需要Space*/
    private boolean lastIsNeedSpace;

    @SuppressLint("ResourceType")
    public HorizontalDecoration(Context context, @IdRes int drawableId, boolean lastIsNeedSpace) {
        this(context, lastIsNeedSpace, ContextCompat.getDrawable(context, drawableId) != null ? ContextCompat.getDrawable(context, drawableId).getIntrinsicWidth() : 0);
    }

    public HorizontalDecoration(Context context, boolean lastIsNeedSpace, int space) {
        this.mContext = context;
        this.space = space;
        this.lastIsNeedSpace = lastIsNeedSpace;
    }


    /**
     * @param outRect 用于规定分割线的范围
     * @param view    进行分割线操作的子view
     * @param parent  父view
     * @param state   (这里暂时不使用)
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int index = parent.getChildAdapterPosition(view);
        if (parent.getAdapter() != null) {
            if (lastIsNeedSpace) {/*所有Item都需要Space*/
                outRect.bottom = space;
            } else {
                if (index < parent.getAdapter().getItemCount() - 1) {/*设置最后一个Item不画Decoration*/
                    outRect.bottom = space;
                }
            }
        }
    }
}
