package com.hpbr.bosszhipin.common.decoration;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/**
 * recyclerview 竖直方向分割线, 可以设置颜色
 *
 * <AUTHOR>
 * @since 2023-8-7
 */
public class HorizontalItemDecoration extends RecyclerView.ItemDecoration {

    /*最后一个Item是否需要Space*/
    private boolean lastIsNeedSpace;

    private Drawable mDivider;

    private final Rect mBounds = new Rect();

    public HorizontalItemDecoration(Drawable divider, boolean lastIsNeedSpace) {
        mDivider = divider;
        this.lastIsNeedSpace = lastIsNeedSpace;
    }


    @Override
    public void onDraw(Canvas canvas, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(canvas, parent, state);
        int itemCount = parent.getAdapter().getItemCount();

        final int left;
        final int right;
        if (parent.getClipToPadding()) {
            left = parent.getPaddingLeft();
            right = parent.getWidth() - parent.getPaddingRight();
            canvas.clipRect(left, parent.getPaddingTop(), right,
                    parent.getHeight() - parent.getPaddingBottom());
        } else {
            left = 0;
            right = parent.getWidth();
        }

        final int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {

            if (lastIsNeedSpace || i != itemCount - 1) {
                final View child = parent.getChildAt(i);
                parent.getDecoratedBoundsWithMargins(child, mBounds);
                final int bottom = mBounds.bottom + Math.round(child.getTranslationY());
                final int top = bottom - mDivider.getIntrinsicHeight();
                mDivider.setBounds(left, top, right, bottom);
                mDivider.draw(canvas);
            }
        }
    }


    /**
     * @param outRect 用于规定分割线的范围
     * @param view    进行分割线操作的子view
     * @param parent  父view
     * @param state   (这里暂时不使用)
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int index = parent.getChildAdapterPosition(view);
        if (!lastIsNeedSpace && index == parent.getAdapter().getItemCount() - 1) {
            outRect.setEmpty();
        } else {
            // 显示分隔线
            outRect.set(0, 0, 0, mDivider.getIntrinsicHeight());
        }
    }
}
