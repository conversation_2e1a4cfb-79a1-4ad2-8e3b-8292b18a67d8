package com.hpbr.bosszhipin.common.decoration;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.base.App;
import com.twl.utils.DisplayHelper;

import java.io.Serializable;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：SectionDecoration
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2020/10/22  6:45 PM
 */
public class SectionDecoration extends RecyclerView.ItemDecoration {
    private List<NameBean> dataList;

    private DecorationCallback callback;
    private TextPaint textPaint;
    private Paint paint;
    private int topGap;
    private int alignBottom;
    private int alignLeft;
    private Paint.FontMetrics fontMetrics;


    public SectionDecoration(List<NameBean> dataList, Context context, DecorationCallback decorationCallback) {
        Resources res = context.getResources();
        this.dataList = dataList;
        this.callback = decorationCallback;
        //设置悬浮栏的画笔---paint
        paint = new Paint();
        paint.setColor(0xFFF5F5F6);

        //设置悬浮栏中文本的画笔
        textPaint = new TextPaint();
        textPaint.setAntiAlias(true);
        textPaint.setTextSize(DisplayHelper.dp2px(App.get(), 14));
        textPaint.setColor(Color.DKGRAY);
        textPaint.setTextAlign(Paint.Align.LEFT);
        fontMetrics = new Paint.FontMetrics();
        //决定悬浮栏的高度等
        topGap = DisplayHelper.dp2px(App.get(), 25);
        //文本距离底部的距离
        alignBottom = DisplayHelper.dp2px(App.get(), 6);
        //文本距离左边的距离
        alignLeft = DisplayHelper.dp2px(App.get(), 20);
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        int pos = parent.getChildAdapterPosition(view);
        String groupId = callback.getGroupId(pos,dataList);
        if (TextUtils.isEmpty(groupId)) return;
        //只有是同一组的第一个才显示悬浮栏
        if (pos == 0 || isFirstInGroup(pos)) {
            outRect.top = topGap;
            if (TextUtils.equals(dataList.get(pos).getName(), "")) {
                outRect.top = 0;
            }
        } else {
            outRect.top = 0;
        }
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);
        int left = parent.getPaddingLeft();
        int right = parent.getWidth() - parent.getPaddingRight();
        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View view = parent.getChildAt(i);
            int position = parent.getChildAdapterPosition(view);
            String groupId = callback.getGroupId(position,dataList);
            if (TextUtils.isEmpty(groupId)) return;
            String textLine = callback.getGroupFirstLine(position,dataList);
            if (TextUtils.isEmpty(textLine)) {
                float top = view.getTop();
                float bottom = view.getTop();
                c.drawRect(left, top, right, bottom, paint);
                return;
            } else {
                if (position == 0 || isFirstInGroup(position)) {
                    float top = view.getTop() - topGap;
                    float bottom = view.getTop();
                    //绘制悬浮栏
                    c.drawRect(left, top - topGap, right, bottom, paint);
                    //绘制文本
                    c.drawText(textLine, left, bottom, textPaint);
                }
            }
        }
    }

    @Override
    public void onDrawOver(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        int itemCount = state.getItemCount();
        int childCount = parent.getChildCount();
        int left = parent.getPaddingLeft();
        int right = parent.getWidth() - parent.getPaddingRight();
        float lineHeight = textPaint.getTextSize() + fontMetrics.descent;

        String preGroupId = "";
        String groupId = "-1";
        for (int i = 0; i < childCount; i++) {
            View view = parent.getChildAt(i);
            int position = parent.getChildAdapterPosition(view);

            preGroupId = groupId;
            groupId = callback.getGroupId(position,dataList);
            if (TextUtils.isEmpty(groupId) || TextUtils.equals(groupId, preGroupId)) continue;

            String textLine = callback.getGroupFirstLine(position,dataList);
            if (TextUtils.isEmpty(textLine)) continue;

            int viewBottom = view.getBottom();
            float textY = Math.max(topGap, view.getTop());
            //下一个和当前不一样移动当前
            if (position + 1 < itemCount) {
                String nextGroupId = callback.getGroupId(position + 1,dataList);
                //组内最后一个view进入了header
                if (!TextUtils.equals(nextGroupId, groupId) && viewBottom < textY) {
                    textY = viewBottom;
                }
            }
            //textY - topGap决定了悬浮栏绘制的高度和位置
            c.drawRect(left, textY - topGap, right, textY, paint);
            //left+2*alignBottom 决定了文本往左偏移的多少（加-->向左移）
            //textY-alignBottom  决定了文本往右偏移的多少  (减-->向上移)
            c.drawText(textLine, left + alignLeft, textY - alignBottom, textPaint);
        }
    }


    /**
     * 判断是不是组中的第一个位置
     *
     * @param pos
     * @return
     */
    private boolean isFirstInGroup(int pos) {
        if (pos == 0) {
            return true;
        } else {
            // 因为是根据 字符串内容的相同与否 来判断是不是同意组的，所以此处的标记id 要是String类型
            // 如果你只是做联系人列表，悬浮框里显示的只是一个字母，则标记id直接用 int 类型就行了
            String prevGroupId = callback.getGroupId(pos - 1,dataList);
            String groupId = callback.getGroupId(pos,dataList);
            //判断前一个字符串 与 当前字符串 是否相同
            if (TextUtils.equals(prevGroupId, groupId)) {
                return false;
            } else {
                return true;
            }
        }
    }

    //定义一个借口方便外界的调用
    public interface DecorationCallback {
        String getGroupId(int position,List<NameBean> dataList);

        String getGroupFirstLine(int position,List<NameBean> dataList);
    }

    /**
     * 设置悬浮栏上字体大小
     *
     * @param dpValue
     */
    public void setTitleSize(int dpValue) {
        textPaint.setTextSize(TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, App.getApplication().getApplicationContext().getResources().getDisplayMetrics()));
    }

    /**
     * 设置悬浮栏上字体的颜色
     *
     * @param color
     */
    public void setTextColor(@ColorInt int color) {
        textPaint.setColor(color);
    }

    /**
     * 设置悬浮栏的高度
     *
     * @param dpValue
     */
    public void setTopGap(int dpValue) {
        this.topGap = ZPUIDisplayHelper.dp2px(App.get(), dpValue);
    }

    /**
     * 设置悬浮栏上字体距离底部的距离
     *
     * @param dpValue
     */
    public void setAlignBottom(int dpValue) {
        this.alignBottom = ZPUIDisplayHelper.dp2px(App.get(), dpValue);
    }

    /**
     * 设置悬浮栏上字体距离左边的距离
     *
     * @param dpValue
     */
    public void setAlignLeft(int dpValue) {
        this.alignLeft = ZPUIDisplayHelper.dp2px(App.get(), dpValue);
    }

    public void setDataList(List<NameBean> dataList) {
        this.dataList = dataList;
    }

    public List<NameBean> getDataList() {
        return dataList;
    }

    /**
     * 设置悬浮栏的背景颜色
     *
     * @param color
     */
    public void setHeadBgColor(@ColorInt int color) {
        paint.setColor(color);
    }

    public static class NameBean implements Serializable {
        private static final long serialVersionUID = 7281267545354107420L;
        public String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
