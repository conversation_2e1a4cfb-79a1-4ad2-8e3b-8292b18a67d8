package com.hpbr.bosszhipin.common.decoration;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * @ClassName ：VerticalDecoration
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2020/10/19  7:28 PM
 */
public class VerticalDecoration extends RecyclerView.ItemDecoration {
    private Context mContext;
    private Drawable mDrawable;
    private Drawable firstLeftDrawable;
    /*最后一个Item是否需要Space*/
    private boolean lastIsNeedSpace;
    /*第一个Item左侧是否需要Space*/
    private boolean firstILeftsNeedSpace;

    public VerticalDecoration(Context context, int drawableId, boolean lastIsNeedSpace) {
        this.mContext = context;
        this.mDrawable = ContextCompat.getDrawable(this.mContext, drawableId);
        this.lastIsNeedSpace = lastIsNeedSpace;
    }

    public VerticalDecoration(Context context, int drawableId, boolean lastIsNeedSpace, boolean firstILeftsNeedSpace, int firstLeftDrawableId) {
        this.mContext = context;
        this.mDrawable = ContextCompat.getDrawable(this.mContext, drawableId);
        this.lastIsNeedSpace = lastIsNeedSpace;
        this.firstILeftsNeedSpace=firstILeftsNeedSpace;
        this.firstLeftDrawable=ContextCompat.getDrawable(this.mContext, firstLeftDrawableId);
    }

    /**
     * @param outRect 用于规定分割线的范围
     * @param view    进行分割线操作的子view
     * @param parent  父view
     * @param state   (这里暂时不使用)
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int index = parent.getChildAdapterPosition(view);
        if (parent.getAdapter() != null) {
            if (lastIsNeedSpace) {/*所有Item都需要Space*/
                outRect.right = mDrawable.getIntrinsicWidth();
            } else {
                if (index < parent.getAdapter().getItemCount() - 1) {/*设置最后一个Item不画Decoration*/
                    outRect.right = mDrawable.getIntrinsicWidth();
                }
            }

            /*第一个Item左侧是否需要Item*/
            if (firstILeftsNeedSpace && index == 0) {
                outRect.left = firstLeftDrawable.getIntrinsicWidth();
            }
        }
    }
}
