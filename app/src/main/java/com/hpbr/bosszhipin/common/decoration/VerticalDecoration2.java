package com.hpbr.bosszhipin.common.decoration;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：VerticalDecoration2
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/9/5  8:01 下午
 */
public class VerticalDecoration2 extends RecyclerView.ItemDecoration {
    private Context mContext;
    private int space;
    /*最后一个Item是否需要Space*/
    private boolean lastIsNeedSpace;

    public VerticalDecoration2(Context context, int space, boolean lastIsNeedSpace) {
        this.mContext = context;
        this.space = space;
        this.lastIsNeedSpace = lastIsNeedSpace;
    }

    /**
     * @param outRect 用于规定分割线的范围
     * @param view    进行分割线操作的子view
     * @param parent  父view
     * @param state   (这里暂时不使用)
     */
    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int index = parent.getChildAdapterPosition(view);
        if (parent.getAdapter() != null) {
            if (lastIsNeedSpace) {/*所有Item都需要Space*/
                outRect.right = ZPUIDisplayHelper.dp2px(mContext, space);
            } else {
                if (index < parent.getAdapter().getItemCount() - 1) {/*设置最后一个Item不画Decoration*/
                    outRect.right = ZPUIDisplayHelper.dp2px(mContext, space);
                }
            }
        }
    }
}
