package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerProxyCertBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: fanfan
 * Date: 2020-08-27
 */
public class AgentExpatriateExplainDialog {

    private Activity activity;
    private BottomView bottomView;
    private ImageView ivClose;
    private LinearLayout linearLayout;
    private List<ServerProxyCertBean> dataList = new ArrayList<>();
    private boolean dissmissOutside = false;

    public DissmissListener getDissmissListener() {
        return dissmissListener;
    }

    public void setDissmissListener(DissmissListener dissmissListener) {
        this.dissmissListener = dissmissListener;
    }

    private DissmissListener dissmissListener;


    public AgentExpatriateExplainDialog(Activity activity, List<ServerProxyCertBean> dataList) {
        this.activity = activity;
        this.dataList = dataList;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_expatriate_explain_dialog, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        ivClose = view.findViewById(R.id.iv_close);
        linearLayout = view.findViewById(R.id.ll_content);
        if (!LList.isEmpty(dataList)) {
            for (ServerProxyCertBean serverProxyCertBean : dataList) {
                View itemView = LayoutInflater.from(activity).inflate(R.layout.view_item_expatriate_explain_dialog, null);
                MTextView mtvTitle = itemView.findViewById(R.id.tv_title);
                MTextView mtvContent = itemView.findViewById(R.id.tv_content);
                mtvTitle.setText(serverProxyCertBean.certName);
                mtvContent.setText(serverProxyCertBean.certDeclare);
                linearLayout.addView(itemView);
            }
        }
        bottomView.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (dissmissOutside) {
                    if (dissmissListener != null) {
                        dissmissListener.dissmissOutside();
                    }

                }
            }
        });

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        view.findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dissmissOutside = true;
                dismiss();
            }
        });
    }

    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
        }
    }


    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public interface DissmissListener {
        void dissmissOutside();
    }
}
