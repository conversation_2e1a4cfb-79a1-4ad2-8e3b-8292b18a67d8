package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.JobAgentAdapter;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.banner.ZPUIBannerLayout;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class AgentProxyRecruitTipBottomDialog {

    private Activity activity;
    private BottomView bottomView;
    private MTextView mtvTitle;
    private MTextView mButton;
    private MTextView mtvIknow;
    private ImageView ivClose;
    private ZPUIBannerLayout mBlGuide;
    private List<DialogBean> dataList = new ArrayList<>();
    private int currentPosition = 1;

    public AgentProxyRecruitTipBottomDialog(Activity activity) {
        this.activity = activity;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_expatriate_tip_bottom_dialog_new, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        mtvTitle = view.findViewById(R.id.tv_title);
        mtvTitle.setText("代招职位说明");
        ivClose = view.findViewById(R.id.iv_close);
        mtvIknow = view.findViewById(R.id.btn_i_see);
        mButton = view.findViewById(R.id.btn_confirm);
        mBlGuide = view.findViewById(R.id.bl_guide);
        mBlGuide.setAutoPlaying(false);
        initData();
        bindBanner(dataList);
        mBlGuide.getRecyclerView().addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(RecyclerView rv, MotionEvent e) {
                // set return "true" = disables scrolling behaviour on dragging
                return true;
            }

            @Override
            public void onTouchEvent(RecyclerView rv, MotionEvent e) {
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
        mBlGuide.setOnIndicatorIndexChangedListener(new ZPUIBannerLayout.OnIndicatorIndexChangedListener() {
            @Override
            public void onIndexChanged(int position) {
                currentPosition = position + 1;
                switch (position) {

                    case 0:
                        mtvTitle.setText("代招职位说明");
                        mButton.setVisibility(View.VISIBLE);
                        mtvIknow.setVisibility(View.GONE);
                    case 1:
                        mtvTitle.setText("公司资质说明");
                        mButton.setVisibility(View.VISIBLE);
                        mtvIknow.setVisibility(View.GONE);
                        break;
                    case 2:
                        mtvTitle.setText("温馨提示");
                        mButton.setVisibility(View.GONE);
                        mtvIknow.setVisibility(View.VISIBLE);
                        break;
                    default:
                        break;
                }
            }
        });
        mBlGuide.getRecyclerView().addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(RecyclerView rv, MotionEvent e) {
                // set return "true" = disables scrolling behaviour on dragging
                return true;
            }

            @Override
            public void onTouchEvent(RecyclerView rv, MotionEvent e) {
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
        mButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int index = mBlGuide.getLayoutManager().getCurrentPosition();
                AnalyticsFactory.create().action("action-detail-dzhpop-click").param("p", 2).param("p2", currentPosition).param("p3", "下一步").build();
                ++index;
                mBlGuide.getRecyclerView().smoothScrollToPosition(index);

            }
        });

        mtvIknow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AnalyticsFactory.create().action("action-detail-dzhpop-click").param("p", 2).param("p2", 3).param("p3", "我知道啦").build();
                dismiss();
            }
        });

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AnalyticsFactory.create().action("action-detail-dzhpop-click").param("p", 2).param("p2", currentPosition).param("p3", "close").build();
                dismiss();
            }
        });


    }

    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
            AnalyticsFactory.create().action("action-detail-dzhpop-show").param("p", 2).build();
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void bindBanner(@NonNull List<DialogBean> list) {
        JobAgentAdapter adapter = new JobAgentAdapter(list);
        mBlGuide.setAdapter(adapter);
    }

    public static class DialogBean {
        public int pidSourceId;
        public String title;
        public String desc;
        public String tips;
    }

    private void initData() {
        dataList.clear();
        DialogBean firstStep = new DialogBean();

        firstStep.desc = "入职代招职位后，您将与上方展示的用人企业签订劳动合同，并为其进行工作";
        firstStep.pidSourceId = R.mipmap.banner_recruit_stepone;
        dataList.add(firstStep);
        DialogBean secondStep = new DialogBean();

        secondStep.desc = "该人才经纪人企业已取得人社部发放的人力资源服务许可证，具有经营该业务的资质";
        secondStep.pidSourceId = R.mipmap.banner_recruit_steptwo;
        dataList.add(secondStep);
        DialogBean thirdStep = new DialogBean();

        thirdStep.desc = "沟通该职位时，请与人才经纪人确定您最终的人企关系，包括：与谁签订劳动合同、由谁支付工资、由谁缴纳社保等";
        thirdStep.pidSourceId = R.mipmap.banner_hunter_stepthree;
        thirdStep.tips = "如您不想看该类职位，可在“设置-隐私设置”中开启屏蔽";
        dataList.add(thirdStep);
    }



}
