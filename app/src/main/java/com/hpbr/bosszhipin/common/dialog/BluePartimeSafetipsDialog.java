package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.bean.ServerSafeTipOverlayBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class BluePartimeSafetipsDialog {

    private final Activity activity;
    private BottomView bottomView;
    private MTextView mtvTitle;
    private SimpleDraweeView sdvExplain;
    private MTextView mtvDesc;
    private ImageView mIvClose;
    private ZPUIRoundButton mBtnConfirm;

    private final ServerSafeTipOverlayBean partimeSafetyTipBean;
    private final long bossId;
    private final long jobId;

    public BluePartimeSafetipsDialog(Activity activity, ServerSafeTipOverlayBean partimeSafetyTipBean, long bossId, long jobId) {
        this.activity = activity;
        this.partimeSafetyTipBean = partimeSafetyTipBean;
        this.bossId = bossId;
        this.jobId = jobId;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_blue_partime_safetiips, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        mtvTitle = view.findViewById(R.id.tv_title);
        sdvExplain = view.findViewById(R.id.sdv_explain);
        mtvDesc = view.findViewById(R.id.tv_tip);
        mIvClose = view.findViewById(R.id.iv_close);
        mBtnConfirm = view.findViewById(R.id.btn_confirm);

        initData();
    }

    private void initData() {

        if (partimeSafetyTipBean != null) {
            mtvTitle.setText(partimeSafetyTipBean.title, View.GONE);
            mtvDesc.setText(partimeSafetyTipBean.content, View.GONE);
            mIvClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                }
            });
            mBtnConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    AnalyticsFactory.create().action("system-safely-parttimejob-detailpopupclick")
                            .param("p", bossId)
                            .param("p2", jobId)
                            .param("p3", partimeSafetyTipBean.itemType)
                            .build();
                }
            });

            if (partimeSafetyTipBean.picConfig != null) {
                int configWidth = partimeSafetyTipBean.picConfig.width;
                int configHeight = partimeSafetyTipBean.picConfig.height;

                if (configHeight > 0 && configWidth > 0) {
                    float ratio = (float) configHeight / (float) configWidth;

                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) sdvExplain.getLayoutParams();
                    int realWidth = DisplayHelper.getScreenWidth(activity);
                    int reatHeight = (int) (realWidth * ratio);
                    layoutParams.height = reatHeight;
                    sdvExplain.setLayoutParams(layoutParams);
                    sdvExplain.setImageURI(partimeSafetyTipBean.picConfig.url);
                }
            }

        }
    }


    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
            AnalyticsFactory.create().action("system-safely-parttimejob-detailpopup")
                    .param("p", bossId)
                    .param("p2", jobId)
                    .param("p3", partimeSafetyTipBean.itemType)
                    .build();

        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }


}
