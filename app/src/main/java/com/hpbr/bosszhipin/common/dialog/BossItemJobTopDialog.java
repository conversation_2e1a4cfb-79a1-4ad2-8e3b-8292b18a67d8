package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;

import net.bosszhipin.api.bean.ServerJobTopActivityInfoBean;

import zpui.lib.ui.utils.ZPUIDrawableHelper;

/**
 * Author: zhouyou
 * Date: 2019/5/9
 */
public class BossItemJobTopDialog implements View.OnClickListener {

    private Activity activity;
    private Dialog d;
    private ServerJobTopActivityInfoBean data;

    public BossItemJobTopDialog(Activity activity, @NonNull ServerJobTopActivityInfoBean data) {
        this.activity = activity;
        this.data = data;
    }

    private boolean isShow() {
        return activity != null && !activity.isFinishing();
    }

    public boolean show() {
        if (!isShow()) return false;
        if (data == null) return false;
        d = new Dialog(activity, R.style.common_dialog);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_boss_item_job_top_dialog, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.75f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        SimpleDraweeView ivBg = v.findViewById(R.id.iv_bg);
        if (!TextUtils.isEmpty(data.imgUrl)) {
            ivBg.setImageURI(data.imgUrl);
        }

        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) ivBg.getLayoutParams();
        layoutParams.height = 0;
        layoutParams.width = 0;

        float ratio = 0;
        if (data.height > 0) {
            ratio = data.width * 1.0f / data.height;
        }
        layoutParams.dimensionRatio = String.valueOf(ratio);
        ivBg.setLayoutParams(layoutParams);
        ivBg.setOnClickListener(this);

        ImageView ivClose = v.findViewById(R.id.iv_close);
        ivClose.setImageDrawable(generateTintDrawable());
        ivClose.setOnClickListener(this);

        d.show();
        return true;
    }

    private Drawable generateTintDrawable() {
        Drawable origin = ContextCompat.getDrawable(activity, R.mipmap.screenshot_close);
        Drawable drawableTint = null;
        if (origin != null) {
            drawableTint = origin.mutate();
            ZPUIDrawableHelper.setDrawableTintColor(drawableTint, Color.parseColor("#B8B8B8"));
        }
        return drawableTint;
    }

    public void dismiss() {
        if (d != null) {
            d.dismiss();
            d = null;
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_bg) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_COUPON)
                    .param("p", 1)
                    .build();
            dismiss();
            new ZPManager(activity, data.jumpUrl).handler();
        } else if (id == R.id.iv_close) {
            dismiss();
        }
    }
}
