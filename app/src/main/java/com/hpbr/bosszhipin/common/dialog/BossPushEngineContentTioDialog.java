package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.appcompat.app.AlertDialog;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.common.popup.BossPushEngineContentTipTask;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import net.bosszhipin.api.GetPushEngineContentTipResponse;

/**
 * create by guofeng
 * date on 2023/5/16
 */

public class BossPushEngineContentTioDialog {

    private final Activity activity;

    public BossPushEngineContentTioDialog(Activity activity) {
        this.activity = activity;
    }

    @SuppressLint("twl_bad_token")
    public void show() {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_WINDOW_EXPOSE).build();
        GetPushEngineContentTipResponse data = BossPushEngineContentTipTask.getInstance().getData();
        View view = LayoutInflater.from(activity).inflate(R.layout.boss_push_engine_content_tip_dialog, null);

        SimpleDraweeView mBgCoverView = view.findViewById(R.id.mBgCoverView);
        float width = data.width;
        float height = data.height;
        String img = data.img;
        String jumpUrl = data.jumpUrl;

        mBgCoverView.setImageURI(img);


        //宽度固定 高越大 ratio越小
        float serverRatio = width / height;
        float localRatio = 0.7f;
        float ratio = Math.max(serverRatio, localRatio);

        // 设置宽高比
        mBgCoverView.setAspectRatio(ratio);

        final AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setView(view);
        AlertDialog alertDialog = builder.create();
        alertDialog.setCanceledOnTouchOutside(false);
        alertDialog.show();

        Window window = alertDialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        }

        mBgCoverView.setOnClickListener(v -> {

            new ZPManager(activity, jumpUrl).handler();
            alertDialog.dismiss();

        });


        view.findViewById(R.id.mCloseView).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                alertDialog.dismiss();
            }
        });

        BossPushEngineContentTipTask.getInstance().saveDialogShowTime();
    }


} 