package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintProperties;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.monch.lbase.util.LList;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2022/10/18 11:23
 * 选择器底部弹窗
 * <p>
 * 蓝湖：
 * <a href="https://lanhu.kanzhun-inc.com/web/#/item/project/detailDetach?pid=3f1266ab-1dc3-479f-bf3f-5a354208e749&project_id=3f1266ab-1dc3-479f-bf3f-5a354208e749&image_id=fd636445-b097-4357-a5ce-4c2755171815&fromEditor=true">...</a>
 */
public class BottomSelectorDialog implements View.OnClickListener {

    private final Activity activity;

    private BottomView bottomView;

    private ConstraintLayout clDialog;
    private RecyclerView rvList;
    private TextView tvCancel;

    private final int maxDialogHeight;
    private final String headerText;
    private final List<BaseItemBean> itemList;
    private final View.OnClickListener cancelClickListener;

    private ItemAdapter itemAdapter;


    public BottomSelectorDialog(Activity activity, int maxDialogHeight, String headerText, List<BaseItemBean> itemList, View.OnClickListener cancelClickListener) {
        this.activity = activity;
        this.maxDialogHeight = maxDialogHeight;
        this.headerText = headerText;
        this.itemList = itemList;
        this.cancelClickListener = cancelClickListener;
        initView();

    }

    private void initView() {
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_bottom_selector, null);

        clDialog = view.findViewById(R.id.cl_dialog);
        rvList = view.findViewById(R.id.rv_list);
        tvCancel = view.findViewById(R.id.tv_cancel);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        //设置弹窗最大高度
        int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) - ZPUIDisplayHelper.dp2px(activity, 55);
        new ConstraintProperties(clDialog)
                .constrainMaxHeight(maxDialogHeight > 0 ? maxDialogHeight : maxHeight)
                .apply();

        //选项列表适配器
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false);
        rvList.setLayoutManager(linearLayoutManager);
        itemAdapter = new ItemAdapter(getItemList());
        rvList.setAdapter(itemAdapter);
        itemAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                BaseItemBean baseItemBean = (BaseItemBean) adapter.getItem(position);
                if (baseItemBean instanceof ItemBean) {
                    ItemBean itemBean = (ItemBean) baseItemBean;
                    if (itemBean.onClickListener != null) {
                        dismiss();
                        itemBean.onClickListener.onClick(view);
                    }
                } else if (baseItemBean instanceof ItemWithDescBean) {
                    ItemWithDescBean itemWithDescBean = (ItemWithDescBean) baseItemBean;
                    if (itemWithDescBean.onClickListener != null) {
                        dismiss();
                        itemWithDescBean.onClickListener.onClick(view);
                    }
                }


            }
        });

        tvCancel.setOnClickListener(this);

    }


    public void show() {
        bottomView.showBottomView(true);
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    @Override
    public void onClick(View v) {

        int i = v.getId();
        if (i == R.id.tv_cancel) {
            dismiss();
            if (cancelClickListener != null) {
                cancelClickListener.onClick(v);
            }
        }
    }

    private List<BaseItemBean> getItemList() {
        List<BaseItemBean> list = new ArrayList<>();

        DividerBean grayLine_0_0_0_0 = DividerBean.obj().setDividerType(DividerBean.DividerType.TYPE_GRAY_LINE).setMarginLeftDp(0).setMarginRightDp(0);

        if (!TextUtils.isEmpty(headerText)) {
            list.add(new HeaderBean(headerText));
        }

        if (LList.hasElement(itemList)) {
            int size = itemList.size();
            for (int i = 0; i < size; i++) {
                if (itemList.get(i) != null) {
                    list.add(itemList.get(i));
                }

                if (i != size - 1) {
                    list.add(grayLine_0_0_0_0);
                }

            }
        }

        return list;
    }

    /**
     * 构造器
     */
    public static class Builder {

        private final Activity activity;

        private int maxDialogHeight;
        private String headerText;
        private List<BaseItemBean> itemList;
        private View.OnClickListener cancelClickListener;

        public Builder(Activity activity) {
            this.activity = activity;
        }

        public Builder setHeaderText(String headerText) {
            this.headerText = headerText;
            return this;
        }

        public Builder setItem(String itemText, View.OnClickListener onClickListener) {
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            if (!TextUtils.isEmpty(itemText) && onClickListener != null) {
                itemList.add(new ItemBean(itemText, onClickListener));
            }
            return this;
        }

        public Builder setItemBean(BaseItemBean bean) {
            if (itemList == null) {
                itemList = new ArrayList<>();
            }
            if (bean != null) {
                itemList.add(bean);
            }
            return this;
        }

        public Builder setMaxDialogHeight(int maxDialogHeight) {
            this.maxDialogHeight = maxDialogHeight;
            return this;
        }

        public Builder setCancelAction(View.OnClickListener cancelClickListener) {
            this.cancelClickListener = cancelClickListener;
            return this;
        }


        public BottomSelectorDialog build() {
            return new BottomSelectorDialog(activity, maxDialogHeight, headerText, itemList, cancelClickListener);
        }
    }

    /**
     * 列表适配器
     */
    public static class ItemAdapter extends BaseMultipleItemRvAdapter<BaseItemBean, BaseViewHolder> {

        public ItemAdapter(@Nullable List<BaseItemBean> data) {
            super(data);
        }

        @Override
        protected int getViewType(List<BaseItemBean> data, int position) {
            BaseItemBean item = data.get(position);
            return item.getItemType();
        }

        @Override
        protected void registerItemProvider() {
            registerProviders(
                    new HeaderProvider(),
                    new ItemProvider(),
                    new ItemWithDescProvider(),
                    new DividerProvider()
            );
        }

    }

    /**
     * header
     */
    public static class HeaderProvider extends BaseItemProvider<HeaderBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_HEADER;
        }

        @Override
        public int layout() {
            return R.layout.item_bottom_selector_dialog_header;
        }

        @Override
        public void convert(BaseViewHolder helper, HeaderBean data, int position) {
            if (data != null) {
                helper.setText(R.id.tv_header, data.text);
            }
        }

    }

    /**
     * item
     */
    public static class ItemProvider extends BaseItemProvider<ItemBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_ITEM;
        }

        @Override
        public int layout() {
            return R.layout.item_bottom_selector_dialog_item;
        }

        @Override
        public void convert(BaseViewHolder helper, ItemBean data, int position) {
            if (data != null) {
                helper.setText(R.id.tv_item, data.text);
            }
        }

    }

    /**
     * 带描述item
     */
    public static class ItemWithDescProvider extends BaseItemProvider<ItemWithDescBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_ITEM_WITH_DESC;
        }

        @Override
        public int layout() {
            return R.layout.item_bottom_selector_dialog_item_with_desc;
        }

        @Override
        public void convert(BaseViewHolder helper, ItemWithDescBean data, int position) {
            if (data != null) {
                helper.setText(R.id.tv_desc, data.desc);
                TextView tvTitle = helper.getView(R.id.tv_title);
                tvTitle.setText(data.title);
                tvTitle.setCompoundDrawablesWithIntrinsicBounds(data.icon, 0, 0, 0);
            }
        }

    }

    /**
     * 分割线or透明间隔
     */
    public static class DividerProvider extends BaseItemProvider<DividerBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_divider;
        }

        @Override
        public void convert(BaseViewHolder helper, DividerBean data, int position) {
            if (data != null) {
                if (helper.itemView instanceof ConstraintLayout) {
                    ConstraintLayout clRoot = (ConstraintLayout) helper.itemView;
                    ConstraintSet set = new ConstraintSet();
                    set.clone(clRoot);
                    set.setAlpha(R.id.v_divider, isSpaceDivider(data) ? 0 : 1);
                    set.constrainHeight(R.id.v_divider, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isSpaceDivider(data) ? data.getSpaceHeightDp() : 0.3f));
                    set.setMargin(R.id.v_divider, ConstraintSet.LEFT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginLeftDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.TOP, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginTopDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.RIGHT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginRightDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.BOTTOM, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginBottomDp() : 0));

                    set.applyTo(clRoot);

                }
            }
        }

        private boolean isSpaceDivider(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_SPACE;
        }

        private boolean isGrayLine(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_GRAY_LINE;
        }
    }

    /**
     * 列表样式
     */
    public static abstract class BaseItemBean extends BaseEntity implements MultiItemEntity {

        private static final long serialVersionUID = -1404978582145219367L;

        public static final int ITEM_TYPE_HEADER = 1; //列表header
        public static final int ITEM_TYPE_ITEM = 2; // 列表项
        public static final int ITEM_TYPE_DIVIDER = 3; // 分割线
        public static final int ITEM_TYPE_ITEM_WITH_DESC = 4; // 列表项(带描述)
    }

    /**
     * header
     */
    public static class HeaderBean extends BaseItemBean {

        private static final long serialVersionUID = 8486639436647500981L;

        public String text;

        public HeaderBean(String text) {
            this.text = text;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_HEADER;
        }
    }

    /**
     * item
     */
    public static class ItemBean extends BaseItemBean {

        private static final long serialVersionUID = -7268856788756378998L;

        public String text;
        public View.OnClickListener onClickListener;

        public ItemBean(String text, View.OnClickListener onClickListener) {
            this.text = text;
            this.onClickListener = onClickListener;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_ITEM;
        }
    }

    /**
     * item with desc
     */
    public static class ItemWithDescBean extends BaseItemBean {
        private static final long serialVersionUID = -8425427651503105822L;

        @DrawableRes
        public int icon;
        public String title;
        public String desc;
        public View.OnClickListener onClickListener;

        public ItemWithDescBean(int icon, String title, String desc, View.OnClickListener onClickListener) {
            this.icon = icon;
            this.title = title;
            this.desc = desc;
            this.onClickListener = onClickListener;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_ITEM_WITH_DESC;
        }
    }

    /**
     * 分割线or透明间隔
     */
    public static class DividerBean extends BaseItemBean {

        private static final long serialVersionUID = -7225705299049643726L;
        @DividerBean.DividerType
        private int dividerType;
        private float spaceHeightDp;//间隔的高度
        private float marginLeftDp = 20;//灰线
        private float marginTopDp;//灰线
        private float marginRightDp = 20;//灰线
        private float marginBottomDp;//灰线

        private DividerBean() {
        }

        public static DividerBean obj() {
            return new DividerBean();
        }

        public int getDividerType() {
            return dividerType;
        }

        public DividerBean setDividerType(@DividerBean.DividerType int type) {
            this.dividerType = type;
            return this;
        }

        public float getSpaceHeightDp() {
            return spaceHeightDp;
        }

        public DividerBean setSpaceHeightDp(float spaceHeightDp) {
            this.spaceHeightDp = spaceHeightDp;
            return this;
        }

        public float getMarginLeftDp() {
            return marginLeftDp;
        }

        public DividerBean setMarginLeftDp(float marginLeftDp) {
            this.marginLeftDp = marginLeftDp;
            return this;
        }

        public float getMarginTopDp() {
            return marginTopDp;
        }

        public DividerBean setMarginTopDp(float marginTopDp) {
            this.marginTopDp = marginTopDp;
            return this;
        }

        public float getMarginRightDp() {
            return marginRightDp;
        }

        public DividerBean setMarginRightDp(float marginRightDp) {
            this.marginRightDp = marginRightDp;
            return this;
        }

        public float getMarginBottomDp() {
            return marginBottomDp;
        }

        public DividerBean setMarginBottomDp(float marginBottomDp) {
            this.marginBottomDp = marginBottomDp;
            return this;
        }


        @Override
        public int getItemType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }


        @IntDef({
                DividerBean.DividerType.TYPE_SPACE,
                DividerBean.DividerType.TYPE_GRAY_LINE
        })
        @Retention(RetentionPolicy.SOURCE)
        public @interface DividerType {
            int TYPE_SPACE = 1;//间隔
            int TYPE_GRAY_LINE = 2;//灰线
        }
    }

} 