package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import net.bosszhipin.api.bean.ServerBrandComBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: zhouyou
 * Date: 2020/12/19
 */
public class BrandExistDialog {

    private Activity activity;
    private BottomView bottomView;
    private ServerBrandComBean brandCom;

    private OnJoinCallBack callBack;

    public void setCallBack(OnJoinCallBack callBack) {
        this.callBack = callBack;
    }

    public BrandExistDialog(Activity activity, ServerBrandComBean brandCom) {
        this.activity = activity;
        this.brandCom = brandCom;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_brand_exist_dialog, null);
        view.findViewById(R.id.iv_cancel).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

        MTextView tvTitle = view.findViewById(R.id.tv_title);
        SimpleDraweeView ivLogo = view.findViewById(R.id.iv_logo);
        MTextView tvBrandName = view.findViewById(R.id.tv_brand_name);
        MTextView tvBrandCom = view.findViewById(R.id.tv_brand_com);
        ZPUIRoundButton btnBrandIndustry = view.findViewById(R.id.btn_brand_industry);
        MTextView tvUserCount = view.findViewById(R.id.tv_user_count);

        tvTitle.setText("【" + brandCom.brandName + "】已存在，可直接加入");
        ivLogo.setImageURI(brandCom.brandLogo);
        tvBrandName.setText(brandCom.brandName);
        if (brandCom.showComName) {
            tvBrandCom.setVisibility(View.VISIBLE);
            tvBrandCom.setText(brandCom.comName);
        } else {
            tvBrandCom.setVisibility(View.GONE);
        }
        btnBrandIndustry.setText(brandCom.brandIndustryName);
        tvUserCount.setText(brandCom.userCount + "个Boss");

        view.findViewById(R.id.tv_view_homepage).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (callBack != null) {
                    callBack.onViewHomePage();
                }
            }
        });

        view.findViewById(R.id.btn_join).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (callBack != null) {
                    callBack.onBrandJoin();
                }
            }
        });

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void show() {
        if (activity != null && !activity.isFinishing() && bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public interface OnJoinCallBack {
        void onBrandJoin();

        void onViewHomePage();
    }
}
