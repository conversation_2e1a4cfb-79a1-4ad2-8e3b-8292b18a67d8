package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * created by tong<PERSON><PERSON>un.
 * date: 2024/4/8
 * time: 9:50 AM
 * description:
 */
public class CallVirtualPhoneDialog {

    private final Activity activity;
    private BottomView bottomView;

    private final Runnable callPhoneRunnable;

    private String titleDesc;
    private String buttonText;

    public CallVirtualPhoneDialog(Activity activity,  Runnable callPhoneRunnable, String titleDesc, String buttonText) {
        this.activity = activity;
        this.callPhoneRunnable = callPhoneRunnable;
        this.titleDesc = titleDesc;
        this.buttonText = buttonText;
        init();
    }

    public CallVirtualPhoneDialog(Activity activity, Runnable callPhoneRunnable) {
        this.activity = activity;
        this.callPhoneRunnable = callPhoneRunnable;
        init();
    }


    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_call_virtual_phone_dialog, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        View ivClose = view.findViewById(R.id.iv_close);
        View llButton = view.findViewById(R.id.ll_button);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);

        if (!LText.empty(titleDesc)) {
            tvDesc.setText(titleDesc);
        }
        if (!LText.empty(buttonText)) {
            btnConfirm.setText(buttonText);
        }

        ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

        llButton.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (callPhoneRunnable != null) {
                    callPhoneRunnable.run();
                }
            }
        });
    }

    public void show() {
        if (activity != null && !activity.isFinishing() && bottomView != null) {
            bottomView.showBottomView(true);
        }
    }


    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }
}
