package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.CloseCertViolateRuleRequest;
import net.bosszhipin.api.CloseCertViolateRuleResponse;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerCertViolateRuleDialogBean;
import net.bosszhipin.api.bean.ServerCertViolateRuleItemBean;
import net.bosszhipin.api.bean.ServerHlShotDescBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2022/8/23
 */
public class CertificationViolateRuleDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final ServerCertViolateRuleDialogBean dialog;

    private String buttonUrl;

    public CertificationViolateRuleDialog(Activity activity, @NonNull ServerCertViolateRuleDialogBean dialog) {
        this.activity = activity;
        this.dialog = dialog;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_dialog_certification_violate_rule, null);

        MTextView tvTitle = view.findViewById(R.id.tv_title);
        tvTitle.setText(dialog.title);

        RecyclerView rvWarnList = view.findViewById(R.id.rv_warn_list);
        Adapter adapter = new Adapter(dialog.warningList);
        rvWarnList.setAdapter(adapter);

        rvWarnList.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                rvWarnList.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int rvHeight = 0;
                int size = rvWarnList.getChildCount();
                for (int i = 0; i < size; i++) {
                    View view = rvWarnList.getChildAt(i);
                    if (view != null) {
                        rvHeight += view.getHeight();
                    }
                }

                int maxHeight = (int) (ZPUIDisplayHelper.getScreenHeight(activity) * 0.4);
                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) rvWarnList.getLayoutParams();
                if (rvHeight > maxHeight) {
                    params.height = maxHeight;
                } else {
                    params.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
                }
                rvWarnList.setLayoutParams(params);
            }
        });

        ZPUIRoundButton btnMore = view.findViewById(R.id.btn_more);
        ZPUIRoundButton btnKnow = view.findViewById(R.id.btn_know);
        ServerButtonBean button = dialog.button;

        if (button == null) {
            btnMore.setVisibility(View.GONE);
        } else {
            buttonUrl = button.url;
            btnMore.setVisibility(View.VISIBLE);
            btnMore.setText(button.text);
            btnMore.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    dismiss();
                    new ZPManager(activity, buttonUrl).handler();

                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_REMINDER_CLICK)
                            .param("p", "")
                            .param("p2", buttonUrl)
                            .param("p3", 1)
                            .build();
                }
            });
        }

        btnKnow.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_REMINDER_CLICK)
                        .param("p", dialog.violateRuleTypeIds)
                        .param("p2", buttonUrl)
                        .param("p3", 0)
                        .build();
            }
        });

        view.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_REMINDER_CLICK)
                        .param("p", dialog.violateRuleTypeIds)
                        .param("p2", buttonUrl)
                        .param("p3", 2)
                        .build();
            }
        });

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        closeDialogRequest();
    }

    private static class Adapter extends BaseRvAdapter<ServerCertViolateRuleItemBean, BaseViewHolder> {

        public Adapter(@Nullable List<ServerCertViolateRuleItemBean> data) {
            super(R.layout.item_certification_violate_warn, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerCertViolateRuleItemBean item) {
            if (item == null) {
                return;
            }
            MTextView tvTopDesc = helper.getView(R.id.tv_top_desc);
            tvTopDesc.setText(item.topDesc, View.GONE);

            MTextView tvContent = helper.getView(R.id.tv_content);
            SpannableStringBuilder builder = ViewCommon.setTextHighLight(item.name,
                    item.highlightList,
                    ContextCompat.getColor(mContext, R.color.text_c11));
            tvContent.setText(builder, View.GONE);

            MTextView tvJobInfo = helper.getView(R.id.tv_job_info);
            SpannableStringBuilder sbJobInfo = null;
            if (item.jobInfo != null) {
                ServerHlShotDescBean jobInfo = item.jobInfo;
                sbJobInfo = ViewCommon.setTextHighLight(jobInfo.name,
                        jobInfo.highlightList,
                        ContextCompat.getColor(mContext, R.color.text_c11));
            }
            tvJobInfo.setText(sbJobInfo, View.GONE);

            MTextView tvTailDesc = helper.getView(R.id.tv_tail_desc);
            tvTailDesc.setText(item.tailDesc, View.GONE);
        }
    }

    private void closeDialogRequest() {
        CloseCertViolateRuleRequest request = new CloseCertViolateRuleRequest(new ApiRequestCallback<CloseCertViolateRuleResponse>() {
            @Override
            public void onSuccess(ApiData<CloseCertViolateRuleResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.encId = dialog.encId;
        HttpExecutor.execute(request);
    }

    public void show() {
        if (ActivityUtils.isValid(activity)) {
            bottomView.showBottomView(false);
        }
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_REMINDER_POPUP)
                .param("p", dialog.violateRuleTypeIds)
                .param("p2", buttonUrl)
                .build();
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }
}
