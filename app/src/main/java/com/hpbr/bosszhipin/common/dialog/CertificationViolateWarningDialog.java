package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.CloseCertViolateWarningRequest;
import net.bosszhipin.api.CloseCertViolateWarningResponse;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerCertViolateWarningDialogBean;
import net.bosszhipin.base.ApiRequestCallback;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2022/8/23
 */
public class CertificationViolateWarningDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final ServerCertViolateWarningDialogBean dialog;
    private OnWarningDialogClickCallback callback;

    public CertificationViolateWarningDialog(Activity activity, @NonNull ServerCertViolateWarningDialogBean dialog, OnWarningDialogClickCallback callback) {
        this.activity = activity;
        this.dialog = dialog;
        this.callback = callback;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_dialog_certification_violate_warning, null);

        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvTopDesc = view.findViewById(R.id.tv_top_desc);
        MTextView tvTailDesc = view.findViewById(R.id.tv_tail_desc);
        MTextView tvJobInfo = view.findViewById(R.id.tv_job_info);
        NestedScrollView sv = view.findViewById(R.id.sv);

        tvTitle.setText(dialog.title);

        SpannableStringBuilder sbContent = null;
        if (dialog.warningContent != null) {
            sbContent = ViewCommon.setTextHighLight(dialog.warningContent.name, dialog.warningContent.highlightList, ContextCompat.getColor(activity, R.color.color_FFE03641));
        }
        tvTopDesc.setText(sbContent, View.GONE);
        tvTopDesc.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                tvTopDesc.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int tvHeight = tvTopDesc.getHeight();
                int maxHeight = (int) (ZPUIDisplayHelper.getScreenHeight(activity) * 0.4);
                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) sv.getLayoutParams();
                if (tvHeight > maxHeight) {
                    params.height = maxHeight;
                } else {
                    params.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
                }
                sv.setLayoutParams(params);
            }
        });

        SpannableStringBuilder sbJobInfo = null;
        if (dialog.jobInfo != null) {
            sbJobInfo = ViewCommon.setTextHighLight(dialog.jobInfo.name, dialog.jobInfo.highlightList, ContextCompat.getColor(activity, R.color.text_c11));
        }
        tvJobInfo.setText(sbJobInfo, View.GONE);
        tvTailDesc.setText(dialog.articleTitle, View.GONE);
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);
        MTextView tvView = view.findViewById(R.id.tv_view);



        ServerButtonBean button = dialog.button;
        if (button == null) {
            btnConfirm.setVisibility(View.GONE);
        } else {
            btnConfirm.setVisibility(View.VISIBLE);
            btnConfirm.setText(button.text);
            btnConfirm.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    closeDialogRequest();
                    dismiss();
                    new ZPManager(activity, button.url).handler();
                    if (callback != null) {
                        callback.confirmAction();
                    }

                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_WARNING_CLICK)
                            .param("p", dialog.violateRuleTypeIds)
                            .param("p2", button.url)
                            .build();
                }
            });
        }

        ServerButtonBean secondButton = dialog.secondButton;
        if (secondButton == null) {
            tvView.setVisibility(View.GONE);
        } else {
            tvView.setVisibility(View.VISIBLE);
            tvView.setText(secondButton.text);
            tvView.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    new ZPManager(activity, secondButton.url).handler();
                }
            });
        }

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    private void closeDialogRequest() {
        CloseCertViolateWarningRequest request = new CloseCertViolateWarningRequest(new ApiRequestCallback<CloseCertViolateWarningResponse>() {
            @Override
            public void onSuccess(ApiData<CloseCertViolateWarningResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.encId = dialog.encId;
        HttpExecutor.execute(request);
    }

    public void show() {
        if (ActivityUtils.isValid(activity)) {
            bottomView.showBottomView(false);
        }

        String url = "";
        if (dialog.button != null) {
            url = dialog.button.url;
        }
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_WARNING_POPUP)
                .param("p", dialog.violateRuleTypeIds)
                .param("p2", url)
                .build();
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public interface OnWarningDialogClickCallback {
        void confirmAction();
    }
}
