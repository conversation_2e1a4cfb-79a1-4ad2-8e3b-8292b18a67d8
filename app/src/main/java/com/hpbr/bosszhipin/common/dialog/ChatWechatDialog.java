package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.InputFilter;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.UpdateWeixinRequest;
import net.bosszhipin.api.UpdateWeixinResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by zhouyou on 2015/5/4.
 */
public class ChatWechatDialog {

    private Dialog d;
    private final Activity activity;
    private IWechatClickListener l;

    private String weixin;

    public ChatWechatDialog(Activity mActivity, IWechatClickListener l) {
        this.activity = mActivity;
        this.l = l;
    }

    public void setWeixin(String weixin) {
        this.weixin = weixin;
    }

    public void show() {
        d = new Dialog(activity, R.style.fullScreen_DialogStyle);
        d.setCanceledOnTouchOutside(true);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_chat_set_view, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();

        WindowManager.LayoutParams params = window.getAttributes();
        params.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE;
        params.dimAmount = 0.6f;
        window.setAttributes(params);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);


        final MEditText etInputWechat = (MEditText) v.findViewById(R.id.et_input);
        RelativeLayout rlBlank = (RelativeLayout) v.findViewById(R.id.rl_blank);
        MTextView tvSave = (MTextView) v.findViewById(R.id.tv_save);
        View rootView = v.findViewById(R.id.ll_top);

        rootView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {

            }
        });


        etInputWechat.setFilters(new InputFilter[]{new WeChatFilter()});

        if (!LText.empty(weixin)) {
            etInputWechat.setText(weixin);
            etInputWechat.setSelection(weixin.length());
        }

        tvSave.setOnClickListener(v1 -> onSaveListener(etInputWechat));
        rlBlank.setOnClickListener(v12 -> dismiss());
        etInputWechat.setFocusable(true);
        etInputWechat.requestFocus();
        if (ActivityUtils.isValid(activity)) {
            d.show();
        }
    }

    //保存事件
    private void onSaveListener(MEditText etInputWechat) {
        String number = etInputWechat.getText().toString();
        //不足6位，toast：至少输入6位
        if (number.length() < 6) {
            ToastUtils.showText("至少输入6位");
            return;
        }
        //超过20位，toast：最多输入20位
        if (number.length() > 20) {
            ToastUtils.showText("最多输入20位");
            return;
        }
        //现有不为空和特殊符号的，toast：请输入正确的微信号
        updateWechat(number);
    }

    private void dismiss() {
        if (d != null) {
            d.dismiss();
        }
    }

    public interface IWechatClickListener {
        void setWechat(String input);
    }

    //提交微信号码
    private void updateWechat(String wechat) {

        final ProgressDialog progressDialog = new ProgressDialog(activity);

        UpdateWeixinRequest updateWeixinRequest = new UpdateWeixinRequest(new ApiRequestCallback<UpdateWeixinResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                progressDialog.show("");
            }

            @Override
            public void handleInChildThread(ApiData<UpdateWeixinResponse> data) {
                super.handleInChildThread(data);
                UserManager.setWeiXin(data.resp.encryptWeixin);
            }

            @Override
            public void onSuccess(ApiData<UpdateWeixinResponse> data) {
                if (!TextUtils.isEmpty(wechat)) {
                    if (l != null) {
                        l.setWechat(wechat);
                    }
                }
                dismiss();
            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(activity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        updateWeixinRequest.weixin = wechat;
        HttpExecutor.execute(updateWeixinRequest);

    }


    /**
     * 微信号筛选
     * 可使用6-20位大小写字母、数字、下划线和减号
     */
    private static class WeChatFilter implements InputFilter {

        @Override
        public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
            String regex = "^([A-Za-z0-9_]|[-])+$";
            Pattern compile = Pattern.compile(regex);
            String tSource = source.toString();
            String tOldText = dest.toString();

            if (!LText.empty(tSource)) {
                if (tSource.length() > tOldText.length()) {
                    String newText = tSource.substring(dend, end);
                    Matcher matcher = compile.matcher(newText);
                    if (matcher.matches()) {
                        return null;//返回source
                    } else {
                        return new SpannableString("");//特殊符合用""代替
                    }
                } else {
                    Matcher matcher = compile.matcher(source);
                    if (matcher.matches()) {
                        return null;//返回source
                    } else {
                        return new SpannableString("");//特殊符合用""代替
                    }
                }
            }

            return null;
        }
    }
}
