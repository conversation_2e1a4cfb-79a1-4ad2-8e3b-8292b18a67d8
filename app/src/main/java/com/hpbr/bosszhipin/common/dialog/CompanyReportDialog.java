package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LList;

import java.util.List;

public class CompanyReportDialog {

    private static final String REASON_CANCEL = "取消";
    private BottomView bottomView;
    private OnReportListener listener;

    public void setListener(OnReportListener listener) {
        this.listener = listener;
    }

    public void show(final Context context, @NonNull List<String> items) {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing()))
            return;
        if (!LList.isEmpty(items)) {
            items.add(REASON_CANCEL);
        }
        @SuppressLint("InflateParams") View view = LayoutInflater.from(context).inflate(R.layout.view_report_dialog, null);
        ListView listView = view.findViewById(R.id.list_view);
        listView.setAdapter(new ReportListAdapter(context, items));
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                bottomView.dismissBottomView();
                Object obj = parent.getItemAtPosition(position);
                String reason = (String) obj;
                if (listener != null && !REASON_CANCEL.equals(reason)) {
                    listener.onReportAction(reason);
                }
                bottomView.dismissBottomView();
                bottomView = null;
            }
        });
        bottomView = new BottomView(context, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }


    private static class ReportListAdapter extends LBaseAdapter<String> {

        ReportListAdapter(Context context, List<String> data) {
            super(context, data);
        }

        @SuppressLint("InflateParams")
        @Override
        public View getView(int position, View convertView, String item, LayoutInflater inflater) {
            ReportViewHolder holder;
            if (convertView == null) {
                convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_report_data_view, null);
                holder = new ReportViewHolder(convertView);
                convertView.setTag(holder);
            } else {
                holder = (ReportViewHolder) convertView.getTag();
            }
            if (item != null) {
                holder.tvReportName.setText(item);
                if (REASON_CANCEL.equals(item)) {
                    holder.tvReportName.setTextColor(Color.BLUE);
                } else {
                    holder.tvReportName.setTextColor(ContextCompat.getColor(getContext(), R.color.text_c6));
                }
            }
            return convertView;
        }

        static class ReportViewHolder {

            MTextView tvReportName;

            ReportViewHolder(View convertView) {
                tvReportName = convertView.findViewById(R.id.tv_report_name);
            }
        }
    }

    public interface OnReportListener {
        void onReportAction(String reason);
    }

}
