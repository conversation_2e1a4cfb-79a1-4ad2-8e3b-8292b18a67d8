package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MButton;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;

/**
 * Author: zhouyou
 * Date: 2019-09-04
 */
public class ContactMeDialog {

    private Activity activity;
    private BottomView bv;
    private String phone;
    private String encryptPhone;
    private OnPhoneCommitListener listener;

    /**
     * 是否是编辑态
     */
    private boolean isEdit = false;

    private MTextView tvPhone;
    private MEditText etInput;
    private MButton btnModify;

    public ContactMeDialog(Activity activity, String phone, String encryptPhone, OnPhoneCommitListener listener) {
        this.activity = activity;
        this.phone = phone;
        this.encryptPhone = encryptPhone;
        this.listener = listener;
        initViews();
    }

    private void initViews() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_contact_me_b50_dialog, null);
        bv = new BottomView(activity, R.style.BottomViewTheme_Defalut, view);
        bv.setAnimation(R.style.BottomToTopAnim);
        bv.setOnCancelListener(dialog -> {
            dialog.cancel();
            AppUtil.hideSoftInput(activity);
            AppUtil.finishActivity(activity, ActivityAnimType.NONE);
        });

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                AppUtil.hideSoftInput(activity);
                AppUtil.finishActivity(activity, ActivityAnimType.NONE);
            }
        });

        btnModify = view.findViewById(R.id.btn_modify);
        etInput = view.findViewById(R.id.et_input);
        tvPhone = view.findViewById(R.id.tv_phone);
        tvPhone.setText(phone);
        initMode();

        btnModify.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                switchEdit();
            }
        });

        view.findViewById(R.id.btn_commit).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    if (isEdit) {
                        String input = etInput.getText().toString().trim();
                        if (!LText.isMobile(input)) {
                            AnimUtil.errorInputAnim(etInput, "手机号码格式有误");
                        } else {
                            dismiss();
                            AppUtil.hideSoftInput(activity);
                            listener.onCommit(true, input);
                        }
                    } else {
                        dismiss();
                        AppUtil.hideSoftInput(activity);
                        listener.onCommit(false, encryptPhone);
                    }
                }
            }
        });
    }

    private void initMode() {
        tvPhone.setVisibility(View.VISIBLE);
        etInput.setVisibility(View.GONE);
        btnModify.setText("修改");
        AppUtil.hideSoftInput(activity);
    }

    private void editMode() {
        tvPhone.setVisibility(View.GONE);
        etInput.setVisibility(View.VISIBLE);
        btnModify.setText("使用注册手机号");
        etInput.requestFocus();
        AppUtil.showSoftInput(activity, etInput);
    }

    private void switchEdit() {
        if (isEdit) {
            isEdit = false;
            initMode();
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_PASSIVE_CALL_BACK).build();
        } else {
            isEdit = true;
            editMode();
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_PASSIVE_CALL_EDIT).build();
        }
    }

    /**
     * 展示
     */
    public void show() {
        if (bv != null) {
            bv.showBottomView(true);
        }
    }

    /**
     * 关闭
     */
    public void dismiss() {
        if (bv != null) {
            bv.dismissBottomView();
            bv = null;
        }
    }

    public interface OnPhoneCommitListener {

        void onCommit(boolean isEdit, String phone);
    }
}
