package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.PopupWindow;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BubbleLayout;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.Scale;
import com.twl.ui.popup.XGravity;
import com.twl.ui.popup.YGravity;
import com.twl.ui.popup.ZPUIPopup;

/**
 * @ClassName ：ContentBeyondAbovePopWindow
 * @Description ："描述内容超出字数上限"的提示
 * <AUTHOR> SheYi
 * @Date ：2020/8/27  10:21 AM
 */
public class ContentBeyondAbovePopWindow {
    private ZPUIPopup popup;
    private BubbleLayout blLayout;

    public void show(Activity activity, View anchor,String descContent) {
        if (popup == null) {
            View view = LayoutInflater.from(activity).inflate(R.layout.content_beyond_above_pop_up, null, false);
            blLayout = view.findViewById(R.id.bl_layout);
            MTextView tv_desc_content = view.findViewById(R.id.tv_desc_content);
            blLayout.setVisibility(View.INVISIBLE);
            tv_desc_content.setText(descContent+"");

            view.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    view.getViewTreeObserver().removeOnPreDrawListener(this);
                    int width = blLayout.getMeasuredWidth();
                    blLayout.setArrowPosition(width-Scale.dip2px(activity,30));
                    return true;
                }
            });
            popup = ZPUIPopup.create(activity)
                    .setOutsideTouchable(false)
                    .setTouchable(false)
                    .setInputMethodMode(PopupWindow.INPUT_METHOD_NOT_NEEDED)
                    .setContentView(view)
                    .apply();
        }
        if (popup.isShowing()) {
            popup.dismiss();
        } else {
            popup.showAtAnchorView(anchor, YGravity.BELOW, XGravity.ALIGN_RIGHT, Scale.dip2px(activity,15), -Scale.dip2px(activity,3), true,2000);
            blLayout.setVisibility(View.VISIBLE);
        }
    }
}
