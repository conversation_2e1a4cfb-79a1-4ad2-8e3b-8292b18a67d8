package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import net.bosszhipin.api.bean.WebCallItem;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2022/6/6
 */
public class CustomWeCallStepOneDialog {

    private BottomView bottomView;

    private final Context context;
    private final String mPhoneNumber;
    private final String mTrimmedPhoneNumber;

    private final ActionListener listener;


    public CustomWeCallStepOneDialog(Context context, String phoneNumber, ActionListener listener) {
        this.context = context;
        this.listener = listener;
        if (TextUtils.isEmpty(phoneNumber)) {
            phoneNumber = VersionAndDatasCommon.getInstance().getCustomWebPhone();
        }
        if (TextUtils.isEmpty(phoneNumber)) {
            phoneNumber = "************";
        }
        mPhoneNumber = phoneNumber;
        mTrimmedPhoneNumber = mPhoneNumber.replace("-", "");
    }

    public void show() {
        View v = LayoutInflater.from(context).inflate(R.layout.dialog_custom_web_phone_step_one, null);
        v.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });
        RecyclerView rvCallList = v.findViewById(R.id.rv_call_list);

        WebCallAdapter adapter = new WebCallAdapter(getWebCallList());
        adapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                WebCallItem item = (WebCallItem) adapter.getItem(position);
                if (item == null) {
                    return;
                }
                switch (item.itemType) {
                    case WebCallItem.ITEM_HOT_LINE:
                        if (listener != null) {
                            listener.onHotLineConnect(mTrimmedPhoneNumber);
                        }
                        dismiss();
                        break;
                    default:
                        break;
                }
            }
        });
        rvCallList.setAdapter(adapter);

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, v);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    private List<WebCallItem> getWebCallList() {
        List<WebCallItem> webCallList = new ArrayList<>();
        if (UserGrayFeatureManager.getInstance().isInternetPhoneGray()) {
            WebCallItem item0 = new WebCallItem(WebCallItem.ITEM_WEB_CALL, "语音通话", "无需话费，图文+通话沟通更方便");
            webCallList.add(item0);
        }

        WebCallItem item1 = new WebCallItem(WebCallItem.ITEM_HOT_LINE, "拨打热线", "咨询热线：" + mPhoneNumber);
        webCallList.add(item1);
        return webCallList;
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    private static class WebCallAdapter extends BaseRvAdapter<WebCallItem, BaseViewHolder> {

        public WebCallAdapter(@Nullable List<WebCallItem> data) {
            super(R.layout.item_web_call, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, WebCallItem item) {
            if (item == null) return;
            FrameLayout flParent = helper.getView(R.id.fl_parent);
            flParent.setPadding(0, 0, 0, ZPUIDisplayHelper.dp2px(mContext, helper.getAdapterPosition() == 0 ? 12f : 0));

            ImageView ivIcon = helper.getView(R.id.iv_icon);
            MTextView tvWebCallTitle = helper.getView(R.id.tv_web_call_title);
            MTextView tvWebCallDesc = helper.getView(R.id.tv_web_call_desc);

            ZPUIRoundButton btnTag = helper.getView(R.id.btn_tag);
            if (item.itemType == WebCallItem.ITEM_WEB_CALL) {
                btnTag.setVisibility(View.VISIBLE);
            } else {
                btnTag.setVisibility(View.GONE);
            }

            tvWebCallTitle.setText(item.title);
            tvWebCallDesc.setText(item.desc);

            switch (item.itemType) {
                case WebCallItem.ITEM_WEB_CALL:
                    ivIcon.setImageResource(R.mipmap.ic_web_call_icon);
                    break;
                case WebCallItem.ITEM_HOT_LINE:
                    ivIcon.setImageResource(R.mipmap.ic_hot_line_icon);
                    break;
                default:
                    ivIcon.setImageResource(0);
                    break;
            }
        }
    }

    public interface ActionListener {

        void onHotLineConnect(String phoneNumber);
    }
}
