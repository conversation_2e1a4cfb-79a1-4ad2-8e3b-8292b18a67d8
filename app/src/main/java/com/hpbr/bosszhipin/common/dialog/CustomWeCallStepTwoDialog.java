package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.decoration.GridSpacingItemDecoration;

import net.bosszhipin.api.bean.ServiceConsultTypeBean;
import net.bosszhipin.api.bean.WebCallItem;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2022/6/6
 */
public class CustomWeCallStepTwoDialog {

    private BottomView bottomView;
    private final Context context;
    private final ActionListener listener;
    private final List<ServiceConsultTypeBean> list;
    private boolean onBackForward;

    public CustomWeCallStepTwoDialog(Context context, List<ServiceConsultTypeBean> list, ActionListener listener) {
        this.context = context;
        this.listener = listener;
        this.list = list;
    }

    public void show() {
        View v = LayoutInflater.from(context).inflate(R.layout.dialog_custom_web_phone_step_two, null);
        v.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onDismiss();
                }
            }
        });

        RecyclerView rvConsultList = v.findViewById(R.id.rv_consult_list);
        int space = ZPUIDisplayHelper.dp2px(context, 12);
        GridSpacingItemDecoration decoration = new GridSpacingItemDecoration(2, space, false);
        rvConsultList.addItemDecoration(decoration);

        ConsultItemAdapter consultItemAdapter = new ConsultItemAdapter(list);
        consultItemAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                ServiceConsultTypeBean item = (ServiceConsultTypeBean) adapter.getItem(position);
                if (item == null) {
                    return;
                }
                if (listener != null) {
                    listener.onConsultTypeSelect(item);
                }
                dismiss();
            }
        });
        rvConsultList.setAdapter(consultItemAdapter);

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, v);
        bottomView.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if (listener != null) {
                    listener.onDismiss();
                }
            }
        });
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    private static class ConsultItemAdapter extends BaseRvAdapter<ServiceConsultTypeBean, BaseViewHolder> {

        public ConsultItemAdapter(@Nullable List<ServiceConsultTypeBean> data) {
            super(R.layout.item_consult_type, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServiceConsultTypeBean item) {
            if (item == null) return;
            helper.setText(R.id.tv_consult_type, item.navigationName);
        }
    }

    public interface ActionListener {

        void onConsultTypeSelect(@NonNull ServiceConsultTypeBean item);

        void onDismiss();
    }
}
