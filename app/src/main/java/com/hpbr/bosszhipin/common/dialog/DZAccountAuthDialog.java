package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.View;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.JDAccountBindResponse;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

public class DZAccountAuthDialog {

    /**
     * 1106.060 用户账号认证状态灰度
     */
    public static boolean showAccountBindBlockDialog(Activity activity, boolean dianZhangZpSource, JDAccountBindResponse resp,
                                              ZPFunction.Fun0 agreeClickListener) {
        boolean intercept = false;
        /* 1106.060 用户账号认证状态灰度，0否1是*/
        boolean accountAuthStatusGray = UserGrayFeatureManager.getInstance().isAccountAuthStatusGray();
        if (accountAuthStatusGray && null != resp && dianZhangZpSource && ActivityUtils.isValid(activity)) {
            intercept = true;
            DialogUtils d = new DialogUtils.Builder(activity)
                    .setDoubleButton()
                    .setTitle(resp.title)
                    .setTitleIsNullGone(true)
                    .setDesc(ZPUtils.getHighlightedClickDesc(activity, resp.text, resp.highlightList, url -> {
                        if (null != url) {
                            new ZPManager(activity, url).handler();
                        }
                    }))
                    .setPositiveAction("同意", new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            reportAuthAgreeStatus(activity, () -> {
                                if (null != agreeClickListener) {
                                    agreeClickListener.call();
                                }
                            });
                        }
                    })
                    .setNegativeAction("不同意")
                    .build();
            d.show();
        }
        return intercept;
    }

    public static void reportAuthAgreeStatus(Activity activity, Runnable successRun) {
        SimpleApiRequest.POST(LoginURLConfig.URL_GET_USER_ACCOUNT_STATUS_UPDATE)
                .addParam("status", 1)
                .setRequestCallback(new SimpleApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onStart() {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).showProgressDialog();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<HttpResponse> data) {
                        if (null != successRun) {
                            successRun.run();
                        }
                    }

                    @Override
                    public void onComplete() {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).dismissProgressDialog();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (null != reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    }
                }).execute();
    }
}
