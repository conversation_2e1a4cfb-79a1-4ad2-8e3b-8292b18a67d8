package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintProperties;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.module.my.activity.geek.view.DesignWorksAttachmentItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.DesignWorksOptionInfoResponse;
import net.bosszhipin.api.bean.ServerDesignWorksAttachmentBean;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2023/01/03 10:26
 * 选择附件简历
 */
public class DesignWorksAttachmentSelectDialog implements View.OnClickListener {

    private final Activity activity;
    private BottomView bottomView;

    private ZPUIConstraintLayout clDialog;
    private MTextView tvDialogTitle;
    private ImageView ivClose;
    private RecyclerView rvList;
    private ZPUIRoundButton rbPosAction;

    private DesignWorksOptionInfoResponse response;
    private Callback callback;

    private ItemAdapter itemAdapter;
    private List<BaseItemBean> dataList = new ArrayList<>();

    public DesignWorksAttachmentSelectDialog(Activity activity, DesignWorksOptionInfoResponse response, Callback callback) {
        this.activity = activity;
        this.response = response;
        this.callback = callback;
        initView();
    }


    private void initView() {
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_select_design_works_attachment, null);

        clDialog = view.findViewById(R.id.cl_dialog);
        tvDialogTitle = view.findViewById(R.id.tv_dialog_title);
        ivClose = view.findViewById(R.id.iv_close);
        rvList = view.findViewById(R.id.rv_list);
        rbPosAction = view.findViewById(R.id.rb_pos_action);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        //设置弹窗最大高度
        int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) - ZPUIDisplayHelper.dp2px(activity, 55);
        new ConstraintProperties(clDialog)
                .constrainMaxHeight(maxHeight)
                .apply();

        tvDialogTitle.setText("选择作品附件");
        rbPosAction.setText("确定");

        //设置图文列表适配器
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollHorizontally() {
                return false;
            }

            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
        rvList.setLayoutManager(linearLayoutManager);
        itemAdapter = new ItemAdapter(convertData());
        itemAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                AttachmentBean selectItem = (AttachmentBean) adapter.getItem(position);
                selectItem(selectItem);
            }
        });
        rvList.setAdapter(itemAdapter);

        ivClose.setOnClickListener(this);
        rbPosAction.setOnClickListener(this);

    }

    /**
     * 转换列表数据
     */
    private List<BaseItemBean> convertData() {
        if (response != null && LList.hasElement(response.annexWorks)) {
            List<ServerDesignWorksAttachmentBean> annexWorks = response.annexWorks;
            for (ServerDesignWorksAttachmentBean bean : annexWorks) {
                if (bean == null) continue;
                dataList.add(new AttachmentBean(false, bean));
            }
            dataList.add(DividerBean.obj().setDividerType(DividerBean.DividerType.TYPE_SPACE).setSpaceHeightDp(24));
            AttachmentBean attachmentBean = (AttachmentBean) LList.getElement(dataList, 0);
            if (attachmentBean != null) {
                attachmentBean.select = true;
            }
        }
        return dataList;
    }

    /**
     * 设置选中
     */
    @SuppressLint("NotifyDataSetChanged")
    private void selectItem(AttachmentBean bean) {
        if (bean == null) return;
        for (BaseItemBean baseItemBean : dataList) {
            if (baseItemBean instanceof AttachmentBean) {
                AttachmentBean attachmentBean = (AttachmentBean) baseItemBean;
                attachmentBean.select = attachmentBean == bean;
            }

        }
        itemAdapter.notifyDataSetChanged();
    }

    /**
     * 获取选中项
     */
    private ServerDesignWorksAttachmentBean getSelectItem() {
        for (BaseItemBean baseItemBean : dataList) {
            if (baseItemBean instanceof AttachmentBean) {
                AttachmentBean attachmentBean = (AttachmentBean) baseItemBean;
                if (attachmentBean.select && attachmentBean.itemBean != null) {
                    return attachmentBean.itemBean;
                }
            }

        }
        return null;
    }


    @SuppressLint("NotifyDataSetChanged")
    private void refreshList(List<BaseItemBean> list) {
        if (LList.isEmpty(list)) return;
        dataList.clear();
        dataList.addAll(list);
        itemAdapter.notifyDataSetChanged();
    }


    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_close) {
            dismiss();
            if (callback != null) {
                callback.onClickClose();
            }

        } else if (id == R.id.rb_pos_action) {
            dismiss();
            if (callback != null) {
                callback.onClickConfirm(getSelectItem());
            }
        }
    }

    /**
     * 构造器
     */
    public static class Builder {

        private final Activity activity;

        private DesignWorksOptionInfoResponse response;
        private Callback callback;


        public Builder(Activity activity) {
            this.activity = activity;
        }

        public DesignWorksAttachmentSelectDialog.Builder setResponse(DesignWorksOptionInfoResponse response) {
            this.response = response;
            return this;
        }

        public DesignWorksAttachmentSelectDialog.Builder setCallback(Callback callback) {
            this.callback = callback;
            return this;
        }

        public DesignWorksAttachmentSelectDialog build() {
            return new DesignWorksAttachmentSelectDialog(activity, response, callback);
        }
    }

    public interface Callback {
        void onClickClose();

        void onClickConfirm(ServerDesignWorksAttachmentBean bean);
    }

    /**
     * 图文列表适配器
     */
    public static class ItemAdapter extends BaseMultipleItemRvAdapter<BaseItemBean, BaseViewHolder> {

        public ItemAdapter(@Nullable List<BaseItemBean> data) {
            super(data);
        }

        @Override
        protected int getViewType(List<BaseItemBean> data, int position) {
            BaseItemBean item = data.get(position);
            return item.getItemType();
        }

        @Override
        protected void registerItemProvider() {
            registerProviders(
                    new AttachmentProvider(),
                    new DividerProvider()
            );
        }

    }

    public static class AttachmentProvider extends BaseItemProvider<AttachmentBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_ATTACHMENT;
        }

        @Override
        public int layout() {
            return R.layout.item_view_resume_item5;
        }

        @Override
        public void convert(BaseViewHolder helper, AttachmentBean data, int position) {
            if (data != null) {
                DesignWorksAttachmentItemView itemView = helper.getView(R.id.view_item);
                itemView.setValue(data);
            }
        }

    }


    /**
     * 分割线or透明间隔
     */
    public static class DividerProvider extends BaseItemProvider<DividerBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_divider;
        }

        @Override
        public void convert(BaseViewHolder helper, DividerBean data, int position) {
            if (data != null) {
                if (helper.itemView instanceof ConstraintLayout) {
                    ConstraintLayout clRoot = (ConstraintLayout) helper.itemView;
                    ConstraintSet set = new ConstraintSet();
                    set.clone(clRoot);
                    set.setAlpha(R.id.v_divider, isSpaceDivider(data) ? 0 : 1);
                    set.constrainHeight(R.id.v_divider, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isSpaceDivider(data) ? data.getSpaceHeightDp() : 0.3f));
                    set.setMargin(R.id.v_divider, ConstraintSet.LEFT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginLeftDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.TOP, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginTopDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.RIGHT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginRightDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.BOTTOM, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginBottomDp() : 0));

                    set.applyTo(clRoot);

                }
            }
        }

        private boolean isSpaceDivider(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_SPACE;
        }

        private boolean isGrayLine(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_GRAY_LINE;
        }
    }

    /**
     * 列表样式
     */
    public static abstract class BaseItemBean extends BaseEntity implements MultiItemEntity {

        private static final long serialVersionUID = -6482658250633994478L;

        public static final int ITEM_TYPE_ATTACHMENT = 1; //附件item
        public static final int ITEM_TYPE_DIVIDER = 2; // 分割线
    }

    /**
     * 资源文件提示图片
     */
    public static class AttachmentBean extends BaseItemBean {

        private static final long serialVersionUID = 8129177274054191401L;

        public boolean select;
        public ServerDesignWorksAttachmentBean itemBean;

        public AttachmentBean(boolean select, ServerDesignWorksAttachmentBean itemBean) {
            this.select = select;
            this.itemBean = itemBean;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_ATTACHMENT;
        }
    }

    /**
     * 分割线or透明间隔
     */
    public static class DividerBean extends BaseItemBean {

        private static final long serialVersionUID = -1552731056299192620L;
        @DividerType
        private int dividerType;
        private float spaceHeightDp;//间隔的高度
        private float marginLeftDp = 20;//灰线
        private float marginTopDp;//灰线
        private float marginRightDp = 20;//灰线
        private float marginBottomDp;//灰线

        private DividerBean() {
        }

        public static DividerBean obj() {
            return new DividerBean();
        }

        public int getDividerType() {
            return dividerType;
        }

        public DividerBean setDividerType(@DividerType int type) {
            this.dividerType = type;
            return this;
        }

        public float getSpaceHeightDp() {
            return spaceHeightDp;
        }

        public DividerBean setSpaceHeightDp(float spaceHeightDp) {
            this.spaceHeightDp = spaceHeightDp;
            return this;
        }

        public float getMarginLeftDp() {
            return marginLeftDp;
        }

        public DividerBean setMarginLeftDp(float marginLeftDp) {
            this.marginLeftDp = marginLeftDp;
            return this;
        }

        public float getMarginTopDp() {
            return marginTopDp;
        }

        public DividerBean setMarginTopDp(float marginTopDp) {
            this.marginTopDp = marginTopDp;
            return this;
        }

        public float getMarginRightDp() {
            return marginRightDp;
        }

        public DividerBean setMarginRightDp(float marginRightDp) {
            this.marginRightDp = marginRightDp;
            return this;
        }

        public float getMarginBottomDp() {
            return marginBottomDp;
        }

        public DividerBean setMarginBottomDp(float marginBottomDp) {
            this.marginBottomDp = marginBottomDp;
            return this;
        }


        @Override
        public int getItemType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }


        @IntDef({
                DividerType.TYPE_SPACE,
                DividerType.TYPE_GRAY_LINE
        })
        @Retention(RetentionPolicy.SOURCE)
        public @interface DividerType {
            int TYPE_SPACE = 1;//间隔
            int TYPE_GRAY_LINE = 2;//灰线
        }
    }

}


