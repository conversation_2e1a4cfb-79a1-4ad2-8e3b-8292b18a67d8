package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LDate;

import net.bosszhipin.api.SmsItemPreUseResponse;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: Luhao
 * Date: 2019/2/19.
 */
public class EditMessageContentDialog {

    private Activity activity;
    private MEditText etInput;
    private InputUtils inputUtils;
    private BottomView bv;
    private MTextView countTv;

    private SmsItemPreUseResponse data;


    public EditMessageContentDialog(Activity activity, SmsItemPreUseResponse data) {
        this.activity = activity;
        this.data = data;
        inputUtils = new InputUtils(activity, 40);
    }

    public void show() {
        if (activity == null || activity.isFinishing()) return;
        if (data == null) return;
        View parentView = LayoutInflater.from(activity).inflate(R.layout.view_edit_message_dialog, null);
        MTextView tvMessageCount = parentView.findViewById(R.id.tv_message_count);
        SimpleDraweeView ivVipIcon = parentView.findViewById(R.id.iv_vip_icon);
        parentView.findViewById(R.id.close_iv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        etInput = parentView.findViewById(R.id.et_input);

        if (data.vip4LeftCount > 0 && data.normalLeftCount <= 0) {
            ivVipIcon.setVisibility(View.VISIBLE);
            ivVipIcon.setImageURI(data.vip4Url);
        } else {
            ivVipIcon.setVisibility(View.GONE);
        }
        tvMessageCount.setText(getMessageCountString(data.leftCount, data.vip4LeftCount, data.normalLeftCount));
        ZPUIRoundButton btnCommit = parentView.findViewById(R.id.btn_commit);
        if (data.needActivate) {
            btnCommit.setText("激活并发送");
        } else {
            btnCommit.setText(activity.getString(R.string.string_send_immediately_by_left_times, data.consumptionCount, data.leftCount));
        }
        countTv = parentView.findViewById(R.id.input_count_tv);
        MTextView tvSendTextTip = parentView.findViewById(R.id.tv_send_text_tip);
        etInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                inputUtils.checkInputCount(countTv, s.toString());
            }
        });

        String message = data.lastSmsContent;
        if (TextUtils.isEmpty(message)) {
            etInput.setTextWithSelection("你好，我对你的简历非常感兴趣。不知可否深入聊聊");
        } else {
            etInput.setTextWithSelection(message);
        }
        btnCommit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (inputUtils.isInputLargerThanMaxLength(etInput.getTextContent())) {
                    AnimUtil.errorInputAnim(etInput, "超过字数限制");
                    return;
                }
                if (TextUtils.isEmpty(etInput.getTextContent())) {
                    AnimUtil.errorInputAnim(etInput, "不能为空");
                    return;
                }
                dismiss();
                if (commitListener != null) {
                    commitListener.commit(etInput.getTextContent());
                }
            }
        });

        String currentTimeStr = new SimpleDateFormat("HH:mm:ss", Locale.getDefault()).format(new Date());
        boolean timeBetweenTwoTime = LDate.isBetweenTwoTime("23:00:00", "08:00:00", currentTimeStr); /* 23:00 - 08:00 */
        tvSendTextTip.setVisibility(timeBetweenTwoTime ? View.VISIBLE : View.GONE);

        bv = new BottomView(activity, R.style.BottomViewTheme_Transparent, parentView);
        bv.setAnimation(R.style.BottomToTopAnim);
        bv.showBottomView(true);
    }

    private CharSequence getMessageCountString(int leftCount, int vip4LeftCount, int normalLeftCount) {
        CharSequence s;

        if (vip4LeftCount > 0) {
            if (normalLeftCount > 0) {
                s = Html.fromHtml(activity.getString(R.string.string_message_using_count_3, leftCount, vip4LeftCount, normalLeftCount));
            } else {
                s = Html.fromHtml(activity.getString(R.string.string_message_using_count_2, vip4LeftCount));
            }
        } else {
            s = Html.fromHtml(activity.getString(R.string.string_message_using_count_1, leftCount));
        }
        return s;
    }

    public void dismiss() {
        if (bv != null) {
            AppUtil.hideSoftInput(activity, etInput);
            bv.dismissBottomView();
            bv = null;
        }
    }

    public interface CommitListener {
        void commit(String input);
    }

    private CommitListener commitListener;

    public void setCommitListener(CommitListener commitListener) {
        this.commitListener = commitListener;
    }
}
