package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;

public class EntryAppDialog implements View.OnClickListener {
    private Context context;
    private BottomView bottomView;
    private long bindId;
    private String note;

    public EntryAppDialog(Context context, long bindId, boolean isInDetail) {
        this.context = context;
        this.bindId = bindId;
        if (isInDetail) {
            note = "加入BOSS直聘，立即和Boss开聊~";
        } else {
            note = "加入BOSS直聘，查看更多信息~";
        }
    }

    public void show() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_entry_app, null);
        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        MTextView tvNote = (MTextView) view.findViewById(R.id.tv_note);
        tvNote.setText(note);
        view.findViewById(R.id.tv_confirm).setOnClickListener(this);
        bottomView.showBottomView(true);
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    @Override
    public void onClick(View v) {
        dismiss();
        Bundle build = new Bundle();
        build.putLong(Constants.DATA_LONG, bindId);
        LoginRouter.startLoginActivity(context,build, ActivityAnimType.DEFAULT);
    }
}
