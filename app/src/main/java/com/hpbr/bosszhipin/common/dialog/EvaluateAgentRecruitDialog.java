package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.EvaluateAgentRecruitInfoRequest;
import net.bosszhipin.api.EvaluateAgentRecruitInfoResponse;
import net.bosszhipin.api.EvaluateAgentRecruitPostRequest;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;

import message.handler.MessageUtils;
import zpui.lib.ui.ratingbar.ZPUIScaleRatingBar;
import zpui.lib.ui.ratingbar.base.BaseRatingBar;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * <AUTHOR>
 */
public class EvaluateAgentRecruitDialog {

    private final InputUtils mInputUtils;
    private Context context;
    private ZPUIScaleRatingBar ratingbar;
    private MTextView rateComment;
    private FlexboxLayout fbLayout;
    private ZPUIRoundButton commit;
    private EditText edMark;
    private EvaluateAgentRecruitInfoResponse mEvaluateInfoResponse;
    private MTextView mTvDialogTitle;
    private TextView mTvInputNumber;
    private TextView tvStatusText;
    private View mContainerView;
    private BottomView mButtonView;
    private View llContent;
    private String intermediaryId;
    private String securityId;

    private int from;
    private float ratingNum;
    private long msgId;

    public EvaluateAgentRecruitDialog(Context context, String intermediaryId, String securityId) {
        this.context = context;
        this.intermediaryId = intermediaryId;
        this.securityId = securityId;
        mInputUtils = new InputUtils(context, 60);
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public void setFrom(int from) {
        this.from = from;
    }

    public EvaluateAgentRecruitInfoResponse.AttachDetailBean attachDetailBean;

    public void setAttachDetailBean(EvaluateAgentRecruitInfoResponse.AttachDetailBean attachDetailBean) {
        this.attachDetailBean = attachDetailBean;
    }

    private void setRatingNum(float ratingNum) {
        this.ratingNum = ratingNum;
    }

    public void setEvaluateInfoResponse(EvaluateAgentRecruitInfoResponse evaluateInfoResponse) {
        mEvaluateInfoResponse = evaluateInfoResponse;
    }

    public static void start(Context context,
                             final String securityId,
                             String intermediaryId,
                             String source,
                             long msgId,
                             float ratingNum,
                             String agentFriendId, Listener mListener) {
        EvaluateAgentRecruitInfoRequest request = new EvaluateAgentRecruitInfoRequest(new ApiRequestCallback<EvaluateAgentRecruitInfoResponse>() {
            @Override
            public void onSuccess(ApiData<EvaluateAgentRecruitInfoResponse> data) {
                if (TextUtils.isEmpty(data.resp.securityId)) {
                    data.resp.securityId = securityId;
                }
                EvaluateAgentRecruitInfoResponse.AttachDetailBean attachDetail = data.resp.attachDetail;
                EvaluateAgentRecruitDialog evaluateHunterCommon = new EvaluateAgentRecruitDialog(context, intermediaryId, data.resp.securityId);
                evaluateHunterCommon.setEvaluateInfoResponse(data.resp);
                evaluateHunterCommon.setAttachDetailBean(attachDetail);
                evaluateHunterCommon.setMsgId(msgId);
                evaluateHunterCommon.setRatingNum(ratingNum);
                evaluateHunterCommon.setFrom(LText.getInt(source));
                evaluateHunterCommon.setAgentFriendId(agentFriendId);
                evaluateHunterCommon.setListener(mListener);
                evaluateHunterCommon.show();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.intermediaryId = intermediaryId;
        request.securityId = securityId;
        request.source = source;
        request.execute();
    }

    public View createView() {
        mContainerView = LayoutInflater.from(context).inflate(R.layout.evaluate_agent_recruit_ratingbar_view, null);
        commit = mContainerView.findViewById(R.id.tv_commit);
        fbLayout = mContainerView.findViewById(R.id.container_fl);
        rateComment = mContainerView.findViewById(R.id.rate_comment_tv);
        mTvDialogTitle = mContainerView.findViewById(R.id.tv_dialog_title);
        ratingbar = mContainerView.findViewById(R.id.ratingbar);
        edMark = mContainerView.findViewById(R.id.ed_mark);
        llContent = mContainerView.findViewById(R.id.ll_content);
        mTvInputNumber = mContainerView.findViewById(R.id.input_count_tv);
        tvStatusText = mContainerView.findViewById(R.id.tv_status_text);
        mContainerView.findViewById(R.id.mCloseView).setOnClickListener(v -> dismiss());

        if (mEvaluateInfoResponse == null || LList.isEmpty(mEvaluateInfoResponse.list)) {
            TLog.debug("EvaluateStaffServiceDialog", "mEvaluateInfoBean is null");
            return null;
        }
        rateComment.setText(mEvaluateInfoResponse.subTitle);
        edMark.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s == null) {
                    return;
                }
                String str = s.toString().trim();
                mInputUtils.checkInputCount(mTvInputNumber, str);
            }
        });

        mTvDialogTitle.setText(mEvaluateInfoResponse.title, View.GONE);

        mTvInputNumber.setVisibility(View.VISIBLE);
        llContent.setVisibility(mEvaluateInfoResponse.evaluationDetail != null && !LText.isEmptyOrNull(mEvaluateInfoResponse.evaluationDetail.suggest) ? View.VISIBLE : View.GONE);
        ratingbar.setClearRatingEnabled(false);
        commit.setEnabled(false);

        ratingbar.setOnRatingChangeListener(new BaseRatingBar.OnRatingChangeListener() {
            @Override
            public void onRatingChange(BaseRatingBar ratingBar, float rating, boolean fromUser) {
                setValue(rating);
                commit.setEnabled(rating > 0 && !LText.isEmptyOrNull(getLabelValue()));
            }
        });

        if (mEvaluateInfoResponse.evaluationDetail != null) {
            edMark.setText(mEvaluateInfoResponse.evaluationDetail.suggest);
            //默认不显示键盘
            edMark.setFocusableInTouchMode(false);
            edMark.setFocusable(false);
            ratingbar.setRating(mEvaluateInfoResponse.evaluationDetail.starNum);
            commit.setText("重新提交");
            tvStatusText.setText("已匿名评价");
            tvStatusText.setEnabled(true);
        } else {
            ratingbar.setRating(this.ratingNum);
            commit.setText("匿名提交");
            tvStatusText.setText("匿名");
            tvStatusText.setEnabled(false);
        }

        //点击显示键盘
        edMark.setOnTouchListener((v, event) -> {
            edMark.setFocusableInTouchMode(true);
            edMark.setFocusable(true);
            return false;
        });


        //评论
        commit.setOnClickListener(v -> {
            AppUtil.hideSoftInput(context, edMark);
            httpReport();
        });
        return mContainerView;
    }

    public void show(View view) {
        if (view == null) {
            return;
        }
        mButtonView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        mButtonView.setAnimation(R.style.BottomToTopAnim);
        mButtonView.setOnDismissListener(dialog -> MessageUtils.sendChatViewReplaceReceiver(context, LText.getInt(intermediaryId), msgId));
        mButtonView.setKeyboardListener(new BottomView.KeyboardListener() {
            @Override
            public void onKeyboardStatus(boolean show) {
                if (llContent.isShown()) {
                    fbLayout.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            fbLayout.setVisibility(show ? View.GONE : View.VISIBLE);
                        }
                    }, 150);
                }
                edMark.setMinLines(show ? 2 : 1);
            }
        });
        mButtonView.showBottomView(true);
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_ASSESS_HUNTER_JOB).secId(securityId).param("p3", from).build();
    }

    public void show() {
        show(createView());
    }

    /**
     * 获得选择标签
     *
     * @return
     */
    private String getLabelValue() {
        int childCount = fbLayout.getChildCount();
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < childCount; i++) {
            View child = fbLayout.getChildAt(i);
            if (child instanceof CheckBox) {
                CheckBox checkBox = (CheckBox) child;
                if (checkBox.isChecked()) {
                    String value = checkBox.getText().toString();
                    stringBuffer.append(",").append(value);
                }
            }
        }
        if (stringBuffer.length() > 0) {
            return stringBuffer.substring(1);
        }
        return "";
    }

    /**
     * 选择选择星Id
     *
     * @return
     */
    private long getRatingId() {
        if (ratingbar.getVisibility() == View.VISIBLE) {
            int index = (int) (ratingbar.getRating() - 1);
            EvaluateAgentRecruitInfoResponse.EvaluateBean item = LList.getElement(mEvaluateInfoResponse.list, index);
            if (item != null) {
                return item.starNum;
            }
        } else {
            return -1;
        }
        return 0;
    }

    private void httpReport() {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_ASSESS_HUNTER_JOB_COMFIRM).secId(securityId).param("p2", from).build();
        long ratingId = getRatingId();
        EvaluateAgentRecruitPostRequest request = new EvaluateAgentRecruitPostRequest(new ApiRequestCallback<HttpResponse>() {

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {

                if (mListener != null) {
                    mListener.onEvaluteSuccess(ratingId);
                }

                if (attachDetailBean == null || LList.isEmpty(attachDetailBean.tags)) {
                    ToastUtils.showText("感谢您的反馈～");
                } else { // 接口判断是否需要提示【面试体验评价】
                    InterviewExperienceDialog dialog = new InterviewExperienceDialog();
                    dialog.setAttachDetailBean(attachDetailBean);
                    dialog.setSecurityId(securityId);
                    dialog.show();
                }

                //关闭之前的弹窗
                dismiss();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        if (mInputUtils.isInputLargerThanMaxLength(edMark.getText().toString())) {
            AnimUtil.errorInputAnim(edMark, "文字评价不能超过60个字");
            return;
        }
        if (ratingId < 1) {
            ToastUtils.showText("请选择职位推荐满意度");
            return;
        }
        String labelValue = getLabelValue();
        if (LText.isEmptyOrNull(labelValue)) {
            ToastUtils.showText("请选择评价标签");
            return;
        }
        request.star = ratingId;
        request.tags = labelValue;
        request.content = edMark.getText().toString();
        request.intermediaryId = intermediaryId;
        request.securityId = mEvaluateInfoResponse.securityId;
        request.messageId = msgId;
        request.source = from;
        request.execute();
    }




    private void dismiss() {
        if (mButtonView != null) {
            mButtonView.dismissBottomView();
        }
    }


    private String agentFriendId;

    public void setAgentFriendId(String agentFriendId) {
        this.agentFriendId = agentFriendId;
    }

    Listener mListener;

    public void setListener(Listener listener) {
        mListener = listener;
    }

    public interface Listener {
        void onEvaluteSuccess(long star);
    }

    public void setValue(float rating) {
        if (rating > 0) {
            rateComment.setTextColor(ContextCompat.getColor(context, R.color.color_FFFF7847));
        } else {
            rateComment.setTextColor(ContextCompat.getColor(context, R.color.text_c4));
        }
        fbLayout.removeAllViews();
        int index = (int) (rating - 1);
        EvaluateAgentRecruitInfoResponse.EvaluateBean item = LList.getElement(mEvaluateInfoResponse.list, index);
        if (item != null) {
            // 描述
            rateComment.setText(item.starDesc);
            // label
            if (item.tags != null) {
                for (String tag : item.tags) {
                    if (LText.empty(tag)) {
                        continue;
                    }

                    CheckBox boxView = (CheckBox) LayoutInflater.from(context).inflate(R.layout.item_agent_recruit_evaluate_tag_rate, fbLayout, false);
                    if (mEvaluateInfoResponse.evaluationDetail != null && !LList.isEmpty(mEvaluateInfoResponse.evaluationDetail.tags)) {
                        if (mEvaluateInfoResponse.evaluationDetail.tags.contains(tag)) {
                            boxView.setChecked(true);
                        }
                    }
                    boxView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            commit.setEnabled(ratingbar.getRating() > 0 && !LText.isEmptyOrNull(getLabelValue()));
                        }
                    });
                    boxView.setText(tag);
                    fbLayout.addView(boxView);
                }
            }
            if (llContent.getVisibility() == View.GONE) {
                final CheckBox boxView = (CheckBox) LayoutInflater.from(context).inflate(R.layout.item_agent_recruit_evaluate_tag_rate, fbLayout, false);
                boxView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                        fbLayout.removeView(boxView);
                        llContent.setVisibility(View.VISIBLE);
                        edMark.requestFocus();
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_ASSESS_HUNTER_EVALUATION_CLICKEVALUATIONBUTTON).secId(securityId).build();
                    }
                });
                boxView.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.ic_quick_reply_edit, 0, 0, 0);
                boxView.setText("写评价");
                fbLayout.addView(boxView);
            }
        }
    }
}
