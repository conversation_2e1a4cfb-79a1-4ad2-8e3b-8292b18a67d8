package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.NonNull;

/**
 * @ClassName ：FullScreenDialog
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/1/12  1:46 下午
 */
public class FullScreenDialog extends Dialog {

    public FullScreenDialog(@NonNull Context context) {
        this(context,0);
    }

    public FullScreenDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.MATCH_PARENT;
        if (isFullScreen(getOwnerActivity())) {//判断是不是全屏
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
        }
        window.setAttributes(params);
    }

    private boolean isFullScreen(Activity activity) {
        if (activity == null) {
            return false;
        }
        int flags = activity.getWindow().getAttributes().flags;
        return (flags & WindowManager.LayoutParams.FLAG_FULLSCREEN) ==
                WindowManager.LayoutParams.FLAG_FULLSCREEN;
    }

}
