package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.graphics.Typeface;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.enums.ChatBlockRemindType;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.resume.views.ChatBlocGarbageTopView;
import com.hpbr.bosszhipin.module.resume.views.JDResumePreviewView;
import com.hpbr.bosszhipin.module.resume.views.ResumePreviewGeekCardViewJD;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.MButton;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.spannable.SpannableHelper;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.AttachmentResumeVisibilityRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UpdateNotifySettingsRequest;
import net.bosszhipin.api.UpdateNotifySettingsResponse;
import net.bosszhipin.api.bean.HighLightBean;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerChatRemindBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;

/**
 * Author: ZhouYou
 * Date: 2017/11/27.
 */
public class GeekChatNotifyDialog implements View.OnClickListener {

    private Dialog dialog;
    private boolean isChecked;

    private MTextView tvCheckNotRemind;
    private LinearLayout llActions;
    private Activity activity;
    private ZPUIConstraintLayout clResume;

    private final int action;

    private OnChatRemindDialogClickListener listener;
    private long remindType;

    private boolean doNothingWhenCanceling;

    private ResumePreviewGeekCardViewJD resumePreviewGeekCardViewJD;

    private JDResumePreviewView mJDResumePreviewView;


    private ChatBlocGarbageTopView mGarbageCVView;
    private ImageView iv_banner_pic;

    private boolean isResumePreviewNewStyle; // 1107.1 是否展示新样式个人信息

    public void setDoNothingWhenCanceling(boolean doNothingWhenCanceling) {
        this.doNothingWhenCanceling = doNothingWhenCanceling;
    }

    public void setOnChatRemindDialogClickListener(OnChatRemindDialogClickListener listener) {
        this.listener = listener;
    }

    public GeekChatNotifyDialog(Activity activity, ServerChatRemindBean data) {
        this(activity, GeekConsts.Action.ACTION_START_CHAT, data);
    }

    public GeekChatNotifyDialog(Activity activity, int action, ServerChatRemindBean data) {
        this.action = action;
        if (activity == null || activity.isFinishing()) return;
        if (data == null) return;
        this.activity = activity;
        remindType = data.remindType;
        dialog = new Dialog(activity, R.style.common_dialog_3);
        dialog.setCanceledOnTouchOutside(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.dialog_geek_chat_notify, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        dialog.setOnDismissListener(dialog -> {
            if(null!=listener){
                listener.onDismiss();
            }
        });
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        }
        setupData(v, data);
    }

    private void setupData(View view, ServerChatRemindBean data) {
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc_out);
        SimpleDraweeView sdvTopBannerImage = view.findViewById(R.id.sdv_top_banner_image);
        iv_banner_pic = view.findViewById(R.id.iv_banner_pic);
        llActions = view.findViewById(R.id.ll_actions);
        clResume = view.findViewById(R.id.cl_geek_cv);
        tvCheckNotRemind = view.findViewById(R.id.tv_check_not_remind);
        resumePreviewGeekCardViewJD = view.findViewById(R.id.resume_card_view);
        mJDResumePreviewView = view.findViewById(R.id.resume_card_view_with_eye);
        mGarbageCVView = view.findViewById(R.id.top_garbage);
        if (data.showNotRemind) {
            tvCheckNotRemind.setVisibility(View.VISIBLE);
            tvCheckNotRemind.setOnClickListener(this);
        } else {
            tvCheckNotRemind.setVisibility(View.GONE);
            tvCheckNotRemind.setOnClickListener(null);
        }
        setCheck();

        //1217 南北阁协议，没有标题，也没有顶部图
        if(data.remindType == ChatBlockRemindType.TYPE_NAN_BEI_GE && TextUtils.isEmpty(data.title)){
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)tvTitle.getLayoutParams();
            int dp_20 = DisplayHelper.dp2px(activity, 20);
            int dp_10 = DisplayHelper.dp2px(activity, 10);
            layoutParams.setMargins(dp_20,dp_10,dp_20,0);
            tvTitle.setLayoutParams(layoutParams);
        }else{
            if (!TextUtils.isEmpty(data.title)) {
                SpannableStringBuilder builder = new SpannableStringBuilder(data.title);
                if (LList.isNotEmpty(data.highlightTitle)) {
                    SpannableHelper.lightOn(builder, data.highlightTitle, ContextCompat.getColor(activity, R.color.color_Y7_light));
                }
                tvTitle.setText(builder);
            }
        }

        //1217之后开始支持内容高亮
        SpannableStringBuilder builder = new SpannableStringBuilder(data.content);
        List<HighLightBean> highLightBeans = highLightConvert(data.contentHighlight);
        SpannableHelper.lightOn(builder, highLightBeans, ContextCompat.getColor(activity, R.color.color_FF0D9EA3_4D0D9EA3));
        SpannableHelper.clickOn(tvDesc, builder, highLightBeans, new SpannableHelper.OnSpannableItemClick() {
            @Override
            public void onClick(HighLightBean highLightBean, int index) {
                if(null!=highLightBean && !TextUtils.isEmpty(highLightBean.url)){
                    new ZPManager(activity,highLightBean.url).handler();
                }
            }
        });
        tvDesc.setText(builder);

        List<ServerButtonBean> actionList = data.buttonList;
        int count = LList.getCount(actionList);
        if (count == 1) {
            setSingleAction(actionList.get(0));
            setSingleAction(actionList.get(0));
        } else if (count == 2) {
            setDoubleActions(actionList.get(0), actionList.get(1));
        }

        isResumePreviewNewStyle = data.needShowDetailWithEye();
        // 顶部个人信息展示
        if (isResumePreviewNewStyle) {
            clResume.setVisibility(View.VISIBLE);
            mJDResumePreviewView.setVisibility(View.VISIBLE);
            resumePreviewGeekCardViewJD.setVisibility(View.GONE);
            mGarbageCVView.setVisibility(View.GONE);

            UserBean loginUser = UserManager.getLoginUser();
            if (loginUser != null && loginUser.geekInfo != null) {
                mJDResumePreviewView.setData(loginUser, loginUser.geekInfo);
            } else {
                clResume.setVisibility(View.GONE);
            }
        } else if (data.needShowDetailWithoutEye()) {
            clResume.setVisibility(View.VISIBLE);
            UserBean loginUser = UserManager.getLoginUser();
            if (loginUser != null && loginUser.geekInfo != null) {
                resumePreviewGeekCardViewJD.setData(loginUser, loginUser.geekInfo);
            } else {
                clResume.setVisibility(View.GONE);
            }
        } else if (data.is1116GarbageNewType()) {
            clResume.setVisibility(View.VISIBLE);
            iv_banner_pic.setVisibility(View.INVISIBLE);
            mJDResumePreviewView.setVisibility(View.GONE);
            resumePreviewGeekCardViewJD.setVisibility(View.GONE);
            mGarbageCVView.setVisibility(View.VISIBLE);
            mGarbageCVView.setData(data.example);
        } else {
            clResume.setVisibility(View.GONE);
        }

        //style==2 显示顶部图片「通用」
        if(2==data.style && !TextUtils.isEmpty(data.iconUrl)){
            sdvTopBannerImage.setVisibility(View.VISIBLE);
            sdvTopBannerImage.setImageURI(data.iconUrl);
        }else{
            sdvTopBannerImage.setVisibility(View.GONE);
        }
    }

    private void setSingleAction(ServerButtonBean button) {
        if (button == null) return;
        llActions.removeAllViews();
        MButton btn = getButtonStyle(button, R.color.app_green_dark);
        llActions.addView(btn);
    }

    private void setDoubleActions(ServerButtonBean button0, ServerButtonBean button1) {
        if (button0 == null || button1 == null) return;
        llActions.removeAllViews();
        MButton btn0 = getButtonStyle(button0, R.color.text_c3);
        llActions.addView(btn0);
        View view = new View(activity);
        view.setLayoutParams(getButtonDividerLayoutParams());
        view.setBackgroundColor(ContextCompat.getColor(activity, R.color.app_divider));
        llActions.addView(view);
        MButton btn1 = getButtonStyle(button1, R.color.app_green_dark);
        llActions.addView(btn1);
    }

    private MButton getButtonStyle(final ServerButtonBean bean, int color) {
        MButton button = new MButton(activity);
        button.setLayoutParams(getButtonLayoutParams());
        button.setText(bean.text);
        button.setTypeface(Typeface.DEFAULT_BOLD);
        button.setGravity(Gravity.CENTER);
        button.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
        button.setTextColor(ContextCompat.getColor(activity, color));
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                actionDispatcher(bean);
            }
        });
        return button;
    }

    /**
     * actionType 按钮动作类型，0：关闭对话框 1~6：保留 7：开聊 8：URL跳转 9：跳转到上一个页面  11:继续（客户端需要判断是继续哪一个动作，6.04只支持继续开聊和继续投递）
     * 25: 需要授权南北阁开关「1217.801」
     */
    private void actionDispatcher(ServerButtonBean bean) {
        dismiss();
        switch (bean.actionType) {
            case 7: // 开聊
                if (listener != null) {
                    listener.onStartChat();
                }
                break;
            case 8: // Url跳转
                if (listener != null) {
                    if (!TextUtils.isEmpty(bean.url)) {
                        listener.onStartProtocolJump(bean.url);
                    } else {
                        L.e("url -> " + bean.url);
                    }
                }
                break;
            case 9: // 返回上一个页面
                if (!doNothingWhenCanceling) {
                    AppUtil.finishActivity(activity);
                }
                break;
            case 11: // 继续
                switch (action) {
                    case GeekConsts.Action.ACTION_ONE_KEY_SEND_RESUME:
                        if (listener != null) {
                            // 一键投递
                            listener.onOneKeySendResume();
                        }
                        break;
                    case GeekConsts.Action.ACTION_START_CHAT:
                        if (listener != null) {
                            // 立即沟通
                            listener.onStartChat();
                        }
                        break;
                    case GeekConsts.Action.ACTION_SEND_PHONE_CARD:
                        if (listener != null) {
                            // 发送电话卡
                            listener.onSendPhoneCard();
                        }
                        break;
                    case GeekConsts.Action.ACTION_SEND_WECHAT_CARD:
                        if (listener != null) {
                            // 发送电话卡
                            listener.onSendWechat();
                        }
                        break;
                }
                break;
            case 24://调用修改简历状态接口，并发起沟通 「1126.602公开简历不需要展示Check复选框」
                if (listener != null) {//去开聊
                    mJDResumePreviewView.freshEyeIcon(true);
                    updateResumeVisible();
                    listener.onStartChat();
                }
                break;
            case 25://需要授权南北阁开关，再去开聊「1217.801」
                UpdateNotifySettingsRequest updateNotifySettingsRequest = new UpdateNotifySettingsRequest(new SimpleApiRequestCallback<UpdateNotifySettingsResponse>() {
                    @Override
                    public void onComplete() {
                        super.onComplete();
                        //不管成功还是失败，都继续往下走
                        if (listener != null) {//去开聊
                            listener.onStartChat();
                        }
                    }
                });
                updateNotifySettingsRequest.notifyType = 162;//授权南北阁开关
                updateNotifySettingsRequest.settingType = 4;//打开
                updateNotifySettingsRequest.execute();
                break;
            default:
                break;
        }
        //1217 如果有ba，走ba埋点，没有走业务埋点
        if(TextUtils.isEmpty(bean.ba)){
            if (listener != null) {
                listener.bgAction(bean.actionType);
            }
        }else{
            AnalyticsFactory.bgAction(bean.ba);
        }
        if(null!=listener){
            listener.actionDispatcher(bean);
        }

        if (isChecked && !isResumePreviewNewStyle) {
            if (listener != null) {
                listener.onStopChatRemind(remindType);
            }
        }
    }

    private LinearLayout.LayoutParams getButtonLayoutParams() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        params.weight = 1;
        return params;
    }

    private LinearLayout.LayoutParams getButtonDividerLayoutParams() {
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Scale.dip2px(activity, 0.5f), ViewGroup.LayoutParams.MATCH_PARENT);
        params.setMargins(0, Scale.dip2px(activity, 10), 0, Scale.dip2px(activity, 10));
        return params;
    }

    public boolean show() {
        if (dialog != null) {
            dialog.show();
            return true;
        }
        return false;
    }

    private void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.tv_check_not_remind) {
            isChecked = !isChecked;
            setCheck();
        }
    }

    private void setCheck() {
        tvCheckNotRemind.setCompoundDrawablesWithIntrinsicBounds(isChecked ? R.mipmap.ic_checkbox_checked : R.mipmap.ic_checkbox_default, 0, 0, 0);
    }

    private void updateResumeVisible() {
        AttachmentResumeVisibilityRequest request = new AttachmentResumeVisibilityRequest(new SimpleApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                UserBean user = UserManager.getLoginUser();
                if (user != null && user.geekInfo != null) {
                    user.geekInfo.resumeStatus = 0;
                    UserBean mLoginUser = UserManager.getLoginUser();
                    if (mLoginUser != null && mLoginUser.geekInfo != null) {
                        mLoginUser.geekInfo = user.geekInfo;
                        UserManager.save(mLoginUser);
                    }
                }
                sendResumeStatusChangeAction();
            }


            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.isHidden = 0;
        HttpExecutor.execute(request);
    }


    private void sendResumeStatusChangeAction() {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_GEEK_RESUME_HIDE_ACTION);
        intent.putExtra(Constants.DATA_BOOLEAN, false);
        ReceiverUtils.sendBroadcast(activity, intent);
    }

    private List<HighLightBean> highLightConvert(List<ServerHighlightListBean> contentHighlight) {
        List<HighLightBean> highLightBeans = new ArrayList<>();
        if (null != contentHighlight) {
            for (ServerHighlightListBean highlightListBean : contentHighlight) {
                HighLightBean highLightBean = new HighLightBean();
                highLightBean.start = highlightListBean.startIndex;
                highLightBean.end = highlightListBean.endIndex;
                highLightBean.url = highlightListBean.url;
                highLightBeans.add(highLightBean);
            }
        }
        return highLightBeans;
    }

    public interface OnChatRemindDialogClickListener {

        void onStartChat();

        void onOneKeySendResume();

        void onSendPhoneCard();

        void onStartProtocolJump(@NonNull String url);

        void onStopChatRemind(long remindType);

        void bgAction(int actionType);

        default void onSendWechat() {
        }
        default void actionDispatcher(@NonNull ServerButtonBean bean) {

        }
        default void onDismiss() {

        }
    }
}
