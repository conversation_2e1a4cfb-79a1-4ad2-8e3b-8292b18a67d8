package com.hpbr.bosszhipin.common.dialog;

import android.view.LayoutInflater;
import android.view.View;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.DirectCallActivityResponse;
import net.bosszhipin.api.DirectCallOpenRequest;
import net.bosszhipin.api.DirectCallOpenResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class GeekDirectCallActivityDialog implements View.OnClickListener {


    private final BaseActivity activity;
    private final DirectCallActivityResponse data;
    private BottomView bottomView;
    private final int source;
    private final IOpenDirectListener listener;


    public GeekDirectCallActivityDialog(BaseActivity activity, DirectCallActivityResponse data, int source, IOpenDirectListener listener) {
        this.activity = activity;
        this.data = data;
        this.source = source;
        this.listener = listener;
    }


    public boolean show() {

        View v = LayoutInflater.from(activity).inflate(R.layout.view_directcall_activity_dialog, null);
        v.findViewById(R.id.iv_close).setOnClickListener(this);
        v.findViewById(R.id.btn_confirm).setOnClickListener(this);

        MTextView mtvMainTitle = v.findViewById(R.id.tv_title);
        MTextView mtvDesc = v.findViewById(R.id.mtv_exp_desc);
        SimpleDraweeView sdvIcon = v.findViewById(R.id.sdv_activity);
        MTextView mtvItemTitle = v.findViewById(R.id.mtv_act_title);
        MTextView mtvItemDesc = v.findViewById(R.id.mtv_act_desc);


        if (data != null) {
            mtvMainTitle.setText(data.title);
            mtvDesc.setText(data.desc);
            sdvIcon.setImageURI(data.itemIcon);
            mtvItemTitle.setText(data.itemName);
            mtvItemDesc.setText(data.itemNote);
        }
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, v);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
        return true;
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void openCallRequest(String asign) {
        DirectCallOpenRequest request = new DirectCallOpenRequest(new ApiRequestCallback<DirectCallOpenResponse>() {
            @Override
            public void onSuccess(ApiData<DirectCallOpenResponse> data) {
                if (data != null && data.resp != null && data.resp.result) {
                    ToastUtils.showText(data.resp.toast);
                    if (listener != null) {
                        listener.onCallOpend();
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.source = source;
        request.activitySign = asign;
        HttpExecutor.execute(request);
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_close) {
            dismiss();
        } else if (i == R.id.btn_confirm) {
            dismiss();
            if (data != null) {
                openCallRequest(data.activitySign);
            }
            AnalyticsFactory.create().action("directcall-authorization-freeitem-click").param("p", source).build();
        }
    }


    public interface IOpenDirectListener {
        void onCallOpend();
    }
}
