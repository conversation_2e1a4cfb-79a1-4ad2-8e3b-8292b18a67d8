package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MButton;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.GetUseFeedbackReasonResponse;
import net.bosszhipin.api.GetUserFeedbackReasonRequest;
import net.bosszhipin.api.bean.ServerUserFeedbackItemBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class GeekFeedbackDialog {

    private Activity activity;
    private int reasonType;
    private BottomView bottomView;
    private MTextView mtvTitle;
    private MTextView mtvDesc;
    private ImageView ivClose;
    private FlexboxLayout flexboxLayout;
    private MButton mButton;
    private String anotherReasonInputTemp;
    private DialogInterface.OnDismissListener onDismissListener;
    private List<ServerUserFeedbackItemBean> labels = new ArrayList<>();

    public GeekFeedbackDialog(Activity activity, int reasonType, DialogInterface.OnDismissListener onDismissListener) {
        this.activity = activity;
        this.reasonType = reasonType;
        this.onDismissListener = onDismissListener;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.item_privacy_feed_back_dialog, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.setOnDismissListener(this.onDismissListener);
        mtvTitle = view.findViewById(R.id.tv_condition_title);
        mtvDesc = view.findViewById(R.id.tv_condition_desc);
        ivClose = view.findViewById(R.id.iv_close);
        flexboxLayout = view.findViewById(R.id.layout_item_tags);
        mButton = view.findViewById(R.id.tv_ensure);
        mButton.setEnabled(false);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        initData();

    }

    private void initData() {
        requestFeedbackReason();
    }

    public void requestFeedbackReason() {


        GetUserFeedbackReasonRequest reasonRequest = new GetUserFeedbackReasonRequest(new ApiRequestCallback<GetUseFeedbackReasonResponse>() {
            @Override
            public void onSuccess(ApiData<GetUseFeedbackReasonResponse> data) {
                if (data != null && data.resp != null) {
                    GetUseFeedbackReasonResponse reasonResponse = data.resp;

                    mtvTitle.setText(reasonResponse.mainTitle);
                    mtvDesc.setText(reasonResponse.title);

                    if (!LList.isEmpty(reasonResponse.labels)) {
                        labels = reasonResponse.labels;
                        flushFlexBoxlayout(reasonResponse.labels);
                    }
                    mButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            ToastUtils.showText("提交成功！感谢您的反馈");
                            dismiss();
                        }
                    });
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                dismiss();
                ToastUtils.showText(reason.getErrReason());
            }
        });
        reasonRequest.reasonType = this.reasonType;
        HttpExecutor.execute(reasonRequest);
    }

    private void flushFlexBoxlayout(List<ServerUserFeedbackItemBean> labels) {

        for (ServerUserFeedbackItemBean itemBean : labels) {
            TextView tvTag = (TextView) LayoutInflater.from(activity).inflate(R.layout.item_label_feedback_reason, flexboxLayout, false);
            tvTag.setText(itemBean.name);
            tvTag.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    boolean isChecked = !itemBean.isSelected;
                    if (itemBean.name.equals("其他")) {
                        tvTag.setActivated(true);
                        itemBean.isSelected = isChecked;
                        mButton.setEnabled(checkHasSelectedReasons(labels));
                        DialogUtils d = new DialogUtils.InputBuilder(activity)
                                .setTitle("其他的原因")
                                .setMaxInput(100)
                                .setInputText(anotherReasonInputTemp)
                                .setInputHint("请在此处填写，感谢您的反馈！")
                                .setInputCancelListener("取消", input -> {
                                    anotherReasonInputTemp = input;
                                    tvTag.setActivated(false);
                                    itemBean.isSelected = false;
                                    mButton.setEnabled(checkHasSelectedReasons(labels));
                                })
                                .setPositiveAction("确定", input -> {
                                    anotherReasonInputTemp = input;
                                    tvTag.setActivated(true);
                                    itemBean.isSelected = isChecked;
                                    mButton.setEnabled(checkHasSelectedReasons(labels));

                                })
                                .build();
                        d.showInput();

                    } else {
                        tvTag.setActivated(isChecked);
                        itemBean.isSelected = isChecked;
                        mButton.setEnabled(checkHasSelectedReasons(labels));
                    }
                }
            });

            final ViewGroup.LayoutParams lp = tvTag.getLayoutParams();
            final float totaldivider = DisplayHelper.dp2px(activity, 70);
            float tagWidth = (float) ((DisplayHelper.getScreenWidth(activity) - totaldivider) / 3.0);
            lp.width = (int) tagWidth;
            lp.height = ViewGroup.LayoutParams.WRAP_CONTENT;//lp.height=LayoutParams.WRAP_CONTENT;
            tvTag.setLayoutParams(lp);
            flexboxLayout.addView(tvTag);
        }
    }


    private boolean checkHasSelectedReasons(List<ServerUserFeedbackItemBean> labels) {

        boolean hasSelected = false;

        if (!LList.isEmpty(labels)) {
            for (ServerUserFeedbackItemBean rootReason : labels) {
                if (rootReason.isSelected) {
                    hasSelected = true;
                    break;
                }
            }

        }
        return hasSelected;
    }


    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
            AnalyticsFactory.create().action("action-detail-dzhpop-show").param("p", 1).build();
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private String getSelectResultString() {

        String resultString = "";
        if (!LList.isEmpty(labels)) {
            for (ServerUserFeedbackItemBean rootReason : labels) {
                if (rootReason.isSelected) {
                    resultString = StringUtil.connectTextWithChar(",", resultString, rootReason.name);
                }
            }
        }

        return resultString;
    }

    private String getAnotherReasonString() {
        String resultString = "";
        if (!LList.isEmpty(labels)) {
            for (ServerUserFeedbackItemBean rootReason : labels) {
                if (rootReason.name.equals("其他") && rootReason.isSelected) {
                    resultString = anotherReasonInputTemp;
                    break;
                }
            }
        }

        return resultString;
    }

}
