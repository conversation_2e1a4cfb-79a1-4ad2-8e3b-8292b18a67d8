package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.resume.views.FlowLayout;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GetGeekDirectionGuideResponse;
import net.bosszhipin.api.bean.ServerAfterNameIconBean;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.base.SimpleApiRequest;

import zpui.lib.ui.floatlayout.ZPUIFloatLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class GeekHunterToogleDialog implements View.OnClickListener {


    private final Activity activity;
    private final GetGeekDirectionGuideResponse data;
    private BottomView bottomView;
    private MTextView tvPositionName;//职位名称
    private MTextView tvSalaryStatus;//薪资-停止招聘
    private MTextView tvCompanyName;//公司名称
    private MTextView tvStage;//融资状态
    private MTextView tvDistance;
    private MTextView tvEmployer;//雇主名称和职位
    private ImageView ivOnlinePoint;
    private ZPUIFloatLayout flAfterNameIcons;
    private FlowLayout flRequireInfo;//公司要求信息 【10年以上】【本科】
    private SimpleDraweeView ivAvatar;//雇主头像
    private MTextView tvScale; // 710 新增，公司规模：100-499人
    private ConstraintLayout clJob;

    public GeekHunterToogleDialog(Activity activity, GetGeekDirectionGuideResponse data) {
        this.activity = activity;
        this.data = data;
    }


    public boolean show() {
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_hunter_toogle_dialog, null);
        v.findViewById(R.id.iv_close).setOnClickListener(this);
        ZPUIRoundButton btnConfirm = v.findViewById(R.id.btn_confirm);

        MTextView mtvMainTitle = v.findViewById(R.id.tv_title);
        MTextView mtvSubTitle = v.findViewById(R.id.tv_sub_title);
        tvPositionName = v.findViewById(R.id.tv_position_name);
        tvSalaryStatus = v.findViewById(R.id.tv_salary_statue);
        flAfterNameIcons = v.findViewById(R.id.fl_after_name_icons);
        tvCompanyName = v.findViewById(R.id.tv_company_name);
        tvStage = v.findViewById(R.id.tv_stage);
        tvScale = v.findViewById(R.id.tv_scale);
        ivAvatar = v.findViewById(R.id.iv_avatar);
        ivOnlinePoint = v.findViewById(R.id.iv_online_point);
        tvDistance = v.findViewById(R.id.tv_distance);
        tvEmployer = v.findViewById(R.id.tv_employer);
        flRequireInfo = v.findViewById(R.id.fl_require_info);
        clJob  = v.findViewById(R.id.cl_job);

        if (data != null) {

            mtvMainTitle.setText(data.title);
            mtvSubTitle.setText(data.content);

            if (!LList.isEmpty(data.buttonList)) {
                ServerButtonBean buttonBean = LList.getElement(data.buttonList, 0);
                if (buttonBean != null) {
                    btnConfirm.setText(buttonBean.text);
                    btnConfirm.setOnClickListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {

                            new ZPManager(activity, buttonBean.url).handler();
                            AnalyticsFactory.create().action("biz-item-AccurateSearch-F1hunterrecall-settingclick").build();
                            dismiss();
                        }
                    });
                }
            }
            ServerJobCardBean bean = data.job;

            if (bean != null) {
                tvPositionName.setText(bean.jobName);
                tvSalaryStatus.setText(bean.salaryDesc);
                flAfterNameIcons.setVisibility(View.GONE);
                flAfterNameIcons.removeAllViews();
                if (!LList.isEmpty(bean.afterNameIcons)) {
                    flAfterNameIcons.setVisibility(View.VISIBLE);
                    for (ServerAfterNameIconBean icon : bean.afterNameIcons) {
                        if (icon == null || TextUtils.isEmpty(icon.url) || icon.height <= 0 || icon.width <= 0)
                            continue;
                        int iconHeight = ZPUIDisplayHelper.dp2px(activity, 16f);
                        int iconWidth = iconHeight * icon.width / icon.height;

                        SimpleDraweeView ivIcon = new SimpleDraweeView(activity);
                        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(iconWidth, iconHeight);
                        ivIcon.setLayoutParams(params);
                        ivIcon.setImageURI(icon.url);
                        flAfterNameIcons.addView(ivIcon);
                    }
                }


                if (ivOnlinePoint != null) {
                    if (!bean.online) {
                        ivOnlinePoint.setVisibility(View.GONE);
                    } else {
                        ivOnlinePoint.setVisibility(View.VISIBLE);
                    }
                }

                //开始绘制标签
                if (!LList.isEmpty(bean.jobLabels)) {
                    for (String text : bean.jobLabels) {
                        if (TextUtils.isEmpty(text)) continue;
                        flRequireInfo.addView(getLabel(activity, text));
                    }
                }

                tvStage.setText(bean.brandStageName, View.GONE);
                tvScale.setText(bean.brandScaleName, View.GONE);
                tvCompanyName.setText(bean.brandName, View.GONE);
                //绘制标签结束
                ViewCommon.setAvatar(ivAvatar, 0, bean.bossAvatar);
                String employerInfo = StringUtil.connectTextWithChar(" · ", bean.bossName, bean.bossTitle);
                tvEmployer.setText(employerInfo);

                //距离
                String distance = bean.distance;
                //卡片样式为2时，展示对应的城市商圈信息
                distance = StringUtil.connectTextWithChar("  ", distance, bean.cityName, bean.areaDistrict, bean.businessDistrict);
                tvDistance.setText(distance, View.GONE);

                clJob.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        ToastUtils.showText("请先调整隐私设置");
                        AnalyticsFactory.create().action("biz-item-AccurateSearch-F1hunterrecall-jobclick").build();

                    }
                });

            }

        }


        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, v);
        bottomView.setAnimation(R.style.BottomToTopAnim);


        bottomView.showBottomView(true);
        accountExposureRequest();
        return true;
    }


    private FrameLayout getLabel(Context context, String text) {
        FrameLayout layout = new FrameLayout(context);
        layout.setPadding(0, 0, Scale.dip2px(context, 6), 0);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layout.setLayoutParams(params);

        int paddingHorizontal = ZPUIDisplayHelper.dp2px(activity, 7);
        int paddingVertical = ZPUIDisplayHelper.dp2px(activity, 3);
        MTextView textView = new MTextView(context);
        textView.setText(text);
        textView.setSingleLine();
        textView.setGravity(Gravity.CENTER);
        textView.setBackgroundResource(R.drawable.bg_storke_e0e0e0_4_corner);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setTextColor(ContextCompat.getColor(context, R.color.color_FF525252_FF9E9EA1));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        layout.addView(textView);

        return layout;
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void accountExposureRequest() {
        SimpleApiRequest.GET(GeekUrlConfig.URL_GEEK_CLOSE_PARTTIME_TIP).addParam("type",21).addParam("closeType",2).execute();
        AnalyticsFactory.create().action("biz-item-AccurateSearch-F1hunterrecallexpose").build();
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_close) {
            dismiss();
        }
    }
}
