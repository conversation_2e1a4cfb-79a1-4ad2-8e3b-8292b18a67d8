package com.hpbr.bosszhipin.common.dialog;

import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GeekItemF1PopWindowClickRequest;
import net.bosszhipin.api.GeekItemF1PopWindowClickResponse;
import net.bosszhipin.api.GetGeekItemF1PopWindowResponse;
import net.bosszhipin.base.ApiRequestCallback;

import zpui.lib.ui.utils.ZPUIDrawableHelper;

/**
 * Author: zhouyou
 * Date: 2019/5/9
 */
public class GeekItemF1PopWindowDialog implements View.OnClickListener {

    private BaseActivity activity;
    private Dialog d;
    private GetGeekItemF1PopWindowResponse data;

    public GeekItemF1PopWindowDialog(BaseActivity activity, @NonNull GetGeekItemF1PopWindowResponse data) {
        this.activity = activity;
        this.data = data;
    }

    private boolean isShow() {
        return activity != null && !activity.isFinishing();
    }

    public boolean show() {
        if (!isShow()) return false;
        if (data == null) return false;
        d = new Dialog(activity, R.style.common_dialog);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_item_collection_dialog, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.75f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        SimpleDraweeView ivBg = v.findViewById(R.id.iv_bg);
        if (!TextUtils.isEmpty(data.imgUrl)) {
            ivBg.setImageURI(data.imgUrl);
        }

        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) ivBg.getLayoutParams();
        layoutParams.height = 0;
        layoutParams.width = 0;

        float ratio = 0;
        if (data.height > 0) {
            ratio = data.width * 1.0f / data.height;
        }
        layoutParams.dimensionRatio = String.valueOf(ratio);
        ivBg.setLayoutParams(layoutParams);
        ivBg.setOnClickListener(this);

        ImageView ivClose = v.findViewById(R.id.iv_close);
        if (data.canShowExit) {
            ivClose.setVisibility(View.VISIBLE);
            ivClose.setImageDrawable(generateTintDrawable());
            ivClose.setOnClickListener(this);
        } else {
            ivClose.setVisibility(View.GONE);
        }
        d.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                closeAction(0);
            }
        });
        d.show();

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CLIENT_EXPOSURE_ENTRANCE)
                .param("p", "search-chat-marketing")
                .param("p2", data.interaction)
                .build();

        return true;
    }

    private Drawable generateTintDrawable() {
        Drawable origin = ContextCompat.getDrawable(activity, R.mipmap.screenshot_close);
        Drawable drawableTint = null;
        if (origin != null) {
            drawableTint = origin.mutate();
            ZPUIDrawableHelper.setDrawableTintColor(drawableTint, Color.parseColor("#B8B8B8"));
        }
        return drawableTint;
    }

    private void closeAction(int interaction) {
        GeekItemF1PopWindowClickRequest request = new GeekItemF1PopWindowClickRequest(new ApiRequestCallback<GeekItemF1PopWindowClickResponse>() {
            @Override
            public void onSuccess(ApiData<GeekItemF1PopWindowClickResponse> data) {

            }

            @Override
            public void onComplete() {
                data = null;
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.businessType = data.businessType;
        request.interaction = interaction;
        HttpExecutor.execute(request);
    }

    public void dismiss() {
        if (d != null) {
            d.dismiss();
            d = null;
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_bg) {
            dismiss();
            closeAction(data.interaction);
            new ZPManager(activity, data.jumpUrl).handler();
        } else if (id == R.id.iv_close) {
            dismiss();
            closeAction(0);
        }
    }
}
