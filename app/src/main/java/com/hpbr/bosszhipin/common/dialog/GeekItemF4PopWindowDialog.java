package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekVipPopWindowCloseRequest;
import net.bosszhipin.api.GeekVipPopWindowCloseResponse;
import net.bosszhipin.api.bean.ServerGeekVipPopWindowInfoBean;
import net.bosszhipin.base.ApiRequestCallback;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * Author: zhouyou
 * Date: 2019/5/9
 */
public class GeekItemF4PopWindowDialog implements View.OnClickListener {

    private final Activity activity;
    private Dialog d;
    private final ServerGeekVipPopWindowInfoBean data;

    public GeekItemF4PopWindowDialog(Activity activity, @NonNull ServerGeekVipPopWindowInfoBean data) {
        this.activity = activity;
        this.data = data;
    }

    public boolean show() {
        d = new Dialog(activity, R.style.common_dialog);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_item_f4_collection_dialog, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.75f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }

        SimpleDraweeView ivBg = v.findViewById(R.id.iv_bg);
        if (!TextUtils.isEmpty(data.imgUrl)) {
            ivBg.setImageURI(data.imgUrl);
        }

        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) ivBg.getLayoutParams();
        layoutParams.height = 0;
        layoutParams.width = 0;

        float ratio = 0;
        if (data.height > 0) {
            ratio = data.width * 1.0f / data.height;
        }
        layoutParams.dimensionRatio = String.valueOf(ratio);
        ivBg.setLayoutParams(layoutParams);
        ivBg.setOnClickListener(this);

        v.findViewById(R.id.iv_close).setOnClickListener(this);

        if (ActivityUtils.isValid(activity)) {
            d.show();
            expose(data.businessType);
            return true;
        }
        return false;
    }

    public void dismiss() {
        if (d != null) {
            d.dismiss();
            d = null;
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_bg) {
            dismiss();
            new ZPManager(activity, data.jumpUrl).handler();
        } else if (id == R.id.iv_close) {
            dismiss();
        }
    }

    private void expose(int businessType) {
        GeekVipPopWindowCloseRequest request = new GeekVipPopWindowCloseRequest(new ApiRequestCallback<GeekVipPopWindowCloseResponse>() {
            @Override
            public void onSuccess(ApiData<GeekVipPopWindowCloseResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.businessType = businessType;
        HttpExecutor.execute(request);
    }
}
