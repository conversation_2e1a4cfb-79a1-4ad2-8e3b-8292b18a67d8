package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.manager.ZPManager;
import net.bosszhipin.api.bean.GeekJDPreChatDialogQueryBean;
import net.bosszhipin.api.bean.GeekJDPreChatDialogQueryButtonBean;
import net.bosszhipin.api.bean.GeekJDPreChatDialogQueryHighBean;
import com.hpbr.bosszhipin.utils.SpannableUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerHighlightListBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2023/12/18 14:17
 *     desc   : 境外地址工作地点
 *     version: 1126.62
 * </pre>
 */
public class GeekJDLanguageCollectDialog {
    private BottomView bottomView;
    private Context context;
    private MTextView tvTitle;
    private MTextView tvDesc;
    private View viewDivider;
    private MTextView tvCancel;
    private MTextView tvSure;
    private GeekJDPreChatDialogQueryBean dialog;
    private Runnable runnable;
    private Runnable cancelRunnable;
    public GeekJDLanguageCollectDialog(Context context, GeekJDPreChatDialogQueryBean dialog) {
        this.context = context;
        this.dialog = dialog;
    }

    public void show() {
        if(null==dialog) return;

        View view = LayoutInflater.from(context).inflate(R.layout.view_jd_language_collect, null);
        initView(view);

        //标题
        tvTitle.setText(dialog.title);

        //内容
        List<ServerHighlightListBean> highlightListBeans = new ArrayList<>();
        for(GeekJDPreChatDialogQueryHighBean highBean: dialog.contentHighlight){
            ServerHighlightListBean highlightListBean = new ServerHighlightListBean();
            highlightListBean.linkUrl = highBean.url;
            highlightListBean.startIndex = highBean.startIndex;
            highlightListBean.endIndex = highBean.endIndex;
            highlightListBeans.add(highlightListBean);
        }
        SpannableStringBuilder highlightSpanBuilder = SpannableUtils.getHighlightSpanBuilder(context, dialog.content, highlightListBeans, new SpannableUtils.HighLightClickListener() {
            @Override
            public void onClickHighLightContent(int index, ServerHighlightListBean highlightListBean) {
                new ZPManager(context, highlightListBean.linkUrl).handler();
            }
        });
        tvDesc.setMovementMethod(LinkMovementMethod.getInstance());
        tvDesc.setText(highlightSpanBuilder);

        //按钮
        //取消
        if(null!=getButtonBean(dialog.buttonList,0)){
            GeekJDPreChatDialogQueryButtonBean buttonBean = getButtonBean(dialog.buttonList,0);
            if(null!=buttonBean){
                tvCancel.setText(buttonBean.text);
                tvCancel.setOnClickListener(v -> {
                    dismiss();
                    if(null!=cancelRunnable){
                        cancelRunnable.run();
                    }
                });
            }
        }

        //确认
        if(null!=getButtonBean(dialog.buttonList,1)){
            GeekJDPreChatDialogQueryButtonBean buttonBean = getButtonBean(dialog.buttonList,1);
            if(null!=buttonBean){
                tvSure.setText(buttonBean.text);
                tvSure.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        dismiss();
                        if(null!=runnable){
                            runnable.run();
                        }
                    }
                });
            }
        }

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.showBottomView(false);
        bottomView.setGravity(Gravity.CENTER);
    }

    private GeekJDPreChatDialogQueryButtonBean getButtonBean(List<GeekJDPreChatDialogQueryButtonBean> buttonList, int actionType){
        if(LList.getCount(buttonList)>0){
            for(GeekJDPreChatDialogQueryButtonBean buttonBean: buttonList){
                if(buttonBean.actionType == actionType){
                    return buttonBean;
                }
            }
        }
        return null;
    }
    private void initView(View view) {
        tvTitle = (MTextView) view.findViewById(R.id.tv_title);
        tvDesc = (MTextView) view.findViewById(R.id.tv_desc);
        viewDivider = (View) view.findViewById(R.id.view_divider);
        tvCancel = (MTextView) view.findViewById(R.id.tv_cancel);
        tvSure = (MTextView) view.findViewById(R.id.tv_sure);
    }
    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public void setClickSureCallBack(Runnable runnable){
        this.runnable = runnable;
    }
    public void setClickCancelCallBack(Runnable runnable){
        this.cancelRunnable = runnable;
    }
}
