package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.UpdateNotifySettingsRequest;
import net.bosszhipin.api.UpdateNotifySettingsResponse;
import net.bosszhipin.base.ApiRequestCallback;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class GeekJDOneSignDialog implements View.OnClickListener {


    private final Activity activity;
    private BottomView bottomView;

    boolean supportCall = true;
    boolean supportWechat = true;
    boolean openVirtual = false;
    private MTextView button;

    public void setListener(View.OnClickListener listener) {
        this.listener = listener;
    }

    private View.OnClickListener listener;

    private ZPUIConstraintLayout clCall;
    private ZPUIConstraintLayout clWechat;
    private ImageView ivCall;
    private ImageView ivWecaht;
    private RelativeLayout blDirectCall;
    private MTextView mtvOpenCall;
    private CheckBox cbCall;

    public GeekJDOneSignDialog(Activity activity) {
        this.activity = activity;
    }


    public boolean show() {
        View v = LayoutInflater.from(activity).inflate(R.layout.view_jd_onekey_apply, null);
        v.findViewById(R.id.iv_close).setOnClickListener(this);
        v.findViewById(R.id.btn_confirm).setOnClickListener(this);


        button = v.findViewById(R.id.btn_confirm);
        clCall = v.findViewById(R.id.rl_call);
        clWechat = v.findViewById(R.id.rl_wechat);
        ivCall = v.findViewById(R.id.iv_select_call);
        ivWecaht = v.findViewById(R.id.iv_select_call_wechat);
        blDirectCall = v.findViewById(R.id.bl_layout);
        mtvOpenCall = v.findViewById(R.id.tv_content2);
        cbCall = v.findViewById(R.id.cb_call);


        if (!NotifyUtils.isVirtualCallSettingOpen()) {
            blDirectCall.setVisibility(View.VISIBLE);
        } else {
            blDirectCall.setVisibility(View.GONE);
        }

        cbCall.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean checked) {
                openVirtual = checked;
            }
        });

        mtvOpenCall.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                SettingRouter.jumpToGeekVirtualCallAuthorizeActivity(activity, 0);
            }
        });


        clCall.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (supportCall) {
                    supportCall = false;
                    clCall.setBackground(null);
                    clCall.setBackgroundColor(ContextCompat.getColor(activity, R.color.color_FEFFFFFF_FF1D1D1F));
                    ivCall.setVisibility(View.GONE);
                } else {
                    supportCall = true;
                    clCall.setBackground(ContextCompat.getDrawable(activity, R.drawable.bg_3_stroke_15b3b3_solidwhite));
                    ivCall.setVisibility(View.VISIBLE);
                }

                if (!supportCall && !supportWechat) {
                    button.setAlpha(0.6f);

                } else {
                    button.setAlpha(1f);

                }
            }
        });

        clWechat.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (supportWechat) {
                    supportWechat = false;
                    clWechat.setBackground(null);
                    clWechat.setBackgroundColor(ContextCompat.getColor(activity, R.color.color_FEFFFFFF_FF1D1D1F));
                    ivWecaht.setVisibility(View.GONE);
                } else {
                    supportWechat = true;
                    clWechat.setBackground(ContextCompat.getDrawable(activity, R.drawable.bg_3_stroke_15b3b3_solidwhite));
                    ivWecaht.setVisibility(View.VISIBLE);
                }

                if (!supportCall && !supportWechat) {
                    button.setAlpha(0.6f);

                } else {
                    button.setAlpha(1f);

                }
            }
        });

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, v);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);

        return true;
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }


    private void updateSetting(int notifyType, int type) {
        UpdateNotifySettingsRequest request = new UpdateNotifySettingsRequest(new ApiRequestCallback<UpdateNotifySettingsResponse>() {
            @Override
            public void onSuccess(ApiData<UpdateNotifySettingsResponse> data) {
                if (notifyType == 115) {
                    NotifyUtils.setVirtualCallSettingOpen(true);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.notifyType = notifyType;
        request.settingType = type;
        HttpExecutor.execute(request);
    }


    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_close) {
            dismiss();
        } else if (i == R.id.btn_confirm) {
            if (!supportCall && !supportWechat) {
                ToastUtils.showText("至少提供一种联系方式哦~");
                button.setAlpha(0.6f);
            } else {
                updateSetting(150, supportCall ? 4 : 5);
                updateSetting(151, supportWechat ? 4 : 5);
                if (openVirtual) {
                    updateSetting(115, 4);
                }
                if (listener != null) {
                    listener.onClick(v);
                }
                dismiss();

            }
        }
    }
}
