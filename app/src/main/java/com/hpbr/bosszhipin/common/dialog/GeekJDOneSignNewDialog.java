package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module_geek_export.JDOneSignButtonType;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.SpannableUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.UpdateNotifySettingsRequest;
import net.bosszhipin.api.UpdateNotifySettingsResponse;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2025/02/11 14:17
 *     desc   : 一键报名弹框
 *     version: 1303.110
 * </pre>
 */
public class GeekJDOneSignNewDialog {
    private static final int NOTIFY_TYPE_PHONE = 170;
    private static final int NOTIFY_TYPE_WECHAT = 171;
    private static final int SETTING_TYPE_ON = 4;
    private static final int SETTING_TYPE_OFF = 5;

    private static final int OPT_TYPE_EXPOSURE = 0;
    private static final int OPT_TYPE_CLOSE = 1;
    private static final int OPT_TYPE_CHAT = 2;
    private static final int OPT_TYPE_SIGNUP = 3;

    private BottomView bottomView;
    private final Context context;
    private Runnable leftRunnable;
    private Runnable rightRunnable;

    private ConstraintLayout clPhoneLayout;
    private CheckBox cbxPhone;
    private MTextView tvPhone;
    private ConstraintLayout clWechatLayout;
    private CheckBox cbxWechat;
    private MTextView tvWechat;
    private ZPUIRoundButton btnLeft;
    private ZPUIRoundButton btnRight;
    private FrameLayout flClose;

    private final @JDOneSignButtonType int buttonType;
    private long bossId;
    private long jobId;
    private long expectId;

    public GeekJDOneSignNewDialog(@NonNull Context context, @JDOneSignButtonType int buttonType) {
        this.context = context;
        this.buttonType = buttonType;
    }

    public void show() {
        if (!ActivityUtils.isValid(context)) return;

        View view = LayoutInflater.from(context).inflate(R.layout.view_jd_one_sign_new, null);
        initView(view);
        initData();
        initListener();

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.showBottomView(false);
        bottomView.setGravity(Gravity.BOTTOM);

        requestOneSignGrayDialogOpt(OPT_TYPE_EXPOSURE);
        trackExposure();
    }

    private void initData() {
        // 设置手机号
        String phone = UserManager.getPhone();
        if (!TextUtils.isEmpty(phone)) {
            tvPhone.setText(ViewCommon.keepPhoneSecret(phone));
        }

        // 设置微信号
        setupWechatInfo(context);

        // 设置默认选中状态
        setupDefaultSelection();

        // 设置按钮状态
        setupButtons();
    }

    private void setupWechatInfo(Context context) {
        String weiXin = UserManager.getWeiXin();
        if (TextUtils.isEmpty(weiXin)) {
            setupEmptyWechat(context);
        } else {
            tvWechat.setText(weiXin);
        }
    }

    private void setupEmptyWechat(Context context) {
        String start = "未添加微信号 ";
        String end = "去添加";
        List<ServerHighlightListBean> highlightList = new ArrayList<>();
        ServerHighlightListBean highlight = new ServerHighlightListBean();
        highlight.startIndex = start.length();
        highlight.endIndex = start.length() + end.length();
        highlightList.add(highlight);

        SpannableStringBuilder spanBuilder = SpannableUtils.getHighlightSpanBuilder(
                context,
                start + end,
                highlightList,
                (index, bean) -> showWechatDialog()
        );
        tvWechat.setMovementMethod(LinkMovementMethod.getInstance());
        tvWechat.setText(spanBuilder);
    }

    private void showWechatDialog() {
        if (ActivityUtils.isValid(context)) {
            new ChatWechatDialog((Activity) context, input -> tvWechat.setText(UserManager.getWeiXin())).show();
        }
    }

    private void setupDefaultSelection() {
        int oneSignPhone = NotifyUtils.getOneSignPhoneSettingType();
        int oneSignWechat = NotifyUtils.getOneSignWechatSettingType();
        //第一次两个都是关
        if (oneSignPhone != SETTING_TYPE_ON && oneSignWechat != SETTING_TYPE_ON) {
            oneSignPhone = SETTING_TYPE_ON;
            oneSignWechat = SETTING_TYPE_OFF;
        }
        //特殊case，两个都是开
        if (oneSignPhone == SETTING_TYPE_ON && oneSignWechat == SETTING_TYPE_ON) {
            oneSignWechat = SETTING_TYPE_OFF;
        }
        //如果选中微信，但是没有微信号，也重置成手机号
        if (oneSignWechat == SETTING_TYPE_ON && TextUtils.isEmpty(UserManager.getWeiXin())) {
            oneSignPhone = SETTING_TYPE_ON;
            oneSignWechat = SETTING_TYPE_OFF;
        }
        cbxPhone.setChecked(oneSignPhone == SETTING_TYPE_ON);
        cbxWechat.setChecked(oneSignWechat == SETTING_TYPE_ON);
    }

    private void setupButtons() {
        if (buttonType == JDOneSignButtonType.TYPE_GRAY_1_SINGLE || buttonType == JDOneSignButtonType.TYPE_GRAY_5_SINGLE) {
            btnLeft.setVisibility(View.GONE);
            btnRight.setText("确认报名");
        } else if (buttonType == JDOneSignButtonType.TYPE_GRAY_2_DOUBLE) {
            btnLeft.setVisibility(View.VISIBLE);
            btnLeft.setText("直接沟通");
            btnRight.setText("确定");
        }
    }

    private void initListener() {
        clPhoneLayout.setOnClickListener(v -> updateSelection(true));
        clWechatLayout.setOnClickListener(v -> updateSelection(false));

        btnLeft.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                handleLeftButtonClick();
            }
        });

        btnRight.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                handleRightButtonClick();
            }
        });

        flClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                handleClose();
            }
        });
    }

    private void updateSelection(boolean isPhoneSelected) {
        cbxPhone.setChecked(isPhoneSelected);
        cbxWechat.setChecked(!isPhoneSelected);
    }

    private void handleLeftButtonClick() {
        dismiss();
        updateNotifySettings();
        if (leftRunnable != null) {
            leftRunnable.run();
        }
        requestOneSignGrayDialogOpt(OPT_TYPE_CHAT);
        trackClick("chat");
    }

    private void handleRightButtonClick() {
        if (cbxWechat.isChecked() && TextUtils.isEmpty(UserManager.getWeiXin())) {
            showWechatDialog();
            return;
        }

        dismiss();
        updateNotifySettings();
        if (rightRunnable != null) {
            rightRunnable.run();
        }
        requestOneSignGrayDialogOpt(OPT_TYPE_SIGNUP);
        trackClick("signup");
    }

    private void handleClose() {
        dismiss();
        requestOneSignGrayDialogOpt(OPT_TYPE_CLOSE);
        trackClick("close");
    }

    private void updateNotifySettings() {
        updateSetting(NOTIFY_TYPE_PHONE, cbxPhone.isChecked() ? SETTING_TYPE_ON : SETTING_TYPE_OFF);
        updateSetting(NOTIFY_TYPE_WECHAT, cbxWechat.isChecked() ? SETTING_TYPE_ON : SETTING_TYPE_OFF);
    }

    private void trackExposure() {
        AnalyticsFactory.create().action("geek-connect-signup-exposure")
                .param("p", bossId)
                .param("p2", jobId)
                .param("p3", expectId)
                .build();
    }

    private void trackClick(String optType) {
        AnalyticsFactory.create().action("geek-connect-signup-click")
                .param("p", bossId)
                .param("p2", jobId)
                .param("p3", expectId)
                .param("p4", optType)
                .build();
    }

    private void initView(View view) {
        clPhoneLayout = view.findViewById(R.id.cl_phone_layout);
        cbxPhone = view.findViewById(R.id.cbx_phone);
        tvPhone = view.findViewById(R.id.tv_phone);
        clWechatLayout = view.findViewById(R.id.cl_wechat_layout);
        cbxWechat = view.findViewById(R.id.cbx_wechat);
        tvWechat = view.findViewById(R.id.tv_wechat);
        btnLeft = view.findViewById(R.id.btn_left);
        btnRight = view.findViewById(R.id.btn_right);
        flClose = view.findViewById(R.id.fl_close);
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public GeekJDOneSignNewDialog setClickLeftCallBack(Runnable runnable) {
        this.leftRunnable = runnable;
        return this;
    }

    public GeekJDOneSignNewDialog setClickRightCallBack(Runnable runnable) {
        this.rightRunnable = runnable;
        return this;
    }

    public void requestOneSignGrayDialogOpt(int optType) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_ZPGEEK_APP_JD_START_CHAT_PROCESS_DIALOG_SAVE)
                .addParam("startChatProcessExpGroup", buttonType)//开聊流程实验组类型
                .addParam("optType", optType)//0-曝光，1-点击X，2-直接开聊，3-确定报名
                .execute();
    }

    private void updateSetting(int notifyType, int type) {
        UpdateNotifySettingsRequest request = new UpdateNotifySettingsRequest(new ApiRequestCallback<UpdateNotifySettingsResponse>() {
            @Override
            public void onSuccess(ApiData<UpdateNotifySettingsResponse> data) {
                if (null != data && null != data.resp && TextUtils.isEmpty(data.resp.toast)) {
                    if(notifyType == NOTIFY_TYPE_PHONE){
                        NotifyUtils.setOneSignPhoneSettingType(type);
                    }else if(notifyType == NOTIFY_TYPE_WECHAT){
                        NotifyUtils.setOneSignWechatSettingType(type);
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.notifyType = notifyType;
        request.settingType = type;
        request.execute();
    }

    public GeekJDOneSignNewDialog setParams(long bossId, long jobId, long expectId) {
        this.bossId = bossId;
        this.jobId = jobId;
        this.expectId = expectId;
        return this;
    }
}
