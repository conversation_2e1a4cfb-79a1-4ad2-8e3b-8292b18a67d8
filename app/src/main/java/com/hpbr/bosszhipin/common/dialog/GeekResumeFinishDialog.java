package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.utils.ActivityUtils;

/**
 * Author: fanfan
 */
public class GeekResumeFinishDialog implements View.OnClickListener {

    private Dialog dialog;
    private Activity activity;
    private MTextView mtvSee;
    private MTextView mtvChat;
    private MTextView mtvTitle;
    private MTextView mtvDesc;
    public static final int CONTENT_TYPE_WORK_CONTENT = 1;
    public static final int CONTENT_TYPE_WORK_ADVANTAGE = 2;
    private int contentType;

    private Callback callback;


    public GeekResumeFinishDialog(Activity activity, Callback callback, int contentType) {

        if (activity == null || activity.isFinishing()) return;

        this.activity = activity;
        this.callback = callback;
        this.contentType = contentType;
        dialog = new Dialog(activity, R.style.common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.dialog_geek_resume_finish, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        }


        mtvChat = v.findViewById(R.id.tv_chat);
        mtvSee = v.findViewById(R.id.tv_see);
        mtvTitle = v.findViewById(R.id.tv_title);
        mtvDesc = v.findViewById(R.id.tv_desc_out);


        String title = "工作内容保存成功";
        String desc = "您填写的工作内容已成功保存至在线简历，可前往查看或修改";
        if (contentType == CONTENT_TYPE_WORK_CONTENT) {
            desc = "您填写的工作内容已成功保存至在线简历，可前往查看或修改";
            title = "工作内容保存成功";
        } else if (contentType == CONTENT_TYPE_WORK_ADVANTAGE) {
            desc = "您填写的个人优势已成功保存至在线简历，可前往查看或修改";
            title = "个人优势保存成功";
        }

        mtvTitle.setText(title);
        mtvDesc.setText(desc);
        mtvChat.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (callback != null) {
                    callback.onClickChat();
                    dismiss();
                    AnalyticsFactory.create().action("limit-popup-altered-click")
                            .param("p", contentType)
                            .param("p2", 2)
                            .build();
                }
            }
        });

        mtvSee.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (callback != null) {
                    callback.onClickSee();
                    dismiss();
                    AnalyticsFactory.create().action("limit-popup-altered-click")
                            .param("p", contentType)
                            .param("p2", 1)
                            .build();
                }
            }
        });


    }


    public boolean show() {
        if (dialog != null && ActivityUtils.isValid(activity)) {
            dialog.show();
            AnalyticsFactory.create().action("limit-popup-altered-show").param("p", contentType).build();
            return true;
        }
        return false;
    }

    private void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
    }


    public interface Callback {

        void onClickSee();

        void onClickChat();
    }
}
