package com.hpbr.bosszhipin.common.dialog;

import android.app.Dialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.MTextView;

import net.bosszhipin.api.GeekStudyOverseasAuthenticationResponse;

import zpui.lib.ui.shadow.layout.ZPUILinearLayout;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class GeekStudyOverseasAuthenticationDialog implements View.OnClickListener {

    public static final int GUIDE_TYPE_AT_SCHOOL = 2;// 8.12 1:弹窗样式为3年以上工作经验 2:为三年及以下工作经验
    public static final int GUIDE_TYPE_GRADUATE = 1;

    private BaseActivity activity;
    private GeekStudyOverseasAuthenticationResponse data;
    private Dialog dialog;

    public GeekStudyOverseasAuthenticationDialog(BaseActivity activity, GeekStudyOverseasAuthenticationResponse data) {
        this.activity = activity;
        this.data = data;
    }

    private boolean isShow() {
        return !(activity == null || activity.isFinishing()) && data.needGuide == 1 && !TextUtils.isEmpty(data.guideUrl);
    }

    public boolean show() {
        if (!isShow()) return false;
        dialog = new Dialog(activity, R.style.common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_study_overseas_authentication_new, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.75f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        }

        v.findViewById(R.id.iv_close).setOnClickListener(this);
        v.findViewById(R.id.tv_next).setOnClickListener(this);
        MTextView mtvMainTitle = v.findViewById(R.id.tv_title);
        ZPUILinearLayout llPrivilage = v.findViewById(R.id.ll_privalige);
        ZPUILinearLayout llSections = v.findViewById(R.id.ll_section);
        if (data != null) {
            if (data.guideType == GUIDE_TYPE_AT_SCHOOL) {
                llPrivilage.setVisibility(View.VISIBLE);
                llSections.setVisibility(View.VISIBLE);
                mtvMainTitle.setText("你已解锁海归专区");
            } else {
                mtvMainTitle.setText("认证留学生");
                llPrivilage.setVisibility(View.GONE);
                llSections.setVisibility(View.GONE);
            }
        }
        dialog.show();
        AnalyticsFactory.create().action("action-tools-overseas-cert").build();
        return true;
    }

    public void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_close) {
            AnalyticsFactory.create().action("action-tools-overseas-certno").build();
            dismiss();
        } else if (i == R.id.tv_next) {
            AnalyticsFactory.create().action("action-tools-overseas-certyes").build();
            dismiss();
            new ZPManager(activity, data.guideUrl).handler();
        }
    }
}
