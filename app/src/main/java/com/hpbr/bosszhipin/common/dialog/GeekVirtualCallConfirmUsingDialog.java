package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import net.bosszhipin.api.DirectCallGeekPreUseResponse;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: zhouyou
 * Date: 2019-11-11
 */
public class GeekVirtualCallConfirmUsingDialog {

    private final Context context;
    private BottomView bottomView;
    private final View.OnClickListener listener;
    private final DirectCallGeekPreUseResponse resp;

    public GeekVirtualCallConfirmUsingDialog(Context context, DirectCallGeekPreUseResponse resp, View.OnClickListener listener) {
        this.context = context;
        this.listener = listener;
        this.resp = resp;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_geek_virtual_call_confirm_using_dialog, null);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        MTextView tvTip = view.findViewById(R.id.tv_tip);
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);

        tvTitle.setText(resp.title);
        tvDesc.setText(resp.content, View.GONE);
        tvTip.setText(resp.notice, View.GONE);

        btnConfirm.setText("立刻拨打");

        btnConfirm.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onClick(v);
                }
            }
        });
        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }
}
