package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.decorator.AppDividerDecorator;
import com.twl.utils.ActivityUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.DirectCallCancelWindowResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2019-11-13
 */
public class GeekVirtualCallFunctionDisableDialog {

    private BottomView bottomView;
    private final Activity activity;
    private final OnReasonSelectListener listener;
    @SuppressWarnings("FieldCanBeLocal, unused")
    // 弹窗来源，暂无使用场景，先做保留
    private final int source; //1:期望管理页，2:设置-电话助手授权，3: F1-回流用户引导完善页面

    public GeekVirtualCallFunctionDisableDialog(Activity activity, int source, OnReasonSelectListener listener) {
        this.activity = activity;
        this.listener = listener;
        this.source = source;
        initViews();
    }

    private void initViews() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_geek_virtual_call_function_disable_dialog, null);

        RecyclerView rvList = view.findViewById(R.id.rvList);
        AppDividerDecorator decorator = new AppDividerDecorator();
        decorator.setDividerColor(ContextCompat.getColor(activity, R.color.app_divider1));
        decorator.setDividerHeight(ZPUIDisplayHelper.dp2px(activity, 0.5f));
        rvList.addItemDecoration(decorator);

        DisableReasonAdapter adapter = new DisableReasonAdapter(activity, (reason, code) -> {
            dismiss();
            if (listener != null) {
                listener.onReasonSelect(reason, code);
            }
        });
        rvList.setAdapter(adapter);

        ImageView ivClose = view.findViewById(R.id.ivClose);
        ivClose.setOnClickListener(v -> dismiss());

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void show() {
        if (ActivityUtils.isValid(activity) && bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (ActivityUtils.isValid(activity) && bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    public static final long ALREADY_FIND_JOB = 100;
    public static final long SICK_ALWAYS_RECEIVE_PHONE = 101;
    public static final long NO_ONE_CALL_ME_NOT_WORK = 102;
    public static final long PHONE_NUMBER_ERROR = 103;
    public static final long THINK_AGAIN = 104;
    public static final long OTHER_REASON = 105;

    class DisableReasonAdapter extends RecyclerView.Adapter<DisableReasonAdapter.ReasonViewHolder> {

        private final Activity activity;
        private final OnReasonSelectListener listener;
        private final List<LevelBean> reasonList = new ArrayList<LevelBean>() {
            private static final long serialVersionUID = -8458054066770649477L;
            {
                Context context = App.getAppContext();
                add(new LevelBean(ALREADY_FIND_JOB, context.getString(R.string.string_already_find_job)));
                add(new LevelBean(SICK_ALWAYS_RECEIVE_PHONE, context.getString(R.string.string_sick_always_receive_phone)));
                add(new LevelBean(NO_ONE_CALL_ME_NOT_WORK, context.getString(R.string.string_no_one_call_me)));
                add(new LevelBean(PHONE_NUMBER_ERROR, context.getString(R.string.string_phone_number_error)));
                add(new LevelBean(THINK_AGAIN, context.getString(R.string.string_think_again)));
            }
        };

        DisableReasonAdapter(Activity activity, OnReasonSelectListener listener) {
            this.activity = activity;
            this.listener = listener;
            Collections.shuffle(reasonList);
            // 在具体原因列表随机后的基础上，增加其他原因选项
            reasonList.add(new LevelBean(OTHER_REASON, App.getAppContext().getString(R.string.string_other_reason)));
        }

        @NonNull
        @Override
        public ReasonViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(activity).inflate(R.layout.item_virtual_call_function_disable_reason, parent, false);
            return new ReasonViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ReasonViewHolder holder, int position) {
            LevelBean reason = getItem(position);
            holder.tvReason.setText(reason.name);
            holder.tvReason.setOnClickListener(v -> {
                AnalyticsFactory.create().action("directcall-authorize-popup-click1").param("p2", reason.name).build();

                if (reason.code == THINK_AGAIN) {
                    dismiss();
                    getThinkAgainDialogData(reason);
                    return;
                }

                if (reason.code == PHONE_NUMBER_ERROR) {
                    dismiss();
                    showPhoneNumberErrorDialog(reason.name, reason.code);
                    return;
                }

                if (listener != null) {
                    listener.onReasonSelect(reason.name, reason.code);
                }

            });
        }

        private void showPhoneNumberErrorDialog(@Nullable String name, long code) {
            String desc = activity.getString(R.string.string_current_phone_num_is, ViewCommon.keepPhoneSecret(UserManager.getPhone()));
            new DialogUtils.Builder(activity)
                    .setTitle(R.string.string_need_change_phone_num)
                    .setDesc(desc)
                    .setDoubleButton()
                    .setPositiveAction(R.string.string_to_change, v -> {
                        LoginRouter.startChangeMobileActivity(activity);
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_ITEM_DIRECT_CALL_POUP_CLICK)
                                .param("p", 1)
                                .param("p2", 2)
                                .build();

                    }).setNegativeAction(R.string.string_close_authorize, v -> {
                        if (listener != null) {
                            listener.onReasonSelect(name, code);
                        }
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_ITEM_DIRECT_CALL_POUP_CLICK)
                                .param("p", 1)
                                .param("p2", 1)
                                .build();
                    }).build().show();
            AnalyticsFactory.create()
                    .action(AnalyticsAction.ACTION_ITEM_DIRECT_CALL_AUTHORIZE_POPUP)
                    .param("p", 1)
                    .build();
        }

        private void getThinkAgainDialogData(@NonNull LevelBean reason) {
            SimpleApiRequest.POST(BusinessUrlConfig.URL_DIRECT_CALL_CANCEL_WINDOW)
                    .setRequestCallback(new SimpleCommonApiRequestCallback<DirectCallCancelWindowResponse>() {
                        @Override
                        public void onSuccess(ApiData<DirectCallCancelWindowResponse> data) {
                            if (data.resp.result != null && data.resp.result.reasonWindow != null) {
                                long firstShowTime = SpManager.get().user().getLong("geekcall-authorizeclose", 0);
                                if (ActivityUtils.isValid(activity) && System.currentTimeMillis() - firstShowTime > 7 * 24 * 3600) {
                                    //一周之内只显示一次
                                    showThinkAgainDialog(data.resp.result.reasonWindow, reason.name, reason.code);
                                    return;
                                }
                            }
                            if (listener != null) {
                                listener.onReasonSelect(reason.name, reason.code);
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason errorReason) {
                            if (listener != null) {
                                listener.onReasonSelect(reason.name, reason.code);
                            }
                        }
                    }).execute();
        }

        private void showThinkAgainDialog(@NonNull DirectCallCancelWindowResponse.ReasonWindowBean reasonWindow, @Nullable String name, long code) {
            SpannableStringBuilder builder = ViewCommon.setTextHighLight(reasonWindow.subTitle, reasonWindow.highlightVOList, ContextCompat.getColor(activity, R.color.light_green));
            new DialogUtils.Builder(activity)
                    .setTitle(reasonWindow.title)
                    .setDesc(builder)
                    .setDoubleButton()
                    .setPositiveAction(reasonWindow.buttonOpenText, v -> {
                        if (listener != null) {
                            listener.onReset();
                        }
                    })
                    .setNegativeAction(reasonWindow.buttonCloseText, v -> {
                        if (listener != null) {
                            listener.onReasonSelect(name, code);
                        }
                    }).build().show();
            SpManager.get().user().edit().putLong("geekcall-authorizeclose", System.currentTimeMillis()).apply();
        }

        private LevelBean getItem(int position) {
            return LList.getElement(reasonList, position);
        }

        @Override
        public int getItemCount() {
            return LList.getCount(reasonList);
        }

        class ReasonViewHolder extends RecyclerView.ViewHolder {

            MTextView tvReason;

            ReasonViewHolder(@NonNull View itemView) {
                super(itemView);
                tvReason = itemView.findViewById(R.id.tvReason);
            }
        }
    }

    public interface OnReasonSelectListener {

        void onReasonSelect(String reason, long code);

        default void onReset() {

        }
    }
}
