package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.View;
import android.view.Window;
import android.widget.LinearLayout;

import androidx.annotation.DrawableRes;
import androidx.annotation.IntDef;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.views.GuideView;
import com.twl.utils.ActivityUtils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @ClassName ：GuideDialog
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/1/12  11:38 上午
 */
public class GuideDialog {

    private FullScreenDialog dialog;

    @HorizontalGravity
    private int horizontalGravity = HorizontalGravity.ALIGN_LEFT;
    @VerticalGravity
    private int verticalGravity = VerticalGravity.ALIGN_TOP;
    private int horizontalOffset;
    private int verticalOffset;
    /*目标View（高亮View）上线左右的设置的距离值，如果不设置距离值默认为0*/
    private GuideView.TargetViewSpace targetViewSpace;
    /*高亮区域圆角半径*/
    private int roundCornerRadius;
    /*引导层上的图片资源id*/
    @DrawableRes
    private int resId;
    private View guideContentView;
    private OnDialogDismissListener onDialogDismissListener;
    private boolean hideTopLeft;
    private boolean hideTopRight;
    private boolean hideBottomLeft;
    private boolean hideBottomRight;
    private boolean navigationBarTransparent;
    private boolean enableAnimation;
    private Window window;

    public GuideDialog setHorizontalGravityAndOffset(@HorizontalGravity int horizontalGravity, int horizontalOffset) {
        this.horizontalGravity = horizontalGravity;
        this.horizontalOffset = horizontalOffset;
        return this;
    }

    public GuideDialog setVerticalGravityAndOffset(@VerticalGravity int verticalGravity, int verticalOffset) {
        this.verticalGravity = verticalGravity;
        this.verticalOffset = verticalOffset;
        return this;
    }

    public GuideDialog setTargetViewSpace(GuideView.TargetViewSpace targetViewSpace) {
        this.targetViewSpace = targetViewSpace;
        return this;
    }

    /**
     * 设置透明区域 圆角的半径
     */
    public GuideDialog setRoundCornerRadius(int roundCornerRadius) {
        this.roundCornerRadius = roundCornerRadius;
        return this;
    }

    /**
     * 设置图片资源
     */
    public GuideDialog setGuideImageResource(@DrawableRes int resId) {
        this.resId = resId;
        return this;
    }

    /**
     * 设置高亮区域外的另一个VIew是布局
     */
    public GuideDialog setGuideContentView(View guideContentView) {
        this.guideContentView = guideContentView;
        return this;
    }

    public GuideDialog setOnDialogDismissListener(OnDialogDismissListener onDialogDismissListener) {
        this.onDialogDismissListener = onDialogDismissListener;
        return this;
    }

    public GuideDialog setRoundCorner(boolean hideTopLeft, boolean hideTopRight, boolean hideBottomLeft, boolean hideBottomRight) {
        this.hideTopLeft = hideTopLeft;
        this.hideTopRight = hideTopRight;
        this.hideBottomLeft = hideBottomLeft;
        this.hideBottomRight = hideBottomRight;
        return this;
    }

    /**
     * 设置NavigationBar是否是半透明色
     *
     * @param isTransparent
     * @return
     */
    public GuideDialog setNavigationBarTransparent(boolean isTransparent) {
        this.navigationBarTransparent = isTransparent;
        return this;
    }

    public GuideDialog enableWindowAnimation(boolean enable) {
        this.enableAnimation = enable;
        return this;
    }

    /**
     * 显示弹框
     */
    public void showDialog(Activity activity, View targetView) {
        if (!ActivityUtils.isValid(activity)) return;
        if (dialog != null && dialog.isShowing()) dialog.dismiss();

        View view = View.inflate(activity, R.layout.layout_guide_dialog, null);
        int themeResId;
        if (navigationBarTransparent) {
            themeResId = enableAnimation ? R.style.fullScreen_DialogStyle_navigationBar_transparent_with_anim : R.style.fullScreen_DialogStyle_navigationBar_transparent;
        } else {
            themeResId = enableAnimation ? R.style.fullScreen_DialogStyle_with_anim : R.style.fullScreen_DialogStyle;
        }

        dialog = new FullScreenDialog(activity, themeResId);
        window = dialog.getWindow();
        ImmersiveUtils.immersiveStyleBar(window, true, false);
        dialog.setCanceledOnTouchOutside(false);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(view, lp);

        GuideView guideView = view.findViewById(R.id.guideView);
        guideView.setRoundCornerRadius(roundCornerRadius)
                .setHorizontalGravityAndOffset(horizontalGravity, horizontalOffset)
                .setVerticalGravityAndOffset(verticalGravity, verticalOffset)
                .setGuideImageResource(resId)
                .setGuideContentView(guideContentView)
                .setRoundCorner(hideTopLeft, hideTopRight, hideBottomLeft, hideBottomRight)
                .setOnEventListener(this::dismissDialog)
                .build(targetView, targetViewSpace);

        if (ActivityUtils.isValid(activity)) {
            dialog.show();
        }
    }

    /**
     * 关闭对话框
     */
    public void dismissDialog() {
        if (onDialogDismissListener != null) onDialogDismissListener.onDismiss();
        if (dialog != null && dialog.isShowing()) dialog.dismiss();
    }

    @Nullable
    public Window getWindow() {
        return dialog != null ? dialog.getWindow() : null;
    }


    /**
     * 弹框关闭的回调
     */
    public interface OnDialogDismissListener {

        void onDismiss();
    }


    @IntDef({HorizontalGravity.ALIGN_LEFT, HorizontalGravity.ALIGN_RIGHT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface HorizontalGravity {
        int ALIGN_LEFT = 1;
        int ALIGN_RIGHT = 2;
    }

    @IntDef({VerticalGravity.ALIGN_TOP, VerticalGravity.ALIGN_BOTTOM})
    @Retention(RetentionPolicy.SOURCE)
    public @interface VerticalGravity {
        int ALIGN_TOP = 1;
        int ALIGN_BOTTOM = 2;
    }

}
