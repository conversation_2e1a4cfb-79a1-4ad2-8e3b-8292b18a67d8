package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.common.listeners.OnHotPositionActionListener;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerButtonBean;

import java.util.List;
import java.util.Map;

/**
 * Author: ZhouYou
 * Date: 2017/6/28.
 */
public class HotJobCommonDialog {

    private DialogUtils d;
    private final Activity activity;
    private String dialogTitle;
    private String dialogDesc;
    private String iconUrl;
    private List<ServerButtonBean> actionList;
    private final OnHotPositionActionListener listener;
    private boolean isCancelable = true;

    /**
     * 设置标题
     *
     * @param dialogTitle
     */
    public void setDialogTitle(String dialogTitle) {
        this.dialogTitle = dialogTitle;
    }

    /**
     * 设置描述
     *
     * @param dialogDesc
     */
    public void setDialogDesc(String dialogDesc) {
        this.dialogDesc = dialogDesc;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    /**
     * 设置操作
     *
     * @param actionList
     */
    public void setActionList(List<ServerButtonBean> actionList) {
        this.actionList = actionList;
    }

    public HotJobCommonDialog(Activity activity, OnHotPositionActionListener listener) {
        this.activity = activity;
        this.listener = listener;
    }

    public void setCancelable(boolean cancelable) {
        isCancelable = cancelable;
    }

    public boolean isShow() {
        return !(activity == null || activity.isFinishing()) && !(LList.isEmpty(actionList) || LList.getCount(actionList) > 2);
    }

    public void dismiss() {
        if (d != null) {
            d.dismiss();
            d = null;
        }
    }

    public void show() {
        int size = actionList.size();
        switch (size) {
            case 1:
                ServerButtonBean action = actionList.get(0);
                showSingle(action);
                break;
            default:
                ServerButtonBean leftButton = actionList.get(0);
                ServerButtonBean rightButton = actionList.get(1);
                showDouble(leftButton, rightButton);
                break;
        }
    }

    private void showSingle(final ServerButtonBean action) {
        if (action == null) return;

        DialogUtils.ImageParams imageParams = null;
        if (!TextUtils.isEmpty(iconUrl)) {
            imageParams = new DialogUtils.ImageParams(DialogUtils.ImageParams.TOP_BACKGROUND, Uri.parse(iconUrl), 147 / 295f);
        }
        d = new DialogUtils.Builder(activity)
                .setSingleButton()
                .setCancelable(isCancelable)
                .setTitle(dialogTitle)
                .setDesc(dialogDesc)
                .setTopBackground(imageParams)
                .setPositiveAction(action.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        actionDispatcher(action);
                    }
                })
                .setDismissListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) listener.close();
                    }
                })
                .build();
        d.show();
    }

    private void showDouble(final ServerButtonBean leftButton, final ServerButtonBean rightButton) {
        if (leftButton == null || rightButton == null) return;
        DialogUtils.ImageParams imageParams = null;
        if (!TextUtils.isEmpty(iconUrl)) {
            imageParams = new DialogUtils.ImageParams(DialogUtils.ImageParams.TOP_BACKGROUND, Uri.parse(iconUrl), 0.5f);
        }
        d = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setCancelable(isCancelable)
                .setTitle(dialogTitle)
                .setDesc(dialogDesc)
                .setTopBackground(imageParams)
                .setNegativeAction(leftButton.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        actionDispatcher(leftButton);
                    }
                })
                .setPositiveAction(rightButton.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        actionDispatcher(rightButton);
                    }
                })
                .setDismissListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (listener != null) listener.close();
                    }
                })
                .build();
        d.show();
    }

    private void actionDispatcher(ServerButtonBean bean) {
        String actionUrl = bean.url;
        if (TextUtils.isEmpty(actionUrl)) {
            if (listener != null) listener.close();
        } else {
            ZPManager manager = new ZPManager(activity, actionUrl);
            Map<String, String> map = ZPManager.UrlHandler.getParams(actionUrl);
            if (manager.isRefreshPage()) {
                if (listener != null) listener.reload();
            }
            // 处理其余
            else {
                manager.handler();
            }
        }
        AnalyticsFactory.bgAction(bean.ba);
    }

    public static class SimpleHotJobActionListener implements OnHotPositionActionListener {

        @Override
        public void close() {

        }

        @Override
        public void reload() {

        }
    }
}
