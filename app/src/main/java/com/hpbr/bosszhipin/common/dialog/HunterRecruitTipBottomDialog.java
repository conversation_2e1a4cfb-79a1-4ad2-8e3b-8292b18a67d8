package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.JobHunterAdapter;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.banner.ZPUIBannerLayout;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class HunterRecruitTipBottomDialog {

    private Activity activity;
    private BottomView bottomView;
    private MTextView mtvTitle;
    private MTextView mButton;
    private MTextView mtvIknow;
    private ZPUIBannerLayout mBlGuide;
    private List<DialogBean> dataList = new ArrayList<>();

    public HunterRecruitTipBottomDialog(Activity activity) {
        this.activity = activity;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_hunter_tip_bottom_dialog_new, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        mtvTitle = view.findViewById(R.id.tv_title);
        mtvTitle.setText("猎头职位说明");
        ImageView ivClose = view.findViewById(R.id.iv_close);
        mtvIknow = view.findViewById(R.id.btn_i_see);
        mButton = view.findViewById(R.id.btn_confirm);
        mBlGuide = view.findViewById(R.id.bl_guide);
        mBlGuide.setAutoPlaying(false);
        initData();
        bindBanner(dataList);
        mBlGuide.getRecyclerView().addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(RecyclerView rv, MotionEvent e) {
                // set return "true" = disables scrolling behaviour on dragging
                return true;
            }

            @Override
            public void onTouchEvent(RecyclerView rv, MotionEvent e) {
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
        mBlGuide.setOnIndicatorIndexChangedListener(new ZPUIBannerLayout.OnIndicatorIndexChangedListener() {
            @Override
            public void onIndexChanged(int position) {
                switch (position) {
                    case 0:
                        mtvTitle.setText("猎头职位说明");
                        mButton.setVisibility(View.VISIBLE);
                        mtvIknow.setVisibility(View.GONE);
                        break;
                    case 1:
                        mtvTitle.setText("公司资质说明");
                        mButton.setVisibility(View.VISIBLE);
                        mtvIknow.setVisibility(View.GONE);
                        break;
                    case 2:
                        mtvTitle.setText("温馨提示");
                        mButton.setVisibility(View.GONE);
                        mtvIknow.setVisibility(View.VISIBLE);
                        break;
                    default:
                        break;
                }
            }
        });
        mBlGuide.getRecyclerView().addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            @Override
            public boolean onInterceptTouchEvent(RecyclerView rv, MotionEvent e) {
                // set return "true" = disables scrolling behaviour on dragging
                return true;
            }

            @Override
            public void onTouchEvent(RecyclerView rv, MotionEvent e) {
            }

            @Override
            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
        mButton.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                int index = mBlGuide.getLayoutManager().getCurrentPosition();
                ++index;
                mBlGuide.getRecyclerView().smoothScrollToPosition(index);
            }
        });

        mtvIknow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });


    }

    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void bindBanner(@NonNull List<DialogBean> list) {
        JobHunterAdapter adapter = new JobHunterAdapter(list);
        mBlGuide.setAdapter(adapter);
    }

    public static class DialogBean {
        public int pidSourceId;
        public String title;
        public String desc;
        public String tips;
    }

    private void initData() {
        dataList.clear();
        DialogBean firstStep = new DialogBean();

        firstStep.desc = "入职客户公司后，您将与该客户公司签订劳动合同，并为其进行工作";
        firstStep.pidSourceId = R.mipmap.banner_hunter_stepone;
        dataList.add(firstStep);
        DialogBean secondStep = new DialogBean();

        secondStep.desc = "该猎头顾问已取得人社部发放的人力资源服务许可证，具有经营该业务的资质";
        secondStep.pidSourceId = R.mipmap.banner_hunter_steptwo;
        dataList.add(secondStep);
        DialogBean thirdStep = new DialogBean();

        thirdStep.desc = "沟通该职位时，请与猎头顾问确定您最终的人企关系，包括：与谁签订劳动合同、由谁支付工资、由谁缴纳社保等";
        thirdStep.pidSourceId = R.mipmap.banner_hunter_stepthree;
        thirdStep.tips = "如您不想看该类职位，可在“设置-隐私保护”中开启屏蔽";
        dataList.add(thirdStep);
    }



}
