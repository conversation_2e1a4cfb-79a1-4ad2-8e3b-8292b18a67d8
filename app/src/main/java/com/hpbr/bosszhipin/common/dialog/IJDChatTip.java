package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import net.bosszhipin.api.bean.ServerJDChatTemplateBean;

import java.util.List;

/**
 * create by guofeng
 * date on 2023/3/13
 */
public interface IJDChatTip {


    View createJDChatView(Activity activity, @Nullable List<ServerJDChatTemplateBean> serverJDChatTemplateBeans, IOperate iOperate);

    void onResume(@Nullable IonResume ionResume);


    public interface IOperate {

        void onCloseViewListener();

        void onSendListener(@Nullable ServerJDChatTemplateBean selectBean, boolean isGreetingNeedCustomUpdate);
    }


    public interface IonResume {
        void onResumeListener(boolean needRequest);
    }
}
