package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;

/**
 * <AUTHOR>
 * @date 2022/09/30 14:29
 * 图片文字识别，上传图片
 */
public class ImageOcrUploadDialog implements View.OnClickListener {

    private final Activity activity;

    private BottomView bottomView;
    private ZPUIConstraintLayout clCamera;
    private TextView tvCamera;
    private ZPUIConstraintLayout clGallery;
    private TextView tvGallery;
    private ZPUIConstraintLayout clCancel;
    private TextView tvCancel;

    private final String cameraText;
    private final String galleryText;
    private final String cancelText;
    private final View.OnClickListener cameraClickListener;
    private final View.OnClickListener galleryClickListener;
    private final View.OnClickListener cancelClickListener;

    public ImageOcrUploadDialog(Activity activity, String cameraText, String galleryText, String cancelText, View.OnClickListener cameraClickListener, View.OnClickListener galleryClickListener, View.OnClickListener cancelClickListener) {
        this.activity = activity;
        this.cameraText = cameraText;
        this.galleryText = galleryText;
        this.cancelText = cancelText;
        this.cameraClickListener = cameraClickListener;
        this.galleryClickListener = galleryClickListener;
        this.cancelClickListener = cancelClickListener;
        initView();
    }

    private void initView() {
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_image_ocr_upload, null);
        clCamera = view.findViewById(R.id.cl_camera);
        tvCamera = view.findViewById(R.id.tv_camera);
        clGallery = view.findViewById(R.id.cl_gallery);
        tvGallery = view.findViewById(R.id.tv_gallery);
        clCancel = view.findViewById(R.id.cl_cancel);
        tvCancel = view.findViewById(R.id.tv_cancel);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        clCamera.setVisibility(TextUtils.isEmpty(cameraText) || cameraClickListener == null ? View.GONE : View.VISIBLE);
        clGallery.setVisibility(TextUtils.isEmpty(galleryText) || galleryClickListener == null ? View.GONE : View.VISIBLE);

        clCamera.setOnClickListener(this);
        clGallery.setOnClickListener(this);
        clCancel.setOnClickListener(this);

        tvCamera.setText(cameraText);
        tvGallery.setText(galleryText);
        tvCancel.setText(cancelText);

    }


    public void show() {
        bottomView.showBottomView(true);
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    @Override
    public void onClick(View v) {
        dismiss();
        int i = v.getId();
        if (i == R.id.cl_camera) {
            if (cameraClickListener != null) {
                cameraClickListener.onClick(v);
            }

        } else if (i == R.id.cl_gallery) {
            if (galleryClickListener != null) {
                galleryClickListener.onClick(v);
            }

        } else if (i == R.id.cl_cancel) {
            if (cancelClickListener != null) {
                cancelClickListener.onClick(v);
            }
        }
    }

    public static class Builder {
        private final Activity activity;

        private String cameraText;
        private String galleryText;
        private String cancelText;
        private View.OnClickListener cameraClickListener;
        private View.OnClickListener galleryClickListener;
        private View.OnClickListener cancelClickListener;


        public Builder(Activity activity) {
            this.activity = activity;
        }

        public Builder setCameraAction(View.OnClickListener cameraClickListener) {
            return setCameraAction("拍照", cameraClickListener);
        }

        public Builder setCameraAction(String cameraText, View.OnClickListener cameraClickListener) {
            this.cameraText = cameraText;
            this.cameraClickListener = cameraClickListener;
            return this;
        }

        public Builder setGalleryAction(View.OnClickListener galleryClickListener) {
            return setGalleryAction("从相册中选择", galleryClickListener);
        }

        public Builder setGalleryAction(String galleryText, View.OnClickListener galleryClickListener) {
            this.galleryText = galleryText;
            this.galleryClickListener = galleryClickListener;
            return this;
        }

        public Builder setCancelAction(View.OnClickListener cancelClickListener) {
            return setCancelAction("取消", cancelClickListener);
        }

        public Builder setCancelAction(String cancelText, View.OnClickListener cancelClickListener) {
            this.cancelText = cancelText;
            this.cancelClickListener = cancelClickListener;
            return this;
        }

        public ImageOcrUploadDialog build() {
            return new ImageOcrUploadDialog(activity, cameraText, galleryText, cancelText, cameraClickListener, galleryClickListener, cancelClickListener);
        }
    }

} 