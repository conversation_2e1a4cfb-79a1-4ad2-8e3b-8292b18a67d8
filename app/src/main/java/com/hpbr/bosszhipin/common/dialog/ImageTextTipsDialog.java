package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.res.ColorStateList;
import android.graphics.drawable.Animatable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintProperties;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.controller.BaseControllerListener;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.image.ImageInfo;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButtonDrawable;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * <AUTHOR>
 * @date 2022/10/17 11:40
 * 图文提示底部弹窗
 */
public class ImageTextTipsDialog implements View.OnClickListener {

    private final Activity activity;
    private BottomView bottomView;

    private ZPUIConstraintLayout clDialog;
    private MTextView tvDialogTitle;
    private ImageView ivClose;
    private RecyclerView rvList;
    private ZPUIConstraintLayout clBottom;
    private ZPUIRoundButton rbNegBtn;
    private ZPUIRoundButton rbPosAction;

    private final String titleText;
    //    private final boolean showNegBtn;
    //    private final boolean showBottomDivider;
    private final boolean negBtnStrokeStyle;
    private final String negActionText;
    private final String posActionText;
    private final List<BaseItemBean> dataList;
    private final int maxDialogHeight;
    private final View.OnClickListener closeClickListener;
    private final View.OnClickListener negClickListener;
    private final View.OnClickListener posClickListener;

    private Builder builder;

    private ItemAdapter itemAdapter;

    public ImageTextTipsDialog(Builder builder) {
        this.builder = builder;
        this.activity = builder.activity;
        this.titleText = builder.titleText;
        this.negBtnStrokeStyle = builder.negBtnStrokeStyle;
        this.negActionText = builder.negActionText;
        this.posActionText = builder.posActionText;
        this.dataList = builder.dataList;
        this.maxDialogHeight = builder.maxDialogHeight;
        this.closeClickListener = builder.closeClickListener;
        this.negClickListener = builder.negClickListener;
        this.posClickListener = builder.posClickListener;
        initView();
    }


    private void initView() {
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_image_text_tips, null);

        clDialog = view.findViewById(R.id.cl_dialog);
        tvDialogTitle = view.findViewById(R.id.tv_dialog_title);
        ivClose = view.findViewById(R.id.iv_close);
        rvList = view.findViewById(R.id.rv_list);
        clBottom = view.findViewById(R.id.cl_bottom);
        rbNegBtn = view.findViewById(R.id.rb_neg_btn);
        rbPosAction = view.findViewById(R.id.rb_pos_action);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        //设置弹窗最大高度
        int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) - ZPUIDisplayHelper.dp2px(activity, 55);
        new ConstraintProperties(clDialog)
                .constrainMaxHeight(maxDialogHeight > 0 ? maxDialogHeight : maxHeight)
                .apply();

        tvDialogTitle.setText(!TextUtils.isEmpty(titleText) ? titleText : "温馨提示");
        rbNegBtn.setVisibility(negClickListener != null ? View.VISIBLE : View.GONE);
        rbNegBtn.setText(negActionText);
        rbPosAction.setText(posActionText);
//        clBottom.setBottomDividerAlpha(showBottomDivider ? 255 : 0);
        if (negBtnStrokeStyle) {
            setNegBtnStrokeStyle();
        }

        //设置图文列表适配器
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false) {
            @Override
            public boolean canScrollHorizontally() {
                return false;
            }

            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
        rvList.setLayoutManager(linearLayoutManager);
        itemAdapter = new ItemAdapter(LList.hasElement(dataList) ? dataList : new ArrayList<>());
        rvList.setAdapter(itemAdapter);

        ivClose.setOnClickListener(this);
        rbNegBtn.setOnClickListener(this);
        rbPosAction.setOnClickListener(this);

    }

    /**
     * 背景色
     */
    private void setNegBtnStrokeStyle() {

        @ColorInt
        int backgroundColorNormal = ContextCompat.getColor(activity, R.color.color_FFFFFFFF_FF151517);
        int backgroundColorPressed = ContextCompat.getColor(activity, R.color.color_FFF5F5F5_FF262629);
        int backgroundColorDisable = ContextCompat.getColor(activity, R.color.color_FFF5F5F5_FF262629);
        ColorStateList backgroundStateList = ZPUIRoundButtonDrawable.getColorStateList(backgroundColorNormal, backgroundColorPressed, backgroundColorDisable);

        @ColorInt
        int strokeColorNormal = ContextCompat.getColor(activity, twl.lib.common.R.color.color_BOSS5);
        int strokeColorPressed = ContextCompat.getColor(activity, twl.lib.common.R.color.app_green_dark_press);
        int strokeColorDisable = ContextCompat.getColor(activity, twl.lib.common.R.color.app_green_dark_press);
        int strokeWidth = ZPUIDisplayHelper.dp2px(activity, 1);
        ColorStateList strokeStateList = ZPUIRoundButtonDrawable.getColorStateList(strokeColorNormal, strokeColorPressed, strokeColorDisable);

        @ColorInt
        int textColorNormal = ContextCompat.getColor(activity, R.color.color_BOSS7);
        int textColorPressed = ContextCompat.getColor(activity, R.color.app_green_dark_press);
        int textColorDisable = ContextCompat.getColor(activity, R.color.app_green_dark_press);
        ColorStateList textStateList = ZPUIRoundButtonDrawable.getColorStateList(textColorNormal, textColorPressed, textColorDisable);

        ZPUIRoundButtonDrawable buttonDrawable = (ZPUIRoundButtonDrawable) rbNegBtn.getBackground();
        /*背景色*/
        buttonDrawable.setColor(backgroundStateList);
        /*边框*/
        buttonDrawable.setStrokeData(strokeWidth, strokeStateList);
        /*圆角*/
        buttonDrawable.setCornerRadius(ZPUIDisplayHelper.dp2px(activity, 8));
        /*文本颜色*/
        rbNegBtn.setTextColor(textStateList);
    }


    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_close) {
            dismiss();
            if (closeClickListener != null) {
                closeClickListener.onClick(v);
            }

        } else if (id == R.id.rb_neg_btn) {
            dismiss();
            if (negClickListener != null) {
                negClickListener.onClick(v);
            }

        } else if (id == R.id.rb_pos_action) {
            dismiss();
            if (posClickListener != null) {
                posClickListener.onClick(v);
            }
        }
    }

    /**
     * 构造器
     */
    public static class Builder {

        private final Activity activity;

        private String titleText;
        private boolean negBtnStrokeStyle;
        private String negActionText;
        private String posActionText;
        private List<BaseItemBean> dataList;
        private int maxDialogHeight;
        private View.OnClickListener closeClickListener;
        private View.OnClickListener negClickListener;
        private View.OnClickListener posClickListener;


        public Builder(Activity activity) {
            this.activity = activity;
        }

        public ImageTextTipsDialog.Builder setDialogTitle(String titleText) {
            this.titleText = titleText;
            return this;
        }

        public ImageTextTipsDialog.Builder setCloseAction(View.OnClickListener closeClickListener) {
            this.closeClickListener = closeClickListener;
            return this;
        }

//        public ImageTextTipsDialog.Builder setShowNegBtn(boolean showNegBtn) {
//            this.showNegBtn = showNegBtn;
//            return this;
//        }

        public ImageTextTipsDialog.Builder setDataList(List<BaseItemBean> dataList) {
            this.dataList = dataList;
            return this;
        }

//        public ImageTextTipsDialog.Builder setDataList(BaseItemBean... beans) {
//
//            List<BaseItemBean> list = new ArrayList<>();
//            if (beans != null) {
//                for (BaseItemBean bean : beans) {
//                    if (bean == null) continue;
//                    list.add(bean);
//                }
//            }
//            this.dataList = list;
//            return this;
//        }

        public ImageTextTipsDialog.Builder setMaxDialogHeight(int maxDialogHeight) {
            this.maxDialogHeight = maxDialogHeight;
            return this;
        }

        public ImageTextTipsDialog.Builder setNegativeAction(View.OnClickListener negClickListener) {
            return setNegativeAction("我再想想", negClickListener);
        }


        public ImageTextTipsDialog.Builder setNegativeAction(String negActionText, View.OnClickListener negClickListener) {
            return setNegativeAction(false, negActionText, negClickListener);
        }


        public ImageTextTipsDialog.Builder setNegativeAction(boolean negBtnStrokeStyle, String negActionText, View.OnClickListener negClickListener) {
            this.negBtnStrokeStyle = negBtnStrokeStyle;
            this.negActionText = negActionText;
            this.negClickListener = negClickListener;
            return this;
        }

        public ImageTextTipsDialog.Builder setPositiveAction(View.OnClickListener posClickListener) {
            return setPositiveAction("我知道了", posClickListener);
        }

        public ImageTextTipsDialog.Builder setPositiveAction(String posActionText, View.OnClickListener posClickListener) {
            this.posActionText = posActionText;
            this.posClickListener = posClickListener;
            return this;
        }

        public ImageTextTipsDialog build() {
            return new ImageTextTipsDialog(this);
        }
    }

    /**
     * 图文列表适配器
     */
    public static class ItemAdapter extends BaseMultipleItemRvAdapter<BaseItemBean, BaseViewHolder> {

        public ItemAdapter(@Nullable List<BaseItemBean> data) {
            super(data);
        }

        @Override
        protected int getViewType(List<BaseItemBean> data, int position) {
            BaseItemBean item = data.get(position);
            return item.getItemType();
        }

        @Override
        protected void registerItemProvider() {
            registerProviders(
                    new ResImageProvider(),
                    new NetImageProvider(),
                    new TextProvider(),
                    new DividerProvider()
            );
        }

    }

    /**
     * 资源文件提示图片
     */
    public static class ResImageProvider extends BaseItemProvider<ResImageBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_RES_IMAGE;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_res_image;
        }

        @Override
        public void convert(BaseViewHolder helper, ResImageBean data, int position) {
            if (data != null) {
                ConstraintLayout cl_root = helper.getView(R.id.cl_root);
                ImageView iv_image = helper.getView(R.id.iv_image);
                if (data.ratio > 0) {
                    ConstraintSet set = new ConstraintSet();
                    set.clone(cl_root);
                    set.setDimensionRatio(R.id.iv_image, TextUtils.concat("h,1:", String.valueOf(data.ratio)).toString());
                    set.applyTo(cl_root);
                }
                iv_image.setImageResource(data.resId);


            }
        }

    }

    /**
     * 网络提示图片
     */
    public static class NetImageProvider extends BaseItemProvider<NetImageBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_NET_IMAGE;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_image;
        }

        @Override
        public void convert(BaseViewHolder helper, NetImageBean data, int position) {
            if (data != null) {
                SimpleDraweeView sdvImage = helper.getView(R.id.sdv_image);

                DraweeController controller = Fresco.newDraweeControllerBuilder()
                        .setUri(data.url)
                        .setControllerListener(new BaseControllerListener<ImageInfo>() {
                            @Override
                            public void onFinalImageSet(String id, ImageInfo imageInfo, Animatable anim) {
                                if (imageInfo == null) {
                                    return;
                                }

                                //根据sdvImage宽度和宽高比，设置sdvImage高度
                                int imageWidth = ZPUIDisplayHelper.getScreenWidth(helper.itemView.getContext()) - ZPUIDisplayHelper.dp2px(helper.itemView.getContext(), 40);
                                ViewGroup.LayoutParams layoutParams = sdvImage.getLayoutParams();
                                int height = imageInfo.getHeight();
                                int width = imageInfo.getWidth();
                                layoutParams.width = imageWidth;
                                layoutParams.height = (int) ((float) (imageWidth * height) / (float) width);
                                sdvImage.setLayoutParams(layoutParams);
                            }
                        })
                        .build();
                sdvImage.setController(controller);
            }
        }

    }

    /**
     * 提示文本
     */
    public static class TextProvider extends BaseItemProvider<TextBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_TEXT;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_text;
        }

        @Override
        public void convert(BaseViewHolder helper, TextBean data, int position) {
            if (data != null) {
                helper.setText(R.id.tv_text, data.text);
            }
        }

    }

    /**
     * 分割线or透明间隔
     */
    public static class DividerProvider extends BaseItemProvider<DividerBean, BaseViewHolder> {

        @Override
        public int viewType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }

        @Override
        public int layout() {
            return R.layout.item_image_text_tips_dialog_divider;
        }

        @Override
        public void convert(BaseViewHolder helper, DividerBean data, int position) {
            if (data != null) {
                if (helper.itemView instanceof ConstraintLayout) {
                    ConstraintLayout clRoot = (ConstraintLayout) helper.itemView;
                    ConstraintSet set = new ConstraintSet();
                    set.clone(clRoot);
                    set.setAlpha(R.id.v_divider, isSpaceDivider(data) ? 0 : 1);
                    set.constrainHeight(R.id.v_divider, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isSpaceDivider(data) ? data.getSpaceHeightDp() : 0.3f));
                    set.setMargin(R.id.v_divider, ConstraintSet.LEFT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginLeftDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.TOP, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginTopDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.RIGHT, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginRightDp() : 0));
                    set.setMargin(R.id.v_divider, ConstraintSet.BOTTOM, ZPUIDisplayHelper.dp2px(clRoot.getContext(), isGrayLine(data) ? data.getMarginBottomDp() : 0));

                    set.applyTo(clRoot);

                }
            }
        }

        private boolean isSpaceDivider(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_SPACE;
        }

        private boolean isGrayLine(@NonNull DividerBean data) {
            return data.getDividerType() == DividerBean.DividerType.TYPE_GRAY_LINE;
        }
    }

    /**
     * 列表样式
     */
    public static abstract class BaseItemBean extends BaseEntity implements MultiItemEntity {

        private static final long serialVersionUID = -1404978582145219367L;

        public static final int ITEM_TYPE_RES_IMAGE = 1; //本地图片
        public static final int ITEM_TYPE_NET_IMAGE = 2; //网络图片
        public static final int ITEM_TYPE_TEXT = 3; // 文字
        public static final int ITEM_TYPE_DIVIDER = 4; // 分割线
    }

    /**
     * 资源文件提示图片
     */
    public static class ResImageBean extends BaseItemBean {

        private static final long serialVersionUID = 8129177274054191401L;
        @DrawableRes
        public int resId;

        public double ratio;//高/宽

        public ResImageBean(@DrawableRes int resId, double ratio) {
            this.resId = resId;
            this.ratio = ratio;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_RES_IMAGE;
        }
    }

    /**
     * 网络提示图片
     */
    public static class NetImageBean extends BaseItemBean {

        private static final long serialVersionUID = 5420727197118978549L;

        public String url;

        public NetImageBean(String url) {
            this.url = url;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_NET_IMAGE;
        }
    }

    /**
     * 提示文本
     */
    public static class TextBean extends BaseItemBean {

        private static final long serialVersionUID = 7558337503846247635L;

        public String text;

        public TextBean(String text) {
            this.text = text;
        }

        @Override
        public int getItemType() {
            return ITEM_TYPE_TEXT;
        }
    }

    /**
     * 分割线or透明间隔
     */
    public static class DividerBean extends BaseItemBean {

        private static final long serialVersionUID = -1552731056299192620L;
        @DividerType
        private int dividerType;
        private float spaceHeightDp;//间隔的高度
        private float marginLeftDp = 20;//灰线
        private float marginTopDp;//灰线
        private float marginRightDp = 20;//灰线
        private float marginBottomDp;//灰线

        private DividerBean() {
        }

        public static DividerBean obj() {
            return new DividerBean();
        }

        public int getDividerType() {
            return dividerType;
        }

        public DividerBean setDividerType(@DividerBean.DividerType int type) {
            this.dividerType = type;
            return this;
        }

        public float getSpaceHeightDp() {
            return spaceHeightDp;
        }

        public DividerBean setSpaceHeightDp(float spaceHeightDp) {
            this.spaceHeightDp = spaceHeightDp;
            return this;
        }

        public float getMarginLeftDp() {
            return marginLeftDp;
        }

        public DividerBean setMarginLeftDp(float marginLeftDp) {
            this.marginLeftDp = marginLeftDp;
            return this;
        }

        public float getMarginTopDp() {
            return marginTopDp;
        }

        public DividerBean setMarginTopDp(float marginTopDp) {
            this.marginTopDp = marginTopDp;
            return this;
        }

        public float getMarginRightDp() {
            return marginRightDp;
        }

        public DividerBean setMarginRightDp(float marginRightDp) {
            this.marginRightDp = marginRightDp;
            return this;
        }

        public float getMarginBottomDp() {
            return marginBottomDp;
        }

        public DividerBean setMarginBottomDp(float marginBottomDp) {
            this.marginBottomDp = marginBottomDp;
            return this;
        }


        @Override
        public int getItemType() {
            return BaseItemBean.ITEM_TYPE_DIVIDER;
        }


        @IntDef({
                DividerType.TYPE_SPACE,
                DividerType.TYPE_GRAY_LINE
        })
        @Retention(RetentionPolicy.SOURCE)
        public @interface DividerType {
            int TYPE_SPACE = 1;//间隔
            int TYPE_GRAY_LINE = 2;//灰线
        }
    }

}


