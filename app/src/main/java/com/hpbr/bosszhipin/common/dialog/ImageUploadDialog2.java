package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.adapter.AvatarAdapter;
import com.hpbr.bosszhipin.utils.DefaultConstants;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Created by zhouyou on 2015/5/27.
 * 该对话框支持【打开相机】、【打卡图库】、【默认头像】操作
 */
public class ImageUploadDialog2 {

    private Activity activity;

    /**
     * 是否需要【默认头像】功能 | 默认为不要【默认头像】
     */
    private boolean isNeedDefaultAvatar = false;

    /**
     * 0-照片来源相机和相册，1只要相机，2只要相册
     */
    private int albumType = 0;

    private boolean isCancelDismiss = true;


    public void setNeedDefaultAvatar(boolean isNeedDefaultAvatar) {
        this.isNeedDefaultAvatar = isNeedDefaultAvatar;
    }

    public ImageUploadDialog2(Activity activity) {
        this.activity = activity;
    }

    public void setAlbumType(int albumType) {
        this.albumType = albumType;
    }


    public void show(OnClickCallBack callBack) {
        View view = ((LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.view_select_photo, null);
        final LinearLayout llSelectMethod = view.findViewById(R.id.ll_select_method); // 选择拍照方式
        MTextView tvCamera = view.findViewById(R.id.tv_open_camera);
        MTextView tvGallery = view.findViewById(R.id.tv_open_gallery);
        MTextView tvDefault = view.findViewById(R.id.tv_open_default); // 牛人默认头像
        MTextView tvCancel = view.findViewById(R.id.tv_cancel);
        final GridView gvDefaultAvatar = view.findViewById(R.id.gv_default_avatar);
        if (isNeedDefaultAvatar) {
            tvDefault.setVisibility(View.VISIBLE);
        } else {
            tvDefault.setVisibility(View.GONE);
        }
        if (albumType == 1) {
            tvGallery.setVisibility(View.GONE);
        } else if (albumType == 2) {
            tvCamera.setVisibility(View.GONE);
        }

        final BottomView bv = new BottomView(activity, R.style.BottomViewTheme_Defalut, view);
        bv.setAnimation(R.style.BottomToTopAnim);
        bv.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (callBack != null && isCancelDismiss) {
                    callBack.onCancelClickListener();
                }
            }
        });
        bv.showBottomView(true);
        // 点击事件【取消】
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bv.dismissBottomView();
            }
        });
        // 点击事件【打开相机】
        tvCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isCancelDismiss = false;
                bv.dismissBottomView();
                if (callBack != null) {
                    callBack.onCameraClickListener();
                }
            }
        });
        // 点击事件【打开图库】
        tvGallery.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isCancelDismiss = false;
                bv.dismissBottomView();
                if (callBack != null) {
                    callBack.onGalleryClickListener();
                }
            }
        });
        // 点击事件【默认头像】
        tvDefault.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (llSelectMethod.getVisibility() == View.VISIBLE) {
                    llSelectMethod.setVisibility(View.GONE);
                    gvDefaultAvatar.setVisibility(View.VISIBLE);
                    AvatarAdapter adapter = new AvatarAdapter(activity, DefaultConstants.DEFAULT_AVATARS);
                    gvDefaultAvatar.setAdapter(adapter);
                }
            }
        });
        // 选择一个默认头像
        gvDefaultAvatar.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                GridView gv = (GridView) parent;
                int res = (int) gv.getItemAtPosition(position);
                isCancelDismiss = false;
                bv.dismissBottomView();
                if (callBack != null) {
                    callBack.onDefaultAvatarClickListener(position, res);
                }
            }
        });
    }


    public static class OnClickCallBack {

        public void onCancelClickListener() {

        }

        public void onCameraClickListener() {

        }

        public void onGalleryClickListener() {

        }

        public void onDefaultAvatarClickListener(int position, int res) {

        }
    }


}
