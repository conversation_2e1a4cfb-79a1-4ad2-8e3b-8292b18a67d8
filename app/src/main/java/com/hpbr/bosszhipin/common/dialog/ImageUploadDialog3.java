package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.adapter.AvatarAdapter;
import com.hpbr.bosszhipin.utils.DefaultConstants;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.twl.ui.ToastUtils;

/**
 * Created by zhouyou on 2015/5/27.
 * 该对话框支持【打开相机】、【打卡图库】、【默认头像】操作
 */
public class ImageUploadDialog3 {

    private Activity activity;

    private BottomView bottomView;
    private TextView tvLeftNum;

    private int[] arrDefaultHeadRes = new int[]{R.drawable.icon_head_portrait_one, R.drawable.icon_head_portrait_two, R.drawable.icon_head_portrait_three, R.drawable.icon_head_portrait_four};
    private int[] arrAgentHeadRes = new int[]{R.mipmap.ic_agent_head_portrait_one, R.mipmap.ic_agent_head_portrait_two, R.mipmap.ic_agent_head_portrait_three, R.mipmap.ic_agent_head_portrait_four};
    private String[] arrDefaultHeadTip = new String[]{"非人物照", "五官遮挡", "模糊不清", "衣着不当"};
    private String[] arrAgentHeadTip = new String[]{"非本人", "非正装", "五官遮挡", "模糊不清"};
    private boolean showAgentDefault = false;//显示人才经纪人默认提示
    private boolean showModifyNum = false;//显示剩余修改次数
    private int totalCount;//当月可更换头像的总次数
    private int leftCount;//当月可更换头像的剩余次数

    /**
     * 是否需要【默认头像】功能 | 默认为不要【默认头像】
     */
    private boolean isNeedDefaultAvatar = false;

    /**
     * 0-照片来源相机和相册，1只要相机，2只要相册
     */
    private int albumType = 0;

    /**
     * 是否显示上传人像提示
     */
    private boolean showTips = false;

    public void setShowAgentDefault(boolean showAgentDefault) {
        this.showAgentDefault = showAgentDefault;
    }

    public void setShowModifyNum(boolean showModifyNum) {
        this.showModifyNum = showModifyNum;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public void setLeftCount(int leftCount) {
        this.leftCount = leftCount;
    }

    public void setNeedDefaultAvatar(boolean isNeedDefaultAvatar) {
        this.isNeedDefaultAvatar = isNeedDefaultAvatar;
    }

    public ImageUploadDialog3(Activity activity) {
        this.activity = activity;
    }

    public void setAlbumType(int albumType) {
        this.albumType = albumType;
    }

    public void setShowTips(boolean showTips) {
        this.showTips = showTips;
    }

    public void show(OnClickCallBack callBack) {
        View view = ((LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.view_select_photo3, null);

        TextView tvCorrectTip = view.findViewById(R.id.tips_succeed);
        ImageView ivCorrectTip = view.findViewById(R.id.icon_head_portrait);
        TextView tvNoGoodTip = view.findViewById(R.id.tv_no_good_tip);
        ImageView ivNoGoodOne = view.findViewById(R.id.icon_head_portrait_one);
        TextView tvNoGoodOne = view.findViewById(R.id.tv_head_portrait_one);
        ImageView ivNoGoodTwo = view.findViewById(R.id.icon_head_portrait_two);
        TextView tvNoGoodTwo = view.findViewById(R.id.tv_head_portrait_two);
        ImageView ivNoGoodThree = view.findViewById(R.id.icon_head_portrait_three);
        TextView tvNoGoodThree = view.findViewById(R.id.tv_head_portrait_three);
        ImageView ivNoGoodFour = view.findViewById(R.id.icon_head_portrait_four);
        TextView tvNoGoodFour = view.findViewById(R.id.tv_head_portrait_four);
        tvLeftNum = view.findViewById(R.id.tv_left_num);

        final LinearLayout llSelectMethod = view.findViewById(R.id.ll_select_method); // 选择拍照方式
        MTextView tvCamera = view.findViewById(R.id.tv_open_camera);
        MTextView tvGallery = view.findViewById(R.id.tv_open_gallery);
        MTextView tvDefault = view.findViewById(R.id.tv_open_default); // 牛人默认头像
        MTextView tvCancel = view.findViewById(R.id.tv_cancel);
        final GridView gvDefaultAvatar = view.findViewById(R.id.gv_default_avatar);

        tvCorrectTip.setText(showAgentDefault ? "上传本人正装头像，更快通过审核" : "上传真实头像，更容易赢得好感");
        ivCorrectTip.setImageResource(showAgentDefault ? R.mipmap.ic_agent_head_portrait : R.drawable.icon_head_portrait);
        tvNoGoodTip.setVisibility(showAgentDefault ? View.VISIBLE : View.GONE);

        ivNoGoodOne.setImageResource(showAgentDefault ? arrAgentHeadRes[0] : arrDefaultHeadRes[0]);
        tvNoGoodOne.setText(showAgentDefault ? arrAgentHeadTip[0] : arrDefaultHeadTip[0]);
        ivNoGoodTwo.setImageResource(showAgentDefault ? arrAgentHeadRes[1] : arrDefaultHeadRes[1]);
        tvNoGoodTwo.setText(showAgentDefault ? arrAgentHeadTip[1] : arrDefaultHeadTip[1]);
        ivNoGoodThree.setImageResource(showAgentDefault ? arrAgentHeadRes[2] : arrDefaultHeadRes[2]);
        tvNoGoodThree.setText(showAgentDefault ? arrAgentHeadTip[2] : arrDefaultHeadTip[2]);
        ivNoGoodFour.setImageResource(showAgentDefault ? arrAgentHeadRes[3] : arrDefaultHeadRes[3]);
        tvNoGoodFour.setText(showAgentDefault ? arrAgentHeadTip[3] : arrDefaultHeadTip[3]);

        if (showModifyNum) {
            tvLeftNum.setVisibility(View.VISIBLE);
            if (leftCount == 0) {
                tvLeftNum.setText(String.format("每月可修改%s次，本月您的修改次数已用完", totalCount));
            } else {
                tvLeftNum.setText(String.format("每月可修改%s次，本月您还可以修改%s次", totalCount, leftCount));
            }
        } else {
            tvLeftNum.setVisibility(View.GONE);
        }

        if (isNeedDefaultAvatar) {
            tvDefault.setVisibility(View.VISIBLE);
        } else {
            tvDefault.setVisibility(View.GONE);
        }
        if (albumType == 1) {
            tvGallery.setVisibility(View.GONE);
        } else if (albumType == 2) {
            tvCamera.setVisibility(View.GONE);
        }
        view.findViewById(R.id.layout_upload_tips).setVisibility(showTips ? View.VISIBLE : View.GONE);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
        // 点击事件【取消】
        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomView.dismissBottomView();
                if (callBack != null) {
                    callBack.onCancelClickListener();
                }
            }
        });
        // 点击事件【打开相机】
        tvCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomView.dismissBottomView();
                if (showModifyNum && leftCount == 0) {
                    ToastUtils.showText(String.format("您本月%s次修改机会已用完，本月已不能更改头像", totalCount));
                    return;
                }
                if (callBack != null) {
                    callBack.onCameraClickListener();
                }
            }
        });
        // 点击事件【打开图库】
        tvGallery.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomView.dismissBottomView();
                if (showModifyNum && leftCount == 0) {
                    ToastUtils.showText(String.format("您本月%s次修改机会已用完，本月已不能更改头像", totalCount));
                    return;
                }
                if (callBack != null) {
                    callBack.onGalleryClickListener();
                }
            }
        });
        // 点击事件【默认头像】
        tvDefault.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (llSelectMethod.getVisibility() == View.VISIBLE) {
                    llSelectMethod.setVisibility(View.GONE);
                    gvDefaultAvatar.setVisibility(View.VISIBLE);
                    AvatarAdapter adapter = new AvatarAdapter(activity, DefaultConstants.DEFAULT_AVATARS);
                    gvDefaultAvatar.setAdapter(adapter);
                }
            }
        });
        // 选择一个默认头像
        gvDefaultAvatar.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                GridView gv = (GridView) parent;
                int res = (int) gv.getItemAtPosition(position);
                bottomView.dismissBottomView();
                if (callBack != null) {
                    callBack.onDefaultAvatarClickListener(position, res);
                }
            }
        });
    }

    public BottomView getBottomView() {
        return bottomView;
    }


    public static class OnClickCallBack {

        public void onCancelClickListener() {

        }

        public void onCameraClickListener() {

        }

        public void onGalleryClickListener() {

        }

        public void onDefaultAvatarClickListener(int position, int res) {

        }
    }

}
