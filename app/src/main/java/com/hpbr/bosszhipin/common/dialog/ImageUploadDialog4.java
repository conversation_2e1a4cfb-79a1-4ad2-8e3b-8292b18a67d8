package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

public class ImageUploadDialog4 {

    @NonNull
    private final Activity activity;

    private BottomView bottomView;

    public ImageUploadDialog4(@NonNull Activity activity) {
        this.activity = activity;
    }

    public void show(@NonNull OnClickCallback callBack) {
        @SuppressLint("InflateParams") View view = ((LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.view_select_photo4, null);

        MTextView tvCaptureImage = view.findViewById(R.id.tv_capture_image);
        MTextView tvCaptureVideo = view.findViewById(R.id.tv_capture_video);
        MTextView tvGallery = view.findViewById(R.id.tv_open_gallery);
        MTextView tvFile = view.findViewById(R.id.tv_open_file);
        MTextView tvCancel = view.findViewById(R.id.tv_cancel);

        int resourceType = callBack.getResourceType();
        boolean showDocument = callBack.showDocument();//是否显示文档选择
        if (resourceType == 1) { // 选择/拍摄图片。隐藏拍摄视频
            tvCaptureVideo.setVisibility(View.GONE);
        } else if (resourceType == 2) { // 选择/录制视频。隐藏拍照
            tvCaptureImage.setVisibility(View.GONE);
        }
        tvFile.setVisibility(showDocument ? View.VISIBLE : View.GONE);

        // 点击事件【打开拍照片】
        tvCaptureImage.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                bottomView.dismissBottomView();
                callBack.onCaptureImageClick();
            }
        });

        // 点击事件【打开拍视频】
        tvCaptureVideo.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                bottomView.dismissBottomView();
                callBack.onCaptureVideoClick();
            }
        });

        // 点击事件【打开图库】
        tvGallery.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                bottomView.dismissBottomView();
                callBack.onGalleryClick();
            }
        });
        //点击事件【打开文件选择】
        tvFile.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                bottomView.dismissBottomView();
                callBack.onFileClick();
            }
        });

        // 点击事件【取消】
        tvCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                bottomView.dismissBottomView();
                callBack.onCancelClick();
            }
        });


        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    public interface OnClickCallback {

        void onCaptureImageClick();

        void onCaptureVideoClick();

        void onGalleryClick();

        void onFileClick();

        boolean showDocument();

        void onCancelClick();

        int getResourceType();
    }

}
