package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.EvaluateAgentRecruitInfoResponse;
import net.bosszhipin.api.EvaluateAttachSaveRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

/**
 * create by guofeng
 * date on 2020/11/25
 */
public class InterviewExperienceDialog {


    private Activity getTopActivity() {
        return ForegroundUtils.get().getTopActivity();
    }

    private BottomView mButtonView;

    private String securityId;

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    private EvaluateAgentRecruitInfoResponse.AttachDetailBean attachDetailBean;

    public void setAttachDetailBean(EvaluateAgentRecruitInfoResponse.AttachDetailBean attachDetailBean) {
        this.attachDetailBean = attachDetailBean;
    }

    public void show() {
        if (attachDetailBean == null) return;
        Activity topActivity = getTopActivity();
        if (!ActivityUtils.isValid(topActivity)) return;
        View view = LayoutInflater.from(topActivity).inflate(R.layout.item_interview_experience_dialog, null);
        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        MTextView mAnnoance = view.findViewById(R.id.mAnnoance);
        ImageView mCloseView = view.findViewById(R.id.mCloseView);
        LinearLayout mTagContainer = view.findViewById(R.id.mTagContainer);
        mTitleView.setText(attachDetailBean.title);
        mAnnoance.setText(attachDetailBean.isAnonymousDesc);
        mAnnoance.setVisibility(attachDetailBean.isAnonymous ? View.VISIBLE : View.GONE);
        mCloseView.setOnClickListener(v -> dismiss());
        mTagContainer.removeAllViews();
        List<String> tags = attachDetailBean.tags;

        int mTagContainerWidth = App.get().getDisplayWidth() - Scale.dip2px(App.get(), 40);

        if (tags != null) {
            for (int i = 0; i < tags.size(); i++) {
                String tag = LList.getElement(tags, i);
                if (tag == null) continue;
                CheckBox boxView = (CheckBox) LayoutInflater.from(getTopActivity()).inflate(R.layout.item_label_anno_commit, null);
                boxView.setText(tag);
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                params.width = (mTagContainerWidth - Scale.dip2px(App.get(), 40)) / 3;
                if (i == 0) {
                    params.leftMargin = 0;
                } else {
                    params.leftMargin = Scale.dip2px(App.get(), 20);
                }
                mTagContainer.addView(boxView, params);
                boxView.setChecked(true);
                boxView.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View view) {
                        boxView.setChecked(true);
                        HttpPost(tag);
                    }
                });
            }
        }

        mButtonView = new BottomView(topActivity, R.style.BottomViewTheme_Transparent, view);
        mButtonView.setAnimation(R.style.BottomToTopAnim);
        mButtonView.showBottomView(false);
    }

    //提交数据
    private void HttpPost(String tag) {
        EvaluateAttachSaveRequest request = new EvaluateAttachSaveRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                ToastUtils.showText("感谢您的反馈～");
            }

            @Override
            public void onComplete() {
                dismiss();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        request.tag = tag;
        HttpExecutor.execute(request);
    }



    //关闭窗口
    private void dismiss() {
        if (mButtonView != null) {
            mButtonView.dismissBottomView();
        }
    }

} 