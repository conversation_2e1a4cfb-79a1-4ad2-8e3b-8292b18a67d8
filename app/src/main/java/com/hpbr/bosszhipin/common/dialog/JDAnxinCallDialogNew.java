package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.position.callback.OnBossJobActionListener;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.spannable.SpannableHelper;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GetAnxinbaoCallStatusResponse;
import net.bosszhipin.api.PostAnxinbaoCallResponse;
import net.bosszhipin.api.bean.HighLightBean;
import net.bosszhipin.api.bean.PostAnxinbaoCallRequest;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class JDAnxinCallDialogNew implements View.OnClickListener {


    private final Activity activity;
    private final GetAnxinbaoCallStatusResponse data;
    private BottomView bottomView;
    private int status;
    private String securityId;
    private String lid;
    private OnBossJobActionListener actionListener;
    private AppCompatCheckBox appCompatCheckBox;
    private boolean isCheckNoSee = true;
    private MTextView mtvCheckBox;
    private int type;
    private long jobId;

    public void setApplyListner(ApplyListner applyListner) {
        this.applyListner = applyListner;
    }

    private ApplyListner applyListner;


    public JDAnxinCallDialogNew(Activity activity, GetAnxinbaoCallStatusResponse data, int status, String sid, String lid, OnBossJobActionListener jobActionListener, int type, long jobid) {
        this.activity = activity;
        this.data = data;
        this.status = status;
        this.lid = lid;
        this.securityId = sid;
        this.actionListener = jobActionListener;
        this.type = type;
        this.jobId = jobid;
    }


    //安心保+海螺高选：一键报名使用，电话联系使用不带new那个
    public boolean show() {
        if(null==data || !ActivityUtils.isValid(activity)) return false;
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_anxincall_dialog, null);
        v.findViewById(R.id.iv_close).setOnClickListener(this);
        MTextView mtvDesc = v.findViewById(R.id.mtv_desc);
        MTextView mtvDescHighlight = v.findViewById(R.id.mtv_desc_highlight);
        MTextView mtvTips = v.findViewById(R.id.mtv_tips);
        MTextView mtvCall = v.findViewById(R.id.mtv_call);
        ImageView ivBgDialog = v.findViewById(R.id.iv_dialog_bg);
        ImageView ivTitle = v.findViewById(R.id.iv_title);
        ConstraintLayout clAnxinActions = v.findViewById(R.id.layout_anxin_actions);
        ConstraintLayout clGaoxuanActions = v.findViewById(R.id.layout_gaoxuan_actions);
        appCompatCheckBox = v.findViewById(R.id.cb_check);
        mtvCheckBox = v.findViewById(R.id.mtv_checkbox);

        appCompatCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean isCheckd) {
                isCheckNoSee = isCheckd;
            }
        });

        //1218.801 海螺高选黄色样式
        if(data.jobType == 1){
            ivBgDialog.setImageResource(R.mipmap.geek_bg_jd_hai_luo_gao_xuan_dialog);
            ivTitle.setImageResource(R.mipmap.geek_ic_jd_title_gao_xuan_normal);
            //顶部条
            mtvTips.setBackground(ContextCompat.getDrawable(activity,R.drawable.bg_4_corner_color_ff7847));
            mtvTips.setTextColor(ContextCompat.getColor(activity,R.color.color_Y7));
            //action
            clAnxinActions.setVisibility(View.GONE);
            clGaoxuanActions.setVisibility(View.VISIBLE);
            //底部条
            mtvDesc.setBackground(ContextCompat.getDrawable(activity,R.drawable.bg_4_corner_color_ff7847));
        }else{
            ivBgDialog.setImageResource(R.mipmap.bg_anxincall_top);
            ivTitle.setImageResource(R.mipmap.ic_anxincall_title);
            //顶部条
            mtvTips.setBackgroundColor(ContextCompat.getColor(activity,R.color.color_FFE4FBFB_1F15B3B3));
            mtvTips.setTextColor(ContextCompat.getColor(activity,R.color.color_FF0CC2A5));
            //action
            clAnxinActions.setVisibility(View.VISIBLE);
            clGaoxuanActions.setVisibility(View.GONE);
            //底部条
            mtvDesc.setBackground(ContextCompat.getDrawable(activity,R.drawable.bg_8_corner_color_1200bebe));
        }

        //描述条
        mtvDesc.setText(data.remindText, !TextUtils.isEmpty(data.remindText));

        //顶部提示条
        mtvTips.setText(data.tipText);

        //1213 高亮文案
        if(!TextUtils.isEmpty(data.highLightText)){
            mtvDescHighlight.setVisibility(View.VISIBLE);
            mtvDescHighlight.setText(getHighlightText(activity, data.highLightText ,data.highLightTextIndex,mtvDescHighlight));
        }else{
            mtvDescHighlight.setVisibility(View.GONE);
        }

        if (data.button != null) {
            mtvCall.setText(data.button.text);
        }
        if (type == 0) {
            mtvTips.setVisibility(View.GONE);
            mtvCheckBox.setText("本月不再展示，直接拨打");


            AnalyticsFactory.create().action("anxianbao-hotline-call-show")
                    .param("p4", 0)
                    .param("p5", 0)
                    .param("p6", lid)
                    .secId(securityId)
                    .build();


            mtvCall.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    AnalyticsFactory.create().action("detail-boss-quick-click")
                            .param("p", jobId)
                            .param("p2", 0)
                            .param("p3", lid)
                            .build();

                    requestCall(securityId, 0); // 直接拨打
                }
            });
        } else if (type == 1) {
            mtvTips.setVisibility(View.VISIBLE);
            mtvCheckBox.setText("本月不再展示，直接报名");
            AnalyticsFactory.create().action("anxianbao-hotline-call-show")
                    .param("p4", 0)
                    .param("p5", 1)
                    .param("p6", lid)
                    .secId(securityId)
                    .build();


            mtvCall.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) { // 管家在线联系我
                        // 跳转报名页
                        if (applyListner != null) {
                            applyListner.oneKeyApply(isCheckNoSee ? 1 : 0);
                        }
                        dismiss();

                        if (actionListener != null) {
                            actionListener.onRemoveAnxinCallCard();
                        }
                    AnalyticsFactory.create().action("detail-boss-quick-click")
                            .param("p", jobId)
                            .param("p2", 1)
                            .param("p3", lid)
                            .build();

                }
            });
        }

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, v);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);

        return true;
    }


    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }


    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_close) {
            dismiss();
        }
    }
    private SpannableStringBuilder getHighlightText(Activity activity, String text, List<HighLightBean> highLightBeans, MTextView spanTextView) {
        SpannableStringBuilder builder = new SpannableStringBuilder(text);
        SpannableHelper.lightOn(builder, highLightBeans, ContextCompat.getColor(activity, R.color.color_BOSS7));
        SpannableHelper.clickOn(spanTextView, builder, highLightBeans, (highLightBean,index) -> {
            if(null!=highLightBean && !TextUtils.isEmpty(highLightBean.url)){
                new ZPManager(activity, highLightBean.url).handler();
            }
        });
        return builder;
    }
    private void requestCall(String sid, int type) {
        PostAnxinbaoCallRequest request = new PostAnxinbaoCallRequest(new ApiRequestCallback<PostAnxinbaoCallResponse>() {
            @Override
            public void onSuccess(ApiData<PostAnxinbaoCallResponse> data) {
                if (data != null && data.resp != null) {
                    if (type == 0) {
                        if (!TextUtils.isEmpty(data.resp.telephone)) {
                            new ZPManager(activity, data.resp.telephone).handler();
                        }
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if(null!=reason && !TextUtils.isEmpty(reason.getErrReason())){
                    ToastUtils.showText(reason.getErrReason());
                }
            }
        });

        request.securityId = sid;
        request.type = type;
        request.settingValue = isCheckNoSee ? 1 : 0;
        HttpExecutor.execute(request);
    }

    public interface ApplyListner {
        void oneKeyApply(int settingValue);
    }
}
