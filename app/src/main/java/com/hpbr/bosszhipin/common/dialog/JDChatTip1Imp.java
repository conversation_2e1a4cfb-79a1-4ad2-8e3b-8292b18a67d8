package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerJDChatTemplateBean;

import java.util.List;

/**
 * create by guofeng
 * date on 2023/3/13
 */

public class JDChatTip1Imp implements IJDChatTip {

    private final long bossId;

    private final long jobId;


    public JDChatTip1Imp(long bossId, long jobId) {
        this.bossId = bossId;
        this.jobId = jobId;
    }

    @Override
    public View createJDChatView(Activity activity, @Nullable List<ServerJDChatTemplateBean> serverJDChatTemplateBeans, IOperate iOperate) {


        AnalyticsFactory.create().action("detial-geek-greeting-expo").param("p", bossId).param("p2", jobId).build();

        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_jd_chattips, null);

        RecyclerView rcvTips = v.findViewById(R.id.rcv_tips);


        MTextView mtvJump = v.findViewById(R.id.mtv_jump);
        MTextView mtvEnsure = v.findViewById(R.id.mtv_ensure);

        mtvJump.setOnClickListener(v1 -> SettingRouter.jumpToGreetingWordsActivity(activity));


        v.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {

                RecyclerView.Adapter adapter = rcvTips.getAdapter();
                if (adapter instanceof ChatTipAdapter) {
                    ChatTipAdapter chatTipAdapter = (ChatTipAdapter) adapter;
                    AnalyticsFactory.create().action("detial-geek-greeting-click").param("p", bossId).param("p2", jobId).param("p3", 0).param("p4", chatTipAdapter.getSelectPosition()).build();
                }


                if (iOperate != null) {
                    iOperate.onCloseViewListener();
                }
            }
        });


        rcvTips.setVisibility(View.VISIBLE);

        ChatTipAdapter adapter = new ChatTipAdapter(serverJDChatTemplateBeans);
        rcvTips.setAdapter(adapter);

        mtvEnsure.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {

                int selectPosition = adapter.getSelectPosition();

                AnalyticsFactory.create().action("detial-geek-greeting-click").param("p", bossId).param("p2", jobId).param("p3", 1).param("p4", selectPosition).build();

                if (iOperate != null) {
                    iOperate.onSendListener(adapter.getSelectTemplateBean(), false);
                }
            }
        });


        return v;
    }

    @Override
    public void onResume(@Nullable IonResume ionResume) {
        if (ionResume != null) {
            ionResume.onResumeListener(true);
        }
    }



    private static class ChatTipAdapter extends BaseQuickAdapter<ServerJDChatTemplateBean, BaseViewHolder> {

        private ServerJDChatTemplateBean selectTemplateBean;

        public ChatTipAdapter(@Nullable List<ServerJDChatTemplateBean> data) {
            super(R.layout.item_jd_chattips, data);
            //默认选择第一个
            selectTemplateBean = LList.getElement(data, 0);
        }

        public ServerJDChatTemplateBean getSelectTemplateBean() {
            return selectTemplateBean;
        }

        public int getSelectPosition() {
            ServerJDChatTemplateBean selectTemplateBean = getSelectTemplateBean();
            if (selectTemplateBean != null) {
                return getData().indexOf(selectTemplateBean);
            }
            return 0;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerJDChatTemplateBean item) {

            ImageView ivChecked = helper.getView(R.id.iv_checked);
            MTextView mtvDesc = helper.getView(R.id.mtv_item);

            boolean isSelect = item == selectTemplateBean;

            ivChecked.setImageResource(isSelect ? R.mipmap.ic_jd_tips_checked : R.mipmap.ic_jd_tips_unchecked);
            mtvDesc.setTextColor(ContextCompat.getColor(mContext, isSelect ? R.color.color_FF0D9EA3 : R.color.color_FF292929_E6FFFFFF));
            helper.getView(R.id.rl_item).setBackground(ContextCompat.getDrawable(mContext, isSelect ? R.drawable.bg_8_corner_1f15b3b3_solid : R.drawable.bg_8_corner_cccccc_stroke));
            mtvDesc.setText(item.demo);
            helper.itemView.setOnClickListener(v -> {
                selectTemplateBean = item;
                notifyDataSetChanged();
            });
        }


    }


}