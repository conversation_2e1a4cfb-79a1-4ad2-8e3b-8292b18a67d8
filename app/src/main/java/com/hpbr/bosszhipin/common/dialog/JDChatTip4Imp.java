package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import net.bosszhipin.api.bean.ServerJDChatTemplateBean;

import java.util.List;

/**
 * create by guofeng
 * date on 2023/6/15
 */

public class JDChatTip4Imp implements IJDChatTip {

    private final long bossId;
    private final long jobId;

    public JDChatTip4Imp(long bossId, long jobId) {
        this.bossId = bossId;
        this.jobId = jobId;
    }

    @Override
    public View createJDChatView(Activity activity, @Nullable List<ServerJDChatTemplateBean> serverJDChatTemplateBeans, IOperate iOperate) {

        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_jd_chattips_empty, null);

        View llEmpty = v.findViewById(R.id.ll_empty);
        MTextView mtvJump = v.findViewById(R.id.mtv_jump);
        MTextView mtvEnsure = v.findViewById(R.id.mtv_ensure);

        mtvJump.setOnClickListener(v1 -> SettingRouter.jumpToGreetingWordsActivity(activity));
        llEmpty.setVisibility(View.VISIBLE);
        mtvEnsure.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AnalyticsFactory.create().action("detial-geek-greeting-click").param("p", bossId).param("p2", jobId).param("p3", 1).param("p4", 1).build();
                if (iOperate != null) {
                    iOperate.onSendListener(null, false);
                }
            }
        });

        v.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {

                if (iOperate != null) {
                    iOperate.onCloseViewListener();
                }

            }
        });


        return v;
    }

    @Override
    public void onResume(@Nullable IonResume ionResume) {
        if (ionResume != null) {
            ionResume.onResumeListener(true);
        }
    }

}