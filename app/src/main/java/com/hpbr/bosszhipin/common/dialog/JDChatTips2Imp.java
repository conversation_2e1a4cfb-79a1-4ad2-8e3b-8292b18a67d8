package com.hpbr.bosszhipin.common.dialog;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHOOSE_CHAT_INTRODUCE_CARD_CLICK;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.setting_export.view.MTextViewEditIcon;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerJDChatTemplateBean;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2023/3/13
 */

public class JDChatTips2Imp implements IJDChatTip {
    private final long friendId;

    private final int friendSource;

    private final long jobId;

    public JDChatTips2Imp(long friendId,
                          int friendSource, long jobId) {
        this.friendId = friendId;
        this.friendSource = friendSource;
        this.jobId = jobId;
        autoSend = false;
    }


    private static class ListItemAdapter extends BaseRvAdapter<ServerJDChatTemplateBean, BaseViewHolder> {
        private Activity activity;
        private final long jobId;

        private final long friendId;
        private int friendSource;

        public ListItemAdapter(Activity activity, long jobId, long friendId, int friendSource, @Nullable List<ServerJDChatTemplateBean> data) {
            super(R.layout.item_jd_chattips2, data);
            this.jobId = jobId;
            this.friendId = friendId;
            this.activity = activity;
            this.friendSource = friendSource;
        }

        private int selectPosition;

        public void setSelectPosition(int selectPosition) {
            this.selectPosition = selectPosition;
        }

        public int getSelectPosition() {
            return selectPosition;
        }

        public ServerJDChatTemplateBean getSelectChatBean() {
            return LList.getElement(getData(), selectPosition);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerJDChatTemplateBean serverJDChatTemplateBean) {
            if (serverJDChatTemplateBean == null) {
                return;
            }

            ImageView mCheckView = helper.getView(R.id.mCheckView);
            MTextViewEditIcon mTextView = helper.getView(R.id.mTextView);
            View mRootLayout = helper.getView(R.id.mRootLayout);

            boolean isSelfGreetingType = serverJDChatTemplateBean.greetingType == 1;
            if (selectPosition == helper.getAdapterPosition()) {
                mCheckView.setImageResource(R.mipmap.ic_jd_tips_checked);
                // 仅自定义招呼语才能编辑，展示编辑入口
                if (isSelfGreetingType) {
                    mTextView.setTextWithIcon(serverJDChatTemplateBean.demo, R.mipmap.ic_back_chat_edit, new MTextViewEditIcon.OnClickCallback() {
                        @Override
                        public void onItemClickListener() {
                            ListItemAdapter.this.onItemClickListener(helper.getAdapterPosition());
                        }

                        @Override
                        public void onIconClickListener() {
                            // 确保选中的情况下点击文本末尾才执行点击逻辑，MTextViewEditIcon点击检测实现存在误判
                            if (selectPosition == helper.getAdapterPosition()) {
                                //跳转到自我介绍页面
                                SingleRouter.jumpGenerateAdvantageFromJD(activity, friendId, friendSource, serverJDChatTemplateBean.templateId, serverJDChatTemplateBean.demo);
                                //客户端埋点
                                onCompleteBgAction(serverJDChatTemplateBean);
                            } else {
                                ListItemAdapter.this.onItemClickListener(helper.getAdapterPosition());
                            }
                        }
                    });
                } else {
                    mTextView.setText(serverJDChatTemplateBean.demo);
                }

                mTextView.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF0D9EA3_FFFFFFFF));
                mRootLayout.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_8_corner_1f15b3b3_solid));
            } else {
                mCheckView.setImageResource(R.mipmap.ic_jd_tips_unchecked);
                mTextView.setText(serverJDChatTemplateBean.demo);
                mTextView.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF292929_E6FFFFFF));
                mRootLayout.setBackground(ContextCompat.getDrawable(mContext, R.drawable.bg_8_corner_cccccc_stroke));
            }

            helper.itemView.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    onItemClickListener(helper.getAdapterPosition());
                }
            });

        }

        private void onItemClickListener(int position) {
            setSelectPosition(position);
            notifyDataSetChanged();
        }

        //客户端埋点
        private void onCompleteBgAction(ServerJDChatTemplateBean serverJDChatTemplateBean) {
            AnalyticsFactory
                    .create()
                    .action(ACTION_CHOOSE_CHAT_INTRODUCE_CARD_CLICK)
                    .param("p6", "1")
                    .buildSync();


            AnalyticsFactory.create()
                    .action("detial-geek-greeting-click")
                    .param("p", friendId)
                    .param("p2", jobId)
                    .param("p3", 2)
                    .param("p7", serverJDChatTemplateBean.demo)
                    .debug()
                    .build();
        }
    }

    public static boolean autoSend;


    private List<ServerJDChatTemplateBean> serverJDChatTemplateBeans;

    private IOperate iOperate;

    @Override
    public View createJDChatView(Activity activity,
                                 @Nullable List<ServerJDChatTemplateBean> serverJDChatTemplateBeans,
                                 @NonNull IOperate iOperate) {
        this.iOperate = iOperate;
        this.serverJDChatTemplateBeans = serverJDChatTemplateBeans;

        statsExpo();
        View v = LayoutInflater.from(activity).inflate(R.layout.view_geek_jd_chattips2, null);

        ImageView mCloseView = v.findViewById(R.id.mCloseView);

        MTextView mSendView = v.findViewById(R.id.mSendView);

        RecyclerView mItemContainer = v.findViewById(R.id.mItemContainer);
        mItemContainer.setLayoutManager(new LinearLayoutManager(activity));


        ListItemAdapter adapter = new ListItemAdapter(activity, jobId, friendId, friendSource, serverJDChatTemplateBeans);
        adapter.setSelectPosition(0);
        mItemContainer.setAdapter(adapter);


        mSendView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                iOperate.onSendListener(adapter.getSelectChatBean(), true);
                onGeekGreetingClickAction(adapter.getSelectChatBean(), adapter.getSelectPosition(), 1);
            }
        });

        mCloseView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                iOperate.onCloseViewListener();
                onGeekGreetingClickAction(adapter.getSelectChatBean(), adapter.getSelectPosition(),0);
            }
        });

        return v;
    }

    @Override
    public void onResume(@Nullable IonResume ionResume) {
        if (autoSend && iOperate != null) {
            for (ServerJDChatTemplateBean serverJDChatTemplateBean : serverJDChatTemplateBeans) {
                if (serverJDChatTemplateBean.greetingType == 1) {
                    //找到新招呼,自动发送
                    iOperate.onSendListener(serverJDChatTemplateBean, false);
                    break;
                }
            }
            autoSend = false;
        }
    }

    private void statsExpo(){
        ServerJDChatTemplateBean serverJDChatTemplateBean = getSelfIntroGreet();
        String p3Value = serverJDChatTemplateBean == null ? "" : serverJDChatTemplateBean.demo;
        String p4Value = getGreetingExpoP4();
        AnalyticsFactory.create().action("detial-geek-greeting-expo")
                .param("p", friendId)
                .param("p2", jobId)
                .param("p3", p3Value)
                .param("p4", p4Value)
                .debug().build();
    }

    private void onGeekGreetingClickAction(ServerJDChatTemplateBean serverJDChatTemplateBean, int position, int p3) {
        if (serverJDChatTemplateBean == null) {
            return;
        }

        AnalyticsFactory analyticsFactory = AnalyticsFactory.create()
                .action("detial-geek-greeting-click")
                .param("p", friendId)
                .param("p2", jobId)
                .param("p3", p3)
                .param("p7", serverJDChatTemplateBean.demo);

        if (p3 == 1) {
            analyticsFactory.param("p4", position + 1);
            analyticsFactory.param("p5", serverJDChatTemplateBean.templateId);
            analyticsFactory.param("p6", serverJDChatTemplateBean.greetingType == 1 ? 0 : 1);
        }
        analyticsFactory.debug().build();
    }

    @Nullable
    private ServerJDChatTemplateBean getSelfIntroGreet() {
        if (LList.isEmpty(serverJDChatTemplateBeans)) {
            return null;
        }

        for (ServerJDChatTemplateBean serverJDChatTemplateBean : serverJDChatTemplateBeans) {
            if (serverJDChatTemplateBean != null && serverJDChatTemplateBean.greetingType == 1) {
                return serverJDChatTemplateBean;
            }
        }
        return null;
    }

    private String getGreetingExpoP4() {
        if (LList.isEmpty(serverJDChatTemplateBeans)) {
            return "";
        }

        List<String> nonSelfIntroGreets = new ArrayList<>();
        for (ServerJDChatTemplateBean serverJDChatTemplateBean : serverJDChatTemplateBeans) {
            if (serverJDChatTemplateBean != null && serverJDChatTemplateBean.greetingType == 0) {
                nonSelfIntroGreets.add(String.valueOf(serverJDChatTemplateBean.templateId));
            }
        }

        return StringUtil.connectTextWithChar(",", nonSelfIntroGreets);
    }
}