package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.utils.ActivityUtils;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.bean.ServerSafeTipOverlayBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;


/**
 * 1112.601【策略】【C】【f2互动-新职位】【搜索】职位详情页点击关键词进搜索
 * 用户首次进入可点详情页时，进行新手引导。新手引导弹窗优先级低于蒙层、弹窗，高于气泡，点击我知道了，弹窗收起。
 */
public class JDSearchWordNewUserDialog {

    private final Activity activity;
    private BottomView bottomView;
    private MTextView tvTitle;
    private MTextView tvDesc;
    private ImageView ivClose;
    private SimpleDraweeView sdvContent;
    private View divider;
    private ZPUIRoundButton btnConfirm;
    private ServerSafeTipOverlayBean overlayBean;

    public JDSearchWordNewUserDialog(Activity activity, ServerSafeTipOverlayBean overlayBean) {
        this.activity = activity;
        this.overlayBean = overlayBean;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_search_word_new_user, null);
        initView(view);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        if(null==overlayBean) return;
        //设置数据显示
        tvTitle.setText(overlayBean.title);
        tvDesc.setText(overlayBean.content);
        //图片
        if (overlayBean != null && overlayBean.picConfig != null && overlayBean.picConfig.width > 0 && overlayBean.picConfig.height > 0 && !TextUtils.isEmpty(overlayBean.picConfig.url)) {
            float width = DisplayHelper.getScreenWidth(activity) - DisplayHelper.px2dp(view.getContext(),40);
            float ratio = ((float) overlayBean.picConfig.height / (float) overlayBean.picConfig.width);
            float actualHeight = width * ratio;
            //兜底，如果高度超过屏幕高度70%，限制下，否则底部按钮显示不出来
            int screenHeight = DisplayHelper.getScreenHeight(activity);
            actualHeight = Math.min(screenHeight * 0.7f, actualHeight);

            ViewGroup.LayoutParams params = sdvContent.getLayoutParams();
            params.height = (int) actualHeight;
            params.width = (int) width;
            sdvContent.setLayoutParams(params);
            sdvContent.setImageURI(overlayBean.picConfig.url);
        }
        //按钮
        ServerSafeTipOverlayBean.ServerBtnActionBean btnBean = LList.getElement(overlayBean.btnList, 0);
        btnConfirm.setVisibility(null!=btnBean ? View.VISIBLE : View.GONE);
        if(null!=btnBean){
            btnConfirm.setText(btnBean.btnText);
            btnConfirm.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    dismiss();
                }
            });
        }
        ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

    }

    public void show() {
        if (ActivityUtils.isValid(activity)) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }


    private void initView(View view) {
        tvTitle = (MTextView) view.findViewById(R.id.tv_title);
        tvDesc = (MTextView) view.findViewById(R.id.tv_desc);
        ivClose = (ImageView) view.findViewById(R.id.iv_close);
        sdvContent = (SimpleDraweeView) view.findViewById(R.id.sdv_content);
        divider = (View) view.findViewById(R.id.divider);
        btnConfirm = (ZPUIRoundButton) view.findViewById(R.id.btn_confirm);
    }
}
