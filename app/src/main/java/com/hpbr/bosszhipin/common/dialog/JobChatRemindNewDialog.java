package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerChatBlockBean;
import net.bosszhipin.api.bean.ServerChatRemindBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_ADDFRIEND_LIMIT_CLOSE;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class JobChatRemindNewDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final ServerChatRemindBean chatRemindBean;
    private final Runnable runnable;
    private Runnable closeRunnable;
    private final String lid;
    private Runnable dismissRunnable;


    public JobChatRemindNewDialog(Activity activity, ServerChatRemindBean chatRemindBean, String lid, Runnable runnable) {
        this.activity = activity;
        this.chatRemindBean = chatRemindBean;
        this.runnable = runnable;
        this.lid = lid;
        init();
    }

    public JobChatRemindNewDialog(Activity activity, ServerChatRemindBean chatRemindBean, String lid, Runnable runnable,Runnable closeRunnable) {
        this.activity = activity;
        this.chatRemindBean = chatRemindBean;
        this.runnable = runnable;
        this.closeRunnable = closeRunnable;
        this.lid = lid;
        init();
    }

    private void init() {

        View view = LayoutInflater.from(activity).inflate(R.layout.view_job_chatremind_dialog, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        LinearLayout ll_button = view.findViewById(R.id.ll_button);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        ImageView ivClose = view.findViewById(R.id.iv_close);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AnalyticsFactory.create().action(ACTION_ADDFRIEND_LIMIT_CLOSE)
                        .param("p", lid)
                        .build();
                if(null!=closeRunnable){
                    closeRunnable.run();
                }
                dismiss();
            }
        });
        tvTitle.setText(chatRemindBean.title, View.GONE);
        tvDesc.setText(chatRemindBean.content, View.GONE);
        LinearLayout llReasonContainer = view.findViewById(R.id.ll_reason_container);

        if (chatRemindBean != null && !LList.isEmpty(chatRemindBean.blockInfos)) {
            for (ServerChatBlockBean item : chatRemindBean.blockInfos) {
                if (item == null) continue;
                View itemView = LayoutInflater.from(activity).inflate(R.layout.item_chat_remind_new, null);
                MTextView mtvGo = itemView.findViewById(R.id.btn_go);
                MTextView mtvReason = itemView.findViewById(R.id.tv_reason);

                mtvReason.setText(item.text);

                if (!LList.isEmpty(item.textHighlight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLight(item.text,
                            item.textHighlight, activity.getResources().getColor(R.color.app_green_dark));
                    mtvReason.setText(builder);
                }

                ServerButtonBean buttonBean = LList.getElement(item.buttonList, 0);
                if (buttonBean != null) {
                    mtvGo.setText(buttonBean.text);
                    mtvGo.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            new ZPManager(activity, buttonBean.url).handler();
                            dismiss();
                        }
                    });
                }

                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                params.weight = 1;
                view.setLayoutParams(params);
                llReasonContainer.addView(itemView);
            }
        }
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);

        if (chatRemindBean.blockLevel == 0) {
            ll_button.setVisibility(View.VISIBLE);
            btnConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (runnable != null) {
                        runnable.run();
                        dismiss();
                    }
                }
            });
        } else {
            ll_button.setVisibility(View.GONE);
        }

        bottomView.setOnDismissListener(dialog -> {
            if (null != dismissRunnable) {
                dismissRunnable.run();
            }
        });
    }


    public void show() {
        if (activity != null && !activity.isFinishing() && bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void setDismissRunnable(Runnable dismissRunnable) {
        this.dismissRunnable = dismissRunnable;
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }


}
