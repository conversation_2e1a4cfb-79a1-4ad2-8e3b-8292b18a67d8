package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.EmojiForbiddenInputFilterListener;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MEditText;
import com.monch.lbase.widget.T;

/**
 * 作者：周游
 * 日期：2016/3/9
 */
public class KeywordCustomizeDialog {

    private Activity activity;
    private Dialog d;

    private MEditText etInput;

    private OnKeywordCustomizeCallback callback;

    public KeywordCustomizeDialog(Activity activity, OnKeywordCustomizeCallback callback) {
        this.activity = activity;
        this.callback = callback;
    }


    /**
     * 显示积分不够窗口
     */
    public void show() {
        d = new Dialog(activity, R.style.common_cancelable_dialog);
        d.setCanceledOnTouchOutside(true);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_keyword_customize, null);
        d.setContentView(v);

        Window window = d.getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.dimAmount = 0.6f;
        window.setAttributes(params);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        v.findViewById(R.id.tv_cancel).setOnClickListener(onClickListener);
        v.findViewById(R.id.tv_add).setOnClickListener(onClickListener);
        etInput = (MEditText) v.findViewById(R.id.et_input);
        etInput.setFilters(new InputFilter[]{new EmojiForbiddenInputFilterListener()});
        if (activity != null && !activity.isFinishing() && !d.isShowing()) {
            d.show();
        }
    }

    private View.OnClickListener onClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int i = v.getId();
            if (i == R.id.tv_cancel) {
                dismissDialog();

            } else if (i == R.id.tv_add) {
                add();

            } else {
            }
        }
    };

    private void add() {
        String input = etInput.getText().toString().trim();
        if (TextUtils.isEmpty(input)) {
            T.ss("请输入内容");
        } else if (StringUtil.getChineseCount(input) > 2 * 6) {
            T.ss("输入的内容不能超过6个字");
        } else if (input.contains(StringUtil.SPLIT_CHAR_KEYWORD)) {
            T.ss("输入的内容不能包含特殊字符");
        } else {
            // dismissDialog();
            if (callback != null) callback.onKeywordCustomize(input);
        }
    }

    /**
     * 取消对话框
     */
    public void dismissDialog() {
        if (d != null && d.isShowing()) {
            d.cancel();
            d.dismiss();
        }
    }

    public interface OnKeywordCustomizeCallback {
        void onKeywordCustomize(String text);
    }
}
