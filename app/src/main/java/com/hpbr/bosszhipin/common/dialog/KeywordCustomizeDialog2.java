package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.NonNull;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.EmojiForbiddenInputFilterListener;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MEditText;

public class KeywordCustomizeDialog2 {

    @NonNull
    private final Activity activity;
    private Dialog d;
    private MEditText etInput;
    @NonNull
    private final OnKeywordCustomizeCallback callback;

    public KeywordCustomizeDialog2(@NonNull Activity activity, @NonNull OnKeywordCustomizeCallback callback) {
        this.activity = activity;
        this.callback = callback;
    }

    public void show() {
        d = new Dialog(activity, R.style.common_cancelable_dialog);
        d.setCanceledOnTouchOutside(false);
        d.setCancelable(false);
        @SuppressLint("InflateParams") View v = LayoutInflater.from(activity).inflate(R.layout.view_keyword_customize2, null);
        d.setContentView(v);

        Window window = d.getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.dimAmount = 0.6f;
        window.setAttributes(params);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        v.findViewById(R.id.tv_cancel).setOnClickListener(onClickListener);
        v.findViewById(R.id.tv_add).setOnClickListener(onClickListener);
        etInput = v.findViewById(R.id.et_input);
        etInput.setFilters(new InputFilter[]{new EmojiForbiddenInputFilterListener()});
        if (activity != null && !activity.isFinishing() && !d.isShowing()) {
            d.show();
        }

        etInput.postDelayed(() -> AppUtil.showSoftInput(activity, etInput), 300);
    }

    @NonNull
    private final View.OnClickListener onClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int i = v.getId();
            if (i == R.id.tv_cancel) {
                dismissDialog();
            } else if (i == R.id.tv_add) {
                add();
            }
        }
    };

    private void add() {
        String input = etInput.getText().toString().trim();
        if (TextUtils.isEmpty(input)) {
            AnimUtil.errorInputAnim(etInput, "请输入内容");
        } else if (StringUtil.getChineseCount(input) > 2 * 8) {
            AnimUtil.errorInputAnim(etInput, "输入的内容不能超过8个字");
        } else if (input.contains(StringUtil.SPLIT_CHAR_KEYWORD)) {
            AnimUtil.errorInputAnim(etInput, "输入的内容不能包含特殊字符");
        } else {
            if (callback != null) callback.onKeywordCustomize(input);
        }
    }

    public void dismissDialog() {
        if (d != null && d.isShowing()) {
            d.cancel();
            d.dismiss();
        }
    }

    public interface OnKeywordCustomizeCallback {
        void onKeywordCustomize(String text);
    }

}
