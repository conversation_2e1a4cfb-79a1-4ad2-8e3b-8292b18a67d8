package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.DialogContentsAdapter;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.bean.ServerAgreementNoticeBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2024/8/6
 */
public class MainUserAgreementDialog {

    private final Activity activity;
    private Dialog dialog;

    private RecyclerView rvContents;

    public MainUserAgreementDialog(Activity activity, @NonNull ServerAgreementNoticeBean bean, OnAgreementClickListener listener) {
        this.activity = activity;
        dialog = new Dialog(activity, R.style.twl_ui_common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_dialog_main_user_agreement, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
        MTextView tvTitle = v.findViewById(R.id.tv_title);
        rvContents = v.findViewById(R.id.rv_contents);
        rvContents.setNestedScrollingEnabled(false);


        MTextView tvNegative = v.findViewById(R.id.tv_negative);
        MTextView tvPositive = v.findViewById(R.id.tv_positive);

        tvTitle.setText(bean.title, View.GONE);

        List<CharSequence> descList = new ArrayList<>();
        SpannableStringBuilder desc = getContent(bean);
        if (desc != null) {
            descList.add(desc);
        }
        DialogContentsAdapter contentsAdapter = new DialogContentsAdapter(activity);
        contentsAdapter.setContents(descList);
        rvContents.setAdapter(contentsAdapter);

        rvContents.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                rvContents.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int rvHeight = 0;
                int size = rvContents.getChildCount();
                for (int i = 0; i < size; i++) {
                    View view = rvContents.getChildAt(i);
                    if (view != null) {
                        rvHeight += view.getHeight();
                    }
                }

                int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) / 2;
                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) rvContents.getLayoutParams();
                if (rvHeight > maxHeight) {
                    params.height = maxHeight;
                } else {
                    params.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
                }
                rvContents.setLayoutParams(params);
            }
        });

        tvNegative.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onNegativeClick();
                }
            }
        });

        tvPositive.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.onPositiveClick();
                }
            }
        });
    }

    private SpannableStringBuilder getContent(@NonNull ServerAgreementNoticeBean bean) {
        SpannableStringBuilder builder = null;
        String content = bean.content;
        if (!TextUtils.isEmpty(content)) {
            builder = new SpannableStringBuilder();
            builder.append(content);
            int length = content.length();
            if (!LList.isEmpty(bean.contentHighlightList)) {
                int size = bean.contentHighlightList.size();
                int startIndex;
                int endIndex;
                for (int i = 0; i < size; i++) {
                    ServerHighlightListBean item = bean.contentHighlightList.get(i);
                    if (item == null) continue;
                    startIndex = item.startIndex;
                    endIndex = item.endIndex;
                    if (startIndex < 0 || endIndex <= startIndex || endIndex > length) continue;
                    builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.app_green_dark)), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    builder.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            new ZPManager(activity, item.linkUrl).handler();
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            ds.setUnderlineText(false);
                            ds.setColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                        }
                    }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }
        return builder;
    }

    public void show() {
        if (ActivityUtils.isValid(activity)) {
            if (dialog != null) {
                dialog.show();
            }
        }
    }

    private void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }


    public boolean isShowing() {
        if (dialog != null) {
            return dialog.isShowing();
        }
        return false;
    }

    public interface OnAgreementClickListener {

        void onNegativeClick();

        void onPositiveClick();
    }
}
