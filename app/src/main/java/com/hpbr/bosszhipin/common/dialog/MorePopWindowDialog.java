package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;

/**
 * 作者：guofeng
 * ＊ 日期:16/11/1
 */

public class MorePopWindowDialog implements View.OnClickListener {

    private Activity activity;
    private BottomView bottomView;
    private LinearLayout rePostPosition;
    private OnClickCallBack callBack;
    //是否发布职位,用于埋点
    private boolean isCreate;
    private View shareView;

    public MorePopWindowDialog(Activity activity, boolean isCreate) {
        this.activity = activity;
        this.isCreate = isCreate;
        init();
    }

    public void setCallBack(OnClickCallBack callBack) {
        this.callBack = callBack;
    }

    public void hideShare() {
        shareView.setVisibility(View.GONE);
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_more_poupwindow, null);
//        view.findViewById(R.id.ll_recharge_now).setOnClickListener(this);
        view.findViewById(R.id.ll_preview).setOnClickListener(this);
        view.findViewById(R.id.ll_cancel).setOnClickListener(this);
        shareView = view.findViewById(R.id.ll_share);
        shareView.setOnClickListener(this);
        rePostPosition = (LinearLayout) view.findViewById(R.id.ll_repost);
        rePostPosition.setOnClickListener(this);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void hideRePostPosition() {
        rePostPosition.setVisibility(View.GONE);
    }

    public void show() {
        if (bottomView != null && activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
        }
    }


    @Override
    public void onClick(View v) {
        dismiss();
        int i = v.getId();
        if (i == R.id.ll_repost) {
            if (callBack != null) {
                callBack.onRePostListener();
            }

        } else if (i == R.id.ll_preview) {
            if (callBack != null) {
                callBack.onPreviewListener();
            }

        } else if (i == R.id.ll_share) {
            if (callBack != null) {
                callBack.onPositionShareListener();
            }

        } else if (i == R.id.ll_cancel) {
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    public interface OnClickCallBack {
        void onRePostListener();

        void onPreviewListener();

        void onPositionShareListener();
    }
}
