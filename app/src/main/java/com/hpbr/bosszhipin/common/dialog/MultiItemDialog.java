package com.hpbr.bosszhipin.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/5/23.
 * 用于通用选项的dialog
 */
public class MultiItemDialog {

    private Context context;
    private Dialog d;

    public MultiItemDialog(Context context) {
        this.context = context;
    }

    public void show(String[] items, final View.OnClickListener l) {
        if (context == null) return;
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        if (inflater == null) return;
        d = new Dialog(context, R.style.common_dialog);
        View v = inflater.inflate(R.layout.dialog_items, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
        WindowManager.LayoutParams windowlp = window.getAttributes();
        windowlp.dimAmount = 0.6f;
        window.setAttributes(windowlp);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        LinearLayout llItems = (LinearLayout) v.findViewById(R.id.ll_items);
        LinearLayout blank = (LinearLayout) v.findViewById(R.id.blank);

        llItems.removeAllViews();

        if (items != null && items.length > 0) {
            int index = 0;

            for (final String str : items) {
                View item = inflater.inflate(R.layout.item_options, null);

                RelativeLayout rlItem = (RelativeLayout) item.findViewById(R.id.rl_item);
                TextView tvItem = item.findViewById(R.id.tv_item);
                View divider = item.findViewById(R.id.divider);

                tvItem.setText(str);
                rlItem.setTag(index);

                if (index == items.length - 1) {
                    divider.setVisibility(View.GONE);
                } else {
                    divider.setVisibility(View.VISIBLE);
                }

                rlItem.setOnClickListener(new View.OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        dismiss();

                        if (l != null) {
                            l.onClick(v);
                        }
                    }
                });

                llItems.addView(item);
                index++;
            }
        }

        // 点击空白区域
        blank.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                if (d != null) {
                    d.dismiss();
                }
            }
        });

        if (!d.isShowing()) {
            d.show();
        }
    }

    public void dismiss() {
        if (d != null) {
            d.dismiss();
        }
    }
}
