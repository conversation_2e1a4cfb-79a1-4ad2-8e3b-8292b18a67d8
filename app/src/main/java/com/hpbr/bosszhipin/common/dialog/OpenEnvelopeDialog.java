package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.Scale;

/**
 * 作者：guofeng
 * ＊ 日期:16/11/3
 * 拆红包窗口
 */

public class OpenEnvelopeDialog {

    private Activity activity;
    private Dialog dialog;

    public OpenEnvelopeDialog(Activity activity) {
        this.activity = activity;
    }

    private OnEnvelopeOpenListener listener;

    public void setOnEnvelopeOpenListener(OnEnvelopeOpenListener listener) {
        this.listener = listener;
    }

    public void show() {
        if (activity == null || activity.isFinishing()) return;
        View view = LayoutInflater.from(activity).inflate(R.layout.view_open_envelope_dialog, null);
        dialog = new Dialog(activity, R.style.common_cancelable_dialog);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.show();
        int width = App.get().getDisplayWidth() - Scale.dip2px(activity, 40);
        int height = (int) (width * 1.382);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = width;
            params.height = height;
            params.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(params);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        }
        MTextView fromName = (MTextView) view.findViewById(R.id.tv_name);
        MTextView envelopeDesc = (MTextView) view.findViewById(R.id.tv_desc);
        MTextView greetWords = (MTextView) view.findViewById(R.id.tv_greet);
        ImageView openEnvelope = (ImageView) view.findViewById(R.id.iv_open);

        //px下缩放比例
        float scale = width / (float) Scale.dip2px(activity, 601);
        //动态计算标题距离
        RelativeLayout.LayoutParams fromParams = (RelativeLayout.LayoutParams) fromName.getLayoutParams();
        fromParams.topMargin = (int) (Scale.dip2px(activity, 140) * scale);
        fromName.setLayoutParams(fromParams);
        //动态计算描述距离
        RelativeLayout.LayoutParams envelopeParams = (RelativeLayout.LayoutParams) envelopeDesc.getLayoutParams();
        envelopeParams.topMargin = (int) (Scale.dip2px(activity, 216) * scale);
        envelopeDesc.setLayoutParams(envelopeParams);
        //动态计算问候语距离
        RelativeLayout.LayoutParams greetParams = (RelativeLayout.LayoutParams) greetWords.getLayoutParams();
        greetParams.topMargin = (int) (Scale.dip2px(activity, 328) * scale);
        greetWords.setLayoutParams(greetParams);
        //动态计算打开按钮距离
        RelativeLayout.LayoutParams openParams = (RelativeLayout.LayoutParams) openEnvelope.getLayoutParams();
        openParams.topMargin = (int) (Scale.dip2px(activity, 468) * scale);
        openParams.width = (int) (Scale.dip2px(activity, 184) * scale);
        openParams.height = (int) (Scale.dip2px(activity, 180) * scale);
        openEnvelope.setLayoutParams(openParams);

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        openEnvelope.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) listener.openEnvelope();
            }
        });
    }


    private void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    public interface OnEnvelopeOpenListener {
        void openEnvelope();
    }
}
