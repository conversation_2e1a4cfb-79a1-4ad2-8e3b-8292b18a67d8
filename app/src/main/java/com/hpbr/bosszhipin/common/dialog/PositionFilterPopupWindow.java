package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.main.views.BaseFilterView;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * 作者：ZhouYou
 * 日期：2017/2/7.
 */
public class PositionFilterPopupWindow extends BaseFilterView implements View.OnClickListener {

    public static final int POSITION_ALL = 0;
    public static final int POSITION_ONLINE = 1;
    public static final int POSITION_WAIT_FOR_OPENING = 2;
    public static final int POSITION_CLOSED = 3;
    public static final int POSITION_NOT_PASS = 4;


    private LayoutInflater inflater;
    private OnPositionFilteringListener listener;
    private int selectType = POSITION_ALL;

    public void setOnPositionFilteringListener(OnPositionFilteringListener listener) {
        this.listener = listener;
    }

    public PositionFilterPopupWindow(Activity activity, View operationView) {
        super(activity, operationView, 0);
        this.activity = activity;
        inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
    }

    public void setSelectType(int selectType) {
        this.selectType = selectType;
    }

    @Override
    protected boolean haveData() {
        return true;
    }

    @Override
    protected View getView() {
        @SuppressLint("InflateParams") View view = inflater.inflate(R.layout.view_popup_position_filter, null);
        LinearLayout llParent = view.findViewById(R.id.ll_parent);
        llParent.setOnClickListener(this);
        view.findViewById(R.id.rl_position_all).setOnClickListener(this);
        view.findViewById(R.id.rl_position_online).setOnClickListener(this);
        view.findViewById(R.id.rl_position_wait_for_opening).setOnClickListener(this);
        view.findViewById(R.id.rl_position_closed).setOnClickListener(this);
        view.findViewById(R.id.rl_position_failed_to_pass).setOnClickListener(this);

        MTextView tvPositionAll = view.findViewById(R.id.tv_position_all);
        MTextView tvPositionOnline = view.findViewById(R.id.tv_position_online);
        MTextView tvPositionWaitForOpening = view.findViewById(R.id.tv_position_wait_for_opening);
        MTextView tvPositionClosed = view.findViewById(R.id.tv_position_closed);
        MTextView tvPositionFailedToPass = view.findViewById(R.id.tv_position_failed_to_pass);
        //重置字体颜色和粗体
        tvPositionAll.getPaint().setFakeBoldText(false);
        tvPositionOnline.getPaint().setFakeBoldText(false);
        tvPositionWaitForOpening.getPaint().setFakeBoldText(false);
        tvPositionClosed.getPaint().setFakeBoldText(false);
        tvPositionFailedToPass.getPaint().setFakeBoldText(false);

        tvPositionAll.setTextColor(ContextCompat.getColor(activity, R.color.text_c2));
        tvPositionOnline.setTextColor(ContextCompat.getColor(activity, R.color.text_c2));
        tvPositionWaitForOpening.setTextColor(ContextCompat.getColor(activity, R.color.text_c2));
        tvPositionClosed.setTextColor(ContextCompat.getColor(activity, R.color.text_c2));
        tvPositionFailedToPass.setTextColor(ContextCompat.getColor(activity, R.color.text_c2));

        switch (selectType) {
            case POSITION_ALL:
                tvPositionAll.getPaint().setFakeBoldText(true);
                tvPositionAll.setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                tvPositionAll.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.tick_with_tint, 0);
                break;
            case POSITION_ONLINE:
                tvPositionOnline.getPaint().setFakeBoldText(true);
                tvPositionOnline.setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                tvPositionOnline.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.tick_with_tint, 0);
                break;
            case POSITION_WAIT_FOR_OPENING:
                tvPositionWaitForOpening.getPaint().setFakeBoldText(true);
                tvPositionWaitForOpening.setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                tvPositionWaitForOpening.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.tick_with_tint, 0);
                break;
            case POSITION_CLOSED:
                tvPositionClosed.getPaint().setFakeBoldText(true);
                tvPositionClosed.setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                tvPositionClosed.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.tick_with_tint, 0);
                break;
            case POSITION_NOT_PASS:
                tvPositionFailedToPass.getPaint().setFakeBoldText(true);
                tvPositionFailedToPass.setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                tvPositionFailedToPass.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.tick_with_tint, 0);
                break;
            default:
                break;
        }
        return view;
    }

    @Override
    public void onClick(View v) {
        dismiss();
        int i = v.getId();
        if (i == R.id.rl_position_all) {
            listener.onPositionFilter(POSITION_ALL);

        } else if (i == R.id.rl_position_online) {
            listener.onPositionFilter(POSITION_ONLINE);

        } else if (i == R.id.rl_position_wait_for_opening) {
            listener.onPositionFilter(POSITION_WAIT_FOR_OPENING);

        } else if (i == R.id.rl_position_closed) {
            listener.onPositionFilter(POSITION_CLOSED);

        } else if (i == R.id.rl_position_failed_to_pass) {
            listener.onPositionFilter(POSITION_NOT_PASS);

        }
    }

    public interface OnPositionFilteringListener {
        void onPositionFilter(int filterState);
    }
}
