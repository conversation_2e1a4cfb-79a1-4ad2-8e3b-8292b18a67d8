package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.utils.ActivityUtils;

public class PreviewResumeMoreDialog {

    @NonNull
    private final Activity activity;

    @Nullable
    private BottomView bottomView;

    public PreviewResumeMoreDialog(@NonNull Activity activity) {
        this.activity = activity;
    }

    public void show(@NonNull Callback callBack) {
        if (!ActivityUtils.isValid(activity)) {
            return;
        }

        @SuppressLint("InflateParams") View view = ((LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.dialog_preview_resume_more, null);

        MTextView tv_share = view.findViewById(R.id.tv_share);
        MTextView tv_virtual_phone_explain = view.findViewById(R.id.tv_virtual_phone_explain);
        MTextView tvCancel = view.findViewById(R.id.tv_cancel);

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);

        tvCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                callBack.onCancelClickListener();
            }
        });
        tv_share.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                callBack.onShareClick();
            }
        });
        tv_virtual_phone_explain.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                callBack.onVirtualPhoneExplainClick();
            }
        });
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public interface Callback {

        void onCancelClickListener();

        void onShareClick();

        void onVirtualPhoneExplainClick();
    }

}
