package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.PostAnonymousGeekFeedbackRequest;
import net.bosszhipin.api.PostAnonymousGeekFeedbackResponse;
import net.bosszhipin.api.bean.ServerGeekFeedBack;
import net.bosszhipin.api.bean.ServerGeekListBean;
import net.bosszhipin.base.ApiRequestCallback;

import androidx.annotation.NonNull;

/**
 * create by guofeng
 * date on 2020/8/25
 */
public class RecommendAnonymousFeedBackDialog {

    private static final int DURATION_MILLIS = 200;

    public static final int SOURCE_FROM_ADVANCED_SEARCH_LIST = 1;
    public static final int SOURCE_FROM_F1_REFINED_LIST = 2;
    public static final int SOURCE_FROM_F2_MID_PAGE_SUBSCRIBE_RECOMMEND_LIST = 3;
    public static final int SOURCE_FROM_F2_MID_PAGE_KEYWORD_RECOMMEND_LIST = 4;
    public static final int SOURCE_FROM_F1_RELATIVE_RECOMMEND_LIST = 5;
    public static final int SOURCE_FROM_F2_MID_PAGE_NEW_GEEK_RECOMMEND_LIST = 6;

    private Context context;

    private BottomView bottomView;

    private RelativeLayout mContainer;

    private int source;

    public RecommendAnonymousFeedBackDialog(Context mContext) {
        this.context = mContext;
    }

    private OnPostCallBack postCallBack;

    public void setPostCallBack(OnPostCallBack postCallBack) {
        this.postCallBack = postCallBack;
    }

    @NonNull
    private ServerGeekListBean data;

    public void setData(@NonNull ServerGeekListBean data) {
        this.data = data;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public void showView() {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recommend_geek_feed_back, null);
        mContainer = view.findViewById(R.id.mContainer);
        mContainer.removeAllViews();
        mContainer.addView(createFirstLevel());
        view.findViewById(R.id.mCover).setOnClickListener(v -> dismiss());
        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    public interface OnItemClickCallBack {
        void onItemClickListener(ServerGeekFeedBack item);
    }

    // 创建一级LEVEL
    private View createFirstLevel() {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recommend_feed_back, null);
        LinearLayout mItemContainer = view.findViewById(R.id.mItemContainer);
        MTextView mTitle = view.findViewById(R.id.mTitle);
        ImageView ivClose = view.findViewById(R.id.iv_close);
        ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });
        mTitle.setText(data.feedbackTitle);
        mItemContainer.removeAllViews();
        if (!LList.isEmpty(data.feedbackCodeConfigList)) {
            for (ServerGeekFeedBack item : data.feedbackCodeConfigList) {
                if (item == null) continue;
                View convertView = LayoutInflater.from(context).inflate(R.layout.item_recommend_feed_back_first_level, null);
                ImageView ivArrow = convertView.findViewById(R.id.mArrow);
                ivArrow.setVisibility(LList.isEmpty(item.feedbackL2List) ? View.GONE : View.VISIBLE);
                MTextView mTitleText = convertView.findViewById(R.id.mTitleText);
                mTitleText.setText(item.memo);
                mItemContainer.addView(convertView);
                convertView.setOnClickListener(v -> onFirstLevelItemClickListener(item));
            }
        }
        return view;
    }


    //点击一级ITEM
    private void onFirstLevelItemClickListener(ServerGeekFeedBack item) {
        ////0展示文本，1展示输入框
        if (item.showType == 0) {
            if (LList.isEmpty(item.feedbackL2List)) {
                dismiss();
                postDataToServer("", item.code);
            } else {
                //显示二级View
                showSecondLevelViewWithAnimation(item);
            }
            return;
        }
        if (item.showType == 1) {//1展示输入框
            dismiss();
            showInputDialog(item.code);
        }
    }

    //显示二级View
    private void showSecondLevelViewWithAnimation(ServerGeekFeedBack item) {
        if (mContainer.getChildCount() > 0) {
            View firstLevel = mContainer.getChildAt(0);
            if (firstLevel == null) return;
            TranslateAnimation firstLevelAnimation = new TranslateAnimation(
                    Animation.RELATIVE_TO_SELF, 0f, Animation.RELATIVE_TO_SELF, -1f,
                    Animation.RELATIVE_TO_SELF, 0f, Animation.RELATIVE_TO_SELF, 0f
            );
            firstLevelAnimation.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    //level1的高度
                    int sourHeight = mContainer.getMeasuredHeight();
                    mContainer.removeAllViews();
                    View secondLevel = createSecondLevel(item);
                    int measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                    secondLevel.measure(measureSpec, measureSpec);
                    //level2的高度
                    int newHeight = secondLevel.getMeasuredHeight();
                    mContainer.addView(secondLevel);

                    if (newHeight <= sourHeight) {
                        ViewGroup.LayoutParams layoutParams = mContainer.getLayoutParams();
                        layoutParams.height = sourHeight;
                        mContainer.setLayoutParams(layoutParams);
                    }

                    TranslateAnimation translate = new TranslateAnimation(
                            Animation.RELATIVE_TO_SELF, 1f, Animation.RELATIVE_TO_SELF, 0f,
                            Animation.RELATIVE_TO_SELF, 0f, Animation.RELATIVE_TO_SELF, 0f
                    );
                    translate.setDuration(DURATION_MILLIS);
                    secondLevel.startAnimation(translate);
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
            firstLevelAnimation.setDuration(DURATION_MILLIS);
            firstLevel.startAnimation(firstLevelAnimation);
        }
    }

    //显示输入弹窗
    private void showInputDialog(long reasonType) {
        DialogUtils d = new DialogUtils.InputBuilder((Activity) context)
                .setTitle("输入原因")
                .setMaxInput(50)
                .setInputHint("请输入")
                .setNegativeAction(R.string.string_cancel)
                .setPositiveAction("确定", input -> {
                    if (TextUtils.isEmpty(input)) {
                        ToastUtils.showText("您还没有输入哦~");
                    } else if (StringUtil.getChineseCount(input) > 2 * 50) {
                        ToastUtils.showText("最多只能输入50个字哦~");
                    } else {
                        postDataToServer(input, reasonType);
                    }
                })
                .build();
        d.showInput();
    }

    //提交数据到服务器
    private void postDataToServer(String customText, long reasonType) {
        PostAnonymousGeekFeedbackRequest request = new PostAnonymousGeekFeedbackRequest(new ApiRequestCallback<PostAnonymousGeekFeedbackResponse>() {
            @Override
            public void onSuccess(ApiData<PostAnonymousGeekFeedbackResponse> data) {
                dismiss();
                if (postCallBack != null) {
                    postCallBack.onPostSuccess();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());

            }
        });
        request.securityId = data.securityId;
        request.feedbackType = 2;
        request.content = customText;
        request.code = reasonType;
        request.source = source;
        request.lid = data.lid;
        HttpExecutor.execute(request);
    }

    //创建二级LEVEL
    private View createSecondLevel(ServerGeekFeedBack item) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recommend_feed_back, null);
        LinearLayout mItemContainer = view.findViewById(R.id.mItemContainer);
        MTextView mTitle = view.findViewById(R.id.mTitle);
        mTitle.setText(item.titleL2);

        ImageView ivClose = view.findViewById(R.id.iv_close);
        ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

        mItemContainer.removeAllViews();
        if (item.feedbackL2List != null) {
            for (ServerGeekFeedBack data : item.feedbackL2List) {
                if (data == null) continue;
                View convertView = LayoutInflater.from(context).inflate(R.layout.item_recommend_feed_back_first_level, null);
                MTextView mTitleText = convertView.findViewById(R.id.mTitleText);
                ImageView mArrow = convertView.findViewById(R.id.mArrow);
                mArrow.setVisibility(View.GONE);
                mTitleText.setText(data.memo);
                mItemContainer.addView(convertView);
                convertView.setOnClickListener(v -> {
                    //关闭弹窗
                    dismiss();
                    //////0展示文本，1展示输入框
                    if (data.showType == 1) {
                        showInputDialog(data.code);
                        return;
                    }
                    //提交数据到服务器
                    postDataToServer("", data.code);
                });
            }
        }

        return view;
    }

    //关闭窗口
    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }


    public interface OnPostCallBack {
        void onPostSuccess();
    }

}