package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.my.entity.WorkBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.PositionCardTagLayout;
import com.hpbr.bosszhipin.views.wheelview.workexp.WorkExpUtil;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.ui.CollapseTextView2;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2019/1/21.
 */

public class ReplaceWorkContentDialog {

    private Activity activity;
    private BottomView bottomView;
    private WorkBean workBean;

    private List<Long> expandCache = new ArrayList<>();

    public ReplaceWorkContentDialog(Activity activity, WorkBean workBean) {
        this.activity = activity;
        this.workBean = workBean;
    }

    private OnWorkExpActionCallback actionCallback;

    public void setOnItemClickCallback(OnWorkExpActionCallback actionCallback) {
        this.actionCallback = actionCallback;
    }

    private MTextView mCompany;
    private MTextView mRange;
    private MTextView mPosition;
    private CollapseTextView2 mContent;
    private PositionCardTagLayout tagLayout;

    public void show() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_replace_work_content, null);
        mCompany = view.findViewById(R.id.tv_company);
        mRange = view.findViewById(R.id.tv_range);
        mPosition = view.findViewById(R.id.tv_position);
        mContent = view.findViewById(R.id.tv_content);
        mContent.setCollapseColor(R.color.text_c1);
        mContent.setCollapseTextBold();
        tagLayout = view.findViewById(R.id.ll_tags);

        setExpData();

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        view.findViewById(R.id.btn_replace_exp).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (actionCallback != null) {
                    actionCallback.onWorkExpReplace(workBean);
                }
            }
        });
        view.findViewById(R.id.btn_add_exp).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (actionCallback != null) {
                    actionCallback.onWorkExpAdd(workBean);
                }
            }
        });
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView2(false);
    }

    private void setExpData() {
        if (workBean != null) {
            mCompany.setText(workBean.company);
            if (workBean.startDate > 0) {
                String start = WorkExpUtil.date8ToText(workBean.startDate);
                String end = WorkExpUtil.date8ToText(workBean.endDate);
                mRange.setText(start + "-" + end);
                mRange.setVisibility(View.VISIBLE);
            } else {
                mRange.setVisibility(View.GONE);
            }
            mPosition.setText(workBean.positionClassName);

            mContent.setVisibility(!LText.empty(workBean.responsibility) ? View.VISIBLE : View.GONE);

            if (expandCache.contains(workBean.updateId)) {
                mContent.setWrapContentText(workBean.responsibility);
            } else {
                mContent.setExpandText("展开");
                mContent.initWidth(App.get().getDisplayWidth() - Scale.dip2px(App.getAppContext(), 78));
                mContent.setMaxLines(3);
                mContent.setCloseText(workBean.responsibility);
                mContent.setMovementMethod(LinkMovementMethod.getInstance());
                mContent.setOnTextExpandListener(isExpand -> {
                    if (isExpand) {
                        expandCache.add(workBean.updateId);
                    }
                });
            }
            List<String> list = StringUtil.splitKeywords(workBean.workEmphasis);
            tagLayout.addView(list, R.drawable.bg_gray_empty_corner_radius3, ContextCompat.getColor(App.getAppContext(), R.color.text_c6));

        }
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    public interface OnWorkExpActionCallback {
        void onWorkExpAdd(WorkBean workBean);

        void onWorkExpReplace(WorkBean workBean);
    }

}
