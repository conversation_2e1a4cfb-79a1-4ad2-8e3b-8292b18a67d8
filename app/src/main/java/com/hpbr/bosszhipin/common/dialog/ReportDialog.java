package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ListView;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LList;

import java.util.List;

/**
 * Created by monch on 2016/12/1.
 */

public class ReportDialog {

    //面试爽约举报类型
    public static final LevelBean RESON_INTEVIEW = new LevelBean(2020, "面试爽约");
    public static final LevelBean RESON_CANCEL = new LevelBean(0, "取消");

    private BottomView bottomView;
    private OnReportListener listener;

    private ExtarReson extarReson;

    public ExtarReson getExtarReson() {
        return extarReson;
    }

    public void setExtarReson(ExtarReson extarReson) {
        this.extarReson = extarReson;
    }

    public void setListener(OnReportListener listener) {
        this.listener = listener;
    }

    private OnItemClickCallBack callBack;

    public void setCallBack(OnItemClickCallBack callBack) {
        this.callBack = callBack;
    }

    public void show(final Context context) {
        show(context, 0, 0, 0);
    }

    public void show(final Context context, final long reportUserId, long jobId, long expectId) {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing()))
            return;
        final List<LevelBean> items = VersionAndDatasCommon.getInstance().getReportList();
        if (!LList.isEmpty(items)) {
            items.add(RESON_CANCEL);

            //加入额外数据
            if (null != getExtarReson() && getExtarReson().position >= 0) {
                addToPosition(items, getExtarReson().levelBeans, getExtarReson().position);
            } else if (null != getExtarReson() && getExtarReson().beforeAnchorCode >= 0) {
                addToBeforAnchor(items, getExtarReson().levelBeans, getExtarReson().beforeAnchorCode);
            }
        }
        View view = LayoutInflater.from(context).inflate(R.layout.view_report_dialog, null);
        ListView listView = view.findViewById(R.id.list_view);
        listView.setAdapter(new ReportListAdapter(context, items));
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                bottomView.dismissBottomView();
                Object obj = parent.getItemAtPosition(position);
                if (obj != null && obj instanceof LevelBean) {
                    LevelBean item = (LevelBean) obj;
                    if (listener != null && item.code > 0) {
                        listener.onReportAction(position, reportUserId, jobId, expectId, item.code, item.name);
                    }
                    if (callBack != null) {
                        callBack.onItemClickListener(item.name, item.code);
                    }
                }
            }
        });
        bottomView = new BottomView(context, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    private void addExtarReson() {

        ExtarReson extarReson = getExtarReson();
        if (null != extarReson && LList.getCount(extarReson.levelBeans) > 0) {


        }
    }

    //讲额外原因数据加在指定位置
    private void addToPosition(List<LevelBean> desc, List<LevelBean> src, int position) {

        if (LList.getCount(desc) <= 0 || LList.getCount(src) <= 0) return;

        if (position < 0) position = 0;
        if (position >= desc.size()) position = desc.size();

        desc.addAll(position, src);
    }

    //讲额外原因数据加载指定code位置
    private void addToBeforAnchor(List<LevelBean> desc, List<LevelBean> src, int anchorCode) {

        if (LList.getCount(desc) <= 0 || LList.getCount(src) <= 0) return;

        int pos = 0;

        for (int i = 0; i < desc.size(); i++) {
            LevelBean levelBean = desc.get(i);
            if (null == levelBean) continue;
            if (levelBean.code == anchorCode) {
                pos = i;
                break;
            }
        }

        desc.addAll(pos, src);
    }

    public interface OnItemClickCallBack {
        void onItemClickListener(String text, long code);
    }

    private static class ReportListAdapter extends LBaseAdapter<LevelBean> {

        public ReportListAdapter(Context context, List<LevelBean> data) {
            super(context, data);
        }

        @Override
        public View getView(int position, View convertView, LevelBean item, LayoutInflater inflater) {
            ReportViewHolder holder;
            if (convertView == null) {
                convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_report_data_view, null);
                holder = new ReportViewHolder(convertView);
                convertView.setTag(holder);
            } else {
                holder = (ReportViewHolder) convertView.getTag();
            }
            if (item != null) {
                holder.tvReportName.setText(item.name);
                if (item.code <= 0) {
                    holder.tvReportName.setTextColor(Color.BLUE);
                } else {
                    holder.tvReportName.setTextColor(ContextCompat.getColor(getContext(), R.color.text_c6));
                }
            }
            return convertView;
        }

        static class ReportViewHolder {

            MTextView tvReportName;

            ReportViewHolder(View convertView) {
                tvReportName = convertView.findViewById(R.id.tv_report_name);
            }
        }
    }

    public interface OnReportListener {
        void onReportAction(int index, long reportUserId, long jobId, long expectId, long code, String name);
    }

    public static class ExtarReson {
        //加在指定位置
        public int position = -1;
        public int beforeAnchorCode = -1;
        //额外数据
        public List<LevelBean> levelBeans;

        public ExtarReson(List<LevelBean> levelBeans) {
            this.levelBeans = levelBeans;
        }

        public ExtarReson setPosition(int position) {
            this.position = position;
            return this;
        }

        public ExtarReson setBeforeAnchorCode(int beforeAnchorCode) {
            this.beforeAnchorCode = beforeAnchorCode;
            return this;
        }

        public ExtarReson setLevelBeans(List<LevelBean> levelBeans) {
            this.levelBeans = levelBeans;
            return this;
        }
    }
}
