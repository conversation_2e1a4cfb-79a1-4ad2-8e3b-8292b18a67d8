package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.my.interfaces.IReportListener;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;

import java.util.List;

/**
 * Created by zhouyou on 2015/11/2.
 */
public class ReportIndustryDialog {

    private Activity context;
    private Dialog d;
    private List<LevelBean> list;

    private IReportListener listener;

    public ReportIndustryDialog(Activity context, List<LevelBean> list) {
        this.context = context;
        this.list = list;
        if (context instanceof IReportListener) {
            this.listener = (IReportListener) context;
        }
    }

    public void show() {
        if (list == null || list.size() <= 0) return;
        d = new Dialog(context, R.style.common_dialog);
        d.setCanceledOnTouchOutside(true);
        View v = LayoutInflater.from(context).inflate(R.layout.view_report, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();

        WindowManager.LayoutParams params = window.getAttributes();
        params.dimAmount = 0.6f;
        window.setAttributes(params);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

        MTextView tvReportTitle = (MTextView) v.findViewById(R.id.tv_report_title);
        tvReportTitle.setText("您要找的行业可能是");
        LinearLayout llKeywords = (LinearLayout) v.findViewById(R.id.ll_keywords);
        addTextView(llKeywords);
        v.findViewById(R.id.tv_report_directly).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (listener != null) {
                    listener.reportDirectly();
                }
            }
        });

        d.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if (listener != null) {
                    listener.onReportInterrupt();
                }
            }
        });

        if (context != null && !context.isFinishing() && !d.isShowing()) {
            d.show();
        }
    }

    private void addTextView(LinearLayout ll) {
        int padding15 = Scale.dip2px(context, 15);
        int margin5 = Scale.dip2px(context, 5);
        int margin10 = Scale.dip2px(context, 10);
        for (LevelBean item : list) {
            if (item == null || LText.empty(item.name)) continue;

            MTextView tv = new MTextView(context);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, Scale.dip2px(context, 35));
            params.topMargin = params.bottomMargin = margin5;
            params.leftMargin = params.rightMargin = margin10;
            tv.setLayoutParams(params);
            tv.setPadding(padding15, 0, padding15, 0);
            tv.setGravity(Gravity.CENTER);
            tv.setBackgroundResource(R.drawable.bg_selector_green_button);
            tv.setOnClickListener(new OnKeywordTextClickListener(item));
            tv.setText(item.name);
            tv.setTextColor(Color.WHITE);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            ll.addView(tv);
        }
    }

    private void dismiss() {
        if (d != null) {
            d.dismiss();
        }
    }

    private class OnKeywordTextClickListener implements View.OnClickListener {

        private LevelBean item;

        public OnKeywordTextClickListener(LevelBean item) {
            this.item = item;
        }

        @Override
        public void onClick(View v) {
            dismiss();
            if (listener != null) {
                listener.onKeywordClick(item);
            }

        }
    }
}
