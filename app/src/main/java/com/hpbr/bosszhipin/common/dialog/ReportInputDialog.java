package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MEditText;
import com.monch.lbase.util.LText;

/**
 * Created by zhouyou on 2015/11/8.
 */
public class ReportInputDialog {

    /**
     * @param input 输入内容
     * @param searchOrNot 0 - 不查询相关内容 | 1 - 查询相关内容
     */
    public static final int REQUEST_NO_SEARCH = 0;
    public static final int REQUEST_SEARCH = 1;

    private Activity context;
    private Dialog d;
    private MEditText etInput;
    private String promptText;

    private IOnReportAction listener;

    public ReportInputDialog(Activity context) {
        this.context = context;
        if (context instanceof IOnReportAction) {
            this.listener = (IOnReportAction) context;
        }
    }

    public void setPromptText(String text) {
        promptText = text;
    }

    public void show() {
        d = new Dialog(context, R.style.common_dialog);
        d.setCanceledOnTouchOutside(true);
        View v = LayoutInflater.from(context).inflate(R.layout.view_report_input, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();

        WindowManager.LayoutParams params = window.getAttributes();
        params.dimAmount = 0.6f;
        window.setAttributes(params);
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);

        if (!TextUtils.isEmpty(promptText)) {
            TextView promptTextView = (TextView) v.findViewById(R.id.tv_prompt_text);
            promptTextView.setText(promptText);
        }
        etInput = (MEditText) v.findViewById(R.id.et_input);
        v.findViewById(R.id.tv_cancel).setOnClickListener(new MyOnClickListener());
        v.findViewById(R.id.tv_report).setOnClickListener(new MyOnClickListener());

        if (context != null && !context.isFinishing() && !d.isShowing()) {
            d.show();
        }

        d.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                if (listener != null) {
                    listener.onCancel();
                }
            }
        });
    }

    private void dismiss() {
        if (d != null) {
            d.dismiss();
        }
    }

    private class MyOnClickListener implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            int i = v.getId();
            if (i == R.id.tv_report) {
                String input = etInput.getText().toString().trim();

                if (LText.empty(input)) {
                    AnimUtil.emptyInputAnim(etInput);
                } else if (StringUtil.getChineseCount(input) > 2 * 10) {
                    AnimUtil.errorInputAnim(etInput, "最多可输入10个字");
                } else {
                    dismiss();
                    if (listener != null) {
                        listener.onReport(input);
                    }
                }


            } else if (i == R.id.tv_cancel) {
                dismiss();
                if (listener != null) {
                    listener.onCancel();
                }

            } else {
            }
        }
    }

    public interface IOnReportAction {
        void onReport(String input);

        void onCancel();
    }
}
