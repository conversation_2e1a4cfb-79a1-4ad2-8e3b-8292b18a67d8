package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.DialogInterface;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.module_boss_export.BossUrlConfig;
import com.hpbr.bosszhipin.module_boss_export.OnJobCloseCallback;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GetBossReportDisposeJobListResponse;
import net.bosszhipin.api.bean.ServerBossReportJobBean;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2022/8/23
 */
public class ReportOnlineJobDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final GetBossReportDisposeJobListResponse data;
    private final int itemCount;

    public ReportOnlineJobDialog(Activity activity, @NonNull GetBossReportDisposeJobListResponse data) {
        this.activity = activity;
        this.data = data;

        itemCount = LList.getCount(data.list);
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_dialog_report_online_job, null);

        RecyclerView rvList = view.findViewById(R.id.rv_list);

        ConstraintLayout.LayoutParams listParams = (ConstraintLayout.LayoutParams) rvList.getLayoutParams();
        if (itemCount >= 3) {
            listParams.height = 0;
        } else {
            listParams.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
        }
        rvList.setLayoutParams(listParams);

        Adapter mAdapter = new Adapter(data.list);
        mAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                ServerBossReportJobBean item = (ServerBossReportJobBean) adapter.getItem(position);
                if (item == null) {
                    return;
                }
                ParamBean paramBean = new ParamBean();
                paramBean.userId = UserManager.getUID();
                paramBean.jobId = item.jobId;
                paramBean.securityId = item.securityId;
                BossPageRouter.jumpToBossSelfActivity(activity, paramBean);

                dismiss();
            }
        });
        mAdapter.setOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                ServerBossReportJobBean item = (ServerBossReportJobBean) adapter.getItem(position);
                if (item == null) {
                    return;
                }
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_INFO_JOB_POPUP_STOP_CLICK)
                        .param("p", item.jobId)
                        .build();
                closeJob(mAdapter, item);
            }
        });
        rvList.setAdapter(mAdapter);

        FrameLayout footerView = new FrameLayout(activity);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ZPUIDisplayHelper.dp2px(activity, 16f));
        footerView.setLayoutParams(params);
        mAdapter.addFooterView(footerView);

        view.findViewById(R.id.iv_close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_INFO_JOB_POPUP_STOP_CLICK)
                        .param("p", 0)
                        .build();
            }
        });

        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_INFO_JOB_POPUP_STOP_CLICK)
                        .param("p", 0)
                        .build();
            }
        });
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void show() {
        if (data == null) return;
        if (ActivityUtils.isValid(activity)) {
            dialogShowRequest();

            if (itemCount > 3) {
                int dialogHeight = (int) (ZPUIDisplayHelper.getScreenHeight(activity) * 0.5f);
                bottomView.showBottomView5(true, dialogHeight);
            } else {
                bottomView.showBottomView(true);
            }
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void closeJob(@NonNull Adapter adapter, @NonNull ServerBossReportJobBean item) {
        BossPageRouter.closeJob(activity, item.jobId, new OnJobCloseCallback() {
            @Override
            public void onJobCloseSuccess() {
                item.closed = 1;
                adapter.notifyDataSetChanged();

                boolean allClosed = true;
                for (ServerBossReportJobBean item : adapter.getData()) {
                    if (item == null) continue;
                    if (item.closed == 0) {
                        allClosed = false;
                        break;
                    }
                }

                if (allClosed) {
                    dismiss();
                }
            }
        });
    }

    private static class Adapter extends BaseRvAdapter<ServerBossReportJobBean, BaseViewHolder> {

        public Adapter(@Nullable List<ServerBossReportJobBean> data) {
            super(R.layout.item_report_online_job, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerBossReportJobBean item) {
            if (item == null) return;
            MTextView tvJobName = helper.getView(R.id.tv_job_name);
            MTextView tvJobInfo = helper.getView(R.id.tv_job_info);
            ZPUIRoundButton btnConfirm = helper.getView(R.id.btn_confirm);

            tvJobName.setText(item.jobName);
            List<String> jobInfos = new ArrayList<>();
            if (!TextUtils.isEmpty(item.addressShowText)) {
                jobInfos.add(item.addressShowText);
            }
            if (!TextUtils.isEmpty(item.degreeName)) {
                jobInfos.add(item.degreeName);
            }
            if (!TextUtils.isEmpty(item.experienceName)) {
                jobInfos.add(item.experienceName);
            }
            if (!TextUtils.isEmpty(item.salaryDesc)) {
                jobInfos.add(item.salaryDesc);
            }
            createJobInfo(tvJobInfo, jobInfos);

            if (item.closed == 1) {
                btnConfirm.setText("已关闭");
                btnConfirm.setEnabled(false);
            } else {
                btnConfirm.setText("关闭职位");
                btnConfirm.setEnabled(true);
            }

            helper.addOnClickListener(R.id.btn_confirm);
        }

        private void createJobInfo(MTextView tv, List<String> jobInfos) {
            if (LList.isEmpty(jobInfos)) return;
            StringBuilder stb = new StringBuilder();
            int max = jobInfos.size() - 1;
            for (int i = 0; i <= max; i++) {
                String info = jobInfos.get(i);
                if (TextUtils.isEmpty(info)) continue;
                stb.append(info);
                if (i != max) {
                    stb.append("  |  ");
                }
            }
            CharSequence cs = formatString(stb.toString(), "\\|", ContextCompat.getColor(mContext, R.color.app_divider1));
            tv.setText(cs);
        }

        /**
         * 将字符串中的特定 字符高亮
         */
        private CharSequence formatString(String text, String regular, int colorResId) {

            try {
                SpannableStringBuilder builder = new SpannableStringBuilder(text);

                Pattern p = Pattern.compile(regular);
                Matcher matcher = p.matcher(text);
                int start = 0;
                int end = 0;
                while (matcher.find()) {
                    if (matcher.start() == end) {
                        end = matcher.end();
                    } else {
                        if (start != end) {
                            ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                            builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                        }
                        start = matcher.start();
                        end = matcher.end();
                    }
                }
                if (start != end) {
                    ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                    builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                return builder;
            } catch (Exception e) {
                return text;
            }
        }
    }

    private void dialogShowRequest() {
        StringBuilder jobIds = new StringBuilder();
        if (!LList.isEmpty(data.list)) {
            int size = LList.getCount(data.list);
            for (int i = 0; i < size; i++) {
                ServerBossReportJobBean item = data.list.get(i);
                if (item == null || item.jobId <= 0) continue;
                jobIds.append(item.jobId).append(",");
                if (i == size - 1) {
                    jobIds.append(item.jobId);
                } else {
                    jobIds.append(item.jobId).append(",");
                }
            }
        }
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_USER_INFO_JOB_POPUP_STOP_EXPO)
                .param("p", jobIds.toString())
                .build();
        SimpleApiRequest.GET(BossUrlConfig.URL_BOSS_REPORT_DISPOSE_DIALOG_SHOW).execute();
    }


}
