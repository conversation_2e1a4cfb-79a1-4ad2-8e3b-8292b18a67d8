package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;


/**
 * 1006.10【C】交换求作品集优化 @邹东
 * {@literal https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157169877}
 */
public class RequireResumeStyleDialog {
    //附件简历 默认值
    public static final int RESUME_ATTACHED = 0;
    // 作品集
    public static final int RESUME_PORTFOLIO = 1;

    private Context context;
    private OnExchangeResumeStyleCallBack exchangeResumeStyleCallBack;

    public RequireResumeStyleDialog(Context context, OnExchangeResumeStyleCallBack exchangeResumeStyleCallBack) {
        this.context = context;
        this.exchangeResumeStyleCallBack = exchangeResumeStyleCallBack;
    }

    long friendId;
    long jobId;

    public RequireResumeStyleDialog setParams(long friendId,long jobId){
        this.friendId = friendId;
        this.jobId = jobId;
        return this;
    }
    private BottomView bv;



    public void show() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_exchange_resume_style_dialog, null);
        TextView img_resume_portfolio = view.findViewById(R.id.img_resume_portfolio);
        TextView img_resume_attached = view.findViewById(R.id.img_resume_attached);


        //点击作品集
        img_resume_portfolio.setOnClickListener(v -> {
            dismiss();
            if (exchangeResumeStyleCallBack != null) {
                exchangeResumeStyleCallBack.onClickStyle(v,RESUME_PORTFOLIO);
            }
            AnalyticsFactory.create().action("resume-portfolio-exchange-window-click")
                    .param("p",String.valueOf(friendId))
                    .param("p2",String.valueOf(jobId))
                    .param("p3","1")
                    .build();
        });

        //点击附件简历
        img_resume_attached.setOnClickListener(v -> {
            dismiss();
            if (exchangeResumeStyleCallBack != null) {
                exchangeResumeStyleCallBack.onClickStyle(v,RESUME_ATTACHED);
            }
            AnalyticsFactory.create().action("resume-portfolio-exchange-window-click")
                    .param("p",String.valueOf(friendId))
                    .param("p2",String.valueOf(jobId))
                    .param("p3","2")
                    .build();
        });
        //点击取消
        view.findViewById(R.id.img_cancel).setOnClickListener(v -> {
            dismiss();
            AnalyticsFactory.create().action("resume-portfolio-exchange-window-click")
                    .param("p",String.valueOf(friendId))
                    .param("p2",String.valueOf(jobId))
                    .param("p3","0")
                    .build();
        });

        bv = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bv.setAnimation(R.style.BottomToTopAnim);
        bv.showBottomView(true);
        AnalyticsFactory.create().action("resume-portfolio-exchange-window-expo")
                .param("p",String.valueOf(friendId))
                .param("p2",String.valueOf(jobId))
                .build();
    }


    public void dismiss() {
        if (bv != null) {
            bv.dismissBottomView();
        }
    }

    public interface OnExchangeResumeStyleCallBack {

        void onClickStyle(View view ,int style);
    }
}
