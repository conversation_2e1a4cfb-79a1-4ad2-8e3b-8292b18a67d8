package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerSafeTipOverlayBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class SafeTipBottomDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final ServerSafeTipOverlayBean safeBean;

    private long jobId;

    public SafeTipBottomDialog(Activity activity, ServerSafeTipOverlayBean dialogBean, long jobId) {
        this.activity = activity;
        this.safeBean = dialogBean;
        this.jobId = jobId;
        init();
    }

    private void init() {

        AnalyticsFactory
                .create()
                .action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_POPUP)
                .param("p", safeBean.itemType)//场景id
                .param("p2", safeBean.title)
                .param("p3", safeBean.content)
                .param("p4", jobId)// 弹出时进入的职位id
                .build();

        View view = LayoutInflater.from(activity).inflate(R.layout.view_safe_tip_bottom_dialog, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        SimpleDraweeView sdvIcon = view.findViewById(R.id.sdv_safe_icon);
        ImageView ivCancel = view.findViewById(R.id.iv_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);

        tvTitle.setText(safeBean.title, View.GONE);
        tvDesc.setText(safeBean.content, View.GONE);
        if (!TextUtils.isEmpty(safeBean.picUrl)) {
            sdvIcon.setImageURI(safeBean.picUrl);
        }

        if (null != LList.getElement(safeBean.btnList, 0)) {
            ServerSafeTipOverlayBean.ServerBtnActionBean firstCancel = LList.getElement(safeBean.btnList, 0);
            btnCancel.setText(firstCancel.btnText);
            btnCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dismiss();
                    AnalyticsFactory.create().action("operation-push-safepopup-click").param("p2", 2).build();

                    AnalyticsFactory
                            .create()
                            .action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_POPUPCLICK)
                            .param("p", 1)
                            .param("p2", safeBean.itemType)//场景id
                            .param("p3", safeBean.title)
                            .param("p4", safeBean.content)
                            .param("p5", jobId)// 弹出时进入的职位id
                            .build();
                }
            });
        }

        if (null != LList.getElement(safeBean.btnList, 1)) {
            ServerSafeTipOverlayBean.ServerBtnActionBean confirm = LList.getElement(safeBean.btnList, 1);
            btnConfirm.setText(confirm.btnText);
            btnConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent intent = new Intent(activity, WebViewActivity.class);
                    intent.putExtra(Constants.DATA_URL, confirm.btnUrl);
                    intent.putExtra(Constants.DATA_FROM, WebViewActivity.FROM_GEEK_SKILL_MAP);
                    AppUtil.startActivity(activity, intent);
                    dismiss();
                    AnalyticsFactory.create().action("operation-push-safepopup-click").param("p2", 1).build();

                    AnalyticsFactory
                            .create()
                            .action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_POPUPCLICK)
                            .param("p", 0)
                            .param("p2", safeBean.itemType)//场景id
                            .param("p3", safeBean.title)
                            .param("p4", safeBean.content)
                            .param("p5", jobId)// 弹出时进入的职位id
                            .build();
                }
            });

        }
        ivCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                AnalyticsFactory.create().action("operation-push-safepopup-click").param("p2", 0).build();

                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_ADMIN_USER_SAFETY_POPUPCLICK)
                        .param("p", 2)
                        .param("p2", safeBean.itemType)//场景id
                        .param("p3", safeBean.title)
                        .param("p4", safeBean.content)
                        .param("p5", jobId)// 弹出时进入的职位id
                        .build();


            }
        });


    }

    public void show() {
        if (activity != null && !activity.isFinishing() && bottomView != null) {
            bottomView.showBottomView(false);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }
}
