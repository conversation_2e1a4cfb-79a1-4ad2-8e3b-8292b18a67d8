package com.hpbr.bosszhipin.common.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.SelectImageQualityAdapter;
import com.hpbr.bosszhipin.common.enums.SelectImageQualityType;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.photoselect.PhotoSelectManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;


public class SelectImageQualityDialog extends BaseBottomSheetFragment {
    private static final String TAG = "SelectImageQualityDialog";
    private Context context;
    private SelectImageQualityAdapter adapter;
    private Builder builder;


    private static SelectImageQualityDialog getInstance(Builder builder) {
        SelectImageQualityDialog fragment = new SelectImageQualityDialog();
        fragment.builder = builder;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
        setDraggable(false);
    }

    @Override
    protected boolean enableTouchOutSide() {
        return false;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_select_image_quality, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (context == null) {
            return;
        }
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        btnGo.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                List<SelectImageQualityItemBean> list =  adapter.getData();
                for (SelectImageQualityItemBean selectImageQualityItemBean : list) {
                    if (selectImageQualityItemBean.isSelected){
                        PhotoSelectManager.savePickOriginal(selectImageQualityItemBean.type == SelectImageQualityType.ORIGIN_IMAGE);
                        if (builder.positiveCallBack!=null) {
                            builder.positiveCallBack.onPositiveClick(selectImageQualityItemBean);
                        }
                        break;
                    }
                }
                dismissAllowingStateLoss();
            }
        });
        RecyclerView rvData = view.findViewById(R.id.rvData);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context);
        rvData.setLayoutManager(linearLayoutManager);
        adapter = new SelectImageQualityAdapter();
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                if (!LList.isEmpty(adapter.getData())){
                    for (int i = 0; i < adapter.getData().size(); i++) {
                        adapter.getData().get(i).isSelected =i==position;
                    }
                    adapter.notifyDataSetChanged();
                }

            }
        });
        rvData.setAdapter(adapter);
        adapter.setNewData(builder.list);
        if (!TextUtils.isEmpty(builder.title)) {
            tvTitle.setText(builder.title);
        }
        if (!TextUtils.isEmpty(builder.subTitle)) {
            tvSubTitle.setText(builder.subTitle);
        }
    }
    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
    }


    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
    }


    public static class Builder implements Serializable {
        private static final long serialVersionUID = -6534636826782647865L;
        private String title;
        private String subTitle;

        private PositiveCallBack positiveCallBack;

        public Builder setPositiveCallBack(PositiveCallBack positiveCallBack) {
            this.positiveCallBack = positiveCallBack;
            return this;
        }

        private List<SelectImageQualityItemBean> list;

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setSubTitle(String subTitle) {
            this.subTitle = subTitle;
            return this;
        }

        public Builder setList(List<SelectImageQualityItemBean> list) {
            this.list = list;
            return this;
        }

        public SelectImageQualityDialog build() {
            if (LList.isEmpty(list)) {
                boolean pickOriginal = PhotoSelectManager.isPickOriginal();
                list = new ArrayList<>();
                list.add(new SelectImageQualityItemBean("发送压缩图片", !pickOriginal, SelectImageQualityType.COMPRESS_IMAGE));
                list.add(new SelectImageQualityItemBean("发送原图", pickOriginal, SelectImageQualityType.ORIGIN_IMAGE));
            }
            return SelectImageQualityDialog.getInstance(this);
        }
    }

    public static class SelectImageQualityItemBean implements Serializable {
        private static final long serialVersionUID = -1127183634804092872L;
        public String title;
        public boolean isSelected;
        public int type;

        public SelectImageQualityItemBean(String title, boolean isSelected, int type) {
            this.title = title;
            this.isSelected = isSelected;
            this.type = type;
        }

        @NonNull
        @Override
        public String toString() {
            return "SelectImageQualityItemBean{" +
                    "title='" + title + '\'' +
                    ", isSelected=" + isSelected +
                    ", type=" + type +
                    '}';
        }
    }


    /**
     * 确认，也就是第一个按钮的监听回调
     */
    public interface PositiveCallBack {
        void onPositiveClick(SelectImageQualityItemBean imageQualityItemBean);
    }


}
