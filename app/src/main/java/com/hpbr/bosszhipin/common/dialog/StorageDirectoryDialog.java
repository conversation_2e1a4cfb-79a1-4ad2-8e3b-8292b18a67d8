package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.utils.platform.Utils;
import com.twl.anti.DeviceInfo;
import com.twl.ui.ToastUtils;
import com.twl.ui.buttonlayout.ZPUIDynamicButtonPanelLayout;
import com.twl.ui.buttonlayout.button.PanelButtonStyleDispatcher;
import com.twl.utils.ConverterUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import java.io.File;
import java.io.FileFilter;
import java.util.ArrayList;
import java.util.List;


public class StorageDirectoryDialog {

    private Context context;
    private BottomView bottomView;
    private ZPUIDynamicButtonPanelLayout dynamicButtonPanelLayout;
    private static boolean isShow = false;
    public StorageDirectoryDialog(Context context) {
        this.context = context;
    }

    public void show() {
        if (!isNotEnoughSpace() || isShow) {
            return;
        }
        isShow = true;
        View view = LayoutInflater.from(context).inflate(R.layout.view_storage_view_app, null);
        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        dynamicButtonPanelLayout = view.findViewById(R.id.dynamicButtonPanelLayout);
        buildButtons();
        bottomView.showBottomView(true);
        ApmAnalyzer.create().action("action_temp","minimumStorage").p2(DeviceInfo.getAvailableInternalMemorySize()).report();
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    public static boolean isNotEnoughSpace() {
        if (AndroidDataStarGray.getInstance().minimumStorage() < 1) {
            return false;
        }
        return DeviceInfo.strToInt(DeviceInfo.getAvailableInternalMemorySize()) <= AndroidDataStarGray.getInstance().minimumStorage();
    }

    public static final boolean isExternalFileSize = true;

    public static String getCacheMemorySize() {
        long dirInternalFileSize = FileUtils.getDirFileSize(PathUtils.getCacheDirInternalFile());
        long dirExternalFileSize = 0;
        if (isExternalFileSize) {
            dirExternalFileSize = FileUtils.getDirFileSize(PathUtils.getCacheDirExternalPath());
        }
        return ConverterUtils.byte2FitMemorySize(dirInternalFileSize + dirExternalFileSize);
    }

    public static long getCacheMemory() {
        long dirInternalFileSize = FileUtils.getDirFileSize(PathUtils.getCacheDirInternalFile());
        long dirExternalFileSize = 0;
        if (isExternalFileSize) {
            dirExternalFileSize = FileUtils.getDirFileSize(PathUtils.getCacheDirExternalPath());
        }
        return dirInternalFileSize + dirExternalFileSize;
    }

    /**
     * 渲染底部按钮，根据是否是最后一题进行判断
     */
    private void buildButtons() {

        List<ZPUIDynamicButtonPanelLayout.Button> buttonList = new ArrayList<>();

        ZPUIDynamicButtonPanelLayout.Button closeButton = ZPUIDynamicButtonPanelLayout.Button.create("取消");
        closeButton.key = PanelButtonStyleDispatcher.IButtonKey.GRAY;
        closeButton.buttonWeight = 1;
        closeButton.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        };

        String string = "清除缓存" + getCacheMemorySize();
        ZPUIDynamicButtonPanelLayout.Button rightBtb = ZPUIDynamicButtonPanelLayout.Button.create(string);
        rightBtb.buttonWeight = 2;
        rightBtb.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clearCacheAsync();
                dismiss();
                ApmAnalyzer.create().action("action_temp","clearCache").p2("1").report();
                Utils.runOnUiThreadDelayed(() -> ToastUtils.showText("已" + string), 1000);
            }
        };

        buttonList.add(closeButton);
        buttonList.add(rightBtb);
        dynamicButtonPanelLayout.refreshButtons(buttonList);
        dynamicButtonPanelLayout.build();
    }

    public static void clearCacheAsync() {
        AppThreadFactory.createThread(new Runnable() {
            @Override
            public void run() {
                StorageDirectoryDialog.clearCache();
            }
        }).start();
    }

    public static void clearCache() {
        FileUtils.deleteFilesInDirOnlyFile(PathUtils.getCacheDirInternalFile(), pathname -> true);
        if (isExternalFileSize) {
            String cacheDirExternalPath = PathUtils.getCacheDirExternalPath();
            if (!TextUtils.isEmpty(cacheDirExternalPath)) {
                FileUtils.deleteFilesInDirOnlyFile(new File(cacheDirExternalPath), new FileFilter() {
                    @Override
                    public boolean accept(File pathname) {
                        return !TextUtils.equals(pathname.getName(), "log");
                    }
                });
            }
        }
    }

}
