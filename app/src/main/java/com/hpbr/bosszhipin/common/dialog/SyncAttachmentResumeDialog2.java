package com.hpbr.bosszhipin.common.dialog;

import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.text.HtmlCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.export.ResumeService;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;

import net.bosszhipin.api.bean.ServerSyncResumeInfoBean;
import net.bosszhipin.api.bean.ServerSyncResumeInfoBean.Entry;

import org.json.JSONObject;

import java.util.List;
import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * 1004.6【C】附件简历同步入口常驻&弹窗优化（PC端同步）@林倩 @丁振举@国栋 @权恩民@张亚东 @张祥东@王晓臣
 * <p>
 * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=147059089
 */
public class SyncAttachmentResumeDialog2 extends BaseBottomSheetFragment implements View.OnClickListener {

    @NonNull
    private final ServerSyncResumeInfoBean bean;
    @NonNull
    private final ResumeService.Callback callback;

    private TextView tvTitle;
    private ImageView ivClose;
    private LinearLayout llRemain;
    private ZPUIRoundButton mbBeginSync;

    public SyncAttachmentResumeDialog2(@NonNull ServerSyncResumeInfoBean bean,
                                       @NonNull ResumeService.Callback callback) {
        this.bean = bean;
        this.callback = callback;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHeightWrapContent(true);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        AnalyticsFactory factory = AnalyticsFactory.create().action("sync-upload-pop-expose");
        int from = callback.getFrom();
        if (from > 0) {
            factory.param("p", from);
        }

        String p2 = "";
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("1", bean.workExpRemain);
            jsonObject.put("2", bean.projExpRemain);
            jsonObject.put("3", bean.eduExpRemain);
            jsonObject.put("4", bean.geekDescRemain);
            jsonObject.put("5", bean.clubExpRemain);
            jsonObject.put("6", bean.qualificationRemain);
            jsonObject.put("7", bean.professionalSkillRemain);
            jsonObject.put("8", bean.honorRemain);
            jsonObject.put("9", bean.headImgRemain);
            jsonObject.put("10", bean.weixinRemain);
            jsonObject.put("11", bean.designImgRemain);
            p2 = jsonObject.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (!TextUtils.isEmpty(p2)) {
            factory.param("p2", p2);
        }
        factory.param("p6", bean.parserId);


        factory.build();

        return inflater.inflate(R.layout.view_sync_attachment_resume_tip_dialog2, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        tvTitle = view.findViewById(R.id.tv_title);
        ivClose = view.findViewById(R.id.iv_close);
        llRemain = view.findViewById(R.id.ll_remain);
        mbBeginSync = view.findViewById(R.id.mb_begin_sync);

        initListener();
        initData();
    }

    private void initListener() {
        ivClose.setOnClickListener(this);
        mbBeginSync.setOnClickListener(this);
    }

    private void initData() {
        int remainCount = bean.remainCount();
        tvTitle.setText(HtmlCompat.fromHtml(String.format(Locale.getDefault(), "附件简历中有<font color='#0D9EA3'>%d项</font>内容可同步", remainCount), HtmlCompat.FROM_HTML_MODE_LEGACY));

        // 个人优势 > 工作经历 > 项目经历 > 教育经历
        List<Entry> entryList = bean.buildEntries(true);
        for (Entry entry : entryList) {
            View entryView = LayoutInflater.from(llRemain.getContext()).inflate(R.layout.view_sync_attachment_resume_item, llRemain, false);
            TextView tvName = entryView.findViewById(R.id.tv_name);
            TextView tvDesc = entryView.findViewById(R.id.tv_desc);
            TextView tvCount = entryView.findViewById(R.id.tv_count);

            tvName.setText(entry.getName());
            tvDesc.setText(entry.getDesc());
            tvCount.setText(entry.getUploadingDisplayed());

            llRemain.addView(entryView);
        }
//        for (Entry entry : entryList) {
//            View entryView = LayoutInflater.from(llRemain.getContext()).inflate(R.layout.view_sync_attachment_resume_item, llRemain, false);
//            TextView tvName = entryView.findViewById(R.id.tv_name);
//            TextView tvDesc = entryView.findViewById(R.id.tv_desc);
//            TextView tvCount = entryView.findViewById(R.id.tv_count);
//
//            tvName.setText(entry.getName());
//            tvDesc.setText(entry.getDesc());
//            tvCount.setText(entry.getUploadingDisplayed());
//
//            llRemain.addView(entryView);
//        }
    }

    @Override
    public void onClick(View v) {
        int viewId = v.getId();
        if (viewId == R.id.iv_close) {
            dismissAllowingStateLoss();
        } else if (viewId == R.id.mb_begin_sync) {
            /*
                曝光场景：
                    1: 新上传附件简历曝光
                    2: 旧附件简历预览
                    3:「我的在线简历」动效进入
            */
            AnalyticsFactory factory = AnalyticsFactory.create().action("sync-upload-pop-click");
            int from = callback.getFrom();
            if (from > 0) {
                factory.param("p", from);
            }
            factory.build();

            callback.onSync();

            dismissAllowingStateLoss();

            new ZPManager(v.getContext(), bean.url).handler();
        }
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        callback.onClose();
    }

}
