package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.MButton;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Author: ZhouYou
 * Date: 2018/5/14.
 */
public class TextInputDialog implements View.OnClickListener {

    private Activity activity;
    private Dialog d;

    private String posText;
    private String negText;
    private OnTextInputConfirmListener posListener;
    private View.OnClickListener negListener;

    private MTextView tvTitle;
    private MEditText etInput;

    private MButton btnCancel;
    private MButton btnConfirm;

    private int maxInputLength;

    public TextInputDialog(Activity activity) {
        if (activity == null || activity.isFinishing()) {
            return;
        }
        this.activity = activity;
        d = new Dialog(activity, R.style.common_dialog);
        d.setCanceledOnTouchOutside(true);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_text_input_dialog, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        d.addContentView(v, lp);
        Window window = d.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        }
        tvTitle = (MTextView) v.findViewById(R.id.tv_title);
        etInput = (MEditText) v.findViewById(R.id.et_input);
        btnCancel = (MButton) v.findViewById(R.id.btn_cancel);
        btnConfirm = (MButton) v.findViewById(R.id.btn_confirm);
        btnCancel.setOnClickListener(this);
        btnConfirm.setOnClickListener(this);
    }

    public TextInputDialog setTitle(String title) {
        tvTitle.setText(title, View.GONE);
        return this;
    }

    public TextInputDialog setTitle(int maxInputLength) {
        tvTitle.setText(activity.getString(R.string.string_input_max_length, maxInputLength), View.GONE);
        return setMaxInputLength(maxInputLength);
    }

    public TextInputDialog setPositiveButton(String name) {
        return setPositiveButton(name, null);
    }

    public TextInputDialog setPositiveButton(String name, OnTextInputConfirmListener listener) {
        this.posListener = listener;
        this.posText = name;
        return this;
    }

    public TextInputDialog setNegativeButton(String name) {
        return setNegativeButton(name, null);
    }

    public TextInputDialog setNegativeButton(String name, View.OnClickListener listener) {
        this.negListener = listener;
        this.negText = name;
        return this;
    }

    public TextInputDialog setMaxInputLength(int maxInputLength) {
        this.maxInputLength = maxInputLength;
        return this;
    }

    public void show() {
        btnConfirm.setText(posText);
        btnCancel.setText(negText);
        d.show();
    }

    private void dismiss() {
        if (d != null) {
            d.dismiss();
            d = null;
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.btn_confirm) {
            InputUtils inputUtils = new InputUtils(activity, maxInputLength);
            String input = getInputText();
            if (TextUtils.isEmpty(input)) {
                AnimUtil.emptyInputAnim(etInput);
            } else if (inputUtils.isInputLargerThanMaxLength(input)) {
                AnimUtil.errorInputAnim(etInput, "最多可输入" + maxInputLength + "个字");
            } else {
                dismiss();
                if (posListener != null) {
                    posListener.onConfirm(input);
                }
            }

        } else if (i == R.id.btn_cancel) {
            dismiss();
            if (negListener != null) {
                negListener.onClick(v);
            }

        } else {
        }
    }

    public String getInputText() {
        return etInput.getText().toString().trim();
    }

    public interface OnTextInputConfirmListener {
        void onConfirm(String input);
    }
}
