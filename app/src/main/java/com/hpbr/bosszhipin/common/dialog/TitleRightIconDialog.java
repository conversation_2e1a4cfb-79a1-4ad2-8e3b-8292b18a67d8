package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.DrawableRes;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.DialogContentsAdapter;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.utils.ActivityUtils;

import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class TitleRightIconDialog implements View.OnClickListener {

    private static final int CONSTANT_SINGLELINE_MAX_LENGTH = 6;

    private Activity activity;
    private Dialog dialog;

    private ImageView ivDismiss;
    private MTextView tvTitle;
    private RecyclerView rvContents;
    private DialogContentsAdapter contentsAdapter;
    private MTextView tvExtraText;
    private LinearLayout mLyBtnContainer;

    private MTextView tvPositive;
    private MTextView tvNegative;

    private String posText;
    private String negText;
    private View.OnClickListener posListener;
    private View.OnClickListener negListener;
    private View.OnClickListener dismissListener;
    private SimpleDraweeView ivTopBackground;
    private SimpleDraweeView ivTopIcon;
    private SimpleDraweeView ivTitleRightIcon;


    public TitleRightIconDialog(Activity activity) {
        this.activity = activity;
        dialog = new Dialog(activity, R.style.twl_ui_common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.dialog_two_button_right_icon, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            }
        }
        mLyBtnContainer = v.findViewById(R.id.ll_btn_container);
        tvTitle = v.findViewById(R.id.tv_title);
        rvContents = v.findViewById(R.id.rv_contents);
        rvContents.setNestedScrollingEnabled(false);
        ivTopBackground = v.findViewById(R.id.iv_top_background);
        ivTopIcon = v.findViewById(R.id.iv_top_icon);
        tvExtraText = v.findViewById(R.id.tv_extra_text);
        ivTitleRightIcon = v.findViewById(R.id.iv_right_icon);

        ivDismiss = v.findViewById(R.id.iv_dismiss);

        contentsAdapter = new DialogContentsAdapter(activity);
        rvContents.setAdapter(contentsAdapter);

        rvContents.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                rvContents.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                int rvHeight = 0;
                int size = rvContents.getChildCount();
                for (int i = 0; i < size; i++) {
                    View view = rvContents.getChildAt(i);
                    if (view != null) {
                        rvHeight += view.getHeight();
                    }
                }

                int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) / 2;
                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) rvContents.getLayoutParams();
                if (rvHeight > maxHeight) {
                    params.height = maxHeight;
                } else {
                    params.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
                }
                rvContents.setLayoutParams(params);
            }
        });
    }

    public TitleRightIconDialog setLandscape() {
        WindowManager m = activity.getWindowManager();
        Display d = m.getDefaultDisplay();
        int width = Math.min(d.getWidth(), d.getHeight());

        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams lp = window.getAttributes();
            lp.width = width; //设置宽度
            lp.height = LinearLayout.LayoutParams.WRAP_CONTENT; //设置高度
            dialog.getWindow().setAttributes(lp);
        }
        return this;
    }

    public TitleRightIconDialog setCancelable(boolean isCancelable) {
        dialog.setCancelable(isCancelable);
        return this;
    }

    public TitleRightIconDialog setTitle(String title) {
        if (TextUtils.isEmpty(title)) {
            title = "温馨提示";
        }
        tvTitle.setText(title, View.GONE);
        return this;
    }

    public void setTitleRightIcon(@DrawableRes int titleRightIcon) {
        tvTitle.setCompoundDrawablesWithIntrinsicBounds(0, 0, titleRightIcon, 0);
        tvTitle.setCompoundDrawablePadding(Scale.dip2px(activity, 5));
    }

    public void setTitleRightIcon(String uri) {
        Uri networkUri = StringUtil.getNetworkUri(uri);
        if (null != networkUri) {
            ivTitleRightIcon.setImageURI(uri);
        }
    }

    public TitleRightIconDialog setContent(List<CharSequence> descList) {
        if (LList.isEmpty(descList)) {
            rvContents.setVisibility(View.GONE);
        } else {
            rvContents.setVisibility(View.VISIBLE);
            if (contentsAdapter != null) {
                contentsAdapter.setContents(descList);
                contentsAdapter.notifyDataSetChanged();
            }
        }
        return this;
    }

    public TitleRightIconDialog setTopBackground(DialogUtils.ImageParams imageParams) {
        if (imageParams == null || imageParams.uri == null || imageParams.type <= 0 || imageParams.ratioHtW <= 0) {
            ivTopBackground.setVisibility(View.GONE);
            ivTopIcon.setVisibility(View.GONE);
        } else {
            int imageType = imageParams.type;
            switch (imageType) {
                case DialogUtils.ImageParams.TOP_BACKGROUND:
                    ivTopBackground.setVisibility(View.VISIBLE);
                    ivTopBackground.setImageURI(imageParams.uri);
                    String widthVsHeight = "1:" + imageParams.ratioHtW;
                    ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) ivTopBackground.getLayoutParams();
                    if (layoutParams != null) {
                        layoutParams.dimensionRatio = widthVsHeight;
                        ivTopBackground.setLayoutParams(layoutParams);
                    }
                    break;
                case DialogUtils.ImageParams.TOP_ICON:
                    ivTopIcon.setVisibility(View.VISIBLE);
                    ivTopIcon.setImageURI(imageParams.uri);
                    tvTitle.setGravity(Gravity.CENTER);
                    break;
                default:
                    break;
            }
        }
        return this;
    }

    public TitleRightIconDialog setTopIcon(Uri uri) {
        if (uri == null) {
            ivTopIcon.setVisibility(View.GONE);
        } else {
            ivTopIcon.setVisibility(View.VISIBLE);
            ivTopBackground.setImageURI(uri);
            tvTitle.setGravity(Gravity.CENTER);
        }
        return this;
    }

    public TitleRightIconDialog setExtraText(String extraText) {
        if (!TextUtils.isEmpty(extraText)) {
            tvExtraText.setVisibility(View.VISIBLE);
            tvExtraText.setText(extraText);
        }
        return this;
    }

    public TitleRightIconDialog setPositiveButton(String name, View.OnClickListener listener) {
        this.posListener = listener;
        this.posText = name;
        return this;
    }

    public TitleRightIconDialog setNegativeButton(String name, View.OnClickListener listener) {
        this.negListener = listener;
        this.negText = name;
        return this;
    }

    public TitleRightIconDialog setDismissListener(View.OnClickListener listener) {
        this.dismissListener = listener;
        if (listener != null) {
            ivDismiss.setVisibility(View.VISIBLE);
            ivDismiss.setOnClickListener(this);
        } else {
            ivDismiss.setVisibility(View.GONE);
            ivDismiss.setOnClickListener(null);
        }
        return this;
    }

    public void show() {
        setButtonStyle();
        if (ActivityUtils.isValid(activity)) {
            if (dialog != null) {
                dialog.show();
            }
        }
    }

    private void setButtonStyle() {
        if (TextUtils.getTrimmedLength(posText) > CONSTANT_SINGLELINE_MAX_LENGTH || TextUtils.getTrimmedLength(negText) > CONSTANT_SINGLELINE_MAX_LENGTH) {
            mLyBtnContainer.setOrientation(LinearLayout.VERTICAL);
            LayoutInflater.from(activity).inflate(R.layout.twl_ui_dialog_button_include_vertical, mLyBtnContainer, true);
        } else {
            mLyBtnContainer.setOrientation(LinearLayout.HORIZONTAL);
            LayoutInflater.from(activity).inflate(R.layout.twl_ui_dialog_button_include_horizontal, mLyBtnContainer, true);
        }

        tvPositive = mLyBtnContainer.findViewById(R.id.tv_positive);
        tvNegative = mLyBtnContainer.findViewById(R.id.tv_negative);

        tvPositive.setOnClickListener(this);
        tvNegative.setOnClickListener(this);

        tvPositive.setText(posText);
        tvNegative.setText(negText);
    }

    public void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void onClick(View v) {
        dismiss();
        int i = v.getId();
        if (i == R.id.iv_dismiss) {
            if (dismissListener != null) {
                dismissListener.onClick(v);
            }
        } else if (i == R.id.tv_positive) {
            if (posListener != null) {
                posListener.onClick(v);
            }
        } else if (i == R.id.tv_negative) {
            if (negListener != null) {
                negListener.onClick(v);
            }
        }
    }


    public boolean isShowing() {
        if (dialog != null) {
            return dialog.isShowing();
        }
        return false;
    }

}
