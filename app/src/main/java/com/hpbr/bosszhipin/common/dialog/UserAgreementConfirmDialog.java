package com.hpbr.bosszhipin.common.dialog;

import android.app.Dialog;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.UserAgreementConfirmRequest;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.api.bean.ServerUserAgreementBean;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Author: zhouyou
 * Date: 2019/7/27
 */
public class UserAgreementConfirmDialog implements View.OnClickListener {

    private BaseActivity activity;
    private Dialog dialog;
    private OnAgreementConfirmListener listener;

    public UserAgreementConfirmDialog(BaseActivity activity, ServerUserAgreementBean userAgreementBean, OnAgreementConfirmListener listener) {
        this.activity = activity;
        this.listener = listener;
        if (activity == null || activity.isFinishing()) return;
        if (userAgreementBean == null) return;
        dialog = new Dialog(activity, R.style.common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
        View v = LayoutInflater.from(activity).inflate(R.layout.view_user_agreement_confirm_dialog, null);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(v, lp);
        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            }
        }
        MTextView tvTitle = v.findViewById(R.id.tv_title);
        MTextView tvContent = v.findViewById(R.id.tv_content);
        v.findViewById(R.id.btn_confirm).setOnClickListener(this);

        tvTitle.setText(userAgreementBean.title, View.GONE);
        SpannableStringBuilder builder = new SpannableStringBuilder();
        String content = userAgreementBean.content;
        if (!TextUtils.isEmpty(content)) {
            builder.append(content);
            int length = content.length();
            if (!LList.isEmpty(userAgreementBean.highlightList)) {
                int size = userAgreementBean.highlightList.size();
                int startIndex;
                int endIndex;
                for (int i = 0; i < size; i++) {
                    ServerHighlightListBean bean = userAgreementBean.highlightList.get(i);
                    if (bean == null) continue;
                    startIndex = bean.startIndex;
                    endIndex = bean.endIndex;
                    if (startIndex < 0 || endIndex <= startIndex || endIndex > length) continue;
                    builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.app_green_dark)), startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    builder.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(@NonNull View widget) {
                            new ZPManager(activity, userAgreementBean.url).handler();
                        }

                        @Override
                        public void updateDrawState(@NonNull TextPaint ds) {
                            ds.setUnderlineText(false);
                            ds.setColor(ContextCompat.getColor(activity, R.color.app_green_dark));
                        }
                    }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }
        tvContent.setText(builder);
        tvContent.setMovementMethod(LinkMovementMethod.getInstance());
    }

    public void show() {
        if (dialog != null) {
            dialog.show();
        }
    }

    public void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
            dialog = null;
        }
    }

    @Override
    public void onClick(View v) {
        dismiss();
        int i = v.getId();
        if (i == R.id.btn_confirm) {
            confirmAgreement();
        }
    }

    private void confirmAgreement() {
        UserAgreementConfirmRequest request = new UserAgreementConfirmRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                dismiss();
                if (listener != null) {
                    listener.onConfirm();
                }
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        HttpExecutor.execute(request);
    }

    public interface OnAgreementConfirmListener {
        void onConfirm();
    }
}
