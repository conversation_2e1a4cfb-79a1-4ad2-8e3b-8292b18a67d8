package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.PopupWindow;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BubbleLayout;
import com.twl.ui.popup.XGravity;
import com.twl.ui.popup.YGravity;
import com.twl.ui.popup.ZPUIPopup;

/**
 * Author: zhouyou
 * Date: 2020-01-10
 */
public class VipPrivilegeFilterGuidePopWindow {

    private ZPUIPopup popup;

    public void show(Activity activity, View anchor) {
        if (popup == null) {
            View view = LayoutInflater.from(activity).inflate(R.layout.view_vip_privilege_filter_guide_pop_up, null, false);
            BubbleLayout blLayout = view.findViewById(R.id.bl_layout);

            view.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    view.getViewTreeObserver().removeOnPreDrawListener(this);
                    int[] anchorLocation = new int[2];
                    anchor.getLocationOnScreen(anchorLocation);
                    int anchorX = anchorLocation[0];

                    int[] viewLocation = new int[2];
                    view.getLocationOnScreen(viewLocation);
                    int viewX = viewLocation[0];

                    int arrowPosition = (int) (anchorX - viewX + (anchor.getRight() - anchor.getLeft()) / 2 - blLayout.getArrowWidth() / 2);

                    blLayout.setArrowPosition(arrowPosition);
                    return true;
                }
            });

            popup = ZPUIPopup.create(activity)
                    .setOutsideTouchable(true)
                    .setTouchable(false)
                    .setInputMethodMode(PopupWindow.INPUT_METHOD_NOT_NEEDED)
                    .setContentView(view)
                    .apply();
        }
        if (popup.isShowing()) {
            popup.dismiss();
        } else {
            popup.showAtAnchorView(anchor, YGravity.BELOW, XGravity.ALIGN_RIGHT, 0, 10, false);
        }
    }
}
