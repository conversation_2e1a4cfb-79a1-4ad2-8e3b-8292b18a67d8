package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.DirectCallPreUseResponse;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

/**
 * Author: zhouyou
 * Date: 2019-11-11
 */
public class VirtualCallConfirmUsingDialog {

    private final Activity activity;
    private BottomView bottomView;
    private final View.OnClickListener listener;
    private final DirectCallPreUseResponse resp;

    public VirtualCallConfirmUsingDialog(Activity activity, DirectCallPreUseResponse resp, View.OnClickListener listener) {
        this.activity = activity;
        this.listener = listener;
        this.resp = resp;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_virtual_call_confirm_using_dialog, null);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvPrivilegeDesc = view.findViewById(R.id.tv_privilege_desc);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        MTextView tvTip = view.findViewById(R.id.tv_tip);

        tvTitle.setText("拨打电话");

        //数据返回为空,隐藏View
        if (LText.empty(resp.itemUseDesc)) {
            tvPrivilegeDesc.setVisibility(View.GONE);
        }

        if (resp.highlight != null) {
            tvPrivilegeDesc.setText(getHighlightText(resp.itemUseDesc, resp.highlight.itemUseDesc));
            tvDesc.setText(getHighlightText(resp.itemDesc, resp.highlight.itemDesc));
        } else {
            tvPrivilegeDesc.setText(resp.itemUseDesc);
            tvDesc.setText(resp.itemDesc);
        }

        tvTip.setText(resp.notice, View.GONE);

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        view.findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (resp.tipPopWindow != null) {
                    showTipPopDialog(resp.tipPopWindow);
                    return;
                }
                if (listener != null) {
                    listener.onClick(v);
                }
            }
        });
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    private void showTipPopDialog(@NonNull ServerDialogBean dialog) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_EXPO).build();

        String negActionString = "仍要拨打";
        String posActionString = "我知道了";

        DialogUtils d = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setTitle(dialog.title)
                .setDesc(dialog.content)
                .setNegativeAction(negActionString, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_CLICK)
                                .param("p", negActionString)
                                .build();
                        dismiss();
                        if (listener != null) {
                            listener.onClick(v);
                        }
                    }
                })
                .setPositiveAction(posActionString, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_CLICK)
                                .param("p", posActionString)
                                .build();
                    }
                })
                .build();
        d.show();
    }

    private SpannableStringBuilder getHighlightText(String text, List<ServerHighlightListBean> highlightList) {
        return ViewCommon.setTextHighLight(text, highlightList, ContextCompat.getColor(activity, R.color.app_green_dark));
    }

    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }
}
