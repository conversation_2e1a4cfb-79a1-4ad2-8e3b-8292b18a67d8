package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Author: zhouyou
 * Date: 2021/12/24
 */
public class VirtualCallNightProtectionDialog {

    private final Context context;
    private BottomView bottomView;
    private final String title;
    private final String content;

    public VirtualCallNightProtectionDialog(Context context, String title, String content) {
        this.context = context;
        this.title = title;
        this.content = content;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_virtual_call_night_protection_dialog, null);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);

        tvTitle.setText(title);
        tvDesc.setText(content);

        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        view.findViewById(R.id.btn_confirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
    }

    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }
}
