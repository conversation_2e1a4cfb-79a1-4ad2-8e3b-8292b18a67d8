package com.hpbr.bosszhipin.common.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerDirectCallSimGeekDetailBean;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import androidx.core.content.ContextCompat;

/**
 * Author: zhouyou
 * Date: 2022/5/31
 */
public class VirtualCallSimGeekDetailDialog implements View.OnClickListener {

    private final Context context;
    private BottomView bottomView;
    private final ServerDirectCallSimGeekDetailBean simGeekDetail;
    private final OnSimGeekDetailActionCallback callback;

    public VirtualCallSimGeekDetailDialog(Context context, ServerDirectCallSimGeekDetailBean simGeekDetail, OnSimGeekDetailActionCallback callback) {
        this.context = context;
        this.simGeekDetail = simGeekDetail;
        this.callback = callback;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_virtual_call_sim_geek_detail_dialog, null);

        MTextView tvGeekName = view.findViewById(R.id.tv_geek_name);
        SimpleDraweeView ivAvatar = view.findViewById(R.id.iv_avatar);
        ImageView ivGender = view.findViewById(R.id.iv_gender);

        MTextView tvGeekExpect = view.findViewById(R.id.tv_geek_expect);

        MTextView tvWorkYear = view.findViewById(R.id.tv_work_year);
        MTextView tvDegree = view.findViewById(R.id.tv_degree);
        MTextView tvAge = view.findViewById(R.id.tv_age);
        MTextView tvApplyStatus = view.findViewById(R.id.tv_apply_status);
        MTextView tvGeekDesc = view.findViewById(R.id.tv_geek_desc);

        tvGeekName.setText(simGeekDetail.name);
        ivGender.setImageResource(ViewCommon.getGenderIcon(simGeekDetail.gender));
        ivAvatar.setImageURI(simGeekDetail.headUrl);

        String positionString = "";
        if (!TextUtils.isEmpty(simGeekDetail.position)) {
            positionString = "期望：" + simGeekDetail.position;
        }
        List<String> expectStrings = new ArrayList<>();
        if (!TextUtils.isEmpty(positionString)) {
            expectStrings.add(positionString);
        }
        if (!TextUtils.isEmpty(simGeekDetail.salary)) {
            expectStrings.add(simGeekDetail.salary);
        }
        tvGeekExpect.setText(createExpect(expectStrings));

        tvWorkYear.setText(simGeekDetail.exp, View.GONE);
        tvDegree.setText(simGeekDetail.degree, View.GONE);
        tvAge.setText(simGeekDetail.ageDesc, View.GONE);
        tvApplyStatus.setText(simGeekDetail.applyStatusDesc, View.GONE);
        tvGeekDesc.setText(simGeekDetail.geekDesc, View.GONE);

        view.findViewById(R.id.cl_geek_card).setOnClickListener(this);
        view.findViewById(R.id.btn_view_detail).setOnClickListener(this);
        view.findViewById(R.id.fl_call).setOnClickListener(this);
        view.findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                actions(2);
            }
        });

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                actions(2);
            }
        });
    }


    private CharSequence createExpect(List<String> strings) {
        if (LList.isEmpty(strings)) return null;
        StringBuilder stb = new StringBuilder();
        int max = strings.size() - 1;
        for (int i = 0; i <= max; i++) {
            String info = strings.get(i);
            if (TextUtils.isEmpty(info)) continue;
            stb.append(info);
            if (i != max) {
                stb.append(" | ");
            }
        }
        return formatString(stb.toString(), "\\|", ContextCompat.getColor(context, R.color.text_c3));
    }

    /**
     * 将字符串中的特定 字符高亮
     */
    private CharSequence formatString(String text, String regular, int colorResId) {

        try {
            SpannableStringBuilder builder = new SpannableStringBuilder(text);

            Pattern p = Pattern.compile(regular);
            Matcher matcher = p.matcher(text);
            int start = 0;
            int end = 0;
            while (matcher.find()) {
                if (matcher.start() == end) {
                    end = matcher.end();
                } else {
                    if (start != end) {
                        ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                        builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                    start = matcher.start();
                    end = matcher.end();
                }
            }
            if (start != end) {
                ForegroundColorSpan span = new ForegroundColorSpan(colorResId);
                builder.setSpan(span, start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            return builder;
        } catch (Exception e) {
            return text;
        }
    }

    public void show() {
        if (bottomView != null) {
            bottomView.showBottomView(true);
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_view_detail || id == R.id.cl_geek_card) {
            actions(0);
            if (callback != null) {
                callback.onViewGeekDetail();
            }
        } else if (id == R.id.fl_call) {
            actions(1);
            dismiss();
            if (callback != null) {
                callback.onGeekCall();
            }
        }
    }

    private void actions(int actionType) {
        if (simGeekDetail == null) return;
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_DIRECT_PHONE_BACKUP_CLICK)
                .param("p", simGeekDetail.securityId)
                .param("p2", actionType)
                .build();
    }

    public interface OnSimGeekDetailActionCallback {
        void onViewGeekDetail();

        void onGeekCall();
    }
}
