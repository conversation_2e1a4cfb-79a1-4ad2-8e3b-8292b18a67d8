package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.block.utils.VirtualCallUtils;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.hpbr.bosszhipin.views.BaseBottomDialogFragment;
import com.hpbr.bosszhipin.views.BottomDialog;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.weiget.ZPLinkMovementMethod;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.DirectCallPreUseResponse;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 *
 */
public class VirtualCallUseBottomDialog extends BaseBottomDialogFragment {

    public static VirtualCallUseBottomDialog getInstance(DirectCallPreUseResponse resp, View.OnClickListener listener) {
        VirtualCallUseBottomDialog fragment = new VirtualCallUseBottomDialog();
        fragment.listener = listener;
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_ENTITY, resp);
        fragment.setArguments(bundle);
        return fragment;
    }

    private View.OnClickListener listener;
    private DirectCallPreUseResponse resp;

    protected MTextView tvTitle;
    protected ImageView ivClose;

    protected LinearLayout ll;
    protected MTextView tvDesc;
    protected Button btnConfirm;

    protected LinearLayout inputLL;
    protected TextView tvChangePhoneTip;
    protected TextView tvChangePhoneDesc;
    protected TextView etInput;
    protected TextView tvChangePhoneBottomTip;

    public boolean isCanInputPhone() {
        return resp.changePhone == 1 && resp.changePhoneWindow != null;
    }

    protected String lastPhone;

    public static final String TEXT_CHANGE_PHONE = "修改呼出号码";

    public static final String TEXT_CALL_PHONE = "拨打电话";

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Bundle arguments = getArguments();

        resp = null != arguments ? (DirectCallPreUseResponse) arguments.getSerializable(Constants.DATA_ENTITY) : null;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.view_virtual_call_use_bottom_dialog, container, false);
    }

    @Override
    public BottomDialog.Builder buildDialog(@NonNull BottomDialog.Builder builder) {
        BottomDialog.Builder builder1 = super.buildDialog(builder);
        return builder1.setDimAmount(0.7f).setFitEditWindow(true);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (null != resp) {
            initView(view);
        } else {
            dismiss();
        }
    }

    private boolean inputShown = false;

    @Override
    public boolean disableOnKeyBack() {
        boolean intercept = false;
        if (isCanInputPhone() && inputShown) {
            changeEntryExit();
            intercept = true;
        }
        return intercept;
    }

    protected void setTitleBackInfo(String title, boolean isShowBack) {
        tvTitle.setText(title);
        tvTitle.setCompoundDrawablesWithIntrinsicBounds(isShowBack ? R.mipmap.ic_action_back_black : 0, 0, 0, 0);
        tvTitle.setOnClickListener(isShowBack ? new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                changeEntryExit();
            }
        } : null);
        ivClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });
    }

    protected void setDisableConfirmBtn(boolean disable){
        btnConfirm.setEnabled(!disable);
    }

    private void changeEntryExit() {
        changePageStatus(inputLL, ll, inputShown = !inputShown, new MyAnimListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                setDisableConfirmBtn(true);
                setTitleBackInfo(!inputShown ? TEXT_CALL_PHONE : TEXT_CHANGE_PHONE, inputShown);
                setBtnText(inputShown);
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                setDisableConfirmBtn(false);
            }
        });

        if (!inputShown) {
            App.get().getMainHandler().post(() -> AppUtil.hideSoftInput(getMContext(), etInput));
        }
    }

    protected void setBtnText(boolean isShowBack) {
        if (isCanInputPhone() && isShowBack) {
            btnConfirm.setText(resp.changePhoneWindow.buttonText);
        } else if (!setBtnText()) {
            btnConfirm.setText("立刻拨打");
        }
    }

    // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，本次沟通使用道具沟通钥匙时，按钮上展示文案 buttonText
    private boolean setBtnText() {
        if (resp == null || LText.isEmptyOrNull(resp.buttonText) || resp.itemType != VirtualCallUtils.TYPE_CALL_CONNECT_KEY) {
            return false;
        }
        if (btnConfirm != null) {
            btnConfirm.setText(resp.buttonText);
        }
        return true;
    }

    protected void initView(View view) {

        tvTitle = view.findViewById(R.id.tv_title);
        ivClose = view.findViewById(R.id.iv_close);
        ll = view.findViewById(R.id.ll_container);
        btnConfirm = view.findViewById(R.id.btn_confirm);

        MTextView tvPrivilegeDesc = view.findViewById(R.id.tv_privilege_desc);
        tvDesc = view.findViewById(R.id.tv_desc);
        MTextView tvTip = view.findViewById(R.id.tv_tip);

        initInputView(view);

        setTitleBackInfo(!inputShown ? TEXT_CALL_PHONE : TEXT_CHANGE_PHONE, isCanInputPhone() && inputShown);

        setDesc(null);
        setBtnText();

        tvPrivilegeDesc.setText(getHighlightText(resp.itemUseDesc,
                resp.highlight != null ? resp.highlight.itemUseDesc : null), View.GONE);

        tvTip.setText(resp.notice, View.GONE);

        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                confirm(v);
            }
        });
    }

    public String replacePhoneNumber(String text, String newPhoneNumber) {
        String pattern = "\\d{3}\\*{4}\\d{4}";
        String newText = text.toString().replaceAll(pattern, newPhoneNumber);
        TLog.debug("MrfLog", "【VirtualCallUseBottomDialog】-【replacePhoneNumber】: : %s, newPhoneNumber: %s", newText, newPhoneNumber);
        return newText;
    }

    public String encryptPhoneNumber(String phoneNumber) {
        if (null != phoneNumber && phoneNumber.length() == 11) {
            phoneNumber = phoneNumber.subSequence(0, 3) + "****" + phoneNumber.subSequence(7, 11);
        }
        return phoneNumber;
    }


    private void setDesc(String newPhone) {
        String itemDesc = LText.toString(resp.itemDesc, "");
        /*更新手机号*/
        if (newPhone != null) {
            itemDesc = replacePhoneNumber(itemDesc, newPhone);
        }
        CharSequence desc = getHighlightText(itemDesc, null != resp.highlight ? resp.highlight.itemDesc : null);

        /*1206.276【商业】商业侧虚拟电话流程增加本机提示 - 如果存在并且为1，则在第一句最后出 “修改呼出号码>” 并支持点击弹窗*/
        if (isCanInputPhone()) {
            desc = ZPUtils.appendClickSpannable(getMContext(), desc, true, TEXT_CHANGE_PHONE + ">", R.color.app_green_dark, url -> {
                changePageStatus(inputLL, ll, inputShown = !inputShown, new MyAnimListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {
                        setDisableConfirmBtn(true);
                        setTitleBackInfo(!inputShown ? TEXT_CALL_PHONE : TEXT_CHANGE_PHONE, inputShown);
                        setBtnText(inputShown);
                    }

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        setDisableConfirmBtn(false);
                    }
                });

                App.get().getMainHandler().post(() -> AppUtil.showSoftInput2(getMContext(), etInput));
            });
        }
        tvDesc.setMovementMethod(ZPLinkMovementMethod.getInstance());
        tvDesc.setHighlightColor(Color.TRANSPARENT);
        tvDesc.setText(desc);
    }

    protected String getInputContent() {
        CharSequence cs = etInput.getText();
        return cs != null ? cs.toString().trim() : "";
    }

    protected void confirm(View view) {
        if (inputShown) {

            String phone = getInputContent();

            if (LText.isEmptyOrNull(phone)) {
                ToastUtils.showText("手机号码格式错误，请修改");

            } else {
                requestChangePhone(phone, newPhone -> {
                    lastPhone = newPhone;
                    setDesc(encryptPhoneNumber(newPhone));
                    changeEntryExit();
                });
            }

        } else {
            dismiss();
            if (resp.tipPopWindow != null) {
                showTipPopDialog(resp.tipPopWindow);
                return;
            }
            if (listener != null) {
                listener.onClick(view);
            }
        }
    }

    protected void initInputView(View view) {
        inputLL = view.findViewById(R.id.ll_container_input);
        tvChangePhoneTip = view.findViewById(R.id.tv_change_phone_tip);
        tvChangePhoneDesc = view.findViewById(R.id.tv_change_phone_desc);
        etInput = view.findViewById(R.id.et_input);
        tvChangePhoneBottomTip = view.findViewById(R.id.tv_change_phone_bottom_tip);

        if (resp.changePhoneWindow != null) {
            tvChangePhoneTip.setText(resp.changePhoneWindow.content);
            tvChangePhoneDesc.setHint(resp.changePhoneWindow.textBoxDesc);
            etInput.setHint(resp.changePhoneWindow.textBoxText);
            tvChangePhoneBottomTip.setHint(resp.changePhoneWindow.note);
        }
    }

    private void showTipPopDialog(@NonNull ServerDialogBean dialog) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_EXPO).build();

        String negActionString = "仍要拨打";
        String posActionString = "我知道了";

        DialogUtils d = new DialogUtils.Builder((Activity) getMContext())
                .setDoubleButton()
                .setTitle(dialog.title)
                .setDesc(dialog.content)
                .setNegativeAction(negActionString, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_CLICK)
                                .param("p", negActionString)
                                .build();
                        dismiss();
                        if (listener != null) {
                            listener.onClick(v);
                        }
                    }
                })
                .setPositiveAction(posActionString, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_CALL_MUCH_CLICK)
                                .param("p", posActionString)
                                .build();
                    }
                })
                .build();
        d.show();
    }

    private SpannableStringBuilder getHighlightText(String text, List<ServerHighlightListBean> highlightList) {
        return ViewCommon.setTextHighLight(text, highlightList, ContextCompat.getColor(getMContext(), R.color.app_green_dark));
    }


    public void requestChangePhone(String phone, ZPFunction.Fun1<String> listener) {
        SimpleApiRequest.GET(BusinessUrlConfig.URL_ZP_ITEM_DIRECT_CALL_CHANGE_CALL)
                .addParam("phone", LText.toString(phone, ""))
                .setRequestCallback(new ApiRequestCallback<ChangeResultResponse>() {

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onSuccess(ApiData<ChangeResultResponse> data) {
                        if (data.resp != null && data.resp.result && isResumed() && isVisible()) {
                            if (null != listener) {
                                listener.call(phone);
                                ToastUtils.showText(data.resp.toast);
                            }
                        }
                    }

                    @Override
                    public void onComplete() {
                        dismissProgressDialog();
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                }).execute();
    }


    protected static class MyAnimListener implements Animation.AnimationListener {
        @Override
        public void onAnimationStart(Animation animation) {

        }

        @Override
        public void onAnimationEnd(Animation animation) {

        }

        @Override
        public void onAnimationRepeat(Animation animation) {

        }
    }

    public static class ChangeResultResponse extends HttpResponse {
        private static final long serialVersionUID = -1139991293076770928L;
        public boolean result;
        public String toast;
    }

}
