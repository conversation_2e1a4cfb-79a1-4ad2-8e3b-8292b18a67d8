package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module_boss_export.BossUrlConfig;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.MsgResultResponse;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.HashMap;
import java.util.Map;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.ViewCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;

import static com.hpbr.bosszhipin.data.db.entry.ContactBean.FROM_BOSS;

/**
 * Created by guofeng
 * on 2022/5/16.
 */
public class VirtualPhoneManage {

    public static class VirtualPhoneBean extends BaseServerBean {

        private static final long serialVersionUID = 7267821586334759658L;

        public String securityId;
        public String icon;
        public String title;
        public String confirmButtonText;
        public String refuseButtonText;
        public long endTime;

        @Override
        public String toString() {
            return "VirtualPhoneBean{" +
                    "securityId='" + securityId + '\'' +
                    ", icon='" + icon + '\'' +
                    ", title='" + title + '\'' +
                    ", confirmButtonText='" + confirmButtonText + '\'' +
                    ", refuseButtonText='" + refuseButtonText + '\'' +
                    ", endTime=" + endTime +
                    '}';
        }
    }

    public static class VirtualCacheBean {

        private VirtualCacheBean() {
        }

        public static VirtualCacheBean instance = new VirtualCacheBean();

        public static VirtualCacheBean getInstance() {
            return instance;
        }

        private final Map<String, VirtualPhoneBean> cache = new HashMap<>();

        private synchronized String getKey(long friendId, int friendSource) {
            return UserManager.getUID() + "_" +
                    UserManager.getUserRole().get() + "_" +
                    friendId + "_" +
                    friendSource;
        }

        public synchronized void removeData(long friendId, int friendSource) {
            String key = getKey(friendId, friendSource);
            cache.remove(key);
        }

        public synchronized void addData(long friendId, int friendSource, VirtualPhoneBean extend) {
            if (extend == null) return;
            String key = getKey(friendId, friendSource);
            cache.remove(key);
            cache.put(key, extend);
        }

        public synchronized VirtualPhoneBean getExtendValue(long friendId, int friendSource) {
            String key = getKey(friendId, friendSource);
            return cache.get(key);
        }

    }

    public static class VirtualDialog {
        private String icon;

        private String title;

        private String securityId;

        private String confirmButtonText;

        private String refuseButtonText;

        private View view;

        private long friendId;

        public void setFriendId(long friendId) {
            this.friendId = friendId;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public void setSecurityId(String securityId) {
            this.securityId = securityId;
        }

        public void setConfirmButtonText(String confirmButtonText) {
            this.confirmButtonText = confirmButtonText;
        }

        public void setRefuseButtonText(String refuseButtonText) {
            this.refuseButtonText = refuseButtonText;
        }


        private ViewGroup rootView;


        public void setRootView(ViewGroup mListParentView) {
            rootView = mListParentView;
        }


        private boolean needCheckOnResume;

        public void checkShowDialog() {
            SimpleApiRequest get = SimpleApiRequest.GET(BusinessUrlConfig.URL_ZP_ITEM_SEARCH_HUNTER_GEEK_CALL_REPLY_SHOW);
            get.setRequestCallback(new ApiRequestCallback<SuccessBooleanResponse>() {

                @Override
                public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                    if (data.resp.isResult()) {
                        Activity topActivity = ForegroundUtils.get().getTopActivity();

                        if (!ActivityUtils.isValid(topActivity)) return;
                        if (!SingleRouter.checkIsChatActivityBossUser(topActivity)) return;

                        view = LayoutInflater.from(topActivity).inflate(R.layout.dialog_virtual_phone, null);

                        SimpleDraweeView mIconView = view.findViewById(R.id.mIconView);
                        MTextView mTitleView = view.findViewById(R.id.mTitleView);
                        MTextView mNotInterestedView = view.findViewById(R.id.mNotInterestedView);
                        MTextView mSureView = view.findViewById(R.id.mSureView);

                        mIconView.setImageURI(icon);
                        mTitleView.setText(title);

                        mSureView.setText(confirmButtonText);
                        mNotInterestedView.setText(refuseButtonText);
                        mSureView.setOnClickListener(v -> report(1));
                        mNotInterestedView.setOnClickListener(v -> report(2));

                        rootView.addView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

                        //删除缓存数据
                        VirtualPhoneManage.VirtualCacheBean.getInstance().removeData(friendId, FROM_BOSS);

                        if (topActivity instanceof AppCompatActivity) {
                            AppCompatActivity activity = (AppCompatActivity) topActivity;
                            //监控页面返回删除view
                            activity.getLifecycle().addObserver((LifecycleEventObserver) (source, event) -> {
                                if (Lifecycle.Event.ON_RESUME == event) {
                                    if (needCheckOnResume) {
                                        removeView();
                                    }
                                }

                                if (Lifecycle.Event.ON_PAUSE == event) {
                                    needCheckOnResume = true;
                                }
                            });
                        }

                    }
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            get.addParam("securityId", securityId);
            HttpExecutor.execute(get);
        }



        private void removeView() {
            if (ViewCompat.isAttachedToWindow(rootView)
                    && ViewCompat.isAttachedToWindow(view)) {
                rootView.removeView(view);
            }
            VirtualPhoneManage.VirtualCacheBean.getInstance().removeData(friendId, FROM_BOSS);
        }

        /**
         * 1同意2拒绝
         *
         * @param option
         */
        private void report(int option) {
            SimpleApiRequest callReplayRequest = SimpleApiRequest.POST(BossUrlConfig.URL_GEEK_CALL_REPLY);
            callReplayRequest.setRequestCallback(new ApiRequestCallback<MsgResultResponse>() {
                @Override
                public void onSuccess(ApiData<MsgResultResponse> data) {
                    MsgResultResponse resp = data.resp;
                    String msg = resp.msg;
                    if (!LText.empty(msg)) {
                        ToastUtils.showText(msg);
                    }
                }

                @Override
                public void onComplete() {
                    removeView();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            callReplayRequest.addParam("securityId", securityId);
            callReplayRequest.addParam("option", option);
            HttpExecutor.execute(callReplayRequest);
        }
    }

}
