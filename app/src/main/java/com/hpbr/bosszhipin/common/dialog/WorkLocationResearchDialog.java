package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.common.OnClickNoFastListener;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.config.RequestMethod;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.WorkLocationReaearchResponse;
import net.bosszhipin.api.bean.HightLightBean;
import net.bosszhipin.api.bean.WorkLocationResearchBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 * create by guofeng
 * date on 2022/2/10
 */

public class WorkLocationResearchDialog {

    private static final String SUPPORT_TYPE = "1";

    private static final String IS_NO_ANSWER = "-1";


    private BottomView bottomView;

    private LinearLayout mContainer;


    /**
     * 显示 弹窗
     *
     * @param workLocationResearchBean
     */
    public void show(@Nullable WorkLocationResearchBean workLocationResearchBean) {
        if (workLocationResearchBean == null) return;
        //不支持的类型
        if (!LText.equal(SUPPORT_TYPE, workLocationResearchBean.type)) return;

        //已经回答过,toast提示
        if (!LText.equal(workLocationResearchBean.answer, IS_NO_ANSWER)) {
            ToastUtils.showText("您已提交过反馈。");
            return;
        }


        //上下文是否可用
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (!ActivityUtils.isValid(topActivity)) return;

        View view = LayoutInflater.from(topActivity).inflate(R.layout.dialog_work_location_research, null);
        mContainer = view.findViewById(R.id.mContainer);
        view.findViewById(R.id.mBgCoverView).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

        showChooseView(workLocationResearchBean, topActivity);

        bottomView = new BottomView(topActivity, R.style.BottomViewTheme_Transparent, view);
        bottomView.showBottomView(true);
    }

    /**
     * 显示回答弹窗内容
     *
     * @param workLocationResearchBean
     * @param topActivity
     */
    private void showChooseView(@NonNull WorkLocationResearchBean
                                        workLocationResearchBean, Activity topActivity) {
        mContainer.removeAllViews();
        View view = LayoutInflater.from(topActivity).inflate(R.layout.dialog_work_location_choose, null);
        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        ImageView mCloseView = view.findViewById(R.id.mCloseView);
        LinearLayout mEmotionContainer = view.findViewById(R.id.mEmotionContainer);

        List<WorkLocationResearchBean.OptionsBean> options = workLocationResearchBean.options;
        if (options != null) {
            //产品要求最多显示俩个，否则UI没法布局
            if (LList.getCount(options) > 2) {
                options = options.subList(0, 2);
            }
            for (int i = 0; i < options.size(); i++) {
                WorkLocationResearchBean.OptionsBean option = LList.getElement(options, i);
                if (option == null) continue;
                String iconUrl = option.icon;
                String textName = option.option;
                View emotionView = LayoutInflater.from(topActivity).inflate(R.layout.dialog_work_location_emotion, null);
                MTextView mTextName = emotionView.findViewById(R.id.mTextName);
                SimpleDraweeView mAvatarView = emotionView.findViewById(R.id.mAvatarView);
                View mLayoutView = emotionView.findViewById(R.id.mLayoutView);
                mTextName.setText(textName);
                mAvatarView.setImageURI(StringUtil.getNetworkUri(iconUrl));

                if (i == 1) {
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                    params.leftMargin = Scale.dip2px(topActivity, 10);
                    mEmotionContainer.addView(emotionView, params);
                } else {
                    mEmotionContainer.addView(emotionView);
                }

                String securityId = workLocationResearchBean.securityId;
                String msgId = workLocationResearchBean.msgId;

                int index = i;
                mLayoutView.setOnClickListener(v -> postSelectData(index, securityId, msgId));

            }
        }

        mTitleView.setText(workLocationResearchBean.title);
        mCloseView.setOnClickListener(v -> {

            dismiss();

            //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=167075731
            if (LText.equal(workLocationResearchBean.scene, "1")) {
                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_GEEK_CERTIFICATION_COMPLETE_CLOSE)
                        .secId(workLocationResearchBean.securityId)
                        .param("p", workLocationResearchBean.scene)//区分场景： 1、安心保职位聊天页 0其他
                        .param("p2", workLocationResearchBean.dialogFromBgAnaly)// 1 自动弹窗 ， 0 点击消息卡片
                        .buildSync();
            }

        });
        mContainer.addView(view);
    }

    /**
     * 提交选择的数据
     */
    private void postSelectData(int index, String securityId, String msgId) {


        SimpleApiRequest request = new SimpleApiRequest(ChatUrlConfig.URL_ZPCHAT_FEEDBACK_ANSWER_JOB_QUESTION, RequestMethod.GET);
        request.setRequestCallback(new ApiRequestCallback<WorkLocationReaearchResponse>() {
            @Override
            public void onSuccess(ApiData<WorkLocationReaearchResponse> data) {
                /**
                 * {"code":0,"message":"Success","zpData":
                 * {"icon":"https://img.bosszhipin.com/beijin/icon/722a9b71077205c3b918dafabafc18ec75efb8b1e98b5a26db0c21c2c524c354.png",
                 * "content":"感谢您的反馈，我们将尽快核实。您也可以点击举报对方，维护自身权益",
                 * "highlight":[{"word":"举报对方","start":21,"end":25,"url":"https://www.baidu.com"}]}}
                 */
                WorkLocationReaearchResponse resp = data.resp;
                String content = resp.content;
                String icon = resp.icon;
                String toastContent = resp.toastContent;
                List<HightLightBean> highlight = resp.highlight;
                showResultDialog(content, icon, highlight);
                if (!LText.empty(toastContent)) {
                    ToastUtils.showText(toastContent);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.addParam("securityId", securityId);
        request.addParam("answer", index);
        request.addParam("msgId", msgId);
        HttpExecutor.execute(request);
    }

    /**
     * 显示结果页面
     *
     * @param content
     * @param icon
     * @param highlight
     */
    private void showResultDialog(String content, String
            icon, List<HightLightBean> highlight) {
        mContainer.removeAllViews();
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (!ActivityUtils.isValid(topActivity)) return;
        View view = LayoutInflater.from(topActivity).inflate(R.layout.dialog_work_location_result, null);
        view.findViewById(R.id.mCloseView).setOnClickListener(v -> dismiss());
        SimpleDraweeView mAvatarView = view.findViewById(R.id.mAvatarView);
        MTextView mDescText = view.findViewById(R.id.mDescText);
        mAvatarView.setImageURI(StringUtil.getNetworkUri(icon));

        if (highlight != null) {

            HightLightBean itemHighlight = LList.getElement(highlight, 0);

            if (itemHighlight == null) return;

            int start = itemHighlight.start;
            int end = itemHighlight.end;
            String url = itemHighlight.url;

            if (content.length() >= end && content.length() > start) {
                SpannableString spannableString = new SpannableString(content);
                spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(topActivity, R.color.app_green_dark)),
                        start,
                        end,
                        Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                spannableString.setSpan(new ClickableSpan() {

                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                        //去掉高亮的下划线
                        ds.setUnderlineText(false);
                    }

                    @Override
                    public void onClick(@NonNull View widget) {
                        //关闭弹窗
                        dismiss();

                        new ZPManager(topActivity, url).handler();

                    }
                }, start, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);

                mDescText.setMovementMethod(LinkMovementMethod.getInstance());
                mDescText.setText(spannableString);
            } else {
                mDescText.setText(content);
            }

        } else {
            mDescText.setText(content);
        }

        mContainer.addView(view);
    }


    //关闭弹窗
    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

}