package com.hpbr.bosszhipin.common.dialog;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.JobYouXuanExplainAdapter;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.bean.ServerSafeTipOverlayBean;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.banner.ZPUIBannerLayout;

/**
 * Author: zhouyou
 * Date: 2020-01-07
 */
public class YouXuanExplainDialoog {

    private final Activity activity;
    private BottomView bottomView;
    private MTextView mtvTitle;
    private MTextView mButton;
    private ZPUIBannerLayout mBlGuide;
    private List<ServerSafeTipOverlayBean> dataList = new ArrayList<>();
    private ImageView ivClose;
    private final int currentPosition = 1;
    private JobYouXuanExplainAdapter adapter;
    private final String jobId;

    public YouXuanExplainDialoog(Activity activity, List<ServerSafeTipOverlayBean> dataList, String jobId) {
        this.activity = activity;
        this.dataList = dataList;
        this.jobId = jobId;
        init();
    }

    private void init() {
        View view = LayoutInflater.from(activity).inflate(R.layout.view_youxuan_explain, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        mtvTitle = view.findViewById(R.id.tv_title);
        ivClose = view.findViewById(R.id.iv_close);
        mButton = view.findViewById(R.id.btn_confirm);
        mBlGuide = view.findViewById(R.id.bl_guide);
        mBlGuide.setAutoPlaying(false);
        bindBanner(dataList);

        mBlGuide.setOnIndicatorIndexChangedListener(new ZPUIBannerLayout.OnIndicatorIndexChangedListener() {
            @Override
            public void onIndexChanged(int position) {

                switch (position) {
                    case 0:
                        mButton.setVisibility(View.VISIBLE);
                        mButton.setText("下一步(1/3)");
                        break;
                    case 1:
                        mButton.setVisibility(View.VISIBLE);
                        mButton.setText("下一步(2/3)");
                        break;
                    case 2:
                        mButton.setText("我知道了");
                        mButton.setVisibility(View.VISIBLE);

                        break;
                    default:
                        break;
                }
            }
        });
        mBlGuide.getRecyclerView().smoothScrollToPosition(0);
        ivClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();

            }
        });

        mButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                int index = mBlGuide.getLayoutManager().getCurrentPosition();

                if (index == 2) {
                    mgeOverLayFrequency(3, 0, adapter.isSelectNoMore ? 1 : 0);
                    AnalyticsFactory.create().action("preferred-introduce-exp").param("p", "click").param("p2", adapter.isSelectNoMore ? 1 : 0).param("p3", jobId).build();
                    dismiss();
                } else {
                    ++index;
                    mBlGuide.getRecyclerView().smoothScrollToPosition(index);
                }


            }
        });

    }

    public void show() {
        if (activity != null && !activity.isFinishing()) {
            bottomView.showBottomView(true);
            AnalyticsFactory.create().action("action-detail-dzhpop-show").param("p", 1).build();
        }
    }

    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

    private void bindBanner(@NonNull List<ServerSafeTipOverlayBean> list) {
        adapter = new JobYouXuanExplainAdapter(list);
        mBlGuide.setAdapter(adapter);
    }


    public void mgeOverLayFrequency(int overlayType, int overlayItemType, long optType) {
        SimpleApiRequest.POST(GeekUrlConfig.URL_ZPGEEK_JD_OVERLAY_CLOSE)
                .addParam("overlayType", overlayType)
                .addParam("overlayItemType", overlayItemType)
                .addParam("optType", optType)

                .setRequestCallback(new SimpleApiRequestCallback<HttpResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<HttpResponse> data) {
                        super.handleInChildThread(data);
                    }

                    @Override
                    public void handleErrorInChildThread(ErrorReason reason) {
                        super.handleErrorInChildThread(reason);
                    }
                })
                .execute();
    }

}
