package com.hpbr.bosszhipin.common.dictionary;

import java.util.List;

/**
 * Created by monch on 2017/7/1.
 */

public class DictionaryData<T> {
    private int index;
    private List<T> data;
    private boolean hasBefore, hasLast;
    DictionaryData(int index, List<T> data, boolean hasBefore, boolean hasLast) {
        this.index = index;
        this.data = data;
        this.hasBefore = hasBefore;
        this.hasLast = hasLast;
    }
    public int getIndex() {
        return index;
    }
    public List<T> getData() {
        return data;
    }
    public boolean isHasBefore() {
        return hasBefore;
    }
    public boolean isHasLast() {
        return hasLast;
    }
}
