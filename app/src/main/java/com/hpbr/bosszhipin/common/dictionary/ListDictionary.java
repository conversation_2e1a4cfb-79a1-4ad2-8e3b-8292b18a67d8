package com.hpbr.bosszhipin.common.dictionary;

import com.hpbr.bosszhipin.module.commend.entity.ParamBean;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Created by monch on 2017/7/1.
 */

public abstract class ListDictionary<T> {

    private Map<String, Integer> mIdControlIndexDictionary;
    private Map<Integer, List<T>> mIndexControlListDictionary;
    private List<Integer> mIndexArray;

    public ListDictionary() {
        mIdControlIndexDictionary = new HashMap<>();
        mIndexControlListDictionary = new HashMap<>();
        mIndexArray = new LinkedList<>();
    }

    /**
     * 添加数据
     * @param index
     * @param list
     */
    public synchronized void putList(final int index, final List<T> list) {
        if (list == null || list.isEmpty()) return;
        for (T item : list) {
            mIdControlIndexDictionary.put(itemToKey(item), index);
        }
        mIndexControlListDictionary.put(index, list);
        mIndexArray.add(index);
    }

    // 数据转Key
    protected abstract String itemToKey(T item);

    // 计算index
    private int computeIndex(boolean isLast, int index) {
        final int size = mIndexArray.size();
        if (isLast) {
            // 向后计算
            for (int i = 0; i < size; i++) {
                if (mIndexArray.get(i) == index) {
                    final int temp = i + 1;
                    if (temp < size && mIndexArray.get(temp) != index) {
                        return temp;
                    }
                }
            }
        } else {
            // 向前计算
            for (int i = 0; i < size; i++) {
                if (mIndexArray.get(i) == index) {
                    final int temp = i - 1;
                    if (temp >= 0 && mIndexArray.get(temp) != index) {
                        return temp;
                    }
                }
            }
        }
        return -1;
    }

    /**
     * 使用Item获取数据
     * @param item 查询数据
     * @return
     */
    public synchronized DictionaryData<T> getListWithItem(T item) {
        if (item == null) return null;
        final String key = itemToKey(item);
        int index = mIdControlIndexDictionary.containsKey(key) ?
                mIdControlIndexDictionary.get(key) : -1;
        if (index < 0) return null;
        List<T> list = mIndexControlListDictionary.containsKey(index) ?
                mIndexControlListDictionary.get(index) : null;
        if (list == null) return null;
        return new DictionaryData<T>(index, list, computeIndex(false, index) >= 0,
                computeIndex(true, index) >= 0);
    }

    /**
     * 获取下一页数据
     * @param last true向后 false向前
     * @param index 当前页码
     * @return
     */
    public synchronized DictionaryData<T> getListWithNext(boolean last, int index) {
        final int nextIndex = computeIndex(last, index);
        if (nextIndex < 0) return null;
        List<T> list = mIndexControlListDictionary.containsKey(nextIndex) ?
                mIndexControlListDictionary.get(nextIndex) : null;
        if (list == null) return null;
        return new DictionaryData<T>(nextIndex, list, computeIndex(false, nextIndex) >= 0,
                computeIndex(true, nextIndex) >= 0);
    }

    /**
     * 清空数据
     */
    public synchronized void clear() {
        mIdControlIndexDictionary.clear();
        mIndexControlListDictionary.clear();
        mIndexArray.clear();
    }

}
