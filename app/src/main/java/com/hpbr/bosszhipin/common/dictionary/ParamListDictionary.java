package com.hpbr.bosszhipin.common.dictionary;

import com.hpbr.bosszhipin.module.commend.entity.ParamBean;

import java.util.List;

/**
 * Created by monch on 2017/7/1.
 */

public class ParamListDictionary extends ListDictionary<ParamBean> {

    @Override
    protected String itemToKey(ParamBean item) {
        return item.userId + "-" + item.jobId + "-" +
                item.expectId + "-" + item.from;
    }
}
