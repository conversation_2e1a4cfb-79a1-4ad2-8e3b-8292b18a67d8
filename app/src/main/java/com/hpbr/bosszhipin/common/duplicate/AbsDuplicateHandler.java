package com.hpbr.bosszhipin.common.duplicate;

import androidx.collection.ArrayMap;

import java.util.List;
import java.util.Map;

/**
 * Created by monch on 15/11/6.
 */
public abstract class AbsDuplicateHandler<T> {
    protected Map<Long, Boolean> map = new ArrayMap<>();
    public abstract List<T> getList(List<T> list);
    public abstract List<Object> getGenericList(List<T> list);
    public abstract List<Object> getNormalList(List<T> list);
    public void clear() {
        map.clear();
    }
    protected boolean containsKey(long id) {
        return map.containsKey(id);
    }
    protected void put(long id) {
        if (containsKey(id)) return;
        map.put(id, true);
    }
}
