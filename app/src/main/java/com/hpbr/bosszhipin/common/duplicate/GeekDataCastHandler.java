package com.hpbr.bosszhipin.common.duplicate;

import net.bosszhipin.api.bean.ServerGeekCardBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2017/9/20.
 */
public class GeekDataCastHandler extends AbsDuplicateHandler<ServerGeekCardBean> {
    @Override
    public List<ServerGeekCardBean> getList(List<ServerGeekCardBean> list) {
        return handler(new ArrayList<ServerGeekCardBean>(), list);
    }

    @Override
    public List<Object> getGenericList(List<ServerGeekCardBean> list) {
        return handler(new ArrayList<>(), list);
    }

    @Override
    public List<Object> getNormalList(List<ServerGeekCardBean> list) {
        return transfer(new ArrayList<>(), list);
    }

    private <T> List<T> handler(List<T> result, List<ServerGeekCardBean> list) {
        if (list == null) return result;
        for (ServerGeekCardBean bean : list) {
            if (containsKey(bean.expectId)) continue;
            put(bean.expectId);
            result.add((T) bean);
        }
        return result;
    }

    private <T> List<T> transfer(List<T> result, List<ServerGeekCardBean> list) {
        if (list == null) return result;
        for (ServerGeekCardBean bean : list) {
            result.add((T) bean);
        }
        return result;
    }
}
