package com.hpbr.bosszhipin.common.duplicate;

import net.bosszhipin.api.bean.ServerGeekListBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2017/9/20.
 */
public class GeekSearchDataCastHandler extends AbsDuplicateHandler<ServerGeekListBean> {
    @Override
    public List<ServerGeekListBean> getList(List<ServerGeekListBean> list) {
        return handler(new ArrayList<>(), list);
    }

    @Override
    public List<Object> getGenericList(List<ServerGeekListBean> list) {
        return handler(new ArrayList<>(), list);
    }

    @Override
    public List<Object> getNormalList(List<ServerGeekListBean> list) {
        return transfer(new ArrayList<>(), list);
    }

    private <T> List<T> handler(List<T> result, List<ServerGeekListBean> list) {
        if (list == null) return result;
        for (ServerGeekListBean bean : list) {
            if (containsKey(bean.expectId)) continue;
            put(bean.expectId);
            result.add((T) bean);
        }
        return result;
    }

    private <T> List<T> transfer(List<T> result, List<ServerGeekListBean> list) {
        if (list == null) return result;
        for (ServerGeekListBean bean : list) {
            result.add((T) bean);
        }
        return result;
    }
}
