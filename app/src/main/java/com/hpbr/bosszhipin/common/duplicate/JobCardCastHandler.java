package com.hpbr.bosszhipin.common.duplicate;

import androidx.collection.ArrayMap;

import com.hpbr.bosszhipin.base.GeekBaseCardBean;

import net.bosszhipin.api.bean.ServerJobCardBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author: Zhou<PERSON><PERSON>
 * Date: 2017/9/20.
 */
public class JobCardCastHandler<T extends GeekBaseCardBean> {
    private Map<String, Boolean> map = new ArrayMap<>();

    public List<GeekBaseCardBean> getList(List<T> list) {
        return handler(new ArrayList<>(), list);
    }

    private List<GeekBaseCardBean> handler(List<GeekBaseCardBean> result, List<T> list) {
        if (list == null) return result;
        for (T item : list) {
            if (item instanceof ServerJobCardBean) {
                ServerJobCardBean bean = (ServerJobCardBean) item;
                if (map.containsKey(bean.securityId)) continue;
                map.put(bean.securityId, true);
                result.add((T) bean);
            }
        }
        return result;
    }

    public void clear() {
        map.clear();
    }
}
