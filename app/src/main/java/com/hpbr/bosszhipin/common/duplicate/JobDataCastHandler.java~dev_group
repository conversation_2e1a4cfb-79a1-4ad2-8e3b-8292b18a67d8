package com.hpbr.bosszhipin.common.duplicate;

import net.bosszhipin.api.bean.ServerJobCardBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2018/5/5.
 */
public class JobDataCastHandler extends AbsDuplicateHandler<ServerJobCardBean> {
    @Override
    public List<ServerJobCardBean> getList(List<ServerJobCardBean> list) {
        return handler(new ArrayList<ServerJobCardBean>(), list);
    }

    @Override
    public List<Object> getGenericList(List<ServerJobCardBean> list) {
        return handler(new ArrayList<>(), list);
    }

    @Override
    public List<Object> getNormalList(List<ServerJobCardBean> list) {
        return transfer(new ArrayList<>(), list);
    }

    private <T> List<T> handler(List<T> result, List<ServerJobCardBean> list) {
        if (list == null) return result;
        for (ServerJobCardBean bean : list) {
            if (containsKey(bean.jobId)) continue;
            put(bean.jobId);
            result.add((T) bean);
        }
        return result;
    }

    private <T> List<T> transfer(List<T> result, List<ServerJobCardBean> list) {
        if (list == null) return result;
        for (ServerJobCardBean bean : list) {
            result.add((T) bean);
        }
        return result;
    }
}
