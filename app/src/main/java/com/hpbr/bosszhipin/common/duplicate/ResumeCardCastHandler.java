package com.hpbr.bosszhipin.common.duplicate;

import com.hpbr.bosszhipin.base.BossBaseCardBean;

import net.bosszhipin.api.bean.ServerGeekCardBean;
import net.bosszhipin.api.bean.ServerGeekListBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: ZhouYou
 * Date: 2017/9/20.
 */
public class ResumeCardCastHandler<T extends BossBaseCardBean> {
    private Map<String, Boolean> map = new ConcurrentHashMap<>();

    public List<BossBaseCardBean> getList(List<T> list) {
        return handler(new ArrayList<>(), list);
    }

    private List<BossBaseCardBean> handler(List<BossBaseCardBean> result, List<T> list) {
        if (list == null) return result;
        for (T item : list) {
            if (item instanceof ServerGeekCardBean) {
                ServerGeekCardBean bean = (ServerGeekCardBean) item;
                if (map.containsKey(bean.securityId)) continue;
                map.put(bean.securityId, true);
                result.add((T) bean);
            } else if (item instanceof ServerGeekListBean) {
                ServerGeekListBean bean = (ServerGeekListBean) item;
                if (map.containsKey(bean.securityId)) continue;
                map.put(bean.securityId, true);
                result.add((T) bean);
            }
        }
        return result;
    }

    public void clear() {
        map.clear();
    }
}
