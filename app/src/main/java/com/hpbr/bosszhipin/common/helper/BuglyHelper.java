package com.hpbr.bosszhipin.common.helper;

import android.content.Context;

import com.hpbr.bosszhipin.BuildKey;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.common.app.crash.CrashRepairMan;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.manager.CrashReporter;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;
import com.hpbr.bosszhipin.startup.process.Processer;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.mms.service.AppStatus;
import com.twl.utils.LogWatcher;

import java.util.Map;

/**
 * bugly 相关帮助类
 */
public final class BuglyHelper {

    private static boolean sInited = false;

    public static void initBugly(Context context) {
        if (PrivacyDelayInitializer.getInstance().isDelay()) {
            return;
        }
        ensureInitBugly(context);
    }

    public static void postCatchedException(Throwable throwable) {
        ensureInitBugly(App.get());
        CrashReport.postCatchedException(throwable);
    }

    private static void ensureInitBugly(Context context){
        if (!sInited) {
            sInited = true;
            innerInitBugly(context);
        }
    }

    private static void innerInitBugly(Context context) {
        CrashReport.setDeviceId(context,MobileUtil.getDid());
        // Bugly处理器
        CrashReport.UserStrategy userStrategy = new CrashReport.UserStrategy(context);
        switch (Processer.getProcessId()) {
            case Processer.PROCESS_MAIN:
                userStrategy.setCrashHandleCallback(new CrashReport.CrashHandleCallback() {
                    @Override
                    public synchronized Map<String, String> onCrashHandleStart(int crashType, String errorType, String errorMessage, String errorStack) {
                        ExtInfoHelper.print();
                        CrashReporter.crashAction(crashType, errorType, errorMessage, errorStack);
                        CrashRepairMan.onCatch(crashType, errorType, errorMessage, errorStack);
                        CrashProtectInitHelper.checkNativeCrash(crashType, errorMessage ,errorStack);
                        LogWatcher.check(errorMessage);
                        return super.onCrashHandleStart(crashType, errorType, errorMessage, errorStack);
                    }
                });
                break;
            case Processer.PROCESS_MMS:
                userStrategy.setCrashHandleCallback(new CrashReport.CrashHandleCallback() {
                    @Override
                    public synchronized Map<String, String> onCrashHandleStart(int crashType, String errorType, String errorMessage, String errorStack) {
                        CrashReport.putUserData(App.getAppContext(), "userID", AppStatus.getUserName());
                        CrashReporter.crashAction(crashType, errorType, errorMessage, errorStack);
                        CrashRepairMan.onCatch(crashType, errorType, errorMessage, errorStack);
                        CrashProtectInitHelper.checkNativeCrash(crashType, errorMessage, errorStack);
                        return super.onCrashHandleStart(crashType, errorType, errorMessage, errorStack);
                    }
                });
                break;
        }
        userStrategy.setUploadProcess(Processer.isMainProcess());
        CrashReport.initCrashReport(context, PackageConfigContants.PACKAGE_BUGLY_KEY, BuildInfoUtils.isDebug(), userStrategy);
        if (Processer.isMainProcess()) {
            try {
                if (UserManager.getUID() > 0) {
                    CrashReport.setUserId(String.valueOf(UserManager.getUID()) + UserManager.getUserRole().get());
                }
                if (BuildInfoUtils.isDebug()) {
                    CrashReport.putUserData(context,"buildTime",BuildInfoUtils.getString(BuildKey.BUILD_TIME));
                    CrashReport.putUserData(context,"buildType",BuildInfoUtils.getString(BuildKey.BUILD_TYPE));
                    CrashReport.putUserData(context,"buildBranch", BuildInfoUtils.getString(BuildKey.GIT_BRANCH));
                    CrashReport.putUserData(context,"hostConfig", HostConfig.CONFIG.name());
                }
            } catch (Exception ignored) {

            }
        }
        // Bugly 设置渠道
        CrashReport.setAppChannel(context, MobileUtil.getChannel());

        //在bugly之后注册，因为bugly在主线程崩溃后，会吃子线程的异常
        CrashProtectInitHelper.registerCrashCatcher();
    }

}
