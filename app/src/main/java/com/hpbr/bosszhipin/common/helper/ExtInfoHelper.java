package com.hpbr.bosszhipin.common.helper;

import android.app.Activity;
import android.content.Context;
import android.os.Build;

import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;

public final class ExtInfoHelper {
    private static final String TAG = "ExtInfoHelper";

    public static void print() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            return;
        }

        Context activity = Utils.getTopActivityOrApp();
        if (activity instanceof Activity) {
            try {
                boolean inMultiWindowMode = ScreenUtil.isInMultiWindowMode(activity);
                boolean isInPictureInPictureMode = ((Activity) activity).isInPictureInPictureMode();
                TLog.info(TAG, "inMultiWindowMode = %s, isInPictureInPictureMode = %s", inMultiWindowMode, isInPictureInPictureMode);
            } catch (Exception e) {
                TLog.error(TAG, e, "printScreenInfo error");
            }
        } else {
            TLog.info(TAG, "can't get a valid activity , just skip");
        }
    }

}
