package com.hpbr.bosszhipin.common.helper;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.TwlAppProvider;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.module.main.activity.ShortcutsReceiveActivity;
import com.hpbr.bosszhipin.module.main.activity.ZpReceiveActivity;
import com.hpbr.bosszhipin.push.NotifyDetailActivity;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.twl.xlog.rules.XLogRules;

public class LifeCycleLogHelper {

    public static void init(Application application){
        Utils.getActivityLifecycle().addOnAppStatusChangedListener(new Utils.OnAppStatusChangedListener() {
            @Override
            public void onForeground(Activity activity) {
                TwlAppProvider.refreshLocPer();
                LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.APP_STATUS).put("foreground", true).info();
            }

            @Override
            public void onBackground(Activity activity) {
                LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.APP_STATUS).put("foreground", false).info();
            }
        });
        application.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            boolean isStartLauncherActivity;
            boolean isReport;
            public String getArgs(@NonNull Activity activity){
                return String.format("%s@%s", activity.getClass().getSimpleName(), activity.hashCode());
            }

            @Override
            public void onActivityPreCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                if (activity instanceof NotifyDetailActivity || activity instanceof ZpReceiveActivity || activity instanceof ShortcutsReceiveActivity) {
                    return;
                }
                isStartLauncherActivity = isStartLauncherActivity || activity instanceof WelcomeActivity;
                boolean isHandler = false;
                if ("GetStartedActivity2".equals(activity.getClass().getSimpleName()) && UserManager.isCurrentLoginStatus()) { //登陆态 唤起App 进入了登录页面
                    if (AndroidDataStarGray.getInstance().startupFixFirstLauncher() == 1) {
                        activity.startActivity(new Intent(activity, WelcomeActivity.class));
                        activity.finish();
                        activity.overridePendingTransition(0, 0);
                        isHandler = true;
                        ApmAnalyzer.create().action("action_startup", "type_login_activity_error").p2(activity.getClass().getName()).p3(String.valueOf(isStartLauncherActivity)).debug().report();
                    }
                }
                if (!isStartLauncherActivity && !isReport) {
                    ApmAnalyzer.create().action("action_startup", "type_launcher_activity_error").p2(activity.getClass().getName()).p3(String.valueOf(isHandler)).debug().report();
                    isReport = true;
                }
            }


            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                TLog.info(XLogRules.TAG_ACT_CREATE, getArgs(activity));
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {
                TLog.info(XLogRules.TAG_ACT_START, getArgs(activity));
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                TLog.info(XLogRules.TAG_ACT_RESUME, getArgs(activity));
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                TLog.info(XLogRules.TAG_ACT_PAUSE, getArgs(activity));
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {
                TLog.info(XLogRules.TAG_ACT_STOP, getArgs(activity));
            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                TLog.info(XLogRules.TAG_ACT_DESTROY, getArgs(activity));
            }
        });
    }
}
