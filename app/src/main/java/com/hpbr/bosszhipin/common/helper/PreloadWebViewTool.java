package com.hpbr.bosszhipin.common.helper;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.MutableContextWrapper;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.os.Looper;
import android.text.TextUtils;
import android.webkit.CookieManager;
import android.webkit.RenderProcessGoneDetail;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebStorage;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.module.webview.WebViewNetTest;
import com.hpbr.bosszhipin.module.webview.WebViewUrlCheck;
import com.hpbr.bosszhipin.utils.AppStatusUtil;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GetCommonFeConfigResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.lang.ref.SoftReference;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * created by tongxiaoyun.
 * date: 2024/8/16
 * time: 下午3:17
 * description: webview 预加载工具类
 * 启动webivew预加载的入口
 * 1.首次进入F4
 * 2.切换身份后
 */
public class PreloadWebViewTool {

    private static final String TAG = "PreloadWebView";

    private int preloadStatus = NONE;

    private String preloadUrl;

    private String startWebViewURL;

    private long startEnter;

    private long startLoad;

    private static final int START = 1;
    private static final int NONE = 0;
    private static final int FAIL = 2;
    private static final int SUCCESS = 3;
    private static final int RELEASE = 4;
    private static final int NEED_CLEAR_CACHE = 5;
    public static final String FE_CONFIG = "key_fe_config";
    public static final String FE_LOAD_URL_TIME = "key_fe_load_url_time";
    private static final short MAX_LIMIT_COUNT_2 = 2;
    private static final short MAX_LIMIT_COUNT_3 = 3;
    private short mSSLErrorReportCount = 0;
    private short mHttpCodeReportCount = 0;
    private short mErrorReportCount = 0;
    private short mOtherErrorReportCount = 0;


    private SoftReference<WebView> webViewSoftReference;

    private String md5;

    public static PreloadWebViewTool getInstance() {
        return Holder.INSTANCE;
    }

    private static class Holder {
        private static final PreloadWebViewTool INSTANCE = new PreloadWebViewTool();
    }

    public void preload() {
        TLog.info(TAG, "WebView preload preloadStatus = %d", preloadStatus);
        if (preloadStatus == START || preloadStatus == SUCCESS || preloadStatus == FAIL || preloadStatus == RELEASE) {
            //只加载一次 无论是否加载成功 都只加载一次
            return;
        }
        if (preloadStatus == NEED_CLEAR_CACHE) {
            //需要清除缓存的
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (topActivity instanceof MainActivity) {
                //仅在 MainActivity 进行清理
                preloadStatus = NONE;
                clearCache();
            }
            return;
        }
        requestH5Config();

    }

    public void resetReloadStatus() {
        resetStatus();
        preload();
    }

    public boolean checkLowMemory() {
        ActivityManager.MemoryInfo info = getMemoryInfo();
        long availMem = info.availMem >> 30;
        int webViewInSecondMem = AndroidDataStarGray.getInstance().webViewInSecondMem();
        boolean lowMemory = info.lowMemory;
        TLog.info(TAG, "availMem : %d,webviewInSecondMem: %d , lowMemory : %b ", availMem, webViewInSecondMem, lowMemory);
        return lowMemory || availMem < webViewInSecondMem;
    }

    public void resetStatus() {
        preloadStatus = NONE;
        SpManager.get()
                .user()
                .edit()
                .remove(FE_LOAD_URL_TIME)
                .remove(FE_CONFIG)
                .apply();
    }


    public void requestH5Config() {
        TLog.info(TAG, "webViewInSecond %b", AndroidDataStarGray.getInstance().webViewInSecond());
        if (!AndroidDataStarGray.getInstance().webViewInSecond()) {
            return;
        }
        if (checkLowMemory()) {
            preloadStatus = FAIL;
            return;
        }

        preloadStatus = START;
        int identity = AccountHelper.getIdentity();
        SimpleApiRequest request = configRequest(initLocalData(), identity);
        request.setRequestCallback(new ApiRequestCallback<GetCommonFeConfigResponse>() {
            @Override
            public void onSuccess(ApiData<GetCommonFeConfigResponse> data) {
                if (data.resp == null) {
                    preloadStatus = FAIL;
                    return;
                }
                String gsonResp = GsonUtils.toJson(data.resp);
                TLog.info(TAG, "onSuccess() called with: resp = [ %s ]", GsonUtils.toJson(data.resp));
                preloadUrl = data.resp.url;
                String oldYearMonthDay = SpManager.get().user().getString(FE_LOAD_URL_TIME, "");
                if (LText.empty(preloadUrl)) {
                    preloadStatus = FAIL;
                    if (data.resp.switchInSecond == 1) {
                        dotWebViewSecondOther("urlEmpty", gsonResp);
                    }
                    TLog.info(TAG, "onSuccess() called preloadUrl is null");
                    return;
                }
                if (data.resp.switchInSecond == 0) {
                    preloadStatus = FAIL;
                    //实验组 变 对照组降级处理
                    if (LText.notEmpty(oldYearMonthDay)) {
                        Activity topActivity = ForegroundUtils.get().getTopActivity();
                        if (topActivity instanceof MainActivity) {
                            //仅在 MainActivity 进行清理
                            clearCache();
                        } else {
                            preloadStatus = NEED_CLEAR_CACHE;
                        }
                    }
                    TLog.info(TAG, "onSuccess() called switch is 0");
                    return;
                }

                if (identity != AccountHelper.getIdentity()) {
                    preloadStatus = FAIL;
                    StringBuilder desc = new StringBuilder();
                    desc.append(identity);
                    desc.append("->");
                    desc.append(AccountHelper.getIdentity());
                    dotWebViewSecondOther("identityError", desc.toString());
                    TLog.info(TAG, "onSuccess() called identity is different");
                    return;
                }
                //是否加载空白页面
                String newYearMonthDay = DateUtil.time2Date("yyyy-MM-dd", System.currentTimeMillis());
                boolean loadBlank = false;
                if (AndroidDataStarGray.getInstance().webViewInSecondBlank()) {
                    loadBlank = LText.equal(md5, data.resp.md5) && LText.equal(oldYearMonthDay, newYearMonthDay);
                }
                SpManager.get().user().edit().putString(FE_CONFIG, gsonResp).apply();
                registerIdleHandlerCreateWebView(loadBlank);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.debug(TAG, "onFailed() called with: reason = [ %s ]", reason == null ? "" : reason);
                dotWebViewSecondOther("requestError", reason == null ? "" : reason.toString());
                preloadStatus = FAIL;
            }
        }).execute();
    }

    private GetCommonFeConfigResponse initLocalData() {
        String feConfigLocation = SpManager.get().user().getString(FE_CONFIG, null);
        GetCommonFeConfigResponse locationGetCommonFeConfigResponse = null;
        if (LText.notEmpty(feConfigLocation)) {
            locationGetCommonFeConfigResponse = GsonUtils.fromJson(feConfigLocation, GetCommonFeConfigResponse.class);
        }
        return locationGetCommonFeConfigResponse;
    }

    private @NonNull SimpleApiRequest configRequest(GetCommonFeConfigResponse locationGetCommonFeConfigResponse, int identity) {
        SimpleApiRequest request = SimpleApiRequest.GET(URLConfig.URL_COMMON_FE_CONFIG);
        request.addParam("curidentity", identity);
        if (locationGetCommonFeConfigResponse != null) {
            md5 = locationGetCommonFeConfigResponse.md5;
            request.addParam("prets", locationGetCommonFeConfigResponse.prets)
                    .addParam("md5", md5);
        }
        return request;
    }

    private void clearCache() {
        WebStorage.getInstance().deleteAllData();
        CookieManager c = CookieManager.getInstance();
        c.removeAllCookies(value -> {
        });
        SpManager.get().user().edit().remove(FE_LOAD_URL_TIME).apply();
    }


    public void clearCacheAsync() {
        resetStatus();
        AppThreadFactory.createThread(() -> {
            WebStorage.getInstance().deleteAllData();
            CookieManager c = CookieManager.getInstance();
            c.removeAllCookies(value -> {
            });
        });
    }

    private void registerIdleHandlerCreateWebView(boolean loadBlank) {
        Looper.myQueue().addIdleHandler(() -> {
            TLog.debug(TAG, "preload queueIdle() called");
            webViewSoftReference = new SoftReference<>(createWebView(loadBlank));
            return false;
        });
    }


    private WebView createWebView(boolean loadBlank) {
        TLog.debug(TAG, "create WebView start %d", System.currentTimeMillis());
        WebView webview = new WebView(new MutableContextWrapper(App.get()));
        WebSettings settings = webview.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);
        //setAppCacheEnabled 被废弃，参考这篇文章的方案三https://blog.csdn.net/qq_26914291/article/details/128645935
        settings.setCacheMode(WebSettings.LOAD_DEFAULT);
        webview.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                TLog.info(TAG, "onPageFinished() called with: url = [ %s ]", url);
                preloadStatus = SUCCESS;
                String yearMonthDay = DateUtil.time2Date("yyyy-MM-dd", System.currentTimeMillis());
                SpManager.get().user().edit().putString(FE_LOAD_URL_TIME, yearMonthDay).apply();

            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && error != null) {
                    TLog.info(TAG, "onReceivedError() called with:  errorCode = [ %d ] , errorDesc = [ %s ]", error.getErrorCode(), error.getDescription());
                    String failingUrl = "";
                    if (request != null) {
                        failingUrl = request.getUrl() == null ? "" : request.getUrl().toString();
                    }
                    String description = error.getDescription() == null ? "" : error.getDescription().toString();
                    dotSecondWebViewError(failingUrl, error.getErrorCode(), description);
                }
                preloadStatus = FAIL;
            }


            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                super.onReceivedSslError(view, handler, error);
                TLog.info(TAG, "onReceivedSslError() called with: error = [ %s ]", error == null ? "" : error.toString());
                preloadStatus = FAIL;
                String url = view != null ? view.getUrl() : "";
                dotSecondWebViewSSLError(url, error);
            }

            @Deprecated
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                TLog.info(TAG, "onReceivedError() called with: errorCode = [ %d ], description = [ %s ], failingUrl = [ %s ]", errorCode, description, failingUrl);
                preloadStatus = FAIL;
                dotSecondWebViewError(failingUrl, errorCode, description);
            }

            @Override
            public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
                super.onReceivedHttpError(view, request, errorResponse);
                preloadStatus = FAIL;
                Uri uri = request != null ? request.getUrl() : null;
                String url = uri != null ? uri.toString() : "";
                String ref = view != null ? view.getUrl() : "";
                int code = errorResponse != null ? errorResponse.getStatusCode() : -1;
                dotSecondWebViewHttpCodeError(url, String.valueOf(code), ref);
            }

            @Override
            public boolean onRenderProcessGone(WebView view, RenderProcessGoneDetail detail) {
                if (view != null) {
                    dotSecondWebViewGone(view.getUrl());
                }
                return true;
            }
        });
        if (!loadBlank) {
            syncCookie(preloadUrl);
        }
        webview.removeJavascriptInterface("searchBoxJavaBridge_");
        webview.loadUrl(loadBlank ? "about:blank" : preloadUrl);
        TLog.debug(TAG, "create WebView end %d", System.currentTimeMillis());
        return webview;
    }

    /**
     * 设置cookie
     *
     * @param url
     */
    void syncCookie(String url) {
        if (App.get() == null) return;
        String t2 = UserManager.getToken();
        if (LText.empty(t2)) return;
        t2 = "t2=" + t2;
        URL cookUrl = null;
        try {
            cookUrl = new URL(url);
        } catch (MalformedURLException e) {
            MException.printError(e);
            cookUrl = null;
        }
        if (cookUrl == null) return;
        String host = cookUrl.getHost();
        if (WebViewUrlCheck.isExternalUrl(url)) return;
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.flush();
        cookieManager.setAcceptCookie(true);
        String cookieUrl = cookUrl.getProtocol() + "://" + cookUrl.getHost() + "/";
        String domain = getDomain(host);
        String domainValue = TextUtils.isEmpty(domain) ? "" : (";Domain=" + domain);
        cookieManager.setCookie(cookieUrl, t2 + domainValue);
        cookieManager.flush();
    }


    private String getDomain(String host) {
        if (host != null) {
            String regex = "\\b((?:[0-9]{1,3}\\.){3}[0-9]{1,3})\\b";
            Pattern pattern = Pattern.compile(regex); //IP
            Matcher matcher = pattern.matcher(host);
            if (!matcher.matches()) { //非IP
                int startIndex = host.lastIndexOf(".") - 1;
                int endIndex = host.lastIndexOf(".", startIndex);
                if (endIndex > 0 && endIndex < host.length()) {
                    return host.substring(endIndex);
                }
            }
        }
        return host;
    }

    public void releaseWebView() {
        if (webViewSoftReference == null) return;
        if (webViewSoftReference.get() == null) return;
        preloadStatus = RELEASE;
        webViewSoftReference.get().destroy();
        webViewSoftReference = null;
    }

    public String getStartWebViewURL() {
        return startWebViewURL;
    }

    public void setStartWebViewURL(String startWebViewURL) {
        this.startWebViewURL = startWebViewURL;
    }

    public long getStartEnter() {
        return startEnter;
    }

    public void markStartEnter() {
        this.startEnter = System.currentTimeMillis();
        TLog.info(TAG, "markStartEnter : %d", startEnter);
    }

    public long getStartLoad() {
        return startLoad;
    }

    public void markStartLoad() {
        this.startLoad = System.currentTimeMillis();
        TLog.info(TAG, "startLoad : %d", startLoad);
    }


    private void dotSecondWebViewGone(String url) {
        ApmAnalyzer analyzer = ApmAnalyzer.create();
        analyzer.action(ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND, ApmAnalyticsAction.TYPE_RENDER_PROCESS_GONE);
        //加载的链接
        if (LText.notEmpty(url)) {
            analyzer.p2(url);
        }
        //当前在哪个页面
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity != null) {
            analyzer.p5(topActivity.getClass().getName());
        }
        //是否在前台
        analyzer.p6(String.valueOf(App.get().isForeground()));
        //可用内存大小
        analyzer.p7(String.valueOf(getMemoryInfo().availMem));
        //停留在后台的时间
        analyzer.p8(String.valueOf(AppStatusUtil.getAppInBackgroundTime()));
        analyzer.report();
    }


    private void dotSecondWebViewError(String failingUrl, int errorCode, String description) {
        if (mErrorReportCount >= MAX_LIMIT_COUNT_2) return;
        mErrorReportCount++;
        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND, ApmAnalyticsAction.TYPE_WEBVIEW_ERROR)
                .p2(failingUrl)
                .p3(String.valueOf(errorCode))
                .p4(description)
                .report();
    }


    private void dotSecondWebViewSSLError(String url, SslError sslError) {
        if (mSSLErrorReportCount >= MAX_LIMIT_COUNT_2) return;
        mSSLErrorReportCount++;
        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND, ApmAnalyticsAction.TYPE_WEBVIEW_SSL_ERROR)
                .p2(url)
                .p3(String.valueOf(sslError))
                .report();
        WebViewNetTest.netTestDns(sslError, ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND);
    }


    private void dotSecondWebViewHttpCodeError(String url, String code, String ref) {
        if (mHttpCodeReportCount >= MAX_LIMIT_COUNT_2) return;
        mHttpCodeReportCount++;
        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND, ApmAnalyticsAction.TYPE_WEBVIEW_HTTP_CODE)
                .p2(url)
                .p3(code)
                .p4(ref)
                .report();
    }


    private void dotWebViewSecondOther(String tag, String desc) {
        if (mOtherErrorReportCount >= MAX_LIMIT_COUNT_3) return;
        mOtherErrorReportCount++;
        ApmAnalyzer
                .create()
                .action(ApmAnalyticsAction.ACTION_WEB_VIEW_IN_SECOND, "type_other_error")
                .p3(tag)
                .p4(desc)
                .report();
    }


    private ActivityManager.MemoryInfo getMemoryInfo() {
        ActivityManager manager = (ActivityManager) App.get().getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo info = new ActivityManager.MemoryInfo();
        manager.getMemoryInfo(info);
        return info;
    }

    public boolean isLoadSuccess() {
        return preloadStatus == SUCCESS;
    }
}
