package com.hpbr.bosszhipin.common.helper;

import com.hpbr.apm.config.content.ContentSupplier;
import com.hpbr.apm.config.content.bean.pri.PrivateConfig;

public final class ShuMengHelper {
    /**
     * 获取apm个人配置, 是否关闭数盟
     *
     * @return
     */
    public static boolean isDisableShuMeng() {
        //注意要在apm初始化后使用
        boolean disable = false;
        try {
            PrivateConfig privateConfig = ContentSupplier.get().getPrivateConfig();
            if (privateConfig != null) {
                disable = privateConfig.disableSzlm;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return disable;
    }
}
