package com.hpbr.bosszhipin.common.helper;

import androidx.annotation.NonNull;
import androidx.annotation.Size;

import com.facebook.drawee.generic.RoundingParams;
import com.hpbr.bosszhipin.base.App;
import com.monch.lbase.util.Scale;

import java.util.Arrays;
import java.util.List;

/**
 * Created by zhangxiangdong on 2019/8/1 10:54.
 * <p>
 * https://lanhu.kanzhun-inc.com/web/#/item/board?pid=6986691c-1cc3-41ed-b901-de84df49853c
 */
public class SudokuHelper {

    private static final int COLUMN = 3;
    private static final int COLUMN_2 = 2;
    private static final int ONE_FRAME = 1;

    private final List<?> imageList;
    private boolean enableFourSquare = false; // 4张图片时使用正方形样式（2x2）

    public void setEnableFourSquare(boolean enableFourSquare) {
        this.enableFourSquare = enableFourSquare;
    }

    public static SudokuHelper of(@NonNull @Size(9) List<?> imageList) {
        return new SudokuHelper(imageList);
    }

    public static SudokuHelper of(@NonNull @Size(9) String[] imageArr) {
        return new SudokuHelper(Arrays.asList(imageArr));
    }

    private SudokuHelper(@NonNull @Size(9) List<?> imageList) {
        this.imageList = imageList;
    }

    public RoundingParams getRoundingParams(int position, boolean enableAllRoundCorner) {
        int corner = Scale.dip2px(App.getAppContext(), 6);
        if (enableAllRoundCorner) {
            return RoundingParams.fromCornersRadii(corner, corner, corner, corner);
        } else {
            return RoundingParams.fromCornersRadii(
                    hasTopLeftSibling(position) ? 0 : corner,
                    hasTopRightSibling(position) ? 0 : corner,
                    hasBottomRightSibling(position) ? 0 : corner,
                    hasBottomLeftSibling(position) ? 0 : corner
            );
        }
    }

    private boolean hasTopLeftSibling(int position) {
        return hasLeftSibling(position) || hasTopSibling(position);
    }

    private boolean hasTopRightSibling(int position) {
        return hasTopSibling(position) || hasRightSibling(position);
    }

    private boolean hasBottomRightSibling(int position) {
        return hasBottomSibling(position) || hasRightSibling(position);
    }

    private boolean hasBottomLeftSibling(int position) {
        return hasLeftSibling(position) || hasBottomSibling(position);
    }

    private boolean hasLeftSibling(int position) {
        int row = position / getColumn();
        int leftSibling = position - ONE_FRAME;
        int leftSiblingRow = leftSibling / getColumn();
        return row == leftSiblingRow && isInArray(leftSibling);
    }

    private boolean hasTopSibling(int position) {
        int topSibling = position - getColumn();
        return isInArray(topSibling);
    }

    private boolean hasRightSibling(int position) {
        int row = position / getColumn();
        int rightSibling = position + ONE_FRAME;
        int rightSiblingRow = rightSibling / getColumn();
        return row == rightSiblingRow && isInArray(rightSibling);
    }

    private boolean hasBottomSibling(int position) {
        int bottomSibling = position + getColumn();
        return isInArray(bottomSibling);
    }

    private int getColumn() {
        return enableFourSquare ? COLUMN_2 : COLUMN;
    }

    private boolean isInArray(int position) {
        return position >= 0 && position < imageList.size();
    }

}
