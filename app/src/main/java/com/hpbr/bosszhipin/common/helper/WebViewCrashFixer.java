package com.hpbr.bosszhipin.common.helper;

import android.content.Context;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import java.util.Locale;

public final class WebViewCrashFixer {
    private static final String TAG = "WebViewCrashFixer";
    private static final String KEY_WEB_VIEW = "webview";
    private static final String KEY_WEB_VIEW_CHROMIUM_SP = "WebViewChromiumPrefs";


    private WebViewCrashFixer() {
        throw new UnsupportedOperationException("u can't instantiate me...");
    }

    /**
     * 删除data/data/packageName/目录下所有含有 webview 的文件夹
     */
    public static void deleteWebViewCache(Context context, boolean auto) {
        boolean deleteResult;
        String errorMsg = "";
        StringBuilder deleteDir = new StringBuilder();

        try {
            context
                    .getSharedPreferences(KEY_WEB_VIEW_CHROMIUM_SP, Context.MODE_PRIVATE)
                    .edit()
                    .clear()
                    .apply();


            deleteResult = FileUtils.deleteFilesInDirWithFilter(PathUtils.getDataDirInternalPath(), pathname -> {
                String absolutePath = LText.getDefaultIfEmptyString(pathname.getAbsolutePath(), "");
                boolean result = FileUtils.isDir(pathname) && absolutePath.toLowerCase(Locale.ROOT).contains(KEY_WEB_VIEW);
                if (result) {
                    deleteDir.append(FileUtils.getFileName(pathname)).append(",");
                    TLog.info(TAG, "delete: %s", absolutePath);
                } else {
                    TLog.debug(TAG, "skip: %s", absolutePath);
                }
                return result;
            });
        } catch (Exception e) {
            deleteResult = false;
            errorMsg = e.getMessage();
            TLog.error(TAG, e, "deleteWebViewCache error: %s", errorMsg);
        }

        try {
            ApmAnalyzer.create().action(AnalyticsAction.ACTION_WEB_ViEW_FIX, AnalyticsAction.TYPE_DELETE)
                    .param("p2", String.valueOf(deleteResult))
                    .param("p3", deleteDir.toString())
                    .param("p4", errorMsg)
                    .param("p5", String.valueOf(auto))
                    .debug()
                    .report();
        } catch (Exception e) {
            TLog.error(TAG, e, "apm report error: %s", errorMsg);
        }
    }

    private static boolean  hasOpenWebView = false;

    /**
     * 本次启动，首次打开H5时埋点
     */
    public static void dotWebViewPageOpen() {
        PreloadWebViewTool.getInstance().markStartEnter();
        if (hasOpenWebView){
            return;
        }
        hasOpenWebView = true;
        ApmAnalyzer.create().action(AnalyticsAction.ACTION_WEB_ViEW_FIX, AnalyticsAction.TYPE_OPEN)
                .report();
    }

}