package com.hpbr.bosszhipin.common.listeners;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;

/**
 * Created by monch on 15/8/17.
 */
public abstract class AbsCopyTextListener {

    private Context context;
    private String title;
    private String text;

    public AbsCopyTextListener(Context context, String title, String text) {
        this.context = context;
        this.title = title;
        this.text = text;
    }

    protected void copy() {
        showDialog();
    }


    private void showDialog() {
        if (context == null || TextUtils.isEmpty(text)) return;
        String desc = LText.empty(title) ? "是否复制该内容" : "是否复制" + title;
        DialogUtils d = new DialogUtils.Builder((Activity) context)
                .setDoubleButton()
                .setTitle(R.string.warm_prompt)
                .setDesc(desc)
                .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        StringUtil.copyText(context, text);
                        ToastUtils.showText("复制完成");
                    }
                })
                .setNegativeAction(R.string.string_cancel)
                .build();
        d.show();
    }


}
