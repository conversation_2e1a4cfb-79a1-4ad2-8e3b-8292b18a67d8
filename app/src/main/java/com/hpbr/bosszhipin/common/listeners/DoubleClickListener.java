package com.hpbr.bosszhipin.common.listeners;

import android.os.Handler;
import android.os.Looper;
import android.view.View;

/**
 * @ClassName ：DoubleClickListener
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/4/26  8:52 下午
 */
public class DoubleClickListener implements View.OnClickListener {

    //记录连续点击次数
    private int clickCount = 0;
    private Handler handler;
    private DoubleClickCallBack myClickCallBack;
    private final int DURATION = 200;

    public DoubleClickListener(DoubleClickListener.DoubleClickCallBack myClickCallBack) {
        this.myClickCallBack = myClickCallBack;
        handler = new Handler(Looper.getMainLooper());
    }

    @Override

    public void onClick(View v) {
        clickCount++;
        handler.postDelayed(() -> {
            if (clickCount == 1) {
                if (myClickCallBack != null) myClickCallBack.oneClick(v);
            } else if (clickCount == 2) {
                if (myClickCallBack != null) myClickCallBack.doubleClick(v);
            }
            handler.removeCallbacksAndMessages(null);
            clickCount = 0;
        }, DURATION);//延时timeout后执行run方法中的代码
    }

    public interface DoubleClickCallBack {
        /**
         * 点击一次的回调
         *
         * @param view
         */
        void oneClick(View view);

        /**
         * 连续点击两次的回调
         *
         * @param view
         */
        void doubleClick(View view);

    }
}
