package com.hpbr.bosszhipin.common.listeners;

import android.app.Activity;
import android.view.View;

import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.AvatarActivity;
import com.hpbr.bosszhipin.module.main.AxisBean;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2016/6/13.
 */
public class OnAvatarClickListener implements View.OnClickListener {

    private Activity activity;

    /**
     * 小头像
     */
    private String userAvatar;

    /**
     * 大头像
     */
    private String userAvatarLarge;

    /**
     * 默认头像
     */
    private int userHeadImage;

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public void setUserAvatarLarge(String userAvatarLarge) {
        this.userAvatarLarge = userAvatarLarge;
    }

    public void setUserHeadImage(int userHeadImage) {
        this.userHeadImage = userHeadImage;
    }

    private View.OnClickListener bgActionListener;

    public OnAvatarClickListener(Activity activity) {
        this(activity, null);
    }

    public OnAvatarClickListener(Activity activity, View.OnClickListener bgActionListener) {
        this.activity = activity;
        this.bgActionListener = bgActionListener;
    }

    @Override
    public void onClick(View v) {
        viewAvatar(v);
        if (bgActionListener != null) {
            bgActionListener.onClick(v);
        }
    }

    /**
     * 查看我的头像预览
     *
     * @param v
     */
    private void viewAvatar(View v) {
        if (!UserManager.isCurrentLoginStatus()) {
            L.d("无法关联头像控件(看自己) - 未登录");
            return;
        }
        if (activity == null || activity.isFinishing()) {
            L.d("无法关联头像控件(看自己) - 页面被销毁");
            return;
        }
        if (userHeadImage > 0 && userHeadImage < 17) {
            L.d("默认头像，不能查看");
            return;
        }
        if (LText.empty(userAvatar)) {
            L.d("小头像为空，不能查看");
            return;
        }
        AxisBean axis = ViewCommon.getAvatarAxis(activity, v);
        AvatarActivity.Builder.build(activity)
                .axis(axis)
                .urls(new String[]{userAvatar, userAvatarLarge})
                .launch();
    }

}
