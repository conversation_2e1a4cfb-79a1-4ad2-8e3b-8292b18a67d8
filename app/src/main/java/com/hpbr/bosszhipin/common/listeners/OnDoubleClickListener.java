package com.hpbr.bosszhipin.common.listeners;

import android.view.MotionEvent;
import android.view.View;

public class OnDoubleClickListener implements View.OnClickListener {

    private static final int DURATION = 400;



    private long preClickTime;

    private OnDoubleClickCallBack callBack;

    public void setOnDoubleClickListener(View view, OnDoubleClickCallBack callBack) {
        this.callBack = callBack;
        view.setOnClickListener(this);
    }


    /**
     * Called when a view has been clicked.
     *
     * @param v The view that was clicked.
     */
    @Override
    public void onClick(View v) {
        if (preClickTime == 0) {
            preClickTime = System.currentTimeMillis();
        } else {
            long currentClickTime = System.currentTimeMillis();
            //双击
            if (currentClickTime - preClickTime < DURATION) {
                if (callBack != null) {
                    callBack.onDoubleClickListener();
                }
            }
            preClickTime = currentClickTime;
        }
    }


    public interface OnDoubleClickCallBack {
        void onDoubleClickListener();
    }

}
