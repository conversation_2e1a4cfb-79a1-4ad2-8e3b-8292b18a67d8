package com.hpbr.bosszhipin.common.report;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.module.contacts.activity.ReportEvidenceActivity;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.monch.lbase.util.LText;

/***
 * 新版牛人端举报
 *
 * 804.8【用户产品】牛人举报投诉流程优化改版
 *
 * wiki:https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=63946558
 *
 * 原因列表:
 * /mpa/html/mix/geek-report-reasons?reportedId=1&source=2&targetId=3&securityId=4&identity=1
 */
public class GeekReport {

    /***
     * @param reportedId 举报
     * @param jobId      targetId 职位id
     * @param securityId
     * @param source
     * identity   老接口，登录用户的身份 0 牛人 1 boss
     */
    public void show(final Context context, final long reportedId, long jobId, int source, String securityId, long expectId) {

//        final String webHost = URLConfig.getWebHost();
//        final String Path = "mpa/html/mix/geek-report-reasons?";
//
//        StringBuilder url = new StringBuilder();
//        url.append(webHost).append(Path)
//                .append("reportedId=").append(reportedId)
//                .append("&targetId=").append(jobId)
//                .append("&securityId=").append(securityId)
//                .append("&source=").append(covertServerSource(source))//Server接口使用
//                .append("&sourceFrom=").append(covertH5DetailReportDoneSource(source))//H5埋点用来源
//                .append("&expectId=").append(expectId)//
//                .append("&identity=").append(UserManager.isGeekRole() ? 0 : 1);
//
//        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing()))
//            return;
////        new ZPManager(context, url.toString()).handler();
//
//        Intent intent = new Intent(context, WebViewActivity.class);
//        intent.putExtra(Constants.DATA_URL, url.toString());
//        intent.putExtra(Constants.DATA_FROM, WebViewActivity.FROM_GEEK_REPORT);
//        AppUtil.startActivity(context, intent);

        show(context, reportedId, jobId, source, securityId, expectId, "");
    }

    public void show(final Context context, final long reportedId, long jobId, int source, String securityId, long expectId, String imgUrl) {

        String urlReportChat = UserGrayFeatureManager.getInstance().getUrlReportChat();
        if (LText.empty(urlReportChat)) {
            final String webHost = URLConfig.getWebHost();
            final String path = "mpa/html/mix/geek-report-reasons";
            urlReportChat = webHost + path;
        }

        StringBuilder url = new StringBuilder();
        url.append(urlReportChat)
                .append("?")
                .append("reportedId=").append(reportedId)
                .append("&targetId=").append(jobId)
                .append("&securityId=").append(securityId)
                .append("&source=").append(covertServerSource(source))
                .append("&sourceFrom=").append(covertH5DetailReportDoneSource(source))//H5埋点用来源
                .append("&expectId=").append(expectId)//
                .append("&identity=").append(UserManager.isGeekRole() ? 0 : 1)
                .append("&intercept=").append(1);

        if (!TextUtils.isEmpty(imgUrl)) {
            url.append("&images=").append(imgUrl);
        }


        if (context == null || (context instanceof Activity && ((Activity) context).isFinishing()))
            return;
//        new ZPManager(context, url.toString()).handler();

        Intent intent = new Intent(context, WebViewActivity.class);
        intent.putExtra(Constants.DATA_URL, url.toString());
        intent.putExtra(Constants.DATA_FROM, WebViewActivity.FROM_GEEK_REPORT);
        AppUtil.startActivity(context, intent);
    }

    /**
     * 将本地页面跳转的来源转换为H5埋点所需要的source
     * <p>
     * 埋点detail-report-done：和 detail-report-click
     **/
    private int covertH5DetailReportDoneSource(int source) {
        switch (source) {
            case ReportEvidenceActivity.SOURCE_CHAT:
                return 1;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME:
                return 2;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL:
                return 3;
            case ReportEvidenceActivity.SOURCE_CUSTOMER_SERVICE:
                return 4;
            case ReportEvidenceActivity.SOURCE_GEEK_INTERVIEW_FEEDBACK:
                return 5;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME_QUICK_HANDLER:
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_QUICK_HANDLER:
                return 6;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_SAFETIP:
                return 7;
            case ReportEvidenceActivity.SOURCE_GEEK_CHAT_SCREEN:
                return 9;
            case ReportEvidenceActivity.SOURCE_INTERVIEW_SECURITY_DIALOG:
                return 10;
            default:
                return 0;
        }
    }

    /**
     * 将本地页面跳转的来源转换为接口所需要的source
     */
    private int covertServerSource(int source) {
        switch (source) {
            case ReportEvidenceActivity.SOURCE_CHAT:
                return 1;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME:
                return 2;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL:
                return 3;
            case ReportEvidenceActivity.SOURCE_CUSTOMER_SERVICE:
                return 4;
            case ReportEvidenceActivity.SOURCE_GEEK_INTERVIEW_FEEDBACK:
                return 5;
            case ReportEvidenceActivity.SOURCE_GEEK_RESUME_QUICK_HANDLER:
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_QUICK_HANDLER:
                return 6;
            case ReportEvidenceActivity.SOURCE_BOSS_DETAIL_SAFETIP:
                return 7;
            case ReportEvidenceActivity.SOURCE_GEEK_CHAT_SCREEN:
                return 9;
            case ReportEvidenceActivity.SOURCE_INTERVIEW_SECURITY_DIALOG:
                return 10;
            default:
                return 0;
        }
    }
}
