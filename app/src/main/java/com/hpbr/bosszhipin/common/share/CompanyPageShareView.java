package com.hpbr.bosszhipin.common.share;


import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;

import java.io.Serializable;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.drawable.RoundedBitmapDrawable;
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * created by yhy
 * date 2021/7/19
 * desc:
 */
public class CompanyPageShareView extends FrameLayout {

    private LinearLayout mLinearLayout3;
    private TextView mCompanyTv;
    private TextView mCompanyDescTv;
    private ImageView mCompanyLogo;
    private MTextView mCompanyTimeTv;
    private MTextView mCompanyLocationTv;
    private MTextView mCompanyWealTv;
    private MTextView mCompanyPositionTv;

    private LinearLayout mCompanyTimeLl;
    private LinearLayout mCompanyLocationLl;
    private LinearLayout mCompanyWealLl;
    private LinearLayout mCompanyPositionLl;

    public CompanyPageShareView(@NonNull Context context) {
        super(context);
        View.inflate(context, R.layout.view_company_share, this);
        mLinearLayout3 = (LinearLayout) findViewById(R.id.linearLayout3);
        mCompanyTv = (TextView) findViewById(R.id.company_tv);
        mCompanyDescTv = (TextView) findViewById(R.id.company_desc_tv);
        mCompanyLogo = findViewById(R.id.company_logo);
        mCompanyTimeTv = (MTextView) findViewById(R.id.company_time_tv);
        mCompanyLocationTv = (MTextView) findViewById(R.id.company_location_tv);
        mCompanyWealTv = (MTextView) findViewById(R.id.company_weal_tv);
        mCompanyPositionTv = (MTextView) findViewById(R.id.company_position_tv);

        mLinearLayout3 = (LinearLayout) findViewById(R.id.linearLayout3);
        mCompanyTimeLl = (LinearLayout) findViewById(R.id.company_time_ll);
        mCompanyLocationLl = (LinearLayout) findViewById(R.id.company_location_ll);
        mCompanyWealLl = (LinearLayout) findViewById(R.id.company_weal_ll);
        mCompanyPositionLl = (LinearLayout) findViewById(R.id.company_position_ll);

    }

    public void setData(CompanyShareData data) {
        mCompanyTv.setText(data.brandName);
        mCompanyDescTv.setText(StringUtil.connectTextWithChar(" · ", data.stageName, data.scaleName, data.industryName));

        if (data.logo != null) {
            RoundedBitmapDrawable drawableImage = RoundedBitmapDrawableFactory.create(getResources(), data.logo);
            drawableImage.setCornerRadius(ZPUIDisplayHelper.dp2px(getContext(), 4));
            mCompanyLogo.setImageDrawable(drawableImage);
        } else {
            mCompanyLogo.setImageResource(R.mipmap.ic_company_logo_default);
        }

        if (TextUtils.isEmpty(data.startDate) || TextUtils.isEmpty(data.endDate)) {
            mCompanyTimeLl.setVisibility(GONE);
        } else {
            mCompanyTimeTv.setText(StringUtil.connectTextWithChar(" - ", data.startDate, data.endDate));
        }

        if (TextUtils.isEmpty(data.location)) {
            mCompanyLocationLl.setVisibility(GONE);
        } else {
            mCompanyLocationTv.setText(data.location);
        }
        if (data.welNum == 0) {
            mCompanyWealLl.setVisibility(GONE);
        } else {
            mCompanyWealLl.setVisibility(VISIBLE);
            mCompanyWealTv.setText(String.format(Locale.getDefault(), "%1d项福利待遇", data.welNum));
        }


    }

    public static class CompanyShareData implements Serializable {
        private static final long serialVersionUID = 3383548296951432966L;

        private String brandName;
        private transient Bitmap logo;
        private String stageName;//上市
        private String scaleName;// 规模
        private String industryName;// 行业
        private String startDate;
        private String endDate;
        private String location;
        private int welNum;
        private long positionNum;

        public CompanyShareData setBrandName(String brandName) {
            this.brandName = brandName;
            return this;
        }

        public CompanyShareData setLogo(@Nullable Bitmap logo) {
            this.logo = logo;
            return this;
        }

        public CompanyShareData setStageName(String stageName) {
            this.stageName = stageName;
            return this;
        }

        public CompanyShareData setScaleName(String scaleName) {
            this.scaleName = scaleName;
            return this;
        }

        public CompanyShareData setLocation(String location) {
            this.location = location;
            return this;
        }

        public CompanyShareData setIndustryName(String industryName) {
            this.industryName = industryName;
            return this;
        }

        public CompanyShareData setStartDate(String startDate) {
            this.startDate = startDate;
            return this;
        }

        public CompanyShareData setEndDate(String endDate) {
            this.endDate = endDate;
            return this;
        }

        public CompanyShareData setWelNum(int welNum) {
            this.welNum = welNum;
            return this;
        }

        public CompanyShareData setPositionNum(long positionNum) {
            this.positionNum = positionNum;
            return this;
        }

        /**
         * 回收logo资源
         */
        public void recycleBitmap() {
            if (logo != null && !logo.isRecycled()) {
                logo.recycle();
                logo = null;
            }
        }
    }
}