package com.hpbr.bosszhipin.common.share;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.facebook.drawee.drawable.RoundedBitmapDrawable;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerCommonIconBean;
import net.bosszhipin.api.bean.ServerHandicappedBean;
import net.bosszhipin.api.bean.ServerHandicappedDetailBean;

import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class GeekSharePositionCardView extends FrameLayout {

    private final MTextView desc;
    private final TextView tv_job_title;
    private final MTextView salary;
    private final SimpleDraweeView sdvIcon;
    private final MTextView tv_job_des;
    private final com.hpbr.bosszhipin.module.resume.views.FlowLayout fbl;

    private final LinearLayout llHandiBlock;
    private final RecyclerView rcvSkills;
    private final LinearLayout llNomore;
    private final SimpleDraweeView sdvIconHandi;
    private final MTextView mtvHandiDesc;

    public GeekSharePositionCardView(@NonNull Context context) {
        super(context);
        View.inflate(context, R.layout.view_geek_share_position_mini, this);
        desc = findViewById(R.id.desc);
        sdvIcon = findViewById(R.id.sdv_handi_icon);
        salary = findViewById(R.id.salary);
        fbl = findViewById(R.id.fbl);
        tv_job_des = findViewById(R.id.tv_job_des);
        tv_job_title = findViewById(R.id.tv_job_title);

        rcvSkills = findViewById(R.id.rcv_skills);
        llNomore = findViewById(R.id.ll_nomre);
        sdvIconHandi = findViewById(R.id.sdv_icon);
        llHandiBlock = findViewById(R.id.ll_handi_block);
        mtvHandiDesc = findViewById(R.id.mtv_hadi_desc);

    }

    public void setData(@NonNull CardData cardData) {
        RoundedBitmapDrawable drawable = new RoundedBitmapDrawable(getResources(), cardData.getBitmapAvatar());
        drawable.setCircle(true);
        desc.setText(cardData.getDesc());
        salary.setText(cardData.getSalary());
        tv_job_des.setText(cardData.jobDesc);

        setFlexBoxData(cardData.bossUpLabels, cardData.requiredSkill);


        if (cardData.pageType == 2) {
            sdvIcon.setVisibility(VISIBLE);
            sdvIcon.setImageResource(R.mipmap.ic_jd_share_youxuan);
        } else if (cardData.pageType == 3) {
            sdvIcon.setVisibility(VISIBLE);
            sdvIcon.setImageResource(R.mipmap.ic_jd_share_anxin);
        } else {
            sdvIcon.setVisibility(GONE);
        }

        if (cardData.handiInfo != null && (cardData.handiInfo.showType == 1 || cardData.handiInfo.showType == 0)) {
            tv_job_title.setVisibility(GONE);
            tv_job_des.setVisibility(GONE);
            llHandiBlock.setVisibility(VISIBLE);
            setHandiData(cardData.handiInfo);
        } else {
            tv_job_title.setVisibility(VISIBLE);
            tv_job_des.setVisibility(VISIBLE);
            llHandiBlock.setVisibility(GONE);
        }
    }

    private void setFlexBoxData(List<String> bossupLabels, List<String> flexBoxData) {
        if (!LList.isEmpty(flexBoxData) || !LList.isEmpty(bossupLabels)) {
            fbl.setVisibility(View.VISIBLE);
            fbl.removeAllViews();

            if (!LList.isEmpty(bossupLabels)) {
                for (String text : bossupLabels) {
                    if (TextUtils.isEmpty(text)) {
                        continue;
                    }
                    fbl.addView(getAnxintag(getContext(), text));
                }
            }

            if (!LList.isEmpty(flexBoxData)) {
                for (String text : flexBoxData) {
                    if (TextUtils.isEmpty(text)) {
                        continue;
                    }
                    fbl.addView(getTag(getContext(), text));
                }
            }
        } else {
            fbl.setVisibility(View.GONE);
        }
    }


    private FrameLayout getTag(Context context, String text) {
        FrameLayout frameLayout = new FrameLayout(context);
        frameLayout.setPadding(0, 0, ZPUIDisplayHelper.dp2px(context, 6), 0);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        int paddingHorizontal = ZPUIDisplayHelper.dp2px(context, 6);
        int paddingVertical = ZPUIDisplayHelper.dp2px(context, 3);
        MTextView textView = new MTextView(context);
        textView.setText(text);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setGravity(Gravity.CENTER);
//        textView.setMaxWidth(ZPUIDisplayHelper.dp2px(context, 65));
        textView.setSingleLine();
        textView.setHorizontallyScrolling(false);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        textView.setTextColor(ContextCompat.getColor(context, R.color.text_c2));
        textView.setBackgroundResource(R.drawable.bg_f1_match_word_gray);
        textView.setLayoutParams(params);
        frameLayout.addView(textView);
        return frameLayout;
    }

    private FrameLayout getAnxintag(Context context, String text) {
        FrameLayout frameLayout = new FrameLayout(context);
        frameLayout.setPadding(0, 0, ZPUIDisplayHelper.dp2px(context, 6), 0);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        int paddingHorizontal = ZPUIDisplayHelper.dp2px(context, 6);
        int paddingVertical = ZPUIDisplayHelper.dp2px(context, 3);
        MTextView textView = new MTextView(context);
        textView.setText(text);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setGravity(Gravity.CENTER);
//        textView.setMaxWidth(ZPUIDisplayHelper.dp2px(context, 65));
        textView.setSingleLine();
        textView.setHorizontallyScrolling(false);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        textView.setTextColor(ContextCompat.getColor(context, R.color.color_FF0D9EA3));
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setBackgroundResource(R.drawable.bg_position_card_4_corner_1ec882);
        textView.setLayoutParams(params);
        frameLayout.addView(textView);
        return frameLayout;
    }

    private TextView getTag(Context context) {
        int paddingHorizontal = ZPUIDisplayHelper.dp2px(context, 6);
        int paddingVertical = ZPUIDisplayHelper.dp2px(context, 3);
        TextView textView = new TextView(context);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setGravity(Gravity.CENTER);
//        textView.setSingleLine();
        textView.setMaxLines(1);
        textView.setEllipsize(TextUtils.TruncateAt.END);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        textView.setTextColor(ContextCompat.getColor(context, R.color.text_c2));
        textView.setBackgroundResource(R.drawable.bg_f1_match_word_gray);
        return textView;
    }

    @SuppressWarnings("UnusedReturnValue")
    public static class CardData {
        public List<String> requiredSkill;
        public List<String> bossUpLabels;
        public List<ServerCommonIconBean> titleIcons;
        public ServerHandicappedBean handiInfo;
        private String desc;
        private String jobDesc;
        private String salary;
        private String name;
        private String position;
        private String status;
        private String avatar;
        private String miniProgramTitle;
        private long jobId;
        private int pageType;
        private transient Bitmap bitmapAvatar;

        private CardData() {
        }

        public static CardData obj() {
            return new CardData();
        }

        public CardData desc(String desc) {
            this.desc = desc;
            return this;
        }

        public CardData salary(String salary) {
            this.salary = salary;
            return this;
        }

        public CardData setPageType(int pageType) {
            this.pageType = pageType;
            return this;
        }

        public CardData name(String name) {
            this.name = name;
            return this;
        }

        public CardData position(String position) {
            this.position = position;
            return this;
        }

        public CardData status(String status) {
            this.status = status;
            return this;
        }

        public CardData handiInfo(ServerHandicappedBean handiInfo) {
            this.handiInfo = handiInfo;
            return this;
        }

        public CardData avatar(String avatar) {
            this.avatar = avatar;
            return this;
        }

        public String getJobDesc() {
            return jobDesc;
        }

        public CardData miniProgramTitle(String miniProgramTitle) {
            this.miniProgramTitle = miniProgramTitle;
            return this;
        }

        public CardData jobId(long jobId) {
            this.jobId = jobId;
            return this;
        }

        public CardData bitmapAvatar(Bitmap bitmapAvatar) {
            this.bitmapAvatar = bitmapAvatar;
            return this;
        }

        public CardData setRequiredSkill(List<String> requiredSkill) {
            this.requiredSkill = requiredSkill;
            return this;
        }

        public CardData setBossupLables(List<String> bossupLables) {
            this.bossUpLabels = bossupLables;
            return this;
        }

        public CardData setIcons(List<ServerCommonIconBean> icons) {
            this.titleIcons = icons;
            return this;
        }


        public String getDesc() {
            return desc;
        }

        public String getSalary() {
            return salary;
        }

        public String getName() {
            return name;
        }

        public String getPosition() {
            return position;
        }

        public String getStatus() {
            return status;
        }

        public String getAvatar() {
            return avatar;
        }

        public String getMiniProgramTitle() {
            return miniProgramTitle;
        }

        public long getJobId() {
            return jobId;
        }

        public Bitmap getBitmapAvatar() {
            return bitmapAvatar;
        }

        public CardData jobDesc(String jobDesc) {
            this.jobDesc = jobDesc;
            return this;
        }
    }


    public void setHandiData(ServerHandicappedBean info) {

        if (info != null && info.showType == 0 && !LList.isEmpty(info.detailInfos)) {
            HandiInfoAdapter adapter = new HandiInfoAdapter(info.detailInfos);
            rcvSkills.setAdapter(adapter);
            llNomore.setVisibility(View.GONE);
            rcvSkills.setVisibility(View.VISIBLE);
        } else if (info != null && info.showType == 1 && !LList.isEmpty(info.detailInfos)) {
            llNomore.setVisibility(View.VISIBLE);
            rcvSkills.setVisibility(View.GONE);
            ServerHandicappedDetailBean bean = LList.getElement(info.detailInfos, 0);
            if (bean != null) {
                sdvIconHandi.setImageURI(bean.icon);
                mtvHandiDesc.setText(bean.title);
            }
        }

    }


    public static class HandiInfoAdapter extends BaseQuickAdapter<ServerHandicappedDetailBean, BaseViewHolder> {

        public HandiInfoAdapter(@Nullable List<ServerHandicappedDetailBean> data) {
            super(R.layout.item_handicapped_info, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerHandicappedDetailBean item) {
            SimpleDraweeView ivLogo = helper.getView(R.id.iv_name);
            MTextView mtvName = helper.getView(R.id.tv_name);
            MTextView mtvDesc = helper.getView(R.id.tv_desc);

            mtvName.setText(item.title);
            mtvDesc.setText(item.content);

            try {
                ivLogo.setImageResource(getIconResourceByType(item.acceptType));
            } catch (Exception e) {
                e.printStackTrace();
            }

        }


        private int getIconResourceByType(int type) {
            if (type == 1) {
                return R.mipmap.icon_handi_qita;
            } else if (type == 2) {
                return R.mipmap.icon_handi_shili;
            } else if (type == 3) {
                return R.mipmap.icon_handi_tingli;
            } else if (type == 4) {
                return R.mipmap.icon_handi_yuyan;
            } else if (type == 5) {
                return R.mipmap.icon_handi_zhili;
            } else if (type == 6) {
                return R.mipmap.icon_handi_canji;
            } else if (type == 7) {
                return R.mipmap.icon_handi_jingshen;
            } else {
                return R.mipmap.ic_hadicapped_type;
            }
        }
    }


}
