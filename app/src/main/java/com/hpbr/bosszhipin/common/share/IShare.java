package com.hpbr.bosszhipin.common.share;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.module.share.linteners.ShareType;

import net.bosszhipin.api.GeekSharePositionResponse;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/5/15 10:48.
 */
public interface IShare {

    void shareWechat();

    void shareWechatFriend(@NonNull GeekSharePositionResponse resp);//分享到微信好友

    void shareMoment();

    void shareSms();

    void shareQQ();

    interface Callback {
        void onStart(ShareType type, int subType, String desc);

        void onComplete(ShareType type, boolean success, int subType, String desc);
    }

}
