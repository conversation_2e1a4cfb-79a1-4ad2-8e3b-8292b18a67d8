package com.hpbr.bosszhipin.common.share;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.login.util.QQUtil;
import com.hpbr.bosszhipin.module.share.linteners.ShareType;
import com.tencent.connect.common.Constants;
import com.tencent.connect.share.QQShare;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.tencent.tauth.UiError;
import com.twl.ui.ToastUtils;

/**
 * QQ分享回调接收页
 */
public class QQShareCallBackActivity extends BaseActivity2 {
    private static final String KEY_QQ_SHARE = "KEY_QQ_SHARE";
    private Tencent mTencent;
    /**
     * 分享类型，埋点使用
     */
    private int shareSubType;;
    private final IUiListener qqShareListener = new IUiListener() {
        @Override
        public void onComplete(Object o) {
            ShareCommon.sendShareCompleteBroadcast(ShareType.QQ, true, shareSubType, "");
        }

        @Override
        public void onError(UiError uiError) {
            handleShareFailed(R.string.string_share_failed);
        }

        @Override
        public void onCancel() {
            ShareCommon.sendShareCompleteBroadcast(ShareType.QQ, false, shareSubType, "");
        }

        @Override
        public void onWarning(int i) {

        }
    };

    public static void start(Context context, ShareQQ paramsBean) {
        Intent intent = new Intent(context, QQShareCallBackActivity.class);
        intent.putExtra(KEY_QQ_SHARE, paramsBean);
        AppUtil.startActivity(context, intent, ActivityAnimType.NONE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        makeStatusBarTransparent();
        if (!Tencent.isSupportShareToQQ(this)) {
            // qqSdk有缓存，需要重置 https://wiki.connect.qq.com/%e9%87%8d%e7%bd%aeqq-tim%e5%ae%89%e8%a3%85%e4%bf%a1%e6%81%af%e5%86%85%e5%ad%98%e7%bc%93%e5%ad%98
            Tencent.resetTargetAppInfoCache();
            if (!Tencent.isSupportShareToQQ(this)) {
                // 确实没有安装qq
                handleShareFailed(R.string.string_no_install_qq);
                return;
            }
        }

        mTencent = QQUtil.getTencent();
        if (mTencent == null) {
            handleShareFailed(R.string.string_share_failed);
            return;
        }

        if (getIntent() == null || getIntent().getSerializableExtra(KEY_QQ_SHARE) == null) {
            handleShareFailed(R.string.string_share_failed);
            return;
        }
        ShareQQ shareParams = (ShareQQ) getIntent().getSerializableExtra(KEY_QQ_SHARE);
        if (shareParams == null) {
            return;
        }
        shareSubType = shareParams.getSubType();
        final Bundle params = new Bundle();
        if (shareParams.getShareType() != QQShare.SHARE_TO_QQ_TYPE_IMAGE) {
            params.putString(QQShare.SHARE_TO_QQ_TITLE, shareParams.getQqTitle());
            params.putString(QQShare.SHARE_TO_QQ_TARGET_URL, shareParams.getQqUrl());
            params.putString(QQShare.SHARE_TO_QQ_SUMMARY, shareParams.getQqDesc());
            params.putString(QQShare.SHARE_TO_QQ_IMAGE_URL, shareParams.getQqImageUrl());
        } else {
            params.putString(QQShare.SHARE_TO_QQ_IMAGE_LOCAL_URL, shareParams.getQqImageUrl());
        }
        params.putString(QQShare.SHARE_TO_QQ_APP_NAME, this.getString(R.string.app_name));
        params.putInt(QQShare.SHARE_TO_QQ_KEY_TYPE, shareParams.getShareType());
        mTencent.shareToQQ(this, params, qqShareListener);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.REQUEST_QQ_SHARE) {
            Tencent.onActivityResultData(requestCode, resultCode, data, qqShareListener);
            if (data == null) {
                // 检查qq是否未安装（处理启动app时安装了qq，在后台卸载了qq的极端情况）
                Tencent.resetTargetAppInfoCache();
                if (!Tencent.isSupportShareToQQ(this)) {
                    ToastUtils.showText(R.string.string_no_install_qq);
                }
            }
            AppUtil.finishActivity(this, ActivityAnimType.NONE);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mTencent = null;
    }

    private void handleShareFailed(@StringRes int errorToast) {
        ShareCommon.sendShareCompleteBroadcast(ShareType.QQ, false, shareSubType, "");
        ToastUtils.showText(errorToast);
        AppUtil.finishActivity(this, ActivityAnimType.NONE);
    }

}
