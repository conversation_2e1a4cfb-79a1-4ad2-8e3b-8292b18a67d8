package com.hpbr.bosszhipin.common.share;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.ThumbnailUtils;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.DownloadPhotoCommon;
import com.hpbr.bosszhipin.config.OtherConfig;
import com.hpbr.bosszhipin.module.share.ShareSMS;
import com.hpbr.bosszhipin.module.share.ShareWechat;
import com.hpbr.bosszhipin.module.share.linteners.ShareType;
import com.hpbr.bosszhipin.module.webview.DownLoadUtil;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.tencent.connect.share.QQShare;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelmsg.WXTextObject;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.twl.ui.ToastUtils;
import com.twl.utils.ConverterUtils;
import com.twl.utils.file.FileIOUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.GeekSharePositionResponse;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;

/**
 * Created by monch on 2017/2/17.
 */
public class ShareCommon implements IShare {

    private static final String LOG_TAG = "share_image";
    private static final int THUMB_SIZE = 120;
    private static final long SIZE_30K = 30 * 1024L;
    private static final int MAX_TEXT_SIZE = 10_000;

    private static final int DEFAULT_AVATAR_RES_ID = R.mipmap.logo;

    private static ShareCommon mStaticShareInstance;
    public static ShareType mShareType;

    @NonNull
    private final WeakReference<Activity> host;
    private final String weixinTitle;
    private final String weixinDesc;
    private final String qqTitle;
    private final String qqDesc;
    private final String qqIcon;
    private final String smsTitle;
    private final String clickUrl;
    private final String avatarUrl;
    private String friendMomentUrl;
    private final String copyLinkUrl;
    private final int avatarIndex;
    private final File imageFile;
    private final String text;
    @NonNull
    private final WeakReference<Callback> callbackWeakRef;
    private final int from;
    private final long jobId;

    public String getWeixinTitle() {
        return weixinTitle;
    }

    public String getWeixinDesc() {
        return weixinDesc;
    }

    public String getSmsTitle() {
        return smsTitle;
    }

    public String getClickUrl() {
        return clickUrl;
    }

    public String getCopyLink() {
        if (!TextUtils.isEmpty(copyLinkUrl)) {
            return copyLinkUrl;
        } else {
            return clickUrl;
        }
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    private ShareCommon(Builder builder) {
        host = builder.host;
        this.weixinTitle = builder.weixinTitle;
        this.weixinDesc = builder.weixinDesc;
        this.smsTitle = builder.smsTitle;
        this.clickUrl = builder.clickUrl;
        this.avatarUrl = builder.avatarUrl;
        this.avatarIndex = builder.avatarIndex;
        this.imageFile = builder.imageFile;
        this.text = builder.text;
        this.copyLinkUrl = builder.copyLinkUrl;
        this.callbackWeakRef = new WeakReference<>(builder.callback);
        this.from = builder.from;
        this.jobId = builder.jobId;
        this.qqTitle = builder.qqTitle;
        this.qqDesc = builder.qqDesc;
        this.qqIcon = builder.qqIcon;
        ShareCommon.mStaticShareInstance = this;
    }

    @Override
    public void shareWechat() {
        shareWechat(0);
    }

    @Override
    public void shareWechatFriend(@NonNull GeekSharePositionResponse resp) {
        /** 分享小程序到微信好友（没用到） */
    }

    public void shareWechat(int subType) {
        mShareType = ShareType.WECHAT;
        start(mShareType, subType, "");
        shareWechat(mShareType);
    }

    @Override
    public void shareMoment() {
        shareMoment(0);
    }

    public void shareMoment(int subType) {
        mShareType = ShareType.WEMOMENT;
        start(mShareType, subType, "");
        shareWechat(mShareType);
    }

    @Override
    public void shareSms() {
        if (this.host == null || this.host.get() == null) return;
        Activity activity = this.host.get();
        mShareType = ShareType.SMS;
        start(mShareType);
        ShareSMS sms = new ShareSMS(activity);
        sms.setTitle(smsTitle);
        sms.share();
        complete(mShareType, true, null);
    }



    @Override
    public void shareQQ() {
        shareQQ(0);
    }

    public void shareQQ(int subType) {
        Activity activity = this.host.get();
        if (activity == null) {
            return;
        }
        if (LText.isEmptyOrNull(qqTitle)) {
            // 分享必须有标题内容，否则QQ回调失败
            ToastUtils.showText(R.string.string_share_failed);
            return;
        }
        mShareType = ShareType.QQ;
        start(mShareType, subType, "");
        ShareQQ shareQQ = new ShareQQ()
                .setQqTitle(qqTitle)
                .setQqDesc(qqDesc)
                .setQqUrl(clickUrl)
                .setQqImageUrl(qqIcon)
                .setSubType(subType);
        shareQQ.share(activity);
    }

    private void dealShareResult(Bitmap bitmap, ShareType type) {

        if (this.host == null || this.host.get() == null) return;
        Activity activity = this.host.get();

        byte[] result = null;
        if (bitmap == null) {
            Bitmap icon = BitmapFactory.decodeResource(activity.getResources(), DEFAULT_AVATAR_RES_ID);
            if (icon != null) {
                result = ConverterUtils.bitmap2Bytes(icon);
            }
        } else {
            result = ConverterUtils.bitmap2Bytes(bitmap);
        }
        int thumbSize = THUMB_SIZE;
        while (result != null && result.length >= SIZE_30K) {
            Bitmap sourceBitmap = BitmapFactory.decodeByteArray(result, 0, result.length);
            if (sourceBitmap == null) break;
            Bitmap thumb = ThumbnailUtils.extractThumbnail(sourceBitmap, thumbSize, thumbSize);
            if (thumb == null) break;
            result = bmpToByteArray(thumb);
            thumbSize -= 10;
        }
        ShareWechat shareInstance = new ShareWechat();
        shareInstance.setWapUrl(clickUrl);
        shareInstance.setTitle(weixinTitle);
        shareInstance.setDesc(weixinDesc);
        shareInstance.setAvatar(result);
//        shareInstance.setImageFile(imageFile);
        shareInstance.setType(type);
        shareInstance.share();
    }

    private void shareWechat(final ShareType type) {
        DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
        downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
            @Override
            public void onDownloadComplete(Bitmap bitmap) {
                dealShareResult(bitmap, type);
            }

            @Override
            public void onDownloadFailed() {
                dealShareResult(null, type);
            }
        });
        downloadPhotoCommon.onNewDownloadTask(avatarUrl);
    }


    private static byte[] bmpToByteArray(final Bitmap bmp) {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        bmp.compress(Bitmap.CompressFormat.PNG, 100, output);
        byte[] result = output.toByteArray();
        try {
            output.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    private void start(ShareType type, int subType, String desc) {
        if (callbackWeakRef.get() != null) {
            callbackWeakRef.get().onStart(type, subType, desc);
        }
    }

    private void start(ShareType type) {
        start(type, 0, "");
    }

    private void complete(ShareType type, boolean success, String desc) {
        complete(type, success, 0, desc);
    }

    private void complete(ShareType type, boolean success, int subType, String desc) {
        if (callbackWeakRef.get() != null) {
            callbackWeakRef.get().onComplete(type, success, subType, desc);
        }
    }

    public static class Builder implements Serializable {
        private static final long serialVersionUID = 7518354525618602855L;

        private transient WeakReference<Activity> host;
        private String weixinTitle;
        private String weixinDesc;
        private String qqTitle;
        private String qqDesc;
        private String qqIcon;
        private String smsTitle;

        private String clickUrl;
        private String friendMomentUrl;
        private String copyLinkUrl;

        private String avatarUrl;
        private int avatarIndex;
        private transient File imageFile;
        private String text;
        private transient Callback callback;
        private int from = -1;
        private long jobId = -1;

        public static Builder create(Activity activity) {
            Builder builder = new Builder();
            builder.host = new WeakReference<>(activity);
            return builder;
        }

        public static Builder create() {
            return new Builder();
        }

        public Builder setHost(Activity activity) {
            this.host = new WeakReference<>(activity);
            return this;
        }

        public Builder setWeixin(String title, String desc) {
            this.weixinTitle = title;
            this.weixinDesc = desc;
            return this;
        }

        public Builder setWeixinTitle(String title) {
            this.weixinTitle = title;
            return this;
        }

        public Builder setWeixinDesc(String desc) {
            this.weixinDesc = desc;
            return this;
        }

        public Builder setSmsTitle(String text) {
            this.smsTitle = text;
            return this;
        }

        public Builder setClickUrl(String text) {
            this.clickUrl = text;
            return this;
        }

        public Builder setFriendMomentUrl(String text) {
            this.friendMomentUrl = text;
            return this;
        }

        public Builder setCopyLinkeUrl(String text) {
            this.copyLinkUrl = text;
            return this;
        }

        public Builder setAvatar(String avatarUrl) {
            this.avatarUrl = avatarUrl;
            return this;
        }

        public Builder setAvatar(String text, int index) {
            this.avatarUrl = text;
            this.avatarIndex = index;
            return this;
        }

        public Builder setImageFile(File file) {
            this.imageFile = file;
            return this;
        }

        public Builder setText(String text) {
            this.text = text;
            return this;
        }

        public Builder setCallback(Callback callback) {
            this.callback = callback;
            return this;
        }

        public Builder setFrom(int from) {
            this.from = from;
            return this;
        }

        public Builder setJobId(long jobId) {
            this.jobId = jobId;
            return this;
        }

        public Builder setQQTitle(String title) {
            this.qqTitle = title;
            return this;
        }

        public Builder setQQDesc(String desc) {
            this.qqDesc = desc;
            return this;
        }

        public Builder setQQIcon(String icon) {
            this.qqIcon = icon;
            return this;
        }

        public ShareCommon build() {
            return new ShareCommon(this);
        }

        public void shareWechat() {
            new ShareCommon(this).shareWechat();
        }

        public void shareWemoment() {
            new ShareCommon(this).shareMoment();
        }

        public void shareSms() {
            new ShareCommon(this).shareSms();
        }

    }

    public static void sendShareCompleteBroadcast(ShareType type, boolean success, String desc) {
        if (mStaticShareInstance != null) {
            mStaticShareInstance.complete(type, success, desc);
        }
        mShareType = null;
        mStaticShareInstance = null;
    }

    public static void sendShareCompleteBroadcast(ShareType type, boolean success, int subType, String desc) {
        if (mStaticShareInstance != null) {
            mStaticShareInstance.complete(type, success, subType, desc);
        }
        mShareType = null;
        mStaticShareInstance = null;
    }

    public void shareImageToChat(Bitmap imageData, int subType, String mainTitle) {

        if (imageData == null) return;

        mShareType = ShareType.WECHAT;
        start(mShareType, subType, mainTitle);
        App.getRemoteExecutor().submit(new Runnable() {
            @Override
            public void run() {
                byte[] result = null;
                Bitmap thumbBmp = Bitmap.createScaledBitmap(imageData, 120, 220, true);
                result = bitmap2ByteArray(thumbBmp);
                boolean ret = shareImage(imageData, result, SendMessageToWX.Req.WXSceneSession);
                App.get().getMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (ret) {
                            complete(mShareType, true, subType, mainTitle);
                        }
                    }
                });

            }
        });

    }

    public void shareQQImage(@Nullable String localImageUrl, int subType) {
        Activity activity = this.host.get();
        if (activity == null) {
            return;
        }
        mShareType = ShareType.QQ;
        start(mShareType, subType, "");
        ShareQQ shareQQ = new ShareQQ()
                .setQqImageUrl(localImageUrl == null ? "" : localImageUrl)
                .setShareType(QQShare.SHARE_TO_QQ_TYPE_IMAGE)
                .setSubType(subType);
        shareQQ.share(activity);
    }

    public String getQQShareImageUrl(@Nullable Bitmap bitmap) {
        if (bitmap == null) {
            return "";
        }
        final String[] url = {""};
        try {
            String path = PathUtils.getCacheDirInternalChildPath("qqShare");
            final String fileName = FileUtils.getUniqueFileName(null,"zp_",".png");
            final File imageFile = new File(path, fileName);
            FileIOUtils.saveBitmap(bitmap, imageFile, new FileIOUtils.OnSaveFileResultCallBack() {
                @Override
                public void onSuccess(File file, String parentPath) {
                    url[0] = file.getPath();
                }

                @Override
                public void onFail() {

                }
            });
        } catch (Exception e) {
            TLog.error("ShareCommon", "get qq share image url failed, %s", e.getMessage());
        }

        return url[0];
    }

    public void shareImageToChat(Bitmap imageData, int subType) {
        shareImageToChat(imageData, subType, "");
    }

    public void shareImageToChat(Bitmap imageData, String mainTitle) {
        shareImageToChat(imageData, 0, mainTitle);
    }

    public void shareImageToMoment(Bitmap imageData, int subType, String mainTitle) {

        if (this.host == null || this.host.get() == null) return;
        Activity activity = this.host.get();

        mShareType = ShareType.WEMOMENT;
        start(mShareType, subType, mainTitle);
        App.getRemoteExecutor().submit(new Runnable() {
            @Override
            public void run() {
                byte[] result = null;
                Bitmap thumbBmp = Bitmap.createScaledBitmap(imageData, 120, 220, true);
                result = bitmap2ByteArray(thumbBmp);
                boolean ret = shareImage(imageData, result, SendMessageToWX.Req.WXSceneTimeline);
                App.get().getMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (ret) {
                            complete(mShareType, true, subType, mainTitle);
                        }
                    }
                });

            }
        });

    }

    public void shareImageToMoment(Bitmap imageData, int subType) {
        shareImageToMoment(imageData, subType, "");
    }

    public void shareImageToMoment(Bitmap imageData, String mainTitle) {
        shareImageToMoment(imageData, 0, mainTitle);
    }

    /**
     * 使用Bitmap形式分享
     * intent不要太大超过1M，此处设置的250kb
     */
    private boolean shareImage(Bitmap imageData, byte[] result, int scene) {

        if (this.host == null || this.host.get() == null) return false;
        Activity activity = this.host.get();

        IWXAPI api = WXAPIFactory.createWXAPI(activity.getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        api.registerApp(OtherConfig.WE_CHAT_APP_ID);
        if (!api.isWXAppInstalled()) {
            App.get().getMainHandler().post(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showText("未检测到您的微信客户端");
                }
            });
            return false;
        }
        //初始化 WXImageObject 和 WXMediaMessage 对象
        WXImageObject imgObj = new WXImageObject();
        imgObj.imageData = FileIOUtils.compressBitmap(imageData, 250);
//        imgObj.setImagePath(imageFile.getAbsolutePath());

        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;
        imageData.recycle();
        //设置缩略图
        msg.thumbData = result;
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = "img";
        req.message = msg;
        req.scene = scene;
//        req.openId = getOpenId();
        //调用api接口，发送数据到微信
        return api.sendReq(req);
    }


    private boolean shareImage(Bitmap imageData, int scene) {

        if (this.host == null || this.host.get() == null) return false;
        Activity activity = this.host.get();

        IWXAPI api = WXAPIFactory.createWXAPI(activity.getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        api.registerApp(OtherConfig.WE_CHAT_APP_ID);
        if (!api.isWXAppInstalled()) {
            T.ss("未检测到您的微信客户端");
            return false;
        }

        //初始化 WXImageObject 和 WXMediaMessage 对象
        WXImageObject imgObj = new WXImageObject(imageData);
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

        //设置缩略图
        Bitmap thumbBmp = Bitmap.createScaledBitmap(imageData, 120, 220, true);
        imageData.recycle();
//        msg.thumbData = bmpToByteArray(thumbBmp);
        msg.thumbData = bitmap2ByteArray(thumbBmp);
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = "img";
        req.message = msg;
        req.scene = scene;
//        req.openId = getOpenId();
        //调用api接口，发送数据到微信
        return api.sendReq(req);
    }

    private byte[] bitmap2ByteArray(Bitmap bitmap) {

        if (this.host == null || this.host.get() == null) return new byte[]{};
        Activity activity = this.host.get();

        byte[] result = null;
        if (bitmap == null) {
            Bitmap icon = BitmapFactory.decodeResource(activity.getResources(), DEFAULT_AVATAR_RES_ID);
            if (icon != null) {
                result = ConverterUtils.bitmap2Bytes(icon);
            }
        } else {
            result = ConverterUtils.bitmap2Bytes(bitmap);
        }
        int thumbSize = THUMB_SIZE;
        while (result != null && result.length >= SIZE_30K) {
            Bitmap sourceBitmap = BitmapFactory.decodeByteArray(result, 0, result.length);
            if (sourceBitmap == null) break;
            Bitmap thumb = ThumbnailUtils.extractThumbnail(sourceBitmap, thumbSize, thumbSize);
            if (thumb == null) break;
            result = bmpToByteArray(thumb);
            thumbSize -= 10;
        }
        return result;
    }

    public void shareImage(Bitmap data, String uniqueIdentity, Runnable callback, String mainTitle) { // 保存图片

        if (this.host == null || this.host.get() == null) return;
        Activity activity = this.host.get();

        mShareType = ShareType.PICTURE;
        start(mShareType);
        Executors.newSingleThreadExecutor().execute(() -> {
            DownLoadUtil.saveBitmapToLocalImage(uniqueIdentity, data, activity.getApplicationContext(), false);
            App.get().getMainHandler().post(() -> {
                callback.run();
                complete(mShareType, true, mainTitle);
            });
        });
    }

    public void shareLink(@NonNull String url) {

        if (this.host == null || this.host.get() == null) return;
        Activity activity = this.host.get();

        mShareType = ShareType.LINK;
        start(mShareType);

        ClipboardManager clipboard = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);
        if (clipboard != null) {
            ClipData clip = ClipData.newPlainText("Copied Link", url);
            clipboard.setPrimaryClip(clip);
            T.ss("链接已复制");
            complete(mShareType, true, "链接已复制");
        } else {
            T.ss("复制失败，请稍候重试");
        }
    }

    /**
     * 分享文字
     */
    public void shareTextToChat() {
        mShareType = ShareType.WECHAT;
        boolean ret = shareTextToChat(SendMessageToWX.Req.WXSceneSession);
    }

    /**
     * 分享文字
     */
    public boolean shareTextToChat(int scene) {

        if (this.host == null || this.host.get() == null || TextUtils.isEmpty(text)) return false;
        int length = text.getBytes(StandardCharsets.UTF_8).length;
        if (length > MAX_TEXT_SIZE) {
            ToastUtils.showText("文本过长，超出微信分享的上限");
            return false;
        }
        Activity activity = this.host.get();

        IWXAPI api = WXAPIFactory.createWXAPI(activity.getApplicationContext(), OtherConfig.WE_CHAT_APP_ID, true);
        api.registerApp(OtherConfig.WE_CHAT_APP_ID);
        if (!api.isWXAppInstalled()) {
            ToastUtils.showText("未检测到您的微信客户端");
            return false;
        }

        WXMediaMessage msg = new WXMediaMessage();
        WXTextObject textObj = new WXTextObject();
        textObj.text = text;
        msg.mediaObject = textObj;
        msg.description = text;
        //构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = "text" + System.currentTimeMillis();
        req.message = msg;
        req.scene = scene;
//        req.openId = getOpenId();
        //调用api接口，发送数据到微信
        return api.sendReq(req);
    }

}
