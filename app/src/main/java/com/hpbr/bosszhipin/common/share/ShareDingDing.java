package com.hpbr.bosszhipin.common.share;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.dingtalk.share.ddsharemodule.DDShareApiFactory;
import com.android.dingtalk.share.ddsharemodule.IDDShareApi;
import com.android.dingtalk.share.ddsharemodule.message.DDMediaMessage;
import com.android.dingtalk.share.ddsharemodule.message.DDWebpageMessage;
import com.android.dingtalk.share.ddsharemodule.message.SendMessageToDD;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.AppShareGeekV2Request;
import net.bosszhipin.api.AppShareGeekV2Response;
import net.bosszhipin.base.ApiRequestCallback;

import java.net.URLEncoder;

/**
 * Created by zhangxiangdong on 2019/5/5 11:39.
 */
public class ShareDingDing implements IDingDingShare {

    private static final String ONLINE_APP_ID = "dingoakjqlzhxsesajt2kk";
    private static final String LOG_TAG = ShareDingDing.class.getSimpleName();

    @NonNull
    private final Context context;
    @NonNull
    private final IDDShareApi iddShareApi;
    private String url;
    private String title;
    private String content;
    private String thumbUrl;
    private ShareInfo shareInfo;

    public ShareDingDing(@NonNull Context context) {
        this.context = context;
        iddShareApi = DDShareApiFactory.createDDShareApi(context, ONLINE_APP_ID, false);
    }

    public ShareDingDing url(@NonNull String url) {
        this.url = url;
        return this;
    }

    public ShareDingDing title(@NonNull String title) {
        this.title = title;
        return this;
    }

    public ShareDingDing content(@Nullable String content) {
        this.content = content;
        return this;
    }

    public ShareDingDing thumbUrl(@Nullable String thumbUrl) {
        this.thumbUrl = thumbUrl;
        return this;
    }

    public ShareDingDing shareInfo(@Nullable ShareInfo shareInfo) {
        this.shareInfo = shareInfo;
        return this;
    }

    private void share(@Nullable String shareUrl) {
        if (LText.empty(shareUrl)) {
            ToastUtils.showText(LText.getString(R.string.string_share_failed_and_retry));
            return;
        }

        //初始化一个DDWebpageMessage并填充网页链接地址
        DDWebpageMessage webPageObject = new DDWebpageMessage();
        webPageObject.mUrl = buildSharableUrl(shareUrl);

        //构造一个DDMediaMessage对象
        DDMediaMessage webMessage = new DDMediaMessage();
        webMessage.mMediaObject = webPageObject;

        //填充网页分享必需参数
        webMessage.mTitle = title;
        webMessage.mContent = content;
        webMessage.mThumbUrl = thumbUrl;

        //构造一个Req
        SendMessageToDD.Req webReq = new SendMessageToDD.Req();
        webReq.mMediaMessage = webMessage;

        boolean shareSuccess = iddShareApi.sendReq(webReq);
        if (!shareSuccess) {
            ToastUtils.showText(LText.getString(R.string.string_share_failed_and_retry));
        }
    }

    // 判断当前设备是否已经安装钉钉
    private boolean isDingDingInstalled() {
        return iddShareApi.isDDAppInstalled();
    }

    // 判断当前设备是否支持分享到钉钉（已经安装钉钉&&钉钉版本支持分享）
    private boolean isDingDingSupportApi() {
        return iddShareApi.isDDSupportAPI();
    }

    @Nullable
    public static String buildSharableUrl(@Nullable String url) {
        String sharableUrl = null;

        if (url == null) {
            return null;
        }

        try {
            sharableUrl = String.format("dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true&_dt_no_comment=false", URLEncoder.encode(url, "UTF-8"));
            TLog.info(LOG_TAG, "Share Ding -> %s", sharableUrl);
        } catch (Exception e) {
            TLog.error(LOG_TAG, e.getMessage());
        }

        return sharableUrl;
    }

    @Override
    public void shareDingDing() {
        if (!isDingDingInstalled()) {
            ToastUtils.showText(LText.getString(R.string.string_install_dingding_first));
            return;
        }
        if (!isDingDingSupportApi()) {
            ToastUtils.showText(LText.getString(R.string.string_dingding_not_support_share));
            return;
        }

        getShareIdThenShareIfNeeded();
    }

    private void getShareIdThenShareIfNeeded() {
        if (shareInfo != null) {
            AppShareGeekV2Request request = new AppShareGeekV2Request(new ApiRequestCallback<AppShareGeekV2Response>() {

                @Override
                public void onStart() {
                    super.onStart();
                    ((BaseActivity) context).showProgressDialog();
                }

                @Override
                public void onSuccess(ApiData<AppShareGeekV2Response> data) {
                    String shareUrl = data.resp.link;
                    if (LText.empty(shareUrl)) {
                        ToastUtils.showText(LText.getString(R.string.string_share_failed_and_retry));
                    } else {
                        share(shareUrl);
                    }
                }

                @Override
                public void onComplete() {
                    ((BaseActivity) context).dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            request.expectId = shareInfo.expectId;
            request.geekId = shareInfo.geekId;
            request.suid = shareInfo.suid;
            request.securityId = shareInfo.securityId;
            request.execute();
        } else {
            // 无需请求接口获取更多信息，直接分享到钉钉
            share(url);
        }
    }

    public static class ShareInfo {

        private final String geekId;
        private final String expectId;
        private final String suid;
        private final String securityId;

        public String getGeekId() {
            return geekId;
        }

        public String getExpectId() {
            return expectId;
        }

        public String getSuid() {
            return suid;
        }

        public String getSecurityId() {
            return securityId;
        }

        private ShareInfo(Builder builder) {
            geekId = builder.geekId;
            expectId = builder.expectId;
            suid = builder.suid;
            securityId = builder.securityId;
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        public static final class Builder {
            private String geekId;
            private String expectId;
            private String suid;
            private String securityId;

            private Builder() {
            }

            public Builder geekId(String val) {
                geekId = val;
                return this;
            }

            public Builder expectId(String val) {
                expectId = val;
                return this;
            }

            public Builder suid(String val) {
                suid = val;
                return this;
            }

            public Builder securityId(String val) {
                securityId = val;
                return this;
            }

            public ShareInfo build() {
                return new ShareInfo(this);
            }
        }
    }

}
