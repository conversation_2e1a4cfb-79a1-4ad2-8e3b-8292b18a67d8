package com.hpbr.bosszhipin.common.share;

import android.graphics.Bitmap;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/9/7 10:16.
 */
public final class ShareMiniInfo {

    private String interviewId;// 613新增，分享面试
    private String encryptInterviewId;
    private String jobId;
    private String geekId;
    private String expectId;
    @SoureType
    private String sourceType;
    private String securityId;
    private String param;
    private Object extra;
    private Bitmap bitmap;

    private ShareMiniInfo(Builder builder) {
        interviewId = builder.interviewId;
        encryptInterviewId = builder.encryptInterviewId;
        jobId = builder.jobId;
        geekId = builder.geekId;
        expectId = builder.expectId;
        sourceType = builder.sourceType;
        bitmap = builder.bitmap;
        securityId = builder.securityId;
        param = builder.param;
        extra = builder.extra;
    }

    public String getInterviewId() {
        return interviewId;
    }

    public String getEncryptInterviewId() {
        return encryptInterviewId;
    }

    public String getJobId() {
        return jobId;
    }

    public String getGeekId() {
        return geekId;
    }

    public String getExpectId() {
        return expectId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public String getSecurityId() {
        return securityId;
    }

    public String getParam() {
        return param;
    }

    public Object getExtra() {
        return extra;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public @interface SoureType {
        String TYPE_BOSS_SHARE_GEEK = "2"; // Boss分享牛人
        String TYPE_SHARE_INTERVIEW = "4"; // 分享面试
        String TYPE_BOSS_UNION_SHARE = "5"; // Boss联盟分享
        String TYPE_HOME_PAGE_SHARE = "6"; // 牛人boss 分享 个人主页
        String TYPE_COMPANY_PAGE_SHARE = "7"; // 牛人分享公司主页
        String TYPE_INDUSTRY_PAGE_SHARE = "8"; //分享行业主页

        String TYPE_GEEK_SHARE_POSITION = String.valueOf(Integer.MAX_VALUE); // 牛人分享职位
        String TYPE_GEEK_SHARE_GROUP = "10";
    }

    public static final class Builder {
        private String interviewId;
        private String encryptInterviewId;
        private String jobId;
        private String geekId;
        private String expectId;
        @SoureType
        private String sourceType;
        private String securityId;
        private String param;
        private Object extra;
        private Bitmap bitmap;


        private Builder() {
        }

        public Builder setBitmap(Bitmap bitmap) {
            this.bitmap = bitmap;
            return this;
        }

        public Builder interviewId(String interviewId) {
            this.interviewId = interviewId;
            return this;
        }

        public Builder setEncryptInterviewId(String encryptInterviewId) {
            this.encryptInterviewId = encryptInterviewId;
            return this;
        }


        public Builder jobId(String jobId) {
            this.jobId = jobId;
            return this;
        }

        public Builder geekId(String geekId) {
            this.geekId = geekId;
            return this;
        }

        public Builder expectId(String expectId) {
            this.expectId = expectId;
            return this;
        }

        public Builder sourceType(@SoureType String sourceType) {
            this.sourceType = sourceType;
            return this;
        }

        public Builder securityId(String securityId) {
            this.securityId = securityId;
            return this;
        }

        public Builder param(String param) {
            this.param = param;
            return this;
        }

        public Builder extra(Object extra) {
            this.extra = extra;
            return this;
        }

        public ShareMiniInfo build() {
            return new ShareMiniInfo(this);
        }
    }

}
