package com.hpbr.bosszhipin.common.share;

import android.graphics.Bitmap;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.common.share.ShareMiniInfo.SoureType;

/**
 * Created by <PERSON><PERSON><PERSON>ng<PERSON> on 2018/6/19 14:31.
 */
public interface ShareMiniInfoProvider {

    String getInterviewId();// 613新增，分享面试

    String getEncryptInterviewId();

    String getJobId();

    String getGeekId();

    String getExpectId();

    @SoureType
    String getSourceType();

    String getSecurityId();

    String getParam();

    @Nullable
    Object getExtra();

    Bitmap getBitmap();

}
