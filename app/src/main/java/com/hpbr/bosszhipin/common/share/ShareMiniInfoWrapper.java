package com.hpbr.bosszhipin.common.share;

import android.graphics.Bitmap;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public final class ShareMiniInfoWrapper {

    private String miniAppId; // 小程序原始id
    private String link; // 小程序路径
    private String title;
    private String desc;
    private Bitmap bitmap;

    // 兼容低版本的网页链接
    private String webpageUrl;

    public String getMiniAppId() {
        return miniAppId;
    }

    public String getLink() {
        return link;
    }

    public String getTitle() {
        return title;
    }

    public String getDesc() {
        return desc;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public String getWebpageUrl() {
        return webpageUrl;
    }

    public ShareMiniInfoWrapper miniAppId(@NonNull String miniAppId) {
        this.miniAppId = miniAppId;
        return this;
    }

    public ShareMiniInfoWrapper link(@NonNull String link) {
        this.link = link;
        return this;
    }

    public ShareMiniInfoWrapper title(@NonNull String title) {
        this.title = title;
        return this;
    }

    public ShareMiniInfoWrapper desc(@Nullable String desc) {
        this.desc = desc;
        return this;
    }

    public ShareMiniInfoWrapper bitmap(@NonNull Bitmap bitmap) {
        this.bitmap = bitmap;
        return this;
    }

    public ShareMiniInfoWrapper webpageUrl(@Nullable String webpageUrl) {
        this.webpageUrl = webpageUrl;
        return this;
    }

    private ShareMiniInfoWrapper() {
    }

    public static ShareMiniInfoWrapper obj() {
        return new ShareMiniInfoWrapper();
    }

}
