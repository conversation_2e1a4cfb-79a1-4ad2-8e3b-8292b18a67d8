package com.hpbr.bosszhipin.common.share;

import android.content.Context;

import com.tencent.connect.share.QQShare;

import java.io.Serializable;

public class ShareQQ implements Serializable {

    private static final long serialVersionUID = 7418091920731430055L;
    private String qqTitle;
    private String qqDesc;
    private String qqUrl;
    private String qqImageUrl;
    private int subType;
    private int shareType = QQShare.SHARE_TO_QQ_TYPE_DEFAULT;

    public int getShareType() {
        return shareType;
    }

    public String getQqTitle() {
        return qqTitle;
    }

    public String getQqDesc() {
        return qqDesc;
    }

    public String getQqUrl() {
        return qqUrl;
    }

    public String getQqImageUrl() {
        return qqImageUrl;
    }

    public int getSubType() {
        return subType;
    }

    public ShareQQ setShareType(int shareType) {
        this.shareType = shareType;
        return this;
    }

    public ShareQQ setQqTitle(String qqTitle) {
        this.qqTitle = qqTitle;
        return this;
    }

    public ShareQQ setQqDesc(String qqDesc) {
        this.qqDesc = qqDesc;
        return this;
    }

    public ShareQQ setQqUrl(String qqUrl) {
        this.qqUrl = qqUrl;
        return this;
    }

    public ShareQQ setQqImageUrl(String qqImageUrl) {
        this.qqImageUrl = qqImageUrl;
        return this;
    }

    public ShareQQ setSubType(int subType) {
        this.subType = subType;
        return this;
    }

    public void share(Context context) {
        QQShareCallBackActivity.start(context, this);
    }

}
