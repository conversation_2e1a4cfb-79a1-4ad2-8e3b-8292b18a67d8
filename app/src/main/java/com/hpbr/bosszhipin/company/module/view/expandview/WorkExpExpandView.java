package com.hpbr.bosszhipin.company.module.view.expandview;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.bean.BaseWorkExpTopicBean;

import java.util.HashMap;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 * created by yhy
 * date 2020-09-14
 * desc:
 */
public class WorkExpExpandView extends FrameLayout {
    private final int mDefaultTopicStyleSelectKey = 1;
    private final int mDefaultTopicStyleNotSelectKey = 2;
    private final int mDefaultDescStyleKey = 3;
    private final int mDefaultTopicStyle3Key = 4;
    private final int mDefaultDescStyle2Key = 5;
    private final int mDefaultTopicStyle4Key = 6; //JD页
    boolean calculateComplete = false;
    /**
     * 最大行数 ，超过 折叠
     */
    private int mMaxLines;
    /**
     * 控件之间的padding
     */
    private int mSubViewPadding;
    /**
     * 话题的文字大小
     */
    private int mTopicTextSize;
    /**
     * 描述的文字代销
     */
    private int mDescTextSize;
    private LinearLayout mContainerLl;
    private TextView mExpandTv,mCollapseTv;
    /**
     * Ω
     * 整个控件的高度
     */
    private int mTotalHeight;
    /**
     * 根据当前行数，计算出的 需要展示的折叠的高度
     */
    private int mNeedVisibleHeight;
    private WorkExpViewFactory mTopicSelectViewFactory;
    private WorkExpViewFactory mTopicNotSelectViewFactory;
    private WorkExpViewFactory mDescViewFactory;
    private HashMap<Integer, WorkExpViewFactory> mViewFactoryMap = new HashMap<>();
    private String mFoldText = "展开";
    /**
     * 收起时候的文案
     */
    private String mCollapseText = "收起";
    private int position;
    private WorkExpViewHelper.ExpandInfo mExpandInfo;
    private OnTopicClickListener mOnTopicClickListener;
    private OnExpandClickListener mOnExpandClickListener;
    private WorkExpViewHelper mViewHelper;
    private boolean enableExpand = true;
    /**
     * 展开的文案 是否另起一行
     */
    private boolean folderTextDown ;
    /**
     * 允许收起
     */
    private boolean enableCollapse ;

    public WorkExpExpandView(@NonNull Context context) {
        this(context, null);
    }

    public WorkExpExpandView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public WorkExpExpandView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {

        registerViewFactory(context);

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.WorkExpExpandView,
                    defStyleAttr, 0);
            mMaxLines = a.getInt(R.styleable.WorkExpExpandView_max_lines, 5);
            mSubViewPadding = a.getDimensionPixelSize(R.styleable.WorkExpExpandView_lines_padding, 10);

            int foldBgColor = a.getColor(R.styleable.WorkExpExpandView_folder_bg_color, 0x00ffffff);
            mFoldText = a.getString(R.styleable.WorkExpExpandView_folder_text);
            if (TextUtils.isEmpty(mFoldText)) {
                mFoldText = "展开";
            }

            int foldTextColor = a.getColor(R.styleable.WorkExpExpandView_folder_text_color, 0xff15b3b3);
            int foldTextSize = a.getDimensionPixelSize(R.styleable.WorkExpExpandView_folder_text_size, 14); //todo 修改为dp 的size

            mTopicTextSize = a.getDimensionPixelSize(R.styleable.WorkExpExpandView_topic_text_size, 14);
            mDescTextSize = a.getDimensionPixelSize(R.styleable.WorkExpExpandView_desc_text_size, 14);

            int topicStyleSelect = a.getInt(R.styleable.WorkExpExpandView_topic_style_select, mDefaultTopicStyleSelectKey);
            mTopicSelectViewFactory = mViewFactoryMap.get(topicStyleSelect);

            int topicStyleNotSelect = a.getInt(R.styleable.WorkExpExpandView_topic_style_not_select, mDefaultTopicStyleNotSelectKey);
            mTopicNotSelectViewFactory = mViewFactoryMap.get(topicStyleNotSelect);

            int descStyle = a.getInt(R.styleable.WorkExpExpandView_topic_style_desc, mDefaultDescStyleKey);
            mDescViewFactory = mViewFactoryMap.get(descStyle);

            if (mTopicSelectViewFactory == null || mTopicNotSelectViewFactory == null || mDescViewFactory == null) {
                throw new NullPointerException("view style 没有注册");
            }

            a.recycle();
            View view = LayoutInflater.from(context).inflate(R.layout.company_view_expand, this, true);

            mContainerLl = view.findViewById(R.id.container_ll);
            mCollapseTv = view.findViewById(R.id.collapse_tv);
            mCollapseTv.setTextColor(ContextCompat.getColor(getContext(),R.color.color_FF15B3B3));
            String collText = "... " + mCollapseText;
            Spannable spanCollSpan = new SpannableString(collText);
            spanCollSpan.setSpan(new ForegroundColorSpan(mDescViewFactory.getContentEllipsizeColor(context)), 0, 3, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            mCollapseTv.setText(spanCollSpan);

            mExpandTv = view.findViewById(R.id.expand_tv);
            mExpandTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, foldTextSize);
            mExpandTv.setTextColor(foldTextColor);
            mExpandTv.setBackgroundColor(foldBgColor);
            String text = "... " + mFoldText;
            Spannable span = new SpannableString(text);
            span.setSpan(new ForegroundColorSpan(mDescViewFactory.getContentEllipsizeColor(context)), 0, 3, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            mExpandTv.setText(span);
        }
        mCollapseTv.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if(mExpandInfo!=null){
                    mCollapseTv.setVisibility(GONE);
                    doCollapseAnimate();
                }
            }
        });

        mExpandTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (enableExpand) {
                    if (mExpandInfo != null) {
                        mExpandInfo.isCollapse = false;
                        if (mViewHelper != null) {
                            mViewHelper.put(position, mExpandInfo);
                        }
                    }
                    if (mExpandInfo != null && mExpandInfo.childIndex < mContainerLl.getChildCount() && mContainerLl.getChildAt(mExpandInfo.childIndex) instanceof WorkExpTextView) {
                        WorkExpTextView collapseView = (WorkExpTextView) mContainerLl.getChildAt(mExpandInfo.childIndex);
                        collapseView.setText(collapseView.getTag().toString());
                    }
//                    view.setText(view.getTag().toString());
                    mExpandTv.setVisibility(GONE);
                    doExpandAnimate();
                }

                if (mOnExpandClickListener != null) {
                    mOnExpandClickListener.onExpand();
                }

            }
        });


    }

    private void registerViewFactory(Context context) {
        mViewFactoryMap.put(mDefaultTopicStyleSelectKey, new WorkExpViewStyle1Factory(context));
        mViewFactoryMap.put(mDefaultTopicStyleNotSelectKey, new WorkExpViewStyle2Factory(context));
        mViewFactoryMap.put(mDefaultDescStyleKey, new WorkExpViewStyleDesc1Factory(context));
        //公司主页用到的
        mViewFactoryMap.put(mDefaultTopicStyle3Key, new WorkExpViewStyle3Factory(context));
        mViewFactoryMap.put(mDefaultDescStyle2Key, new WorkExpViewStyleDesc2Factory(context));

        mViewFactoryMap.put(mDefaultTopicStyle4Key, new WorkExpViewStyle4Factory(context));
    }

    /**
     * view 之间的padding 使用 占位view。 如果使用bottomMargin的话，比如：当总view 只需要展示5行的时候，第一个view占了三行，第二个view会优先展示margin 导致剩余的2行 展示不全。
     *
     * @return
     */
    private View getPaddingView() {
        View view = new View(getContext());
//        view.setBackgroundColor(Color.parseColor("#000000"));
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, mSubViewPadding);
        view.setLayoutParams(params);
        return view;
    }

    public void setMaxLines(int maxLines) {
        mMaxLines = maxLines;
    }

    /**
     * 当展开另起一行的时候，当前那一行需要隐藏  Invisible
     */
    private WorkExpTextView mNeedInvisibleView;

    public void setData(List<BaseWorkExpTopicBean> data, int position) {
        this.position = position;
        mExpandTv.setVisibility(GONE);
        mContainerLl.removeAllViews();
        mTotalHeight = 0;
        mNeedVisibleHeight = 0;
        calculateComplete = false;
        for (int i = 0; i < data.size(); i++) {

            WorkExpTextView textView = null;
            BaseWorkExpTopicBean contentBean = data.get(i);
            if (null != contentBean && !TextUtils.isEmpty(contentBean.subTitle)) {
                if (contentBean.isMarkDown == 1) { //命中灰度 可点击需要高亮
                    textView = mTopicSelectViewFactory.getView(mTopicTextSize);
                    textView.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (mOnTopicClickListener != null) {
                                mOnTopicClickListener.onTopicClick(contentBean);
                            }
                        }
                    });

                } else {//未命中灰度 不可点击 不高亮
                    textView = mTopicNotSelectViewFactory.getView(mTopicTextSize);

                }
                String temp = contentBean.subTitle;
                temp = temp.replaceAll("#", "");
                textView.setText(temp);
                textView.setTag(temp);
                mContainerLl.addView(textView);
                if (mSubViewPadding > 0) {
                    mContainerLl.addView(getPaddingView());// 添加padding view;
                }

            }
            if (null != contentBean && !TextUtils.isEmpty(contentBean.desc)) {
                WorkExpTextView descView = mDescViewFactory.getView(mDescTextSize);
                descView.setText(contentBean.desc);
                descView.setTag(contentBean.desc);
                mContainerLl.addView(descView);
                if (mSubViewPadding > 0) {
                    mContainerLl.addView(getPaddingView());// 添加padding view;
                }
            }

        }
        if (mViewHelper != null && mViewHelper.get(position) != null) { // 已经测量过  主要用于列表复用中
            mExpandInfo = mViewHelper.get(position);
//            Log.e("aaa", position + "  setdata  " + mExpandInfo.toString());
            mTotalHeight = mExpandInfo.totalHeight;
            mNeedVisibleHeight = mExpandInfo.needVisibleHeight;
            if(folderTextDown && mExpandInfo.childIndex < mContainerLl.getChildCount() && mContainerLl.getChildAt(mExpandInfo.childIndex) instanceof WorkExpTextView){
                // 来回滑动 上一个mNeedInvisibleView 会被remove掉  这里要重新寻找
                mNeedInvisibleView = (WorkExpTextView) mContainerLl.getChildAt(mExpandInfo.childIndex);
                if(mExpandInfo.isCollapse){
                    mNeedInvisibleView.setVisibility(INVISIBLE);
                }else{
                    mNeedInvisibleView.setVisibility(VISIBLE);
                }
            }
            /*
            isCollapse moreThanMaxLine 均用于判断展开收起 ，但是分别用于 支持收起，和不支持收起。  不能用一个字段，因为不支持收起的情况下展开后  列表来回滑动
            需要自动收起。用同一个字段 状态会错误
            */
            if (mExpandInfo.isCollapse || !enableCollapse) { //已经收起 或者不支持收起的
                setViewHeight(this, mNeedVisibleHeight);

                if(!enableCollapse){//不支持收起的话， 每次来回滑动 都变成收起状态 这是原来的逻辑。
                    mExpandTv.setVisibility(mExpandInfo.moreThanMaxLine ? VISIBLE : GONE);
                }else{
                    mExpandTv.setVisibility(mExpandInfo.isCollapse ? VISIBLE : GONE);
                }

                if (mExpandInfo.childIndex < mContainerLl.getChildCount() && mContainerLl.getChildAt(mExpandInfo.childIndex) instanceof WorkExpTextView
                && mExpandTv.getVisibility()==VISIBLE) {

                    setExpandContent((WorkExpTextView) mContainerLl.getChildAt(mExpandInfo.childIndex), mExpandInfo.start, mExpandInfo.end, mExpandInfo.remainWidth);
                }
            }

            return;
        }
        mExpandInfo = new WorkExpViewHelper.ExpandInfo();
        mContainerLl.post(new Runnable() {
            @Override
            public void run() {
                if(folderTextDown){//展开另起一行的话  吧最大行数+1就可以了 然后让展开把做露出的一行遮挡
                    mMaxLines+=1;
                }
                int lineCountNum = 0;
                int currentHeight = 0; //到上个view 为止。 所有view的高度
                for (int i = 0; i < mContainerLl.getChildCount(); i++) {
                    View childView = mContainerLl.getChildAt(i);
                    mTotalHeight += childView.getHeight();

                    if (!calculateComplete) {
                        if (childView instanceof TextView) {
                            final WorkExpTextView view = (WorkExpTextView) childView;
                            if (view.isTopic) {
                                lineCountNum += 1;
                            } else {
                                lineCountNum += view.getLineCount();
                            }

                            if (lineCountNum > mMaxLines) {// 比如：lineCount = 4 + view.getLineCount = 3  ，mMaxLines =5
                                mExpandTv.setVisibility(VISIBLE);
                                int remainLineCount = mMaxLines - (lineCountNum - view.getLineCount()); //当前这个view 展示到了第几行
                                Layout layout = view.getLayout();
                                if (layout != null) {
                                    mNeedVisibleHeight += getPaddingTop() + currentHeight + view.getPaddingTop() +
                                            layout.getLineBottom(remainLineCount - 1);
                                    setViewHeight(WorkExpExpandView.this, mNeedVisibleHeight);

                                    appendExpandContent(view, remainLineCount, i);
                                    calculateComplete = true;
                                }
                            } else if (lineCountNum == mMaxLines) {// 比如：lineCount = 4 + view.getLineCount = 3  ，mMaxLines =7
                                if (i != mContainerLl.getChildCount() - 2) { //-1是paddingview
                                    mExpandTv.setVisibility(VISIBLE);
                                    // == mContainerLl.getChildCount() - 2 时候  属于刚好是最后一行
                                    Layout layout = view.getLayout();
                                    if (layout != null) {
                                        appendExpandContent(view, layout.getLineCount(), i);
                                    }
                                }
                                calculateComplete = true;
                                mNeedVisibleHeight += getPaddingTop() + currentHeight + view.getHeight();
                                setViewHeight(WorkExpExpandView.this, mNeedVisibleHeight);
                                if(folderTextDown){
                                    mNeedInvisibleView = view;
                                    mNeedInvisibleView.setVisibility(INVISIBLE);
                                }


                            } else {
                                currentHeight += view.getHeight() + mSubViewPadding; // 需要加上view之间的padding
                            }
                        }
                    }
                }

                if(enableCollapse && mContainerLl.getChildCount()>0 && mExpandTv.getVisibility()==VISIBLE){ //如果允许收起的话 最后一行的文案如果充满一行   要截断: xxxxx ...收起
                    View lastTextView =  mContainerLl.getChildAt(mContainerLl.getChildCount()-1);
                    if (lastTextView  instanceof WorkExpTextView) {
                        appendCollapseContent((WorkExpTextView) lastTextView);
                    }
                }
                mExpandInfo.totalHeight = mTotalHeight;
                mExpandInfo.needVisibleHeight = mNeedVisibleHeight;
                mExpandInfo.moreThanMaxLine = mExpandTv.getVisibility() == VISIBLE;// 用于在不支持收起的情况下判断 是否是展开状态
                mExpandInfo.isCollapse = mExpandTv.getVisibility() == VISIBLE;// 用于在支持收起的情况下判断 是否是展开状态
                if (mViewHelper != null) {
                    mViewHelper.put(position, mExpandInfo);
                }
//                Log.e("aaa", position + "  post  " + mExpandInfo.toString());
            }
        });


    }

    /**
     * 追加"展开"的文案
     *
     * @param view
     * @param remainLineCount
     */
    private void appendExpandContent(final WorkExpTextView view, int remainLineCount, int index) {
        Layout layout = view.getLayout();
        if (layout == null) return;
        int currentLine = remainLineCount - 1;//remainLineCount 从1 开始的。 这里是从0
        int end = layout.getLineEnd(currentLine);
        int start = layout.getLineStart(currentLine);
        float remainWidth = getWidth() - layout.getLineWidth(currentLine); // 最后一行的空白
        if (mExpandInfo != null) {

            mExpandInfo.remainWidth = (int) remainWidth;
            mExpandInfo.end = end;
            mExpandInfo.start = start;
            mExpandInfo.childIndex = index;
            mExpandInfo.viewWidth = getWidth();
        }
        setExpandContent(view, start, end, remainWidth);
    }

    private void setExpandContent(WorkExpTextView view, int start, int end, float remainWidth) {
        String text = view.getText().toString();
        if (end == 0 || end > text.length()) return;
        float tipWidth = view.getTextSize() * mFoldText.length() + DisplayHelper.dp2px(getContext(), 20); //查看详情文案的长度
        if (remainWidth - tipWidth > 0) {
            text = text.substring(0, end);
        } else {
            int subCount = (int) ((mExpandInfo.viewWidth - tipWidth) / view.getTextSize()); //计算大概可以放下多少个字
            if (start + subCount < end && subCount > 0) {
                text = text.substring(0, start + subCount);
            }
           /* int position = mFoldText.length() + 3;
            if (end - position > 0) {
                text = text.substring(0, end - position);
            } else {
                text = text.substring(0, end);
            }*/
        }

        if (mExpandInfo != null) {
            mExpandInfo.collapseLineText = text;
        }
        view.setText(text);
    }


    /**
     * 追加"收起"的文案
     * 目前只在jd 使用
     * @param view
     */
    private void appendCollapseContent(final WorkExpTextView view) {
        Layout layout = view.getLayout();
        if (layout == null) return;
        int currentLine = layout.getLineCount()-1;//remainLineCount 从1 开始的。 这里是从0
        int end = layout.getLineEnd(currentLine);
        int start = layout.getLineStart(currentLine);
        float remainWidth = getWidth() - layout.getLineWidth(currentLine); // 最后一行的空白

        String text = view.getText().toString();
        if (end == 0 || end > text.length()) return;
        float tipWidth = view.getTextSize() * mCollapseText.length() + DisplayHelper.dp2px(getContext(), 20); //查看详情文案的长度
        if (remainWidth - tipWidth > 0) {
            text = text.substring(0, end);
        } else {
            int subCount = (int) ((mExpandInfo.viewWidth - tipWidth) / view.getTextSize()); //计算大概可以放下多少个字
            if (start + subCount < end && subCount > 0) {
                text = text.substring(0, start + subCount);
            }
        }
        view.setText(text);
    }

    private void doExpandAnimate() {
        ValueAnimator heightAnimation = ValueAnimator.ofFloat(0f, 1f);
        heightAnimation.setDuration(200);

        heightAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if(folderTextDown && mNeedInvisibleView!=null){
                    mNeedInvisibleView.setVisibility(View.VISIBLE);
                }
                float val = (float) animation.getAnimatedValue();
                setViewHeight(WorkExpExpandView.this, (int) (mNeedVisibleHeight + (mTotalHeight - mNeedVisibleHeight) * val));
                if(val==1&&enableCollapse){
                    mCollapseTv.setVisibility(View.VISIBLE);
                }

            }
        });
        heightAnimation.start();
    }

    private void doCollapseAnimate() {
        ValueAnimator heightAnimation = ValueAnimator.ofFloat(0f, 1f);
        heightAnimation.setDuration(200);

        heightAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float val = (float) animation.getAnimatedValue();
                setViewHeight(WorkExpExpandView.this, (int) (mTotalHeight - (mTotalHeight - mNeedVisibleHeight) * val));
                if(val==1){
                    if(folderTextDown && mNeedInvisibleView!=null){
                        mNeedInvisibleView.setVisibility(View.INVISIBLE);
                    }
                    mExpandTv.setVisibility(View.VISIBLE);
                    if (mExpandInfo!=null && mExpandInfo.childIndex < mContainerLl.getChildCount() && mContainerLl.getChildAt(mExpandInfo.childIndex) instanceof WorkExpTextView) {
                        mExpandInfo.isCollapse = true;
                        WorkExpTextView view = (WorkExpTextView) mContainerLl.getChildAt(mExpandInfo.childIndex);
                        view.setText(mExpandInfo.collapseLineText);
                    }
                }

            }
        });
        heightAnimation.start();
    }

    private void setViewHeight(View view, int height) {
        final ViewGroup.LayoutParams params = view.getLayoutParams();
        params.height = height;
        view.requestLayout();
    }

    public void setOnTopicClickListener(OnTopicClickListener onTopicClickListener) {
        mOnTopicClickListener = onTopicClickListener;
    }

    public void setOnExpandClickListener(OnExpandClickListener onExpandClickListener) {
        mOnExpandClickListener = onExpandClickListener;
    }

    public void setViewHelper(WorkExpViewHelper viewHelper) {
        mViewHelper = viewHelper;
    }

    /**
     *  展开另起一行
     * @param folderTextDown
     */
    public void  setFolderTextDown(boolean folderTextDown) {
        this.folderTextDown = folderTextDown;
        FrameLayout.LayoutParams params = (LayoutParams) mExpandTv.getLayoutParams();
        params.width = LayoutParams.MATCH_PARENT;
        mExpandTv.setLayoutParams(params);
    }

    public void setEnableCollapse(boolean enableCollapse) {
        this.enableCollapse = enableCollapse;
    }

    public void setExpandEnable(boolean enableExpand) {
        this.enableExpand = enableExpand;
    }

    public void setExpandColor(int color) {
        mExpandTv.setTextColor(color);
    }

    public interface OnTopicClickListener {
        void onTopicClick(BaseWorkExpTopicBean topicBean);
    }

    public interface OnExpandClickListener {
        void onExpand();
    }

}
