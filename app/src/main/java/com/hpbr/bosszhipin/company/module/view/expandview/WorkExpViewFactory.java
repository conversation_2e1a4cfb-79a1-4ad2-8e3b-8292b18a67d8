package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;

/**
 * created by yhy
 * date 2020-09-14
 * desc:
 */
public abstract class WorkExpViewFactory {
    public abstract WorkExpTextView getView(int textSize);

    public int getContentEllipsizeColor(Context context) {
        return ContextCompat.getColor(context, R.color.color_FF15B3B3);
    }
//    /**
//     * 获取单行 行高
//     *
//     * @return
//     */
//    protected float getLineHeight(int textSize) {
//        Paint mPaint = new Paint();
//        Paint.FontMetrics fm = mPaint.getFontMetrics();
//        mPaint.setTextSize(textSize);
//        return mPaint.descent() - mPaint.ascent() + fm.bottom - fm.top;
//    }
}
