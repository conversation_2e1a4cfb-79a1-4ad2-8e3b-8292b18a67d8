package com.hpbr.bosszhipin.company.module.view.expandview;

import java.util.HashMap;

/**
 * created by yhy
 * date 2020-09-22
 * desc:
 */
public class WorkExpViewHelper {
    private HashMap<Integer, ExpandInfo> mHashMap = new HashMap<>();

    public void put(int position, ExpandInfo info) {
        mHashMap.put(position, info);
    }

    public ExpandInfo get(int position) {
        return mHashMap.get(position);
    }

    public void clear() {
        mHashMap.clear();
    }

    static class ExpandInfo {
        /*public ExpandInfo(int needVisibleHeight, int totalHeight, int remainWidth, int childIndex, int end,boolean showExpand) {
            this.needVisibleHeight = needVisibleHeight;
            this.totalHeight = totalHeight;
            this.remainWidth = remainWidth;
            this.childIndex = childIndex;
            this.showExpand = showExpand;
            this.end = end;
        }*/

        public int needVisibleHeight;
        public int totalHeight;
        public int remainWidth;
        public int end;
        public int start;
        public int childIndex;
        public int viewWidth;
        public boolean isCollapse;
        public boolean moreThanMaxLine;
        /**
         * 折叠的那一行 展示的文案。 一般这一行可能展示不全
         */
        public String collapseLineText;
        public boolean needFold;

        @Override
        public String toString() {
            return "ExpandInfo{" +
                    "needVisibleHeight=" + needVisibleHeight +
                    ", totalHeight=" + totalHeight +
                    ", remainWidth=" + remainWidth +
                    ", end=" + end +
                    ", start=" + start +
                    ", childIndex=" + childIndex +
                    ", showExpand=" + isCollapse +
                    ", needFold=" + needFold +
                    ", viewWidth=" + viewWidth +
                    '}';
        }
    }
}
