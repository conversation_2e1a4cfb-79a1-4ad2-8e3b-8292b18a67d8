package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.twl.utils.DisplayHelper;



/**
 * created by yhy
 * date 2020-09-14
 * desc: 高亮字体 高亮边框 的样式
 */
public class WorkExpViewStyle1Factory extends WorkExpViewFactory {
    private Context mContext;

    public WorkExpViewStyle1Factory(Context context) {
        mContext = context;
    }

    @Override
    public WorkExpTextView getView(int textSize) {
        WorkExpTextView textView = new WorkExpTextView(mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF0D9EA3));
        int horizonPaddingLeft = DisplayHelper.dp2px(mContext,4);
        int horizonPaddingRight = DisplayHelper.dp2px(mContext,6);
        textView.setPadding(horizonPaddingLeft, 5, horizonPaddingRight, 5);
        textView.setBackgroundResource(R.drawable.company_work_exp_topic_style_1);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        textView.setMaxLines(1);
        textView.setEllipsize(TextUtils.TruncateAt.END);
        textView.setMaxEms(15);
        textView.isTopic = true;
        textView.setLayoutParams(params);

        Drawable drawableLeft = mContext.getResources().getDrawable(
                R.drawable.tv_job_strategy_right_title_icon);

        textView.setCompoundDrawablesWithIntrinsicBounds(drawableLeft,

                null, null, null);

        textView.setCompoundDrawablePadding(horizonPaddingLeft);

        return textView;
    }

}
