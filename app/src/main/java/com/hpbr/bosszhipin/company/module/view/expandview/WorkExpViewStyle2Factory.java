package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.twl.utils.DisplayHelper;



/**
 * created by yhy
 * date 2020-09-14
 * desc: 灰色字体 灰色边框 的样式
 */
public class WorkExpViewStyle2Factory extends WorkExpViewFactory {
    private Context mContext;

    public WorkExpViewStyle2Factory(Context context) {
        mContext = context;
    }

    @Override
    public WorkExpTextView getView(int textSize) {
        WorkExpTextView textView = new WorkExpTextView(mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setTextColor(ContextCompat.getColor(mContext, com.hpbr.bosszhipin.app.R.color.color_FF5E5E5E_FF9E9EA1));
        int horizonPaddingLeft = DisplayHelper.dp2px(mContext,4);
        int horizonPaddingRight = DisplayHelper.dp2px(mContext,6);
        textView.setPadding(horizonPaddingLeft, 5, horizonPaddingRight, 5);
        textView.setMaxLines(1);
        textView.setEllipsize(TextUtils.TruncateAt.END);
        textView.setMaxEms(15);
        textView.isTopic = true;
        textView.setBackgroundResource(R.drawable.company_work_exp_topic_style_2);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        textView.setLayoutParams(params);

        Drawable drawableLeft = mContext.getResources().getDrawable(
                R.mipmap.icon_work_exp_topic_gray);

        textView.setCompoundDrawablesWithIntrinsicBounds(drawableLeft,
                null, null, null);

        textView.setCompoundDrawablePadding(horizonPaddingLeft);
        return textView;
    }

}
