package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.twl.utils.DisplayHelper;

import androidx.core.content.ContextCompat;


/**
 * created by yhy
 * date 2020-09-14
 */
public class WorkExpViewStyle3Factory extends WorkExpViewFactory {
    private Context mContext;

    public WorkExpViewStyle3Factory(Context context) {
        mContext = context;
    }

    @Override
    public WorkExpTextView getView(int textSize) {
        WorkExpTextView textView = new WorkExpTextView(mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setTextColor(ContextCompat.getColor(mContext,R.color.color_96FFFFFF));
        int horizonPadding = DisplayHelper.dp2px(mContext,10);
        textView.setPadding(horizonPadding, 5, horizonPadding, 5);
        textView.setBackgroundResource(R.drawable.company_work_exp_topic_style_3);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        textView.setMaxLines(1);
        textView.setEllipsize(TextUtils.TruncateAt.END);
        textView.setMaxEms(15);
        textView.isTopic = true;
        textView.setLayoutParams(params);

        Drawable drawableLeft = mContext.getResources().getDrawable(
                R.mipmap.icon_work_topic_com);

        textView.setCompoundDrawablesWithIntrinsicBounds(drawableLeft,
                null, null, null);

        textView.setCompoundDrawablePadding(4);
        return textView;
    }

}
