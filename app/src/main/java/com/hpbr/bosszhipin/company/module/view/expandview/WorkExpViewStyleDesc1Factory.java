package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.twl.utils.DisplayHelper;

/**
 * created by yhy
 * date 2020-09-14
 * desc:灰色字体 的样式
 */
public class WorkExpViewStyleDesc1Factory extends WorkExpViewFactory {
    private Context mContext;

    public WorkExpViewStyleDesc1Factory(Context context) {
        mContext = context;
    }

    @Override
    public WorkExpTextView getView(int textSize) {
        WorkExpTextView textView = new WorkExpTextView(mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF5E5E5E_FF9E9EA1));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        textView.setLayoutParams(params);
        textView.setLineSpacing(DisplayHelper.dp2px(mContext, 5), 1.0f);
        return textView;
    }

    @Override
    public int getContentEllipsizeColor(Context context) {
        return ContextCompat.getColor(context, R.color.color_FF5E5E5E_FF9E9EA1);
    }
}
