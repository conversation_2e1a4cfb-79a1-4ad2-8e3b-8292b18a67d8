package com.hpbr.bosszhipin.company.module.view.expandview;

import android.content.Context;
import android.util.TypedValue;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;

import androidx.core.content.ContextCompat;


/**
 * created by yhy
 * date 2020-09-14
 * desc:灰色字体 的样式
 */
public class WorkExpViewStyleDesc2Factory extends WorkExpViewFactory {
    private Context mContext;

    public WorkExpViewStyleDesc2Factory(Context context) {
        mContext = context;
    }

    @Override
    public WorkExpTextView getView(int textSize) {
        WorkExpTextView textView = new WorkExpTextView(mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textView.setTextColor(ContextCompat.getColor(mContext, R.color.color_FFFFFFFF));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
        textView.setLayoutParams(params);
        return textView;
    }

    @Override
    public int getContentEllipsizeColor(Context context) {
        return ContextCompat.getColor(mContext, R.color.color_FFFFFFFF);
    }
}
