package com.hpbr.bosszhipin.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.SystemClock;
import android.text.TextUtils;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.AbsApiRequest;
import com.twl.http.error.ErrorReason;
import com.twl.mms.ApmAction;
import com.twl.mms.common.DNS;
import com.twl.mms.service.AppStatus;
import com.twl.mms.utils.BLog;

import net.bosszhipin.api.MqttIpRequest;
import net.bosszhipin.api.MqttIpResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class CustomHttpDNS extends DNS {
    public static final String TAG = "CustomHttpDNS";
    private static final long TIME = 30;
    public static final int TYPE_RESOLVE_MQTT = 0;
    public static final int TYPE_RESOLVE_HTTP = 1;
    public static final int TYPE_RESOLVE_SSL = 2;
    public static final String SUPPORT_MQTT_IPV6_ONLY = "isSupportMqttIpv6Only";
    private List<InetSocketAddress> mHttpCache;
    private List<InetSocketAddress> mSSLMQTTCache;
    private IPWatchmen mIPWatchmen = new IPWatchmen();
    private long mIPV4TimeoutTimestamp = 0;
    private long mSuccessTimestamp = 0;
    private AtomicBoolean supportIpv6s = new AtomicBoolean(false);

    @Override
    public List<InetSocketAddress> dnsResolve(InetSocketAddress domain, boolean isSupportV6) {
        List<InetSocketAddress> result = Collections.EMPTY_LIST;
        if (domain != null && !TextUtils.isEmpty(domain.getHostName())) {
            if (domain.getPort() == HostConfig.MQTT_PORT) {
                result = resolve(TYPE_RESOLVE_MQTT);
            } else if (domain.getPort() == HostConfig.MQTT_HTTP_PORT) {//http数据会和
                List<InetSocketAddress> cache = mHttpCache;
                if (cache != null && cache.size() > 0) {
                    mHttpCache = null;
                    result = cache;
                } else {
                    result = resolve(TYPE_RESOLVE_HTTP);
                }
            } else if (domain.getPort() == HostConfig.MQTT_SSL_PORT) { // SSL
                List<InetSocketAddress> cache = mSSLMQTTCache;
                if (cache != null && cache.size() > 0) {
                    mSSLMQTTCache = null;
                    result = cache;
                } else {
                    result = resolve(TYPE_RESOLVE_SSL);
                }
            }
        }
        return result;
    }

    @Override
    public boolean isServerIp(String ip) {
        return !mIPWatchmen.isHijick(ip);
    }

    @Override
    public boolean onTimeout() {
        supportIpv6s.set(false);
        if (DNSCommon.isSupportIpV6()) {
            boolean onlyIpv6 = isOnlyIpv6() || DNSCommon.isNativeSupportIpV6();
            if (onlyIpv6) {
                if (AppStatus.gIsOnline) {
                    supportIpv6s.set(true);
                    ApmAnalyzer.create().action(ApmAction.ACTION_MQTT, ApmAction.TYPE_ONLY_SUPPORT_IPV6)
                            .p2(String.valueOf(AppStatus.isForeground()))
                            .p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi")
                            .report();
                }
            }else if (!AppStatus.gIsMobileNet){
                long startTime = System.currentTimeMillis();
                final boolean ipv6Only = DNSCommon.retryCheckIpv6Only();
                long time = System.currentTimeMillis() - startTime;
                ApmAnalyzer.create().action(ApmAction.ACTION_MQTT, ApmAction.TYPE_CHECK_ONLY_SUPPORT_IPV6)
                        .p2(String.valueOf(AppStatus.isForeground()))
                        .p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi")
                        .p4(String.valueOf(time))
                        .p5(String.valueOf(ipv6Only))
                        .report();
                return ipv6Only;
            }
        }
        return false;
    }

    private boolean isIpv6Report = true;
    @Override
    public void onConnectSuccess(InetAddress address) {
        if (isIpv6Report && address instanceof Inet6Address) {
            ApmAnalyzer.create().action(ApmAction.ACTION_MQTT,
                            ApmAction.TYPE_IPV6_SUCCESS).
                    p2(String.valueOf(AppStatus.isForeground())).
                    p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi").
                    report();
            isIpv6Report = false;
        }
    }

    /**
     * 判断是否仅支持IPV6
     *
     * @return
     */
    private boolean isOnlyIpv6() {
        long elapsedTime = mSuccessTimestamp - mIPV4TimeoutTimestamp;
        //ipv4请求失败，且两分钟内ipv6可用的情况下成功，认为当前网络仅仅支持ipv6
        if (elapsedTime > 0 && elapsedTime < 2 * 60 * 1000) {
            return true;
        }
        return DNSCommon.isNativeSupportIpV6();
    }

    private List<InetSocketAddress> resolve(final int resolveType) {
        final List<InetSocketAddress> list = new ArrayList<>(0);
        if (AccountHelper.getUid() > 0) {
            final RequestLockWrapper lockWrapper = new RequestLockWrapper(TIME);
            MqttIpRequest request = new MqttIpRequest(new ApiRequestCallback<MqttIpResponse>() {
                @Override
                public void handleInChildThread(ApiData<MqttIpResponse> data) {
                    super.handleInChildThread(data);
                    if (resolveType == TYPE_RESOLVE_HTTP) {
                        if (!LList.isEmpty(data.resp.mqttHttpIps)) {
                            Collections.shuffle(data.resp.mqttHttpIps);
                            list.addAll(parseIPList(data.resp.mqttHttpIps));
                        }
                    } else {
                        if (resolveType == TYPE_RESOLVE_SSL) {
                            if (!LList.isEmpty(data.resp.tlsIpv4List)) {
                                Collections.shuffle(data.resp.tlsIpv4List);
                                list.addAll(parseIPList(data.resp.tlsIpv4List));
                            }
                            if (isNeedSupportIpv6() && !LList.isEmpty(data.resp.tlsIpv6List)) {
                                Collections.shuffle(data.resp.tlsIpv6List);
                                if (MqttConfig.isSupportIpv6()) { //是否强制使用Ipv6 、连接失败时 或 执行  removeIP  导致 长度变化了， 但是 getIP(index) index 没有变 导致每次跳过一个IP
                                    list.addAll(0, parseIPList(data.resp.tlsIpv6List));
                                }else {
                                    addIPv6List(parseIPList(data.resp.tlsIpv6List), list);
                                }
                            }
                            mSSLMQTTCache = list;
                        } else {
                            List<String> ipWatchMen = new ArrayList<>();
                            if (data.resp.mqttIps != null) {
                                Collections.shuffle(data.resp.mqttIps);
                                list.addAll(parseIPList(data.resp.mqttIps));
                                ipWatchMen.addAll(data.resp.mqttIps);
                            }
                            if (isNeedSupportIpv6() && !LList.isEmpty(data.resp.mqttIpv6s)) {
                                Collections.shuffle(data.resp.mqttIpv6s);
                                if (MqttConfig.isSupportIpv6()) { //是否强制使用Ipv6 、连接失败时 或 执行  removeIP  导致 长度变化了， 但是 getIP(index) index 没有变 导致每次跳过一个IP
                                    list.addAll(0, parseIPList(data.resp.mqttIpv6s));
                                }else {
                                    addIPv6List(parseIPList(data.resp.mqttIpv6s), list);
                                }
                                ipWatchMen.addAll(data.resp.mqttIpv6s);
                            }
                            mIPWatchmen.saveIpList(ipWatchMen);
                        }

                        //HTTP
                        if (data.resp.mqttHttpIps != null) {
                            Collections.shuffle(data.resp.mqttHttpIps);
                            mHttpCache = parseIPList(data.resp.mqttHttpIps);
                        }
                    }
                    lockWrapper.signalAll();
                    if (DNSCommon.isSupportIpV6()) {
                        mSuccessTimestamp = System.currentTimeMillis();
                    }
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    if (AppStatus.gIsOnline) {
                        ApmAnalyzer.create().action(ApmAction.ACTION_MQTT,
                                ApmAction.TYPE_DNS_HTTP_FAILED).
                                p2(String.valueOf(AppStatus.isForeground())).
                                p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi").
                                p4(reason.toString()).report();
                    }
                    lockWrapper.signalAll();

                    Exception exception = reason.getException();
                    if (exception instanceof java.net.ConnectException) {
                        String message = exception.getMessage();
                        if (message != null && message.indexOf('.') > 0) {
                            mIPV4TimeoutTimestamp = System.currentTimeMillis();
                        }
                    }
                }

                @Override
                public void onSuccess(ApiData<MqttIpResponse> data) {

                }

                @Override
                public void onComplete() {
                    lockWrapper.signalAll();
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            lockWrapper.await(request);
        }

        return list;
    }

    //IPV4 和 IPV6 间隔
    private int ipv6PriorityCount = -1;

    private void addIPv6List(List<InetSocketAddress> ipv6List, List<InetSocketAddress> list) {
        if (LList.isEmpty(ipv6List)) return;
        if (ipv6PriorityCount == -1) { //读取配置 默认 2
            ipv6PriorityCount = MqttConfig.isSupportIpv6Split();
        }
        if (ipv6PriorityCount <= 0) return;
        int insertIndex = Math.min(list.size(), ipv6PriorityCount);
        if (ipv6List.size() > ipv6PriorityCount) {
            list.addAll(insertIndex, ipv6List.subList(0, ipv6PriorityCount));
            list.addAll(ipv6List.subList(ipv6PriorityCount, ipv6List.size()));
        } else {
            list.addAll(insertIndex, ipv6List);
        }
    }

    private boolean isNeedSupportIpv6() {
        if (supportIpv6s.get()) {
            return true;
        }

        if (DNSCommon.isSupportIpV6()) { // 接口探测 是否支持Ipv6
            return true;
        }

        TLog.info(TAG, " network check not SupportIpv6");
//        if (MqttConfig.isSupportIpv6()) { //是否使用Ipv6 观星台控制
//            return true;
//        }

        final boolean isSupportMqttIpv6Only = SpManager.get().global().getInt(CustomHttpDNS.SUPPORT_MQTT_IPV6_ONLY, 1) == 1;
        if (isSupportMqttIpv6Only) {
            return DNSCommon.isNativeSupportIpV6();
        }
        return false;
    }

    private static class RequestLockWrapper {
        private static final String TAG = "RequestLockWrapper";

        private Lock mLock = new ReentrantLock();
        private Condition mCondition = mLock.newCondition();

        private long mWaitTime;

        public RequestLockWrapper(long waitTime) {
            mWaitTime = waitTime;
        }

        public void await(AbsApiRequest apiRequest){
            long time = SystemClock.elapsedRealtime();
            try {
                if (mLock.tryLock(mWaitTime, TimeUnit.SECONDS)) {
                    try {
                        HttpExecutor.execute(apiRequest);
                        mCondition.await(mWaitTime, TimeUnit.SECONDS);
                        long usedTime = SystemClock.elapsedRealtime() - time;
                        BLog.d(TAG, "await() called with: wait time = [%d]", usedTime);
                        if (usedTime > 10000) {
                            ApmAnalyzer.create().action(ApmAction.ACTION_MQTT,
                                            ApmAction.TYPE_DNS_TOO_LONG).
                                    p2(String.valueOf(usedTime/10000)).
                                    p3(String.valueOf(usedTime)).
                                    report();
                        }
                    } finally {
                        mLock.unlock();
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        public void signalAll(){
            try {
                if (mLock.tryLock(mWaitTime, TimeUnit.SECONDS)) {
                    try {
                        mCondition.signalAll();
                    } finally {
                        mLock.unlock();
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    private static class IPWatchmen {
        private static final String SP_NAME = "ip_watchmen";
        private static final String KEY = "ip_list";
        private List<String> mIpList;

        public boolean isHijick(String ip) {
            List<String> list = getIpList();
            if (list == null || list.isEmpty()) {
                return false;
            }
            return !list.contains(ip);
        }

        private List<String> getIpList() {
            if (mIpList == null) {
                String ipStr = getSharedPreferences().getString(KEY, "");
                if (!TextUtils.isEmpty(ipStr)) {
                    String[] ips = ipStr.split(",");
                    mIpList = new ArrayList<>(ips.length);
                    for (String ip : ips) {
                        mIpList.add(ip);
                    }
                } else {
                    mIpList = Collections.EMPTY_LIST;
                }
            }
            return mIpList;
        }

        public void saveIpList(List<String> ipList) {
            try {
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < ipList.size(); ) {
                    stringBuilder.append(ipList.get(i++).split(":")[0]);
                    if (i < ipList.size()) {
                        stringBuilder.append(',');
                    }
                }
                getSharedPreferences().edit().putString(KEY, stringBuilder.toString()).commit();
            } catch (Throwable exception) {
                CrashReport.postCatchedException(exception);
            }
        }

        private SharedPreferences getSharedPreferences() {
            SharedPreferences sharedPreferences = App.get().getSharedPreferences(SP_NAME, Context.MODE_PRIVATE | Context.MODE_MULTI_PROCESS);
            return sharedPreferences;
        }
    }

    private static List<InetSocketAddress> parseIPList(List<String> ips){
        List<InetSocketAddress> addressList = Collections.EMPTY_LIST;
        if (ips != null && ips.size() > 0) {
            addressList = new ArrayList<>(ips.size());
            for (String ip : ips) {
                InetSocketAddress inetSocketAddress = strIP2InetSocketAddress(ip);
                if (inetSocketAddress != null) addressList.add(inetSocketAddress);
            }
        }
        return addressList;
    }

    private static InetSocketAddress strIP2InetSocketAddress(String strIP){
        InetSocketAddress socketAddress = null;
        try {
            String[] ipStringSplit = strIP.split(":");
            String ip;
            String port;
            if (ipStringSplit.length > 2) {
                port = ipStringSplit[ipStringSplit.length-1];
                ip = strIP.substring(0,strIP.length() - (port.length() + 1));
                socketAddress  = new InetSocketAddress(InetAddress.getByName(ip), Integer.valueOf(port));
            }else{
                ip = ipStringSplit[0];
                port = ipStringSplit[1];
                byte[] addr = ipStrToBytes(ip);
                if (addr != null) {
                    //使用ip作为host，避免无必要的DNS的反向解析(10s超时)
                    socketAddress = new InetSocketAddress(InetAddress.getByAddress(ip, addr), Integer.valueOf(port));
                }
            }
        }catch (Exception e){

        }
        return socketAddress;
    }

    private static byte[] ipStrToBytes(String ip) {
        byte[] ret = null;
        String[] s = ip.split("\\.");
        switch (s.length) {
            case 4:
            case 8:
                ret = new byte[s.length];
                break;
        }
        if (ret != null) {
            for (int i = 0; i < ret.length; i++) {
                ret[i] = (byte) Integer.parseInt(s[i], 10);
            }
        }
        return ret;
    }
}
