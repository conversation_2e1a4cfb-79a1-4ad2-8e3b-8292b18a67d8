package com.hpbr.bosszhipin.config;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.SystemClock;
import android.text.TextUtils;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.client.AbsApiRequest;
import com.twl.http.error.ErrorReason;
import com.twl.mms.ApmAction;
import com.twl.mms.common.DNS;
import com.twl.mms.service.AppStatus;
import com.twl.mms.utils.BLog;
import com.twl.utils.NetworkUtils;

import net.bosszhipin.api.MqttIpRequest;
import net.bosszhipin.api.MqttIpResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class CustomHttpDNSV2 extends DNS {
    public static final String TAG = "CustomHttpDNSV2";

    private static final long TIME = 30;
    public static final int TYPE_RESOLVE_MQTT = 0;
    public static final int TYPE_RESOLVE_HTTP = 1;
    public static final int TYPE_RESOLVE_SSL = 2;
    public static final String SUPPORT_MQTT_IPV6_ONLY = "isSupportMqttIpv6Only";
    private final IPWatchmen mIPWatchmen = new IPWatchmen();
    private long mIPV4TimeoutTimestamp = 0;
    private long mSuccessTimestamp = 0;

    private IPCacheData ipCacheData;

    @Override
    public List<InetSocketAddress> dnsResolve(InetSocketAddress domain, boolean isSupportV6) {
        List<InetSocketAddress> result = Collections.EMPTY_LIST;
        if (domain != null && !TextUtils.isEmpty(domain.getHostName())) {
            return resolveCache(domain.getPort());
        }
        return result;
    }

    private List<InetSocketAddress> resolveCache(int port) {
        int resolveType;
        if (port == HostConfig.MQTT_PORT) { //TCP
            resolveType = TYPE_RESOLVE_MQTT;
        } else if (port == HostConfig.MQTT_SSL_PORT) { // SSL
            resolveType = TYPE_RESOLVE_SSL;
        } else if (port == HostConfig.MQTT_HTTP_PORT) {//http
            resolveType = TYPE_RESOLVE_HTTP;
        } else {
            return Collections.EMPTY_LIST;
        }
        List<InetSocketAddress> cache = getCacheIP(resolveType);
        TLog.info(TAG, "dnsResolve resolveType = %s  cache = %s", resolveType, LList.getCount(cache));
        if (LList.isNotEmpty(cache)) {
            return cache;
        }
        return resolve(resolveType);
    }

    @Override
    public boolean isServerIp(String ip) {
        return !mIPWatchmen.isHijick(ip);
    }

    @Override
    public boolean onTimeout() {
        ipCacheData = null;
        if (DNSCommon.isSupportIpV6()) {
            ApmAnalyzer.create().action(ApmAction.ACTION_MQTT, ApmAction.TYPE_CHECK_SUPPORT_IPV6)
                    .p2(String.valueOf(AppStatus.isForeground()))
                    .p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi")
                    .p4(String.valueOf(isOnlyIpv6()))
                    .p5(String.valueOf(DNSCommon.isSupportIpV6()))
                    .p6(String.valueOf(DNSCommon.isNativeSupportIpV6()))
                    .report();
        }
        return isNeedSupportIpv6(); //需要和 生成IP 获取逻辑统一   Ipv6变化了，需要重置IP
    }

    @Override
    public boolean changeRefershDNS() {
        return ipCacheData != null;
    }

    @Override
    public boolean supportIpv6() {
        return isNeedSupportIpv6() || NetworkUtils.isSupportOnlyIPv6(Utils.getApp()); //需要和 生成IP 获取逻辑统一   Ipv6变化了，需要重置IP
    }

    private boolean isIpv6Report = true;

    @Override
    public void onConnectSuccess(InetAddress address) {
        if (isIpv6Report) {
            if (!AndroidDataStarGray.getInstance().getMqttConfig().ignore_ipv6_connect_success && address instanceof Inet6Address) {
                ApmAnalyzer.create().action(ApmAction.ACTION_MQTT, ApmAction.TYPE_IPV6_SUCCESS)
                        .p2(String.valueOf(AppStatus.isForeground()))
                        .p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi")
                        .p4(String.valueOf(DNSCommon.isSupportIpV6()))
                        .report();
            }

            String p2 = null;
            if (isHandleIpv6Priority()) { //执行了IPv6优先
                if (address instanceof Inet4Address) {
                    p2 = "IPv4";
                }
            }else if (address instanceof Inet6Address){
                p2 = "IPv6";
            }
            if (!TextUtils.isEmpty(p2)) {
                ApmAnalyzer.create().action(ApmAction.ACTION_MQTT, ApmAction.TYPE_DIFF_SUCCESS)
                        .p2(p2)
                        .p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi")
                        .p4(String.valueOf(DNSCommon.isSupportIpV6()))
                        .p6(String.valueOf(AppStatus.isForeground()))
                        .report();
            }

            isIpv6Report = false;
        }
    }

    private boolean isHandleIpv6Priority() {
        IPCacheData ipDataTmp = ipCacheData;
        if (ipDataTmp != null) {
            return ipDataTmp.isHandleIpv6Priority;
        }
        return false;
    }
    /**
     * 判断是否仅支持IPV6
     *
     * @return
     */
    private boolean isOnlyIpv6() {
        long elapsedTime = mSuccessTimestamp - mIPV4TimeoutTimestamp;
        //ipv4请求失败，且两分钟内ipv6可用的情况下成功，认为当前网络仅仅支持ipv6
        if (elapsedTime > 0 && elapsedTime < 2 * 60 * 1000) {
            return true;
        }
        return DNSCommon.isNativeSupportIpV6();
    }

    private List<InetSocketAddress> resolve(final int resolveType) {
        final List<InetSocketAddress> list = new ArrayList<>(0);
        if (AccountHelper.getUid() > 0) {
            final RequestLockWrapper lockWrapper = new RequestLockWrapper(TIME);
            TLog.info(TAG, "request MqttIpRequest resolveType = %s isSupportIpV6 =%s", resolveType, DNSCommon.isSupportIpV6());
            MqttIpRequest request = new MqttIpRequest(new ApiRequestCallback<MqttIpResponse>() {
                @Override
                public void handleInChildThread(ApiData<MqttIpResponse> data) {
                    super.handleInChildThread(data);
                    ipCacheData = new IPCacheData(data.resp);
                    list.addAll(getCacheIP(resolveType));
                    lockWrapper.signalAll();
                    if (DNSCommon.isSupportIpV6()) {
                        mSuccessTimestamp = System.currentTimeMillis();
                    }
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    if (AppStatus.gIsOnline) {
                        ApmAnalyzer.create().action(ApmAction.ACTION_MQTT,
                                        ApmAction.TYPE_DNS_HTTP_FAILED).
                                p2(String.valueOf(AppStatus.isForeground())).
                                p3(AppStatus.gIsMobileNet ? "Mobile" : "WiFi").
                                p4(reason.toString()).report();
                    }
                    lockWrapper.signalAll();

                    Exception exception = reason.getException();
                    if (exception instanceof java.net.ConnectException) {
                        String message = exception.getMessage();
                        if (message != null && message.indexOf('.') > 0) {
                            mIPV4TimeoutTimestamp = System.currentTimeMillis();
                        }
                    }
                }

                @Override
                public void onSuccess(ApiData<MqttIpResponse> data) {

                }

                @Override
                public void onComplete() {
                    lockWrapper.signalAll();
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            lockWrapper.await(request);
        }

        return list;
    }

    private List<InetSocketAddress> getCacheIP(int resolveType) {
        IPCacheData ipDataTmp = ipCacheData;
        if (ipDataTmp != null) {
            return ipDataTmp.getResolveTypeIP(resolveType);
        }
        return new ArrayList<>(0);
    }

    private boolean isNeedSupportIpv6() {
        if (DNSCommon.isSupportIpV6()) { // 接口探测 是否支持Ipv6
            return true;
        }

        TLog.info(TAG, " network check not SupportIpv6");
//        if (MqttConfig.isSupportIpv6()) { //是否使用Ipv6 观星台控制
//            return true;
//        }

        final boolean isSupportMqttIpv6Only = SpManager.get().global().getInt(SUPPORT_MQTT_IPV6_ONLY, 1) == 1;
        if (isSupportMqttIpv6Only) {
            return DNSCommon.isNativeSupportIpV6();
        }
        return false;
    }

    private static class RequestLockWrapper {
        private static final String TAG = "RequestLockWrapper";

        private Lock mLock = new ReentrantLock();
        private Condition mCondition = mLock.newCondition();

        private long mWaitTime;

        public RequestLockWrapper(long waitTime) {
            mWaitTime = waitTime;
        }

        public void await(AbsApiRequest apiRequest) {
            long time = SystemClock.elapsedRealtime();
            try {
                if (mLock.tryLock(mWaitTime, TimeUnit.SECONDS)) {
                    try {
                        HttpExecutor.execute(apiRequest);
                        mCondition.await(mWaitTime, TimeUnit.SECONDS);
                        long usedTime = SystemClock.elapsedRealtime() - time;
                        BLog.d(TAG, "await() called with: wait time = [%d]", usedTime);
                        if (usedTime > 10000) {
                            ApmAnalyzer.create().action(ApmAction.ACTION_MQTT,
                                            ApmAction.TYPE_DNS_TOO_LONG).
                                    p2(String.valueOf(usedTime / 10000)).
                                    p3(String.valueOf(usedTime)).
                                    report();
                        }
                    } finally {
                        mLock.unlock();
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        public void signalAll() {
            try {
                if (mLock.tryLock(mWaitTime, TimeUnit.SECONDS)) {
                    try {
                        mCondition.signalAll();
                    } finally {
                        mLock.unlock();
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    private static class IPWatchmen {
        private static final String SP_NAME = "ip_watchmen";
        private static final String KEY = "ip_list";
        private List<String> mIpList;

        public boolean isHijick(String ip) {
            List<String> list = getIpList();
            if (list == null || list.isEmpty()) {
                return false;
            }
            return !list.contains(ip);
        }

        private List<String> getIpList() {
            if (mIpList == null) {
                String ipStr = getSharedPreferences().getString(KEY, "");
                if (!TextUtils.isEmpty(ipStr)) {
                    String[] ips = ipStr.split(",");
                    mIpList = new ArrayList<>(ips.length);
                    for (String ip : ips) {
                        mIpList.add(ip);
                    }
                } else {
                    mIpList = Collections.EMPTY_LIST;
                }
            }
            return mIpList;
        }

        public void saveIpList(List<String> ipList) {
            try {
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < ipList.size(); ) {
                    stringBuilder.append(ipList.get(i++).split(":")[0]);
                    if (i < ipList.size()) {
                        stringBuilder.append(',');
                    }
                }
                getSharedPreferences().edit().putString(KEY, stringBuilder.toString()).commit();
            } catch (Throwable exception) {
                CrashReport.postCatchedException(exception);
            }
        }

        private SharedPreferences getSharedPreferences() {
            SharedPreferences sharedPreferences = App.get().getSharedPreferences(SP_NAME, Context.MODE_PRIVATE | Context.MODE_MULTI_PROCESS);
            return sharedPreferences;
        }
    }

    private static List<InetSocketAddress> parseIPList(List<String> ips) {
        List<InetSocketAddress> addressList = Collections.EMPTY_LIST;
        if (ips != null && ips.size() > 0) {
            addressList = new ArrayList<>(ips.size());
            for (String ip : ips) {
                InetSocketAddress inetSocketAddress = strIP2InetSocketAddress(ip);
                if (inetSocketAddress != null) addressList.add(inetSocketAddress);
            }
        }
        return addressList;
    }

    private static InetSocketAddress strIP2InetSocketAddress(String strIP) {
        InetSocketAddress socketAddress = null;
        try {
            String[] ipStringSplit = strIP.split(":");
            String ip;
            String port;
            if (ipStringSplit.length > 2) {
                port = ipStringSplit[ipStringSplit.length - 1];
                ip = strIP.substring(0, strIP.length() - (port.length() + 1));
                if (AndroidDataStarGray.getInstance().getMqttConfig().isIPv6ConnectOptimize) {
                    byte[] addr = IPAddressParser.parseIPv6(ip);
                    BLog.d(TAG, "strIP2InetSocketAddress: %s %s %s", strIP, ip, addr);
                    if (addr != null) {
                        socketAddress = new InetSocketAddress(InetAddress.getByAddress(ip, addr), Integer.valueOf(port));
                    } else {
                        socketAddress = new InetSocketAddress(InetAddress.getByName(ip), Integer.valueOf(port));
                    }
                } else {
                    socketAddress = new InetSocketAddress(InetAddress.getByName(ip), Integer.valueOf(port));
                }
            } else {
                ip = ipStringSplit[0];
                port = ipStringSplit[1];
                byte[] addr = ipStrToBytes(ip);
                if (addr != null) {
                    //使用ip作为host，避免无必要的DNS的反向解析(10s超时)
                    socketAddress = new InetSocketAddress(InetAddress.getByAddress(ip, addr), Integer.valueOf(port));
                }
            }
        } catch (Exception e) {
            BLog.e(TAG, "strIP2InetSocketAddress: ", e);
        }
        return socketAddress;
    }

    private static byte[] ipStrToBytes(String ip) {
        byte[] ret = null;
        String[] s = ip.split("\\.");
        switch (s.length) {
            case 4:
            case 8:
                ret = new byte[s.length];
                break;
        }
        if (ret != null) {
            for (int i = 0; i < ret.length; i++) {
                ret[i] = (byte) Integer.parseInt(s[i], 10);
            }
        }
        return ret;
    }

    class IPCacheData {
        public List<InetSocketAddress> mqttHttpIps = new ArrayList<>();
        public List<InetSocketAddress> mqttIps = new ArrayList<>();
        public List<InetSocketAddress> mqttIpv6s = new ArrayList<>();
        public List<InetSocketAddress> mqttTlsIps = new ArrayList<>();
        public List<InetSocketAddress> mqttTlsIpv6s = new ArrayList<>();
        private final boolean ipv6Priority; //是否优先使用Ipv6

        private final int ipv6PriorityCount; //灰度控制 默认 2个

        private boolean isHandleIpv6Priority = false; //是否执行了 Ipv6优先
        public IPCacheData(MqttIpResponse resp) {
            parseIP(resp);
            boolean disableIpv6 = MqttConfig.isDisableIpv6();
            if (disableIpv6) {
                ipv6Priority = false;
            }else { //优先使用IPv6
                ipv6Priority = resp.ipv6Priority;
            }
            //IPV4 和 IPV6 间隔
            ipv6PriorityCount = MqttConfig.isSupportIpv6Split();
            BLog.d(TAG, "ipv6Priority:%b  %b ipv6PriorityCount:%d", ipv6Priority, disableIpv6, ipv6PriorityCount);
        }

        public boolean isIpv6Priority() {
            return ipv6Priority;
        }

        void parseIP(MqttIpResponse mqttIpResponse) {
            List<String> ipWatchMen = new ArrayList<>();
            if (!LList.isEmpty(mqttIpResponse.mqttHttpIps)) {
                mqttHttpIps.addAll(parseIPList(mqttIpResponse.mqttHttpIps));
            }

            if (!LList.isEmpty(mqttIpResponse.mqttIps)) {
                mqttIps.addAll(parseIPList(mqttIpResponse.mqttIps));
                ipWatchMen.addAll(mqttIpResponse.mqttIps);
            }

            if (!LList.isEmpty(mqttIpResponse.mqttIpv6s)) {
                mqttIpv6s.addAll(parseIPList(mqttIpResponse.mqttIpv6s));
                ipWatchMen.addAll(mqttIpResponse.mqttIpv6s);
            }

            if (!LList.isEmpty(mqttIpResponse.tlsIpv4List)) {
                mqttTlsIps.addAll(parseIPList(mqttIpResponse.tlsIpv4List));
                ipWatchMen.addAll(mqttIpResponse.tlsIpv4List);
            }

            if (!LList.isEmpty(mqttIpResponse.tlsIpv6List)) {
                mqttTlsIpv6s.addAll(parseIPList(mqttIpResponse.tlsIpv6List));
                ipWatchMen.addAll(mqttIpResponse.tlsIpv6List);
            }
            mIPWatchmen.saveIpList(ipWatchMen);
        }

        void reset() {
            mqttHttpIps.clear();
            mqttIps.clear();
            mqttIpv6s.clear();
            mqttTlsIps.clear();
            mqttTlsIpv6s.clear();
        }

        public List<InetSocketAddress> getResolveTypeIP(int resolveType) {
            final List<InetSocketAddress> list = new ArrayList<>(0);
            if (resolveType == TYPE_RESOLVE_HTTP) {
                if (!LList.isEmpty(mqttHttpIps)) {
                    Collections.shuffle(mqttHttpIps);
                    list.addAll(mqttHttpIps);
                }
            } else {
                if (resolveType == TYPE_RESOLVE_SSL) {
                    if (!LList.isEmpty(mqttTlsIps)) {
                        Collections.shuffle(mqttTlsIps);
                        list.addAll(mqttTlsIps);
                    }
                    //处理IPV6
                    addIPv6Address(list, mqttTlsIpv6s);
                } else {
                    if (!LList.isEmpty(mqttIps)) {
                        Collections.shuffle(mqttIps);
                        list.addAll(mqttIps);
                    }
                    //处理IPV6
                    addIPv6Address(list, mqttIpv6s);
                }
            }
            TLog.info(TAG, "  resolveType = %s isSupportIpV6 = %s isHandleIpv6Priority = %b", resolveType, DNSCommon.isSupportIpV6(), isHandleIpv6Priority);
            return list;
        }

        private void addIPv6Address(List<InetSocketAddress> list, List<InetSocketAddress> mqttIpv6s) {
            if (isNeedSupportIpv6() && !LList.isEmpty(mqttIpv6s) && ipv6PriorityCount > 0) {
                Collections.shuffle(mqttIpv6s);
                // 分为两部分处理 IPv6 地址
                List<InetSocketAddress> priorityIpv6List;
                List<InetSocketAddress> fallbackIpv6List;

                // 拆分 IPv6 地址为优先部分和兜底部分
                if (mqttIpv6s.size() > ipv6PriorityCount) {
                    priorityIpv6List = mqttIpv6s.subList(0, ipv6PriorityCount); // 优先部分
                    fallbackIpv6List = mqttIpv6s.subList(ipv6PriorityCount, mqttIpv6s.size()); // 兜底部分
                } else {
                    priorityIpv6List = mqttIpv6s; // 全部作为优先部分
                    fallbackIpv6List = new ArrayList<>(); // 无兜底部分
                }

                // 根据是否优先 IPv6 确定优先部分的插入位置
                if (ipv6Priority) {
                    list.addAll(0, priorityIpv6List); // IPv6 优先时，插入头部
                } else {
                    int insertPosition = Math.min(ipv6PriorityCount, list.size());
                    list.addAll(insertPosition, priorityIpv6List); // 非优先时，插入特定位置
                }

                // 确定兜底部分的插入位置
                if (LList.isNotEmpty(fallbackIpv6List)) {
                    int fallbackInsertPosition = Math.min(ipv6PriorityCount * 2, list.size());
                    list.addAll(fallbackInsertPosition, fallbackIpv6List); // 将兜底部分插入到目标位置
                }
                isHandleIpv6Priority = ipv6Priority;
            } else {
                isHandleIpv6Priority = false;
            }
        }
    }
}
