package com.hpbr.bosszhipin.config;

import com.twl.mms.utils.BLog;

import java.util.Arrays;

public class IPAddressParser {
    /**
     * 解析IPv4地址
     */
    public static byte[] parseIPv4(String ip) {
        byte[] ret = null;
        String[] s = ip.split("\\.");
        switch (s.length) {
            case 4:
            case 8:
                ret = new byte[s.length];
                break;
        }
        if (ret != null) {
            for (int i = 0; i < ret.length; i++) {
                ret[i] = (byte) Integer.parseInt(s[i], 10);
            }
        }
        return ret;
    }

    /**
     * 解析IPv6地址
     */
    public static byte[] parseIPv6(String ip) {
        // 空值检查
        if (ip == null || ip.isEmpty()) {
            BLog.e("IPAddressParser", "IPv6地址为空");
            return null;
        }
        
        try {
            // 检查是否为标准IPv6地址格式
            if (ip.contains(":::")) {
                BLog.e("IPAddressParser", "IPv6地址包含无效的连续冒号");
                return null;
            }
            
            byte[] bytes = new byte[16];
            int index = 0;
            int compressIndex = -1;
            
            // 检查是否有压缩表示法 "::"
            int compressionPos = ip.indexOf("::");
            boolean hasCompression = compressionPos >= 0;
            
            // 计算段数以确定需要展开的零的数量
            String normalizedIP = ip;
            if (hasCompression) {
                // 标准化地址，将"::"替换为一个特殊标记":ZZZ:"用于分割
                normalizedIP = ip.replace("::", ":ZZZ:");
            }
            
            // 分割IP地址
            String[] parts = normalizedIP.split(":");
            
            // 计算实际段数和需要填充的零段数
            int actualSegments = 0;
            for (String part : parts) {
                if (!part.isEmpty() && !part.equals("ZZZ")) {
                    actualSegments++;
                }
            }
            
            // IPv6地址应该有8段
            int segmentsToFill = 8 - actualSegments;
            if (segmentsToFill < 0) {
                BLog.e("IPAddressParser", "IPv6地址段数过多: " + parts.length);
                return null;
            }
            
            // 处理各个段
            for (int i = 0; i < parts.length; i++) {
                String part = parts[i];
                
                // 处理压缩标记
                if ("ZZZ".equals(part)) {
                    compressIndex = index;
                    // 在压缩位置填充对应数量的零
                    for (int j = 0; j < segmentsToFill * 2; j++) {
                        bytes[index++] = 0;
                    }
                    continue;
                }
                
                // 跳过空段
                if (part.isEmpty()) {
                    continue;
                }
                
                // 处理IPv4映射地址
                if (part.contains(".")) {
                    byte[] v4Bytes = parseIPv4(part);
                    if (v4Bytes == null || v4Bytes.length != 4) {
                        BLog.e("IPAddressParser", "IPv6地址中的IPv4部分无效: " + part);
                        return null;
                    }
                    
                    // 确保剩余空间足够
                    if (index + 4 > 16) {
                        BLog.e("IPAddressParser", "IPv6地址长度超出16字节");
                        return null;
                    }
                    
                    System.arraycopy(v4Bytes, 0, bytes, index, 4);
                    index += 4;
                    continue;
                }
                
                // 解析16位段
                try {
                    // 段长度验证
                    if (part.length() > 4) {
                        BLog.e("IPAddressParser", "IPv6地址段长度超过4个字符: " + part);
                        return null;
                    }
                    
                    int value = Integer.parseInt(part, 16);
                    if (value < 0 || value > 0xFFFF) {
                        BLog.e("IPAddressParser", "IPv6地址段值超出范围: " + part);
                        return null;
                    }
                    
                    // 确保剩余空间足够
                    if (index + 2 > 16) {
                        BLog.e("IPAddressParser", "IPv6地址长度超出16字节");
                        return null;
                    }
                    
                    bytes[index++] = (byte) (value >> 8);
                    bytes[index++] = (byte) value;
                } catch (NumberFormatException e) {
                    BLog.e("IPAddressParser", "IPv6地址段格式无效: " + part, e);
                    return null;
                }
            }
            
            // 验证最终长度
            if (index != 16) {
                BLog.e("IPAddressParser", "IPv6地址长度不正确: " + index + " 字节，应为16字节");
                return null;
            }
            
            return bytes;
        } catch (Exception e) {
            BLog.e("IPAddressParser", "解析IPv6地址异常: " + ip, e);
            return null;
        }
    }
}