package com.hpbr.bosszhipin.config;

import android.content.Context;
import android.text.TextUtils;

import com.hpbr.bosszhipin.common.SwitchCommon;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.manager.MqttProcessPushManager;
import com.hpbr.bosszhipin.utils.ApmPrivateConfig;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.mms.client.IReceiveListener;
import com.twl.mms.common.EndpointInfo;
import com.twl.mms.common.MqttOptions;
import com.twl.mms.wrapper.DefaultServerProfile;

import org.json.JSONObject;

import java.io.InputStream;
import java.net.InetSocketAddress;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;

/**
 * Created by monch on 15/7/21.
 */
public class MqttConfig extends DefaultServerProfile {

    /*
     * 聊天协议版本
     */
    public static final String PROTOCOL_VERSION = "1.4";

    /**
     * 聊天类型：消息
     */
    public static final int CHAT_TYPE_MESSAGE = 1;
    /**
     * 聊天类型：状态
     */
    public static final int CHAT_TYPE_PRESENCE = 2;
    /**
     * 聊天类型：同步
     */
    public static final int CHAT_TYPE_IQ = 3;
    /**
     * 聊天类型：同步结果 获取历史聊天记录 or 被推荐人的聊天记录
     */
    public static final int CHAT_TYPE_IQ_RESPONSE = 4;
    /**
     * 聊天类型：发送消息id反馈,（本地发送message的server id更新）
     */
    public static final int CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE = 5;
    /**
     * 聊天类型：消息已读
     */
    public static final int CHAT_TYPE_MESSAGE_HAS_READ = 6;
    /**
     * 聊天类型：数据同步（更新个人详情等）
     */
    public static final int CHAT_TYPE_DATA_SYNC = 7;


    /**
     * 同步消息
     */
    public static final int CHAT_TYPE_PRESENCE_PULL = 0x0100;

    /**
     * 聊天服务状态，切换前台状态
     */
    public static final int CHAT_TYPE_PRESENCE_ONLINE = 0x0200;

    /**
     * 聊天服务状态，切换后台状态
     */
    public static final int CHAT_TYPE_PRESENCE_OFFLINE = 0x0400;


    /**
     * 同步最新聊天记录（出席消息触发的消息同步）
     */
    @Deprecated
    public static final String CHAT_SYNC_RESPONSE = "/message/syncAll";
    /**
     * 获取历史聊天记录
     */
    public static final String CHAT_HISTORY_RESPONSE = "/message/history";
    /**
     * NLP建议
     */
    public static final String CHAT_MESSAGE_SUGGEST = "/message/suggest";

    public static final String CHAT_MESSAGE_AUTH_STATUS = "/authStatus/sync";

    public static final String CHAT_MESSAGE_CHAT_PROPOSAL = "/chat/proposal";


    /**
     * 获取推荐牛人的历史聊天记录
     */
    public static final String CHAT_RECOMMEND_HISTORY_RESPONSE = "/message/share";

    public static final String CHAT_HISTORY_MESSAGE_PULL = "/message/pull";


    ///////////////////////////////////////////////////////////////////////////
    // 一下系统下发的消息id
    ///////////////////////////////////////////////////////////////////////////

    /**
     * 系统微JDUserId
     * 不在f2显示
     */
    public static final int CHAT_SYSTEM_SMALL_USER_ID = 994;
    /**
     * 系统周报UserId
     * 不在f2显示
     */
    public static final int CHAT_SYSTEM_WEEKLY_USER_ID = 993;
    /**
     * 系统消息UserId
     * 通知 f2显示
     */
    public static final int CHAT_SYSTEM_MESSAGE_USER_ID = 899;

    /**
     * 道具通知用户Id
     * 通知 f2显示
     */
    public static final int CHAT_ITEM_NOTIFY_USER_ID = 797;

    /**
     * 同事推荐
     * 通知 f2显示
     */
    public static final int CHAT_MATE_RECOMMEND = 793;
    /**
     * 猎头服务
     * 通知 f2显示
     */
    public static final int CHAT_HUNTER_SERVICE = 991;
    /**
     * 职位通知
     * 通知 f2显示
     */
    public static final int CHAT_POSITION_NOTICE = 794;
    /**
     * VIP月报
     * 通知 f2显示
     */
    public static final int CHAT_VIP_MONTH_REPORT = 792;
    /**
     * 财务通知
     * 通知 f2显示
     */
    public static final int CHAT_FINANCE_NOTICE = 795;
    /**
     * 活动通知用户Id
     * 通知 f2显示
     */
    public static final int CHAT_ITEM_ACTIVITY_USER_ID = 798;

    /**
     * 泰坦星
     */
    @Deprecated
    public static final long CHAT_TAI_TAN_STAR_ID = 888;

    /**
     * Boss优聘UserId
     * 不在f2显示
     */
    public static final long CHAT_YOU_PIN_USER_ID = 898;

    /**
     * 公告消息UserId，最小值
     * 不在f2显示
     */
    public static final int CHAT_ANNOUNCE_MIN = 700;

    /**
     * 公告消息UserId，最大值
     * 不在f2显示
     */
    public static final int CHAT_ANNOUNCE_MAX = 800;

    /**
     * GPT 用户id
     */
    public static final int CHAT_GPT_ID = 878;

    /**
     * 猎头服务
     */
    public static final int HUNTER_SERVICE_IF = 885;


    /**
     * get功能的虚拟好友id
     * 7.17版本 get的好友id由于 1400401 变更为 896
     * f2 显示
     */
    public static final long CHAT_GET_CIRCLE1 = 896;


    /**
     * BOSS安全官
     */
    public static final long CHAT_SECURITY_OFFICER = 887;

    /**
     * 谈笑间 902
     * f2 显示
     */
    public static final long CHAT_GET_TALK_JOY = 889;

    /**
     * 圈子
     * f2 显示
     */
    public static final long CHAT_FROM_CIRCLE = 897;

    /**
     * 805版本 双叶草 891  GET 直联
     * f2 显示
     */
    public static final long CHAT_GET_DIRECT = 891;

    /**
     * 简单完善牛人头部提醒+召回促留存
     */
    public static final long CHAT_COMPLETE_GEEK_INFO = 884;


    /**
     * 客服Id包括 机器人和客服
     * f2 显示
     */
    public static final long CHAT_CUSTOMER_ID = 1400400;
    /**
     * get功能的虚拟好友id
     * f2 显示
     */
    @Deprecated
    public static final long CHAT_GET_CIRCLE = 1400401;

    /**
     * 直播招聘的id
     * f2 显示
     */
    public static final long CHAT_LIVING_RECRUIT = 890;

    /**
     * 【小直同学】的id
     * f2 显示
     */
    public static final long CHAT_USER_COMMUNICATIONS_OFFICER = 879;

    /**
     * 直课堂-课程提醒（1014.901）
     */
    public static final long SYSTEM_TITAN_TAB1_USER_ID = 882L;

    /**
     * 直课堂-平台通知（1014.901）
     */
    public static final long SYSTEM_TITAN_TAB2_USER_ID = 881L;

    /**
     * 直课堂-活动提醒（1014.901）
     */
    public static final long SYSTEM_TITAN_TAB3_USER_ID = 880L;
    /**
     * 短视频招人消息助手（1125.922）
     */
    public static final long SYSTEM_SHORT_VIDEO_USER_ID = 876L;



    /**
     * https://zhishu.zhipin.com/wiki/6ESSnaeQ650
     * 我的意向沟通订单
     */
    public static final long SYSTEM_INTENT_CONTACT_ORDER = 874;

    /**
     *  意向沟通付费专属列表有更新
     */
    public static final long SYSTEM_INTENT_LIST_UPDATE = 873;

    /**
     * 香港职位代招
     * 1218.601 https://zhishu.zhipin.com/wiki/eHLt3piDoNz
     */
    public static final long SYSTEM_HK_RECRUIT_POSITION = 777L;

    /**
     * 订阅号
     */
    public static final long SYSTEM_SUBSCRIPTION = 778L;

    ///////////////////////////////////////////////////////////////////////////
    // 以上是系统下发的消息联系人id
    ///////////////////////////////////////////////////////////////////////////


    ///////////////////////////////////////////////////////////////////////////
    // 以下是客户端虚拟的联系人id
    ///////////////////////////////////////////////////////////////////////////

    /**
     * F2通知的friendId,用于和聊天消息做区分
     */
    public static final long CHAT_NOTIFY_ID = Long.MAX_VALUE - 1;
    /**
     * 对我感兴趣好友id
     */
    public static final long CHAT_INTERESTED_TO_ME = Long.MAX_VALUE - 2;
    /**
     * 查看过我的联系人
     */
    public static final long CHAT_VIEW_ME = Long.MAX_VALUE - 3;
    /**
     * 不合适
     */
    public static final long CHAT_UNFIT = Long.MAX_VALUE - 4;
    /**
     * 新职位或者新牛人
     */
    public static final long CHAT_NEWER = Long.MAX_VALUE - 5;
    /**
     * Boss批量沟通牛人
     */
    public static final long CHAT_BATCH_COMMUNICATION = Long.MAX_VALUE - 6;

    /**
     * 我方Vip筛选不符合要求牛人或者Boss
     */
    public static final long CHAT_INFORMITY_GEEK = Long.MAX_VALUE - 8;

    /**
     * Boss收藏带沟通的牛人
     */
    public static final long CHAT_BOSS_FAVOURITE_GEEK = Long.MAX_VALUE - 11;

    /**
     * 高端技术牛人
     */
    public static final long CHAT_TECHNOLOGY_GEEK = Long.MAX_VALUE - 12;

    /**
     * 精选牛人
     */
    public static final long CHAT_REFINED_GEEK = Long.MAX_VALUE - 13;

    /**
     * 牛人身份-为您推荐
     */
    public static final long CHAT_RECOMMAND = Long.MAX_VALUE - 15;

    /**
     * 消息过滤设置
     */
    public static final long CHAT_POSITION_REQUEST_SETTING = Long.MAX_VALUE - 17;

    /**
     * 新职位-猜你想看
     */
    public static final long CHAT_GUESS_LOOK = Long.MAX_VALUE - 18;

    /**
     * 本地职位
     */
    public static final long CHAT_LOCAL_JOB = Long.MAX_VALUE - 19;


    public static final long CHAT_811_VIP_HIGH_GEEK = Long.MAX_VALUE - 20;

    //批量追聊
    public static final long CHAT_CATCH_BACK_CHAT = Long.MAX_VALUE - 21;

    /**
     * GEEK不感兴趣
     */
    public static final long CHAT_GEEK_UNINTEREST = Long.MAX_VALUE - 23;

    /**
     * 我方引力波ID
     */
    public static final long CHAT_OUR_PART_GRAVITATION_WAVE = Long.MAX_VALUE - 7;

    /**
     * 有个社区圈子功能
     */
    public static final long CHAT_OUR_PART_GRAVITATION_WAVE2 = Long.MAX_VALUE - 22;


    /**
     * 直播运营
     */
    public static final long CHAT_OUR_PART_GRAVITATION_WAVE3 = Long.MAX_VALUE - 24;

    /**
     * 直猎邦官方
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157185503
     */
    public static final long CHAT_OUR_PART_ZHI_LIE_BANG = Long.MAX_VALUE - 25;

    /**
     * 学习服务
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=175715264
     */
    public static final long CHAT_STUDY_SERVICE = Long.MAX_VALUE - 26;


    public static final long CHAT_BOSS_NEW_DRAWER = Long.MAX_VALUE - 27;


    /*「悟空消息」盒子*/
    public static final long CHAT_OUT_SOURCE = Long.MAX_VALUE - 28;

    /*https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=208195352*/
    public static final long CHAT_GEEK_HUNTER = Long.MAX_VALUE - 29;

    public static final long CHAT_OUR_PART_ZHI_LIE_BANG_LIGO = Long.MAX_VALUE - 30;

    /**
     * 1118.251【商业】消息页展示邀约牛人
     */
    public static final long CHAT_INVITE_GEEK = Long.MAX_VALUE - 31;

    public static final long CHAT_HUNTER_PUSH = Long.MAX_VALUE - 32;

    /**
     * 1222.607 【C】聊天列表新增待达成抽屉
     * 定义：当前“仅沟通”tab 下，具有“B 向 C 请求交换，C 未接受交换的联系人”
     */
    public static final long GEEK_WAIT_ACHIEVE = Long.MAX_VALUE - 33;

    /**
     * 1226.80【B/C】聊天场景无用信息收纳功能
     */
    public static final long CHAT_REJECT_INTENT_DRAWER = Long.MAX_VALUE - 34;

    /**
     * 1308.165【B&C】AI直聊功能
     */
    public static final long CHAT_AI_DIRECT_CHAT_DRAWER = Long.MAX_VALUE - 35;

    ///////////////////////////////////////////////////////////////////////////
    // 以上是客户端虚拟的联系人id
    ///////////////////////////////////////////////////////////////////////////


    /**
     * MQTT主题
     */
    public static final String TOPIC_CHAT = "chat";

//    /**
//     * QOS:2 两次握手
//     */
//    public static final int MQTT_QUALITY_OF_SERVICE = 1;

    /**
     * mqtt连接超时时间
     */
//    private static final int MQTT_CONNECT_TIMEOUT = 30;//默认30s；因为Android传输层的重试间隔分别为[ 0.25 0.5 1 2 4 8 16 32 64 64 64 ...],所以30s足够让传输层在较为积极的重试区域内
    private static final int MQTT_CONNECT_TIMEOUT = 30;//默认30s；因为Android传输层的重试间隔分别为[ 0.25 0.5 1 2 4 8 16 32 64 64 64 ...],所以30s足够让传输层在较为积极的重试区域内
    /**
     * mqtt ping时间
     */
    private static final int MQTT_PING_TIME = 360;


    private static EndpointInfo sEndpointInfo;

    @Override
    public EndpointInfo getServerInfo() {
        if (sEndpointInfo != null) {
            return sEndpointInfo;
        }
        EndpointInfo endpointInfo = null;
        if (TextUtils.isEmpty(HostConfig.CONFIG.getMqttHttpAddr())) {
            endpointInfo = new EndpointInfo(InetSocketAddress.createUnresolved(HostConfig.CONFIG.getMqttAddr(),
                    HostConfig.CONFIG.getMqttPort()));

        } else {
            endpointInfo = new EndpointInfo(InetSocketAddress.createUnresolved(HostConfig.CONFIG.getMqttAddr(),
                    HostConfig.CONFIG.getMqttPort()),
                    InetSocketAddress.createUnresolved(
                            HostConfig.CONFIG.getMqttHttpAddr(),
                            HostConfig.CONFIG.getMqttHttpPort()));
        }

        if (sMqttSSLSocketFactory != null) {
            endpointInfo.setSSLEndpoint(InetSocketAddress.createUnresolved(HostConfig.CONFIG.getMqttAddr(), HostConfig.MQTT_SSL_PORT),
                    TextUtils.isEmpty(HostConfig.CONFIG.getMqttHttpAddr()) ? null : InetSocketAddress.createUnresolved(HostConfig.CONFIG.getMqttHttpAddr(), HostConfig.CONFIG.getMqttHttpPort()),
                    isForceSsl(),
                    MqttConfig::isOpenMqttTls);
        }
        endpointInfo.setFixNetChange(AndroidDataStarGray.isFixMqttNetChange());
        endpointInfo.setRetrySSLCount(AndroidDataStarGray.getMaxSSLCount());
        endpointInfo.setIgnoreBgConnect(AndroidDataStarGray.isMqttSSLIgnoreBgConnect());
        sEndpointInfo = endpointInfo;

        if (AndroidDataStarGray.getNewMqttDns()) {
            endpointInfo.setDNSResolve(new CustomHttpDNSV2());
        } else {
            endpointInfo.setDNSResolve(new CustomHttpDNS());
        }
        return endpointInfo;
    }

    private static SSLSocketFactory sMqttSSLSocketFactory = null;

    /**
     * SSL证书配置
     *
     * @param context
     * @return
     */
    public static SSLSocketFactory createMqttSSLSocketFactory(Context context) {
        String ksPath = "client.p12";
        String certPath = "server.crt";

        SSLContext sslContext;
        try {
            char[] ps = "pJD#IW%XPWS#WPVQDVis@Z23%DH#WNZjs%dfN%D#IDPZ@hX9".toCharArray();
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            InputStream ksInputStream = context.getResources().getAssets().open(ksPath);
            keyStore.load(ksInputStream, ps);
            KeyManagerFactory kmf = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            kmf.init(keyStore, ps);

            KeyStore cert = KeyStore.getInstance("PKCS12");
            InputStream certInputStream = context.getResources().getAssets().open(certPath);
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            cert.load(null);
            cert.setCertificateEntry("mqtt-server", certificateFactory.generateCertificate(certInputStream));

            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(cert);
            TrustManager[] trustManagers = null;
            try {
                trustManagers = tmf.getTrustManagers();
            } catch (Exception e) {
                e.printStackTrace();
            }
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(kmf.getKeyManagers(), trustManagers, null);
            sMqttSSLSocketFactory = sslContext.getSocketFactory();
            return sMqttSSLSocketFactory;
        } catch (Exception e) {
            CrashReport.postCatchedException(e);
        }
        return null;
    }


    @Override
    public MqttOptions getMqttOptions() {
        MqttOptions mqttOptions;
        //5.51版本开始默认值设为1，但是为了安全起见，服务端控制开关
        if (SwitchCommon.isMqttQosIsTwo()) {
            mqttOptions = new MqttOptions(TOPIC_CHAT, MQTT_CONNECT_TIMEOUT, MQTT_PING_TIME, true, 2, createMqttSSLSocketFactory(Utils.getApp()));
        } else {
            mqttOptions = new MqttOptions(TOPIC_CHAT, MQTT_CONNECT_TIMEOUT, MQTT_PING_TIME, true, 1, createMqttSSLSocketFactory(Utils.getApp()));
        }
        mqttOptions.setUesOriginalSslCount(isUesOriginalSSlCount());
        return mqttOptions;
    }

    private IReceiveListener mReceiveListener = new IReceiveListener() {
        @Override
        public void onReceive(byte[] data) {
            try {
                MqttProcessPushManager.showPush(data);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    };

    @Override
    public IReceiveListener getReceiveListener() {
        return mReceiveListener;
    }


    public static final String KEY_MQTT_TLS = "mqttTls";

    public static void setOpenMqttTls(boolean mqttTls) {
        SpManager.get().user().edit().putBoolean(KEY_MQTT_TLS, mqttTls).apply();
    }

    public static boolean isOpenMqttTls() {
        int forceProtocol = getForceProtocol();
        if (forceProtocol == 1) {//是否强制SSL
            return true;
        } else if (forceProtocol == 2) {//是否强制TCP
            return false;
        }
        return SpManager.get().user().getBoolean(KEY_MQTT_TLS, true);
    }

    public boolean isForceSsl() {
        return AndroidDataStarGray.getInstance().isForceSsl();
    }

    public static int getForceProtocol() {
        JSONObject configJsonObject = ApmPrivateConfig.getPrivateConfigJsonObject();
        if (configJsonObject != null) {
            //默认 0  1 :强制ssl
            return configJsonObject.optInt("forceProtocol");
        }
        return 0;
    }

    /**
     * 检查当前是否禁用IPv6功能
     *
     * <p>函数执行逻辑分为两级判断：
     * <ol>
     *   <li><b>配置优先检查</b>：从APM私有配置获取JSON配置对象，当存在「disableIpv6」字段时，
     *       直接返回配置值（默认取false）</li>
     *   <li><b>灰度降级策略</b>：当无显式配置时，通过{@link AndroidDataStarGray}获取MQTT配置，
     *       返回其disableIPv6字段值</li>
     * </ol>
     *
     * <p>特别注意：
     * <ul>
     *   <li>当配置不存在时，实际返回的是灰度控制策略的原始值，而非取反结果（与原有注释描述不一致，
     *       建议确认业务逻辑正确性）</li>
     *   <li>配置检查使用optBoolean方法，确保在字段值为null时返回默认值false</li>
     * </ul>
     *
     * @return boolean 禁用标识
     *         <li>true - 要求禁用IPv6</li>
     *         <li>false - 允许使用IPv6</li>
     */
    public static boolean isDisableIpv6() {
        // 一级检查：APM私有配置获取（含空值保护）
        JSONObject configJsonObject = ApmPrivateConfig.getPrivateConfigJsonObject();
        /* 显式配置检查（包含默认值处理） */
        if (configJsonObject != null && configJsonObject.has("disableIpv6")) {
            return configJsonObject.optBoolean("disableIpv6", false);
        }

        /* 灰度策略降级检查（直接返回原始值） */
        return AndroidDataStarGray.getInstance().getMqttConfig().disableIPv6;
    }



    public static boolean isSupportIpv6() {
        return AndroidDataStarGray.getInstance().isSupportIpv6Connect();
    }

    public static int isSupportIpv6Split() {
        return AndroidDataStarGray.getInstance().getMqttConfig().support_ipv6_connect_split;
    }

    public static int isUesOriginalSSlCount() {
        return AndroidDataStarGray.getInstance().getMqttConfig().ues_original_ssl_count;
    }
}
