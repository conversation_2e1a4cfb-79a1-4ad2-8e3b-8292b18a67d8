package com.hpbr.bosszhipin.config;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;

/**
 * 如果需要页面自定义主题色
 * 1、super.onCreate 之前调用  NightModeConfig#setLocalNightMode(androidx.appcompat.app.AppCompatActivity, boolean)
 * 2、Activity 重写 onConfigurationChanged
 * 3、AndroidManifest   activity#   添加 android:configChanges="uiMode
 */
@TargetApi(Build.VERSION_CODES.Q)
public class NightModeConfig {
    public static final String TAG = "NightMode";
    private static final String NIGHT_MODE = "Night_Mode";
    private static final String DEFAULT_NIGHT_MODE = "DEFAULT_NIGHT_MODE";
    private static NightModeConfig sModeConfig;
    private int defaultNightMode = AppCompatDelegate.MODE_NIGHT_NO;
    private SharedPreferences mSharedPreference;
    public NightModeConfig() {
        defaultNightMode = AndroidDataStarGray.defaultTheme();
    }

    public static NightModeConfig getInstance() {
        if (sModeConfig == null) {
            sModeConfig = new NightModeConfig();
        }
        return sModeConfig;
    }

    @TargetApi(Build.VERSION_CODES.Q)
    public static void applySetting() {
//        https://developer.huawei.com/consumer/cn/forum/topic/0202103896809235220
        if (NightUtil.isSupportDark()) {
            int nightMode = NightModeConfig.getInstance().getNightMode();
            AppCompatDelegate.setDefaultNightMode(nightMode);
//            switchTheme(Utils.getApp(), nightMode);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    public static void switchTheme(Context context, @AppCompatDelegate.NightMode int mode) {
        Resources resources = context.getResources();
        DisplayMetrics dm = resources.getDisplayMetrics();
        Configuration config = resources.getConfiguration();
        config.uiMode &= ~Configuration.UI_MODE_NIGHT_MASK;
        config.uiMode |= mode;
        resources.updateConfiguration(config, dm);
    }

    @TargetApi(Build.VERSION_CODES.Q)
    public int getNightMode() {
        return getSharePreference().getInt(NIGHT_MODE, defaultNightMode);
    }

    @TargetApi(Build.VERSION_CODES.Q)
    public void setNightMode(@AppCompatDelegate.NightMode int mode) {
        TLog.debug(TAG, "setNightMode====    isNightMode = %d", mode);
        getSharePreference().edit().putInt(NIGHT_MODE, mode).apply();
        AppCompatDelegate.setDefaultNightMode(mode);
//        applySetting();
    }

    private SharedPreferences getSharePreference() {
        if (mSharedPreference == null) {
            mSharedPreference = Utils.getApp().getSharedPreferences(NIGHT_MODE, Context.MODE_PRIVATE);
        }
        return mSharedPreference;
    }

    /**
     * 深色模式 添加 浮层
     */
    public void addDarkModeFloatView(BaseActivity activity) {
        View contentView = activity.findViewById(Window.ID_ANDROID_CONTENT);
        View addDarkModeFloatView = contentView.findViewWithTag("addDarkModeFloatView");
        if (addDarkModeFloatView == null && contentView instanceof ViewGroup) {
            View view = new View(activity);
            view.setTag("addDarkModeFloatView");
            int color = ContextCompat.getColor(activity, R.color.color_4D000000);
            view.setBackgroundColor(color);
            ((ViewGroup) contentView).addView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            activity.setStatusBarColor(color);
        }
    }
}