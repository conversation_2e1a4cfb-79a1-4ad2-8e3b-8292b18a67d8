package com.hpbr.bosszhipin.config;

import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelmsg.WXMiniProgramObject;

import static com.hpbr.bosszhipin.config.PackageConfigContants.PACKAGE_WEIXIN_MINI_OFFLINE;
import static com.hpbr.bosszhipin.config.PackageConfigContants.PACKAGE_WEIXIN_MINI_ONLINE;

/**
 * Created by monch on 15/6/16.
 */
public class OtherConfig {

    /********** 新浪微博分享相关 **********/

    // 应用的key 请到官方申请正式的appkey替换APP_KEY
    public static final String APP_KEY = "1054079396";

    /********** 微信相关 ***********/

    public static final String WE_CHAT_APP_ID =
            (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
                    PackageConfigContants.PACKAGE_WEIXIN_APPID_ONLINE :
                    PackageConfigContants.PACKAGE_WEIXIN_APPID_OFFLINE;

    /* 微信小程序原始id */
    public static final String WE_CHAT_MINI_ID = (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
            PACKAGE_WEIXIN_MINI_ONLINE : PACKAGE_WEIXIN_MINI_OFFLINE;

    /* 正式版:0，测试版:1，体验版:2 */
    public static final int WE_CHAT_MINIPROGRAM_TYPE = (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
            WXMiniProgramObject.MINIPTOGRAM_TYPE_RELEASE : WXMiniProgramObject.MINIPROGRAM_TYPE_PREVIEW;

    public static  int WE_CHAT_LAUNCH_MINIPROGRAM_TYPE = (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
            WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE : WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;

    //region 7.02新增，用于引导用户打开小程序来关注Boss公众号
    //feat(小程序miniid): 更换，测试环境新配了一套测试账号, gh_7d15d7880aa0
    public static final String MINI_ID_FOR_OFFICIAL_ACCOUNT = (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
            "gh_a327cb04644a" : "gh_c53b69e94d29"; // 正式/测试

    public static final String MINI_PATH_FOR_OFFICIAL_ACCOUNT = (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
            "pages/service/service" : "pages/service/service";

    /**招行银联相关*/
    public static final String CMB_APP_ID =
            (HostConfig.CONFIG == HostConfig.Addr.ONLINE || HostConfig.CONFIG == HostConfig.Addr.PRE) ?
                    PackageConfigContants.PACKAGE_CMB_APPID_ONLINE :
                    PackageConfigContants.PACKAGE_CMB_APPID_OFFLINE;
    //endregion

    // 钉钉
    public static final String DING_TALK_APP_ID = "dingoakjqlzhxsesajt2kk";
}
