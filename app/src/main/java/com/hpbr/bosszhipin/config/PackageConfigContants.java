package com.hpbr.bosszhipin.config;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;


/**
 * Created by wa<PERSON><PERSON> on 2017/4/26.
 */

public class PackageConfigContants {

    public static final String PACKAGE_NAME = getString(R.string.package_name);

    /***file provider访问权限***/
    public static final String PACKAGE_FILE_AUTHORITIES = getString(R.string.file_provider_authorities);

    /***应用程序appid***/
    public static final int PACKAGE_APP_ID = getInt(R.string.package_appid);

    /*** 访问圈子用 ****/
    public static final String QUANZI_APP_ID = getString(R.string.quanzi_appid);
    public static final String QUANZI_SECRET_KEY = getString(R.string.quanzi_secret_key);

    /***应用程序第三方push注册类型***/
    public static final int PACKAGE_TOKEN_TYPE_MI = getInt(R.string.mi_type);
    public static final int PACKAGE_TOKEN_TYPE_MI_PASS_THOUGH = getInt(R.string.mi_pass_through_type);
    public static final int PACKAGE_TOKEN_TYPE_IXT = getInt(R.string.ixt_type);
    public static final int PACKAGE_TOKEN_TYPE_HW = getInt(R.string.hw_type);
    public static final int PACKAGE_TOKEN_TYPE_HW_PASS_THOUGH = getInt(R.string.hw_pass_through_type);

    /***微信相关***/
    public static final String PACKAGE_WEIXIN_APPID_ONLINE = getString(R.string.weixin_appid_online);
    public static final String PACKAGE_WEIXIN_APPID_OFFLINE = getString(R.string.weixin_appid_offline);
    public static final String PACKAGE_WEIXIN_MINI_ONLINE = getString(R.string.weixin_miniid_online);
    public static final String PACKAGE_WEIXIN_MINI_OFFLINE = getString(R.string.weixin_miniid_offline);

    /***小米push相关***/
    public static final String PACKAGE_PUSH_MI_APP_ID = getString(R.string.mi_app_id);
    public static final String PACKAGE_PUSH_MI_APP_KEY = getString(R.string.mi_app_key);

//    /***友盟相关***/
//    public static final String PACKAGE_UMENG_KEY = getString(R.string.umeng_key);

    /***bugly相关***/
    public static final String PACKAGE_BUGLY_KEY = getString(R.string.bugly_key);

    /***腾讯云人脸相关***/
    public static final String TENCENT_FACE_APPID = getString(R.string.tencent_face_appid);
    public static final String TENCENT_FACE_LICENCE = getString(R.string.tencent_face_licence);

    public static int getInt(int id) {
        return Integer.valueOf(App.getAppContext().getString(id));
    }

    public static String getString(int id) {
        return App.getAppContext().getString(id);
    }

    /***QQ SDK相关***/
    public static final String PACKAGE_QQ_APPID = getString(R.string.qq_appid_online);
    public static final String PACKAGE_QQ_APP_KEY = getString(R.string.qq_appkey_online);

    /**
     * 腾讯端视频
     */
    public static final String PACKAGE_LICENSE_URL = getString(R.string.tencent_license_url);
    public static final String PACKAGE_LICENSE_KEY = getString(R.string.tencent_license_key);

    /***招行银联***/
    public static final String PACKAGE_CMB_APPID_ONLINE = getString(R.string.cmb_appid_online);
    public static final String PACKAGE_CMB_APPID_OFFLINE = getString(R.string.cmb_appid_offline);
}
