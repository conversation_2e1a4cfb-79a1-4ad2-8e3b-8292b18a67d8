package com.hpbr.bosszhipin.config;

import com.hpbr.bosszhipin.app.R;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by monch on 15/10/28.
 */
public class WebSiteConfig {

    private static final List<TempEntity> SOCIAL_URL;

    public static final TempEntity OTHER_SOCIAL_ENTITY = new TempEntity(R.mipmap.ic_social_home, "", "其它");

    static {
        SOCIAL_URL = new ArrayList<>();
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_weibo, "weibo.com", "微博"));  // 微博
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_zhihu, "zhihu.com", "知乎"));  // 知乎
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_douban, "douban.com", "豆瓣")); // 豆瓣
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_linkedin, "linkedin.com", "LinkedIn"));   // LinkedIn
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_github, "github.com", "github")); // github
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_csdn, "csdn.net", "CSDN"));   // CSDN
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_dribbble, "dribbble.com", "Dribbble"));   // Dribbble
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_behance, "behance.net", "behance"));    // behance
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_zcool, "zcool.com.cn", "站酷"));   // 站酷
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_lofter, "lofter.com", "LOFTER")); // LOFTER


        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_baidu, "pan.baidu.com", "百度网盘"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_bosszhipin, "zhipin.com", "BOSS直聘"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_weixin, "mp.weixin.qq.com", "微信公众号"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_shimo, "shimo.im", "石墨文档"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_douyin, "douyin.com", "抖音"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_xinpianchang, "xinpianchang.com", "新片场"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_txweiyun, "weiyun.com", "腾讯微云"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_bilibili, "bilibili.com", "bilibili"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_jinshan, "kdocs.cn", "金山文档"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_ui_china, "ui.cn", "UI中国"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_pdf, "pdf.maitube.com", "MaiPDF"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_taobao, "taobao.com", "淘宝"));
        SOCIAL_URL.add(new TempEntity(R.mipmap.ic_social_txwendang, "docs.qq.com", "腾讯文档"));

        SOCIAL_URL.add(OTHER_SOCIAL_ENTITY);   // 其它
    }

    public static final class TempEntity {
        public int resId;
        public String url;
        public  String desc;

        TempEntity(int resId, String url, String desc) {
            this.resId = resId;
            this.url = url;
            this.desc = desc;
        }

        @Override
        public String toString() {
            return "resId=" + resId + ", desc='" + desc;
        }
    }

    public static int getIconResId(String url) {
        String u = url.toLowerCase(Locale.getDefault());
        int resId = 0;
        for (TempEntity entity : SOCIAL_URL) {
            if (LText.empty(entity.url) || u.contains(entity.url)) {
                resId = entity.resId;
                 break;
            }
        }
        return resId;
    }

    public static TempEntity getIconTempEntity(String url) {
        String u = url.toLowerCase(Locale.getDefault());
        TempEntity tempEntity = null;
        for (TempEntity entity : SOCIAL_URL) {
            if (LText.empty(entity.url) || u.contains(entity.url)) {
                tempEntity = entity;
                 break;
            }
        }
        return tempEntity;
    }

    public static boolean isOtherSocialEntity(TempEntity entity) {
        return null != entity
                && OTHER_SOCIAL_ENTITY.resId == entity.resId
                && OTHER_SOCIAL_ENTITY.url.equals(entity.url)
                && OTHER_SOCIAL_ENTITY.desc.equals(entity.desc);


    }
}
