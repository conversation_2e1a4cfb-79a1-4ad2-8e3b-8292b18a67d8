package com.hpbr.bosszhipin.config.custom;

/**
 * Created by wa<PERSON><PERSON> on 2017/6/13.
 */

import android.content.Context;
import android.util.Log;

import java.io.File;

public class ClearUtils {
    private static final String TAG = "ClearUtils";

    /**
     * 清除本应用所有的数据 * * @param context * @param filepath
     */
    public static void cleanApplicationData(Context context, String... filepath) {
        deleteFiles(new File("/data/data/" + context.getPackageName()));
    }

    /**
     * 删除方法 这里只会删除某个文件夹下的文件，如果传入的directory是个文件，将不做处理 * * @param directory
     */
    private static void deleteFiles(File root) {
        if (root.isDirectory()) {
            File[] list = root.listFiles();
            for (File file : list) {
                deleteFiles(file);
            }
        }
        delete(root);
    }

    private static void delete(File file) {
        if (!file.delete()) {
            Log.d(TAG, "文件删除失败：" + file.getAbsolutePath());
        }
    }

}
