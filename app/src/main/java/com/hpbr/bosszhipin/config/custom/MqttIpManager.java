package com.hpbr.bosszhipin.config.custom;

import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.L;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import net.bosszhipin.api.MqttIpRequest;
import net.bosszhipin.api.MqttIpResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by wang<PERSON> on 2018/1/22.
 * mqtt ip管理类，通过向服务端请求mcp接口获得
 *
 */

public class MqttIpManager {
    private static MqttIpManager mInstance = new MqttIpManager();
    private static final String SP_NAME_MQTT_IP = "mqtt_ip";
    private static final String KEY_MQTT_IP = "key_mqtt_ip";
    private SpImpl sp;

    private AtomicBoolean isRefreshing = new AtomicBoolean(false);

    private MqttIpManager(){
        sp = SpFactory.create(App.get(),SP_NAME_MQTT_IP);
        String ipBackUp = sp.getString(KEY_MQTT_IP);
        if(!TextUtils.isEmpty(ipBackUp)){
            try {
                MqttIpResponse ipResponse = GsonUtils.getGson().fromJson(ipBackUp,MqttIpResponse.class);
                if((ipResponse.mqttIps != null && ipResponse.mqttIps.size() >0)||(ipResponse.mqttHttpIps != null && ipResponse.mqttHttpIps.size() >0)){
                    ipsModel = new MqttIpsModel(ipResponse.mqttIps,ipResponse.mqttHttpIps);
                    L.d("test","====1==ipmodel 1:"+ipsModel.getMqttHttpIps().size() +" 2:"+ipsModel.getMqttIps().size());
                }
            }catch (Exception e){

            }
        }
    }

    private MqttIpsModel ipsModel;

    public void clear(){
        sp.remove(KEY_MQTT_IP);
    }

    public static MqttIpManager getInstance(){
        return mInstance;
    }

    public void refresh(){
        if(isRefreshing.get() || TextUtils.isEmpty(UserManager.getToken())){
            return;
        }
        isRefreshing.set(true);
        L.d("test","==token:"+ UserManager.getToken());
        MqttIpRequest request = new MqttIpRequest(new ApiRequestCallback<MqttIpResponse>() {
            @Override
            public void onSuccess(ApiData<MqttIpResponse> data) {
                if((data.resp.mqttIps != null && data.resp.mqttIps.size() >0)||(data.resp.mqttHttpIps != null && data.resp.mqttHttpIps.size() >0)){
                    if(data.resp.mqttIps != null){
                        Collections.shuffle(data.resp.mqttIps);
                    }

                    if(data.resp.mqttHttpIps != null){
                        Collections.shuffle(data.resp.mqttHttpIps);
                    }

                    ipsModel = new MqttIpsModel(data.resp.mqttIps,data.resp.mqttHttpIps);
                    sp.putString(KEY_MQTT_IP, GsonUtils.getGson().toJson(data.resp));

                    L.d("test","==after shuffle:"+ GsonUtils.getGson().toJson(data.resp));
                }
            }

            @Override
            public void onComplete() {
                isRefreshing.set(false);
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    public MqttIpsModel getIpsModel(){
        if(ipsModel == null){
            refresh();
        }
        return ipsModel;
    }

    public static class MqttIpsModel{
        private List<InetSocketAddress> mqttIps = new ArrayList<>();
        private List<InetSocketAddress> mqttHttpIps = new ArrayList<>();

        public MqttIpsModel(List<String> mqttIps,List<String> mqttHttpIps){
            if(mqttIps != null && mqttIps.size() > 0){
                for (String ip:mqttIps){
                    InetSocketAddress address =  transIpStringToInetSocketAddress(ip);
                    if(address != null){
                        this.mqttIps.add(address);
                    }
                }
            }

            if(mqttHttpIps != null && mqttHttpIps.size() > 0){
                for (String ip:mqttHttpIps){
                    InetSocketAddress address =  transIpStringToInetSocketAddress(ip);
                    if(address != null){
                        this.mqttHttpIps.add(address);
                    }
                }
            }
        }

        public List<InetSocketAddress> getMqttIps() {
            return mqttIps;
        }

        public List<InetSocketAddress> getMqttHttpIps() {
            return mqttHttpIps;
        }



        private InetSocketAddress transIpStringToInetSocketAddress(String ipString){
            InetSocketAddress socketAddress = null;
            try {
                String[] ipStringSplit = ipString.split(":");
                String ip = ipStringSplit[0];
                String port = ipStringSplit[1];
                if(ip.split("\\.").length == 4){
                    socketAddress = new InetSocketAddress(InetAddress.getByName(ip),Integer.valueOf(port));
//                    socketAddress =  InetSocketAddress.createUnresolved(ip,Integer.valueOf(port));
                }
            }catch (Exception e){

            }
            return socketAddress;
        }
    }



}
