package com.hpbr.bosszhipin.config.custom;

import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.L;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import net.bosszhipin.api.ServerConfigRequest;
import net.bosszhipin.api.ServerConfigResponse;
import net.bosszhipin.api.bean.ServerPriConfigBean;
import net.bosszhipin.api.bean.ServerPubConfigBean;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Created by wang<PERSON> on 2018/1/22.
 */

public class ServerConfigManager {
    private static ServerConfigManager mInstance = new ServerConfigManager();
    private static final String SP_NAME_SERVER_CONFIG = "server_config";
    private static final String KEY_SERVER_CONFIG= "key_server_config";
    private SpImpl sp;
    private ServerPubConfigBean pubConfigBean;
    private ServerPriConfigBean priConfigBean;

    private ServerConfigManager(){
        sp = SpFactory.create(App.get(),SP_NAME_SERVER_CONFIG);
        String config = sp.getString(KEY_SERVER_CONFIG);
        if(!TextUtils.isEmpty(config)){
            try {
                ServerConfigResponse configResponse = GsonUtils.getGson().fromJson(config,ServerConfigResponse.class);
                if(configResponse != null){
                    pubConfigBean = configResponse.pub;
                    priConfigBean = configResponse.pri;
                }
            }catch (Exception e){
            }
        }
    }

    public static ServerConfigManager getInstance(){
        return mInstance;
    }

    public void clear(){
        sp.remove(KEY_SERVER_CONFIG);
    }

    public void refresh(){
        if(TextUtils.isEmpty(UserManager.getToken())){
            return;
        }
        ServerConfigRequest request = new ServerConfigRequest(new ApiRequestCallback<ServerConfigResponse>() {
            @Override
            public void onSuccess(ApiData<ServerConfigResponse> data) {
                if(data.resp != null){
                    pubConfigBean = data.resp.pub;
                    priConfigBean = data.resp.pri;
                    sp.putString(SP_NAME_SERVER_CONFIG, GsonUtils.getGson().toJson(data.resp));
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

}
