package com.hpbr.bosszhipin.config.custom;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.hpbr.bosszhipin.config.custom.core.Spec;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.config.custom.type.Config;
import com.hpbr.bosszhipin.config.custom.type.MainTabIconConfig;
import com.hpbr.bosszhipin.config.custom.type.F1PageTopConfig;
import com.hpbr.bosszhipin.config.custom.type.PageBottomConfig;
import com.hpbr.bosszhipin.config.custom.type.F4PageTopConfig;
import com.hpbr.bosszhipin.login.config.LoginURLConfig;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;

import net.bosszhipin.api.GetThemeConfigResponse;
import net.bosszhipin.api.GetThemeConfigResponse.ActivityConfig;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ThemeConfigManager {

    @NonNull
    private static final ThemeConfigManager INSTANCE = new ThemeConfigManager();

    @NonNull
    public static ThemeConfigManager getInstance() {
        return INSTANCE;
    }

    @NonNull
    private final Config mainTabIconConfig = new MainTabIconConfig();
    @NonNull
    private final Config f1TopConfig = new F1PageTopConfig();
    @NonNull
    private final Config bottomConfig = new PageBottomConfig();
    @NonNull
    private final Config f4TopConfig = new F4PageTopConfig();

    @NonNull
    private final List<Config> configs = new ArrayList<>(Arrays.asList(mainTabIconConfig, f1TopConfig, bottomConfig, f4TopConfig));

    @NonNull
    private String getLocalDataConfigVersion() {
        /*
            [
                {
                    "activityType": 0,
                    "dataVersion": 1
                },
                {
                    "activityType": 1,
                    "dataVersion": 2
                },
                {
                    "activityType": 2,
                    "dataVersion": 3
                },
                {
                    "activityType": 3,
                    "dataVersion": 4
                }
            ]
         */
        final JSONArray array = new JSONArray();
        for (int i = 0; i < configs.size(); i++) {
            try {
                JSONObject obj = new JSONObject();
                obj.putOpt("activityType", i);
                obj.putOpt("dataVersion", configs.get(i).getLocalDataConfigVersion());
                array.put(obj);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return array.toString();
    }

    //region PUBLIC APIS

    public void requestConfig() {
        SimpleApiRequest.GET(LoginURLConfig.URL_ZPUSER_TAB_BAR_ICON_GET_THEME_CONFIG)
                .addParam("extraInfoReq", getLocalDataConfigVersion())
                .setRequestCallback(new SimpleApiRequestCallback<GetThemeConfigResponse>() {
                    @Override
                    public void onSuccess(ApiData<GetThemeConfigResponse> data) {
                        super.onSuccess(data);

                        parseConfig(data.resp);
                    }

                    private void parseConfig(@NonNull GetThemeConfigResponse resp) {
                        List<ActivityConfig> activityConfigList = resp.activityConfigList;

                        if (activityConfigList != null) {
                            for (ActivityConfig activityConfig : activityConfigList) {
                                // 0:tabbarIcon配置 1:顶部主题配置 2:底部主题配置
                                int type = activityConfig.activityType;
                                Config config = LList.getElement(configs, type);
                                if (config != null) {
                                    config.parseConfig(activityConfig);
                                }
                            }
                        }
                    }
                })
                .execute();
    }

    @WorkerThread
    public void sync() {
        for (Config config : configs) {
            config.sync();
        }
    }

    @SuppressWarnings("unused")
    public void removeConfig() {
        for (Config config : configs) {
            config.removeConfig();
        }
    }

    //region Source
    @NonNull
    public Source getMainTabSource(@NonNull Spec spec) {
        return mainTabIconConfig.getSource(spec);
    }

    @NonNull
    public Source getF1TopPageSource(@NonNull Spec spec) {
        return f1TopConfig.getSource(spec);
    }

    @NonNull
    public Source getBottomPageSource(@NonNull Spec spec) {
        return bottomConfig.getSource(spec);
    }

    @NonNull
    public Source getF4TopPageSource(@NonNull Spec spec) {
        return f4TopConfig.getSource(spec);
    }

    //endregion

    //region ColorType

    public int getMainTabColorType() {
        return mainTabIconConfig.getColorType();
    }

    /**
     * @return 0:默认 1:深色 2:浅色
     */
    public int getF1ColorType() {
        return f1TopConfig.getColorType();
    }

    /**
     * @return 0:默认 1:深色 2:浅色
     */
    public int getBottomColorType() {
        return bottomConfig.getColorType();
    }

    /**
     * @return 0:默认 1:深色 2:浅色
     */
    public int getF4colorType() {
        return f4TopConfig.getColorType();
    }

    //endregion

    //endregion

}
