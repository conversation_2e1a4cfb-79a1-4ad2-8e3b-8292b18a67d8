package com.hpbr.bosszhipin.config.custom;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.Bundle;
import android.os.Process;
import android.util.Log;

import com.hpbr.bosszhipin.BuildKey;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.NotificationConfig;
import com.twl.utils.process.ProcessUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import androidx.core.app.NotificationCompat;

import static com.hpbr.bosszhipin.config.HostConfig.Addr.OFFLINE;
import static com.hpbr.bosszhipin.config.HostConfig.Addr.ONLINE;
import static com.hpbr.bosszhipin.config.HostConfig.Addr.QA;

/**
 * Created by monch on 2017/6/27.
 */

public class UrlConfigManager {

    private static final String SP_NAME = "debug_config";
    private static final String KEY_TYPE = "key_type";
    private static final String KEY_ZP_TAG = "KEY_ZP_TAG";
    static List<Activity> activityList;

    public static void saveConfig(Context context, HostConfig.Addr addr) {
        Log.d("api", "===saveConfig====type:" + addr.getType());
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putInt(KEY_TYPE, addr.getType()).commit();    // 这里必须使用commit，如果使用apply是异步过程，可能未保存到文件，下一步便杀死程序
    }

    public static void resetConfig(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putInt(KEY_TYPE, BuildInfoUtils.getInt(BuildKey.CONFIG)).commit();
    }

    //保存ZP_TAG
    public static void saveZPTag(Context context, String url) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(KEY_ZP_TAG, url).commit();    // 这里必须使用commit，如果使用apply是异步过
    }

    //获得在ZP_TAG的内容
    public static String getZPTagText(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(KEY_ZP_TAG, "");
    }


    public static HostConfig.Addr readConfig(int type) {
        return type == 2 ? OFFLINE :
                type == 3 ? QA :
                        type == 4 ? HostConfig.Addr.PUBLIC :
                                type == 5 ? HostConfig.Addr.PRE :
                                        ONLINE;
    }

    public static void initConfig(Application context) {
        if (!BuildInfoUtils.isDebug()) return;
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        int type = sp.getInt(KEY_TYPE, 0);
        if (type == 0) {
            type = BuildInfoUtils.getInt(BuildKey.CONFIG);
        }
        HostConfig.CONFIG = HostConfig.getAddrByType(type);
        Log.d("api", "=======type:" + type + " name:" + ProcessUtil.getCurProcessName(context));

        registerActivityLifecycle(context);
    }

    private static void registerActivityLifecycle(Application context) {
        activityList = Collections.synchronizedList(new ArrayList<Activity>());
        context.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                activityList.add(activity);
            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {

            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {
                activityList.remove(activity);
            }
        });
    }

    public static void killApplication(Context context) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(19999);
        Iterator<Activity> activitys = activityList.iterator();
        while (activitys.hasNext()) {
            Activity activity = activitys.next();
            activity.finish();
            activitys.remove();
        }
        activityList.clear();
        try {
            if (context != null) {
                String packageName = context.getPackageName();
                ActivityManager activityManager = (ActivityManager) context
                        .getSystemService(Context.ACTIVITY_SERVICE);

                ApplicationInfo applicationInfo = context.getApplicationInfo();
                long uid = applicationInfo.uid;
                List<ActivityManager.RunningAppProcessInfo> processInfos = activityManager
                        .getRunningAppProcesses();
                if (processInfos != null) {
                    for (ActivityManager.RunningAppProcessInfo appProcess : processInfos) {
                        if (!packageName.equals(appProcess.processName) && appProcess.uid == uid) {
                            Process.killProcess(appProcess.pid);
//                            activityManager.killBackgroundProcesses(appProcess.processName);
                            Process.sendSignal(appProcess.pid, Process.SIGNAL_KILL);
                        }
                    }
                }
//                android.os.Process.sendSignal(Process.myPid(), android.os.Process.SIGNAL_KILL);
                Process.killProcess(Process.myPid());
//                System.exit(0);
            }
        } catch (Exception e) {

        }
    }


}
