package com.hpbr.bosszhipin.config.custom.core;

import androidx.annotation.FloatRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.custom.core.source.Source;

public interface Animated {

    default void setAnimation(@Nullable Source source) {
        setAnimation(Factory.defaultImpl(), source);
    }

    default void setAnimation(@NonNull Factory factory, @Nullable Source source) {

    }

    default void playAnimation() {

    }

    default void setRepeatCount(int count) {

    }

    default void setFrame(int frame) {

    }

    default void pauseAnimation() {

    }

    default void setMinAndMaxProgress(@FloatRange(from = 0f, to = 1f) float minProgress,
                                      @FloatRange(from = 0f, to = 1f) float maxProgress) {

    }

    default void setProgress(@FloatRange(from = 0f, to = 1f) float progress) {

    }

}
