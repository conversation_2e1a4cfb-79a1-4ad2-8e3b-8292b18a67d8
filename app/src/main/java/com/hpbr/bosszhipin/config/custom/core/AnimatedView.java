package com.hpbr.bosszhipin.config.custom.core;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;

import androidx.annotation.FloatRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.custom.core.binder.Binder;
import com.hpbr.bosszhipin.config.custom.core.source.Source;

public class AnimatedView extends FrameLayout implements Animated {

    @Nullable
    private Binder binder;

    public AnimatedView(@NonNull Context context) {
        this(context, null);
    }

    public AnimatedView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AnimatedView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void setAnimation(@NonNull Factory factory, @Nullable Source source) {
        if (source != null) {
            binder = factory.getBinder(source);
            binder.bind(this, source);
        } else {
            if (binder != null) {
                binder.bind(this, null);
                binder = null;
            }
        }
    }

    @Override
    public void playAnimation() {
        if (binder != null) {
            binder.playAnimation();
        }
    }

    @Override
    public void setRepeatCount(int count) {
        if (binder != null) {
            binder.setRepeatCount(count);
        }
    }

    @Override
    public void setFrame(int frame) {
        if (binder != null) {
            binder.setFrame(frame);
        }
    }

    @Override
    public void pauseAnimation() {
        if (binder != null) {
            binder.pauseAnimation();
        }
    }

    @Override
    public void setMinAndMaxProgress(@FloatRange(from = 0f, to = 1f) float minProgress,
                                     @FloatRange(from = 0f, to = 1f) float maxProgress) {
        if (binder != null) {
            binder.setMinAndMaxProgress(minProgress, maxProgress);
        }
    }

    @Override
    public void setProgress(@FloatRange(from = 0f, to = 1f) float progress) {
        if (binder != null) {
            binder.setProgress(progress);
        }
    }

}
