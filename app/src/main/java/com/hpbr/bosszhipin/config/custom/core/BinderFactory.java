package com.hpbr.bosszhipin.config.custom.core;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.hpbr.bosszhipin.config.custom.core.binder.JsonBinder;
import com.hpbr.bosszhipin.config.custom.core.binder.Binder;
import com.hpbr.bosszhipin.config.custom.core.binder.WebpBinder;
import com.hpbr.bosszhipin.config.custom.core.source.Source;

import java.util.Map;

public class BinderFactory implements Factory {

    public static final String JSON = ".json";
    public static final String WEBP = ".webp";

    @NonNull
    private final Map<String, Class<? extends Binder>> binderMap = new ArrayMap<String, Class<? extends Binder>>() {
        {
            put(JSON, JsonBinder.class);
            put(WEBP, WebpBinder.class);
        }
    };
    @NonNull
    private final Map<String, Binder> binderCache = new ArrayMap<>();

    @Override
    @NonNull
    public Binder getBinder(@NonNull Source source) {
        final String name = source.name();
        final String ext = source.ext();

        Binder cachedBinder = binderCache.get(name);
        if (cachedBinder != null) {
            return cachedBinder;
        }

        Class<? extends Binder> clazz = binderMap.get(ext);
        try {
            Binder newBinder = clazz.newInstance();
            binderCache.put(name, newBinder);
            return newBinder;
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }

        JsonBinder jsonBinder = new JsonBinder();
        binderCache.put(name, jsonBinder);

        return jsonBinder;
    }

}
