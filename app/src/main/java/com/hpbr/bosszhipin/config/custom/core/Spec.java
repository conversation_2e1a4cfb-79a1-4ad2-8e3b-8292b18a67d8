package com.hpbr.bosszhipin.config.custom.core;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;

public class Spec {

    public static final int REPEAT_COUNT_INFINITE = -1;
    public static final int REPEAT_COUNT_NONE = Integer.MIN_VALUE;

    private String name;
    private boolean isAutoPlay;
    private int repeatCount = REPEAT_COUNT_NONE;
    @IdRes
    private int defaultIdRes;

    private long fileSize;

    private Spec() {

    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public static Spec obj() {
        return new Spec();
    }

    public String getName() {
        return name;
    }

    public boolean isAutoPlay() {
        return isAutoPlay;
    }

    public Spec setName(@NonNull String name) {
        if (name.lastIndexOf(".") > 0) {
            name = name.substring(0, name.lastIndexOf("."));
        }
        this.name = name;
        return this;
    }

    public Spec setAutoPlay(boolean autoPlay) {
        isAutoPlay = autoPlay;
        return this;
    }

    public Spec setDefaultIdRes(@IdRes int defaultRes) {
        this.defaultIdRes = defaultRes;
        return this;
    }

    @IdRes
    public int getDefaultIdRes() {
        return defaultIdRes;
    }

    public int getRepeatCount() {
        return repeatCount;
    }

    public Spec setRepeatCount(int repeatCount) {
        this.repeatCount = repeatCount;
        return this;
    }
}
