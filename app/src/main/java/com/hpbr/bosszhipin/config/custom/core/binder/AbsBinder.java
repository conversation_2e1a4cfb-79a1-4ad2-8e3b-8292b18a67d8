package com.hpbr.bosszhipin.config.custom.core.binder;

import android.view.ViewGroup;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.custom.core.source.Source;

public abstract class AbsBinder implements Binder {

    @Nullable
    private Source source;

    @Override
    @CallSuper
    public void bind(@NonNull ViewGroup parent, @Nullable Source source) {
        this.source = source;
        parent.removeAllViews();
    }

    @Nullable
    public Source getSource() {
        return source;
    }

}
