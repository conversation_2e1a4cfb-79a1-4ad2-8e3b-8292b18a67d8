package com.hpbr.bosszhipin.config.custom.core.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.ViewParent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.airbnb.lottie.LottieAnimationView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.manager.LottieErrorManager;

import java.io.InputStream;

public class JsonBinder extends AbsBinder {

    private LottieAnimationView lottieAnimationView;

    private String playLottieName;
    private long fileSize;
    @Override
    public void bind(@NonNull ViewGroup parent, @Nullable Source source) {
        super.bind(parent, source);
        if (source == null) {
            if (lottieAnimationView != null) {
                ViewParent vp = lottieAnimationView.getParent();
                if (vp instanceof ViewGroup) {
                    ((ViewGroup) vp).removeView(lottieAnimationView);
                    lottieAnimationView = null;
                }
            }
        } else {
            if (lottieAnimationView == null) {
                lottieAnimationView = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_main_tab_lottie, parent, true).findViewById(R.id.lav);
                lottieAnimationView.setFailureListener(result -> {
                    String msg =result==null?"null":result.toString();
                    LottieErrorManager.getInstance().reportError(msg,playLottieName,fileSize);
                });
            } else {
                ViewParent vp = lottieAnimationView.getParent();
                if (vp instanceof ViewGroup) {
                    ((ViewGroup) vp).removeView(lottieAnimationView);
                }
                parent.addView(lottieAnimationView);
            }

            lottieAnimationView.setRepeatCount(source.repeatCount());
            if (source.autoPlay()) {
                lottieAnimationView.playAnimation();
            }

            Object o = source.src();
            if (o instanceof InputStream) {
                lottieAnimationView.setAnimation((InputStream) o, source.name());
            } else if (o instanceof String) {
                lottieAnimationView.setAnimation((String) o);
            } else if (o instanceof Integer) {
                lottieAnimationView.setImageResource((Integer) o);
            }
            playLottieName =source.name();
            fileSize =source.fileSize();
        }
    }

    @Override
    public void playAnimation() {
        super.playAnimation();
        if (lottieAnimationView != null) {
            lottieAnimationView.playAnimation();
        }
    }

    @Override
    public void setRepeatCount(int count) {
        super.setRepeatCount(count);
        if (lottieAnimationView != null) {
            lottieAnimationView.setRepeatCount(count);
        }
    }

    @Override
    public void setFrame(int frame) {
        super.setFrame(frame);
        if (lottieAnimationView != null) {
            lottieAnimationView.setFrame(frame);
        }
    }

    @Override
    public void pauseAnimation() {
        super.pauseAnimation();
        if (lottieAnimationView != null) {
            lottieAnimationView.pauseAnimation();
        }
    }

    @Override
    public void setMinAndMaxProgress(float minProgress, float maxProgress) {
        super.setMinAndMaxProgress(minProgress, maxProgress);
        if (lottieAnimationView != null) {
            lottieAnimationView.setMinAndMaxProgress(minProgress, maxProgress);
        }
    }

    @Override
    public void setProgress(float progress) {
        super.setProgress(progress);
        if (lottieAnimationView != null) {
            lottieAnimationView.setProgress(progress);
        }
    }

}
