package com.hpbr.bosszhipin.config.custom.core.binder;

import android.graphics.drawable.Animatable;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.ViewParent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.controller.BaseControllerListener;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.fresco.animation.drawable.AnimatedDrawable2;
import com.facebook.imagepipeline.image.ImageInfo;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.utils.LoopCountModifyingBackend;

public class WebpBinder extends AbsBinder {

    private SimpleDraweeView simpleDraweeView;

    @Override
    public void bind(@NonNull ViewGroup parent, @Nullable Source source) {
        super.bind(parent, source);
        if (source != null) {
            if (simpleDraweeView == null) {
                simpleDraweeView = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_main_tab_webp, parent, true).findViewById(R.id.lav);
            } else {
                ViewParent vp = simpleDraweeView.getParent();
                if (vp instanceof ViewGroup) {
                    ((ViewGroup) vp).removeView(simpleDraweeView);
                }
                parent.addView(simpleDraweeView);
            }

            bindAnimation(source.autoPlay(), source.repeatCount());
        } else {
            if (simpleDraweeView != null) {
                ViewParent vp = simpleDraweeView.getParent();
                if (vp instanceof ViewGroup) {
                    ((ViewGroup) vp).removeView(simpleDraweeView);
                    simpleDraweeView = null;
                }
            }
        }
    }

    @Override
    public void playAnimation() {
        super.playAnimation();
        if (simpleDraweeView != null) {
            Source source = getSource();
            if (source != null) {
                bindAnimation(true, source.repeatCount());
            }
        }
    }

    private void bindAnimation(boolean autoPlay, int repeatCount) {
        // https://frescolib.org/docs/supported-uris.html
        Source source = getSource();
        if (source != null) {
            Object o = source.src();
            if (o instanceof Uri) {
                DraweeController controller = Fresco.newDraweeControllerBuilder()
                        .setUri((Uri) o)
                        .setAutoPlayAnimations(autoPlay)
                        .setControllerListener(new BaseControllerListener<ImageInfo>() {
                            @Override
                            public void onFinalImageSet(String id,
                                                        ImageInfo imageInfo,
                                                        Animatable anim) {
                                if (anim instanceof AnimatedDrawable2) {
                                    final AnimatedDrawable2 ad2 = (AnimatedDrawable2) anim;
                                    ad2.setAnimationBackend(
                                            // 设置循环次数
                                            new LoopCountModifyingBackend(ad2.getAnimationBackend(), repeatCount)
                                    );
                                }
                            }
                        })
                        .build();
                simpleDraweeView.setController(controller);
            } else if (o instanceof Integer) {
                simpleDraweeView.setBackgroundResource((Integer) o);
            }
        }
    }

    @Override
    public void setFrame(int frame) {
        super.setFrame(frame);
        if (simpleDraweeView != null) {
            DraweeController controller = simpleDraweeView.getController();
            if (controller != null) {
                Animatable animatable = controller.getAnimatable();
                if (animatable instanceof AnimatedDrawable2) {
                    animatable.stop();
                    ((AnimatedDrawable2) animatable).jumpToFrame(frame);
                }
            }
        }
    }

    @Override
    public void setProgress(float progress) {
        super.setProgress(progress);

        if (simpleDraweeView != null) {
            Source source = getSource();
            if (source != null) {
                Object o = source.src();
                if (o instanceof Uri) {
                    DraweeController controller = Fresco.newDraweeControllerBuilder()
                            .setUri((Uri) o)
                            .setAutoPlayAnimations(false)
                            .setControllerListener(new BaseControllerListener<ImageInfo>() {
                                @Override
                                public void onFinalImageSet(String id, ImageInfo imageInfo, Animatable anim) {
                                    if (progress == 1) { // 跳到最后一帧
                                        if (anim instanceof AnimatedDrawable2) {
                                            final AnimatedDrawable2 ad2 = (AnimatedDrawable2) anim;
                                            ad2.setAnimationBackend(
                                                    new LoopCountModifyingBackend(
                                                            ad2.getAnimationBackend(),
                                                            1
                                                    )
                                            );

                                            try {
                                                int fc = ad2.getFrameCount();
                                                if (fc > 0) {
                                                    ad2.jumpToFrame(fc - 1);
                                                }
                                            } catch (Throwable e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                }
                            })
                            .build();
                    simpleDraweeView.setController(controller);
                } else if (o instanceof Integer) {
                    simpleDraweeView.setBackgroundResource((Integer) o);
                }
            }
        }
    }

}
