package com.hpbr.bosszhipin.config.custom.core.source;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.config.custom.core.Spec;

public abstract class AbsSource implements Source {

    @NonNull
    private final Spec spec;

    public AbsSource(@NonNull Spec spec) {
        this.spec = spec;
    }

    @NonNull
    @Override
    public String name() {
        return spec.getName();
    }

    @Override
    public long fileSize() {
        return spec.getFileSize();
    }

    @Override
    public boolean autoPlay() {
        return spec.isAutoPlay();
    }

    @Override
    public int repeatCount() {
        return spec.getRepeatCount();
    }

}
