package com.hpbr.bosszhipin.config.custom.core.source;

import androidx.annotation.NonNull;

import com.airbnb.lottie.LottieDrawable;
import com.hpbr.bosszhipin.config.custom.core.BinderFactory;
import com.hpbr.bosszhipin.config.custom.core.Spec;

public abstract class JsonSource extends AbsSource {

    public JsonSource(@NonNull Spec spec) {
        super(spec);
    }

    @NonNull
    @Override
    public String ext() {
        return BinderFactory.JSON;
    }

    @Override
    public int repeatCount() {
        int repeatCount = super.repeatCount();
        if (repeatCount == Spec.REPEAT_COUNT_INFINITE) {
            return LottieDrawable.INFINITE;
        } else if (repeatCount == Spec.REPEAT_COUNT_NONE) {
            return 0;
        } else if (repeatCount < 0) {
            return 0;
        }
        return repeatCount;
    }

}
