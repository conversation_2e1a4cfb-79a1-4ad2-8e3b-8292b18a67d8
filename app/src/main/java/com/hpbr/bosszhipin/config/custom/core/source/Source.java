package com.hpbr.bosszhipin.config.custom.core.source;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.custom.core.Spec;

public interface Source {

    /**
     * @return 资源文件后缀：.json/.webp
     */
    @NonNull
    String ext();

    /**
     * @return 资源名称（不含文件后缀）：niuren-dark
     */
    @NonNull
    String name();

    /**
     * @return 资源 FileInputStream/URI 等
     */
    @Nullable
    Object src();

    default int repeatCount() {
        return Spec.REPEAT_COUNT_NONE; // 默认不循环播放动画
    }

    default boolean autoPlay() {
        return false;
    }

    default long fileSize() {
        return 0L;
    }
}
