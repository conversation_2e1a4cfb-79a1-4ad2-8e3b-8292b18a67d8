package com.hpbr.bosszhipin.config.custom.core.source;

import androidx.annotation.NonNull;

import com.facebook.fresco.animation.backend.AnimationInformation;
import com.hpbr.bosszhipin.config.custom.core.BinderFactory;
import com.hpbr.bosszhipin.config.custom.core.Spec;

public abstract class WebpSource extends AbsSource {

    public WebpSource(@NonNull Spec spec) {
        super(spec);
    }

    @NonNull
    @Override
    public String ext() {
        return BinderFactory.WEBP;
    }

    @Override
    public int repeatCount() {
        int repeatCount = super.repeatCount();
        if (repeatCount == Spec.REPEAT_COUNT_INFINITE) {
            return AnimationInformation.LOOP_COUNT_INFINITE;
        } else if (repeatCount == Spec.REPEAT_COUNT_NONE) {
            return 1;
        }
        return repeatCount;
    }

}
