package com.hpbr.bosszhipin.config.custom.type;

import android.annotation.SuppressLint;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.hpbr.bosszhipin.common.AppMainThemeColorConfig.ColorType;
import com.hpbr.bosszhipin.config.custom.core.BinderFactory;
import com.hpbr.bosszhipin.config.custom.core.Spec;
import com.hpbr.bosszhipin.config.custom.core.source.JsonSource;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.config.custom.core.source.WebpSource;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.FileIOUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.GetThemeConfigResponse.ActivityConfig;
import net.bosszhipin.base.BaseFileDownloadCallback;
import net.bosszhipin.base.FileDownloadRequest;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Locale;

public abstract class AbsConfig implements Config {

    private boolean isSourceLoaded = false;
    private int fileType = 0;
    private String configDataPath;

    private int colorType = ColorType.THEME_ORIGIN;

    //region KEYS!!!
    @NonNull
    private String getTag() {
        return getPrefix() + "-AbsConfig";
    }

    @NonNull
    private String getConfigFolder() {
        return getPrefix();
    }

    @NonNull
    private String getConfigVersionKey() {
        return getPrefix() + "_version";
    }

    @NonNull
    private String getConfigFileTypeKey() {
        return getPrefix() + "_file_type";
    }

    @NonNull
    private String getConfigDataZipPathKey() {
        return getPrefix() + "_data_zip_path";
    }

    @NonNull
    private String getConfigDataPathKey() {
        return getPrefix() + "_data_path";
    }

    @NonNull
    private String getColorTypeKey() {
        return getPrefix() + "_color_type";
    }

    @NonNull
    private String getNeedShowKey() {
        return getPrefix() + "_need_show";
    }
    //endregion

    @Override
    public void parseConfig(@NonNull ActivityConfig activityConfig) {
        int localConfigVersion = getLocalDataConfigVersion();
        if (activityConfig.whetherHaveConfig) {
            //1109.904需求，后台修改为版本不一致就需要更新
            if (activityConfig.dataVersion != localConfigVersion) {
                removeConfig();

                String configFolderName = getConfigFolder();
                String parentPath = PathUtils.getCacheDirChildPathExternalFirst(configFolderName);
                String fileName = String.format(Locale.getDefault(), configFolderName + "_%d.zip", activityConfig.dataVersion);
                HttpExecutor.download(new FileDownloadRequest(activityConfig.fileUrl, parentPath, fileName, new BaseFileDownloadCallback() {
                    @Override
                    public void onFail(String url, ErrorReason reason) {
                        TLog.info(getTag(), "onFail====" + "url = [ %s ], reason = [ %s ]", url, reason);
                    }

                    @Override
                    public void onSuccess(String url, File file) {
                        TLog.info(getTag(), "url = %s, currentConfigVersion = %d, file = %s", url, activityConfig.dataVersion, file);
                        SpManager.get().global().edit()
                                .putBoolean(getNeedShowKey(), activityConfig.whetherShow)
                                .putInt(getConfigVersionKey(), activityConfig.dataVersion)
                                .putInt(getConfigFileTypeKey(), activityConfig.fileType)
                                .putString(getConfigDataZipPathKey(), file.getAbsolutePath())
                                .putInt(getColorTypeKey(), activityConfig.colorType)
                                .apply(); // 下载成功后保存当前的下载版本号
                    }
                }));
            } else{
                SpManager.get().global().edit()
                        .putBoolean(getNeedShowKey(), activityConfig.whetherShow)
                        .apply();
            }
        } else {
            removeConfig();
        }
    }

    /**
     * 获取本地当前的配置版本
     */
    @Override
    public int getLocalDataConfigVersion() {
        return SpManager.get().global().getInt(getConfigVersionKey(), 0);
    }

    @Override
    @SuppressLint("twl_utils_file")
    @WorkerThread
    public void sync() {
        boolean needShow = SpManager.get().global().getBoolean(getNeedShowKey(), false);
        TLog.info(getTag(), "sync   needShow = %b  ", needShow);
        // 不需要显示
        if (!needShow) return;

        // 版本号
        final int version = SpManager.get().global().getInt(getConfigVersionKey(), 0);
        fileType = SpManager.get().global().getInt(getConfigFileTypeKey(), 0);
        configDataPath = SpManager.get().global().getString(getConfigDataPathKey(), "");

        TLog.info(getTag(), "sync   colorType = %d", colorType);

        isSourceLoaded = false;

        if (!TextUtils.isEmpty(configDataPath)) {
            File configDataDir = new File(configDataPath);
            if (FileUtils.isFileExists(configDataDir)) {
                List<File> listFiles = FileUtils.listFilesInDir(configDataDir);
                isSourceLoaded = checkFileIntegrity(listFiles);
            }
        }

        if (!isSourceLoaded) {
            String configDataZipPath = SpManager.get().global().getString(getConfigDataZipPathKey(), "");
            if (!TextUtils.isEmpty(configDataZipPath)) {
                File configDataZipFile = new File(configDataZipPath);
                if (FileUtils.isFileExists(configDataZipFile)) {
                    File configDataDir = PathUtils.getFilesDirInternalChildFile(getConfigFolder() + "_" + version);
                    File configDataTempDir = FileUtils.getFileByPath(configDataDir.getAbsolutePath() + "_temp");
                    if (configDataTempDir != null) {
                        try {
                            long unzipStartTime = System.currentTimeMillis();
                            List<File> files = FileIOUtils.upZipOnlyFile(configDataZipFile, configDataTempDir.getAbsolutePath(), false);
                            if (checkFileIntegrity(files) && FileUtils.isFileExists(configDataTempDir)) {
                                isSourceLoaded = configDataTempDir.renameTo(configDataDir);
                                configDataPath = configDataDir.getAbsolutePath();

                                // 解压后的路径
                                SpManager.get().global().edit().putString(getConfigDataPathKey(), configDataDir.getAbsolutePath()).apply();
                            }
                            TLog.info(getTag(), "sync   time = %d , version = %d, isLoad = %b, Path = %s", (System.currentTimeMillis() - unzipStartTime), version, isSourceLoaded, configDataDir.getAbsolutePath());
                        } catch (IOException e) {
                            e.printStackTrace();
                            TLog.error(getTag(), e, "sync");
                        }
                    }
                } else {
                    removeConfig();
                }
            }
        }

        if (isSourceLoaded) { // 动图资源加载成功，才获取色值信息，主题需要保持一致
            colorType = SpManager.get().global().getInt(getColorTypeKey(), 0);
        } else {
            colorType = ColorType.THEME_ORIGIN;
        }

        TLog.info(getTag(), "sync finished: isSourceLoaded = %b, colorType = %d", isSourceLoaded, colorType);
    }

    @Override
    public void removeConfig() {
        String pageTopPath = SpManager.get().global().getString(getConfigDataPathKey(), "");
        String pageTopZipPath = SpManager.get().global().getString(getConfigDataZipPathKey(), "");
        FileUtils.delete(pageTopPath);
        FileUtils.delete(pageTopZipPath);
        SpManager.get().global().edit()
                .remove(getConfigDataZipPathKey())
                .remove(getConfigDataPathKey())
                .remove(getConfigVersionKey())
                .remove(getConfigFileTypeKey())
                .remove(getColorTypeKey())
                .apply();
    }

    @NonNull
    @Override
    public Source getSource(@NonNull Spec spec) {
        Source source = buildSource(spec, false);
        return source != null ? source : new WebpSource(spec) {
            @NonNull
            @Override
            public Object src() {
                return spec.getDefaultIdRes();
            }
        };
    }

    @Nullable
    public Source buildSource(@NonNull Spec spec, boolean ignoreColorType) {
        final String fileName = spec.getName();

        TLog.debug(getTag(), "getSource isLoad = %b, Path = %s, fileName = %s", isSourceLoaded, configDataPath, fileName);

        @IdRes final int defaultRes = spec.getDefaultIdRes();

        if (isSourceLoaded && !TextUtils.isEmpty(configDataPath)
                && (ignoreColorType || colorType > ColorType.THEME_ORIGIN)) {
            if (fileType == 1) {
                //region WEBP
                File webpFile = new File(configDataPath, fileName + BinderFactory.WEBP);
                if (FileUtils.isFileExists(webpFile)) { // 判断 webp 文件是否存在
                    return new WebpSource(spec) {
                        @Nullable
                        @Override
                        public Object src() {
                            return Uri.fromFile(webpFile);
                        }
                    };
                }
                //endregion
            } else if (fileType == 0) {
                //region JSON
                File lottieFile = new File(configDataPath, fileName + BinderFactory.JSON);

                if (FileUtils.isFileExists(lottieFile)) { // 判断 json 文件是否存在
                    long size = lottieFile.length();
                    spec.setFileSize(size);
                    return new JsonSource(spec) {
                        @NonNull
                        @Override
                        public Object src() {
                            /*
                                com.hpbr.bosszhipin.config.custom.core.binder.JsonBinder
                                    会处理返回是 InputStream 还是 String 还是 Integer
                             */

                            try {
                                return new FileInputStream(lottieFile);
                            } catch (Throwable e) {
                                e.printStackTrace();
                            }

                            String dap = getDefaultJsonAssertPath(fileName);
                            return dap != null ? dap : defaultRes;
                        }
                    };
                }
                //endregion
            }
        }

        return null;
    }

    @Override
    public int getColorType() {
        return colorType;
    }

    @NonNull
    public String getPrefix() {
        // 前缀取自子类文件名，子类可覆写该方法，注意避免文件名冲突！
        final String regex = "([a-z0-9])([A-Z])";
        final String replacement = "$1_$2";
        return getClass().getSimpleName().replaceAll(regex, replacement).toLowerCase();
    }

    @Nullable
    public String getDefaultJsonAssertPath(@NonNull String fileName) {
        return null;
    }

}
