package com.hpbr.bosszhipin.config.custom.type;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.hpbr.bosszhipin.common.AppMainThemeColorConfig.ColorType;
import com.hpbr.bosszhipin.config.custom.core.Spec;
import com.hpbr.bosszhipin.config.custom.core.source.Source;

import net.bosszhipin.api.GetThemeConfigResponse.ActivityConfig;

import java.io.File;
import java.util.List;

public interface Config {

    void parseConfig(@NonNull ActivityConfig activityConfig);

    void removeConfig();

    int getLocalDataConfigVersion();

    @WorkerThread
    void sync();

    default boolean checkFileIntegrity(@NonNull List<File> files) {
        return files.size() > 0;
    }

    @NonNull
    Source getSource(@NonNull Spec spec);

    default int getColorType() {
        return ColorType.THEME_ORIGIN;
    }

}
