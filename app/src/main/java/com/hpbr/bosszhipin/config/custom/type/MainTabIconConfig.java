package com.hpbr.bosszhipin.config.custom.type;

import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.custom.core.BinderFactory;
import com.hpbr.bosszhipin.config.custom.core.Spec;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.config.custom.core.source.WebpSource;

/**
 * 活动类型 0:tabbarIcon配置
 */
public class MainTabIconConfig extends AbsConfig {

    @Override
    @NonNull
    public Source getSource(@NonNull Spec spec) {
        @Nullable Source source = buildSource(spec, true);
        return source != null ? source : new WebpSource(spec) {
            @NonNull
            @Override
            public Object src() {
                return Uri.parse("asset:///webp/tabbar_icon/" + (spec.getName() + BinderFactory.WEBP));
            }
        };
    }

    @Nullable
    @Override
    public String getDefaultJsonAssertPath(@NonNull String fileName) {
        // 兜底用。示例：lottie/tabbar_icon/niuren.json
        return "lottie/tabbar_icon/" + (fileName + BinderFactory.JSON);
    }

}
