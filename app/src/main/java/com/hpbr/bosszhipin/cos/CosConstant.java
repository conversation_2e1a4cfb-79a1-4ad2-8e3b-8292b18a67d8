package com.hpbr.bosszhipin.cos;

import android.annotation.SuppressLint;

import androidx.annotation.StringDef;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class CosConstant {

    /**
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=*********
     */
    public static class AppName implements Serializable {
        //公司视频/品牌视频
        private static final String COMPANY_NAME = "zhipin-company";
        //片头视频转码/直播片头/代播片头
        private static final String LIVE_NAME = "bosslive";
        //有了社区/工作环境
        private static final String MOMENT_NAME = "zhipin-moment";
        //泰坦星/泰坦星作业/泰坦星课程
        private static final String TITAN_NAME = "titan";
        //视频简历
        private static final String GEEK_NAME = "zhipin-geek";
        //视频招呼
        private static final String GEEK_GREET_NAME = "zhipin-video-greet";
        //用户反馈
        private static final String USER_NAME = "zhipin-user";
        // VR
        private static final String BRAND_VR_NAME = "brand-vr";
        // 地址认证
        private static final String ADDRESS_VERIFY_NAME = "zhipin-cert-vr";
        //日志
        private static final String APP_CLIENT_LOG_NAME = "app-client-log";
        //面试
        private static final String APP_INTERVIEW_NAME = "zhipin-interview-inspect";


        //公司视频/品牌视频
        public static final AppName COMPANY = new AppName(COMPANY_NAME, false);
        //片头视频转码/直播片头/代播片头
        public static final AppName LIVE = new AppName(LIVE_NAME, false);
        //有了社区/有了视频/工作环境
        public static final AppName MOMENT = new AppName(MOMENT_NAME, false);
        //泰坦星/泰坦星作业/泰坦星课程
        public static final AppName TITAN = new AppName(TITAN_NAME, true);
        @SuppressLint("WrongConstant")
        public static final AppName INTEVIEW = new AppName(APP_INTERVIEW_NAME, false);

        //视频简历
        public static final AppName GEEK = new AppName(GEEK_NAME, true);
        //视频招呼
        public static final AppName GEEK_GREET = new AppName(GEEK_GREET_NAME, true);
        //用户反馈
        public static final AppName USER = new AppName(USER_NAME, true);
        //VR
        public static final AppName BRAND_VR = new AppName(BRAND_VR_NAME, false);
        //地址认证
        public static final AppName ADDRESS_VERIFY = new AppName(ADDRESS_VERIFY_NAME, false);
        //log
        public static final AppName APP_CLIENT_LOG = new AppName(APP_CLIENT_LOG_NAME, false);


        private static final long serialVersionUID = 7607200691035533782L;

        private final String appName;
        private final boolean isSecret;

        public AppName(@APP_NAME String appName, boolean isSecret) {
            this.appName = appName;
            this.isSecret = isSecret;
        }

        public String getAppName() {
            return appName;
        }

        public boolean isSecret() {
            return isSecret;
        }

    }


    @StringDef({AppName.COMPANY_NAME, AppName.GEEK_NAME, AppName.GEEK_GREET_NAME, AppName.LIVE_NAME, AppName.MOMENT_NAME, AppName.TITAN_NAME, AppName.USER_NAME, AppName.BRAND_VR_NAME,
            AppName.APP_CLIENT_LOG_NAME, AppName.ADDRESS_VERIFY_NAME})
    @Retention(RetentionPolicy.SOURCE)
    public @interface APP_NAME {
    }
}
