package com.hpbr.bosszhipin.cos;


import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.utils.UploadFileUtil;
import com.hpbr.utils.platform.Utils;
import com.kanzhun.zpcloud.Constants;
import com.kanzhun.zpcloud.NebulaUploadListener;
import com.kanzhun.zpcloud.NebulaUploadManager;
import com.kanzhun.zpcloud.data.NebulaUploadInfo;
import com.kanzhun.zpcloud.data.NebulaUploadNetworkType;
import com.kanzhun.zpcloud.data.NebulaUploadStatus;
import com.kanzhun.zpcloud.engine.UploadRequest;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.FileUtils;

import net.bosszhipin.api.FileUploadResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.io.File;

/**
 * 上传视频
 * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=157185214
 */
public class CosUploadHelper {

    public static final String TAG = "CosUploadHelper";
    private final UploadFileResponse uploadFileResponse = new UploadFileResponse();
    private final UploadFileInfo uploadFileInfo;
    private UploadRequest uploadRequest;

    public CosUploadHelper(UploadFileInfo uploadFileInfo) {
        this.uploadFileInfo = uploadFileInfo;
        this.uploadFileResponse.filePath = uploadFileInfo.getFilePath();
    }

    public CosUploadHelper uploadFile(@NonNull FileUploadCallback callback) {
        requestCredentialThenUpload(callback);
        return this;
    }

    private void requestCredentialThenUpload(@NonNull FileUploadCallback callback) {
        SimpleApiRequest.GET(URLConfig.URL_ZP_COMMON_VIDEO_GETUPLOAD_INFO)
                .addParam("appName", uploadFileInfo.getAppName())
                .addParam("md5", uploadFileInfo.getMD5())
                .addParam("fileName", uploadFileInfo.getFileName())
                .addParam("secret", uploadFileInfo.isSecret() ? 1 : 0)
                .setRequestCallback(new SimpleApiRequestCallback<UploadInfoResult>() {
                    @Override
                    public void handleInChildThread(ApiData<UploadInfoResult> data) {
                        super.handleInChildThread(data);
                        //视频文件上传 需要生成封面图
                        if (uploadFileInfo.isVideoFile() && !uploadFileInfo.isNotNeedCover()) {
                            uploadFileResponse.duration = FileUtils.getVideoFileDuration(Utils.getApp(), new File(uploadFileInfo.getFilePath()));
                            uploadVideoThumbnail(callback);
                        } else { // 上传文件
                            uploadFileResponse.success();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<UploadInfoResult> data) {
                        uploadFile(data.resp, callback);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        uploadFileResponse.setFailure(reason.getErrCode(), reason.getErrReason());
                        uploadFileResponse.forceComplete();
                        publishComplete(callback);
                    }
                }).execute();
    }

    private void publishComplete(@NonNull FileUploadCallback callback) {
        if (uploadFileResponse.requestCount >= 0) {
            callback.onPublishComplete(uploadFileResponse);
        }
    }

    @WorkerThread
    private void uploadVideoThumbnail(@NonNull FileUploadCallback callback) {
        if (uploadFileResponse.coverUrl != null) {
            uploadFileResponse.success();
            return;
        }

        if (uploadFileInfo.getCoverPath() == null) {
            uploadFileInfo.setCoverPath(FileUtils.getVideoThumbnail(Utils.getApp(), uploadFileResponse.filePath));
        }

        if (uploadFileInfo.getCoverPath() == null) {
            uploadFileResponse.setFailure(-1, "视频缩略图生成失败");
            return;
        }
        UploadFileUtil.uploadImage(uploadFileInfo.getCoverSource(), new File(uploadFileInfo.getCoverPath()), new ApiRequestCallback<FileUploadResponse>() {
            @Override
            public void handleInChildThread(ApiData<FileUploadResponse> data) {
                super.handleInChildThread(data);
                uploadFileResponse.coverPath = uploadFileInfo.getCoverPath();
                uploadFileResponse.coverUrl = data.resp.url;
                uploadFileResponse.relativeUrl = data.resp.relativeUrl;
                if (uploadFileResponse.coverUrl != null) {
                    uploadFileResponse.success();
                } else {
                    uploadFileResponse.setFailure(-1, "视频缩略图上传失败");
                }

            }

            @Override
            public void onSuccess(ApiData<FileUploadResponse> data) {
            }

            @Override
            public void onComplete() {
                publishComplete(callback);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                uploadFileResponse.setFailure(reason.getErrCode(), reason.getErrReason());
                cancel();
            }
        });
    }

    private void uploadFile(@NonNull UploadInfoResult uploadInfoResult, @NonNull FileUploadCallback callback) {
        if (uploadInfoResult.cosInfo == null) {
            callback.onPublishComplete(uploadFileResponse.setFailure(-2, "系统参数异常"));
            return;
        }
        //用于埋点
        uploadFileResponse.reportFileId = uploadInfoResult.fileId;

        NebulaUploadInfo nebulaUploadInfo = new NebulaUploadInfo();
        //本地上传参数
        nebulaUploadInfo.setFilePath(uploadFileInfo.getFilePath());
        nebulaUploadInfo.setAppName(uploadFileInfo.getAppName());
        nebulaUploadInfo.setUserId(uploadFileInfo.getUserId());
//        nebulaUploadInfo.setAppType(uploadFileInfo.getAppType());
        nebulaUploadInfo.setHostUrl(uploadFileInfo.getHostUrl());

        //以下透传参数
        nebulaUploadInfo.setAppId(uploadInfoResult.appId);
        nebulaUploadInfo.setSig(uploadInfoResult.signature);
        nebulaUploadInfo.setAuth(uploadInfoResult.authorization);
        nebulaUploadInfo.setFileID(uploadInfoResult.fileId);
        nebulaUploadInfo.setRemoteFilePath(uploadInfoResult.filePath);
        nebulaUploadInfo.setOssType(uploadInfoResult.ossType);

        if (uploadInfoResult.ossType == 0) {
            nebulaUploadInfo.setUploadId(uploadInfoResult.zpOssInfo.uploadId);
            nebulaUploadInfo.setExternalUrl(uploadInfoResult.zpOssInfo.externalUrl);
            nebulaUploadInfo.setUploadType(uploadInfoResult.zpOssInfo.uploadType);
            nebulaUploadInfo.setPartMaxNums(uploadInfoResult.zpOssInfo.partMaxNums);
            nebulaUploadInfo.setPartMaxSize(uploadInfoResult.zpOssInfo.partMaxSize);
            nebulaUploadInfo.setFileMd5(uploadFileInfo.getMD5());
            nebulaUploadInfo.setComplete(TextUtils.equals(uploadInfoResult.zpOssInfo.isComplete, "true"));
        } else {
            nebulaUploadInfo.setSecretID(uploadInfoResult.cosInfo.secretId);
            nebulaUploadInfo.setSecretKey(uploadInfoResult.cosInfo.secretKey);
            nebulaUploadInfo.setToken(uploadInfoResult.cosInfo.token);
            nebulaUploadInfo.setRegionName(uploadInfoResult.cosInfo.region);
            nebulaUploadInfo.setBucketName(uploadInfoResult.cosInfo.bucket);
            nebulaUploadInfo.setBucketFilePath(uploadInfoResult.filePath);
            nebulaUploadInfo.setStartTime(uploadInfoResult.cosInfo.startDate);
            nebulaUploadInfo.setExpiredTime(uploadInfoResult.cosInfo.expirationDate);
        }


        SimpleNebulaUploadListener nebulaUploadListener = new SimpleNebulaUploadListener(callback);
        if (uploadFileInfo.isVideoFile()) {
            uploadRequest = NebulaUploadManager.shareInstance().createVideoUploadRequest(Utils.getApp(), nebulaUploadInfo, nebulaUploadListener);
        } else {
            uploadRequest = NebulaUploadManager.shareInstance().createFileUploadRequest(Utils.getApp(), nebulaUploadInfo, nebulaUploadListener);
        }
        if (null == uploadRequest) {
            TLog.error(TAG, "createUploadRequest is null");
            callback.onPublishComplete(uploadFileResponse.setFailure(-3, "创建上传请求 失败"));
            ApmAnalyzer.create().action(TAG, "onError").p2(uploadFileInfo.getAppName())
                    .p3("-1")
                    .p4(nebulaUploadInfo.toString())
                    .p5(String.valueOf(uploadFileInfo)).report();
            return;
        }
        uploadRequest.start();
    }


    public int cancel() {
        if (uploadRequest != null) {
            uploadRequest.cancel();
            return 1;
        }
        return 0;
    }

    public void pause() {
        if (uploadRequest != null) {
            uploadRequest.pause();
        }
    }

    private class SimpleNebulaUploadListener extends NebulaUploadListener {

        @NonNull
        FileUploadCallback videoFileUploadCallback;

        public SimpleNebulaUploadListener(@NonNull FileUploadCallback videoFileUploadCallback) {
            this.videoFileUploadCallback = videoFileUploadCallback;
        }

        @Override
        public void onWarning(int code, String msg, String detail) {
            TLog.debug(TAG, "onWarning code= %d  msg= %s detail= %s", code, msg, detail);
        }

//        @Override
//        public void onError(int code, String msg, String detail) {
//            TLog.error(TAG, "onError code= %d  msg= %s detail= %s",code,msg,detail);
//            uploadFileResponse.setFailure(code, msg);
//            ApmAnalyzer.create().action(TAG,"onError").p2(uploadFileInfo.getAppName())
//                    .p3(String.valueOf(code))
//                    .p4("msg = "+msg + " detail = "+detail)
//                    .p5(String.valueOf(uploadFileInfo)).report();
//        }

        @Override
        public void onNetworkChange(NebulaUploadNetworkType nebulaUploadNetworkType) {
//            switch (nebulaUploadNetworkType) {
//                case NUNETWORK_CELLULAR:
//                    ToastUtils.showText("发现变成手机流量，为节约用户流量，停止上传！");
//                    break;
//                case NUNETWORK_NONE:
//                    ToastUtils.showText("用户已经断网");
//                    break;
//                case NUNETWORK_WIFI:
//                    ToastUtils.showText("用户已经切换成WIFI");
//                    break;
//                default:
//                    break;
//            }
        }

        @Override
        public void onProgress(int progress, long uploadBytes, long totalBytes) {
            TLog.debug(TAG, "onProgress==== progress = %d uploadBytes= %d totalBytes = %d", progress, uploadBytes, totalBytes);
            videoFileUploadCallback.onPublishProgress(uploadBytes, totalBytes);
        }

        @Override
        public void onResult(int code, String msg, String detail) {
            uploadRequest = null;
            if (Constants.Code.SUCCESS.code() == code) {
//                ToastUtils.showText("上传成功");
                uploadFileResponse.fileId = getNebulaUploadInfo().getFileID();
                uploadFileResponse.bucketFilePath = getNebulaUploadInfo().getBucketFilePath();
                uploadFileResponse.success();
                publishComplete(videoFileUploadCallback);
            } else if (Constants.Code.COS_USER_CANCEL.code() == code) {

            } else {
                uploadFileResponse.setFailure(code, "上传失败，请重新上传");
                publishComplete(videoFileUploadCallback);
                ApmAnalyzer.create().action(TAG, "onResult").p2(uploadFileInfo.getAppName())
                        .p3(String.valueOf(code))
                        .p4("msg = " + msg + " detail = " + detail)
                        .p5(String.valueOf(uploadFileInfo)).report();
            }
            TLog.info(TAG, "onResult code= %d  msg= %s detail= %s uploadFileResponse= %s", code, msg, detail, uploadFileResponse);
        }


        @Override
        public void onStateChange(NebulaUploadStatus status) {
            TLog.debug(TAG, "onStateChange====" + "status = [" + status + "]");
        }
    }
}
