package com.hpbr.bosszhipin.cos;


import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.utils.UploadFileUtil;
import com.kanzhun.zpcloud.exception.NebulaClientException;
import com.kanzhun.zpcloud.util.NebulaDigestUtils;

import java.io.File;

public class UploadFileInfo {

    private String appName;
    private String filePath;
    private String coverPath;
    private boolean isSecret;
    private boolean notNeedCover;//是否需要封面 true 不需要 false 需要

    private String hostUrl = HostConfig.getNebulaUrl();
    private String userId = String.valueOf(AccountHelper.getUid());
    private String appType = "boss-app-android";

    private String coverSource = UploadFileUtil.VIDEO;
    private boolean isVideoFile;

    public static UploadFileInfo FILE(String appName) {
        return new UploadFileInfo().setAppName(appName);
    }

    public static UploadFileInfo VIDEO(String appName) {
        return new UploadFileInfo().setAppName(appName).setVideoType(true);
    }

    public static UploadFileInfo FILE(CosConstant.AppName appName) {
        return new UploadFileInfo().setAppName(appName.getAppName()).setSecret(appName.isSecret());
    }

    public static UploadFileInfo VIDEO(CosConstant.AppName appName) {
        return new UploadFileInfo().setAppName(appName.getAppName()).setSecret(appName.isSecret()).setVideoType(true);
    }


    public String getAppName() {
        return appName;
    }

    public UploadFileInfo setAppName(String appName) {
        this.appName = appName;
        return this;
    }

    public String getCoverSource() {
        return coverSource;
    }

    public UploadFileInfo setCoverSource(String coverSource) {
        this.coverSource = coverSource;
        return this;
    }

    public UploadFileInfo setCoverPath(String coverPath) {
        this.coverPath = coverPath;
        return this;
    }

    public String getCoverPath() {
        return coverPath;
    }

    public String getFilePath() {
        return filePath;
    }

    public UploadFileInfo setFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public boolean isVideoFile() {
        return isVideoFile;
    }

    public UploadFileInfo setVideoType(boolean isVideoFile) {
        this.isVideoFile = isVideoFile;
        return this;
    }

    public boolean isNotNeedCover() {
        return notNeedCover;
    }

    public UploadFileInfo setNotNeedCover(boolean notNeedCover) {
        this.notNeedCover = notNeedCover;
        return this;
    }

    public String getFileName() {
        return new File(filePath).getName();
    }

    public String getMD5() {
        try {
            return NebulaDigestUtils.getMD5(getFilePath());
        } catch (NebulaClientException e) {
            e.printStackTrace();
        }
        return "";
    }

    public boolean isSecret() {
        return isSecret;
    }

    public UploadFileInfo setSecret(boolean secret) {
        isSecret = secret;
        return this;
    }

    public String getHostUrl() {
        return hostUrl;
    }

    public UploadFileInfo setHostUrl(String hostUrl) {
        this.hostUrl = hostUrl;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public UploadFileInfo setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getAppType() {
        return appType;
    }
//
//   public UploadFileInfo setAppType(String appType) {
//      this.appType = appType;
//      return this;
//   }


    @Override
    public String toString() {
        return "UploadFileInfo{" +
                "appName='" + appName + '\'' +
                ", filePath='" + filePath + '\'' +
                ", coverPath='" + coverPath + '\'' +
                ", isSecret=" + isSecret +
                ", hostUrl='" + hostUrl + '\'' +
                ", userId='" + userId + '\'' +
                ", appType='" + appType + '\'' +
                ", isVideoFile=" + isVideoFile +
                '}';
    }
}
