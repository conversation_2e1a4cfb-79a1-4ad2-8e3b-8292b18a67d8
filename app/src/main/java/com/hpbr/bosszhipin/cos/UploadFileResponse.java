package com.hpbr.bosszhipin.cos;

import java.io.Serializable;

public class UploadFileResponse implements Serializable {
    private static final long serialVersionUID = 6686589895016618113L;
    public String filePath;                                              // 原始路径
    public int requestCount;                                                  //错误码
    public int retCode;                                                  //错误码
    public String descMsg;                                                  //错误描述信息
    public String fileId;                                                  //视频文件Id
    public String bucketFilePath;                                           //视频上传后的路径
    public String coverUrl;                                                 //封面存储地址
    public String coverPath;                                                 //封面存储路径
    public long duration;  //本地视频 时长
    public String relativeUrl;
    public String reportFileId;//埋点用的 文件id

    public UploadFileResponse() {
        retCode = -2;
        requestCount = -2;
    }

    public UploadFileResponse setFailure(int retCode, String descMsg) {
        this.retCode = retCode;
        this.descMsg = descMsg;
        requestCount++;
        return this;
    }

    public void forceComplete() {
        requestCount = 0;
    }

    public boolean isSuccess() {
        return retCode == 0;
    }

    public UploadFileResponse success(String fileId) {
        this.fileId = fileId;
        retCode++;
        requestCount++;
        return this;
    }

    public UploadFileResponse success() {
        retCode++;
        requestCount++;
        return this;
    }

    @Override
    public String toString() {
        return "UploadFileResponse{" +
                "retCode=" + retCode +
                "requestCount=" + requestCount +
                ", filePath='" + filePath + '\'' +
                ", fileId='" + fileId + '\'' +
                ", duration='" + duration + '\'' +
                ", bucketFilePath='" + bucketFilePath + '\'' +
                ", coverURL='" + coverUrl + '\'' +
                ", descMsg='" + descMsg + '\'' +
                '}';
    }
}