package com.hpbr.bosszhipin.cos;


import net.bosszhipin.base.HttpResponse;

import java.io.Serializable;


public class UploadInfoResult extends HttpResponse {
    private static final long serialVersionUID = 5186651599213697670L;

    public String fileId; // "D451DF2B424F4B429DC04369B4D24E39",//文件Id
    public String appId;  //"zhipin-company",
    public String signature;
    public String authorization;
    public String filePath;  // "zhipin-company/7/20220609/6455936_a6787950169845ad9473bdf112952f9a-008.mp4",//文件上传路径
    public int ossType;   //oss类型
    public ZpOssInfo zpOssInfo;
    public CosInfo cosInfo;

    public static class ZpOssInfo implements Serializable {
        private static final long serialVersionUID = 5758331743926245567L;
        public String innerUrl;  //内网上传地址
        public String externalUrl;//外网上传地址
        public String uploadType;//文件上传方式，simpleUpload: 简单上传，multiPartUpload: 分片上传
        public String uploadId;//分片上传时，需要的uploadId
        public int partMaxNums;//分片数量
        public int partMaxSize;//分片大小
        public String isComplete;// 是否合并完成true|false

    }

    public static class CosInfo implements Serializable {
        private static final long serialVersionUID = -5881129197248554746L;
        public String secretId;
        public String secretKey;
        public String token;
        public String bucket;
        public String region;
        public String endPoint;
        public long startDate;
        public long expirationDate;

    }

}
