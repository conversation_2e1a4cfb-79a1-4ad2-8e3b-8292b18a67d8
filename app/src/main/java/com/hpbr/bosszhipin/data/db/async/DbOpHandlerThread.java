package com.hpbr.bosszhipin.data.db.async;

import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.MessageQueue;
import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.dao.DaoFactory;
import com.hpbr.bosszhipin.data.db.dao.IContactDao;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactQueueManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactHandleManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.mms.utils.ExceptionUtils;
import com.twl.mms.utils.TWLException;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import message.handler.analysis.ChatAnalyzer;


/**
 * Created by wangtian on 16/9/18.
 */
public class DbOpHandlerThread extends HandlerThread {

    private static DbOpHandlerThread mInstance = new DbOpHandlerThread("analytics_db_op_handlerthread");
    private final IContactDao contactDao;
    private HandlerThread mHandlerThread = null;
    private ConnectHandler mHandler = null;
    private String TAG = "dbop";


    public static DbOpHandlerThread getInstance() {
        return mInstance;
    }

    private DbOpHandlerThread(String name) {
        super(name);
        mHandlerThread = new HandlerThread(name);
        mHandlerThread.start();
        mHandler = new ConnectHandler(mHandlerThread.getLooper());
        contactDao = DaoFactory.getContactDao();
    }

    public Message obtainMessage(int what) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        return message;
    }

    public Message obtainMessage(int what, Object object) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        message.obj = object;
        return message;
    }

    public Message obtainMessage(int what,int arg1, Object object) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        message.arg1 = arg1;
        message.obj = object;
        return message;
    }

    public void sendMessageAtFrontOfQueue(int what, Object object) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        message.obj = object;
        sendMessageAtFrontOfQueue(message);
    }

    public void sendMessageAtFrontOfQueue(int what, int arg1, Object object) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        message.arg1 = arg1;
        message.obj = object;
        sendMessageAtFrontOfQueue(message);
    }


    public void sendMessage(Message msg) {
        mHandler.sendMessage(msg);
    }

    public void sendMessageAtFrontOfQueue(Message msg) {
        mHandler.sendMessageAtFrontOfQueue(msg);
    }


//    private void sendBroadCast(String action) {
//        Intent intent = new Intent(action);
//        App.get().sendBroadcast(intent);
//    }

    public void addIdleHandler(MessageQueue.IdleHandler idleHandler) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && idleHandler != null) {
                Looper.getMainLooper().getQueue().addIdleHandler(idleHandler);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "addIdleHandler = %s", idleHandler);
        }
    }

    public void removeIdleHandler(MessageQueue.IdleHandler idleHandler) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && idleHandler != null) {
                Looper.getMainLooper().getQueue().removeIdleHandler(idleHandler);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "removeIdleHandler = %s", idleHandler);
        }
    }

    public int getUnHandlerCount() {
      return mHandler.getUnHandlerCount();
    }


    class ConnectHandler extends Handler {
        private int HANDLE_TIME_OUT;
        ConnectHandler(Looper looper) {
            super(looper);
            HANDLE_TIME_OUT = AndroidDataStarGray.getInstance().getContactHandleTimeOut();
        }

        private long startTime;
        private long startTimeBatchContact;
        private final AtomicInteger msgCount = new AtomicInteger(0);
        private final Map<String, Integer> countContact = new ConcurrentHashMap<>();
        private int level = 0;
        private final int STEP = 500;
        private final int MONITOR_TIMEOUT = 500;

        private boolean isAnalyzeMessageCount(Message msg) {
            return msg.getTarget() != null && msg.what != DbOpType.OP_CONTACT_NOTIFY_UI && msg.what != DbOpType.OP_TRANSACTION_MONITOR;
        }

        @Override
        public boolean sendMessageAtTime(@NonNull Message msg, long uptimeMillis) {
            analyticSendMessage(msg); //记录发起任务
            return super.sendMessageAtTime(msg, uptimeMillis);
        }

        private void analyticSendMessage(@NonNull Message msg) {
            boolean monitor = isAnalyzeMessageCount(msg);  //屏蔽消息屏障类型消息
            if (monitor) {
                int curCount;
                if (msg.what == DbOpType.OP_CONTACT_INSERT_OR_UPDATE_V2) {
                    String key = getKey(msg);
                    Integer count = countContact.get(key);
                    if (count != null) {
                        count++;
                    } else {
                        count = 1;
                    }
                    countContact.put(key, count);
                } else {
                    msgCount.incrementAndGet();
                }
                curCount = countContact.size() + msgCount.get();
                if (curCount <= 1) {
                    LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_UNHANDLED).put("status", "start").put("msgCount", curCount).info();
                }
            }
        }

        private @NonNull String getKey(@NonNull Message msg) {
            return String.valueOf(msg.obj.hashCode());
        }

        @Override
        public void dispatchMessage(@NonNull Message msg) {
            long startTime = SystemClock.uptimeMillis();
            super.dispatchMessage(msg);
            analyticHandlerMessage(msg, startTime); //结束任务统计
        }

        private void analyticHandlerMessage(@NonNull Message msg, long startTime) {
            long delay = startTime - msg.getWhen();
            long total = SystemClock.uptimeMillis() - startTime;
            boolean isNotIgnore = msg.getWhen() > 0 && isAnalyzeMessageCount(msg);
            if (isNotIgnore) {// 排除 sendMessageAtFrontOfQueue 场景 when = 0
                if (msg.what == DbOpType.OP_CONTACT_INSERT_OR_UPDATE_V2) {
                    countContact.remove(getKey(msg));
                } else {
                    msgCount.decrementAndGet();
                }
                int count = countContact.size() + msgCount.get();
                int msgLevel = count / STEP;
                // 1、消息处理时间大于 500ms
                // 2、消息出来结束
                // 3、消息处理一个阶段 1000、500、0
                if (total > MONITOR_TIMEOUT || count <= 0 || level != msgLevel) { // 处理完了
                    this.level = msgLevel;
                    LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_UNHANDLED)
                            .put("status", count <= 0 ? "end" : "ing")
                            .put("what", msg.what)
                            .put("delay", delay)
                            .put("msgCount", count)
                            .put("time", total)
                            .info();
                }

                if (HANDLE_TIME_OUT > 0 && delay > HANDLE_TIME_OUT * 1000L && delay < 3 * HANDLE_TIME_OUT * 1000L && total < 5000L) { //过滤进程被冻结情况，
                    HANDLE_TIME_OUT = 0;
                    ApmAnalyzer.create().action("action_contact_doctor", "contact_handle_timeout").p2(String.valueOf(delay)).p3(String.valueOf(count)).p4(String.valueOf(total)).p5(String.valueOf(App.get().isForeground())).debug().report();
                }
            }

        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case DbOpType.OP_CONTACT_NOTIFY_UI:
                    removeMessages(DbOpType.OP_CONTACT_NOTIFY_UI);
                    ContactManager.getInstance().refreshContacts();
                    return; //注意这里是刷新， 楼下 sendMessage 需要return
                case DbOpType.OP_CONTACT_DELETE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.deleteContact(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_INSERT_OR_UPDATE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.insertOrUpdateAllField(param.getObject());
                        DbOpCallback<Void> callback = param.getCallback();
                        if (callback != null) {
                            callback.success(null);
                        }
                    }
                    break;
                case DbOpType.OP_CONTACT_INSERT_OR_UPDATE_V2:
                    if (msg.obj instanceof ContactBean) {
                        removeMessages(msg.what, msg.obj);
                        contactDao.insertOrUpdateAllField((ContactBean) msg.obj);
                    }
                    break;
                case DbOpType.OP_BATCH_CONTACT_INSERT_OR_UPDATE_V2:
                    handleBatchInsertOrUpdateV2();
                    break;

                case DbOpType.OP_CONTACT_UPDATE_BASE_INFO:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.upsertContactServerField(param.getObject(), param.getObject().myRole);
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.insertOrUpdateServerField(param.getObject(), param.getObject().myRole);
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_LAST_TEXT:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateLastText(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_DIRECT_CALL_STATUS:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateDirectCallStatus(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_PHONE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updatePhone(param.getObject());
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_WX_REMIND:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateWxRemind(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_SECURITY_ID:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateSecurityId(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_WECHAT:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateWeChat(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_HAS_JUMP_CHAT:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateJumpToChatField(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_IS_FILTERD:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateIsFiltered(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_EXCHANGE_PHONE_TIME:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateExchangePhoneTime(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_EXCHANGE_WECHAT_TIME:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateExchangeWechatTime(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_EXCHANGE_ANNEX_RESUME_TIME:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateExchangeAnnexResumeTime(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_UPDATE_TIME:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateUpdateTime(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_EXCHANGE_INTERVIEW_TIME:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateInviteInterviewTime(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_VIDEO_INTERVIEW:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateVideoInterview(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_INTERVIEW_STATUS:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateInterviewStatus(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_LAST_TEXT_STATUS:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateLastTextStatus(param.getObject(), param.getObject().lastChatClientMessageId);
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_UNREAD_COUNT:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateUnreadCount(param.getObject());
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_NAME_AND_HEAD_URL:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateNameAndHeadUrl(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_DRAFT:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateDraft(param.getObject());
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_INTERVIEW_SCHEDULE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateInterviewSchedule(param.getObject());
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_LABEL:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateLabel(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_IS_TOP:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateTop(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_IS_OPEN_NO_DISTURB:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateIsNoDisturb(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_NOTE:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateNote(param.getObject());
                    }
                    break;

                case DbOpType.OP_CONTACT_UPDATE_WARNING_TIPS:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateWarningTip(param.getObject());
                    }
                    break;
                case DbOpType.OP_CONTACT_UPDATE_FRIEND_COMPANIES:
                    if (msg.obj != null) {
                        DbOpParam<List<ContactBean>, Void> param = (DbOpParam<List<ContactBean>, Void>) msg.obj;
                        contactDao.updateFriendCompanies(param.getObject());
                    }
                    ContactDoctorFactory.checkAllContact(AccountHelper.getIdentity());
                    break;
                case DbOpType.OP_CONTACT_UPDATE_SYNC_GEEK_INFO:
                    if (msg.obj != null) {
                        DbOpParam<ContactBean, Void> param = (DbOpParam<ContactBean, Void>) msg.obj;
                        contactDao.updateSyncGeekInfo(param.getObject());
                    }
                    break;
                case DbOpType.OP_TRANSACTION_MONITOR:
                    LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                            .put("monitor", msg.arg1)
                            .put("time", msg.getWhen() > 0 ? (SystemClock.uptimeMillis() - msg.getWhen()) : 0)
                            .info();
                    if (msg.arg1 == 1) { // start
                        SpManager.get().user().edit().putLong("updateBaseContactInfoV2", msg.arg1).apply();
                    } else if (msg.arg1 > 1) { // end
                        SpManager.get().user().edit().remove("updateBaseContactInfoV2").apply();
                    }
                    break;
                case DbOpType.OP_MONITOR_MESSAGE_HANDLE:
                    if (msg.obj instanceof DbOpParam) {
                        DbOpParam<Object,Message> param = (DbOpParam<Object,Message>) msg.obj;
                        DbOpCallback<Message> callback = param.getCallback();
                        if (callback!=null) {
                            callback.success(msg);
                        }
                    }
                    break;
                case DbOpType.OP_TRANSACTION_START:
                    startTime = System.currentTimeMillis();
                    SpManager.get().user().edit().putLong("updateBaseContactInfo", msg.arg1).apply();
                    TLog.info("contact", "OP_TRANSACTION_START updateBaseContactInfo count = %d tag = %s",msg.arg1, msg.obj);
                    break;
                case DbOpType.OP_TRANSACTION_END:
                    long time_tag = (long) msg.obj;
                    long totalTime = System.currentTimeMillis() - startTime;
                    if (totalTime > 3000) {
                        ApmAnalyzer.create().action("action_contact", "base_info_list_save")
                                .p2(String.valueOf(msg.arg1))
                                .p3(String.valueOf(totalTime))
                                .p4(String.valueOf(System.currentTimeMillis() - time_tag))
                                .debug()
                                .report();
                    }
                    SpManager.get().user().edit().remove("updateBaseContactInfo").apply();
                    TLog.info("contact", "OP_TRANSACTION_END updateBaseContactInfo  count = %d  tag = %s time = %d", msg.arg1,msg.obj, System.currentTimeMillis() - time_tag);
                    break;
                case DbOpType.OP_BATCH_UPDATE_UNREAD_COUNT_CHAT_STATUS:
                    if (msg.obj != null) {
                        try {
                            DbOpParam<List<ContactBean>, Void> param = (DbOpParam<List<ContactBean>, Void>) msg.obj;
                            List<ContactBean> contactBeanList = param.getObject();
                            int updateCount = contactDao.batchUpdateUnreadCountAndLastChatStatus(contactBeanList);
                            TLog.info(TAG, "batch update read info, count = %d", updateCount);
                        } catch (Exception e) {
                            TLog.error(TAG, "OP_BATCH_UPDATE_UNREAD_COUNT_CHAT_STATUS fail, error msg = %s", e);
                        }
                    }
                    break;
                 //-----------------------------------------------getBaseInfoList联系人操作start-----------------------------------------------
                case DbOpType.OP_BATCH_CONTACT_INSERT_CONTACTS:
                    //批量插入联系人
                    if (msg.obj != null) {
                        long curTime = SystemClock.elapsedRealtime();
                        DbOpParam<List<ContactBean>, Void> param = (DbOpParam<List<ContactBean>, Void>) msg.obj;
                        int realSaveCount = 0;
                        List<ContactBean> insertList = new ArrayList<>();
                        if(LList.getCount(param.getObject()) > 0){
                            /*过滤掉id>0的数据*/
                            for(ContactBean contactBean : param.getObject()){
                                if(contactBean.id ==0){
                                    insertList.add(contactBean);
                                }else{
                                    TLog.error(ContactHandleManager.TAG, "sameId==%d , sameFriendId==%d, sameMyRole==%d , sameFriendSource==%d , sameMyId==%d", contactBean.id, contactBean.friendId, contactBean.myRole, contactBean.friendSource, contactBean.myId);
                                }
                            }
                            realSaveCount = contactDao.updateContacts(insertList);
                        }
                        TLog.info(ContactHandleManager.TAG, "insertList==%d, realInsertList==%d, realSaveCount==%d , time==%d ", LList.getCount(param.getObject()), LList.getCount(insertList), realSaveCount, SystemClock.elapsedRealtime() - curTime);
                    }
                    break;
                case DbOpType.OP_BATCH_CONTACT_UPDATE_CONTACTS:
                    //批量更新联系人
                    if (msg.obj != null) {
                        long curTime = SystemClock.elapsedRealtime();
                        DbOpParam<List<ContactBean>, Void> param = (DbOpParam<List<ContactBean>, Void>) msg.obj;

                        if(LList.getCount(param.getObject()) > 0){
                            try {
                                int realUpdateCount = contactDao.updateBaseInfoContacts(param.getObject());
                                TLog.info(ContactHandleManager.TAG, "updateList==%d,  realUpdateCount== %d, time==%d  ", LList.getCount(param.getObject()), realUpdateCount, SystemClock.elapsedRealtime() - curTime);
                            } catch (Exception e) {
                                ChatAnalyzer.reportNew(ChatAnalyzer.BASE_INFO_CRASH).p2("insert update").p3(e.getMessage()).report();
                                TLog.info(ContactHandleManager.TAG, "update insert error %s", e.getMessage());
                            }

                        }
                    }
                    break;
                case DbOpType.OP_BATCH_CONTACT_NEW_TRANSACTION_START:
                    startTimeBatchContact = SystemClock.elapsedRealtime();
                    SpManager.get().user().edit().putLong("updateBaseContactInfo", ContactHandleManager.KEY_SAVE_CONTACT_ERROR).apply();
                    break;
                case DbOpType.OP_BATCH_CONTACT_NEW_TRANSACTION_END_V2:
                    //校验联系人是否存储完成，下次进入会校验并且上报
                    SpManager.get().user().edit().remove("updateBaseContactInfo").apply();

                    if(null!=msg.obj){
                        int insertCount = msg.arg1;
                        int updateCount = (int) msg.obj;
                        long insertAndUpdateCostTime = SystemClock.elapsedRealtime() - startTimeBatchContact;
                        /*存储时间超过3s才上报*/
                        if(insertAndUpdateCostTime > 3000){
                            ApmAnalyzer.create().action("action_contact_doctor", "type_sync_page_contact_time")
                                    .p2(String.valueOf(insertCount))
                                    .p3(String.valueOf(updateCount))
                                    .p4(String.valueOf(insertAndUpdateCostTime))
                                    .debug().report();
                            ChatAnalyzer.reportNew(ChatAnalyzer.INSERT_UPDATE_CONTACT_TIME).p2(String.valueOf(insertCount)).p3(String.valueOf(updateCount)).p4(String.valueOf(insertAndUpdateCostTime)).report();
                        }
                        TLog.info(ContactHandleManager.TAG, "insertAndUpdateCostTime==%d,  totalCount== %d ", insertAndUpdateCostTime, updateCount + insertCount);
                    }
                    break;
                case DbOpType.OP_BATCH_CONTACT_DELETE_CONTACTS:
                    if (msg.obj != null) {
                        try {
                            int role = msg.arg1;
                            if (UserManager.getUserRole(role) != UserManager.getUserRole()) {
                                TLog.info(ContactHandleManager.TAG, "user switch role when check delete contact");
                                return;
                            }
                            long curTime = SystemClock.elapsedRealtime();
                            DbOpParam<List<ContactBean>, Void> param = (DbOpParam<List<ContactBean>, Void>) msg.obj;
                            List<ContactBean> deleteList = param.getObject();
                            int deleteCount = LList.getCount(deleteList);
                            if (deleteCount > 0) {
                                SpManager.get().user().edit().putLong(ContactHandleManager.key_delete_contact_error_sp, LList.getCount(param.getObject())).apply();
                                int realDelCount = 0;
                                //部分机型在特殊运行环境中，超过1000会删除失败，报错「Message over limit siz...too long be cutted」
                                // https://bugly.qq.com/v2/crash-reporting/errors/65d92ea8a7/25875005/report?pid=1&bundleId=&channelId=&version=all&tagList=&start=0&deviceId=&cpuArch=&date=last_7_day
                                //因此超过500数量的数据，按500分块删除
                                for (int i = 0; i < deleteCount; i = i + 500) {
                                    List<ContactBean> newDeleteList;
                                    if (deleteList.size() >= i + 500) {
                                        newDeleteList = deleteList.subList(i, i + 500);
                                    } else {
                                        newDeleteList = deleteList.subList(i, deleteCount);
                                    }
                                    int deleteResult = contactDao.deleteContacts(newDeleteList);
                                    realDelCount = realDelCount + deleteResult;
                                    TLog.info(ContactHandleManager.TAG, "for delete contact realDelCount==%d, now i==%d", realDelCount, i);
                                }
                                SpManager.get().user().edit().remove(ContactHandleManager.key_delete_contact_error_sp).apply();
                                String dbVersion = contactDao.getDBVersion();
                                TLog.info(ContactHandleManager.TAG, "deleteList==%d, realDelCount==%d , time==%d ,dbVersion==%s", LList.getCount(param.getObject()), realDelCount, SystemClock.elapsedRealtime() - curTime, dbVersion);

                                /*删除时间大于2s才上报*/
                                if((SystemClock.elapsedRealtime() - curTime) > 2000){
                                    ChatAnalyzer.reportNew(ChatAnalyzer.BATCH_DELETE_CONTACT_TIME)
                                            .p2(String.valueOf(LList.getCount(param.getObject())))
                                            .p3(String.valueOf(realDelCount))
                                            .p4(String.valueOf(SystemClock.elapsedRealtime() - curTime))
                                            .p5("dbVersion=="+dbVersion)
                                            .report();
                                }

                            }
                            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_SYNC_DEL_CONTACT)
                                    .put("status", "end").put("friendCount",LList.getCount(deleteList)).put("gray","true").put("time",SystemClock.elapsedRealtime() - curTime).info();
                        } catch (Exception e) {
                            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_SYNC_DEL_CONTACT)
                                    .put("status", "error").put("gray","true").put("message",e.getMessage()).info();
                            ChatAnalyzer.reportNew(ChatAnalyzer.BASE_INFO_CRASH).p2("delete contact").p3(e.getMessage()).report();
                            ExceptionUtils.postCatchedException(new TWLException(10086, new Exception("base info delete contact:" + e.getMessage())));
                        }
                    }
                    break;
                case DbOpType.OP_CONTACT_CHECK_SAME_DELETE_NOT_CACHE_V2:
                    try {
                        int role = msg.arg1;
                        //--如果切身份了，就释放掉--
                        DbOpParam<List<ContactBean>, Void> param = null!= msg.obj ?  (DbOpParam<List<ContactBean>, Void>) msg.obj : null;
                        if(UserManager.getUserRole(role) != UserManager.getUserRole()) {
                            TLog.info(ContactHandleManager.TAG, "v2-user switch role when check same contact");
                            return;
                        }
                        //1、db获取重复联系人
                        long startTime = SystemClock.elapsedRealtime();
                        List<ContactBean> sameContactBeans = contactDao.checkSameContacts();

                        //2、删除重复联系人，保留缓存中的联系人
                        StringBuilder delBuilder = new StringBuilder();
                        StringBuilder aliveBuilder = new StringBuilder();
                        StringBuilder logBuilder = new StringBuilder();

                        int totalPrint = 0;
                        for (ContactBean delContactBean : sameContactBeans) {
                            //和数据库一致，删除第一条之后的数据「删除的非缓存中的数据，如果重复数据在原数据后插入，下次重新进入就会加载之后插入的重复数据，会将原数据删除，造成F2列表最后一条消息丢失-」
                            List<ContactBean> delContactList = contactDao.querySameContactsForBaseInfo(delContactBean.friendId, delContactBean.myRole, delContactBean.friendSource, delContactBean.id);
                            contactDao.deleteContacts(delContactList);
                            if(null!=delContactList){
                                for(ContactBean contactBean : delContactList){
                                    if(null!=contactBean){
                                        delBuilder.append("id=").append(contactBean.id).append(",").append("friendId=").append(contactBean.friendId).append(",");
                                        if (totalPrint < 20) {
                                            totalPrint++;
                                            //log  重复联系人的第一个id+重复数量
                                            String log = String.format(Locale.getDefault(),"id==%d, originFriendId==%d ,friendId==%d, myId==%d ,myRole==%d, friendSource==%d", contactBean.id, delContactBean.friendId,
                                                    contactBean.friendId, contactBean.myId, contactBean.myRole, contactBean.friendSource);
                                            logBuilder.append(log).append(".v2.");
                                            TLog.error(ContactHandleManager.TAG, "id==%d, originFriendId==%d ,friendId==%d, myId==%d ,myRole==%d, friendSource==%d", contactBean.id, delContactBean.friendId,
                                                    contactBean.friendId, contactBean.myId, contactBean.myRole, contactBean.friendSource);
                                        }
                                    }
                                }
                                aliveBuilder.append("v2-id=").append(delContactBean.id).append(",").append("friendId=").append(delContactBean.friendId).append(",");
                            }
                        }

                        //3、log
                        if (logBuilder.length() > 0) {
                            long costTime = SystemClock.elapsedRealtime() - startTime;
                            String log = "v2-delete same contacts count=="+LList.getCount(sameContactBeans)+"， insertList="+LList.getCount(param.getObject())+ ",costTime =="+costTime+"\ndelete==" + delBuilder + "\nalive==" + aliveBuilder +"\nreportLong=="+logBuilder;
                            TLog.info(ContactHandleManager.TAG, log);
                            ChatAnalyzer.reportNew(ChatAnalyzer.CHECK_SAME_CONTACTS)
                                    .p2(logBuilder.toString())
                                    .p3(String.valueOf(costTime))
                                    .p4("v2")
                                    .p5(String.valueOf(LList.getCount(param.getObject())))
                                    .p6(String.valueOf(LList.getCount(sameContactBeans)))
                                    .report();
                        } else {
                            TLog.info(ContactHandleManager.TAG, "no same contacts v2");
                        }
                    } catch (Exception e) {
                        ChatAnalyzer.reportNew(ChatAnalyzer.BASE_INFO_CRASH).p2("same contact v2").p3(e.getMessage()).report();
                        ExceptionUtils.postCatchedException(new TWLException(10086, new Exception("same contact v2:" + e.getMessage())));
                    }
                    break;
                //-----------------------------------------------getBaseInfoList联系人操作end-----------------------------------------------
                default:
                    break;
            }
            TLog.debug(TAG, "DbOpHandlerThread handleMessage end %s", msgCount.get());
            //降低频率 新UI 才发送通知
            sendEmptyMessageDelayed(DbOpType.OP_CONTACT_NOTIFY_UI, msgCount.get() < 2 ? 50 : 500); // 优化用户体验，当挤压消息时，再做节流
        }

        /**
         * 处理批量插入更新V2操作
         * 由ContactQueueManager发送过来的批量数据
         */
        private void handleBatchInsertOrUpdateV2() {
            Set<ContactBean> batchSet = ContactQueueManager.getInstance().getAll();
            if (batchSet == null || batchSet.isEmpty()) {
                return;
            }

            long startTime = SystemClock.elapsedRealtime();
            int size = AndroidDataStarGray.getInstance().getContactInsertUpdateBatchSize();

            size = size <= 0 ? 50 : size; // 兜底
            Iterator<ContactBean> iterator = batchSet.iterator();
            List<ContactBean> batchList = new ArrayList<>(size);
            while (iterator.hasNext()) {
                ContactBean contactBean = iterator.next();
                if (contactBean != null) {
                    batchList.add(contactBean);
                }
                if (batchList.size() >= size) {
                    executeBatchInsertOrUpdateV2(batchList);
                    batchList.clear();
                }
            }

            if (!batchList.isEmpty()) {
                executeBatchInsertOrUpdateV2(batchList);
            }

            long duration = SystemClock.elapsedRealtime() - startTime;
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_BATCH_EXECUTE)
                    .put("operationType", "OP_CONTACT_INSERT_OR_UPDATE_V2")
                    .put("batchSize", batchSet.size())
                    .put("duration", duration)
                    .info();

            //平均每条消息处理时间超过 size ms上报
            if(duration / batchSet.size() > size) {
                ApmAnalyzer.create().action("action_contact_doctor", "batchUpdateTimeout")
                        .p2(String.valueOf(duration))
                        .p3(String.valueOf(batchList.size()))
                        .report();
            }

        }

        /**
         * 执行批量插入更新V2操作
         */
        private void executeBatchInsertOrUpdateV2(List<ContactBean> batchList) {
            if (batchList == null || batchList.isEmpty()) {
                return;
            }

            long startTime = SystemClock.elapsedRealtime();

            long affectedRows = contactDao.batchUpdateContacts(batchList);
            long duration = SystemClock.elapsedRealtime() - startTime;

            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_BATCH_EXECUTE)
                    .put("operationType", "OP_CONTACT_INSERT_OR_UPDATE_V2")
                    .put("batchSize", batchList.size())
                    .put("affectedRows", affectedRows)
                    .put("duration", duration)
                    .put("success", affectedRows > 0)
                    .info();

            if (affectedRows <= 0) {
                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_BATCH_EXECUTE)
                        .put("operationType", "OP_CONTACT_INSERT_OR_UPDATE_V2")
                        .put("batchSize", batchList.size())
                        .put("duration", duration)
                        .put("success", false)
                        .info();
                // 批量失败时，回退到单个执行
                fallbackToIndividualExecution(batchList);
            }
        }

        /**
         * 批量操作失败时的回退处理
         */
        private void fallbackToIndividualExecution(List<ContactBean> failedOperations) {
            TLog.info(TAG, "Falling back to individual execution for %d operations", failedOperations.size());

            for (ContactBean contactBean : failedOperations) {
                try {
                    contactDao.insertOrUpdateAllField(contactBean);
                } catch (Exception e) {
                    TLog.error(TAG, e, "Individual fallback execution failed for friendId=%d", contactBean.friendId);
                }
            }
        }

        public int getUnHandlerCount() {
            return  countContact.size() + msgCount.get();
        }
    }
}
