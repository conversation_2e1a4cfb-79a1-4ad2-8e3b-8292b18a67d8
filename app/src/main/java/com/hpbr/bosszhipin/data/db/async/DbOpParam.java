package com.hpbr.bosszhipin.data.db.async;

/**
 * Created by wa<PERSON><PERSON> on 16/9/18.
 */
public class DbOpParam<T,V> {
    private T object;
    private DbOpCallback<V> callback;

    public DbOpParam(T object,DbOpCallback<V> callback) {
        this.object = object;
        this.callback = callback;
    }

    public T getObject() {
        return object;
    }

    public DbOpCallback<V> getCallback() {
        return callback;
    }
}
