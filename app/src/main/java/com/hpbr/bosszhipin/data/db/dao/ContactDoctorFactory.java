package com.hpbr.bosszhipin.data.db.dao;

import android.app.Activity;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Looper;
import android.os.Message;
import android.os.MessageQueue;
import android.os.SystemClock;
import android.text.TextUtils;

import androidx.annotation.AnyThread;
import androidx.annotation.WorkerThread;

import com.bszp.kernel.DataKernel;
import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.chat.db.ChatDatabaseHelper;
import com.bszp.kernel.chat.db.ContactDao;
import com.bszp.kernel.chat.service.ChatHelper;
import com.bszp.kernel.utils.WCDBCheckRunnable;
import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.DBConfig;
import com.hpbr.bosszhipin.data.db.async.DbOpCallback;
import com.hpbr.bosszhipin.data.db.async.DbOpHandlerThread;
import com.hpbr.bosszhipin.data.db.async.DbOpParam;
import com.hpbr.bosszhipin.data.db.async.DbOpType;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactHandleManager;
import com.hpbr.bosszhipin.data.manager.contact.DefaultRefreshStrategyV2;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.listener.Callback;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.UploadLog;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.orm.db.annotation.PrimaryKey;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.mms.service.AppStatus;
import com.twl.utils.GsonUtils;
import com.twl.utils.file.FileUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

import message.handler.analysis.ChatAnalyzer;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDoctorFactory;
import message.server.SyncOverTaskManager;

public class ContactDoctorFactory {
    public static final String TAG = "ContactDoctor";
    public static final int VERSION = 1;
    public static final String MIGRATION_CONTACT = "migration_contact";
    public static final String MIGRATION_CONTACT_ERROR = "migration_contact_error";
    public static final String MIGRATION_GROUP = "migration_group";
    public static final String FIRST_APP_STARTUP = "first_app_startup";
    private static final ContactDaoKernel contactDaoKernel = new ContactDaoKernel();
    private static final ContactDaoImpl contactDao = new ContactDaoImpl();
    private static final GroupInfoDaoKernel groupInfoDaoKernel = new GroupInfoDaoKernel();
    private static final GroupInfoDaoImpl groupInfoDao = new GroupInfoDaoImpl();
    private static final GroupMemberDaoKernel groupMemberDaoKernel = new GroupMemberDaoKernel();
    private static final GroupMemberDao groupMemberDao = new GroupMemberDao();

    public static void checkContactValid(final ContactBean contact) {
        if (!DaoFactory.isUserChatService) {
            return;
        }
        addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                ContactDoctorFactory.checkAllContact(AccountHelper.getIdentity());
                removeIdleHandler(this);
                return false;
            }
        });


        if (!isMigrationContact()) {
            TLog.debug(TAG, "isMigrationContact = false");
            return;
        }

       addIdleHandler(new MessageQueue.IdleHandler() {
            @Override
            public boolean queueIdle() {
                for (int i = 0; i < 3; i++) {
                    if (checkSyncContactValid(contact,i > 1)) {
                        break;
                    }
                    SystemClock.sleep(1000);
                }
                removeIdleHandler(this);
                return false;
            }
        });

    }

    static void addIdleHandler(MessageQueue.IdleHandler idleHandler) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && idleHandler != null) {
                Looper.getMainLooper().getQueue().addIdleHandler(idleHandler);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "addIdleHandler = %s", idleHandler);
        }
    }

    static void removeIdleHandler(MessageQueue.IdleHandler idleHandler) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && idleHandler != null) {
                Looper.getMainLooper().getQueue().removeIdleHandler(idleHandler);
            }
        } catch (Exception e) {
            TLog.error(TAG, e, "removeIdleHandler = %s", idleHandler);
        }
    }
    private static boolean checkSyncContactValid(ContactBean contact, boolean isReport) {
        long startTime = System.currentTimeMillis();
        ContactBean contactBean = contactDao.findContact(contact.friendId, contact.friendSource, contact.myRole);
        TLog.debug(TAG, "contactDao findContact = time = %d", System.currentTimeMillis() - startTime);
        String contactJson = toJson(contactBean);
        startTime = System.currentTimeMillis();
        final ContactBean contactEntity = contactDaoKernel.findContact(contact.friendId, contact.friendSource, contact.myRole);
        TLog.debug(TAG, "contactDaoKernel findContact = time = %d", System.currentTimeMillis() - startTime);

        if (contactBean != null && contactEntity != null) {
            contactEntity.id = contactBean.id;
        }
        final String contactEntityJson = toJson(contactEntity);

        final boolean equals = TextUtils.equals(contactEntityJson, contactJson);
        if (equals) {
            TLog.debug(TAG, "checkContactValid = Contact is same contactBean %s", contactEntityJson);
        } else {
            if (isReport) {
                ApmAnalyzer.create().action(TAG, "checkContact").p2(contactJson).p3(contactEntityJson).p9(String.valueOf(VERSION)).report();
                postDelayUploadLog();
            }
            TLog.error(TAG, "checkContactValid = Contact is Diff contactBean = %s \n contactEntity = %s", contactJson, contactEntityJson);
        }
        return equals;
    }

    static volatile boolean isChecking = false;
    static final int CHECK_ERROR_COUNT = 5;

    @AnyThread
    public static void checkContactsValid() {
        if (!DaoFactory.isUserChatService) {
            return;
        }
        if (!isMigrationContact()) {
            TLog.debug(TAG, "isMigrationContact = false");
            return;
        }
        if (isChecking) { //执行忽略
            return;
        }
        isChecking = true;
        AppThreadFactory.POOL.execute(new WCDBCheckRunnable("checkContactsValid") {
            @Override
            protected void execute() {
                if (!isMigrationContact()) {
                    TLog.debug(TAG, "isMigrationContact = false");
                    isChecking = false;
                    return;
                }
                int identity = AccountHelper.getIdentity();
                final long contactCount = contactDao.getContactCount(identity);
                final long contactCount1 = contactDaoKernel.getContactCount(identity);
                final long contactDelCount = contactDaoKernel.queryDelContactCount();
                if (contactCount1 > 0 && contactCount != contactCount1) {
                    final long contactCount2 = contactDaoKernel.getContactCountWithFriendId();
                    if (contactCount != contactCount2) {
                        int checkErrorCount = SpManager.get().user().getInt(MIGRATION_CONTACT_ERROR, 0);
                        ApmAnalyzer.create().action(TAG, "checkContacts")
                                .p2(String.valueOf(contactCount))
                                .p3(String.valueOf(contactCount1))
                                .p4(String.valueOf(contactCount2))
                                .p5(String.valueOf(checkErrorCount))
                                .p6(String.valueOf(contactDelCount))
                                .p9(String.valueOf(VERSION)).report();
                        if (checkErrorCount > CHECK_ERROR_COUNT) {
                            if (identity == AccountHelper.getIdentity()) {
                                contactDaoKernel.deleteContact();
                                SpManager.get().user().edit().remove(MIGRATION_CONTACT).apply();
                                TLog.error(TAG, "checkContactValid = Contact is Diff error#count = %d  clear all Contact",checkErrorCount);
                            } else {
                                TLog.error(TAG, "checkContactValid = identity is Diff");
                            }
                        }
                        SpManager.get().user().edit().putInt(MIGRATION_CONTACT_ERROR, checkErrorCount + 1).apply();
                    } else {
                        SpManager.get().user().edit().remove(MIGRATION_CONTACT_ERROR).apply();
                    }
                    TLog.error(TAG, "checkContactValid = Contact is Diff contactCount = %d  kernel#contactCount = %d duplicate#contactCount = %d contactDelCount = %d", contactCount, contactCount1,contactCount2,contactDelCount);
                    postDelayUploadLog();
                } else {
                    SpManager.get().user().edit().remove(MIGRATION_CONTACT_ERROR).apply();
                    TLog.info(TAG, "checkContactValid = Contact is same contactCount = %d  kernel#contactCount = %d contactDelCount = %d", contactCount, contactCount1,contactDelCount);
                }
                isChecking = false;
            }
        });
    }

    @WorkerThread
    public static void checkAllContact(int identity) {
        if (BuildInfoUtils.isDebug()) {
            if (!DaoFactory.isUserChatService) {
                return;
            }
            try {
                List<ContactBean> allContactList = contactDao.getAllContactList(identity);
                List<ContactBean> allContactList1 = contactDaoKernel.getAllContactList(identity);
                if (LList.isEmpty(allContactList) || LList.isEmpty(allContactList1)) {
                    return;
                }
                TLog.debug(TAG, "allContactList = %d allContactList1 = %d ", LList.getCount(allContactList), LList.getCount(allContactList1));

                Comparator<ContactBean> comparator = (o1, o2) -> {
                    if (o1.friendId == o2.friendId) {
                        return o1.friendSource - o2.friendSource;
                    }
                    return (int) (o1.friendId - o2.friendId);
                };
                Collections.sort(allContactList, comparator);
                Collections.sort(allContactList1, comparator);
                for (int i = 0; i < allContactList.size(); i++) {
                    ContactBean bean = allContactList.get(i);
                    ContactBean bean1 = allContactList1.get(i);
                    String toJson = toJson(bean);
                    String toJson1 = toJson(bean1);
                    if (!TextUtils.equals(toJson, toJson1)) {
                        TLog.debug(TAG, "friendId = %d friendId1 = %d ", bean.friendId, bean1.friendId);
                        TLog.error(TAG, "checkContactValid = Contacts is Diff \ncontact = %s \ncontact1 = %s", toJson, toJson1);
                    }
                }
                TLog.info(TAG, "checkContactValid = Contacts is finish");
            } catch (Exception e) {
                TLog.error(TAG,e,"checkAllContact");
            }

        }
    }

    /**
     * @return true 迁移完成，false 还未迁移
     */
    private static boolean isMigrationContact() {
        return SpManager.get().user().getInt(MIGRATION_CONTACT, 0) > 0;
    }

    private static boolean isMigrationGroup() {
        if (migrationGroup == -1) {
            migrationGroup = SpManager.get().user().getInt(MIGRATION_GROUP, 0);
        }
        return migrationGroup > 0;
    }

    private static int migrationGroup = -1; //群聊数据 老db是复用的， 新DB是两套，所以聊天数据同步时，有差异
    public static void markMigrationGroup() {
        if (!DaoFactory.isUserChatService) {
            return;
        }
        if (isMigrationGroup()) {
            return;
        }
        TLog.info(TAG, "markMigrationGroup");
        AppThreadFactory.POOL.execute(new WCDBCheckRunnable("markMigrationGroup") {
            @Override
            protected void execute() {
                long startTime = System.currentTimeMillis();
                final List<GroupInfoBean> groupInfoBeans = groupInfoDaoKernel.queryGroupList();
                TLog.info(TAG, "start markMigrationGroup count= %d", LList.getCount(groupInfoBeans));
                ChatDatabaseHelper.runInTransaction(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            List<GroupInfoBean> groupInfoList = GroupManager.getInstance().getGroupInfoList();
                            groupInfoDaoKernel.insertAll(groupInfoList);
                            for (GroupInfoBean groupInfoBean : groupInfoList) {
                                List<GroupMemberBean> groupMembers = GroupManager.getInstance().getGroupMembers(groupInfoBean.groupId);
                                groupMemberDaoKernel.insertAll(groupMembers);
                                TLog.info(TAG, "markMigrationMember count= %d", LList.getCount(groupMembers));
                            }
                            SpManager.get().user().edit().putInt(MIGRATION_GROUP, 1).apply();
                        } catch (Exception e) {
                            TLog.error(TAG,e,"markMigrationGroup#runInTransaction ");
                        }
                    }
                });
                final long time = System.currentTimeMillis() - startTime;
                if (time > 2000) {
                    ApmAnalyzer.create().action(TAG, "migrationTimeOut").p2(String.valueOf(groupInfoBeans)).p3(String.valueOf(time)).p4("1").report();
                }
                TLog.info(TAG, "end markMigrationGroup");
            }
        });
    }

    /**
     * 标记联系人是否迁移完成
     */
    public static void markMigrationContact() {
        if (!DaoFactory.isUserChatService) {
            return;
        }

        if (isMigrationContact()) {
            int error_count = SpManager.get().user().getInt(MIGRATION_CONTACT_ERROR, 0);
            if (error_count == 0) {
                return;
            }
        }
        ChatDatabaseHelper.runInTransaction(new Runnable() {
            @Override
            public void run() {
                try {
                    //这里做迁移是因为，老的数据库是复用的，新的数据库是两套，所以聊天数据同步时，有差异
                    final List<ContactBean> contactList = contactDao.getAllContactList(AccountHelper.getIdentity());
                    ContactDao contactDao = ChatHelper.getContactDao();
                    if (contactDao != null) {
                        contactDao.upsertDataList(ContactTransformUtils.toContactList(contactList), contactDao::insert, contactDao::update);
                    }
                    SpManager.get().user().edit().putInt(MIGRATION_CONTACT, 1).apply();
                } catch (Exception e) {
                    TLog.error(TAG,e,"markMigrationContact#runInTransaction ");
                    ApmAnalyzer.create().action(TAG, "Exception").p2("markMigrationContact").p9(String.valueOf(VERSION)).report();

                }
            }
        });
        TLog.info(TAG, "markMigrationContact");

    }

    public static void checkGroupValid(final GroupInfoBean groupInfo) {
        if (!DaoFactory.isUserChatService) {
            return;
        }

        if (!isMigrationGroup()) {
            TLog.debug(TAG, "isMigrationGroup = false");
            return;
        }
        if (groupInfo == null) {
            TLog.error(TAG, "groupInfo is null");
            return;
        }
        App.getDbExecutor().execute(new WCDBCheckRunnable("checkGroupValid") {
            @Override
            protected void execute() {
                GroupInfoBean groupInfoBean = groupInfoDao.queryGroupInfoByGroupId(groupInfo.groupId);
                String groupJson = toJson(groupInfoBean);

                final GroupInfoBean groupInfoEntity = groupInfoDaoKernel.queryGroupInfoByGroupId(groupInfo.groupId);

                if (groupInfoEntity != null && groupInfoBean != null) {
                    groupInfoEntity.id = groupInfoBean.id;
                }

                final String groupEntityJson = toJson(groupInfoEntity);

                final boolean equals = TextUtils.equals(groupEntityJson, groupJson);
                if (equals) {
                    TLog.info(TAG, "checkGroupValid = Group is same GroupInfo = %s", groupEntityJson);

                    final List<GroupMemberBean> groupMemberBeans = groupMemberDaoKernel.queryGroupList(groupInfo.groupId);
                    Collections.sort(groupMemberBeans, (o1, o2) -> (int) (o1.userId - o2.userId));
                    final String groupMembersEntityJson = toJson(groupMemberBeans);

                    final List<GroupMemberBean> groupMembers = groupMemberDao.queryGroupList(groupInfo.groupId);
                    Collections.sort(groupMembers, (o1, o2) -> (int) (o1.userId - o2.userId));
                    String groupMembersJson = toJson(groupMembers);


                    if (TextUtils.equals(groupMembersJson, groupMembersEntityJson)) {
                        TLog.info(TAG, "checkGroupValid = GroupMember is same groupMembers= %s", groupMembersEntityJson);
                    } else {
                        ApmAnalyzer.create().action(TAG, "checkGroupMember").p2(groupMembersJson).p3(groupMembersEntityJson).p9(String.valueOf(VERSION)).report();
                        TLog.error(TAG, "checkGroupValid = GroupMember is Diff GroupInfo = %s \n GroupInfoEntity = %s", groupMembersJson, groupMembersEntityJson);
                        postDelayUploadLog();
                    }
                } else {
                    ApmAnalyzer.create().action(TAG, "checkGroup").p2(groupJson).p3(groupEntityJson).p9(String.valueOf(VERSION)).report();
                    TLog.error(TAG, "checkGroupValid = Group is Diff GroupInfo = %s \n GroupInfoEntity = %s", groupJson, groupEntityJson);
                }
            }
        });

    }


    public static void checkResult(Class tClass,boolean isCheck, Method method, Object object1, Object object2) {
        if (BuildInfoUtils.isDebug()) {
            if("toString".equals(method.getName())) return;
            if (object1 == object2 && object1 == null) {
                TLog.debug("callOldProxy", "=== checkResult === returnObject1 = %s returnObject2 = %s ", object1, object2);
                return;
            }
            if (tClass == IGroupInfoDao.class || tClass == IGroupMemberDao.class) {
                isCheck = isCheck && isMigrationGroup();
            } else if (tClass == IContactDao.class || tClass == IContactExtraInfoDao.class) {
                isCheck = isCheck && isMigrationContact();
            } else if (tClass == MessageDao.class) {
                isCheck = isCheck && MessageDoctorFactory.isSameChatData();
            }
            if (isCheck) {
                try {
                    if (object1 instanceof List && object2 instanceof List) {
                        final int count1 = LList.getCount((List<? extends Object>) object1);
                        final int count2 = LList.getCount((List<? extends Object>) object2);
                        if (count1 != count2) {
                            TLog.error("callOldProxy", "=== checkResult === count1 = %s count2 = %s ", count1, count2);
                            ApmAnalyzer.create().action(TAG, "checkResult").p2(method.toGenericString()).p3(count1+"="+count2).p9(String.valueOf(VERSION)).report();
                        } else {
                            TLog.debug("callOldProxy", "=== checkResult === count1 = %s  count2 = %s ", count1, count2);
                        }
                    } else if (object1.getClass() == object2.getClass()) {
                        if (object1 instanceof GroupInfoBean) { // 老 DB 群聊数据是公用的，新 DB 群聊数据是分开的 拉取新消息时 未读数 叠加
                            int noneReadCount1 = ((GroupInfoBean) object1).noneReadCount;
                            int noneReadCount2 = ((GroupInfoBean) object2).noneReadCount;
                            if (noneReadCount1 != noneReadCount2 && noneReadCount1 == 2 * noneReadCount2) {
                                ((GroupInfoBean) object2).noneReadCount = noneReadCount1;
                                TLog.info("callOldProxy", "noneReadCount1 = %d noneReadCount2 = %d",noneReadCount1,noneReadCount2);
                            }
                        }
                        String returnObject1 = toJson(object1);
                        String returnObject2 = toJson(object2);
                        if (!TextUtils.equals(returnObject1, returnObject2)) {
                            postDelayUploadLog();
                            TLog.error("callOldProxy", "=== checkResult === method = %s \nreturnObject1 = %s \nreturnObject2 = %s ", method,returnObject1, returnObject2);
                            ApmAnalyzer.create().action(TAG, "checkResult").p2(method.toGenericString()).p3(returnObject1).p4(returnObject2).p9(String.valueOf(VERSION)).report();
                        } else {
                            TLog.debug("callOldProxy", "=== checkResult === \nreturnObject1 = %s \nreturnObject2 = %s ", returnObject1, returnObject2);
                        }
                    } else {
                        TLog.debug("callOldProxy", "=== checkResult === object1 = %s object2 = %s ", object1, object2);
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static void postDelayUploadLog() {
        if (BuildInfoUtils.isDebug()) {
            Utils.removeHandlerCallbacks(uploadLogRun);
            Utils.runOnUiThreadDelayed(uploadLogRun,20*1000);
        }
    }
    private static final Runnable uploadLogRun = () -> UploadLog.getInstance().run();


    private static String toJson(Object object){
       return CHECK_GSON().toJson(object);
    }
    private static Gson GSON_EXCLUDE;

    public static Gson CHECK_GSON() {
        if (GSON_EXCLUDE == null) {
            GSON_EXCLUDE = new GsonBuilder()
                    .registerTypeAdapter(String.class, new StringNullToEmptyTypeAdapter())
                    .setExclusionStrategies(new SkipFieldExclusion())
                    .create();
        }
        return GSON_EXCLUDE;
    }

    public static void checkNewInstallEnvironment(App app) {
        if (!DataKernel.isUserChatService()) {
            return;
        }
        File databasePath = app.getContext().getDatabasePath(DBConfig.DB_NAME);
        boolean dbExists = FileUtils.isFileExists(databasePath);
        if (!dbExists) {
            SpManager.get().user().edit().putInt(MIGRATION_CONTACT, 2).putInt(MIGRATION_GROUP, 2).putLong(FIRST_APP_STARTUP,System.currentTimeMillis()).apply();
        }
        TLog.info(TAG,"checkNewInstallEnvironment = %b",!dbExists);
    }

    public static void reset() {
        migrationGroup = -1;
        opMonitorRSingleMessageHandler = -1;
    }

    public static void checkSaveCorruption() {
        long count = SpManager.get().user().getLong("updateBaseContactInfo", 0);
        if (ContactHandleManager.KEY_SAVE_CONTACT_ERROR == count) {
            //优化后新点
            ChatAnalyzer.reportNew(ChatAnalyzer.BASE_SAVE_CORRUPTION_NEW).debug().report();
        }

        if (SpManager.get().user().getLong("updateBaseContactInfoV2", 0) > 0) {
            final long lastTime = SpManager.get().global().getLong(DefaultRefreshStrategyV2.REFRESH_CONTACT_TIME_KEY, 0);
            boolean isNeedRefresh = ContactHandleManager.isOverTodayHour8(lastTime);
            if (!isNeedRefresh) {
                ApmAnalyzer.create().action("action_contact", "contact_list_corruption").debug().report();
                SpManager.get().user().edit().remove("updateBaseContactInfoV2").apply();
            }
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_LIST_CORRUPTION)
                    .put("isNeedRefresh", isNeedRefresh)
                    .info();
        }
    }

    public static void checkContactMessageCorruption() {
        if (opMonitorRSingleMessageHandler > -1) {
            return;
        }
        //检查 消息触发联系人更新是否中断
        int opMonitorRSingleMessageHandler = SpManager.get().user(MONITOR_FILE_CONTACT).getInt(MONITOR_NEW_CONTACT_MESSAGE, 0);
        if (opMonitorRSingleMessageHandler > 0) {
            SpManager.get().user(MONITOR_FILE_CONTACT).edit().remove(MONITOR_NEW_CONTACT_MESSAGE).apply();
            ApmAnalyzer.create().action("action_contact", "contact_msg_corruption").p2(MONITOR_NEW_CONTACT_MESSAGE).p3(String.valueOf(opMonitorRSingleMessageHandler)).p7(String.valueOf(getGray())).debug().report();
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_HANDLE_MESSAGE_CORRUPTION).put("count", opMonitorRSingleMessageHandler).info();
        }
    }

    public static final String MONITOR_FILE_CONTACT = "OP_MONITOR_MESSAGE_HANDLE";
    public static final String MONITOR_NEW_CONTACT_MESSAGE = "new_contact_message";
    public static int opMonitorRSingleMessageHandler = -1;
    public static void recordContactMessageHandler(Message msg) {
        checkContactMessageCorruption();
        long delayTime = msg.getWhen() > 0 ? (SystemClock.uptimeMillis() - msg.getWhen()) : 0;
        SharedPreferences sp = SpManager.get().user(MONITOR_FILE_CONTACT);
        String key = String.valueOf(msg.obj);
        if (opMonitorRSingleMessageHandler < 0) {
            opMonitorRSingleMessageHandler = 0;
        }
        if (msg.arg1 == 0) { // start
            sp.edit().putLong(key, ++opMonitorRSingleMessageHandler).apply();
        } else { // end
            if (--opMonitorRSingleMessageHandler <= 0) {
                opMonitorRSingleMessageHandler = 0;
                sp.edit().remove(key).apply();
            } else {
                sp.edit().putLong(key, opMonitorRSingleMessageHandler).apply();
            }
        }
        TLog.info(TAG, "recordContactMessageHandler %s step = %d count = %d delayTime = %d", key, msg.arg1, opMonitorRSingleMessageHandler, delayTime);
    }


    private static final AtomicInteger msgHandlePage = new AtomicInteger(0);

    final static ContactMessageHandle contactMessageHandle = new ContactMessageHandle();

    public static ContactMessageHandle getContactMessageHandle() {
        return contactMessageHandle;
    }
    /**
     * 监控收到消息， 联系人数据未存储结束
     *
     */
    public static class ContactMessageHandle implements Utils.OnAppStatusChangedListener {

        public static final String RECORD_KEY = "ContactMessageHandle";
        public static final String FIX_RECORD_KEY = "FixContactMessageHandle";
        private int appStatus = -1;
        private List<String> recordMids = new CopyOnWriteArrayList<>();
        private String checkMessageHandleKeyV2 = null;
        private String checkMessageHandleKey = null;
        private List<String> fix_message_mid;
        public ContactMessageHandle() {
            Utils.getActivityLifecycle().addOnAppStatusChangedListener(this);
        }

        void recordStartV2(List<ChatBean> chatBeans) {
            if (LList.isEmpty(chatBeans) || !AndroidDataStarGray.getInstance().monitorMsgHandleV2()) {
                return;
            }
            reportV2Data(); //上报异常
            String recordMid = getRecordMid(chatBeans);
            if (recordMid != null) {
                this.recordMids.add(recordMid);
            }
            TLog.debug(RECORD_KEY, "recordStartV2 recordMid = %s appStatus = %s  %d isForeground = %b", recordMid, appStatus, LList.getCount(this.recordMids), AppStatus.isForeground());
            if (!AppStatus.isForeground() || appStatus == 0 || LList.getCount(recordMids) > 100) {
                saveData();
            }
        }

        public List<String> getFixMidList() {
            List<String> currRecordMid = new ArrayList<>();
            if (recordMids != null) {
                String recordKey = getRecordKey();
                for (String midKey : recordMids) {
                    if (midKey.startsWith(recordKey) && currRecordMid.size() < AndroidDataStarGray.getInstance().isContactDelayFixDataStep()) {
                        currRecordMid.add(midKey);
                    }
                }
            }
            return currRecordMid;
        }

        List<String> readRecordMidList() {
            if (fix_message_mid == null) {
                String unHandleMessage = SpManager.get().global(MONITOR_FILE_CONTACT).getString(RECORD_KEY, null);
                TLog.debug(RECORD_KEY, "reportV2Data unHandleMessage %s", unHandleMessage);
                if (!TextUtils.isEmpty(unHandleMessage)) {
                    fix_message_mid = GsonUtils.fromJson(unHandleMessage, new TypeToken<List<String>>() {
                    }.getType());
                }
            }
            return fix_message_mid;
        }

        public boolean contactSyncing() {
            return LList.getCount(fix_message_mid) < LList.getCount(recordMids);
        }

        public void delRecordMidList(List<String> recordMids) {
            if (recordMids != null) {
                if (fix_message_mid != null) {
                    fix_message_mid.removeAll(recordMids);
                }
                this.recordMids.removeAll(recordMids);
                saveData();
            }
        }

        public void reportV2Data() {
            if (!AndroidDataStarGray.getInstance().monitorMsgHandleV2()) return;
            String key = getRecordKey();
            if (!TextUtils.equals(key, checkMessageHandleKeyV2)) {
                TLog.info(TAG, "reportV2Data====[%s] [%s]",checkMessageHandleKeyV2,key);
                checkMessageHandleKeyV2 = key;
                fix_message_mid = null;
                List<String> fix_message_mid = readRecordMidList();
                if (!LList.isEmpty(fix_message_mid)) {
                    recordMids = new CopyOnWriteArrayList<>(fix_message_mid);
                    List<String> fixMidList = getFixMidList();
                    int count = LList.getCount(fixMidList);
                    int allCount = LList.getCount(fix_message_mid);
                    String mid_str = StringUtil.connectTextWithChar(",", fixMidList);
                    if (count > 0) {
                        //APM 还未初始化成功
                        Utils.runOnUiThreadDelayed(() -> ApmAnalyzer.create().action("action_contact_doctor", "msg_handle_contact_interrupt_v2")
                                .p3(mid_str)
                                .p4(String.valueOf(fixMidList))
                                .p5(String.valueOf(allCount))
                                .p7(String.valueOf(getGray())).debug().report(),1000);
                    }
                    LogWise.chat(AnalyticLog.ChatAnalytic.MSG_HANDLE_CONTACT)
                            .put("status", "-1")
                            .put("msg_handle_mid", mid_str)
                            .put("msg_handle_count", count)
                            .info();
                } else {
                    recordMids = new CopyOnWriteArrayList<>();
                }
            }
        }

        String getRecordKey() {
            return AccountHelper.getUid() + ":" + AccountHelper.getIdentity();
        }

        public void recordStart(List<ChatBean> chatBeans) {
            if (!UserManager.isCurrentLoginStatus() || LList.isEmpty(chatBeans)) {
                return;
            }
            recordStartV2(chatBeans);

            ChatBean firstChat = LList.getFirstElement(chatBeans);
            ChatBean lastChat = LList.getLastElement(chatBeans);
            if (firstChat == null || lastChat == null) return;
            int count = LList.getCount(chatBeans);
            int msgPage = msgHandlePage.incrementAndGet();
            LogWise.chat(AnalyticLog.ChatAnalytic.MSG_HANDLE_CONTACT)
                    .put("status", "0")
                    .put("msg_handle_start_mid", firstChat.msgId)
                    .put("msg_handle_end_mid", lastChat.msgId)
                    .put("msg_handle_page", msgPage)
                    .put("msg_handle_count", count)
                    .info();
        }

        void recordEndV2(List<ChatBean> chatBeans, Callback<Boolean> callback) {
            if (LList.isEmpty(chatBeans) || !AndroidDataStarGray.getInstance().monitorMsgHandleV2()) {
                return;
            }

            String recordMid = getRecordMid(chatBeans);
            if (recordMid != null) {
                this.recordMids.remove(recordMid);
            }
            if (callback != null) {
                callback.call(contactSyncing());
            }
            TLog.debug(RECORD_KEY, "recordEndV2 recordMid = %s appStatus = %s  %d", recordMid, appStatus, LList.getCount(this.recordMids));
            if (!AppStatus.isForeground() || appStatus == 0) {
                saveData();
            }
        }

        private String getRecordMid(List<ChatBean> chatBeans) {
            if (LList.isEmpty(chatBeans)) return null;
            ChatBean firstChat = LList.getFirstElement(chatBeans);
            ChatBean lastChat = LList.getLastElement(chatBeans);
            if (firstChat == null || lastChat == null) return null;
            if (firstChat == lastChat && firstChat.msgId < 1000) return null; // 忽略消息id小于1000的
            return firstChat.myUserId + ":" + firstChat.myRole + ":" + firstChat.msgId + ":" + lastChat.msgId;
        }

        public void recordEnd(List<ChatBean> chatBeans, Callback<Boolean> callback) {
            if (!UserManager.isCurrentLoginStatus() || LList.isEmpty(chatBeans) || !AndroidDataStarGray.getInstance().monitorMsgHandleV2()) {
                return;
            }
            ChatBean firstChat = LList.getFirstElement(chatBeans);
            ChatBean lastChat = LList.getLastElement(chatBeans);
            if (firstChat == null || lastChat == null) return;
            DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_MONITOR_MESSAGE_HANDLE, new DbOpParam<ChatBean, Message>(firstChat, new DbOpCallback.SimpleDbOpCallback<Message>() {
                @Override
                public void success(Message message) {
                    recordEndV2(chatBeans, callback);
                    int msgPage = msgHandlePage.decrementAndGet();
                    LogWise.chat(AnalyticLog.ChatAnalytic.MSG_HANDLE_CONTACT)
                            .put("status", "1")
                            .put("msg_handle_start_mid", firstChat.msgId)
                            .put("msg_handle_end_mid", lastChat.msgId)
                            .put("msg_handle_page", msgPage)
                            .put("msg_handle_count", LList.getCount(chatBeans))
                            .info();
                    if (msgPage <= 0) {
                        msgHandlePage.set(0);
                    }
                }
            })).sendToTarget();
        }

        @Override
        public void onForeground(Activity activity) {
            SpManager.get().user(MONITOR_FILE_CONTACT).edit().remove(RECORD_KEY).apply();
            appStatus = 1;
            TLog.debug(RECORD_KEY, "onForeground %d", LList.getCount(recordMids));
        }

        @Override
        public void onBackground(Activity activity) {
            TLog.info(RECORD_KEY, "onBackground %d running = %b", LList.getCount(recordMids),SyncOverTaskManager.getInstance().isRunning());
            appStatus = 0;
            saveData();
        }

        private void saveData() {
            if (recordMids.isEmpty()) {
                SpManager.get().global(MONITOR_FILE_CONTACT).edit().remove(RECORD_KEY).apply();
            } else {
                SpManager.get().global(MONITOR_FILE_CONTACT).edit().putString(RECORD_KEY, GsonUtils.toJson(recordMids)).apply();
            }
        }

        public void clearData() {
            recordMids.clear();
            SpManager.get().global(MONITOR_FILE_CONTACT).edit().remove(RECORD_KEY).apply();
        }
    }

    private static int getGray() {
        int gray = 0;
        if (AndroidDataStarGray.getInstance().contactAllField()) {
            gray = 1;
        }
        if (AndroidDataStarGray.getInstance().isMessageDelV2()) {
            gray = 10 + gray;
        }

        if (AndroidDataStarGray.getInstance().isContactDelayFixData()) {
            gray = 100 + gray;
        }
        return gray;
    }

    /**
     * 联系人需删除
     */
    public static class ContactDel {
        /**
         * 开始删除
         *
         * @param delContacts
         */
        public static void start(List<ContactBean> delContacts) {
            if (LList.isEmpty(delContacts)) {
                complete();
            } else {
                List<String> friendIds = new ArrayList<>();
                for (ContactBean delContact : delContacts) {
                    friendIds.add(delContact.friendId + ":" + delContact.friendSource);
                }
                SpManager.get().user(TAG).edit().putString("recordDelContact", GsonUtils.toJson(friendIds)).apply();
            }
        }

        /**
         * 未删除数据
         *
         * @return
         */
        public static List<String> get() {
            String recordDelContact = SpManager.get().user(TAG).getString("recordDelContact", null);
            if (recordDelContact != null) {
                return GsonUtils.fromJson(recordDelContact, new TypeToken<List<String>>() {
                }.getType());
            }
            return null;
        }

        /**
         * 删除结束
         */
        public static void complete() {
            SpManager.get().user(TAG).edit().remove("recordDelContact").apply();
        }
    }

    private static class StringNullToEmptyTypeAdapter extends TypeAdapter<String> {
        @Override
        public void write(JsonWriter out, String value) throws IOException {
            if (value == null) {
                out.value("");
            } else {
                out.value(value);
            }
        }

        @Override
        public String read(JsonReader in) throws IOException {
            if (in.peek() == JsonToken.NULL) {
                in.nextNull();
                return "";
            }
            return in.nextString();
        }
    }

    private static class SkipFieldExclusion implements ExclusionStrategy {
        public static final List<String> ignores = Arrays.asList("certification", "lastRefreshTime","lid");
        @Override
        public boolean shouldSkipField(FieldAttributes f) {
            if (ignores.contains(f.getName())) {
                return true;
            }
            return f.getAnnotation(PrimaryKey.class) != null;
        }

        @Override
        public boolean shouldSkipClass(Class<?> clazz) {
            if (ChatUserBean.class == clazz) {
                return true;
            }
            return false;
        }
    }

    public static void checkOverlappingFriend(List<ContactBean> list) {
        //重复联系人
        final Set<Long> uniqueSet = new HashSet<>();
        final List<ContactBean> uniqueContact = new ArrayList<>();
        //重复数据
        final Set<String> uniqueDataSet = new HashSet<>();
        final List<ContactBean> uniqueData = new ArrayList<>();
        for (ContactBean contactBean : list) {
            if (!uniqueSet.add(contactBean.friendId)) {
                uniqueContact.add(contactBean);
            }
            if (!uniqueDataSet.add(contactBean.friendId + ":" + contactBean.friendSource)) {
                uniqueData.add(contactBean);
            }
        }
        if (!uniqueContact.isEmpty()) {
            String string = StringUtil.connectTextWithChar(",", uniqueContact, (StringUtil.IValueFunction<ContactBean>) contactBean -> contactBean.friendId + ":" + contactBean.friendSource + ":" + contactBean.friendName);
            String uniqueDataStr = LList.isEmpty(uniqueData) ? "0" : StringUtil.connectTextWithChar(",", uniqueData, (StringUtil.IValueFunction<ContactBean>) contactBean -> contactBean.friendId + ":" + contactBean.friendSource + ":" + contactBean.friendName);
            ApmAnalyzer.create().action("action_contact","overlapping").p2(string).p3(uniqueDataStr).debug().report();
        }
    }

    public static void checkOverlappingFriend2(List<Long> list,List<Long> list2) {
        //重复联系人
        final Set<Long> uniqueSet = new HashSet<>();
        final List<Long> uniqueContact = new ArrayList<>();
        //重复数据
        final Set<String> uniqueDataSet = new HashSet<>();
        final List<String> uniqueData = new ArrayList<>();
        if (list != null) {
            for (Long friendId : list) {
                if (!uniqueSet.add(friendId)) {
                    uniqueContact.add(friendId);
                }
                String key = friendId + ":0";
                if (!uniqueDataSet.add(key)) {
                    uniqueData.add(key);
                }
            }
        }
        if (list2 != null) {
            for (Long friendId : list2) {
                if (!uniqueSet.add(friendId)) {
                    uniqueContact.add(friendId);
                }
                String key = friendId + ":1";
                if (!uniqueDataSet.add(key)) {
                    uniqueData.add(key);
                }
            }
        }
        if (!uniqueContact.isEmpty()) {
            String string = StringUtil.connectTextWithChar(",", uniqueContact, (StringUtil.IValueFunction<Long>) String::valueOf);
            String uniqueDataStr = LList.isEmpty(uniqueData) ? "0" : StringUtil.connectTextWithChar(",", uniqueData);
            ApmAnalyzer.create().action("action_contact","overlapping").p2(string).p3(uniqueDataStr).debug().report();
        }
    }
}
