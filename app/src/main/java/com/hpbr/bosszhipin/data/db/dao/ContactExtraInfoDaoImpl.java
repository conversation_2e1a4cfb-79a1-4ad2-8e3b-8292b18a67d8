package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.ContactExtraInfo;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.DataBase;
import com.monch.lbase.orm.db.assit.QueryBuilder;

import java.util.List;


/**
 * Created by wang<PERSON> on 2018/3/2.
 */
public class ContactExtraInfoDaoImpl implements IContactExtraInfoDao {


    @Override
    public long insertOrUpdate(ContactExtraInfo bean) {
        if (bean == null) return -1;
        ContactExtraInfo temp = queryById(bean.friendId);
        if (temp != null) {
            bean.id = temp.id;
        }
        return App.get().db().save(bean);
    }

    @Override
    public void delete(long fiendId) {
        DataBase db = App.get().db();
        db.delete(ContactExtraInfo.class,
                "friendId=" + fiendId
                        + " AND myId=" + UserManager.getUID()
                        + " AND myRole=" + UserManager.getUserRole().get());

    }

    @Override
    public ContactExtraInfo queryById(long friendId) {
        QueryBuilder qb = new QueryBuilder(ContactExtraInfo.class);
        qb.where("friendId=" + friendId + " AND myId=" + UserManager.getUID() + " AND myRole=" + UserManager.getUserRole().get(), null);
        List<ContactExtraInfo> list = App.get().db().query(qb);
        if (list != null && list.size() > 0) {
            if (list.size() > 1) {  // 此处逻辑是防止有多条相同的联系人存在，如果存在多条相同的联系人，将其删除
                for (int i = 1; i < list.size(); i++) {
                    App.get().db().delete(list.get(i));
                }
            }
            return list.get(0);
        }
        return null;
    }
}
