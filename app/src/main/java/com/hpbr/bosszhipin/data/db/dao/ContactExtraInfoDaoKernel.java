package com.hpbr.bosszhipin.data.db.dao;

import android.text.TextUtils;

import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.chat.db.LocalDataDao;
import com.bszp.kernel.chat.db.entity.ContactLocalEntity;
import com.bszp.kernel.chat.service.ChatHelper;
import com.hpbr.bosszhipin.data.db.entry.ContactExtraInfo;


public class ContactExtraInfoDaoKernel implements IContactExtraInfoDao {


    com.bszp.kernel.chat.db.ContactDao getContactDao() {
        return ChatHelper.getContactDao();
    }

    com.bszp.kernel.chat.db.LocalDataDao getLocalDataDao() {
        return ChatHelper.getLocalDataDao();
    }

    @Override
    public long insertOrUpdate(ContactExtraInfo bean) {
        if (bean == null) return -1;
        LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            return localDataDao.addField(bean.friendId, 0, ContactLocalEntity.LocalField.CONTACT_MESSAGE_TYPE_17_TITLE, bean.dangerousHintMessage);
        }

        return -1;
    }

    @Override
    public void delete(long fiendId) {
        LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            localDataDao.removeField(fiendId, 0, ContactLocalEntity.LocalField.CONTACT_MESSAGE_TYPE_17_TITLE);
        }
    }

    @Override
    public ContactExtraInfo queryById(long friendId) {
        LocalDataDao localDataDao = getLocalDataDao();
        if (localDataDao != null) {
            final ContactLocalEntity localEntity = localDataDao.query(friendId, 0);
            if (localEntity != null) {
                final ContactLocalEntity.LocalField localField = localEntity.getLocalField();
                if (localField != null) {
                    if (TextUtils.isEmpty(localField.messageType17Title)) {
                        return null;
                    }
                    ContactExtraInfo contactExtraInfo = new ContactExtraInfo();
                    contactExtraInfo.friendId = friendId;
                    contactExtraInfo.myRole = AccountHelper.getIdentity();
                    contactExtraInfo.dangerousHintMessage = localField.messageType17Title;
                    return contactExtraInfo;
                }
            }
        }

        return null;
    }
}
