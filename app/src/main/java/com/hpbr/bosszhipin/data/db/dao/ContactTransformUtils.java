package com.hpbr.bosszhipin.data.db.dao;


import com.bszp.kernel.account.AccountHelper;
import com.bszp.kernel.chat.db.entity.ContactBaseInfo;
import com.bszp.kernel.chat.db.entity.ContactData;
import com.bszp.kernel.chat.db.entity.ContactEntity;
import com.bszp.kernel.chat.db.entity.ContactLocalEntity;
import com.bszp.kernel.chat.db.entity.GroupData;
import com.bszp.kernel.chat.db.entity.GroupEntity;
import com.bszp.kernel.chat.db.entity.GroupMemberEntity;
import com.bszp.kernel.chat.logic.contact.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.StringUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

class ContactTransformUtils {

    public static final String TAG = "ContactTransformUtils";

    public static List<ContactBaseInfo> toContactBaseInfoList(List<ContactBean> contactBeans) {
        if (contactBeans == null) return null;
        List<ContactBaseInfo> contactEntities = new ArrayList<>(contactBeans.size());
        for (ContactBean contactBean : contactBeans) {
            contactEntities.add(toContactBaseInfo(contactBean));
        }
        return contactEntities;
    }

    public static List<ContactEntity> toContactList(List<ContactBean> contactBeans) {
        if (contactBeans == null) return null;
        List<ContactEntity> contactEntities = new ArrayList<>(contactBeans.size());
        for (ContactBean contactBean : contactBeans) {
            contactEntities.add(toContactEntity(contactBean));
        }
        return contactEntities;
    }

    public static ContactEntity toContactBaseInfo(ContactBean contactBean) {
        ContactEntity contactEntity = new ContactEntity();
        contactEntity.setFriendId(contactBean.friendId);
        contactEntity.setFriendSource(contactBean.friendSource);
        contactEntity.setSecurityId(contactBean.securityId);
        contactEntity.setName(contactBean.friendName);
        contactEntity.setTinyUrl(contactBean.friendDefaultAvatar);
        contactEntity.setTitle(contactBean.geekPositionName);
        contactEntity.setCertification(contactBean.certification);
        contactEntity.setCompany(contactBean.bossCompanyName);
        if (contactBean.friendCompanies != null) {
            String[] split = contactBean.friendCompanies.split(StringUtil.SPLIT_CHAR_KEYWORD);
            contactEntity.setCompanyHistoryList(Arrays.asList(split));
        } else {
            contactEntity.setCompanyHistoryList(null);
        }
        contactEntity.setDatetime(contactBean.updateTime);
        contactEntity.setJobId(contactBean.jobId);
        contactEntity.setJobName(contactBean.bossJobPosition);
        contactEntity.setJobSource(contactBean.jobSource);
        contactEntity.setExpectId(contactBean.jobIntentId);
        contactEntity.setSourceTitle(contactBean.userFromTitle);
        contactEntity.setTop(contactBean.isTop);
        contactEntity.setRejectUser(contactBean.isReject);
        contactEntity.setRejectDesc(contactBean.rejectReason);
        contactEntity.setGoldGeekStatus(contactBean.goldGeekStatus);
        contactEntity.setItemType(contactBean.itemType);
        contactEntity.setHeadline(contactBean.greetingText);
        contactEntity.setFiltered(contactBean.isFiltered);
        contactEntity.setFilterReasonList(contactBean.filterReasonList);
        contactEntity.setTeamMemberSize(contactBean.teamMemberSize);
        contactEntity.setItemSource(contactBean.itemSource);
        contactEntity.setExpectPositionName(contactBean.expectPositionName);
        contactEntity.setStar(contactBean.isStar);
        contactEntity.setHeadhunter(contactBean.isHeadhunter);
        contactEntity.setHighGeek(contactBean.highGeek);
        contactEntity.setPositionName(contactBean.positionName);
        contactEntity.setSalaryDesc(contactBean.salaryDesc);
        contactEntity.setWorkplace(contactBean.workplace);
        contactEntity.setAgentRecruit(contactBean.isAgentRecruit);
        contactEntity.setHunter(contactBean.isHunter);
        contactEntity.setAgency(contactBean.isAgency);
        contactEntity.setNoDisturb(contactBean.noDisturb);
        contactEntity.setInvalidJob(contactBean.invalidJob);
        contactEntity.setFriendType(contactBean.friendType);
        contactEntity.setLowSalary(contactBean.lowSalary);
        contactEntity.setHighSalary(contactBean.highSalary);

        return contactEntity;
    }

    public static ContactEntity toContactEntity(ContactBean contactBean) {
        ContactEntity contactEntity = toContactBaseInfo(contactBean);

        /* 以下是 FullInfo 返回的*/
        contactEntity.setPhoneNumber(contactBean.friendPhone);
        contactEntity.setWxNumber(contactBean.friendWxNumber);
        contactEntity.setBlack(contactBean.isBlack);
        contactEntity.setBlackDesc(contactBean.blackDesc);
        contactEntity.setFreeze(contactBean.isFreeze);
        contactEntity.setInterviewStatus(contactBean.currentInterviewStatus);
        contactEntity.setInterviewStatusDesc(contactBean.currentInterviewDesc);
        contactEntity.setInterviewUrl(contactBean.currentInterviewProtocol);
        contactEntity.setExchangeResumeStatus(contactBean.exchangeResumeStatus);
        contactEntity.setResumeUrl(contactBean.exchangeResumeUrl);
        contactEntity.setWxRemind(contactBean.isWxRemind);
        contactEntity.setVideoResumeUrl(contactBean.videoResumeUrl);
        contactEntity.setPartJobStrategy(contactBean.partJobStrategy);
        contactEntity.setAddTime(contactBean.addTime);
        contactEntity.setDelTime(contactBean.delTime);
        contactEntity.setNote(contactBean.note);
        contactEntity.setLabels(contactBean.labels);
        contactEntity.setGoldInterviewer(contactBean.goldInterviewer);
        contactEntity.setRegionCode(contactBean.regionCode);
        //1012 版本以后 收藏逻辑 移至isStar
        //contactEntity.setStarTypes(transferStarTypes(contactBean.starTypes));
        contactEntity.setPreFreeze(contactBean.isPreFreeze);
        contactEntity.setFreezeInfo(contactBean.freezeInfo);
        contactEntity.setPreFreezeInfo(contactBean.preFreezeInfo);
        contactEntity.setInterviewDateStr(contactBean.interviewDateStr);
        contactEntity.setInterviewTimeStr(contactBean.interviewTimeStr);
        contactEntity.setWorkYear(contactBean.workYear);
        contactEntity.setWorkDesc(contactBean.workDesc);
        contactEntity.setDegree(contactBean.degree);
        contactEntity.setExpectSalary(contactBean.expectSalary);
        contactEntity.setWarningInfo(contactBean.warningTips);
        contactEntity.setIconFlag(contactBean.iconFlag);
        return contactEntity;
    }


    public static List<ContactLocalEntity> toContactLocalList(List<ContactBean> contactBeans) {
        if (contactBeans ==null) return null;
        List<ContactLocalEntity> contactEntities = new ArrayList<>(contactBeans.size());
        for (ContactBean contactBean : contactBeans) {
            contactEntities.add(toContactLocalEntity(contactBean));
        }
        return contactEntities;
    }

    public static ContactLocalEntity toContactLocalEntity(ContactBean contactBean) {
        ContactLocalEntity localEntity = new ContactLocalEntity(contactBean.friendId, contactBean.friendSource);
        localEntity.noneReadCount = contactBean.noneReadCount;
        localEntity.draft = contactBean.RoughDraft;
        localEntity.avatarIndex = contactBean.friendDefaultAvatarIndex;
//        localEntity.friendCompanies = contactBean.friendCompanies;
        localEntity.friendStage = contactBean.fridendStage;
        localEntity.exchangeType = contactBean.messageExchangeIcon;

        localEntity.messageField = new ContactLocalEntity.MessageField();
        localEntity.messageField.summary = contactBean.lastChatText;
        localEntity.messageField.chatTime = contactBean.lastChatTime;
        localEntity.messageField.mid = contactBean.lastMsgId;
        localEntity.messageField.msgState = contactBean.lastChatStatus;
        localEntity.messageField.cmid = contactBean.lastChatClientMessageId;
        localEntity.messageField.quoteMid = contactBean.quoteMessageId;

        localEntity.setStateType(ContactLocalEntity.StateType.IS_CONTACT_HELPER, contactBean.isContactHelper);
        localEntity.setStateType(ContactLocalEntity.StateType.IS_RECEIVE_FRIEND_GREET_MSG, contactBean.receiveFriendGreetMsg);
        localEntity.setStateType(ContactLocalEntity.StateType.IS_ATS, contactBean.ats);
        localEntity.setStateType(ContactLocalEntity.StateType.IS_PLAT_FROM_RESEARCH, contactBean.isPlatFromResearch);
        localEntity.setStateType(ContactLocalEntity.StateType.IS_IMPROVE_MESSAGE_EXPOSURE, contactBean.improveMessageExposure);

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_DIRECT_CALL_STATUS, contactBean.directCallStatus);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_EXCHANGE_PHONE_TIME, contactBean.exchangePhoneTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_EXCHANGE_WX_NUMBER_TIME, contactBean.exchangeWxNumberTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_EXCHANGE_ANNEX_RESUME_TIME, contactBean.exchangeAnnexResumeTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_EXCHANGE_INTERVIEW_TIME, contactBean.exchangeInterviewTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_REFRESH_FULL_INFO_TIME, contactBean.lastRefreshTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_SHORT_MSG, contactBean.videoInterviewF2Text);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_VIDEO_INTERVIEW_LONG_MSG, contactBean.videoInterviewChatTopText);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_PROTOCOL, contactBean.interviewScheduleProtocol);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_STATUS, contactBean.interviewScheduleStatus);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_INTERVIEW_SCHEDULE_TIME, contactBean.interviewScheduleTime);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_HAS_JUMP_TO_CHAT, contactBean.hasJumpToChat);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_LID, contactBean.lid);
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_SWITCH, contactBean.switch1);
            localEntity.extStr = jsonObject.toString();
        } catch (Exception e) {
            TLog.error(TAG, e, "toContactLocalEntity contactData = %s", contactBean);
        }
        return localEntity;
    }

    public static ContactBean toContactBean(ContactData contactData) {
        if (contactData == null || contactData.getContactEntity() == null) {
            TLog.error(TAG, "contactData = %s", contactData);
            return null;
        }
        if (contactData.getContactEntity().getFriendId() < 1) {
            TLog.error(TAG, "getFriendId error contactData = %s", contactData);
        }
        return toContactBean(contactData.getContactEntity(), contactData.getLocalEntity());
    }

    public static ContactBean toContactBean(ContactEntity contactEntity, ContactLocalEntity localEntity) {
        ContactBean contactBean = new ContactBean();
        contactBean.myId = AccountHelper.getUid();
        contactBean.myRole = AccountHelper.getIdentity();
        contactBean.friendId = contactEntity.getFriendId();
        contactBean.friendName = contactEntity.getName();
        contactBean.friendSource = contactEntity.getFriendSource();
        contactBean.securityId = contactEntity.getSecurityId();
        contactBean.jobId = contactEntity.getJobId();
        contactBean.bossJobPosition = contactEntity.getJobName();
        contactBean.jobIntentId = contactEntity.getExpectId();
        contactBean.jobSource = contactEntity.getJobSource();
        contactBean.friendDefaultAvatar = contactEntity.getTinyUrl();
        contactBean.geekPositionName = contactEntity.getTitle();
        contactBean.userFromTitle = contactEntity.getSourceTitle();
        contactBean.certification = contactEntity.getCertification();
        contactBean.bossCompanyName = contactEntity.getCompany();
        contactBean.friendCompanies = StringUtils.connectTextWithChar(StringUtil.SPLIT_CHAR_KEYWORD, contactEntity.getCompanyHistoryList());
        contactBean.expectPositionName = contactEntity.getExpectPositionName();
        contactBean.isTop = contactEntity.isTop();
        contactBean.goldGeekStatus = contactEntity.getGoldGeekStatus();
        contactBean.isHunter = contactEntity.isHunter();
        contactBean.isAgency = contactEntity.isAgency();
        contactBean.isReject = contactEntity.isRejectUser();
        contactBean.isStar = contactEntity.isStar();
        contactBean.rejectReason = contactEntity.getRejectDesc();
        contactBean.isHeadhunter = contactEntity.isHeadhunter();
        contactBean.greetingText = contactEntity.getHeadline();
        contactBean.updateTime = contactEntity.getDatetime();
        contactBean.positionName = contactEntity.getPositionName();
        contactBean.salaryDesc = contactEntity.getSalaryDesc();
        contactBean.workplace = contactEntity.getWorkplace();
        contactBean.isAgentRecruit = contactEntity.isAgentRecruit();
        contactBean.itemType = contactEntity.getItemType();
        contactBean.noDisturb = contactEntity.getNoDisturb();
        contactBean.highGeek = contactEntity.isHighGeek();
        contactBean.isFiltered = contactEntity.isFiltered();
        contactBean.filterReasonList = contactEntity.getFilterReasonList();
        contactBean.teamMemberSize = contactEntity.getTeamMemberSize();
        contactBean.itemSource = contactEntity.getItemSource();
        contactBean.invalidJob = contactEntity.getInvalidJob();
        contactBean.iconFlag=contactEntity.getIconFlag();
        contactBean.friendType = contactEntity.getFriendType();
        contactBean.lowSalary = contactEntity.getLowSalary();
        contactBean.highSalary = contactEntity.getHighSalary();
        //服务器没有下发这个字段
//        contactBean.chatMsgBlockHeadBar = contactEntity.getChatMsgBlockHeadBar();
        //服务器没有下发这个字段
//        contactBean.isTechGeekBlock = contactEntity.isTechGeekBlock();

        /* 以下是 FullInfo 返回的*/
        contactBean.friendPhone = contactEntity.getPhoneNumber();
        contactBean.friendWxNumber = contactEntity.getWxNumber();
        contactBean.isBlack = contactEntity.isBlack();
        contactBean.blackDesc = contactEntity.getBlackDesc();
        contactBean.isFreeze = contactEntity.isFreeze();
        contactBean.isWxRemind = contactEntity.isWxRemind();
        contactBean.videoResumeUrl = contactEntity.getVideoResumeUrl();
        contactBean.partJobStrategy = contactEntity.getPartJobStrategy();
        contactBean.exchangeResumeStatus = contactEntity.getExchangeResumeStatus();
        contactBean.currentInterviewStatus = contactEntity.getInterviewStatus();
        contactBean.currentInterviewDesc = contactEntity.getInterviewStatusDesc();
        contactBean.currentInterviewProtocol = contactEntity.getInterviewUrl();
        contactBean.exchangeResumeUrl = contactEntity.getResumeUrl();
        contactBean.addTime = contactEntity.getAddTime();
        contactBean.delTime = contactEntity.getDelTime();
        contactBean.note = contactEntity.getNote();
        contactBean.labels = contactEntity.getLabels();
        contactBean.regionCode = contactEntity.getRegionCode();
        contactBean.goldInterviewer = contactEntity.getGoldInterviewer();
        //1012 版本以后 收藏逻辑 移至isStar
        //contactBean.starTypes = transferStarTypes(contactEntity.getStarTypes());
        contactBean.freezeInfo = contactEntity.getFreezeInfo();
        contactBean.preFreezeInfo = contactEntity.getPreFreezeInfo();
        contactBean.isPreFreeze = contactEntity.isPreFreeze();
        contactBean.interviewDateStr = contactEntity.getInterviewDateStr();
        contactBean.interviewTimeStr = contactEntity.getInterviewTimeStr();
        contactBean.workDesc = contactEntity.getWorkDesc();
        contactBean.workYear = contactEntity.getWorkYear();
        contactBean.degree = contactEntity.getDegree();
        contactBean.expectSalary = contactEntity.getExpectSalary();
        contactBean.warningTips = contactEntity.getWarningTip();
        return toContactBean(contactBean, localEntity);
    }

    public static ContactBean toContactBean(ContactBean contactBean, ContactLocalEntity localEntity) {
        if (localEntity == null) {
            TLog.error(TAG, "toContactBean  localEntity is null friendId= %d ", contactBean.friendId);
            return contactBean;
        }
        contactBean.noneReadCount = localEntity.noneReadCount;
        contactBean.RoughDraft = localEntity.draft;
        contactBean.friendDefaultAvatarIndex = localEntity.avatarIndex;
//        contactBean.friendCompanies = localEntity.friendCompanies;
        contactBean.fridendStage = localEntity.friendStage;

        if (localEntity.messageField != null) {
            contactBean.lastChatText = localEntity.messageField.summary;
            contactBean.lastChatTime = localEntity.messageField.chatTime;
            contactBean.lastMsgId = localEntity.messageField.mid;
            contactBean.lastChatStatus = localEntity.messageField.msgState;
            contactBean.lastChatClientMessageId = localEntity.messageField.cmid;
            contactBean.quoteMessageId = localEntity.messageField.quoteMid;
        }

        contactBean.messageExchangeIcon = localEntity.exchangeType;
        contactBean.isPlatFromResearch = localEntity.isStateType(ContactLocalEntity.StateType.IS_PLAT_FROM_RESEARCH);
        contactBean.isContactHelper = localEntity.isStateType(ContactLocalEntity.StateType.IS_CONTACT_HELPER);
        contactBean.receiveFriendGreetMsg = localEntity.isStateType(ContactLocalEntity.StateType.IS_RECEIVE_FRIEND_GREET_MSG);
        contactBean.ats = localEntity.isStateType(ContactLocalEntity.StateType.IS_ATS);
        contactBean.improveMessageExposure = localEntity.isStateType(ContactLocalEntity.StateType.IS_IMPROVE_MESSAGE_EXPOSURE);

        ContactLocalEntity.LocalField localField = localEntity.getLocalField();
        if (localField != null) {
            contactBean.lid = localField.lid;
            contactBean.switch1 = localField.switch1;
            contactBean.directCallStatus = localField.directCallStatus;
            contactBean.hasJumpToChat = localField.hasJumpToChat;
            contactBean.exchangePhoneTime = localField.exchangePhoneTime;
            contactBean.exchangeAnnexResumeTime = localField.exchangeAnnexResumeTime;
            contactBean.exchangeInterviewTime = localField.exchangeInterviewTime;
            contactBean.exchangeWxNumberTime = localField.exchangeWxNumberTime;
            contactBean.lastRefreshTime = localField.lastRefreshTime;
            contactBean.videoInterviewF2Text = localField.videoInterviewShortMsg;
            contactBean.videoInterviewChatTopText = localField.videoInterviewLongMsg;
            contactBean.interviewScheduleTime = localField.interviewScheduleTime;
            contactBean.interviewScheduleStatus = localField.interviewScheduleStatus;
            contactBean.interviewScheduleProtocol = localField.interviewScheduleProtocol;
        }

        return contactBean;
    }

    public static List<GroupData> toGroupDataList(List<GroupInfoBean> groupInfoBeans) {
        List<GroupData> groupData = new ArrayList<>();
        for (GroupInfoBean groupInfoBean : groupInfoBeans) {
            groupData.add(toGroupData(groupInfoBean));
        }
        return groupData;
    }

    public static GroupData toGroupData(GroupInfoBean groupInfoBean) {
        return new GroupData(toGroupEntity(groupInfoBean), toGroupLocalEntity(groupInfoBean));
    }

    public static GroupEntity toGroupEntity(GroupInfoBean groupInfoBean) {
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setName(groupInfoBean.name);
        groupEntity.setAvatarUrl(groupInfoBean.avatarUrl);
        groupEntity.setCount(groupInfoBean.count);
        groupEntity.setVersion(groupInfoBean.version);
        groupEntity.setFlag(groupInfoBean.flag);
        groupEntity.setIntroduction(groupInfoBean.introduction);
        groupEntity.setNotice(groupInfoBean.notice);
        groupEntity.setWatch(groupInfoBean.watch);
        groupEntity.setSilent(groupInfoBean.silent);
        groupEntity.setAdminId(groupInfoBean.adminId);
        groupEntity.setGroupId(groupInfoBean.groupId);
        groupEntity.setGid(groupInfoBean.gid);
        groupEntity.setType(groupInfoBean.type);
        groupEntity.setTypeName(groupInfoBean.typeName);
        groupEntity.setInternal(groupInfoBean.internal);
        groupEntity.setSource(groupInfoBean.source);
        if (groupInfoBean.isQuit) {
            groupEntity.setStatus(Constants.STATUS_DEL);
        } else if (groupInfoBean.isHide) {
            groupEntity.setStatus(Constants.STATUS_HIDE);
        }
        groupEntity.setLabel(groupInfoBean.label);
        return groupEntity;
    }

    public static GroupInfoBean toGroupInfoBean(GroupData groupData) {
        if (groupData == null || groupData.getGroupEntity() == null) {
            TLog.error(TAG, "groupData = %s", groupData);
            return null;
        }
        return toGroupInfoBean(groupData.getGroupEntity(), groupData.getLocalEntity());
    }

    public static GroupInfoBean toGroupInfoBean(GroupEntity groupEntity) {
        GroupInfoBean groupInfoBean = new GroupInfoBean();
        groupInfoBean.myUid = AccountHelper.getUid();
        groupInfoBean.name = groupEntity.getName();
        groupInfoBean.avatarUrl = groupEntity.getAvatarUrl();
        groupInfoBean.count = groupEntity.getCount();
        groupInfoBean.version = groupEntity.getVersion();
        groupInfoBean.flag = groupEntity.getFlag();
        groupInfoBean.introduction = groupEntity.getIntroduction();
        groupInfoBean.notice = groupEntity.getNotice();
        groupInfoBean.watch = groupEntity.getWatch();
        groupInfoBean.silent = groupEntity.getSilent();
        groupInfoBean.adminId = groupEntity.getAdminId();
        groupInfoBean.groupId = groupEntity.getGroupId();
        groupInfoBean.gid = groupEntity.getGid();
        groupInfoBean.isQuit = false;
        groupInfoBean.myUid = UserManager.getUID();
        groupInfoBean.type = groupEntity.getType();
        groupInfoBean.typeName = groupEntity.getTypeName();
        groupInfoBean.internal = groupEntity.getInternal();
        groupInfoBean.source = groupEntity.getSource();
        groupInfoBean.isHide = groupEntity.getStatus() != Constants.STATUS_NORMAL;
        groupInfoBean.isQuit = groupEntity.getStatus() == Constants.STATUS_DEL;
        groupInfoBean.label = groupEntity.getLabel();
        return groupInfoBean;
    }

    public static GroupMemberEntity toGroupMemberEntity(GroupMemberBean groupMemberBean) {
        GroupMemberEntity groupMemberEntity = new GroupMemberEntity();
        groupMemberEntity.setUid(groupMemberBean.uid);
        groupMemberEntity.setGroupId(groupMemberBean.groupId);
        groupMemberEntity.setGid(groupMemberBean.gid);
        groupMemberEntity.setUserId(groupMemberBean.userId);
        groupMemberEntity.setName(groupMemberBean.name);
        groupMemberEntity.setAvatarUrl(groupMemberBean.avatarUrl);
        groupMemberEntity.setIdentity(groupMemberBean.identity);
//        groupMemberEntity.setGender(groupMemberBean.gender);
//        groupMemberEntity.setEmail(groupMemberBean.email);
        groupMemberEntity.setAddTime(groupMemberBean.addTime);
//        groupMemberEntity.setInitFlag(groupMemberBean.initFlag);
        groupMemberEntity.setIsAdmin(groupMemberBean.isAdmin);
        groupMemberEntity.setPosition(groupMemberBean.position);
        groupMemberEntity.setBlack(groupMemberBean.black);
        groupMemberEntity.setCertification(groupMemberBean.certification);
//        groupMemberEntity.setWatch(groupMemberBean.watch);
        groupMemberEntity.setSignature(groupMemberBean.signature);
        groupMemberEntity.setSecurityId(groupMemberBean.securityId);
        groupMemberEntity.setCompany(groupMemberBean.company);
//        groupMemberEntity.setSilent(groupMemberBean.silent);
        groupMemberEntity.setPrefix(groupMemberBean.prefix);
        return groupMemberEntity;
    }

    public static GroupMemberBean toGroupMemberBean(GroupMemberEntity groupMemberEntity) {
        GroupMemberBean groupMemberBean = new GroupMemberBean();
        groupMemberBean.myUid = UserManager.getUID();
        groupMemberBean.uid = groupMemberEntity.getUid();
        groupMemberBean.gid = groupMemberEntity.getGid();
        groupMemberBean.groupId = groupMemberEntity.getGroupId();
        groupMemberBean.userId = groupMemberEntity.getUserId();
        groupMemberBean.name = groupMemberEntity.getName();
        groupMemberBean.avatarUrl = groupMemberEntity.getAvatarUrl();
        groupMemberBean.identity = groupMemberEntity.getIdentity();
        groupMemberBean.isAdmin = groupMemberEntity.getIsAdmin();
        groupMemberBean.position = groupMemberEntity.getPosition();
        groupMemberBean.company = groupMemberEntity.getCompany();
        groupMemberBean.certification = groupMemberEntity.getCertification();
        groupMemberBean.black = groupMemberEntity.getBlack();
        groupMemberBean.signature = groupMemberEntity.getSignature();
        groupMemberBean.securityId = groupMemberEntity.getSecurityId();
        groupMemberBean.addTime = groupMemberEntity.getAddTime();
        groupMemberBean.prefix = groupMemberEntity.getPrefix();

        return groupMemberBean;
    }

    public static ContactLocalEntity toGroupLocalEntity(GroupInfoBean groupInfoBean) {
        ContactLocalEntity localEntity = new ContactLocalEntity(groupInfoBean.groupId, -1);
        localEntity.noneReadCount = groupInfoBean.noneReadCount;
        localEntity.draft = groupInfoBean.draft;
        localEntity.updateTime = groupInfoBean.updateTime;
        localEntity.messageField = new ContactLocalEntity.MessageField();
        localEntity.messageField.summary = groupInfoBean.lastChatText;
        localEntity.messageField.mid = groupInfoBean.lastMsgId;
        localEntity.messageField.chatTime = groupInfoBean.lastChatTime;
//        localEntity.messageField.mid = groupInfoBean.lastChatTime;
        localEntity.messageField.msgState = groupInfoBean.lastChatStatus;
        localEntity.messageField.cmid = groupInfoBean.lastChatClientMessageId;

        localEntity.setStateType(ContactLocalEntity.StateType.IS_GROUP_FROM_SOURCE, groupInfoBean.friendHasReplay);


        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ContactLocalEntity.LocalField.CONTACT_REFRESH_FULL_INFO_TIME, groupInfoBean.lastRefreshTime);
            jsonObject.put(ContactLocalEntity.LocalField.GROUP_FROM_SOURCE, groupInfoBean.fromSource);
            jsonObject.put(ContactLocalEntity.LocalField.GROUP_AT_FROM_MESSAGE_ID, groupInfoBean.atFromMessageId);
            localEntity.extStr = jsonObject.toString();
        } catch (Exception e) {
            //ignore
            TLog.error(TAG, e, "toGroupLocalEntity groupInfoBean = %s", groupInfoBean);
        }

        return localEntity;
    }

    public static GroupInfoBean toGroupInfoBean(GroupEntity groupEntity, ContactLocalEntity localEntity) {
        if (groupEntity == null) return null;
        final GroupInfoBean groupInfoBean = toGroupInfoBean(groupEntity);
        return toGroupInfoBean(groupInfoBean, localEntity);
    }

    public static GroupInfoBean toGroupInfoBean(GroupInfoBean groupInfoBean, ContactLocalEntity localEntity) {
        if (localEntity == null) {
            TLog.error(TAG, "toGroupInfoBean  localEntity is null groupId= %d ", groupInfoBean.groupId);
            return groupInfoBean;
        }
        groupInfoBean.noneReadCount = localEntity.noneReadCount;
        groupInfoBean.draft = localEntity.draft;
        groupInfoBean.updateTime = localEntity.updateTime;
        groupInfoBean.lastChatText = localEntity.messageField.summary;
        groupInfoBean.lastMsgId = localEntity.messageField.mid;
        groupInfoBean.lastChatTime = localEntity.messageField.chatTime;
        groupInfoBean.lastChatStatus = localEntity.messageField.msgState;
        groupInfoBean.lastChatClientMessageId = localEntity.messageField.cmid;
        groupInfoBean.friendHasReplay = localEntity.isStateType(ContactLocalEntity.StateType.IS_GROUP_FROM_SOURCE);

        ContactLocalEntity.LocalField localField = localEntity.getLocalField();
        if (localField != null) {
            groupInfoBean.lastRefreshTime = localField.lastRefreshTime;
            groupInfoBean.fromSource = localField.fromSource;
            groupInfoBean.atFromMessageId = localField.atFromMessageId;
        }
        return groupInfoBean;
    }

    private static String transferStarTypes(List<String> star) {
        StringBuilder stringBuilder = new StringBuilder();
        if (star != null) {
            for (String value : star) {
                if (LText.empty(value)) continue;
                stringBuilder.append(value);
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();
    }

    private static List<String> transferStarTypes(String starTypes) {
        if (starTypes != null) {
            final String[] split = starTypes.split(",");
            return Arrays.asList(split);
        }
        return null;
    }
}

