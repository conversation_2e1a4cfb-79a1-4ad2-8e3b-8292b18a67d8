package com.hpbr.bosszhipin.data.db.dao;

import android.util.Log;

import androidx.annotation.WorkerThread;

import com.bszp.kernel.DataKernel;
import com.bszp.kernel.chat.db.ChatDatabaseHelper;
import com.bszp.kernel.chat.db.entity.GroupMemberEntity;
import com.bszp.kernel.chat.logic.contact.ContactUtils;
import com.bszp.kernel.chat.service.ChatHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GetGroupContactListResponse;
import net.bosszhipin.api.bean.ServerAddFriendBean;
import net.bosszhipin.api.bean.ServerGroupInfoBean;
import net.bosszhipin.api.bean.ServerGroupMemberBean;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import message.handler.dao.MessageDoctorFactory;

/**
 * Created by wangtian on 16/11/1.
 */

public class DaoFactory {

    static IContactDao contactDao;
    static IGroupInfoDao groupInfoDao;
    static IGroupMemberDao groupInfoMemberDao;
    static IContactExtraInfoDao iContactExtraInfoDao;

    private volatile static Map<String, AbsDao> daoCache = new HashMap<>();

    public static <A extends AbsDao> A getDao(Class<A> type) {
        String typeName = type.getName();
        if (daoCache.containsKey(typeName)) {
            return (A) daoCache.get(typeName);
        }
        synchronized (DaoFactory.class) {
            if (daoCache.containsKey(typeName)) {
                return (A) daoCache.get(typeName);
            } else {
                A obj = createDao(type);
                if (obj != null) {
                    daoCache.put(typeName, obj);
                    return obj;
                }
            }
        }
        return null;
    }

    static final boolean isUserChatService = DataKernel.isUserChatService();

    static <A extends AbsDao> A createDao(Class<A> type) {
        try {
            Class t = Class.forName(type.getName());
            Constructor c0 = t.getDeclaredConstructor();
            c0.setAccessible(true);
            if (t != null) {
                return (A) c0.newInstance();
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static IGroupInfoDao getGroupInfoDao() {
        if (groupInfoDao == null) {
            TLog.info(ContactDoctorFactory.TAG, "getGroupInfoDao isUserChatService %b", isUserChatService);
            if (isUserChatService) {
                groupInfoDao = syncDataBase(IGroupInfoDao.class, new GroupInfoDaoImpl(), new GroupInfoDaoKernel());
            } else {
                groupInfoDao = new GroupInfoDaoImpl();
            }
        }
        return groupInfoDao;
    }

    public static IGroupMemberDao getGroupMemberDao() {
        if (groupInfoMemberDao == null) {
            if (isUserChatService) {
                groupInfoMemberDao = syncDataBase(IGroupMemberDao.class, new GroupMemberDao(), new GroupMemberDaoKernel());
            } else {
                groupInfoMemberDao = new GroupMemberDao();
            }
        }
        return groupInfoMemberDao;
    }

    public static IContactDao getContactDao() {
        if (contactDao == null) {
            TLog.info(ContactDoctorFactory.TAG, "getContactDao isUserChatService %b", isUserChatService);
            if (isUserChatService) {
                contactDao = syncDataBase(IContactDao.class, new ContactDaoImpl(), new ContactDaoKernel());
            } else {
                contactDao = new ContactDaoImpl();
            }
        }
        return contactDao;
    }

    public static IContactExtraInfoDao getContactExtraInfoDao() {
        if (iContactExtraInfoDao == null) {
            if (isUserChatService) {
                iContactExtraInfoDao = syncDataBase(IContactExtraInfoDao.class, new ContactExtraInfoDaoImpl(), new ContactExtraInfoDaoKernel());
            } else {
                iContactExtraInfoDao = new ContactExtraInfoDaoImpl();
            }
        }
        return iContactExtraInfoDao;
    }

    //同步存储
    public static <T> T syncDataBase(Class<T> tClass, Object object, Object oldObject) {
        return (T) Proxy.newProxyInstance(tClass.getClassLoader(), new Class[]{tClass}, new InvocationHandler() {
            boolean isOpenDB = false; // 避免 WCDB 第一次 open 时 执行sql 耗时问题 分开统计
            private int count = 0;
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                long startTime = System.currentTimeMillis();
                Object returnObject = method.invoke(object, args);
                long delayTime = System.currentTimeMillis() - startTime;
                SyncDataBase syncData = method.getAnnotation(SyncDataBase.class);
                //先不迁移
                if (syncData != null) {
                    try {
                        if (args != null && args.length > 0 && args[0] instanceof List) { // 优化速度
                            TLog.info("callOldProxy", "class = %s  method = %s delayTime = %d ", object.getClass().getSimpleName(), method.getName(), delayTime);
                        } else {
                            TLog.info("callOldProxy", "class = %s  method = %s delayTime = %d args = %s ", object.getClass().getSimpleName(), method.getName(), delayTime, Arrays.toString(args));
                        }
                        startTime = System.currentTimeMillis();
                        Object invoke = method.invoke(oldObject, args);
                        long delayTime2 = System.currentTimeMillis() - startTime;
                        TLog.info("callOldProxy", "class = %s  [backup] method = %s delayTime = %d returnObject = %s", oldObject.getClass().getSimpleName(), method.getName(), delayTime2, invoke);
                        //执行时间超过1s
                        if (delayTime > 1000 || delayTime2 > 1000) {
                            String type = MessageDoctorFactory.WCDB_CHAT_TIMEOUT;
                            if (delayTime > 20000 || delayTime2 > 20000) { //减少异常上报 后台进程冻结了
                                type = MessageDoctorFactory.IMEOUT;
                            } else if (delayTime > 1000) {
                                type = MessageDoctorFactory.ORMDB_CHAT_TIMEOUT;
                            } else if (!isOpenDB) {
                                type = MessageDoctorFactory.WCDB_OPEN_DB_CHAT_TIMEOUT;
                            }
                            ApmAnalyzer.create().action(MessageDoctorFactory.TAG, type)
                                    .p2(String.valueOf(delayTime2))
                                    .p3(String.valueOf(delayTime))
                                    .p4(method.getName())
                                    .p5(String.valueOf(ChatDatabaseHelper.getOpenTime()))
                                    .report();
                        }
                        count = 0;
                        isOpenDB = true;
                        ContactDoctorFactory.checkResult(tClass, syncData == null, method, returnObject, invoke);
                    } catch (Throwable e) {
                        count++;
                        if (count > 15) {
                            DataKernel.setUserChatService(0);
                        }
                        e.printStackTrace();
                        ApmAnalyzer.create().action(MessageDoctorFactory.TAG, MessageDoctorFactory.WCDB_CHAT_EXCEPTION)
                                .p2(oldObject.getClass().getName())
                                .p3(method.getName())
                                .p4(Log.getStackTraceString(e))
                                .p5(String.valueOf(count > 15))
                                .report();
                        TLog.error("callOldProxy", e, "method = %s startTime = %d", method, System.currentTimeMillis() - startTime);
                        if (BuildInfoUtils.isDebug()) {
                            if (Log.getStackTraceString(e).contains("attempt to re-open an already-closed object")) {
                                Utils.runOnUiThread(() -> ToastUtils.showText("测试数据：版本过低低，覆盖安装出现问题，请卸载重新安装或者清理数据"));
                            }
                        }
                    }
                }
                return returnObject;
            }
        });
    }


    public static <T> T multiSyncDataBase(Class<T> tClass, Object object, Object oldObject) {
        return (T) Proxy.newProxyInstance(tClass.getClassLoader(), new Class[]{tClass}, new InvocationHandler() {
            volatile int methodIndex = 0;
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                Object returnObject = method.invoke(object, args);
                SyncDataBase syncData = method.getAnnotation(SyncDataBase.class);
                //先不迁移
                if (syncData != null/* && MigrationWCDB.isMigrationMessage()*/) {
                    methodIndex++;
                    MessageDoctorFactory.POOL.execute(new Runnable() {
                        long startTime = System.currentTimeMillis();
                        @Override
                        public void run() {
                            long delayTime = System.currentTimeMillis() - startTime;
                            if (delayTime > 1000) {
                                ApmAnalyzer.create().action(MessageDoctorFactory.TAG, MessageDoctorFactory.WCDB_CHAT_EXCEPTION).p2("delayTime")
                                        .p3(String.valueOf(delayTime))
                                        .report();
                            }
                            try {
                                startTime = System.currentTimeMillis();
                                TLog.info("callOldProxy", "method = %s args = %s methodIndex = %d", method.getName(), Arrays.toString(args), methodIndex);
                                Object invoke = method.invoke(oldObject, args);
                                TLog.info("callOldProxy", "method = %s returnObject = %s methodIndex = %d", method.getName(), invoke, methodIndex);
                                long delayTime2 = System.currentTimeMillis() - startTime;
                                if (delayTime2 > 1000) {
                                    ApmAnalyzer.create().action(MessageDoctorFactory.TAG, MessageDoctorFactory.WCDB_CHAT_EXCEPTION).p2("delayTime2").p3(String.valueOf(delayTime2)).report();
                                }
                            } catch (Throwable e) {
                                e.printStackTrace();
                                ApmAnalyzer.create().action(MessageDoctorFactory.TAG, MessageDoctorFactory.WCDB_CHAT_EXCEPTION)
                                        .p2(oldObject.getClass().getName())
                                        .p3(method.getName())
                                        .p4(e.getMessage()).report();
                                TLog.error("callOldProxy", e, "method = %s startTime = %d", method, System.currentTimeMillis() - startTime);
                            }
                            //重试 时间 重置
                            startTime = System.currentTimeMillis();
                            methodIndex--;
                        }
                    });
                }
                return returnObject;
            }
        });
    }

    @WorkerThread
    public static void updateFullServer(ServerAddFriendBean serverAddFriend) {
        if (!isUserChatService) {
            return;
        }
        try {
            ChatHelper.getContactDao().updateContactFullInfo(ContactUtils.coverFullInfoData(serverAddFriend));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("updateFullServer")
                    .p3(String.valueOf(serverAddFriend.getFriendId()))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void upsertAllContactBaseInfoList(List<ServerAddFriendBean> serverAddFriends) {
        if (!isUserChatService) {
            return;
        }
        long startTime = System.currentTimeMillis();
        try {
            ChatHelper.getContactDao().replaceAllContacts(ContactUtils.coverBaseInfoData(serverAddFriends));
            ContactDoctorFactory.markMigrationContact();
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("upsertAllContactBaseInfo")
                    .p3(String.valueOf(LList.getCount(serverAddFriends)))
                    .p4(e.getMessage())
                    .report();
        }
        long time = System.currentTimeMillis() - startTime;
        if (time > 2000) {
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("BaseInfoListTimeout")
                    .p3(String.valueOf(LList.getCount(serverAddFriends)))
                    .p4(String.valueOf(time))
                    .report();
        }
        TLog.info("contact", "upsertAllContactBaseInfoList count = %d time = %d", LList.getCount(serverAddFriends), time);
    }

    @WorkerThread
    public static void quickDelete() {
        if (!isUserChatService) {
            return;
        }
        try {
            ChatHelper.getContactDao().quickDelete();
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("quickDelete")
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void upsertContactBaseInfoList(List<ServerAddFriendBean> serverAddFriends) {
        if (!isUserChatService) {
            return;
        }
        long startTime = System.currentTimeMillis();
        try {
            ChatHelper.getContactDao().replaceContacts(ContactUtils.coverBaseInfoData(serverAddFriends));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("upsertContactBaseInfoList")
                    .p3(String.valueOf(LList.getCount(serverAddFriends)))
                    .p4(e.getMessage())
                    .report();
        }
        long time = System.currentTimeMillis() - startTime;
        if (time > 2000) {
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("BaseInfoListV2Timeout")
                    .p3(String.valueOf(LList.getCount(serverAddFriends)))
                    .p4(String.valueOf(time))
                    .report();
        }
        TLog.info("contact", "upsertContactBaseInfoList count = %d time = %d", LList.getCount(serverAddFriends), time);
    }

    @WorkerThread
    public static void upsertAllGroupList(GetGroupContactListResponse resp) {
        if (!isUserChatService) {
            return;
        }
        List<ServerGroupInfoBean> groupList = new ArrayList<>();
        //群聊
        if (resp.groups != null) {
            groupList.addAll(resp.groups);
        }
        //引力波
        if (resp.gravityGroups != null) {
            groupList.addAll(resp.gravityGroups);
        }
        //校友群
        if (!LList.isEmpty(resp.alumnusGroups)) {
            groupList.addAll(resp.alumnusGroups);
        }
        //双叶草群
        if (!LList.isEmpty(resp.directGroups)) {
            groupList.addAll(resp.directGroups);
        }
        try {
            ChatHelper.getGroupDao().replaceAllGroups(ContactUtils.coverGroups(groupList));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("insertGroupList")
                    .p3(String.valueOf(LList.getCount(groupList)))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void insertGroup(ServerGroupInfoBean serverGroupInfoBean) {
        if (!isUserChatService) {
            return;
        }
        if (serverGroupInfoBean == null || serverGroupInfoBean.groupId <= 0) {
            return;
        }
        try {
            ChatHelper.getGroupDao().insert(ContactUtils.coverGroup(serverGroupInfoBean));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("insertGroup")
                    .p3(String.valueOf(serverGroupInfoBean.groupId))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void updateGroupFullInfo(ServerGroupInfoBean group) {
        if (!isUserChatService) {
            return;
        }
        try {
            ChatHelper.getGroupDao().insert(ContactUtils.coverGroup(group));
            ChatHelper.getGroupDao().replaceAllGroupMembers(ContactUtils.coverGroupMembers(group));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("updateGroupFullInfo")
                    .p3(String.valueOf(LList.getCount(group.members)))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void quitGroup(long groupId) {
        if (!isUserChatService) {
            return;
        }
        try {
            ChatHelper.getGroupDao().quitGroup(groupId);
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("updateGroupFullInfo")
                    .p3(String.valueOf(groupId))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void upsertGroupAllMembers(long groupId, String gid, List<ServerGroupMemberBean> newMembers) {
        if (!isUserChatService) {
            return;
        }
        try {
            ChatHelper.getGroupDao().insertMemberServerAll(ContactUtils.coverGroupMembers(groupId, gid, newMembers));
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("insertGroupMembers")
                    .p3(String.valueOf(groupId))
                    .p4(e.getMessage())
                    .report();
        }
    }

    @WorkerThread
    public static void insertGroupMember(long groupId, String gid, ServerGroupMemberBean newMember) {
        if (!isUserChatService) {
            return;
        }
        try {
            GroupMemberEntity groupMemberEntity = ContactUtils.coverGroupMember(newMember);
            groupMemberEntity.setGroupId(groupId);
            groupMemberEntity.setGid(gid);
            ChatHelper.getGroupDao().insertMemberServer(groupMemberEntity);
        } catch (Exception e) {
            e.printStackTrace();
            ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "Exception")
                    .p2("insertGroupMembers")
                    .p3(String.valueOf(groupId))
                    .p4(e.getMessage())
                    .report();
        }
    }

}
