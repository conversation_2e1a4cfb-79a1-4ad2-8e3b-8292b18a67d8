package com.hpbr.bosszhipin.data.db.dao;

import android.content.ContentValues;
import android.database.sqlite.SQLiteDatabase;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.monch.lbase.orm.db.model.ColumnsValue;
import com.monch.lbase.orm.db.model.ConflictAlgorithm;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import java.util.List;


/**
 * Created by wangtian on 2018/4/23.
 */

class GroupInfoDaoImpl implements IGroupInfoDao {
    private static final String TAG = "GroupInfoDaoImpl";
    @Override
    public void insert(GroupInfoBean bean) {
        App.get().db().save(bean);
    }

    @Override
    public void insertAll(List<GroupInfoBean> list) {
        App.get().db().save(list);
    }

    @Override
    public List<GroupInfoBean> queryGroupList() {
        QueryBuilder qb = new QueryBuilder(GroupInfoBean.class);
        qb.where("myUid=" + UserManager.getUID(), null);
        List<GroupInfoBean> list = App.get().db().query(qb);
        return list;
    }

    @Override
    public GroupInfoBean queryGroupInfoByGroupId(long groupId) {
        QueryBuilder qb = new QueryBuilder(GroupInfoBean.class);
        qb.where("myUid=" + UserManager.getUID() + " and groupId = " + groupId, null);
        List<GroupInfoBean> list = App.get().db().query(qb);
        if (list == null || list.size() == 0) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 传入的参数已经是最新的
     **/
    @Override
    public long updateGroupInfoAllFiled(GroupInfoBean bean) {
        return App.get().db().update(bean);
    }

    /**
     * 传入的参数已经是最新的
     **/
    @Override
    public long updateGroupInfoServerFiled(GroupInfoBean bean) {
        return App.get().db().update(bean);
    }

    @Override
    public int updateGroupInfo(long groupId, ColumnsValue columnsValue) {
        GroupInfoBean bean = queryGroupInfoByGroupId(groupId);
        if (bean != null) {
            return App.get().db().update(bean, columnsValue, ConflictAlgorithm.Replace);
        }
        return 0;
    }

    @Override
    public long updateGroupInfo(GroupInfoBean groupInfoBean){
        return 1;
    }
    @Override
    public int delete(long groupId) {
        return App.get().db().delete(GroupInfoBean.class, "myUid=" + UserManager.getUID() + " and groupId = " + groupId);
    }

    @Override
    public int deleteAll() {
        return App.get().db().delete(GroupInfoBean.class, "myUid=" + UserManager.getUID());
    }

    @Override
    public void repairLastMid() {
        SQLiteDatabase database = null;
        try {
            database = App.get().db().getWritableDatabase();
            database.beginTransaction();
            database.execSQL("UPDATE GroupInfo SET lastMsgId = 0");
            database.setTransactionSuccessful();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != database) {
                database.endTransaction();
            }
        }
    }

    @Override
    public int batchUpdateGroupReadInfo(List<GroupInfoBean> list) {
        if (LList.isEmpty(list)){
            return 0;
        }

        SQLiteDatabase database = null;
        int updateCount = 0;
        try {
            database = App.get().db().getWritableDatabase();
            database.beginTransaction();
            ContentValues contentValues = new ContentValues();
            String where = "myUid=? AND groupId=?";
            String myUid = String.valueOf(UserManager.getUID());
            for (GroupInfoBean infoBean : list) {
                if (infoBean == null) {
                    continue;
                }
                contentValues.put("noneReadCount", infoBean.noneReadCount);
                contentValues.put("lastChatStatus", infoBean.lastChatStatus);
                updateCount += database.update(TABLE_NAME, contentValues, where, new String[]{myUid, String.valueOf(infoBean.groupId)});
            }
            database.setTransactionSuccessful();
        } catch (Exception e) {
            TLog.error(TAG, "batchUpdateGroupReadInfo fails. error msg = %s", e);
        } finally {
            if (null != database) {
                database.endTransaction();
            }
        }
        return updateCount;
    }
}
