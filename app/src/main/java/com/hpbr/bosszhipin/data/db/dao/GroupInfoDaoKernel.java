package com.hpbr.bosszhipin.data.db.dao;

import com.bszp.kernel.chat.db.ChatDatabaseHelper;
import com.bszp.kernel.chat.db.GroupDao;
import com.bszp.kernel.chat.db.entity.GroupData;
import com.bszp.kernel.chat.service.ChatHelper;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.monch.lbase.orm.db.model.ColumnsValue;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;

class GroupInfoDaoKernel implements IGroupInfoDao {

    public static final String TAG = "GroupInfoDaoKernel";

    private GroupDao getGroupDao() {
        return ChatHelper.getGroupDao();
    }

    com.bszp.kernel.chat.db.LocalDataDao getLocalDataDao() {
        return ChatHelper.getLocalDataDao();
    }

    @Override
    public void insert(GroupInfoBean bean) {
        replaceGroupInfo(ContactTransformUtils.toGroupData(bean));
    }

    @Override
    public void insertAll(List<GroupInfoBean> list) {
        replaceGroupInfo(ContactTransformUtils.toGroupDataList(list));
    }

    @Override
    public List<GroupInfoBean> queryGroupList() {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            final List<GroupData> groupDataList = groupDao.queryGroupDataList();
            List<GroupInfoBean> groupEntities = new ArrayList<>(groupDataList.size());
            for (GroupData groupData : groupDataList) {
                groupEntities.add(ContactTransformUtils.toGroupInfoBean(groupData));
            }
            return groupEntities;
        }
        return new ArrayList<>();
    }

    @Override
    public GroupInfoBean queryGroupInfoByGroupId(long groupId) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            GroupData groupData = groupDao.queryGroupDataById(groupId);
            return ContactTransformUtils.toGroupInfoBean(groupData);
        }
        return null;
    }

    @Override
    public long updateGroupInfoAllFiled(GroupInfoBean bean) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return replaceGroupInfo(ContactTransformUtils.toGroupData(bean));
        }
        return 0;
    }

    @Override
    public long updateGroupInfoServerFiled(GroupInfoBean bean) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.replace(ContactTransformUtils.toGroupEntity(bean));
        }
        return 0;
    }

    @Override
    public int updateGroupInfo(long groupId, ColumnsValue columnsValue) {
//        GroupDao groupDao = getGroupDao();
//        if (groupDao != null) {
//            StringBuilder params = new StringBuilder();
//            StringBuilder localParams = new StringBuilder();
//            StringBuilder valueParams = new StringBuilder();
//            for (int i = 0; i < columnsValue.columns.length; i++) {
//                if ("draft".equals(columnsValue.columns[i])) {
//                    localParams.append(",").append(columnsValue.columns[i]);
//                    valueParams.append(",?");
//                } else {
//                    params.append(columnsValue.columns[i]).append(" = ?");
//                }
//            }
//            if (params.length() > 0) {
//                groupDao.updateField(new SimpleSQLiteQuery("UPDATE Group SET " + params + " WHERE groupId = " + groupId, columnsValue.values));
//            }
//            if (localParams.length() > 0) {
//                groupDao.updateField(new SimpleSQLiteQuery("INSERT OR REPLACE INTO ContactLocal (friendId,friendSource" + localParams + " VALUES (" + groupId + ",-1" + valueParams + ")", columnsValue.values));
//            }
//        }
        return 1;
    }

    @Override
    public long updateGroupInfo(GroupInfoBean groupInfoBean) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return replaceGroupInfo(ContactTransformUtils.toGroupData(groupInfoBean));
        }
        return 0;
    }

    @Override
    public int delete(long groupId) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.delete(groupId);
        }
        TLog.error(TAG, "group delete groupId = %d", groupId);
        return 0;
    }

    @Override
    public int deleteAll() {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.deleteAll();
        }
        return 0;
    }

    long replaceGroupInfo(List<GroupData> groups) {
        ChatDatabaseHelper.runInTransaction(() -> {
            GroupDao groupDao = getGroupDao();
            if (groupDao != null) {
                //将所有联系人置隐藏状态
                groupDao.quickDelete();
                for (GroupData group : groups) {
                    groupDao.replace(group.getGroupEntity());
                    groupDao.replaceLocal(group.getLocalEntity());
                }
            }
        });
        return LList.getCount(groups);
    }

    long replaceGroupInfo(final GroupData group) {
        final long returnId = 0;
        ChatDatabaseHelper.runInTransaction(() -> {
            GroupDao groupDao = getGroupDao();
            if (groupDao != null) {
                groupDao.replace(group.getGroupEntity());
                groupDao.replaceLocal(group.getLocalEntity());
            }
        });
        return 1;
    }

    @Override
    public void repairLastMid() {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            groupDao.repairLastMid();
        }
    }

    @Override
    public int batchUpdateGroupReadInfo(List<GroupInfoBean> list) {
        return 0;
    }
}
