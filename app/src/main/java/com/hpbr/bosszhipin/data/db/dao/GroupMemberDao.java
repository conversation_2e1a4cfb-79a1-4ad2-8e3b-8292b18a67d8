package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.monch.lbase.orm.db.model.ConflictAlgorithm;

import java.util.List;


/**
 * Created by wangtian on 2018/4/23.
 */

class GroupMemberDao implements IGroupMemberDao {

    @Override
    public void insert(GroupMemberBean bean) {
        App.get().db().save(bean);
    }

    @Override
    public void insertAll(List<GroupMemberBean> list) {
        App.get().db().save(list);
    }

    @Override
    public void update(List<GroupMemberBean> list) {
        App.get().db().update(list, ConflictAlgorithm.Replace);
    }

    @Override
    public List<GroupMemberBean> queryGroupList(long groupId) {
        QueryBuilder qb = new QueryBuilder(GroupMemberBean.class);
        qb.where("myUid=" + UserManager.getUID() + " and groupId = " + groupId, null);
        List<GroupMemberBean> list = App.get().db().query(qb);
        return list;
    }

    @Override
    public int delete(long groupId) {
        return App.get().db().delete(GroupMemberBean.class, "myUid=" + UserManager.getUID() + " and groupId = " + groupId);
    }

    @Override
    public int delete(long groupId, long memberId) {
        return App.get().db().delete(GroupMemberBean.class, "myUid=" + UserManager.getUID() + " and groupId = " + groupId + " and memberId = " + memberId);
    }

    @Override
    public int deleteAll() {
        return App.get().db().delete(GroupMemberBean.class, "myUid=" + UserManager.getUID());
    }

}
