package com.hpbr.bosszhipin.data.db.dao;

import com.bszp.kernel.chat.db.GroupDao;
import com.bszp.kernel.chat.db.entity.GroupMemberEntity;
import com.bszp.kernel.chat.service.ChatHelper;
import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;

import java.util.ArrayList;
import java.util.List;


public class GroupMemberDaoKernel implements IGroupMemberDao {


    private GroupDao getGroupDao() {
        return ChatHelper.getGroupDao();
    }


    @Override
    public void insert(GroupMemberBean bean) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            groupDao.insert(ContactTransformUtils.toGroupMemberEntity(bean));
        }
    }

    @Override
    public void insertAll(List<GroupMemberBean> list) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            List<GroupMemberEntity> groupMemberBeans = new ArrayList<>(list.size());
            for (GroupMemberBean memberBean : list) {
                groupMemberBeans.add(ContactTransformUtils.toGroupMemberEntity(memberBean));
            }
            groupDao.insertGroupMemberAll(groupMemberBeans);
        }
    }

    @Override
    public void update(List<GroupMemberBean> list) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            List<GroupMemberEntity> groupMemberBeans = new ArrayList<>(list.size());
            for (GroupMemberBean memberBean : list) {
                groupMemberBeans.add(ContactTransformUtils.toGroupMemberEntity(memberBean));
            }
            groupDao.insertGroupMemberAll(groupMemberBeans);
        }

    }

    @Override
    public List<GroupMemberBean> queryGroupList(long groupId) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            List<GroupMemberEntity> groupMemberEntities = groupDao.queryGroupMembersById(groupId);
            List<GroupMemberBean> groupMemberBeans = new ArrayList<>(groupMemberEntities.size());
            for (GroupMemberEntity groupMemberEntity : groupMemberEntities) {
                groupMemberBeans.add(ContactTransformUtils.toGroupMemberBean(groupMemberEntity));
            }
            return groupMemberBeans;
        }

        return new ArrayList<>();
    }

    @Override
    public int delete(long groupId) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.deleteGroupMembersById(groupId);
        }
        return 0;
    }

    @Override
    public int delete(long groupId, long memberId) {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.deleteGroupMemberById(groupId, memberId);
        }
        return 0;
    }

    @Override
    public int deleteAll() {
        GroupDao groupDao = getGroupDao();
        if (groupDao != null) {
            return groupDao.deleteGroupMemberAll();
        }
        return 0;
    }


}
