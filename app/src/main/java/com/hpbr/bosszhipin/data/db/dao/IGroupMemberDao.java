package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;

import java.util.List;

public interface IGroupMemberDao {

    @SyncDataBase
    void insert(GroupMemberBean bean);

    @SyncDataBase
    void insertAll(List<GroupMemberBean> list);

    @SyncDataBase
    void update(List<GroupMemberBean> list);

    List<GroupMemberBean> queryGroupList(long groupId);

    @SyncDataBase
    int delete(long groupId);

    @SyncDataBase
    int delete(long groupId, long memberId);

    @SyncDataBase
    int deleteAll();

}
