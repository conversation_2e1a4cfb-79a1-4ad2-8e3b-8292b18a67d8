package com.hpbr.bosszhipin.data.db.dao;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.QuitMemberBean;
import com.monch.lbase.orm.db.assit.QueryBuilder;

import java.util.List;


/**
 * Created by wa<PERSON><PERSON> on 2018/4/23.
 */

public class QuitMemberDao extends AbsDao {

    private QuitMemberDao() {
        this.type = QuitMemberBean.class;
    }


    public void test() {
        QueryBuilder qb = new QueryBuilder(QuitMemberBean.class);
        qb.createStatementForCount();
        App.get().db().queryCount(qb);
    }

    public void insertAll(long groupId,List<QuitMemberBean> list) {
        if(list == null || list.size() == 0){
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        int count = list.size();
        for (int i=0;i<count;i++){
            sb.append(list.get(i).userId);
            if(i != count - 1){
                sb.append(",");
            }
        }
        sb.append(")");
        App.get().db().delete(QuitMemberBean.class,"groupId = "+groupId +" and userId in "+sb.toString());
        App.get().db().save(list);
    }

    public QuitMemberBean query(long groupId,long userId){
        QueryBuilder qb = new QueryBuilder(QuitMemberBean.class);
        qb.where("groupId="+groupId +" and userId=" + userId, null);
        List<QuitMemberBean> list = App.get().db().query(qb);
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

}
