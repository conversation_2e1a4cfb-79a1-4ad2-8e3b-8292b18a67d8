package com.hpbr.bosszhipin.data.db.entry;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.chat.Constants;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.bean.ServerGroupInfoBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON> on 2018/4/23.
 */
@Table("GroupInfo")
public class GroupInfoBean extends BaseEntityAuto {
    public static final int GROUP_SOURCE_AN_XIN_BAO = 19; // 安心保群聊

    public static final int GROUP_SOURCE_ANTELOPE = Constants.GROUP_SOURCE_ANTELOPE; // 羚羊计划

    private static final long serialVersionUID = -1;
    public static final int SWITCH_OFF = 0;
    public static final int SWITCH_ON = 1;
    public static final int USER_TYPE_WUKONG_EMPLOYER = 1;
    public static final int USER_TYPE_WUKONG_SUPPLIER = 2;
    public static final int USER_TYPE_WUKONG_UNKNOWN = 0;

    public long myUid;

    public long groupId;

    public String gid;

    public String name;

    public String avatarUrl;

    public String notice;

    public String introduction;

    public int count;

    public int watch;//是否关注（空：不设置；0：否；1：是）

    public int silent;//是否消息免打扰（空：不设置；0：否；1：是）

    public long adminId;

    public float version;

    public int flag;

    /**
     * 最后聊天时间
     */
    public long lastChatTime;

    public long updateTime;
    /**
     * 最后聊天文本
     */
    public String lastChatText;

    /**
     * 最后一条消息的mid
     */
    public long lastMsgId;


    /**
     * 最后聊天消息状态，-1为好友发送，0为发送中，1为发送成功，2为发送失败，3为已读
     */
    public int lastChatStatus = -1;

    /**
     * 最后聊天消息的消息ID
     */
    public long lastChatClientMessageId = -1L;

    /**
     * 刷新base info的时间点
     */
    public long lastRefreshTime;

    public int noneReadCount;

    public long atFromMessageId;

    //该字段接口没有返回，只依赖action消息->小尾巴
    public String fromSource;

    /**
     * 9 课程训练群 10 双叶草 2人群聊
     * 6表示校友大群  7表示校友二人群
     * <p>
     * 引力波 专用 类型 5 ,11,12
     * 结合 internal 判断 是否是我公司pm人员（1:是，0:不是）(有了社区：对命中方是2，对内部人员是3)
     * <p>
     * 判断是否LIGO（包括产品中心、有了社区和直播平台）：source in (5, 11, 12)
     * 判断展示标签：产品中心 internal in (0,1),  有了社区 internal in (2,3)，直播平台 internal in (4,5)（4外部 5内部）
     * 分组筛选：保持不变 type>0，显示typeName
     */
    public int source;  // 该类群新增source字段

    //是否被踢出群
    public boolean isQuit;

    //是否隐藏
    public boolean isHide;

    /*
        1116.250 悟空群聊任务id
     */
    public int type;//引力波群场景类型

    public String typeName;//引力波群场景类型名称

    public int internal;//是否是我公司pm人员（1:是，0:不是）(有了社区：对命中方是2，对内部人员是3)

    public String draft;//草稿

    public long quoteMsgId;


    public boolean friendHasReplay;//好友是否回复,引力波需要筛选

    public int noticeTopStatus; // 1109.51 群公告置顶状态 0:未置顶 1:已置顶

    public String label; // 1112.61 羚羊计划标签

    public int employer; // 1116.250  1雇主 2服务方 0不确定身份 仅限悟空群聊返回


    public boolean isWatched() {
        return watch == SWITCH_ON;
    }

    public boolean isSilent() {
        return silent == SWITCH_ON;
    }

    public boolean isWukongEmployer() {
        return employer == USER_TYPE_WUKONG_EMPLOYER;
    }

    public boolean isWukongSupplier() {
        return employer == USER_TYPE_WUKONG_SUPPLIER;
    }


    public boolean isAdmin() {
        return UserManager.getUID() == adminId;
    }

    public static GroupInfoBean fromServerGroupInfoBean(ServerGroupInfoBean bean) {
        GroupInfoBean groupInfoBean = new GroupInfoBean();
        groupInfoBean.name = bean.name;
        groupInfoBean.avatarUrl = bean.avatarUrl;
        groupInfoBean.count = bean.count;
        groupInfoBean.version = bean.version;
        groupInfoBean.flag = bean.flag;
        groupInfoBean.introduction = bean.introduction;
        groupInfoBean.notice = bean.notice;
        groupInfoBean.watch = bean.watch;
        groupInfoBean.silent = bean.silent;
        groupInfoBean.adminId = bean.adminId;
        groupInfoBean.groupId = bean.groupId;
        groupInfoBean.gid = bean.gid;
        groupInfoBean.isQuit = false;
        groupInfoBean.myUid = UserManager.getUID();
        groupInfoBean.type = bean.type;
        groupInfoBean.typeName = bean.typeName;
        groupInfoBean.internal = bean.internal;
        groupInfoBean.source = bean.source;
        groupInfoBean.isHide = (UserManager.isBossRole() && bean.source == 10)||bean.hiddenStatus==1;
        groupInfoBean.noticeTopStatus = bean.noticeTopStatus;
        groupInfoBean.label = bean.label;
        groupInfoBean.employer = bean.employer;
        // 把本地的最后一条消息赋值
        GroupInfoBean tempGroupBean = GroupManager.getInstance().getGroupInfo(bean.groupId);
        if (tempGroupBean != null) {
            String lastText = tempGroupBean.lastChatText;
            if (!LText.empty(lastText)) {
                groupInfoBean.lastChatText = lastText;
            }
            groupInfoBean.lastChatTime = tempGroupBean.lastChatTime;
            groupInfoBean.lastMsgId = tempGroupBean.lastMsgId;
            groupInfoBean.fromSource = tempGroupBean.fromSource;
            groupInfoBean.noneReadCount = tempGroupBean.noneReadCount;
            groupInfoBean.friendHasReplay = tempGroupBean.friendHasReplay;
        }

        return groupInfoBean;

    }

    public GroupInfoBean copyAllFiled(GroupInfoBean groupInfoBean) {
        this.name = groupInfoBean.name;
        this.avatarUrl = groupInfoBean.avatarUrl;
        this.count = groupInfoBean.count;
        this.version = groupInfoBean.version;
        this.introduction = groupInfoBean.introduction;
        this.notice = groupInfoBean.notice;
        this.watch = groupInfoBean.watch;
        this.silent = groupInfoBean.silent;
        this.adminId = groupInfoBean.adminId;
        this.gid = groupInfoBean.gid;
        this.lastChatClientMessageId = groupInfoBean.lastChatClientMessageId;
        this.lastChatStatus = groupInfoBean.lastChatStatus;
        this.lastChatTime = groupInfoBean.lastChatTime;
        this.lastChatText = groupInfoBean.lastChatText;
        this.lastMsgId = groupInfoBean.lastMsgId;
        this.lastRefreshTime = groupInfoBean.lastRefreshTime;
        this.noneReadCount = groupInfoBean.noneReadCount;
        this.atFromMessageId = groupInfoBean.atFromMessageId;
        this.isQuit = groupInfoBean.isQuit;
        this.type = groupInfoBean.type;
        this.typeName = groupInfoBean.typeName;
        this.internal = groupInfoBean.internal;
        this.source = groupInfoBean.source;
        this.friendHasReplay = groupInfoBean.friendHasReplay;
        this.label = groupInfoBean.label;
        this.employer = groupInfoBean.employer;
        return this;
    }

    public GroupInfoBean copyServerFiled(GroupInfoBean groupInfoBean) {
        this.name = groupInfoBean.name;
        this.gid = groupInfoBean.gid;
        this.avatarUrl = groupInfoBean.avatarUrl;
        this.count = groupInfoBean.count;
        this.version = groupInfoBean.version;
        this.introduction = groupInfoBean.introduction;
        this.notice = groupInfoBean.notice;
        this.watch = groupInfoBean.watch;
        this.flag = groupInfoBean.flag;
        this.silent = groupInfoBean.silent;
        this.adminId = groupInfoBean.adminId;
        this.isQuit = groupInfoBean.isQuit;
        this.type = groupInfoBean.type;
        this.typeName = groupInfoBean.typeName;
        this.internal = groupInfoBean.internal;
        this.source = groupInfoBean.source;
        this.noticeTopStatus = groupInfoBean.noticeTopStatus;
        this.label = groupInfoBean.label;
        this.employer = groupInfoBean.employer;
        return this;
    }

    public static List<GroupInfoBean> fromServerBeanList(List<ServerGroupInfoBean> serverGroupInfoBeanList) {
        List<GroupInfoBean> list = new ArrayList<>();
        if (serverGroupInfoBeanList != null) {
            for (ServerGroupInfoBean bean : serverGroupInfoBeanList) {
                list.add(fromServerGroupInfoBean(bean));
            }
        }
        return list;
    }

    public boolean isSupportVideo() {
        return source == GROUP_SOURCE_ANTELOPE || source == Constants.GROUP_SOURCE_RESUME;
    }

    @NonNull
    @Override
    public String toString() {
        return GsonUtils.toJson(this);
    }
}

