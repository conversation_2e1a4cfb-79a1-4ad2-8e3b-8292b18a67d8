package com.hpbr.bosszhipin.data.db.entry;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.bean.ServerGroupMemberBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wang<PERSON> on 2018/4/23.
 */
@Table("GroupMember")
public class GroupMemberBean extends BaseEntityAuto {
    private static final long serialVersionUID = -1;
    public long myUid;
    public long groupId;
    public String gid;

    public String uid;
    public long userId;
    public String name;
    public int identity;
    public String avatarUrl;
    public String company;
    public String position;
    public int certification;
    public String signature;
    public boolean quit;
    public String prefix;
    public long addTime;//加群时间
    public int black;//是否全局拉黑
    public String securityId;
    public int isAdmin;//1是群主,群资料页面还没有入群,GroupManager没有缓存情况下,需要根据这个字段判断是否群主


    public boolean isAdmin() {
        return isAdmin == 1;
    }


    public boolean isCertificate() {
        return certification == 1;
    }

    public static GroupMemberBean fromServerBean(ServerGroupMemberBean serverBean, long groupId) {
        GroupMemberBean bean = new GroupMemberBean();
        bean.myUid = UserManager.getUID();
        bean.groupId = groupId;
        bean.gid = LText.empty(serverBean.gid) ? GroupManager.getInstance().getGid(groupId) : serverBean.gid;
        bean.uid = serverBean.uid;
        bean.userId = serverBean.userId;
        bean.name = serverBean.name;
        bean.identity = serverBean.identity;
        bean.avatarUrl = serverBean.avatarUrl;
        bean.company = serverBean.company;
        bean.position = serverBean.position;
        bean.certification = serverBean.certification;
        bean.signature = serverBean.signature;
        bean.prefix = serverBean.prefix;
        bean.black = serverBean.black;
        bean.addTime = serverBean.addTime;
        bean.isAdmin = serverBean.isAdmin;
        bean.securityId = serverBean.securityId;
        return bean;
    }


    public static List<GroupMemberBean> fromServerBeanList(List<ServerGroupMemberBean> serverGroupMemberBeanList, long groupId) {
        List<GroupMemberBean> list = new ArrayList<>();
        if (serverGroupMemberBeanList != null) {
            for (ServerGroupMemberBean bean : serverGroupMemberBeanList) {
                list.add(fromServerBean(bean, groupId));
            }
        }
        return list;
    }

}

