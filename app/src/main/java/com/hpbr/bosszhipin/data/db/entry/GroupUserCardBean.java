package com.hpbr.bosszhipin.data.db.entry;

import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GetUserGroupCardResponse;

/**
 * Author: ZhouYou
 * Date: 2018/5/5.
 * 用户自己的群名片信息
 */
public class GroupUserCardBean extends BaseEntity {
    private static final long serialVersionUID = -3080217976566720849L;
    /**
     * identity : 1
     * initFlag : 0
     * black : 0
     * userId : 6456234
     * name : 小白
     * position : hjdha
     * isAdmin : 0
     * certification : 1
     * avatarUrl : https://img2.bosszhipin.com/boss/avatar/avatar_15.png
     * addTime : 0
     * watch : 0
     * silent : 0
     * signature : 大家好，我是北京华品博睿技术有限公司的hjdha小白。我正在关注北京java方面的人才。
     */

    public int identity;
    public int initFlag; //  0(返回已设置的群名片)， 1(未设置群名片，返回建议的填写信息)
    public int black;//1禁言
    public long userId;
    public String name;
    public String position;
    public int isAdmin;
    public int certification;
    public String avatarUrl;
    public int addTime;
    public int watch;
    public int silent;
    public String signature;

    public boolean isCertificate() {
        return certification == 1;
    }
//
    public boolean isGroupCardComplete() {
        return initFlag == 0;
    }

    public static GroupUserCardBean fromJson(String json) {
        if (TextUtils.isEmpty(json)) return null;
        return GsonUtils.getGson().fromJson(json, GroupUserCardBean.class);
    }

    public void parseFromServer(GetUserGroupCardResponse groupCardResponse) {
        identity = groupCardResponse.identity;
        initFlag = groupCardResponse.initFlag;
        black = groupCardResponse.black;
        userId = groupCardResponse.userId;
        name = groupCardResponse.name;
        position = groupCardResponse.position;
        isAdmin = groupCardResponse.isAdmin;
        certification = groupCardResponse.certification;
        avatarUrl = groupCardResponse.avatarUrl;
        addTime = groupCardResponse.addTime;
        watch = groupCardResponse.watch;
        silent = groupCardResponse.silent;
        signature = groupCardResponse.signature;
    }

    @Override
    public String toString() {
        return "GroupUserCardBean{" +
                "initFlag=" + initFlag +
                ", name='" + name + '\'' +
                ", isAdmin=" + isAdmin +
                ", certification=" + certification +
                '}';
    }
}
