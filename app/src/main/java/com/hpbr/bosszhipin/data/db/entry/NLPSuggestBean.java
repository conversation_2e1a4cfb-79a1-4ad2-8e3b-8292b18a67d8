package com.hpbr.bosszhipin.data.db.entry;

import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.ChatHightLightBean;

import java.util.List;

/**
 * Created by guo<PERSON>
 * on 2017/11/21.
 */
public class NLPSuggestBean extends BaseServerBean {

    private static final long serialVersionUID = 1L;
    //服务器发送的
    public static final int HIDE_NLP = 0;
    //客户端发送的隐藏消息
    public static final int LOCAL_HIDE_NLP = -4;

    public static final int PROTOCOL_TYPE = 1;
    public static final int TEXT_MESSAGE_TYPE = 2;
    public static final int EXCHANGE_PHONE_TYPE = 3;
    public static final int EXCHANGE_WCHAR_TYPE = 4;
    public static final int REQUEST_RESUME_TYPE = 5;
    public static final int SEND_RESUME_TYPE = 6;
    public static final int INTERVIEW_TYPE = 7;
    public static final int BOSS_SEND_LOCATION_MAP = 8;
    public static final int SEND_DAILY = 9;
    public static final int SEND_BACK_CHAT = 10;
    public static final int SEND_SOUND_CALL = 11;//拨打视频-只有猎头做
    public static final int SEND_JOB_CHANGE = 12;//切换职位
    @Deprecated //12.15下线
    public static final int PHONE_AUTH = 13;//13是电话预授权气泡
    @Deprecated //12.15下线
    public static final int RESUME_AUTH = 14;//是简历预授权气泡
    public static final int OPEN_WEIXIN_NOTICE = 15;//打开微信通知
    public static final int DAILY_GUIDE = 16;//引导追聊
    public static final int GEEK_INTRODUCE_GUIDE = 17;//https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=161375578
    public static final int GEEK_CHAT_NLP = 18;//https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=168916139
    public static final int GEEK_CHAT_EMOTION = 19;//https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=187612681
    public static final int EXCHANGE_ACCEPT_PHONE = 22; // C端：接受交换电话。https://zhishu.zhipin.com/wiki/b0DRBXJ8hRg
    public static final int EXCHANGE_ACCEPT_WECHAT = 23; // C端：接受交换微信。
    public static final int VERBAL_INTERVIEW = 24; // 1217.110【招聘者】 蓝领口头约面识别与流程简化


    public static final int VIRTUAL_TYPE = -1;//虚拟的nlp样式,和服务器没关系
    public static final int LOCAL_CHAT_BACK_FEED_BACK = -2;//https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=107276486
    public static final int LOCAL_CHAT_SUGGEST = -3;//https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=140108144



    /**
     * @see type
     * <0 本地模拟
     * 0、隐藏NLP协议
     * 1、协议跳转：value为跳转地址
     * 2、发送文本：value为消息文本
     * 3、交换电话：value为空
     * 4、交换微信：value为空
     * 5、请求简历：value为空
     * 6、发送简历：value为空
     * 7、预约面试：value为建议面试时间戳
     * 8、地理位置
     * 9、追聊
     * 10,回聊
     * 16,口头约面，发送下线面试地址
     * <p>
     * @see label : 展示按钮文本
     * @see value : 参见type说明
     */
    public int type;//协议类型

    public String label;//交换微信

    public String value;//zp协议

    public String extend;//协议扩展字段

    public List<ChatHightLightBean> chatHightLightBeans;



    @Override
    public String toString() {
        return "NLPSuggestBean{" +
                "type=" + type +
                ", label='" + label + '\'' +
                ", value='" + value + '\'' +
                ", extend='" + extend + '\'' +
                ", chatHightLightBeans=" + chatHightLightBeans +
                '}';
    }
}
