package com.hpbr.bosszhipin.data.db.entry;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.monch.lbase.orm.db.annotation.Table;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON> on 2018/5/9.
 */
@Table("QuitMember")
public class QuitMemberBean extends BaseEntityAuto {
    private static final long serialVersionUID = -1;
    public String name;
    public String avatarUrl;
    public long userId;
    public long groupId;
    public String securityId;


    public static List<QuitMemberBean> fromGroupMemberBeanList(List<GroupMemberBean> groupMemberBeanList) {
        List<QuitMemberBean> resultList = new ArrayList<>();
        if (groupMemberBeanList == null) {
            return resultList;
        }

        for (GroupMemberBean bean : groupMemberBeanList) {
            QuitMemberBean quitMemberBean = new QuitMemberBean();
            quitMemberBean.avatarUrl = bean.avatarUrl;
            quitMemberBean.name = bean.name;
            quitMemberBean.userId = bean.userId;
            quitMemberBean.groupId = bean.groupId;
            quitMemberBean.securityId = bean.securityId;
            resultList.add(quitMemberBean);
        }
        return resultList;
    }

    public GroupMemberBean toGroupMemberBean() {
        GroupMemberBean groupMemberBean = new GroupMemberBean();
        groupMemberBean.avatarUrl = this.avatarUrl;
        groupMemberBean.name = this.name;
        groupMemberBean.userId = this.userId;
        groupMemberBean.groupId = this.groupId;
        groupMemberBean.securityId = this.securityId;
        groupMemberBean.quit = true;
        return groupMemberBean;
    }
}
