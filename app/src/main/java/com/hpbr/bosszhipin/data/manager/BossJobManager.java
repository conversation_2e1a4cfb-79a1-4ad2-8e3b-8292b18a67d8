package com.hpbr.bosszhipin.data.manager;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobDefSelectionBean;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2019-10-11
 * Boss职位管理
 */
public class BossJobManager {

    private static final SpImpl sp;
    private static final String CURRENT_SELECT_JOB_KEY = Constants.PREFIX + ".CURRENT_SELECT_JOB_KEY";
    private static final String DEF_SELECTION_PARAMS = Constants.PREFIX + ".DEF_SELECTION_PARAMS";

    static {
        Context context = App.get().getContext();
        sp = SpFactory.create(context, Constants.PREFIX + ".current_select_expect." + UserManager.getUID());
    }

    /**
     * 保存当前在f1中选中的期望职位
     */
    public static void saveCurrSelectJob(JobBean currJob) {
        if (currJob == null) return;
        try {
            String string = GsonUtils.getGson().toJson(currJob);
            if (!TextUtils.isEmpty(string)) {
                sp.putString(CURRENT_SELECT_JOB_KEY, string);
            }
        } catch (Exception e) {
            L.d("期望数据解析失败");
        }
    }

    public static JobBean getCurrSelectJob() {
        String string = sp.getString(CURRENT_SELECT_JOB_KEY);
        try {
            if (!TextUtils.isEmpty(string)) {
                return GsonUtils.getGson().fromJson(string, JobBean.class);
            }
        } catch (Exception e) {
            L.d("期望数据解析失败");
        }
        return null;
    }

    public static long getCurrSelectJobId() {
        long jobId = 0;
        JobBean job = getCurrSelectJob();
        if (job != null) {
            jobId = job.id;
        }
        return jobId;
    }

    public static void saveJobSelectionParams(JobBean job, int sortType) {
        if (job == null) {
            return;
        }
        if (!BossJobListProvider.getInstance().isAvailableJobBean(job.id)) {
            return;
        }

        String oldJson = SpManager.get().user().getString(DEF_SELECTION_PARAMS, "");
        try {
            List<JobDefSelectionBean> list = null;
            if (!TextUtils.isEmpty(oldJson)) {
                list = GsonUtils.getGson().fromJson(oldJson, new TypeToken<List<JobDefSelectionBean>>() {
                }.getType());
            }
            if (list == null) {
                list = new ArrayList<>();
            }
            boolean isEditItem = false;
            for (JobDefSelectionBean item : list) {
                if (item == null) continue;
                if (job.id == item.jobId) {
                    if (job.isCompositeList && item.isCompositeJob) {
                        isEditItem = true;
                        item.sortType = sortType;
                        break;
                    }
                    if (!job.isCompositeList && !item.isCompositeJob) {
                        isEditItem = true;
                        item.sortType = sortType;
                        break;
                    }
                }
            }
            if (!isEditItem) {
                list.add(JobDefSelectionBean.create(job, sortType));
            }
            String newJson = GsonUtils.getGson().toJson(list);
            SpManager.get().user().edit().putString(DEF_SELECTION_PARAMS, newJson).apply();
        } catch (Exception e) {
            L.d("数据解析失败");
        }
    }

    public static List<JobDefSelectionBean> getJobSelectionParams() {
        String json = SpManager.get().user().getString(DEF_SELECTION_PARAMS, "");
        TLog.print("filterSelection", "json result = %s", json);
        try {
            if (!TextUtils.isEmpty(json)) {
                return GsonUtils.getGson().fromJson(json, new TypeToken<List<JobDefSelectionBean>>() {
                }.getType());
            }
        } catch (Exception e) {
            L.d("数据解析失败");
        }
        return null;
    }

}
