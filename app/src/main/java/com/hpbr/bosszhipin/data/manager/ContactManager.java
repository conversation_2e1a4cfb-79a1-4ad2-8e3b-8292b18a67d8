package com.hpbr.bosszhipin.data.manager;


import android.content.Context;
import android.content.Intent;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.async.DbOpCallback;
import com.hpbr.bosszhipin.data.db.async.DbOpHandlerThread;
import com.hpbr.bosszhipin.data.db.async.DbOpParam;
import com.hpbr.bosszhipin.data.db.async.DbOpType;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.dao.DaoFactory;
import com.hpbr.bosszhipin.data.db.dao.IContactDao;
import com.hpbr.bosszhipin.data.db.dao.IContactExtraInfoDao;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.ContactExtraInfo;
import com.hpbr.bosszhipin.data.manager.contact.ContactCache;
import com.hpbr.bosszhipin.data.manager.contact.ContactData;
import com.hpbr.bosszhipin.data.manager.contact.DefaultRefreshStrategyV2;
import com.hpbr.bosszhipin.data.manager.contact.DefaultRefreshStrategyV3;
import com.hpbr.bosszhipin.data.manager.contact.IRefreshStrategy;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.announce.NoticeInfo;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatReaderBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.ChatReaderBeanManager;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.main.DeskTopNoneReadManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.BarGroupManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.push.HonorMsgBoxReceiver;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.bean.job.ServerBossBaseInfoBean;
import net.bosszhipin.api.bean.job.ServerBrandComInfoBean;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import message.handler.MSGSender;

/**
 * Created by monch on 15/5/29.
 */
public class ContactManager {
    public static final String TAG = "ContactManager";
    public static final String ACTION_DELETE_CONTACT = "com.hpbr.bosszhipin.ACTION_DELETE_CONTACT";
    public static final String ACTION_UPDATE_DANGEROUS_HINT_MESSAGE = "com.hpbr.bosszhipin.ACTION_UPDATE_DANGEROUS_HINT_MESSAGE";
    private final MutableLiveData<List<ContactBean>> contactsLiveData = new MutableLiveData<>();
    private final MutableLiveData<Long> contactLiveData = new MutableLiveData<>();
    private final MutableLiveData<ContactData> contactSummaryLiveData = new MutableLiveData<>();

    // 使用线程安全的CopyOnWriteArrayList
    private final CopyOnWriteArrayList<ContactChangeObserver> contactChangeObservers = new CopyOnWriteArrayList<>();
    /**
     * 通知
     */
    public static final long NOTIFY_FRIENDID = 895;
    /**
     * 互动
     */
    public static final long INTERACT_FRIENDID = 894;
    /**
     * 关注
     */
    public static final long FOLLOW_FRIENDID = 893;

    private final IContactDao contactDao;
    private final IContactExtraInfoDao contactExtraInfoDao;
    private final ContactCache contactCache;

    private ContactManager() {
        contactDao = DaoFactory.getContactDao();
        contactExtraInfoDao = DaoFactory.getContactExtraInfoDao();
        contactCache = ContactCache.getInstance();
    }

    private volatile static ContactManager instance;
    /**
     * 联系人列表更新策略
     **/
    private IRefreshStrategy refreshStrategy;


    //发送新招呼广播
    public static void sendNewGreetingBroadcast(Context context) {
        //发送显示 撤销广播
        Intent intent = new Intent();
        intent.setAction(Constants.ACTION_SHOW_GREETING);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }


    /**
     * 技术牛人开关变化
     *
     * @param context
     */
    public static void sendTechnologySwitchBroadcast(Context context) {
        Intent intent = new Intent(Constants.ACTION_TECHNOLOGY_SWITCH_BROADCAST);
        ReceiverUtils.sendBroadcast(context, intent);
    }


    public static final long THROTTLE_TIME = AndroidDataStarGray.getInstance().getContactRefreshThrottleTime();

    //数据防止刷新频繁
    volatile boolean refreshContacts = false;
    volatile long refreshContactsTime = 0;

    Runnable refreshContactsRunnable = () -> {
        // 灰度控制：只有开启联系人汇总数据优化才发送新的数据结构
        long startTime = SystemClock.elapsedRealtime();
        if (AndroidDataStarGray.getInstance().openContactDataOptimize()) {
            ContactData contactData = ContactData.create();
            contactSummaryLiveData.postValue(contactData);
            //其他地方使用 后期删除
            contactsLiveData.postValue(contactData.getF2ContactList());
            //刷新桌面气泡
            refreshDeskNoneRead(contactData.getContactNoneReadMsgCount());
            TLog.debug(TAG, "refreshContactsRunnable contactData: %s", contactData);
        } else {
            List<ContactBean> f2ContactList = F2ContactHelper.getInstance().getF2ContactList();
            contactsLiveData.postValue(f2ContactList);
            //刷新桌面气泡
            refreshDeskNoneRead(f2ContactList);
        }
        refreshContacts = false;
        refreshContactsTime = SystemClock.elapsedRealtime();
        long delayTime = refreshContactsTime - startTime;
        TLog.debug(TAG, "refreshContactsRunnable %b , time = %dms", refreshContacts, delayTime);
        reportTime(delayTime);
    };

    volatile int isReportStep; //限制上报率

    private void reportTime(long delayTime) {
        if (delayTime > 200) {
            int reportStep = (int) (delayTime / 100);
            if (isReportStep != reportStep) {
                ApmAnalyzer.create().action("action_contact", "refreshContactsTime").p2(String.valueOf(delayTime)).p3(String.valueOf(AndroidDataStarGray.getInstance().openContactDataOptimize())).debug().report();
            }
            isReportStep = reportStep;
        }
    }

    /**
     * 发送聊天中数量的广播，主要用于显示红点和红色数量标记
     */
    public void refreshContacts() {
        TLog.debug(TAG, "refreshContacts %b", refreshContacts);
        if (!refreshContacts) {//数据防止刷新频繁
            refreshContacts = true;
            //为了提高用户体验，如果上次刷新时间小于5秒，则延迟刷新
            if (SystemClock.elapsedRealtime() - refreshContactsTime > 5000) {
                AppThreadFactory.POOL.schedule(refreshContactsRunnable, 50, TimeUnit.MILLISECONDS);
            } else {
                AppThreadFactory.POOL.schedule(refreshContactsRunnable, THROTTLE_TIME, TimeUnit.MILLISECONDS);

            }
        }
    }


    @MainThread
    public void observeContacts(@NonNull LifecycleOwner owner, @NonNull Observer<List<ContactBean>> observer) {
        contactsLiveData.observe(owner, observer);
    }

    @MainThread
    public void removeObserve(@NonNull Observer<List<ContactBean>> observer) {
        contactsLiveData.removeObserver(observer);
    }

    /**
     * 观察联系人汇总数据（灰度控制）
     * @param owner 生命周期拥有者
     * @param observer 观察者
     */
    @MainThread
    public void observeContactData(@NonNull LifecycleOwner owner, @NonNull Observer<ContactData> observer) {
        if (AndroidDataStarGray.getInstance().openContactDataOptimize()) {
            contactSummaryLiveData.observe(owner, observer);
        }
    }

    /**
     * 移除联系人汇总数据观察者
     * @param observer 观察者
     */
    @MainThread
    public void removeContactDataObserver(@NonNull Observer<ContactData> observer) {
        contactSummaryLiveData.removeObserver(observer);
    }

    /**
     * 获取联系人汇总数据（同步方法，灰度控制）
     * @return 联系人汇总数据
     */
    public ContactData getContactData() {
        if (AndroidDataStarGray.getInstance().openContactDataOptimize()) {
            ContactData summaryData = contactSummaryLiveData.getValue();
            if (summaryData != null) {
                return summaryData;
            }
        }
        ContactData contactData = ContactData.create();
        contactSummaryLiveData.postValue(contactData);
        TLog.error(TAG, new RuntimeException("getContactData is null"), "getContactData is null");
        return contactData;
    }

    /**
     * 基于LifecycleOwner的观察者注册，可自动管理生命周期
     * @param lifecycleOwner 生命周期拥有者
     * @param observer 观察者
     */
    public void observeContact(@NonNull LifecycleOwner lifecycleOwner, @NonNull ContactChangeObserver observer) {
        if (AndroidDataStarGray.getInstance().openContactUpdateOptimize()) {
            // 添加观察者
            contactChangeObservers.add(observer);

            // 监听生命周期，自动移除观察者
            lifecycleOwner.getLifecycle().addObserver(new LifecycleEventObserver() {
                @Override
                public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
                    if (event == Lifecycle.Event.ON_DESTROY) {
                        removeContactObserve(observer);
                        source.getLifecycle().removeObserver(this);
                    }
                }
            });
        }
    }

    public void removeContactObserve(ContactChangeObserver observer) {
        if (observer == null || !AndroidDataStarGray.getInstance().openContactUpdateOptimize()) {
            return;
        }
        contactChangeObservers.remove(observer);
    }

    public void notifyContactChange(ContactBean contact) {
        if (contact == null || !AndroidDataStarGray.getInstance().openContactUpdateOptimize()) {
            return;
        }
        
        // 清理已回收的观察者并通知有效观察者
        for (ContactChangeObserver observer : contactChangeObservers) {
            try {
                observer.onContactChange(contact);
            } catch (Exception e) {
                TLog.error(TAG, e.getMessage());
            }

        }
    }

    @Deprecated
    @MainThread
    public void observeContact(@NonNull LifecycleOwner owner, @NonNull Observer<Long> observer) {
        contactLiveData.observe(owner, observer);
    }

    @Deprecated
    public void refreshContact(long friendUid) {
        contactLiveData.postValue(friendUid);
        TLog.debug(TAG, "refreshContact %d", friendUid);
    }



    public IRefreshStrategy obtainRefreshStrategy() {
        if (refreshStrategy == null) {
            if (AndroidDataStarGray.getInstance().contactRefreshV3()) {
                refreshStrategy = new DefaultRefreshStrategyV3();
            } else {
                refreshStrategy = new DefaultRefreshStrategyV2();
            }
        }
        return refreshStrategy;
    }

    public static ContactManager getInstance() {
        if (instance == null) {
            synchronized (ContactManager.class) {
                if (instance == null) {
                    instance = new ContactManager();
                }
            }
        }
        return instance;
    }

    /**
     * 切换身份，重新加载缓存
     ***/
    public void switchRole() {
        refreshStrategy = null;
        contactCache.switchRole();
    }

    /**
     * 切清空缓存
     * 注销账号时调用
     ***/
    public void clean() {
        contactCache.clean();
    }

    /**
     * 加载缓存
     * 应用程序加载/登录成功以后加载
     ***/
    public void load() {
        contactCache.load();
    }


    /**
     * //     * 根据好友ID获取联系人
     * //     *
     * //     * @param friendId
     * //     * @return
     * //DEFAULT_FRIEND_SOURCE
     */
    public synchronized ContactBean queryContactByFriendId(long friendId, int myRole, int friendSource) {
        return contactCache.queryContactByFriendId(friendId, myRole, friendSource);
    }


    public List<ContactBean> getAllContact() {
        return contactCache.getContactList();
    }

    /**
     * 获得所有联系人
     *
     * @return
     */
    public List<ContactBean> getAllContactList() {
//        List<ContactBean> list = contactCache.getContactList();
//        return getFilteredContactList(list);
        return F2ContactHelper.getInstance().getSingleContact(true);
    }


    /**
     * 根据id数组，获取一个联系人列表数据
     *
     * @param myRole       我当前的身份
     * @param ids          需要获取联系人的id
     * @param friendSource
     * @return 已排好序的联系人列表
     */
    public List<ContactBean> getSearchContactList(int myRole, long[] ids, int friendSource) {
        if (ids == null) return new ArrayList<>(0);
        List<ContactBean> list = new ArrayList<>(ids.length);
        for (long id : ids) {
            ContactBean bean = contactCache.queryContactByFriendId(id, myRole, friendSource);
            if (bean != null) {
                list.add(bean);
            }
        }
        return list;
    }

    /**
     * 获取列表未读条目
     *
     * @return
     */
    public int getNoneReaderCount() {
        return F2ContactHelper.getInstance().getSingleContactNoneReadMsgCount();
    }


    // 获取多个联系人未读数量
    public int getContactsNoneReadCount(List<Long> friendIds) {
        int noneReadCount = 0;
        for (long friendId : friendIds) {
            noneReadCount += getContactNoneReadCount(friendId);
        }
        return noneReadCount;
    }


    /**
     * 将指定联系人的消息置为已读
     *
     * @param friendIds    好友来源Boss或者店长
     *                     {@link ContactBean#FROM_BOSS}
     *                     {@link ContactBean#FROM_DIAN_ZHANG}
     * @param friendSource
     */
    public void clearContactsNoneRead(Long friendIds, int friendSource) {
        //清除联系人数据
        ContactBean contactBean = ContactManager.getInstance()
                .queryContactByFriendId(friendIds, UserManager.getUserRole().get(), friendSource);
        if (contactBean == null) return;
        contactBean.noneReadCount = 0;
        initContactData(contactBean);
        //通知MainTab刷新
        ContactManager.getInstance().refreshContacts();
    }

    /**
     * 将指定联系人的消息置为已读
     *
     * @param friendIds    多个好友来源
     *                     {@link ContactBean#FROM_BOSS}
     *                     {@link ContactBean#FROM_DIAN_ZHANG}
     * @param friendSource
     */
    public void clearContactsNoneRead(List<Long> friendIds, int friendSource) {
        if (friendIds == null) return;
        for (Long friendId : friendIds) {
            clearContactsNoneRead(friendId, friendSource);
        }
    }

    public void clearContactsNoneRead2(List<Long> friendIds, int friendSource) {
        if (friendIds == null) return;
        for (Long friendId : friendIds) {
            //清除联系人数据
            ContactBean contactBean = ContactManager.getInstance()
                    .queryContactByFriendId(friendId, UserManager.getUserRole().get(), friendSource);
            if (contactBean == null) return;
            contactBean.noneReadCount = 0;
            initContactData(contactBean);
        }
        //通知MainTab刷新
        ContactManager.getInstance().refreshContacts();
    }


    //获取联系人未读数量
    public int getContactNoneReadCount(long friendId) {
        ContactBean contactBean = ContactManager.getInstance()
                .queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
        if (contactBean != null) {
            return contactBean.noneReadCount;
        }
        return 0;
    }


    //获取通知未读数量之前的公告
    public int getNoticeNoneReadCount() {
        int result = 0;
        List<ContactBean> noticeList = getNoticeNoneReadBean();
        int count = LList.getCount(noticeList);
        for (int i = 0; i < count; i++) {
            ContactBean item = LList.getElement(noticeList, i);
            if (item == null) continue;
            result += item.noneReadCount;
        }
        result += HonorMsgBoxReceiver.getInstance().getUnread();
        return result;
    }


    public List<ContactBean> getNoticeNoneReadBean() {
        List<ContactBean> contactList = new ArrayList<>();

        List<NoticeInfo> noticeList = UserManager.isBossRole() ? NoticeInfo.getBossNoticeList() : NoticeInfo.getGeekNoticeList();
        for (NoticeInfo noticeInfo : noticeList) {
            ContactBean item = ContactManager.getInstance()
                    .queryContactByFriendId(noticeInfo.id, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);

            if (item == null) continue;
            //同事推荐SP保存
            int noneReadCount = item.id == NoticeInfo.MATE_NOTICE.id ? RelationContactManager.getRecommendGeekNewCount() : item.noneReadCount;

            if (noneReadCount > 0) {
                contactList.add(item);
            }
        }

        return contactList;
    }


    //清空通知未读消息数量
    public void clearNoticeNoneRead(List<ContactBean> noticeList) {
        int count = LList.getCount(noticeList);
        for (int i = 0; i < count; i++) {
            ContactBean item = LList.getElement(noticeList, i);
            if (item == null) continue;
            item.noneReadCount = 0;
            initContactData(item);
        }
    }

    public void notifyServiceCleanNoneRead(final Long friendIds,int friendSource) {
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                ChatReaderBean bean = ChatReaderBeanManager.
                        getInstance().create(friendIds);
                MSGSender.sendMessageReaderMessage(bean, friendSource);
            }
        });
    }

    public void notifyServiceCleanNoneRead(final List<Long> friendIds,int friendSource) {
        if (friendIds == null || friendIds.size() <= 0) return;
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                for (Long id : friendIds) {
                    ChatReaderBean bean = ChatReaderBeanManager.
                            getInstance().create(id);
                    if (bean == null) continue;
                    MSGSender.sendMessageReaderMessage(bean, friendSource);
                }
            }
        });
    }

    //通知服务器清空已读消息
    public void notifyServiceCleanNoneRead(final List<Long> friendIds) {
        if (friendIds == null || friendIds.size() <= 0) return;
        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {
                for (Long id : friendIds) {
                    ChatReaderBean bean = ChatReaderBeanManager.
                            getInstance().create(id);
                    if (bean == null) continue;
                    MSGSender.sendMessageReaderMessage(bean, ContactBean.FROM_BOSS);
                }
            }
        });
    }

    //通知服务器清空已读消息
    public void notifyServiceCleanNoneRead(long friendId) {
        AppThreadFactory.POOL.execute(() -> {
            ChatReaderBean bean = ChatReaderBeanManager.
                    getInstance().create(friendId);
            if (bean == null) return;
            MSGSender.sendMessageReaderMessage(bean, ContactBean.FROM_BOSS);
        });
    }


    /***
     *
     *
     * =========================增/删/改============================
     *
     * ***/

    /**
     * 删除联系人
     *
     * @param bean
     */
    public void deleteContact(ContactBean bean) {
        contactCache.deleteContact(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_DELETE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
        SingleRouter.deleteContact(bean); //删除部分换成

    }

    public void repairLastMid(){
        IContactDao contactDao = DaoFactory.getContactDao();
        for (ContactBean contactBean : contactCache.getContactList()) {
            contactBean.lastMsgId = 0;
        }
        if (contactDao!=null) {
            contactDao.repairLastMid();
        }
    }


    private static final DbOpParam<String, Message> MONITOR_END_INSERT_OR_UPDATE_PARAM = new DbOpParam<>(ContactDoctorFactory.MONITOR_NEW_CONTACT_MESSAGE, new DbOpCallback.SimpleDbOpCallback<Message>() {
        @Override
        public void success(Message message) {
            message.obj = ContactDoctorFactory.MONITOR_NEW_CONTACT_MESSAGE;
            ContactDoctorFactory.recordContactMessageHandler(message);
        }
    });

    private final DbOpCallback.SimpleDbOpCallback<Void> MONITOR_END_INSERT_OR_UPDATE_CALLBACK =  new DbOpCallback.SimpleDbOpCallback<Void>() {
        @Override
        public void success(Void VOID) {
            Message obtainMessage = DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_MONITOR_MESSAGE_HANDLE, 1, ContactDoctorFactory.MONITOR_NEW_CONTACT_MESSAGE);
            ContactDoctorFactory.recordContactMessageHandler(obtainMessage);
        }
    };

    public void insertOrUpdateAllWithMsg(@NonNull ContactBean contact, int role, ChatBean bean) {
        boolean monitorNewContact = contact.id <= 0; //只监控新增联系人是否丢失
        boolean fastNewAddFriendWithMsg = AndroidDataStarGray.getInstance().isFastNewAddFriendWithMsg();
        if (monitorNewContact) {
            TLog.debug(TAG, "insertOrUpdateAllWithMsg addFriendId ID = %d friendId = %d msgId = %d  fastNewAddFriendWithMsg= %s", contact.id, contact.friendId, bean.msgId, fastNewAddFriendWithMsg);
        }
        if (monitorNewContact) { // 开始
            ContactDoctorFactory.recordContactMessageHandler(DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_MONITOR_MESSAGE_HANDLE, 0, ContactDoctorFactory.MONITOR_NEW_CONTACT_MESSAGE));
        }
        if (monitorNewContact && fastNewAddFriendWithMsg) {
            if (contact.friendId <= 0) {
                return;
            }
            contactCache.insertOrUpdateAllField(contact, role);
            DbOpHandlerThread.getInstance().sendMessageAtFrontOfQueue(DbOpType.OP_CONTACT_INSERT_OR_UPDATE, new DbOpParam<>(contact, MONITOR_END_INSERT_OR_UPDATE_CALLBACK));
        } else {
            insertOrUpdateAllField(contact, role);// 执行
            if (monitorNewContact) { // 结束
                DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_MONITOR_MESSAGE_HANDLE, 1, MONITOR_END_INSERT_OR_UPDATE_PARAM).sendToTarget();
            }
        }
    }

    /**
     * 添加/更新数据（保留id）
     *
     * @param bean
     * @return
     */
    public long insertOrUpdateAllField(ContactBean bean, int myRole) {
        if (bean.friendId <= 0) {
            return 0;
        }
        long result = contactCache.insertOrUpdateAllField(bean, myRole);

        if (AndroidDataStarGray.getInstance().contactAllField()) {
            // 使用V2版本，支持操作监控和批量优化
            // 尝试提交到队列管理器，如果不支持则走原来逻辑
            boolean submitted = ContactQueueManager.getInstance().submitInsertOrUpdateOperation(bean);

            if (!submitted) {
                // 队列管理器不支持，走原来的逻辑
                DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_INSERT_OR_UPDATE_V2, bean).sendToTarget();
            }
        } else {
            DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_INSERT_OR_UPDATE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
        }
        return result;
    }


    public void insertOrUpdateBaseInfo(ContactBean bean, int myRole) {
        if (bean.friendId <= 0) {
            return;
        }
        bean.myRole = myRole;
        contactCache.insertOrUpdateServerField(bean, myRole);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_BASE_INFO, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    public void syncInsertOrUpdateBaseInfo(ContactBean bean, int myRole) {
        if (bean.friendId <= 0) {
            return;
        }
        bean.myRole = myRole;
        contactCache.insertOrUpdateServerField(bean, myRole);
        contactDao.upsertContactServerField(bean, myRole);
    }

    /**
     * 更新联系人列表，保留bean中的本地数据
     *
     * @param bean
     */
    public void insertOrUpdateServerField(ContactBean bean, int myRole) {
        if (bean.friendId <= 0) {
            return;
        }
        bean.myRole = myRole;
        contactCache.insertOrUpdateServerField(bean, myRole);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    /**
     * 更新电话直播的状态
     *
     * @param bean
     */
    public void updateDirectCallStatus(ContactBean bean) {
        contactCache.updateDirectCallStatus(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_DIRECT_CALL_STATUS, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    public void updateWeChat(ContactBean bean) {
        contactCache.updateWeChat(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_WECHAT, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     * 更新数据的固定列：最后聊天记录
     *
     * @param bean
     */
    public void updateLastText(ContactBean bean) {
        contactCache.updateLastText(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_LAST_TEXT, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    public void updatePhone(ContactBean bean) {
        contactCache.updatePhone(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_PHONE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    public void updateWarningTip(ContactBean bean) {
        contactCache.updateWarningTip(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_WARNING_TIPS, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    public void updateLabels(ContactBean bean) {
        contactCache.updateLabel(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_LABEL, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    public void updateNote(ContactBean bean) {
        contactCache.updateNote(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_NOTE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    public void updateUnreadAndCountChatStatus(@NonNull List<ContactBean> contactBeanList) {
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_UPDATE_UNREAD_COUNT_CHAT_STATUS, new DbOpParam<List<ContactBean>, Void>(contactBeanList, null)).sendToTarget();
    }

    @WorkerThread
    public void updateFriendStage(@Nullable ContactBean contactBean) {
        if (contactBean == null) return;
        //更新缓存
        contactCache.updateFriendStage(contactBean);
        //保存到db里面
        DaoFactory.getContactDao().updateFriendStage(contactBean);
    }



    public void updateWxRemind(ContactBean bean) {
        contactCache.updateWxRemind(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_WX_REMIND, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    public void updateSecurityId(ContactBean bean) {
        contactCache.updateSecurityId(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_SECURITY_ID, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    public void updateJumpToChatField(ContactBean bean) {
        contactCache.updateJumpToChatField(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_HAS_JUMP_CHAT, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     * 更新数据的isfiltered
     *
     * @param bean
     */
    public void updateIsFiltered(ContactBean bean) {
        contactCache.updateIsFiltered(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_IS_FILTERD, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateLastText(bean);
    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    public void updateExchangePhoneTime(ContactBean bean) {
        contactCache.updateExchangePhoneTime(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_EXCHANGE_PHONE_TIME, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateExchangePhoneTime(bean);
    }

    /**
     * 更新交换电话时间
     *
     * @param bean
     */
    public void updateExchangeWechatTime(ContactBean bean) {
        contactCache.updateExchangeWechatTime(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_EXCHANGE_WECHAT_TIME, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateExchangeWechatTime(bean);
    }

    /**
     * 更新附件简历时间
     *
     * @param bean
     */
    public void updateExchangeAnnexResumeTime(ContactBean bean) {
        contactCache.updateExchangeAnnexResumeTime(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_EXCHANGE_ANNEX_RESUME_TIME, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateExchangeAnnexResumeTime(bean);
    }

    public void updateUpdateTime(ContactBean bean) {
        contactCache.updateUpdateTime(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_UPDATE_TIME, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    public void updateVideoInterview(ContactBean bean) {
        contactCache.updateVideoInterview(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_VIDEO_INTERVIEW, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     * 更新邀请页面时间
     *
     * @param bean
     */
    public void updateInviteInterviewTime(ContactBean bean) {
        contactCache.updateInviteInterviewTime(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_EXCHANGE_INTERVIEW_TIME, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateInviteInterviewTime(bean);
    }

    // 更新面试状态
    public void updateInterviewStatus(ContactBean bean) {
        contactCache.updateInterviewStatus(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_INTERVIEW_STATUS, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    //更新草稿
    public void updateDraft(ContactBean bean) {
        contactCache.updateDraft(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_DRAFT, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    //更新草稿
    public void updateInterviewSchedule(ContactBean bean) {
        contactCache.updateInterviewSchedule(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_INTERVIEW_SCHEDULE, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     * 更新「是否置顶」
     *
     * @param bean
     */
    public void updateTopFriend(@NonNull ContactBean bean) {
        contactCache.updateIsTop(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_IS_TOP, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     * 更新「是否开启免打扰」
     *
     * @param bean
     */
    public void updateNoDisturb(@NonNull ContactBean bean) {
        contactCache.updateNoDisturb(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_IS_OPEN_NO_DISTURB, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }


    /**
     * 更新联系人最后消息的发送状态
     *
     * @param bean 联系人实例
     */
    public void updateLastTextStatus(ContactBean bean, long lastChatClientMessageId) {
        contactCache.updateLastTextStatus(bean, lastChatClientMessageId);
        bean.lastChatClientMessageId = lastChatClientMessageId;
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_LAST_TEXT_STATUS, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
//        contactDao.updateLastTextStatus(bean,lastChatClientMessageId);
    }


    /**
     * 更新联系人最后消息的发送状态
     */
    public void updateNameAndHeadUrl(long friendId, String name, String headUrl, int friendSource) {
        ContactBean contactBean = contactCache.updateNameAndHeadUrl(friendId, name, headUrl, friendSource);
        if (contactBean != null) {
            DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_NAME_AND_HEAD_URL, new DbOpParam<ContactBean, Void>(contactBean, null)).sendToTarget();
        }
    }

    /**
     * 更新聊天未读数据
     *
     * @param bean
     */
    public void initContactData(ContactBean bean) {
        contactCache.initContactData(bean);
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_UNREAD_COUNT, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
    }

    /**
     *
     * @param step 1-start 2-end
     */
    public void monitorContactSaveData(int step) {
        if (step == 1) {
            DbOpHandlerThread.getInstance().sendMessageAtFrontOfQueue(DbOpType.OP_TRANSACTION_MONITOR, step, null);
        } else {
            DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_TRANSACTION_MONITOR, step, null).sendToTarget();
        }
    }

    /**
     * 更新BaseInfoList 数据
     *
     * @param list
     * @param myRole
     */
    public void updateBaseContactInfo(List<ContactBean> list, int myRole) {
        long timeTag = System.currentTimeMillis();
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_TRANSACTION_START,LList.getCount(list), timeTag).sendToTarget();
        for (ContactBean bean : list) {
            insertOrUpdateBaseInfo(bean, myRole);
        }
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_TRANSACTION_END,LList.getCount(list), timeTag).sendToTarget();
    }

    public void updateContactList(List<ContactBean> list, int myRole) {
        for (ContactBean bean : list) {
            insertOrUpdateServerField(bean, myRole);
        }
    }

    public void updateFriendCompanies(List<ContactBean> list, int myRole) {
        for (ContactBean contactBean : list) {
            contactCache.updateFriendCompanies(contactBean);
        }
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_FRIEND_COMPANIES, new DbOpParam<List<ContactBean>, Void>(list, null)).sendToTarget();
    }

    /**
     * 更新联系人数据
     *
     * @param user
     * @param jobId
     * @param jobIntentId
     */
    public void updateContactInfo(ServerBossBaseInfoBean user, ServerBrandComInfoBean brand, long jobId, long jobIntentId) {
        if (user == null || user.bossId <= 0) return;
        ContactBean contact = queryContactByFriendId(user.bossId, UserManager.getUserRole().get(), user.bossSource);
        if (contact == null) return;
        if (!LText.empty(user.name)) {
            contact.friendName = user.name;
        }
        if (!LText.empty(user.tinyAvatar)) {
            contact.friendDefaultAvatar = user.tinyAvatar;
        }
        if (contact.jobId <= 0) {
            contact.jobId = jobId;
        }
        if (contact.jobIntentId <= 0) {
            contact.jobIntentId = jobIntentId;
        }
        //这个位置会覆盖 baseInfo/fullInfo/addFriend接口company字段，会导致不显示"安心保"
        //if (brand != null && !LText.empty(brand.brandName)) {
        //  contact.bossCompanyName = brand.brandName;
        //  }
        contact.certification = user.certStatus;
        insertOrUpdateAllField(contact, UserManager.getUserRole().get());
    }


    /***
     *
     * =======================下列方法禁止业务层调用=============================
     *
     * ***/

    /**
     * 获取所有联系人数据
     *
     * @return
     */
    public List<ContactBean> getAllContactList(int role) {
        return contactDao.getAllContactList(role);
    }


    /**
     * 获取聊天窗口提醒消息
     **/
    public String getDangerousHintMessage(long friendId) {
        String message = "";
        ContactExtraInfo contactExtraInfo = contactExtraInfoDao.queryById(friendId);
        if (contactExtraInfo != null) {
            message = contactExtraInfo.dangerousHintMessage;
        }
        return message;
    }

    /**
     * 更新聊天窗口提醒消息
     **/
    public void updateDangerousHintMessage(long friendId, String message) {
        if (!TextUtils.isEmpty(message)) {
            ContactExtraInfo bean = new ContactExtraInfo();
            bean.dangerousHintMessage = message;
            bean.friendId = friendId;
            bean.myId = UserManager.getUID();
            bean.myRole = UserManager.getUserRole().get();
            contactExtraInfoDao.insertOrUpdate(bean);
            Intent intent = new Intent();
            intent.addCategory(ContactManager.ACTION_UPDATE_DANGEROUS_HINT_MESSAGE);
            ReceiverUtils.sendBroadcastSystem(App.getAppContext(), intent);
        }

    }

    /**
     * 删除聊天窗口提醒消息
     **/
    public void deleteDangerousHintMessage(long friendId) {
        contactExtraInfoDao.delete(friendId);
    }

    /**
     * @param friendIds
     * @param source    {@link ContactBean#FROM_BOSS,
     *                  ContactBean#FROM_DIAN_ZHANG}
     * @return
     */
    public int getListNoneReadCount(@NonNull List<Long> friendIds, int source) {
        int nonRead = 0;
        for (Long friendId : friendIds) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), source);
            if (contactBean == null) continue;
            nonRead += contactBean.noneReadCount;
        }
        return nonRead;
    }


    /**
     * @param friendIds
     * @return
     */
    @WorkerThread
    public List<Long> filterContactWithEachOther(List<Long> friendIds) {
        List<Long> result = new ArrayList<>();
        for (Long friendId : friendIds) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactBean.FROM_BOSS);
            if (contactBean != null && contactBean.isContactEachOther()) {
                result.add(friendId);
            }
        }
        return result;
    }

    public void updateSyncGeekInfo(List<ContactBean> geekInfoList) {
        if (geekInfoList == null || geekInfoList.isEmpty()) return;
        for (ContactBean bean : geekInfoList) {
            if (bean == null) continue;
            contactCache.updateSyncGeekInfo(bean);
            DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_CONTACT_UPDATE_SYNC_GEEK_INFO, new DbOpParam<ContactBean, Void>(bean, null)).sendToTarget();
        }
    }

    //---start---

    /**
     * 更新缓存信息
     */
    public void insertAndUpdateCache(List<ContactBean> insertList,List<ContactBean> updateList,int identity){
        for(ContactBean contactBean : insertList){
            contactCache.insertOrUpdateServerField(contactBean, identity);
        }
        for(ContactBean contactBean : updateList){
            contactCache.insertOrUpdateServerField(contactBean, identity);
        }
    }

    @WorkerThread
    public int updateContactAllField(List<ContactBean> contactBeans) {
        long uid = AccountHelper.getUid();
        int identity = AccountHelper.getIdentity();
        ContactBean firstElement = LList.getFirstElement(contactBeans);
        if (firstElement != null) {
            if (firstElement.myId == uid && firstElement.myRole == identity) {
                return contactDao.updateContacts(contactBeans);
            } else {
                TLog.error(TAG,"change identity updateContactAllField");
            }
        }
        return -1;
    }
    /**
     * 删除联系人
     * 包含数据库和缓存
     */
    public void batchDeleteContacts(List<ContactBean> deleteList,int role){
        if(deleteList.isEmpty()) return;
        //批量删除数据库联系人
        DbOpHandlerThread.getInstance().obtainMessage(DbOpType.OP_BATCH_CONTACT_DELETE_CONTACTS, role,new DbOpParam<List<ContactBean>, Void>(deleteList, null)).sendToTarget();
        //删除缓存中联系人
        for(ContactBean contactBean : deleteList){
            contactCache.deleteContact(contactBean);
            SingleRouter.deleteContact(contactBean);
        }
        //删除完缓存刷新下联系人
        if (LList.getCount(deleteList) > 0) {
            ContactManager.getInstance().refreshContacts();
        }
    }

    //---end---


    private static void refreshDeskNoneRead(List<ContactBean> f2ContactList) {
        int count = 0;
        if (SecurityFrameworkManager.getInstance().isNeedShowShortcutUnreadCount()) {
            int f2NoneRead = F2ContactHelper.getInstance().getContactNoneReadMsgCount(f2ContactList);
            int f3NoneRead = BarGroupManager.getBubbleCount();
            count = f2NoneRead + f3NoneRead;
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_INFO).put("f2NoneRead", f2NoneRead).put("f3NoneRead", f3NoneRead).info();
        }
        DeskTopNoneReadManager.refreshDeskNoneRead(count);
    }

    private static void refreshDeskNoneRead(int f2NoneRead) {
        int count = 0;
        if (SecurityFrameworkManager.getInstance().isNeedShowShortcutUnreadCount()) {
            int f3NoneRead = BarGroupManager.getBubbleCount();
            count = f2NoneRead + f3NoneRead;
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_INFO).put("f2NoneRead", f2NoneRead).put("f3NoneRead", f3NoneRead).info();
        }
        DeskTopNoneReadManager.refreshDeskNoneRead(count);
    }

    public interface ContactChangeObserver {
        void onContactChange(ContactBean contact);
    }
}
