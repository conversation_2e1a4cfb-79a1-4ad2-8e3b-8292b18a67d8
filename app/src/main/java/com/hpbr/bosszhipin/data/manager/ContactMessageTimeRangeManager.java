package com.hpbr.bosszhipin.data.manager;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.base.App;
import com.monch.lbase.util.LText;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

/**
 * Created by guo<PERSON>
 * on 2020-03-28.
 * 接口获得联系人列表&联系人内容 保存的聊天时间【30天】
 */
public class ContactMessageTimeRangeManager {

    private static final String TAG = "ContactRecordTextManage";

    private static final String SP_KEY_CONTACT = "SP_KEY_CONTACT" + "_" + UserManager.getUID();

    private static final String SP_KEY_MESSAGE = "SP_KEY_MESSAGE" + "_" + UserManager.getUID();

    private static final String SP_KEY_CONTACT_MESSAGE = "SP_KEY_CONTACT_MESSAGE" + "_" + UserManager.getUID();


    public static ContactMessageTimeRangeManager instance = new ContactMessageTimeRangeManager();


    public static ContactMessageTimeRangeManager getInstance() {
        return instance;
    }

    private static SpImpl imp;
    private String timRange = "30天";

    private ContactMessageTimeRangeManager() {
        imp = SpFactory.create(App.getAppContext(), TAG);
    }


    //保存联系人列表和聊天30天内记录
    public void saveContactMessageTimeRangeRecord(@Nullable String timRange) {
        //保存联系人列表,获得30天内的联系人文案
        saveContactTimeRangeRecord(timRange);
        //保存消息记录时间，以下是30天的聊天记录
        saveMessageTimeRange(timRange);
        if (LText.empty(timRange)) {
            this.timRange = timRange;
        }

    }

    public void saveContactFoldText(@Nullable String foldText) {
        imp.putString(SP_KEY_CONTACT_MESSAGE, foldText);
    }

    //保存联系人列表,获得30天内的联系人文案
    private void saveContactTimeRangeRecord(@Nullable String timRange) {
        if (LText.empty(timRange)) {
            timRange = "30天";
        }
        imp.putString(SP_KEY_CONTACT, "以上是" + timRange + "内的联系人");
    }

    //保存消息记录时间，以下是30天的聊天记录
    private void saveMessageTimeRange(@Nullable String text) {
        if (LText.empty(text)) {
            text = "30天";
        }
        imp.putString(SP_KEY_MESSAGE, ("以下是" + text + "内的聊天记录"));
    }

    /**
     * 908.563  只修改B端
     * 获得F2联系人保存的时间
     *
     * @return
     */
    public String getContactTimeRangeRecord() {
        if (UserManager.isBossRole()) {
            return imp.getString(SP_KEY_CONTACT_MESSAGE);
        }
        return imp.getString(SP_KEY_CONTACT);
    }

    //获得消息保存的时间
    public String getMessageTimeRangeRecord() {
        /*兜底文案处理*/
        return imp.getString(SP_KEY_MESSAGE, "以下是30天内的聊天记录");
    }

    public String getMessageTimeRangeRecordString() {
        return this.timRange;
    }


}
