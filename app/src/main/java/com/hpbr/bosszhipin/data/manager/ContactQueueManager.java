package com.hpbr.bosszhipin.data.manager;

import com.hpbr.bosszhipin.data.db.async.DbOpHandlerThread;
import com.hpbr.bosszhipin.data.db.async.DbOpType;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.techwolf.lib.tlog.TLog;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 联系人队列管理器
 * 在ContactManager层面进行队列管理和背压检测
 * 避免在Handler中存储ContactBean对象
 */
public class ContactQueueManager {
    private static final String TAG = "ContactQueueManager";
    
    // 单例实例
    private static volatile ContactQueueManager instance;
    
    // 联系人更新操作队列
    private final ConcurrentLinkedQueue<ContactBean> insertOrUpdateQueue = new ConcurrentLinkedQueue<>();

    private ContactQueueManager() {
    }

    public static ContactQueueManager getInstance() {
        if (instance == null) {
            synchronized (ContactQueueManager.class) {
                if (instance == null) {
                    instance = new ContactQueueManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 提交联系人插入更新操作
     * @param contactBean 联系人数据
     * @return true表示已处理（单个或批量），false表示应该走原来的逻辑
     */
    public boolean submitInsertOrUpdateOperation(ContactBean contactBean) {
        // 检查开关是否启用
        if (AndroidDataStarGray.getInstance().getContactInsertUpdateBatchSize() <= 0) {
            TLog.debug(TAG, "Queue optimization disabled, using original logic");
            return false; // 总开关关闭，走原来逻辑
        }
        
        if (contactBean == null || contactBean.friendId <= 0) {
            return false;
        }
        
        // 将操作数据加入队列
        insertOrUpdateQueue.offer(contactBean);
        
        TLog.debug(TAG, "Contact submitted to queue: friendId=%d, queueSize=%d", 
            contactBean.friendId, insertOrUpdateQueue.size());

        DbOpHandlerThread.getInstance().obtainMessage(
                DbOpType.OP_BATCH_CONTACT_INSERT_OR_UPDATE_V2,
                null
        ).sendToTarget();
        
        return true; // 已加入队列处理
    }

    /**
     * 从队列中取出指定数量的数据
     */
    public Set<ContactBean> getAll() {
        int size = insertOrUpdateQueue.size();
        Set<ContactBean> result = new HashSet<>(size);
        for (int i = 0; i < size; i++) {
            ContactBean contactBean = insertOrUpdateQueue.poll();
            if (contactBean != null) {
                result.add(contactBean);
            }
        }
        return result;
    }
}
