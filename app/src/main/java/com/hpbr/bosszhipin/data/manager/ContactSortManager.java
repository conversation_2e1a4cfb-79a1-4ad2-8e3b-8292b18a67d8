package com.hpbr.bosszhipin.data.manager;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.QuickHandleBean;
import com.hpbr.bosszhipin.utils.SortUtils;
import com.techwolf.lib.tlog.TLog;

import java.util.Comparator;
import java.util.List;

/**
 * Created by guofeng on 2018/3/5.
 */

public final class ContactSortManager {

    private static final String TAG = "ContactSortManager";

    private ContactSortManager() {
    }

    private static  boolean hasPrint;

    /**
     * 目前只有f2聊天需要排序
     * 【置顶联系人】未读联系人在前面,已读联系人在后面
     * 【普通联系人】按照最近时间倒叙
     *
     * @param sourceList
     * @return
     */
    public static List<ContactBean> executeDefaultSort(List<ContactBean> sourceList) {
        if (sourceList != null) {
            /**赋值操作*/
            for (ContactBean item : sourceList) {
                if (item == null) continue;
                item.compareSort = item.lastChatTime > 0 ? item.lastChatTime : item.updateTime;
            }
            /**排序操作*/
            SortUtils.sort(sourceList, new Comparator<ContactBean>() {
                @Override
                public int compare(ContactBean o1, ContactBean o2) {

                    /**
                     * 背景：
                     * 解决排序异常：https://apm.weizhipin.com/main/action-get-info?addition=%255B%257B%2522router%2522%253A%2522home%2522%252C%2522name%2522%253A%2522%25E6%25A6%2582%25E8%25A7%2588%2522%257D%252C%257B%2522router%2522%253A%2522action%2522%252C%2522name%2522%253A%2522%25E7%25BB%259F%25E8%25AE%25A1%25E7%2582%25B9%2522%257D%252C%257B%2522router%2522%253A%2522action-get%2522%252C%2522name%2522%253A%2522action_sort%2522%252C%2522query%2522%253A%257B%2522appKey%2522%253A%2522n9mObpaxi2d2AFbV%2522%252C%2522action%2522%253A%2522action_sort%2522%252C%2522appName%2522%253A%2522Boss-Android%2522%252C%2522breadName%2522%253A%2522action_sort%2522%252C%2522occurFrequency%2522%253A6559477%252C%2522dayOccurFrequency%2522%253A129457%252C%2522influenceCount%2522%253A62679%252C%2522dayInfluenceCount%2522%253A5031%257D%257D%252C%257B%2522name%2522%253A%2522%25E5%2588%2597%25E8%25A1%25A8%2522%252C%2522router%2522%253A%2522%2522%257D%255D&appKey=n9mObpaxi2d2AFbV&action=action_sort&appName=Boss-Android&sysCode=101&queryData=%7B%22curAppVersion%22%3A%22%22,%22childType%22%3A%5B%5D,%22timeType%22%3A0,%22dateDuring%22%3A%5B1711987200000,1714579200000%5D,%22userId%22%3A%22%22,%22oldMode%22%3Afalse%7D&actionData=%5B%7B%22id%22%3A0.09796328813112942,%22field%22%3A%22actionp%22,%22judgeType%22%3A0,%22condition%22%3A1,%22keywords%22%3A%22%22%7D%5D
                     * java.util.TimSort.sort
                     *
                     * 原因：
                     * 在JDK7以后，实现Comparable接口后，要满足一下三个特性：
                     *  1.自反性：x，y 的比较结果和 y，x 的比较结果相反。
                     *  2.传递性：x>y,y>z,则 x>z。
                     *  3.对称性：x=y,则 x,z 比较结果和 y，z 比较结果相同。
                     *
                     * 参考链接：
                     *  https://www.cnblogs.com/-wyl/p/11196995.html
                     */
                    if (o1==null||o2 == null) {
                        if (o1==null&&o2 == null) {
                            if (!hasPrint) {
                                hasPrint=true;
                                TLog.info(TAG, "executeDefaultSort  compare  o1 ,o2 is null ");
                            }
                            return 0;
                        }
                        if (o1 == null) {
                            if (!hasPrint) {
                                hasPrint=true;
                                TLog.info(TAG, "executeDefaultSort  compare  o1  is null ");
                            }
                            return -1;
                        }else{
                            if (!hasPrint) {
                                hasPrint=true;
                                TLog.info(TAG, "executeDefaultSort  compare  o2  is null ");
                            }
                            return 1;
                        }
                    }

                    long l = o1.compareSort - o2.compareSort;
                    if (l > 0) {
                        return -1;
                    }

                    if (l < 0) {
                        return 1;
                    }

                    return 0;
                }
            });
        }
        return sourceList;
    }



    /**
     * f2聊天消息列表需要排序
     * 1.按照置顶优先,置顶和非置顶情况下:置顶优先在前，相同情况下在比时间
     * 2.按照沟通时间排序,
     *
     * @param sourceList
     * @return
     */
    public static List<ContactBean> executeSortByTopStatusAndTime(List<ContactBean> sourceList) {
        if (sourceList != null) {
            /**赋值操作*/
            for (ContactBean item : sourceList) {
                if (item == null) continue;
                item.compareSort = item.lastChatTime > 0 ? item.lastChatTime : item.updateTime;
            }
            /**排序操作*/
            SortUtils.sort(sourceList, (o1, o2) -> {

                if (o1 == null) {
                    return -1;
                }
                if (o2 == null) {
                    return 1;
                }

                //o1置顶,o2没有置顶,o1在前
                if (o1.isTop && !o2.isTop) {
                    return -1;
                }

                //o1没置顶,o2置顶,o2在前
                if (!o1.isTop && o2.isTop) {
                    return 1;
                }
                //o1,o2 都置顶了 看时间
                if (o1.isTop && o2.isTop) {
                    return Math.max(o2.lastChatTime, o2.updateTime) > Math.max(o1.lastChatTime, o1.updateTime) ? 1:-1;
                }

                //其余都按照时间排序
                long l = o1.compareSort - o2.compareSort;
                if (l > 0) {
                    return -1;
                }

                if (l < 0) {
                    return 1;
                }

                return 0;
            });
        }
        return sourceList;
    }

    /**
     * boss快速处理 排序简历
     *
     * @param sourceList
     * @return
     */
    public static List<QuickHandleBean> executeQuickHandleDefaultSort(List<QuickHandleBean> sourceList) {
        if (sourceList != null) {
            /**赋值操作*/
            for (QuickHandleBean item : sourceList) {
                if (item == null) continue;
                if (item.contactBean == null) continue;
                item.contactBean.compareSort = item.contactBean.lastChatTime > 0 ? item.contactBean.lastChatTime : item.contactBean.updateTime;
            }
            /**排序操作*/
            SortUtils.sort(sourceList, new Comparator<QuickHandleBean>() {
                @Override
                public int compare(QuickHandleBean o1, QuickHandleBean o2) {
                    long l = o1.contactBean.compareSort - o2.contactBean.compareSort;
                    if (l > 0) {
                        return -1;
                    }
                    if (l < 0) {
                        return 1;
                    }
                    return 0;
                }
            });
        }
        return sourceList;
    }

    /**
     * 群聊排序
     *
     * @param sourceList
     * @return
     */
    public static List<GroupInfoBean> executeGroupChatSort(List<GroupInfoBean> sourceList) {
        if (sourceList != null) {
            /**排序操作*/
            SortUtils.sort(sourceList, new Comparator<GroupInfoBean>() {
                @Override
                public int compare(GroupInfoBean o1, GroupInfoBean o2) {
                    long l = o1.lastChatTime - o2.lastChatTime;
                    if (l > 0) {
                        return -1;
                    }
                    if (l < 0) {
                        return 1;
                    }
                    return 0;
                }
            });
        }
        return sourceList;
    }


}
