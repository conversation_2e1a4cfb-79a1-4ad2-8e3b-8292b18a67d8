package com.hpbr.bosszhipin.data.manager;


import com.bszp.kernel.utils.AccountRunnable;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.contact.DefaultRefreshStrategyLite;
import com.hpbr.bosszhipin.data.manager.contact.IRefreshStrategy;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.main.fragment.manager.InteractedChatManager;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.BossGetTagCountRequest;
import net.bosszhipin.api.BossMateShareGeekCountRequest;
import net.bosszhipin.api.ContactBaseInfoListResponse;
import net.bosszhipin.api.ContactBaseInfoRequest;
import net.bosszhipin.api.GeekGetTagCountRequest;
import net.bosszhipin.api.GetMateShareGeekResponse;
import net.bosszhipin.api.GetTagCountResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

/**
 * 联系人同步类
 */
public class ContactSyncManager {

    public static final String TAG = "ContactSyncManager";
    private static final ContactSyncManager mInstance = new ContactSyncManager();


    private ContactSyncManager() {
    }

    public static ContactSyncManager getInstance() {
        return mInstance;
    }

    public void syncAll() {
        syncNotifyData();
        syncContactList();
        syncRecommendGeekCount();
    }


    //同步店长和Boss联系人
    public void syncContactBaseInfo(long friendId, int friendSource) {
        if (friendSource == ContactBean.FROM_BOSS) {//来源boss
            syncBaseInfo(Arrays.asList(friendId), null);
        } else {//来源店长
            syncBaseInfo(null, Arrays.asList(friendId));
        }
    }

    //同步店长和Boss联系人
    public void syncContactBaseInfo(List<Long> bossIds, List<Long> dzIds) {
        syncBaseInfo(bossIds, dzIds);
    }

    // 刷新谁看了我的接口
    private void syncNotifyData() {
        if (!UserManager.isCurrentLoginStatus()) {
            return;
        }
        if (UserManager.getUserRole() == ROLE.GEEK) {
            GeekGetTagCountRequest request = new GeekGetTagCountRequest(new ApiRequestCallback<GetTagCountResponse>() {
                @Override
                public void onSuccess(ApiData<GetTagCountResponse> data) {
                    /**感兴趣 */
                    InteractedChatManager.getInstance().updateInterestedMe(data.resp.interested, true);
                    /**看过我 */
                    InteractedChatManager.getInstance().updateViewedMe(data.resp.viewed, true);
                    /**新职位 */
                    InteractedChatManager.getInstance().updateNewer(data.resp.newer, true);
                    /**本地职位*/
                    InteractedChatManager.getInstance().updateLocalJob(data.resp.local, true);
                }

                @Override
                public void onComplete() {
                    //无论有无网络,刷新f2tab和f2列表红点。【无网络情况,f2列表有红点(onDisplay刷新),f2底部没有红点(broadcast刷新)】
                    ContactManager.getInstance().refreshContacts();
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.a = "1";
            HttpExecutor.execute(request);
        } else {
            BossGetTagCountRequest request = new BossGetTagCountRequest(new ApiRequestCallback<GetTagCountResponse>() {
                @Override
                public void onSuccess(ApiData<GetTagCountResponse> data) {
                    /**感兴趣 */
                    InteractedChatManager.getInstance().updateInterestedMe(data.resp.interested, true);
                    /**看过我 */
                    InteractedChatManager.getInstance().updateViewedMe(data.resp.viewed, true);
                    /**新职位 */
                    InteractedChatManager.getInstance().updateNewer(data.resp.newer, true);
                    /** 收藏中待沟通的牛人, 717新增*/
                    InteractedChatManager.getInstance().updateBossFavouriteGeek(data.resp.interestedAndNotContact, true);
                    /** 精选牛人, 717新增*/
                    InteractedChatManager.getInstance().updateBossGetRefinedGeek(data.resp.refinedGeek, true);
                }


                @Override
                public void onComplete() {
                    //无论有无网络,刷新f2tab和f2列表红点。【无网络情况,f2列表有红点(onDisplay刷新),f2底部没有红点(broadcast刷新)】
                    ContactManager.getInstance().refreshContacts();
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.a = "1";
            HttpExecutor.execute(request);
        }
    }

    // 刷新联系人列表接口
    public void syncContactList() {
        if (!UserManager.isCurrentLoginStatus()) return;
        IRefreshStrategy refreshStrategy = ContactManager.getInstance().obtainRefreshStrategy();
        if (refreshStrategy != null && refreshStrategy.isNeedRefresh()) {
            refreshStrategy.doRefresh();
        } else if (refreshStrategy != null && refreshStrategy.isRefreshing()) { // 正在请求中、执行中
            TLog.info(TAG, "ignore syncContactList  is isRefreshing");
        } else if (AndroidDataStarGray.getInstance().defaultRefreshLite()) {
            final DefaultRefreshStrategyLite defaultRefreshStrategyLite = DefaultRefreshStrategyLite.getInstance();
            if (defaultRefreshStrategyLite.isNeedRefresh()) {
                Utils.runOnUiThreadDelayed(new AccountRunnable() {
                    @Override
                    public void safeRun() {
                        if (defaultRefreshStrategyLite.isNeedRefresh()) {
                            defaultRefreshStrategyLite.doRefresh();
                        }
                    }
                }, 8000);
            }
        }
    }

    // 初始化推荐牛人数量
    private void syncRecommendGeekCount() {
        if (!UserManager.isCurrentLoginStatus() || UserManager.getUserRole() == ROLE.GEEK) return;
        BossMateShareGeekCountRequest request = new BossMateShareGeekCountRequest(new ApiRequestCallback<GetMateShareGeekResponse>() {
            @Override
            public void onSuccess(ApiData<GetMateShareGeekResponse> data) {
                RelationContactManager.setRecommendGeekNewCount(data.resp.newCount);
                RelationContactManager.setRecommendGeekAllCount(data.resp.totalCount);
                RelationContactManager.setRecommendGeekLastTime(System.currentTimeMillis());
                RelationContactManager.sendRecommendGeekReceiver(App.getAppContext());
            }


            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    //同步 boss和店长联系人
    private void syncBaseInfo(List<Long> bossSet, List<Long> dzSet) {
        if ((bossSet == null || bossSet.size() == 0) && (dzSet == null || dzSet.size() == 0))
            return;
        ContactBaseInfoRequest request = new ContactBaseInfoRequest(new ApiRequestCallback<ContactBaseInfoListResponse>() {
            @Override
            public void handleInChildThread(ApiData<ContactBaseInfoListResponse> data) {
                super.handleInChildThread(data);
                ContactManager contactManager = ContactManager.getInstance();
                // 解析服务器返回的联系人列表
                List<ContactBean> contactList = new ArrayList<>();
                if (data.resp != null) {
                    ContactMessageTimeRangeManager.getInstance().saveContactMessageTimeRangeRecord(data.resp.timeRange);

                    ContactMessageTimeRangeManager.getInstance().saveContactFoldText(data.resp.foldText);

                    if (data.resp.baseInfoList != null) {
                        int count = data.resp.baseInfoList.size();
                        for (int i = 0; i < count; i++) {
                            ContactBean bean = new ContactBean().fromServerContactBaseInfoBean(data.resp.baseInfoList.get(i), data.resp.userId, data.resp.identity);

                            int friendSource;
                            if (bossSet != null && bossSet.contains(bean.friendId)) {
                                friendSource = ContactBean.FROM_BOSS;
                            } else {
                                friendSource = ContactBean.FROM_DIAN_ZHANG;
                            }

                            ContactBean local = contactManager.queryContactByFriendId(bean.friendId, data.resp.identity, friendSource);
                            if (local != null) {//这几个字段是全量接口返回的，批量接口里面没有，但是都属于服务端字段
                                bean.currentInterviewStatus = local.currentInterviewStatus;
                                bean.currentInterviewDesc = local.currentInterviewDesc;
                                bean.currentInterviewProtocol = local.currentInterviewProtocol;
                                bean.isBlack = local.isBlack;
                                bean.friendPhone = local.friendPhone;
                                bean.friendWxNumber = local.friendWxNumber;
                                bean.isFreeze = local.isFreeze;
                                bean.isTechGeekBlock = local.isTechGeekBlock;
//                                bean.isStar = local.isStar;
                                bean.directCallStatus = local.directCallStatus;
                                bean.messageExchangeIcon = local.messageExchangeIcon;
                            }
                            contactList.add(bean);
                        }
                    }
                    // 更新本地联系人数据
                    contactManager.updateContactList(contactList, data.resp.identity);
                }
            }

            @Override
            public void onSuccess(ApiData<ContactBaseInfoListResponse> data) {
                ContactManager.getInstance().refreshContacts();
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.friendIds = idSetToString(bossSet);
        request.dzFriendIds = idSetToString(dzSet);
        HttpExecutor.execute(request);
    }


    // 用逗号拼接起来id
    private String idSetToString(List<Long> setIds) {
        StringBuilder sb = new StringBuilder();
        if (setIds == null || setIds.size() == 0) {
            return sb.toString();
        }
        Iterator<Long> idIterator = setIds.iterator();
        while (idIterator.hasNext()) {
            sb.append(idIterator.next());
            if (idIterator.hasNext()) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

}
