package com.hpbr.bosszhipin.data.manager;

import android.content.Context;
import android.content.Intent;

import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.monch.lbase.util.SP;

/**
 * 各种类型的联系人数量管理类
 * 比如看过我之类的
 */
public class RelationContactManager {


    public static void setRecommendGeekNewCount(int count) {
        if (UserManager.getUserRole() == ROLE.GEEK) return;
        if (count < 0) {
            count = 0;
        }
        SP.get().putInt(Constants.RECEIVER_RECOMMEND_GEEK_NEW_COUNT_KEY, count);
    }

    public static int getRecommendGeekNewCount() {
        int count = 0;
        if (UserManager.getUserRole() == ROLE.BOSS) {
            count = SP.get().getInt(Constants.RECEIVER_RECOMMEND_GEEK_NEW_COUNT_KEY, 0);
        }
        return count;
    }

    public static void setRecommendGeekAllCount(int count) {
        if (UserManager.getUserRole() == ROLE.GEEK) return;
        if (count < 0) {
            count = 0;
        }
        SP.get().putInt(Constants.RECEIVER_RECOMMEND_GEEK_ALL_COUNT_KEY, count);
    }

    public static int getRecommendGeekAllCount() {
        int count = 0;
        if (UserManager.getUserRole() == ROLE.BOSS) {
            count = SP.get().getInt(Constants.RECEIVER_RECOMMEND_GEEK_ALL_COUNT_KEY, 0);
        }
        return count;
    }

    public static void setRecommendGeekLastTime(long time) {
        if (UserManager.getUserRole() == ROLE.GEEK) return;
        SP.get().putLong(Constants.RECEIVER_RECOMMEND_GEEK_LAST_TIME_KEY, time);
    }

    public static long getRecommendGeekLastTime() {
        if (UserManager.getUserRole() == ROLE.BOSS) {
            return SP.get().getLong(Constants.RECEIVER_RECOMMEND_GEEK_LAST_TIME_KEY, 0);
        }
        return 0;
    }

    public static void setFollowCompanyJobCount(int count) {
        if (UserManager.getUserRole() == ROLE.BOSS) return;
        if (count < 0) {
            count = 0;
        }
        SP.get().putInt(Constants.RECEIVER_GEEK_FOLLOW_COMPANY_JOB_COUNT_KEY, count);
    }

    public static int getFollowCompanyJobCount() {
        if (UserManager.getUserRole() == ROLE.BOSS) return 0;
        return SP.get().getInt(Constants.RECEIVER_GEEK_FOLLOW_COMPANY_JOB_COUNT_KEY);
    }

    /**
     * 发送BOSS接收推荐牛人数量更新的广播
     *
     * @param context
     */
    public static void sendRecommendGeekReceiver(final Context context) {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_RECOMMEND_GEEK_COUNT_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        context.sendBroadcast(intent);
    }
}