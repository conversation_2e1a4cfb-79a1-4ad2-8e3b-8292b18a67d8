package com.hpbr.bosszhipin.data.manager.contact;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.AbsRequestException;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetListFriendCompanyRequest;
import net.bosszhipin.api.GetListFriendCompanyResponse;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import okhttp3.Response;
import okhttp3.ResponseBody;

public class ContactCompany {
    public static final String TAG = "ContactCompanyUpdate";

    //Boss获得好友的公司名称列表
    public static void update() {
        if (UserManager.isBossRole()) { // Boss用
            if (AndroidDataStarGray.getInstance().isIgnoreCompanyUpdate()) return;
            // 7.04 获取好友公司名称列表
            GetListFriendCompanyRequest r = new GetListFriendCompanyRequest(new ApiRequestCallback<GetListFriendCompanyResponse>() {

                @Override
                public ApiData<GetListFriendCompanyResponse> parseResponse(Response resp) throws IOException, AbsRequestException {
                    ResponseBody responseBodyCopy = resp.peekBody(Integer.MAX_VALUE);
                    String rawJson = responseBodyCopy.string();
                    if (rawJson != null) {
                        try {
                            JSONObject jsonObject = new JSONObject(rawJson);
                            JSONObject zpData = jsonObject.getJSONObject("zpData");
                            ApiData<GetListFriendCompanyResponse> data = new ApiData<>();
                            data.resp = new GetListFriendCompanyResponse();
                            if (UserManager.isBossRole() && zpData != null) {
                                if (zpData.has("data")) {
                                    JSONObject dataObject = zpData.optJSONObject("data");
                                    if (dataObject != null) {
                                        Map<String, Object> map = toMap(dataObject);
                                        data.resp.data = map;
                                        insertIntoDb(map, ContactBean.FROM_BOSS);
                                    }
                                }
                                if (zpData.has("dzData")) {
                                    JSONObject dataObject = zpData.optJSONObject("dzData");
                                    if (dataObject != null) {
                                        Map<String, Object> map = toMap(dataObject);
                                        data.resp.dzData = map;
                                        insertIntoDb(map, ContactBean.FROM_DIAN_ZHANG);
                                    }
                                }
                            }
                            return data;
                        } catch (Exception ignored) {
                        }
                    }
                    return super.parseResponse(resp);
                }

                private void insertIntoDb(@Nullable Map<String, Object> map, int friendSource) {
                    if (map == null || map.size() == 0) return;

                    Set<String> friendIds = map.keySet();

                    long[] ids = new long[friendIds.size()];
                    int index = 0;
                    for (String friendId : friendIds) {
                        ids[index] = LText.getLong(friendId);
                        index++;
                    }
                    ContactManager contactManager = ContactManager.getInstance();
                    if (AccountHelper.isBoss()) {
                        List<ContactBean> friendList = contactManager.getSearchContactList(AccountHelper.getIdentity(), ids, friendSource);
                        for (ContactBean contactBean : friendList) { // 7.04新增friendCompanies字段，用于表示该好友加入过的公司（可能多个）。这里更新该字段
                            if (contactBean == null) continue;

                            long friendId = contactBean.friendId;
                            contactBean.friendCompanies = getFriendCompanies(map, String.valueOf(friendId));
                        }
                        contactManager.updateFriendCompanies(friendList, AccountHelper.getIdentity());
                    }
                }

                private String getFriendCompanies(@NonNull Map<String, Object> map, String friendId) {
                    StringBuilder builder = new StringBuilder();
                    Object o = map.get(friendId);
                    if (!(o instanceof List)) return "";

                    List companies = (List) o;
                    int size = companies.size();
                    for (int i = 0; i < size; i++) {
                        Object company = companies.get(i);
                        if (!(company instanceof String)) continue;

                        builder.append((String) company);
                        if (i < size - 1) {
                            builder.append(StringUtil.SPLIT_CHAR_KEYWORD);
                        }
                    }
                    return builder.toString(); // 如："百度#&#腾讯#&#搜狗"
                }

                Map<String, Object> toMap(JSONObject jsonobj) throws JSONException {
                    Map<String, Object> map = new HashMap<>();
                    Iterator<String> keys = jsonobj.keys();
                    while (keys.hasNext()) {
                        String key = keys.next();
                        Object value = jsonobj.get(key);
                        if (value instanceof JSONArray) {
                            value = toList((JSONArray) value);
                        } else if (value instanceof JSONObject) {
                            value = toMap((JSONObject) value);
                        }
                        map.put(key, value);
                    }
                    return map;
                }

                List<Object> toList(JSONArray array) throws JSONException {
                    List<Object> list = new ArrayList<>();
                    for (int i = 0; i < array.length(); i++) {
                        Object value = array.get(i);
                        if (value instanceof JSONArray) {
                            value = toList((JSONArray) value);
                        } else if (value instanceof JSONObject) {
                            value = toMap((JSONObject) value);
                        }
                        list.add(value);
                    }
                    return list;
                }

                @Override
                public void onSuccess(ApiData<GetListFriendCompanyResponse> data) {
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    L.e(TAG, reason.getErrReason());
                }
            });
            //noinspection unchecked
            HttpExecutor.execute(r);
        }
    }
}
