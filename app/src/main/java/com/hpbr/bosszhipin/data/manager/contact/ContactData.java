package com.hpbr.bosszhipin.data.manager.contact;

import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.filter.ContactFilterFactory;
import com.hpbr.bosszhipin.module.contacts.filter.processor.SingleContactStatisticsProcessor;
import com.hpbr.bosszhipin.module.contacts.filter.processor.UnreadCountStatisticsProcessor;
import com.hpbr.bosszhipin.module.contacts.filter.transformer.DefaultSortTransformer;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactModeManager;
import com.techwolf.lib.tlog.TLog;

import java.util.Collections;
import java.util.List;

/**
 * 联系人汇总数据封装类
 * 封装联系人列表和相关未读数量信息，方便上层业务使用
 *
 * 使用新的FilterV2架构进行数据过滤和统计，通过ContactProcessor在一次遍历中完成所有统计
 *
 * <AUTHOR>
 * @since v13.15
 */
public class ContactData {

    public static final String TAG = "ContactData";
    /**
     * F2联系人列表
     */
    @NonNull
    private final List<ContactBean> f2ContactList;

    @NonNull
    private final UnreadCountStatisticsProcessor unreadCountStatisticsProcessor;
    @NonNull
    private final SingleContactStatisticsProcessor friendContactStatisticsProcessor;
    /**
     * 创建时间戳
     */
    private final long createTime;

    private ContactData(@NonNull List<ContactBean> f2ContactList, @NonNull SingleContactStatisticsProcessor friendContactStatisticsProcessor, @NonNull UnreadCountStatisticsProcessor unreadCountStatisticsProcessor) {
        this.f2ContactList = Collections.unmodifiableList(f2ContactList);
        this.friendContactStatisticsProcessor = friendContactStatisticsProcessor;
        this.unreadCountStatisticsProcessor = unreadCountStatisticsProcessor;
        this.createTime = System.currentTimeMillis();
    }

    /**
     * 创建ContactSummaryData实例（使用新的FilterV2架构）
     * 通过ContactProcessor在一次遍历中完成过滤和统计，性能更高
     * @return ContactData实例
     */
    public static ContactData create() {
        long startTime = SystemClock.elapsedRealtime();
        // 获取所有联系人数据
        List<ContactBean> allContacts = F2ContactModeManager.getInstance().getAllContact();
        TLog.debug(TAG, "create contact list: time %d", SystemClock.elapsedRealtime() - startTime);
        // 创建未读数统计处理器
        SingleContactStatisticsProcessor friendProcessor = new SingleContactStatisticsProcessor();
        UnreadCountStatisticsProcessor unreadProcessor = new UnreadCountStatisticsProcessor();
        startTime = SystemClock.elapsedRealtime();
        // 使用新的FilterV2架构获取F2联系人列表，同时统计未读数
        List<ContactBean> f2ContactList = ContactFilterFactory.createF2ContactListFilters()
                .addProcessor(friendProcessor)
                .addProcessor(unreadProcessor)  // 在过滤过程中同时统计未读数
                .setTransformer(new DefaultSortTransformer())
                .apply(allContacts);

        long total = SystemClock.elapsedRealtime() - startTime;
        if (total > 50) {
            TLog.debug(TAG, "create contactData: time %d", total);
        }
        return new ContactData(f2ContactList, friendProcessor, unreadProcessor);
    }


    /**
     * 获取F2联系人列表
     * 【注意】这里返回的不可变列表
     * @return 不可变的联系人列表
     */
    @NonNull
    public List<ContactBean> getF2ContactList() {
        return f2ContactList;
    }

    /**
     *
     * 获取F2未读数量（气泡数量）
     * @return F2未读数量
     */
    public int getContactNoneReadMsgCount() {
        return unreadCountStatisticsProcessor.getContactNoneReadMsgCount();
    }

    /**
     * 获取单聊联系人未读数量
     * @return 单聊联系人未读数量
     */
    public int getSingleContactNoneReadCount() {
        return friendContactStatisticsProcessor.getUnReadCount();
    }

    /**
     * 获取免打扰人数
     * @return
     */
    public int getSilentTypeContactCount() {
        return unreadCountStatisticsProcessor.getSilentTypeContactCount();
    }

    /**
     * 获取创建时间戳
     * @return 创建时间戳
     */
    public long getCreateTime() {
        return createTime;
    }

    /**
     * 联系人列表是否为空
     * @return true表示联系人列表为空
     */
    public boolean isEmpty() {
        return f2ContactList.isEmpty();
    }

    /**
     * 获取联系人数量
     * @return 联系人数量
     */
    public int getContactCount() {
        return f2ContactList.size();
    }

    /**
     * 获取单聊联系人数量
     * @return 联系人数量
     */
    public int getSingleContactCount() {
        return friendContactStatisticsProcessor.getContactCount();
    }

    /**
     * 置顶联系人数
     * @return
     */
    public int getTopContactCount() {
        return unreadCountStatisticsProcessor.getTopContactCount();
    }

    /**
     * 置顶联系人未读数
     * @return
     */
    public int getTopContactUnreadCount() {
        return unreadCountStatisticsProcessor.getTopUnreadCount();
    }

    /**
     * 检查一周内是否有未读消息
     */
    public boolean isNoneReadMsgTimeMillisOutWeek() {
        return friendContactStatisticsProcessor.isNoneReadMsgTimeMillisOutWeek();
    }

    /**
     * 检查n天内是否有未读消息
     */
    public boolean isNoneReadMsgTimeMillisOutDays(int days) {
        return friendContactStatisticsProcessor.isNoneReadMsgTimeMillisOutDays(days);
    }

    /**
     * Boss没有未读的被动消息
     * 或者
     * 最近一条未读的被动消息距离当前>48h（只算单聊/不算系统消息/不区分蓝白）
     *
     * @return
     */
    public boolean checkCanRequestPayBanner() {
        return friendContactStatisticsProcessor.checkCanRequestPayBanner();
    }

    /**
     * 获取最后一条未读消息的时间
     */
    public long getLastNoneReadMsgTime() {
        return friendContactStatisticsProcessor.getLastNoneReadMsgTime();
    }

    public boolean hasMoreThanTwoContactJob() {
        return friendContactStatisticsProcessor.hasMoreThanTwoContactJob();
    }


    @Override
    public String toString() {
        return "ContactSummaryData{" +
                "contactCount=" + getContactCount() +
                ", f2NoneReadCount=" + getContactNoneReadMsgCount() +
                '}';
    }

}