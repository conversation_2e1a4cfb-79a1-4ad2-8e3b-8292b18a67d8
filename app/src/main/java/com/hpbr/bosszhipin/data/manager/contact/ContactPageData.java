package com.hpbr.bosszhipin.data.manager.contact;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.monch.lbase.util.LList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

class ContactPageData implements Serializable {

    public static final int PAGE_SIZE = 1000;

    private static final long serialVersionUID = 4243535868643374784L;
    public final List<ContactBean> allFriends;
    public final List<Long> zpFriendIdList = new ArrayList<>();
    public final List<Long> dzFriendIdList = new ArrayList<>();
    public final List<ContactBean> delFriends = new ArrayList<>();

    public int startIndex;

    public int source = ContactBean.FROM_BOSS;
    public int pageSize;

    public int retryCount;
    public MonitorData monitorData;

    public ContactPageData(List<ContactBean> allFriends, MonitorData monitorData) {
        this.monitorData = monitorData;
        this.allFriends = allFriends;
    }


    protected void computeDelFriend(List<Long> zpFriendIdList, List<Long> dzFriendIdList) {
        for (ContactBean contactBean : allFriends) {
            int index = -1;
            if (contactBean.friendSource == ContactBean.FROM_BOSS) {
                index = zpFriendIdList.indexOf(contactBean.friendId);
            } else if (contactBean.friendSource == ContactBean.FROM_DIAN_ZHANG) {
                index = dzFriendIdList.indexOf(contactBean.friendId);
            }
            if (index == -1) {
                delFriends.add(contactBean);
            }
        }
    }

    protected void computeDelFriend() {
        for (ContactBean contactBean : allFriends) {
            int index = -1;
            if (contactBean.friendSource == ContactBean.FROM_BOSS) {
                index = zpFriendIdList.indexOf(contactBean.friendId);
            } else if (contactBean.friendSource == ContactBean.FROM_DIAN_ZHANG) {
                index = dzFriendIdList.indexOf(contactBean.friendId);
            }
            if (index == -1) {
                delFriends.add(contactBean);
            }
        }
    }

    int computePageSize() {
        int pageSize = 0;
        if (source == ContactBean.FROM_BOSS) {
            if (LList.isNotEmpty(zpFriendIdList)) {
                pageSize = Math.min(PAGE_SIZE, zpFriendIdList.size() - startIndex);
            }
        } else if (source == ContactBean.FROM_DIAN_ZHANG) {
            if (LList.isNotEmpty(dzFriendIdList)) {
                pageSize = Math.min(PAGE_SIZE, dzFriendIdList.size() - startIndex);
            }
        }
        return pageSize;
    }

    List<Long> getFriendIdList() {
        if (pageSize <= 0) {
            return null;
        }
        if (source == ContactBean.FROM_BOSS && LList.isNotEmpty(zpFriendIdList)) {
            int toIndex = pageSize + startIndex;
            if (startIndex < toIndex) {
                return zpFriendIdList.subList(startIndex, toIndex);
            }
        }

        if (source == ContactBean.FROM_DIAN_ZHANG && LList.isNotEmpty(dzFriendIdList)) {
            int toIndex = pageSize + startIndex;
            if (startIndex < toIndex) {
                return dzFriendIdList.subList(startIndex, toIndex);
            }
        }
        return null;
    }

    boolean hasMore() {
        startIndex = startIndex + pageSize; //下一页的index
        pageSize = computePageSize();
        if (pageSize < 1) {
            if (source == ContactBean.FROM_BOSS) {
                source = ContactBean.FROM_DIAN_ZHANG;
                startIndex = 0;
                pageSize = computePageSize();
            }
        }
//            if (pageSize > 0) {
//                this.pageSize = pageSize;
//            }
        return pageSize > 0;
    }


    @NonNull
    @Override
    public String toString() {
        return "{" +
                "\"startIndex\":" + startIndex +
                ", \"source\":" + source +
                ", \"pageSize\":" + pageSize +
                ", \"retryCount\":" + retryCount +
                '}';
    }
}