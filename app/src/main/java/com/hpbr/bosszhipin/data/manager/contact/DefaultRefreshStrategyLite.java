package com.hpbr.bosszhipin.data.manager.contact;


import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ApiCallbackSafeCallback;
import net.bosszhipin.api.ContactFriendListRequest;
import net.bosszhipin.api.ContactFriendListResponse;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class DefaultRefreshStrategyLite extends IRefreshStrategy {
    public static final String TAG = "DefaultRefreshLite";
    public static final String REFRESH_CONTACT_TIME_KEY = "com.hpbr.bosszhipin.InitContactDataService.REFRESH_CONTACT_LITE_TIME_KEY";

    public static final DefaultRefreshStrategyLite DEFAULT_REFRESH_STRATEGY_LITE = new DefaultRefreshStrategyLite();

    public static DefaultRefreshStrategyLite getInstance() {
        return DEFAULT_REFRESH_STRATEGY_LITE;
    }

    private int identity;
    private long uid;

    @Override
    public boolean isNeedRefresh() {
        if (isRefresing.get()) {
            return false;
        }
        final long lastTimeAll = SpManager.get().global().getLong(DefaultRefreshStrategyV2.REFRESH_CONTACT_TIME_KEY, 0);
        if (ContactHandleManager.isOverTodayHour8(lastTimeAll)){
            return false;
        }
        final long lastTime = SpManager.get().global().getLong(REFRESH_CONTACT_TIME_KEY, 0);
        TLog.debug(TAG, "=========isNeedRefresh====== lastTime = %d  lastTimeAll = %d",lastTime,lastTimeAll);
        return System.currentTimeMillis() - Math.max(lastTime,lastTimeAll) > 1000 * 60 * 60 * 2; // 两个小时
    }


    boolean isDiffAccount() {
        return !isRefreshing() || uid != AccountHelper.getUid() || identity != AccountHelper.getIdentity();
    }

    @Override
    public void doRefresh() {
        uid = AccountHelper.getUid();
        identity = AccountHelper.getIdentity();
        TLog.info(TAG, "=========doRefresh====== %d %d %d ", uid, identity, hashCode());
        isRefresing.set(true);
        ContactFriendListRequest request = new ContactFriendListRequest(new ApiCallbackSafeCallback<ContactFriendListResponse>() {
            final long startTime = System.currentTimeMillis();

            @Override
            public void handleInChildThread(ApiData<ContactFriendListResponse> data) {
                long requestTime = System.currentTimeMillis() - startTime;
                List<ContactBean> allContacts = ContactManager.getInstance().getAllContact();
                TLog.debug(TAG, "   request friendList allContact = %d time= %d", LList.getCount(allContacts), requestTime);
                ContactPageData contactPageData = handleContactPageData(data.resp, allContacts);
                long handleDataTime = System.currentTimeMillis() - startTime - requestTime;
                TLog.debug(TAG, "   handleContactPageData time= %d", handleDataTime);
                if (isDiffAccount()) {
                    TLog.error(TAG, "diffAccount uid = %d role = %s hashCode = %d", AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyLite.this.hashCode());
                    return;
                }
                handleDelContact(contactPageData.delFriends);
                updateHandlerTime();

                //监控联系人数量
                if (!LList.isEmpty(allContacts) && LList.isEmpty(data.resp.dzFriendIdList) && LList.isEmpty(data.resp.zpFriendIdList)) {
                    ApmAnalyzer.create().action("action_contact", "contact_empty").p2(String.valueOf(LList.getCount(allContacts))).p3("lite").report();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                resetRefreshing();
            }

            /**
             *
             * @param response
             * @param allContacts
             * @return
             */
            private ContactPageData handleContactPageData(ContactFriendListResponse response, List<ContactBean> allContacts) {
                long startTime = System.currentTimeMillis();
                ContactPageData contactPageData = new ContactPageData(allContacts);
                int zpNewAddCount = 0;
                if (response.zpFriendIdList != null) {
                    contactPageData.zpFriendIdList.addAll(response.zpFriendIdList);
                    for (Long friendId : response.zpFriendIdList) {
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, identity, ContactBean.FROM_BOSS);
                        if (contactBean != null) {
//                            allContacts.remove(contactBean);
                        } else {
                            zpNewAddCount++;
                        }
                    }
                }
                TLog.debug(TAG, "handleContactPageData zpFriendIdList time= %d", System.currentTimeMillis() - startTime);
                int dzNewAddCount = 0;
                if (response.dzFriendIdList != null) {
                    contactPageData.dzFriendIdList.addAll(response.dzFriendIdList);
                    for (Long friendId : response.dzFriendIdList) {
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, identity, ContactBean.FROM_DIAN_ZHANG);
                        if (contactBean != null) {
//                            allContacts.remove(contactBean);
                        } else {
                            dzNewAddCount++;
                        }
                    }
                }

                // 计算删除人
                contactPageData.computeDelFriend();
                TLog.debug(TAG, "handleContactPageData dzFriendIdList time= %d", System.currentTimeMillis() - startTime);

                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH_LITE)
                        .put("status", "init")
                        .put("zpFriendIdList", LList.getCount(response.zpFriendIdList))
                        .put("zpNewAddCount", zpNewAddCount)
                        .put("dzFriendIdList", LList.getCount(response.dzFriendIdList))
                        .put("dzNewAddCount", dzNewAddCount)
                        .put("delContacts", LList.getCount(contactPageData.delFriends)).info();

                return contactPageData;
            }
        });
        request.execute();
    }

    private void handleDelContact(List<ContactBean> delContacts) {
        ContactDoctorFactory.ContactDel.start(delContacts);
        int count = LList.getCount(delContacts);
        if (count > 0) {
            ApmAnalyzer.create().action("action_contact", "del_contact_lite").p2(String.valueOf(count)).debug().report();
            //批量删除联系人
            ContactManager.getInstance().batchDeleteContacts(delContacts, identity);
            //批量删除消息
            ContactHandleManager.deleteMessagesByContacts(delContacts, identity);

            String delFriends = StringUtil.connectTextWithChar(",", delContacts, (StringUtil.IValueFunction<ContactBean>) contactBean -> String.valueOf(contactBean.friendId));
            LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH_LITE)
                    .put("status", "del")
                    .put("friends",delFriends).info();
        }
    }

    private static void updateHandlerTime() {
        long curTime = System.currentTimeMillis();
        SpManager.get().global().edit().putLong(REFRESH_CONTACT_TIME_KEY, curTime).apply();
    }


    @Override
    public void initRefreshTime() {
        resetRefreshing();
        SpManager.get().global().edit().remove(REFRESH_CONTACT_TIME_KEY).apply();
    }



    public static class ContactPageData implements Serializable {
        private static final long serialVersionUID = 745304642621345168L;
        public final List<Long> zpFriendIdList = new ArrayList<>();
        public final List<Long> dzFriendIdList = new ArrayList<>();
        public final List<ContactBean> delFriends = new ArrayList<>();
        public final List<ContactBean> allFriends;

        public ContactPageData(List<ContactBean> allContacts) {
            this.allFriends = allContacts;
        }


        protected void computeDelFriend() {
            for (ContactBean contactBean : allFriends) {
                int index = -1;
                if (contactBean.friendSource == ContactBean.FROM_BOSS) {
                    index = zpFriendIdList.indexOf(contactBean.friendId);

                } else if (contactBean.friendSource == ContactBean.FROM_DIAN_ZHANG) {
                    index = dzFriendIdList.indexOf(contactBean.friendId);
                }
                if (index == -1) {
                    delFriends.add(contactBean);
                }
            }
        }
    }


}
