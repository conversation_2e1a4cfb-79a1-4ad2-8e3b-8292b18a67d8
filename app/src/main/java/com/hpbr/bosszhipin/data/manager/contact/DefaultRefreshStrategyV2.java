package com.hpbr.bosszhipin.data.manager.contact;


import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.dao.DaoFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactMessageTimeRangeManager;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.RejectDrawContactManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.module.workorder.LogDiagnoseManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.ApiCallbackSafeCallback;
import net.bosszhipin.api.ContactBaseInfoListResponse;
import net.bosszhipin.api.ContactBaseInfoRequest;
import net.bosszhipin.api.ContactFriendListRequest;
import net.bosszhipin.api.ContactFriendListResponse;

import java.util.List;

import message.handler.MessageUtils;
import message.handler.analysis.ChatAnalyzer;
import message.handler.dao.MessageDaoFactory;
import message.handler.dao.MigrationWCDB;

public class DefaultRefreshStrategyV2 extends IRefreshStrategy {
    public static final String TAG = "DefaultRefreshV2";
    public static final String REFRESH_CONTACT_TIME_KEY = "com.hpbr.bosszhipin.InitContactDataService.REFRESH_CONTACT_TIME_KEY";

    private final ContactSyncHandler handler;
    // 重试时间
    public static final int[] RETRY_TIME = {1, 2, 3, 4, 8, 16, 32};
    static HandlerThread handlerThread;
    private int identity;
    private long uid;

    static HandlerThread getHandlerThread() {
        if (handlerThread == null) {
            handlerThread = new HandlerThread("DefaultRefreshStrategyV2");
            handlerThread.start();
        }
        return handlerThread;
    }

    public DefaultRefreshStrategyV2() {
        handler = new ContactSyncHandler(getHandlerThread().getLooper());
    }

    private void refreshCurrentInfo() {
        uid = AccountHelper.getUid();
        identity = AccountHelper.getIdentity();
    }

    @Override
    public boolean isNeedRefresh() {
        if (isRefresing.get()) {
            return false;
        }
        final long lastTime = SpManager.get().global().getLong(REFRESH_CONTACT_TIME_KEY, 0);
        return ContactHandleManager.isOverTodayHour8(lastTime);
    }


    boolean isDiffAccount() {
        return !isRefreshing() || uid != AccountHelper.getUid() || identity != AccountHelper.getIdentity();
    }

    @Override
    public void doRefresh() {
        refreshCurrentInfo();
        TLog.info(TAG, "=========doRefresh====== %d %d %d", uid, identity, hashCode());
        isRefresing.set(true);
        ContactFriendListRequest request = new ContactFriendListRequest(new ApiCallbackSafeCallback<ContactFriendListResponse>() {
            final long startTime = System.currentTimeMillis();
            final MonitorData monitorData = new MonitorData();

            @Override
            public void handleInChildThread(ApiData<ContactFriendListResponse> data) {
                long requestTime = System.currentTimeMillis() - startTime;
                List<ContactBean> allContact = ContactManager.getInstance().getAllContact();
                TLog.debug(TAG, "   request friendList allContact = %d time= %d", LList.getCount(allContact), requestTime);
                ContactPageData contactPageData = handleContactPageData(data.resp, allContact);
                long handleDataTime = System.currentTimeMillis() - startTime - requestTime;
                monitorData.setBaseData(contactPageData);
                monitorData.addPrePageTime(requestTime, handleDataTime);
                TLog.debug(TAG, "   handleContactPageData time= %d", handleDataTime);
                //异步拉取联系人
                startContactSync(contactPageData, 0);

                asyncStatisticsMessageCount();
                ContactDoctorFactory.checkOverlappingFriend2(data.resp.zpFriendIdList,data.resp.dzFriendIdList);
                //监控联系人数量
                if (!LList.isEmpty(allContact) && LList.isEmpty(data.resp.dzFriendIdList) && LList.isEmpty(data.resp.zpFriendIdList)) {
                    ApmAnalyzer.create().action("action_contact", "contact_empty").p2(String.valueOf(LList.getCount(allContact))).report();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                resetRefreshing();
            }

            /**
             *
             * @param response
             * @param allContacts
             * @return
             */
            private ContactPageData handleContactPageData(ContactFriendListResponse response, List<ContactBean> allContacts) {
                int zpNewAddCount = 0;
                long startTime = System.currentTimeMillis();
                ContactPageData contactPageData = new ContactPageData(allContacts, monitorData);
                if (response.zpFriendIdList != null) {
                    for (Long friendId : response.zpFriendIdList) {
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, identity, ContactBean.FROM_BOSS);
                        if (contactBean != null) {
                            contactPageData.zpFriendIdList.add(friendId);
                        } else {
                            zpNewAddCount++;
                            contactPageData.zpFriendIdList.add(0, friendId);//新优先处理新增联系人
                        }
                    }
                }
                monitorData.setZpAddCount(zpNewAddCount);
                TLog.debug(TAG, "handleContactPageData zpFriendIdList time= %d", System.currentTimeMillis() - startTime);

                int dzNewAddCount = 0;
                if (response.dzFriendIdList != null) {
                    for (Long friendId : response.dzFriendIdList) {
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, identity, ContactBean.FROM_DIAN_ZHANG);
                        if (contactBean != null) {
                            contactPageData.dzFriendIdList.add(friendId);
                        } else {
                            dzNewAddCount++;
                            contactPageData.dzFriendIdList.add(0, friendId);//新优先处理新增联系人
                        }
                    }
                }

                monitorData.setDzAddCount(dzNewAddCount);
                // 计算需删除数据
                contactPageData.computeDelFriend();

                //计算分页大小及分页数据
                boolean hasMore = contactPageData.hasMore();

                TLog.debug(TAG, "handleContactPageData dzFriendIdList hasMore = %b time= %d", hasMore, System.currentTimeMillis() - startTime);
                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                        .put("status", "init")
                        .put("localFriends", LList.getCount(allContacts))
                        .put("zpFriendIdList", LList.getCount(response.zpFriendIdList))
                        .put("zpNewAddCount", zpNewAddCount)
                        .put("dzFriendIdList", LList.getCount(response.dzFriendIdList))
                        .put("dzNewAddCount", dzNewAddCount)
                        .put("delContacts", LList.getCount(contactPageData.delFriends)).info();
                return contactPageData;
            }
        });
        request.execute();
        GroupManager.getInstance().loadAllGroupInfoFromServer(null);
        // 1226.80【B/C】聊天场景无用信息收纳功能
        refreshRejectFriendData();
    }

    private void handleDelContact(List<ContactBean> delContacts) {
        LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_SYNC_DEL_CONTACT)
                .put("status", "start").put("friendCount", LList.getCount(delContacts)).info();
        long startTime = System.currentTimeMillis();
        ContactDoctorFactory.ContactDel.start(delContacts);
        //删除好友关系
        ContactHandleManager.deleteContactsAndMessagesDaoV2(delContacts, identity);

        LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                .put("status", "end").put("friendCount", LList.getCount(delContacts)).put("time", System.currentTimeMillis()-startTime).info();
    }

    private static void updateHandlerTime() {
        long curTime = System.currentTimeMillis();
        SpManager.get().global().edit().putLong(REFRESH_CONTACT_TIME_KEY, curTime).apply();
    }

    /**
     * 异步统计MessageCount
     */
    private void asyncStatisticsMessageCount() {
        AppThreadFactory.createThread(new Runnable() {
            @Override
            public void run() {
                long currentGeekMessageCount = MessageDaoFactory.getMessageDao().getMessageCount(UserManager.getUID(), 0);
                long currentBossMessageCount = MessageDaoFactory.getMessageDao().getMessageCount(UserManager.getUID(), 1);
                int p3 = 0;
                if (UserManager.isGeekRole()) {
                    p3 = (int) (currentGeekMessageCount / 500);
                } else {
                    p3 = (int) (currentBossMessageCount / 500);
                }
                if (SpManager.get().user().getInt("asyncStatisticsMessageCount", 0) == p3) {
                    return;
                }
                ChatAnalyzer.report(ChatAnalyzer.MESSAGE_COUNT).p2(String.valueOf(AccountHelper.getIdentity())).p3(String.valueOf(p3)).p4(String.valueOf(currentGeekMessageCount)).p5(String.valueOf(currentBossMessageCount)).p8(String.valueOf(MigrationWCDB.getMigrationMessageVersion())).report();
                SpManager.get().user().edit().putInt("asyncStatisticsMessageCount", p3).apply();
            }
        }).start();
    }


    @Override
    public void initRefreshTime() {
        resetRefreshing();
        handler.removeCallbacksAndMessages(null);
        SpManager.get().global().edit().remove(REFRESH_CONTACT_TIME_KEY).apply();
    }


    private void startContactSync(@NonNull ContactPageData contactPageData, long delayMillis) {
        handler.removeMessages(TASK);
        if (isDiffAccount()) {
            TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d refreshing = %b", contactPageData, AccountHelper.getUid(), AccountHelper.getIdentity(), hashCode(), isRefreshing());
            return;
        }
        handler.startSyncing(contactPageData);
        handler.sendMessageDelayed(Message.obtain(handler, TASK, contactPageData), delayMillis);
    }

    public static final int TASK = 1000;

    class ContactSyncHandler extends Handler {


        public ContactSyncHandler(Looper looper) {
            super(looper);
        }


        public synchronized void startSyncing(@NonNull ContactPageData contactPageData) {
            if(contactPageData.startIndex == 0 && contactPageData.source == ContactBean.FROM_BOSS){
                ContactManager.getInstance().monitorContactSaveData(1);
            }
        }

        public synchronized void stopSyncing(@NonNull ContactPageData contactPageData) {
            handleDelContact(contactPageData.delFriends);//删除联系人
            ContactManager.getInstance().monitorContactSaveData(2);
            MonitorData monitor = contactPageData.monitorData;
            if (monitor != null) {
                monitor.stop();
                String jsonDetail = GsonUtils.toJson(monitor);

                LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                        .put("status", "result")
                        .put("success", contactPageData.retryCount == 0)
                        .put("count", monitor.getCount())
                        .put("msgTime", String.valueOf(monitor.getMsgTime()))
                        .put("totalTime", String.valueOf(monitor.getTotalTime()))
                        .put("detail", jsonDetail).info();

                if (contactPageData.retryCount > 0 || monitor.getTotalTime() > 5000) {
                    ApmAnalyzer.create().action("action_contact", "base_info_list")
                            .p2(String.valueOf(contactPageData.retryCount == 0))
                            .p3(String.valueOf(monitor.getCount()))
                            .p4(String.valueOf(monitor.getMsgTime()))
                            .p5(String.valueOf(monitor.getTotalTime()))
                            .p8(jsonDetail).debug()
                            .report();
                }
            }
            resetRefreshing();
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.obj instanceof ContactPageData) {
                ContactPageData pageData = (ContactPageData) msg.obj;

                if (isDiffAccount()) {
                    TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <1> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV2.this.hashCode(), isRefreshing());
                    return;
                }

                final List<Long> friendIds = pageData.getFriendIdList();
                if (LList.isEmpty(friendIds)) {
                    TLog.error(TAG, "friendIds is null");
                    stopSyncing(pageData);
                    updateHandlerTime();
                    return;
                }
                TLog.info(TAG, "pull start contactPage = %s hashCode = %d", pageData,DefaultRefreshStrategyV2.this.hashCode());
                ContactBaseInfoRequest request = new ContactBaseInfoRequest(new ApiCallbackSafeCallback<ContactBaseInfoListResponse>() {
                    final MonitorData monitor = pageData.monitorData;
                    final long httpStartTime = System.currentTimeMillis();

                    @Override
                    public void handleInChildThread(ApiData<ContactBaseInfoListResponse> data) {
                        super.handleInChildThread(data);
                        long requestTime = System.currentTimeMillis() - httpStartTime;
                        LogWise.chat(AnalyticLog.ChatAnalytic.CONTACT_DEFAULT_REFRESH)
                                .put("status", "start")
                                .put("contactPage", pageData)
                                .put("baseInfoList", LList.getCount(data.resp.baseInfoList))
                                .put("time", requestTime)
                                .info();
                        if (monitor != null) {
                            monitor.success(requestTime);
                        }
                        pageData.retryCount = 0;

                        if (data.resp != null) {
                            if (isDiffAccount(data.resp)) {
                                TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <2> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV2.this.hashCode(), isRefreshing());
                                return;
                            }
                            //保存30天聊天记录
                            ContactMessageTimeRangeManager.getInstance().saveContactMessageTimeRangeRecord(data.resp.timeRange);
                            ContactMessageTimeRangeManager.getInstance().saveContactFoldText(data.resp.foldText);
                            TLog.debug(TAG, "pageData = %s ", pageData);
                            //新版联系人
                            DaoFactory.upsertContactBaseInfoList(data.resp.baseInfoList);
                            TLog.debug(TAG, "pageData = %s end", pageData);
                            ContactHandleManager contactHandleManager = null;
                            if (data.resp.baseInfoList != null) {
                                long start = System.currentTimeMillis();
                                int count = data.resp.baseInfoList.size();
                                if (isDiffAccount(data.resp)) {
                                    TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <3> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV2.this.hashCode(), isRefreshing());
                                    return;
                                }
                                contactHandleManager = new ContactHandleManager(data.resp);
                                contactHandleManager.insertAndUpdateCacheV2();
                                contactHandleManager.batchInsertAndUpdateContactsDaoV2();

                                if (monitor != null) {
                                    monitor.addPageTime(new MonitorData.MonitorTime(System.currentTimeMillis() - start, count));
                                }
                            }
                            if (isDiffAccount(data.resp)) {
                                TLog.error(TAG, "pageData = %s ,diffAccount uid = %d role = %s hashCode = %d <5> refreshing = %b", pageData, AccountHelper.getUid(), AccountHelper.getIdentity(), DefaultRefreshStrategyV2.this.hashCode(), isRefreshing());
                                return;
                            }
                            if (pageData.hasMore()) {
                                startContactSync(pageData, 50);
                            } else {
                                stopSyncing(pageData);
                                //Boss获得好友的公司名称列表
                                ContactCompany.update();
                                updateHandlerTime();
                                //更新联系人双聊状态
                                FriendStageManager.getInstance().checkSetFriendStage(F2ContactHelper.getInstance().getSingleContact(false),true);
                                //检测最后一个联系人文案为空
                                int totalCount = LList.getCount(pageData.zpFriendIdList) + LList.getCount(pageData.dzFriendIdList);
                                MessageUtils.checkLastTextEmptyFromDefaultRefresh(totalCount);
                                //检查并删除重复联系人
                                if (null != contactHandleManager) {
                                    contactHandleManager.checkSameContactAndDeleteNotInCacheV2();
                                }
                                LogDiagnoseManager.getInstance().saveContactListSp();
                            }
                        }

                    }

                    private boolean isDiffAccount(ContactBaseInfoListResponse resp) {
                        return !isRefreshing() || resp.userId != uid || resp.identity != identity;
                    }

                    @Override
                    public void onSuccess(ApiData<ContactBaseInfoListResponse> data) {
                        if (isDiffAccount(data.resp)) {
                            return;
                        }
                        ContactManager.getInstance().refreshContacts();
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        if (monitor != null) {
                            monitor.retry();
                        }
                        TLog.error(TAG, "pull contact error pageData = %s reason = %s ", pageData, reason);
                        if (pageData.retryCount < RETRY_TIME.length) {
                            startContactSync(pageData, RETRY_TIME[pageData.retryCount] * 1000L);
                            pageData.retryCount++;
                        } else {
                            stopSyncing(pageData);
                            TLog.error(TAG, "stop retry pull contact pageData = %s", pageData);
                        }
                    }
                });
                if (pageData.source == ContactBean.FROM_BOSS) {
                    request.friendIds = StringUtil.connectTextWithChar(",", friendIds, (StringUtil.IValueFunction<Long>) String::valueOf);
                } else {
                    request.dzFriendIds = StringUtil.connectTextWithChar(",", friendIds, (StringUtil.IValueFunction<Long>) String::valueOf);
                }
                request.execute();
            }

        }

    }


    private void refreshRejectFriendData() {
        RejectDrawContactManager.getInstance().requestDrawerData();
    }
}
