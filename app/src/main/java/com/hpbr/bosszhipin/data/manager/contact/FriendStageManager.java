package com.hpbr.bosszhipin.data.manager.contact;

import static com.hpbr.bosszhipin.event.ApmAnalyticsAction.ACTION_LAST_CHAT_TEXT;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

import message.handler.dao.MessageDaoFactory;

/**
 * create by guofeng
 * date on 2023/5/8
 * ------------------------------------------------------------------------------
 * =========================测试总结：账号大部分联系人都是双聊=====================
 * ------------------------------------------------------------------------------
 * XiaoMi 13 Pro设备 （新设备13系统）
 * 【批量处理1000】
 * 联系人数量：8586 , friendStage全部设置0, 总耗时：24785ms
 * 联系人数量：8586 , friendStage全部是正常,总耗时： 1859ms
 * 【批量处理200】
 * 联系人数量：8586 , friendStage全部设置0, 总耗时：25016ms
 * 联系人数量：8586 , friendStage全部是正常,总耗时： 1365ms（查询处理的数据300来个）
 * ------------------------------------------------------------------------------
 * Mi MAX设备 （比较老的设备7.0系统）
 * 【批量处理200】
 * 联系人数量：6578 , friendStage全部设置0,总耗时：45840ms
 * 联系人数量：6578 , friendStage全部是正常,总耗时： 545ms
 * <p>
 * 【批量处理1000】
 * 联系人数量：6578 , friendStage全部设置0,总耗时：41673ms
 * 联系人数量：6578 , friendStage全部是正常,总耗时： 357ms
 * ------------------------------------------------------------------------------
 * --------------------------------------------------------------------------------
 */
public class FriendStageManager {

    private boolean isRunnable = false;

    private static final FriendStageManager instance = new FriendStageManager();

    public static FriendStageManager getInstance() {
        return instance;
    }

    private FriendStageManager() {
    }

    private static final String TAG = "FriendStageManager";

    public void checkSetFriendStage(@Nullable List<ContactBean> mDatas, boolean isForceRefresh) {
        if (mDatas == null || mDatas.isEmpty()) return;
        if (AndroidDataStarGray.getInstance().isContactDelayFixData()) return;
        if (isRunnable) return;
        if (!isForceRefresh && checkTodayHasRefresh()) {
            L.d(TAG, "当前账号当天已经检测过");
            return;
        }
        isRunnable = true;
        AppThreadFactory.POOL.execute(new RunnableFriendStage(mDatas));
    }

    public static final int BATCH_QUERY_SIZE = 1000;

    private class RunnableFriendStage implements java.lang.Runnable {


        private final List<ContactBean> friendSendToMe = new ArrayList<>();
        private final List<ContactBean> iHaveSendFriend = new ArrayList<>();
        private final List<ContactBean> checkAllStage = new ArrayList<>();

        private final @NonNull List<ContactBean> contactBeans;

        public RunnableFriendStage(@NonNull List<ContactBean> contactBeans) {
            this.contactBeans = contactBeans;
        }


        @Override
        public void run() {
            L.d(TAG, "集合总大小=" + LList.getCount(contactBeans));

            long myUserId = UserManager.getUID();
            int myUserRole = UserManager.getUserRole().get();

            //记录刷新时间
            saveTodayRefreshTime();

            try {

                long startTime = System.currentTimeMillis();

                //按照 我发送/好友发送/双聊 分组
                initGroupByFriendStageStatus();

                if (!isRunnable) return;
                //好友已经给我发送，检测我是否给好友发送了数据，如果是：设置为双聊
                int updateCheckISendStageCount = friendHasSendCheckISendStage(myUserId, myUserRole, startTime);

                if (!isRunnable) return;

                //我已经给好友发送了消息，检测好友是否给我发送了数据，如果是：设置为双聊
                int updateCheckFriendSendStageCount = iHaveSendCheckFriendSendStage(myUserId, myUserRole, startTime);

                if (!isRunnable) return;

                //联系人无状态，判断 新招呼｜仅沟通｜双聊
                int updateAllStageCount = checkAllStage(myUserId, myUserRole, startTime);

                if (!isRunnable) return;

                //通知页面刷新
                ContactManager.getInstance().refreshContacts();

                long totalCoastTime = System.currentTimeMillis() - startTime;

                L.d(TAG, "总耗时：" + (totalCoastTime));



                if (updateCheckISendStageCount > 0
                        || updateCheckFriendSendStageCount > 0
                        || updateAllStageCount > 0
                        || totalCoastTime > 5000) {
                    //收集下大于50s的用户，看下有多少 ，针对耗时的用户分析下原因，8000个联系人全部没有数据测试40s左右可以同步完毕 （性能差的手机）
                    apmReport(totalCoastTime, getReportInfo(updateCheckFriendSendStageCount, updateCheckISendStageCount, updateAllStageCount), "");
                }


            } catch (Exception e) {
                e.printStackTrace();
                apmReport(0, getReportInfo(0, 0, 0), e.getMessage());
            }

            L.d(TAG, "处理完毕");
            //保存当前账号已经初始化过了
            isRunnable = false;
        }


        private String getReportInfo(int updateFriendSendToMe, int updateIHaveSendFriend, int updateCheckAllStage) {
            return "friendSendToMe=" +
                    LList.getCount(friendSendToMe) + "_" + updateIHaveSendFriend + "," +
                    "iHaveSendFriend=" +
                    LList.getCount(iHaveSendFriend) + "_" + updateFriendSendToMe + "," +
                    "noneFriendStage=" +
                    LList.getCount(checkAllStage) + "_" + updateCheckAllStage + ",";
        }

        private int checkAllStage(long myUserId, int myUserRole, long startTime) {
            //好友状态是0，需要查询双聊
            int resultCount = 0;

            if (!LList.isEmpty(checkAllStage)) {

                int count = LList.getCount(checkAllStage);
                for (int i = 0; i < count; i += BATCH_QUERY_SIZE) {

                    if (myUserId != UserManager.getUID()) return 0;
                    if (myUserRole != UserManager.getUserRole().get()) return 0;

                    int endIndex = i + Math.min(count - i, BATCH_QUERY_SIZE);

                    L.d("STAGE_INDEX", "startIndex=" + i + "endIndex=" + endIndex);

                    List<ContactBean> handleList = LList.getSubList(checkAllStage, i, endIndex);
                    if (LList.isEmpty(handleList)) return 0;


                    //好友给我发送过
                    List<ContactBean> friendSendMsgToMe = MessageDaoFactory.getMessageDao()
                            .isFriendSendMsgToMe(myUserId, myUserRole, handleList);
                    for (ContactBean contactBean : friendSendMsgToMe) {
                        contactBean.setFriendStageNew(ContactBean.STAGE_FRIEND_SEND_TO_ME);
                    }

                    //我给好友发送过
                    List<ContactBean> isIHaveSendList = MessageDaoFactory.getMessageDao()
                            .isIHavaSendMsgToFriend(myUserId, myUserRole, handleList);
                    for (ContactBean contactBean : isIHaveSendList) {
                        contactBean.setFriendStageNew(ContactBean.STAGE_HAVE_SEND_TO_FRIEND);
                    }


                    L.d(TAG, "查询：checkAllStage=" + "耗时=" + (System.currentTimeMillis() - startTime));
                    List<ContactBean> allList = new ArrayList<>(friendSendMsgToMe);
                    for (ContactBean contactBean : isIHaveSendList) {
                        if (!allList.contains(contactBean)) {
                            allList.add(contactBean);
                        }
                    }

                    //批量更新的联系人
                    if (!LList.isEmpty(allList)) {
                        App.get().db().save(allList);
                    }

                    resultCount += LList.getCount(allList);
                    L.d(TAG, "更新：checkAllStage 数量=" + LList.getCount(allList));
                }

            }

            return resultCount;
        }

        private int iHaveSendCheckFriendSendStage(long myUserId, int myUserRole, long startTime) {
            //我已经给好友发送过消息,
            if (!LList.isEmpty(iHaveSendFriend)) {

                int count = LList.getCount(iHaveSendFriend);

                for (int i = 0; i < count; i += BATCH_QUERY_SIZE) {

                    if (myUserId != UserManager.getUID()) return 0;
                    if (myUserRole != UserManager.getUserRole().get()) return 0;

                    int endIndex = i + Math.min(count - i, BATCH_QUERY_SIZE);

                    L.d("STAGE_INDEX", "startIndex=" + i + "endIndex=" + endIndex);

                    List<ContactBean> handleList = LList.getSubList(iHaveSendFriend, i, endIndex);
                    if (LList.isEmpty(handleList)) return 0;

                    List<ContactBean> friendSendMsgToMe = MessageDaoFactory.getMessageDao()
                            .isFriendSendMsgToMe(myUserId, myUserRole, handleList);

                    List<ContactBean> updateList = new ArrayList<>();
                    for (ContactBean contactBean : friendSendMsgToMe) {
                        ContactBean cacheBean = ContactManager.getInstance().queryContactByFriendId(contactBean.friendId, contactBean.myRole, contactBean.friendSource);
                        if (cacheBean == null) continue;
                        if (!cacheBean.isFriendHaveSendMsgToMe()) {
                            cacheBean.setFriendStageNew(ContactBean.STAGE_FRIEND_SEND_TO_ME);
                            updateList.add(cacheBean);
                        }
                    }

                    L.d(TAG, "查询：iHaveSendCheckFriendSendStage，friendSendMsgToMe，返回数量=" + LList.getCount(friendSendMsgToMe) + "，耗时=" + (System.currentTimeMillis() - startTime));
                    //批量更新的联系人
                    if (!LList.isEmpty(updateList)) {
                        App.get().db().save(updateList);
                        L.d(TAG, "批量更新：iHaveSendFriend=" + "耗时=" + (System.currentTimeMillis() - startTime));
                    }

                    return LList.getCount(updateList);
                }

            }
            return 0;
        }

        private int friendHasSendCheckISendStage(long myUserId, int myUserRole, long startTime) {
            //好友给我发送过消息
            if (!LList.isEmpty(friendSendToMe)) {

                int count = LList.getCount(friendSendToMe);

                for (int i = 0; i < count; i += BATCH_QUERY_SIZE) {

                    if (myUserId != UserManager.getUID()) return 0;

                    if (myUserRole != UserManager.getUserRole().get()) return 0;

                    int endIndex = i + Math.min(count - i, BATCH_QUERY_SIZE);
                    L.d("STAGE_INDEX", "startIndex=" + i + "endIndex=" + endIndex);

                    List<ContactBean> handleList = LList.getSubList(friendSendToMe, i, endIndex);
                    if (LList.isEmpty(handleList)) return 0;


                    //更新双聊状态
                    List<ContactBean> isIHaveSendList = MessageDaoFactory.getMessageDao()
                            .isIHavaSendMsgToFriend(myUserId, myUserRole, handleList);

                    List<ContactBean> updateList = new ArrayList<>();
                    for (ContactBean contactBean : isIHaveSendList) {
                        ContactBean cacheBean = ContactManager.getInstance().queryContactByFriendId(contactBean.friendId, contactBean.myRole, contactBean.friendSource);
                        if (cacheBean == null) continue;
                        if (!cacheBean.isIHaveSendMsgToFriend()) {
                            cacheBean.setFriendStageNew(ContactBean.STAGE_HAVE_SEND_TO_FRIEND);
                            updateList.add(cacheBean);
                        }
                    }

                    L.d(TAG, "查询：friendHasSendCheckISendStage：isIHaveSendList =返回 " + LList.getCount(isIHaveSendList) + "，耗时=" + (System.currentTimeMillis() - startTime));

                    //批量更新的联系人
                    if (!LList.isEmpty(updateList)) {
                        App.get().db().save(updateList);
                        L.d(TAG, "批量更新：iHaveSendFriend=" + "耗时=" + (System.currentTimeMillis() - startTime));
                    }

                    return LList.getCount(updateList);
                }

            }
            return 0;
        }

        private void initGroupByFriendStageStatus() {

            for (ContactBean mData : contactBeans) {

                if (mData == null) continue;

                long myId = mData.myId;
                int myRole = mData.myRole;

                //系统id不需要处理
                if (mData.friendId < 1000) continue;

                //如果已经是双聊的 不需要处理
                if (mData.isContactEachOther()) continue;

                //推出登录
                if (UserManager.getUID() != myId) return;

                //用户切换了身份, 中断此次轮训
                if (UserManager.getUserRole().get() != myRole) return;

                if (mData.isIHaveSendMsgToFriend()) {
                    //【场景：我给好友发送过消息】检测下DB里面 好友是否给我发送过消息，如果是：返回双聊
                    iHaveSendFriend.add(mData);
                } else if (mData.isFriendHaveSendMsgToMe()) {
                    //【场景：好友给我发送过消息】检测下DB里面 我是否给好友发送过消息，如果是：返回双聊
                    friendSendToMe.add(mData);
                } else {
                    //【检测是否双聊】: fridendStage=0，但是有消息case,不一定双聊 备注：之前新招呼会出现我给对方发送消息的case
                    checkAllStage.add(mData);
                }
            }

            L.d(TAG, "分组完毕：iHaveSendFriend=" + LList.getCount(iHaveSendFriend) + "，friendSendToMe=" + LList.getCount(friendSendToMe) + "，checkAllStage=" + LList.getCount(checkAllStage));
        }
    }

    ///////////////////////////////////////////////////////////////////////////
    // 和SP记录数据有关
    ///////////////////////////////////////////////////////////////////////////

    private static final String SP_REFRESH_KEY = "FriendStageManager_SP_REFRESH_KEY";

    private boolean checkTodayHasRefresh() {
        long time = SpManager.get().user().getLong(SP_REFRESH_KEY, 0);
        return DateUtil.isTodayWithTime(time);
    }

    private void saveTodayRefreshTime() {
        SpManager.get().user().edit().putLong(SP_REFRESH_KEY, System.currentTimeMillis()).apply();
    }


    ///////////////////////////////////////////////////////////////////////////
    // 上报埋点
    ///////////////////////////////////////////////////////////////////////////

    private void apmReport(long time, String info, String error) {
        ApmAnalyzer.create()
                .action(ACTION_LAST_CHAT_TEXT, "updateFriendStage")
                .param("p3", String.valueOf(time))
                .param("p4", info)
                .param("p5", error)
                .param("p6", (time>50000)?"1":"0")
                .report();
    }


    public void resetRunning() {
        isRunnable = false;
        L.d(TAG, "resetRunning");
    }

}