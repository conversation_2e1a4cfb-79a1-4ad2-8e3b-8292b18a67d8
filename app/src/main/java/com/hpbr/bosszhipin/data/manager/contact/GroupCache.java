package com.hpbr.bosszhipin.data.manager.contact;

import android.text.TextUtils;

import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Created by wang<PERSON> on 16/11/2.
 */

public class GroupCache {
    private static final String TAG = "GroupCache";
    private static final GroupCache mInstance = new GroupCache();
    private Map<Long, GroupInfoBean> dataMaps = new HashMap<>();
    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private boolean hasLoad = false;

    private GroupCache() {

    }

    public void clean() {
        try {
            L.debug(TAG, "===========clean====:%s", UserManager.getUserRole());
            lock.writeLock().lock();
            hasLoad = false;
            dataMaps.clear();
        } catch (Exception e) {
            L.e(TAG, "clean 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public static GroupCache getmInstance() {
        return mInstance;
    }

    public void load() {
        L.debug(TAG, "======GroupCache=====load");

        if (TextUtils.isEmpty(UserManager.getToken())) {
            return;
        }

        if (hasLoad) {
            L.debug(TAG, "====GroupCache=======has load=====%s", UserManager.getUserRole());
            return;
        }

        try {
            lock.writeLock().lock();
            dataMaps.clear();
            List<GroupInfoBean> mDatas = GroupManager.getInstance().getGroupInfoListFromDb();
            if (mDatas != null) {
                int size = mDatas.size();
                for (int i = 0; i < size; i++) {
                    GroupInfoBean bean = mDatas.get(i);
                    dataMaps.put(bean.groupId, bean);
                }
                L.debug(TAG, "==GroupCache==load result=======mData:%d", mDatas.size());
            } else {
                L.debug(TAG, "==GroupCache==load result=======mData:%d", 0);
            }
            hasLoad = true;
        } catch (Exception e) {
            L.e(TAG, "load 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    private void checkInitLoad() {
        try {
            lock.readLock().lock();
            if (hasLoad) return;
        } finally {
            lock.readLock().unlock();
        }
        load();
    }

    public GroupInfoBean getGroupInfoByGroupid(long groupId) {
        checkInitLoad();
        GroupInfoBean bean = null;
        try {
            lock.readLock().lock();
            bean = dataMaps.get(groupId);
            return bean;
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return bean;
    }

    public List<GroupInfoBean> getGroupInfoList() {
        checkInitLoad();
        List<GroupInfoBean> list = new ArrayList<>();
        try {
            lock.readLock().lock();
            for (Map.Entry<Long, GroupInfoBean> entry : dataMaps.entrySet()) {
                list.add(entry.getValue());
            }
            return list;
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoListContainQuit 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return list;
    }

    public void deleteGroup(long groupId) {
        checkInitLoad();
        try {
            lock.readLock().lock();
            dataMaps.remove(groupId);
        } catch (Exception e) {
            L.e(TAG, "remove 异常", e);
        } finally {
            lock.readLock().unlock();
        }
    }


    /***
     * 不保留就数据
     **/
    public long insertOrUpdateAllFiled(GroupInfoBean bean) {
        if (bean == null) {
            return -1;
        }
        checkInitLoad();
        L.debug(TAG, "===before=insert======");
//        printMap();
        try {
            lock.writeLock().lock();
            if (dataMaps.get(bean.groupId) != null) {
                dataMaps.get(bean.groupId).copyAllFiled(bean);
            } else {
                if (TextUtils.isEmpty(bean.name)) {
                    bean.name = "未知群";
                }
                dataMaps.put(bean.groupId, bean);
            }
            return 1;
        } catch (Exception e) {
            L.e(TAG, "insert 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
        return -1;
    }

    /***
     * 保留部分旧数据
     **/
    public GroupInfoBean insertOrUpdateServerField(GroupInfoBean bean) {
        if (bean == null) {
            return null;
        }
        checkInitLoad();
        L.debug(TAG, "===before=insertOrUpdateServerField=======");
        try {
            lock.writeLock().lock();
            GroupInfoBean cache = dataMaps.get(bean.groupId);
            if (cache != null) {
                cache.copyServerFiled(bean);
                return cache;
            } else {
                if (TextUtils.isEmpty(bean.name)) {
                    bean.name = "未知群";
                }
                dataMaps.put(bean.groupId, bean);
                return bean;
            }
        } catch (Exception e) {
            L.e(TAG, "insertOrUpdateServerField 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
        return null;
    }

    public long updateX(long groupId, String value) {
        return 1;
    }

    public void deleteGroupInfo(long groupId) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            dataMaps.remove(groupId);
        } catch (Exception e) {
            L.e(TAG, "deleteGroupInfo 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    public GroupInfoBean updateGroupNoneReadCount(long groupId, int noneReadCount) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            GroupInfoBean cache = dataMaps.get(groupId);
            if (cache != null) {
                cache.noneReadCount = noneReadCount;
                return cache;
            }
        } catch (Exception e) {
            TLog.error(TAG, "updateGroupReadInfo 异常: %s", e);
        } finally {
            lock.writeLock().unlock();
        }
        return null;
    }

    public GroupInfoBean updateGroupLastChatStatus(long groupId, int lastChatStatus) {
        checkInitLoad();
        try {
            lock.writeLock().lock();
            GroupInfoBean cache = dataMaps.get(groupId);
            if (cache != null) {
                cache.lastChatStatus = lastChatStatus;
                return cache;
            }
        } catch (Exception e) {
            TLog.error(TAG, "updateGroupReadInfo 异常: %s", e);
        } finally {
            lock.writeLock().unlock();
        }
        return null;
    }
}
