package com.hpbr.bosszhipin.data.manager.contact;

import com.hpbr.bosszhipin.data.db.entry.GroupMemberBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.L;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Created by wang<PERSON> on 16/11/2.
 */

public class GroupMemberCache {
    private static final String TAG = "GroupMemberCache";
    private static final GroupMemberCache mInstance = new GroupMemberCache();
    private Map<Long, Map<String, GroupMemberBean>> memberListMaps = new HashMap<>();

    private Map<String, GroupMemberBean> quitMemberListMaps = new HashMap<>();


    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    private GroupMemberCache() {

    }

    public void clean(long groupId) {
        try {
            lock.writeLock().lock();
            memberListMaps.remove(groupId);
        } catch (Exception e) {
            L.e(TAG, "clean 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }


    public void put(long groupId, List<GroupMemberBean> list) {
        try {
            lock.writeLock().lock();
            if (list == null) {
                memberListMaps.remove(groupId);
                return;
            }
            Map<String, GroupMemberBean> map = new HashMap<>();
            for (GroupMemberBean bean : list) {
                map.put(groupId + "_" + bean.userId, bean);
            }
            memberListMaps.put(groupId, map);
        } catch (Exception e) {
            L.e(TAG, "clean 异常", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取群成员列表
     **/
    public List<GroupMemberBean> getGroupMembers(long groupId) {
        List<GroupMemberBean> list = new ArrayList<>();
        try {
            lock.readLock().lock();
            Map<String, GroupMemberBean> map = memberListMaps.get(groupId);
            if (map != null) {
                list = new ArrayList<>();
                for (Map.Entry<String, GroupMemberBean> entry : map.entrySet()) {
                    list.add(entry.getValue());
                }
            }
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return list;
    }

    public GroupMemberBean deleMember(long groupId, long userId) {
        try {
            lock.readLock().lock();
            Map<String, GroupMemberBean> map = memberListMaps.get(groupId);
            if (map != null) {
               return map.remove(groupId + "_" + userId);
            }
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return null;
    }

    public void addMember(GroupMemberBean bean){
        try {
            lock.readLock().lock();
            Map<String, GroupMemberBean> map = memberListMaps.get(bean.groupId);
            if (map != null) {
                map.put(bean.groupId + "_" + bean.userId,bean);
            }
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
    }

    public void deleMembers(long groupId) {
        try {
            lock.readLock().lock();
            memberListMaps.remove(groupId);
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
    }


    /**
     * 获取群成员信息
     **/
    public GroupMemberBean getGroupMember(long groupId, long memberId) {
        GroupMemberBean bean = null;
        try {
            lock.readLock().lock();
            Map<String, GroupMemberBean> map = memberListMaps.get(groupId);
            if (map != null) {
                bean = map.get(groupId + "_" + memberId);
            }
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return bean;
    }


    public GroupMemberBean getQuitGroupMemberBean(long groupId, long memberId) {
        GroupMemberBean bean = null;
        try {
            lock.readLock().lock();
            bean = quitMemberListMaps.get(groupId + "_" + memberId);
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
        return bean;
    }

    public void putQuitGroupMemberBean(GroupMemberBean groupMemberBean) {
        GroupMemberBean bean = null;
        try {
            lock.readLock().lock();
            quitMemberListMaps.put(groupMemberBean.groupId +"_"+ groupMemberBean.userId,groupMemberBean);
        } catch (Exception e) {
            L.e(TAG, "getGroupInfoByGroupid 异常", e);
        } finally {
            lock.readLock().unlock();
        }
    }

    public static GroupMemberCache getmInstance() {
        return mInstance;
    }
}
