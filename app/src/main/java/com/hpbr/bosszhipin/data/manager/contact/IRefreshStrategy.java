package com.hpbr.bosszhipin.data.manager.contact;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Created by wa<PERSON><PERSON> on 16/11/2.
 */

public abstract class IRefreshStrategy {
    AtomicBoolean isRefresing = new AtomicBoolean(false);

    /**
     * 是否需要刷新
     * **/
    public abstract boolean isNeedRefresh();

    public abstract void doRefresh();


    public boolean isRefreshing() {
        return isRefresing.get();
    }

    public abstract void initRefreshTime();

    public void resetRefreshing() {
        isRefresing.set(false);
    }
}
