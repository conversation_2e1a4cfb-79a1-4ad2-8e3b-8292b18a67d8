package com.hpbr.bosszhipin.data.manager.contact;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;

import com.monch.lbase.util.LList;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Keep
class MonitorData implements Serializable {

    private static final long serialVersionUID = 3761987415282673795L;
    private int page;

    private final long startTime = System.currentTimeMillis();
    private long requestFriendListTime;
    private long handleDataTime;
    private long zpFriendCount;
    private long dzFriendCount;

    private long zpAddCount;
    private long dzAddCount;

    private long delCount;
    private long endTime;
    private int retryCount;

    private long count;
    private long msgTime;

    private final List<Long> monitorHttpTimes = new ArrayList<>();
    private final List<MonitorTime> monitorTimes = new ArrayList<>();

    public MonitorData() {
    }

    void setBaseData(@Nullable ContactPageData contactPageData) {
        if (contactPageData != null) {
            this.zpFriendCount = LList.getCount(contactPageData.zpFriendIdList);
            this.dzFriendCount = LList.getCount(contactPageData.dzFriendIdList);
            this.delCount = LList.getCount(contactPageData.delFriends);
        }
    }

    void addPrePageTime(long requestTime, long handleDataTime) {
        this.requestFriendListTime = requestTime;
        this.handleDataTime = handleDataTime;
    }

    void addPageTime(MonitorTime monitorTime) {
        this.count += monitorTime.count;
        this.msgTime += monitorTime.time;
        this.monitorTimes.add(monitorTime);
    }

    public void setDzAddCount(long dzAddCount) {
        this.dzAddCount = dzAddCount;
    }

    public void setZpAddCount(long zpAddCount) {
        this.zpAddCount = zpAddCount;
    }

    public long getCount() {
        return count;
    }

    public long getZpFriendCount() {
        return zpFriendCount;
    }

    public long getDzFriendCount() {
        return dzFriendCount;
    }

    public long getZpAddCount() {
        return zpAddCount;
    }

    public long getDzAddCount() {
        return dzAddCount;
    }

    public long getDelCount() {
        return delCount;
    }

    long getMsgTime() {
        return msgTime;
    }

    long getTotalTime() {
        return endTime - startTime;
    }

    void stop() {
        this.endTime = System.currentTimeMillis();
    }

    void success(long httpTime) {
        page++;
        this.monitorHttpTimes.add(httpTime);
    }

    void retry() {
        retryCount++;
    }

    @Keep
    public static class MonitorTime implements Serializable {
        private static final long serialVersionUID = 2559697020989500776L;
        public long time;
        public long count;

        public MonitorTime(long time, int count) {
            this.time = time;
            this.count = count;
        }
    }
}