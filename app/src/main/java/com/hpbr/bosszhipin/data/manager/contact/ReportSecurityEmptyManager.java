package com.hpbr.bosszhipin.data.manager.contact;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.List;

//APP启动时候 查询/上报 securityId为空的联系人,限制下评率
public class ReportSecurityEmptyManager {

    private static final String TAG = "ReportSecurityEmptyMana";

    //用于记录打印Tlog日志信息
    private final StringBuilder toLogInfo = new StringBuilder();


    //当前账号securityId为空的联系人数量
    private int emptySecurityContactCount;

    //最多上报的联系人数量，防止数据太多
    private static final int maxReport = 100;

    //降频处理，一次APP使用过程只上报一次
    private static  boolean hasReport=false;

    public void reportEmptySecurityContact() {

        if (hasReport) return;

        final StringBuilder reportInfo=new StringBuilder();

        //获得全部的单聊联系人，包含 不合适的
        List<ContactBean> singleContact = F2ContactHelper.getInstance().getSingleContact(true);
        for (ContactBean contactBean : singleContact) {
            if (contactBean == null) continue;
            if (LText.empty(contactBean.securityId)) {

                toLogInfo.append(contactBean.friendId);
                toLogInfo.append("_");
                toLogInfo.append(contactBean.friendSource);
                toLogInfo.append(",");
                emptySecurityContactCount++;

                if (emptySecurityContactCount <= maxReport) {
                    reportInfo.append(contactBean.friendId);
                    reportInfo.append("_");
                    reportInfo.append(contactBean.friendSource);
                    reportInfo.append(",");
                }

            }
        }


        if (emptySecurityContactCount > 0) {
            hasReport=true;
            //日志打印出全部联系人为空的securityIds
            TLog.info(TAG, "empty security contact info %s ", toLogInfo.toString());
            //上报securityId为空
            ApmAnalyzer.create().action("action_contact", "emptySecurity")
                    .p2(String.valueOf(emptySecurityContactCount))
                    .p3(reportInfo.toString())
                    .debug()
                    .report();
        }

    }

}
