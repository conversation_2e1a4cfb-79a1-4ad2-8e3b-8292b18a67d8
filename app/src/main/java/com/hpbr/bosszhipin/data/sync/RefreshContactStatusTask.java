package com.hpbr.bosszhipin.data.sync;

import android.content.Intent;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.manager.ChatMessageFactory;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.L;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ApiCallbackSafeCallback;
import net.bosszhipin.api.ContactFullInfoRequest;
import net.bosszhipin.api.ContactFullInfoResponse;

import message.handler.dao.MessageDaoFactory;

/**
 * Created by monch on 2016/11/21.
 * 刷新单个联系人的状态
 */

public class RefreshContactStatusTask implements Runnable {

    private static final String TAG = "RefreshContactInfoTask";

    private long uid;
    private int role;
    private int friendSource;

    public RefreshContactStatusTask(long uid, int role, int friendSource) {
        this.uid = uid;
        this.role = role;
        this.friendSource = friendSource;
    }

    @Override
    public void run() {
        final int myRole = UserManager.getUserRole().get();
        if (myRole == role) {
            L.d(TAG, "接收到与现在信息不符的数据同步消息，不允许继续执行");
            return;
        }
        ContactManager manager = ContactManager.getInstance();
        final ContactBean contactData = manager.queryContactByFriendId(uid, myRole, friendSource);
        if (contactData == null) {
            L.d(TAG, "本地当前无此好友，不允许继续执行");
            return;
        }

        ContactFullInfoRequest request = new ContactFullInfoRequest(new ApiCallbackSafeCallback<ContactFullInfoResponse>() {

            @Override
            public void handleInChildThread(ApiData<ContactFullInfoResponse> data) {
                super.handleInChildThread(data);
                if (data.resp != null && data.resp.fullInfo != null) {
                    //好友关系 不存在
                    if (data.resp.fullInfo.getFriendId() <= 0) {
                        finishChatActivity(uid, friendSource);
                        ContactManager.getInstance().deleteContact(contactData);
                        MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), uid, friendSource);
                        return;
                    }
                    //好友关系 不存在 只处理这里 实时删除联系人
                    if (data.resp.fullInfo.getAddTime() < data.resp.fullInfo.getDelTime()) {
                        finishChatActivity(uid, friendSource);
                        ContactManager.getInstance().deleteContact(contactData);
                        MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), uid, friendSource);
                        return;
                    }
                    // addTime >= delTime  需要删除之前的消息 只处理这里
                    if (data.resp.fullInfo.getAddTime() > 0 && data.resp.fullInfo.getAddTime() >= data.resp.fullInfo.getDelTime()) {
                        MessageDaoFactory.getMessageDao().removeChatList(UserManager.getUID(), UserManager.getUserRole().get(), uid, data.resp.fullInfo.getFriendSource(), data.resp.fullInfo.getDelTime());
                    }
                    if (ContactManager.getInstance().queryContactByFriendId(uid, myRole, friendSource) == null) {
                        ApmAnalyzer.create().action(ContactDoctorFactory.TAG, "deleteContact").p2(String.valueOf(uid)).report();
                    }
                    contactData.fromServerContactFullInfoBean(data.resp.fullInfo, data.resp.userId, data.resp.identity);
                    ContactManager.getInstance().insertOrUpdateServerField(contactData, myRole);
                }
            }

            @Override
            public void onSuccess(ApiData<ContactFullInfoResponse> data) {

            }

            @Override
            public void onComplete() {
                ChatMessageFactory.getInstance().createContactTransfer().notifyObservers();
                ContactManager.getInstance().refreshContact(uid);
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.friendSource = contactData.friendSource;
        request.securityId = contactData.securityId;
        request.friendId = String.valueOf(uid);
        HttpExecutor.execute(request);
    }

    private void finishChatActivity(long friendId, int friendSource) {
        Utils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Intent intent = new Intent(ContactManager.ACTION_DELETE_CONTACT);
                intent.putExtra(Constants.DATA_LONG, friendId);
                intent.putExtra(Constants.DATA_INT, friendSource);
                ReceiverUtils.sendBroadcastSystem(Utils.getApp(), intent);
            }
        });
        //在聊天页面直接关闭
//        Activity topActivity = ForegroundUtils.get().getTopActivity();
//        if (topActivity != null) {
//            if (SingleRouter.checkIsChatActivityBossUser(topActivity)) {

//                Intent intent = topActivity.getIntent();
//                if (intent != null) {
//                    long currentChatWith = intent.getLongExtra(SingleConstant.CHAT_LONG_FRIEND_ID, 0);
//                    if (currentChatWith == friendId) {
//                        AppUtil.finishActivity(topActivity);
//                        try {
//                            Thread.sleep(200);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                        }
//                    }
//                }
//            }
//        }
    }

}
