package com.hpbr.bosszhipin.data.sync;

import android.content.Intent;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.BossInteractTabChangedUtils;
import com.hpbr.bosszhipin.common.JobChangedCheckUtils;
import com.hpbr.bosszhipin.common.JobExpectListCheckUtils;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.module.login.util.UserInfoUtil;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;

/**
 * Created by monch on 2016/11/16.
 * 刷新用户信息的任务
 */
public class RefreshUserInfoTask implements Runnable {
    private static final String TAG = "chat";

    private long uid;
    private int role;

    private boolean isCheckSecurityFramework = true;

    public RefreshUserInfoTask setCheckSecurityFramework(boolean isCheckSecurityFramework){
        this.isCheckSecurityFramework = isCheckSecurityFramework;
        return this;
    }

    public RefreshUserInfoTask(long uid, int role) {
        this.uid = uid;
        this.role = role;
    }
    UserInfoUtil.OnGetUserInfoCallback callback;
    public RefreshUserInfoTask setCallback(UserInfoUtil.OnGetUserInfoCallback callback){
        this.callback = callback;
        return this;
    }

    @Override
    public void run() {
        if (uid != UserManager.getUID() || role != UserManager.getUserRole().get()) {
            L.d(TAG, "接收到与现在信息不符的数据同步消息，不允许继续执行");
            return;
        }
        int oldVipStatus = UserManager.getBossVipStatus();

        UserInfoUtil userInfoUtil = new UserInfoUtil();
        userInfoUtil.setCheckSecurityFramework(isCheckSecurityFramework);
        userInfoUtil.setOnGetUserInfoCallback(new UserInfoUtil.OnGetUserInfoCallback() {
            @Override
            public void onGetUserInfoCompleteCallback() {
                L.d(TAG, "用户信息刷新完成");
                if(callback != null){
                    callback.onGetUserInfoCompleteCallback();
                }
            }

            @Override
            public void onGetUserInfoCallback(boolean success, String errorMsg) {
                L.d(TAG, "用户信息刷新" + (success ? "完成" : "失败：" + errorMsg));
                if (role == ROLE.BOSS.get()) {
                    Intent intent = new Intent();
                    intent.setAction(Constants.REFRESH_F3_LOCAL_DATA_ACTION);
                    intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                    App.getAppContext().sendBroadcast(intent);
                    JobChangedCheckUtils.sendJobListChangeBroadcast();

                    sendJobSortReceiver(); // 发送职位排序成功的广播
                    int newVipStatus = UserManager.getBossVipStatus();
                    boolean isShowF2ISeeEntrance = oldVipStatus != newVipStatus;
                    BossInteractTabChangedUtils.sendBroadcast(isShowF2ISeeEntrance);
                    // 1303.111 职位数变化会影响联系人列表展示
                    if (DataStarGray.getInstance().getContactListCardOptimize() != 0) {
                        ContactManager.getInstance().refreshContacts();
                    }
                } else {
                    Intent intent = new Intent();
                    intent.setAction(Constants.REFRESH_F3_LOCAL_DATA_ACTION);
                    intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                    App.getAppContext().sendBroadcast(intent);
                    TLog.info(GeekF1Constant.SEND_EXPECT_LIST_CHANGE_BROADCAST,"RefreshUserInfoTask onGetUserInfoCallback");
                    JobExpectListCheckUtils.sendExpectListChangeBroadcast();
                }
                if(callback != null){
                    callback.onGetUserInfoCallback(success,errorMsg);
                }
            }

            private void sendJobSortReceiver() {
                Intent intent = new Intent(Constants.RECEIVER_JOB_SORT_ACTION);
                ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
            }
        });
        userInfoUtil.requestUserInfo();
    }
}
