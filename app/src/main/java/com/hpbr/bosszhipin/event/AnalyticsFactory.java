package com.hpbr.bosszhipin.event;


import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.event.reporter.AnalyticsMemoryMergeReporter;
import com.hpbr.bosszhipin.event.reporter.AnalyticsMemorySimpleMergeReporter;
import com.hpbr.bosszhipin.event.reporter.AnalyticsMergeReporter;
import com.hpbr.bosszhipin.event.reporter.DefaultAnalyticsReporter;
import com.hpbr.bosszhipin.event.reporter.IAnalyticsReporter;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.bean.AnalyticsStrategyBean;
import com.monch.lbase.LBase;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


/**
 * Created by wang<PERSON> on 2018/11/12.
 */
public class AnalyticsFactory {


    private String action;
    private Map<String, String> params;


    private AnalyticsFactory() {
    }

    public static AnalyticsFactory create() {
        return new AnalyticsFactory();
    }

    public AnalyticsFactory action(String action) {
        this.action = action;
        return this;
    }

    public AnalyticsFactory secId(String securityId) {
        if (!TextUtils.isEmpty(securityId)) {
            if (params == null) {
                params = new HashMap<>();
            }
            params.put("secid", securityId);
        }
        return this;
    }


    public AnalyticsFactory param(String key, int value) {
        return param(key, String.valueOf(value));
    }

    public AnalyticsFactory param(String key, long value) {
        return param(key, String.valueOf(value));
    }


    public AnalyticsFactory param(String key, String value) {
        if (params == null) {
            params = new HashMap<>();
        }
        if (value == null) {
            value = "";
        }
        params.put(key, value);
        return this;
    }

    /**
     * 针对可选参数进行处理
     */
    public AnalyticsFactory paramIfExist(String key, String value, boolean needAdd) {
        if (!needAdd) {
            return this;
        }
        return param(key, value);
    }

    public AnalyticsFactory param(Map<String, String> map) {
        if (map == null || map.isEmpty()) return this;
        if (params == null) {
            params = new HashMap<>();
        }
        params.putAll(map);
        return this;
    }

    public AnalyticsFactory debug() {
        if (LBase.getBuildConfig().DEBUG) {
            StringBuilder sb = new StringBuilder();
            if (!TextUtils.isEmpty(action)) {
                sb.append(" action: ");
                sb.append(action);
                sb.append(" => ");
            }
            if (null != params) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    if (null == entry) continue;
                    sb.append(entry.getKey());
                    sb.append(" = ");
                    sb.append(entry.getValue());
                    sb.append(" ");
                }
            }
            TLog.debug("AnalyticsFactory", "Logger: %s ", sb.toString());
        }
        return this;
    }

    public void build() {
        addEventAction(action, params);
    }

    public void buildSync() {
        addEventActionSync(action, params);
    }

    /***
     *
     * 打点ba
     * 来源：
     * 1、阻断页面的按钮协议
     * 2、阻断页面的阻断数据
     * 3、其他直聘协议
     * **/
    public static void bgAction(String ba) {
        bgAction(ba, null);
    }

    public static void bgAction(String ba, Map map) {
        if (TextUtils.isEmpty(ba)) return;
        try {
            JSONObject joBgAction = getDecodedJsonObject(ba);
            if (joBgAction != null) {
                Iterator<String> it = joBgAction.keys();
                Map<String, String> bgActionParams = new HashMap<>();
                String action = "";
                while (it.hasNext()) {
                    String key = it.next();
                    String value = joBgAction.opt(key).toString();
                    if (TextUtils.equals(key, "action")) {
                        action = value;
                        continue;
                    }
                    bgActionParams.put(key, value);
                }
                if (map != null) {
                    bgActionParams.putAll(map);
                }
                AnalyticsFactory.create().action(action)
                        .param(bgActionParams)
                        .build();
            }
        } catch (Exception e) {

        }
    }

    public static void bgActionUnDecode(String actionParams) {
        //不需要decode
        if (!LText.empty(actionParams)) {
            try {
                JSONObject jsonObject = new JSONObject(actionParams);
                Iterator<String> it = jsonObject.keys();
                AnalyticsFactory factory = AnalyticsFactory.create();
                Map<String, String> params = new HashMap<>();
                while (it.hasNext()) {
                    String key = it.next();
                    String value = jsonObject.opt(key).toString();
                    if (TextUtils.equals(key, "action")) {
                        factory.action(value);
                    } else {
                        params.put(key, value);
                    }
                }
                if (!params.isEmpty()) {
                    factory.param(params);
                }
                factory.build();
            } catch (JSONException e) {
                e.printStackTrace();
            }

        }
    }


    /**
     * 解码
     *
     * @param string
     * @return
     */
    public static JSONObject getDecodedJsonObject(String string) {
        JSONObject jsonObject = null;
        if (!TextUtils.isEmpty(string)) {
            try {
                String s = URLDecoder.decode(string, "UTF-8");
                jsonObject = new JSONObject(s);
            } catch (JSONException e) {
                e.printStackTrace();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return jsonObject;
    }


    /**
     * @param action 事件action
     * @param params 事件的参数
     */
    private static void addEventAction(@NonNull final String action, final Map<String, String> params) {
        addEventActionSync(action, params);
    }

    private synchronized static void addEventActionSync(@NonNull final String action, final Map<String, String> params) {
        com.bszp.kernel.logic.db.entitiy.AnalyticsBean bean = new com.bszp.kernel.logic.db.entitiy.AnalyticsBean();
        bean.action = action;
        bean.params = params;
        bean.time = System.currentTimeMillis();
        reporter.commitAction(bean);
    }

    public static void commitAction() {
        reporter.commitActionNow();
    }

    private static IAnalyticsReporter reporter = initReporter();

    private static IAnalyticsReporter initReporter() {
        AnalyticsStrategyBean strategy = AndroidDataStarGray.getInstance().getAnalyticsStrategy();
        int reporterType = strategy == null ? AnalyticsStrategyBean.REPORTER_DEFAULT : strategy.reporterType;
        int analyticsMergeInterval = strategy == null ? AnalyticsStrategyBean.INTERVAL_DEFAULT : strategy.interval;
        int analyticsCapacity = strategy == null ? AnalyticsStrategyBean.CAPACITY_DEFAULT : strategy.capacity;
        int reportThresholdMin = strategy == null ? AnalyticsStrategyBean.REPORT_THRESHOLD_PERCENTAGE_MIN_DEFAULT : strategy.reportThresholdMin;
        int reportThresholdMax = strategy == null ? AnalyticsStrategyBean.REPORT_THRESHOLD_PERCENTAGE_MAX_DEFAULT : strategy.reportThresholdMax;

        TLog.info("AnalyticsMergeReporter", "reporterType = %d, analyticsMergeInterval =%d, analyticsCapacity =%d, reportThresholdMin =%d, reportThresholdMax =%d",
                reporterType, analyticsMergeInterval, analyticsCapacity, reportThresholdMin, reportThresholdMax);

        if (reporterType == AnalyticsStrategyBean.REPORTER_MERGE) {
            return new AnalyticsMergeReporter(analyticsMergeInterval);
        } else if (reporterType == AnalyticsStrategyBean.REPORTER_MERGE_MEMORY) {
            return new AnalyticsMemoryMergeReporter(analyticsMergeInterval, analyticsCapacity, reportThresholdMin, reportThresholdMax);
        } else if (reporterType == AnalyticsStrategyBean.REPORTER_MERGE_MEMORY_SIMPLE) {
            return new AnalyticsMemorySimpleMergeReporter(analyticsMergeInterval);
        }
        return new DefaultAnalyticsReporter();
    }

}
