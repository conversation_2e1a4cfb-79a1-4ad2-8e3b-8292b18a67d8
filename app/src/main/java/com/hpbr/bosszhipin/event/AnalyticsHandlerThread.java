package com.hpbr.bosszhipin.event;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.bszp.kernel.logic.db.AppDatabase;
import com.bszp.kernel.logic.db.dao.ListAnalyticsDao;
import com.bszp.kernel.logic.db.entitiy.ListAnalyticsBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.data.db.async.DbOpParam;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class AnalyticsHandlerThread extends HandlerThread {

    private static AnalyticsHandlerThread mInstance = new AnalyticsHandlerThread("analytics_db_op_handlerthread");
    private final ListAnalyticsDao analyticsDao;
    private HandlerThread mHandlerThread = null;
    private ConnectHandler mHandler = null;

    public static AnalyticsHandlerThread getInstance() {
        return mInstance;
    }

    private AnalyticsHandlerThread(String name) {
        super(name);
        analyticsDao = AppDatabase.get().getListAnalyticsDao();
        mHandlerThread = new HandlerThread(name);
        mHandlerThread.start();
        mHandler = new ConnectHandler(mHandlerThread.getLooper());
    }

    public Message obtainMessage(int what) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        return message;
    }

    public Message obtainMessage(int what, Object object) {
        Message message = mHandler.obtainMessage();
        message.what = what;
        message.obj = object;
        return message;
    }

    public ConnectHandler getHandler() {
        return mHandler;
    }

    public void sendMessage(Message msg) {
        mHandler.sendMessage(msg);
    }

    class ConnectHandler extends Handler {
        ConnectHandler(Looper looper) {
            super(looper);
        }

        int throwCount = 0;
        boolean isReport = false;

        @Override
        public void dispatchMessage(@NonNull Message msg) {
            try {
                super.dispatchMessage(msg);
            } catch (Throwable e) {
                throwCount++;
                TLog.error("AnalyticsHandlerThread", e, "dispatchMessage msg = %s", msg);
                if (throwCount > 5 && !isReport) {
                    isReport = true;
                    ApmAnalyzer.create().action("action_error", "AnalyticsHandler").p3(Log.getStackTraceString(e)).debug().report();
                }
            }
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT:
                    if (msg.obj != null) {
                        DbOpParam<List<ListAnalyticsBean>, Void> param = (DbOpParam<List<ListAnalyticsBean>, Void>) msg.obj;
                        analyticsDao.inserts(param.getObject());
                    }

                    break;
                case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE:
                    if (msg.obj != null) {
                        DbOpParam<List<ListAnalyticsBean>, Void> param = (DbOpParam<List<ListAnalyticsBean>, Void>) msg.obj;
                        List<ListAnalyticsBean> newList = param.getObject();
                        deleteListAnalyticsEntityByKeys(getKeys(newList));
                        analyticsDao.inserts(newList);
                        
                    }
                    break;
                case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE:
                    if (msg.obj != null) {
                        DbOpParam<List<ListAnalyticsBean>, Void> param = (DbOpParam<List<ListAnalyticsBean>, Void>) msg.obj;
                        List<ListAnalyticsBean> newList = param.getObject();
                        List<ListAnalyticsBean> oldList = queryByKeys(getKeys(newList));
                        Map<String, ListAnalyticsBean> oldMap = null;
                        if (oldList != null) {
                            oldMap = new HashMap<>();
                            for (int i = 0; i < oldList.size(); i++) {
                                oldMap.put(oldList.get(i).key, oldList.get(i));
                            }
                        }

                        if (oldMap != null) {
                            for (int i = 0; i < newList.size(); i++) {
                                if (oldMap.containsKey(newList.get(i).key)) {
                                    newList.get(i).merge(oldMap.get(newList.get(i).key));
                                }
                            }
                        }
                        deleteListAnalyticsEntityByKeys(getKeys(newList));
                        analyticsDao.inserts(newList);
                        if (param.getCallback() != null) {
                            param.getCallback().success(null);
                        }
                    }
                    break;
                case AnalyticsHandlerThread.OP_LIST_ANALYTICS_QUERY_60:
                    if (msg.obj != null) {
                        List<ListAnalyticsBean> limitListAnalytics = analyticsDao.getLimitAnalytics(60);
                        DbOpParam<Void, List<ListAnalyticsBean>> param = (DbOpParam<Void, List<ListAnalyticsBean>>) msg.obj;
                        param.getCallback().success(limitListAnalytics);
                    }
                    break;
                case AnalyticsHandlerThread.OP_LIST_ANALYTICS_QUERY_COUNT:
                    if (msg.obj != null) {
                        DbOpParam<Void, Long> param = (DbOpParam<Void, Long>) msg.obj;
                        try {
                            param.getCallback().success(analyticsDao.getCount());
                        } catch (Throwable e) {
                            TLog.error("AnalyticsHandlerThread",e,"OP_LIST_ANALYTICS_QUERY_COUNT");
                        }
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private int deleteListAnalyticsEntityByKeys(Set<String> keys) {
        if (keys == null || keys.size() == 0) {
            return -1;
        }
        return analyticsDao.deleteKeys(new ArrayList<>(keys));
    }

    private Set<String> getKeys(List<ListAnalyticsBean> lists) {
        Set<String> set = null;
        int count = lists.size();
        if (count > 0) {
            set = new HashSet<>();
        }
        for (int i = 0; i < count; i++) {
            set.add(lists.get(i).key);
        }
        return set;
    }

    private List<ListAnalyticsBean> queryByKeys(Set<String> keys) {
        if (keys == null || keys.size() == 0) {
            return null;
        }
        return analyticsDao.queryKeys(new ArrayList<>(keys));
    }


    public String setToString(Set<String> set) {
        Iterator<String> it = set.iterator();
        if (!it.hasNext())
            return "()";

        StringBuilder sb = new StringBuilder();
        sb.append('(');
        for (; ; ) {
            String e = it.next();
            sb.append("'");
            sb.append(e);
            sb.append("'");
            if (!it.hasNext())
                return sb.append(')').toString();
            sb.append(',');
        }
    }



    /**
     *
     * =========================列表统计表====================
     * ***/
    /**
     * 直接插入列表统计数据
     * **/
    public static final int OP_LIST_ANALYTICS_INSERT = 10101;

    /**
     * 插入去重后的列表统计数据，不更新数据
     * **/

    public static final  int OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE = 10102;

    /**
     * 插入去重后的列表统计数据，更新数据
     * **/
    public static final int OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE = 10103;


    /**
     * 查询列表统计数据
     * **/
    public static final int OP_LIST_ANALYTICS_QUERY_60 = 10401;

    /**
     * 查询列表统计数据总量
     * **/
    public static final int OP_LIST_ANALYTICS_QUERY_COUNT = 10402;


}
