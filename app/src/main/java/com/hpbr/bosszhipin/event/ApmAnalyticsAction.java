package com.hpbr.bosszhipin.event;

import androidx.annotation.StringDef;

/**
 * Created by zhangxiangdong on 2019/4/17 20:29.
 */
public class ApmAnalyticsAction {

    public static final String ACTION_LEGENCY_FILE_SELECTOR = "action_legency_file_selector";
    public static final String ACTION_RESUME = "action_resume";
    public static final String ACTION_MESSAGE_FAILED = "action_message_failed";
//    public static final String ACTION_MESSAGE_UNSUPPORTED = "action_message_unsupported";

//    public static final String ACTION_MQTT = "action_mqtt";
//    public static final String TYPE_MQTT_CONN_INFO = "type_mqtt_conn_info";
//    public static final String TYPE_MQTT_CONN_QUALITY = "type_mqtt_conn_quality";

    public static final String ACTION_GALLERY = "action_gallery";
    public static final String TYPE_IMAGE_SIZE = "type_image_size";

    public static final String ACTION_OPEN_SETTING = "action_open_setting";

    public static final String ACTION_SECURITY = "action_security";
    public static final String TYPE_SECURITY_ENV = "type_security_env";
    public static final String TYPE_INVALID_RESUME_URL = "type_invalid_resume_url"; // 无效的简历url

    public static final String ACTION_NO_PATH_URI = "action_no_path_uri"; // 无法通过文件Uri获取文件真实路径

    public static final String TYPE_JSON_FAILED = "type_json_failed"; // JSONException
    public static final String TYPE_RESPONSE_CALLBACK_EXCEPTION = "type_response_callback_exception"; // JSONException

    public static final String ACTION_SECURITY_HOOK = "action_security_hook";
    public static final String ACTION_SECURITY_ROOT = "action_security_root";
    public static final String ACTION_SECURITY_SIMULATOR = "action_security_simulator";
    public static final String ACTION_SECURITY_REPACKAGE = "action_security_repackage";

    public static final String ACTION_MESSAGE_SYNC = "action_message_sync";
    public static final String TYPE_CONTACT_NO_SYNC = "type_contact_no_sync";
    public static final String TYPE_CHAT_EMPTY = "type_chat_empty";

    public static final String ACTION_SQ_LITE_UPDATE = "action_sq_update";
    public static final String TYPE_SQ_LITE_UPDATE_EXCEPTION = "type_sq_update_exception";
    public static final String TYPE_SQ_LITE_UPDATE_TOO_LONG = "type_sq_update_too_long";
    public static final String TYPE_SQ_LITE_CORRUPTION = "type_db_corruption";
    public static final String TYPE_SQ_LITE_REPAIR = "type_db_repair";
    public static final String TYPE_SQ_LITE_REPAIR_SHOW = "type_db_repair_show";
    public static final String TYPE_SP_CORRUPTION = "type_sp_corruption";


    public static final String ACTION_SECURITY_LOG = "action_security_log";

    @Deprecated
    public static final String ACTION_NONE_READ_COUNT_REPORT = "noneReadCountReport";

    public static final String ACTION_MISSING_SECURITY_ID = "MissingSecurityId";

    public static final String ACTION_LAST_CHAT_TEXT = "LastChatText";

    public static final String ACTION_CHAT_BOSS_JOB_ID_ZERO = "ChatBossJobIdZero";
    public static final String ACTION_LATLNG_IS_NULL = "action_latlon_is_null";

    /**
     * TLog 上报埋点以及子类型
     */
    public static final String ACTION_T_LOG = "action_t_log";
//    public static final String ACTION_T_LOG_SUCCESS = "t_log_success";
//    public static final String ACTION_T_LOG_FAILED = "t_log_failed";
    /**
     * p2: 文件名
     * p3: 文件大小
     */
    public static final String TYPE_TX_REQUEST = "type_tx_request";
    /**
     * p2: 文件名
     * p3: 文件大小
     * p4: 成功（原因）
     * p5: 失败原因
     * p6: 失败原因2
     * p7: 文件id（上传成功才有）
     */
    public static final String TYPE_TX_RESPONSE = "type_tx_response";

    /**
     * p2: 文件名
     * p3: 文件大小
     */
    public static final String TYPE_MCP_REQUEST = "type_mcp_request";
    /**
     * p2: 文件名
     * p3: 文件大小
     * p4: 成功（文件地址）
     * p5: 失败原因
     */
    public static final String TYPE_MCP_RESPONSE = "type_mcp_response";


    @StringDef({
            TYPE_TX_REQUEST, TYPE_TX_RESPONSE, TYPE_MCP_REQUEST, TYPE_MCP_RESPONSE
    })
    public @interface TypeUploader {

    }

    /**
     * 用户登录成功的埋点，以及子类型
     */
    public static final String ACTION_U_LOGIN_SUCCESS = "action_u_login_success";
    public static final String ACTION_LOGIN_FIRST = "login_first";
    public static final String ACTION_LOGIN_EVER = "login_ever";

    public static final String ACTION_OPEN_REPORT = "action_open_report";

    public static final String ACTION_LIVE_ERROR = "action_live_error";

    // 磁盘信息上报，磁盘大小、剩余容量等
    public static final String ACTION_INFO_DAC = "action_info_dac";

    public static final String ACTION_WEB_VIEW_IN_SECOND = "action_web_view_in_second";

    //region APP CRASH（Bugly回调）
    public static final String ACTION_BUGLY_ERROR = "action_bugly_error";

    public static final String ACTION_APP_CRASH = "action_app_crash";

    public static final String TYPE_JAVA_CRASH = "type_java_crash";
    public static final String TYPE_NATIVE = "type_native";
    public static final String TYPE_ANR = "type_anr";
    public static final String TYPE_OTHER = "type_other";

    public static final String ACTION_GEEK_ROUTE_RESULT = "action_geek_route_result";
    public static final String ACTION_GEEK_ROUTE_RESULT_OVER_LIMIT = "action_geek_route_result_over_limit";
    public static final String TYPE_BUS = "bus";
    public static final String TYPE_WALK = "walk";
    public static final String TYPE_RIDE = "ride";
    public static final String TYPE_DRIVE = "drive";

//    public static final String TYPE_JAVA_CATCH = "type_java_catch";
//    public static final String TYPE_U3D = "type_u_3_d";
//    public static final String TYPE_COCOS2DX_JS = "type_cocos_2_dx_js";
//    public static final String TYPE_COCOS2DX_LUA = "type_cocos_2_dx_lua";
//    public static final String TYPE_BLOCK = "type_block";
    //endregion

    public static final String ACTION_WEBVIEW = "action_webview";
    public static final String TYPE_WEBVIEW_HTTP_CODE = "type_webview_http_code";
    public static final String TYPE_WEBVIEW_SSL_ERROR = "type_webview_ssl_error";
    public static final String TYPE_WEBVIEW_SSL_DNS = "type_webview_ssl_dns";
    public static final String TYPE_WEBVIEW_SSL_GUARD_HIT = "type_webview_ssl_guard_hit";
    public static final String TYPE_WEBVIEW_SSL_GUARD_SUCCESS = "type_webview_ssl_guard_success";
    public static final String TYPE_WEBVIEW_ERROR = "type_webview_error";
    public static final String TYPE_RENDER_PROCESS_GONE = "type_render_process_gone";
    public static final String TYPE_LOAD_EXTERNAL_URL = "type_load_external_url";
    public static final String TYPE_WEBVIEW_ERROR_HOST = "type_webview_error_host";
    public static final String TYPE_LOAD_HTTP = "type_load_http";

    public static final String ACTION_WEB_VIEW_PERMISSION = "action_web_view_permission";
    public static final String TYPE_WEB_VIEW_PERMISSION_VIDEO = "type_video";
    public static final String TYPE_WEB_VIEW_PERMISSION_AUDIO = "type_audio";
    public static final String TYPE_WEB_VIEW_PERMISSION_OTHER = "type_other";
    public static final String TYPE_WEB_VIEW_PERMISSION_UNKNOWN = "type_unknown";
    public static final String TYPE_WEB_VIEW_PERMISSION_ERROR = "type_error";

    public static class BizKey {

        public static final String KEY_LOG_ADDRESS = "log_address";

        public static final String KEY_ERROR_REASON = "error_reason";

    }

    public static final String ACTION_ACTION_TXUGC_E = "action_txugc_e";

    public static final String ACTION_PHONE_UNABLE_RECEIVE_MSG = "action_phone_unable_receive_msg";

    public static final String ACTION_SORT = "action_sort";

    public static final String ACTION_F2_INDEX_EXCEPTION = "f2_index_out_exception";

    //解决反馈个别用户聊天表情空白case
    public static final String ACTION_EMOTION_EMPTY = "emotion_empty_report";


    /**
     * SwipRefreshListView异常上报
     */
    public static final String ACTION_SWIPREFRESH_EXCPTION = "action_swiperefresh_expection";

    public static class SwipeExceptionKey {
        // C/B F1 首页item不满一屏但是hasmore为true
        public static String KEY_FIRSTPAGE_AUTO_LOADMORE = "first_page_auto_loadmore";
    }

    /*校招直播视频悬浮小窗打开或关闭静音异常上报*/
    public static final String ACTION_LIVE_FLOAT_WINDOW_OPEN_OR_CLOSE_MUTE_EXCEPTION = "action_live_float_window_open_or_close_mute_exception";
    /*校招直播视频悬浮小窗隐藏或显示的异常上报*/
    public static final String ACTION_LIVE_FLOAT_WINDOW_SHOW_OR_HIDE_EXCEPTION = "action_live_float_window_show_or_hide_exception";
    /*V组音频采集回调信息上报*/
    public static final String ACTION_AUDIO_CAPTURE_BY_V_GROUP_INFO = "action_audio_capture_by_v_group_info";

    /**
     * v组 安全连拍相关埋点上报
     */
    public static final String ACTION_V_TARGET_IDENTIFICATION = "action_target_identification";

    /**
     * 用于nlp传参
     */
    public static final String ACTION_NLP = "action_nlp";
    public static final String TYPE_ENV_INFO = "type_env_info";
    public static final String TYPE_VAR_INFO = "type_var_info";
    public static final String TYPE_APP_CLONE = "tpye_app_clone";
    public static final String TYPE_DID_CHANAGE = "tpye_did_chanage";


    /**
     * 安全相关的业务异常
     */
    public static final String ACTION_SECURITY_ERR = "action_security_error";
    public static final String TYPE_SEC_ENV = "type_sec_env";
    public static final String TYPE_SEC_CHECKER = "type_sec_checker";
    public static final String TYPE_SYS_FILE = "type_sys_file";

    /**
     * 捞取系统文件
     */
    public static final String ACTION_SYS_FILE = "action_sys_file";

    public static final String USER_MARK_MISS_SECURITY_ID = "userMarkMissSecurityId";

    public static final String ACTION_POSITION_DATA_FALL_BACK = "action_position_data_fall_back";
    public static final String ACTION_POSITION_DATA_FALL_BACK_POSITION = "position";

    public static final String ACTION_FULL_INFO_EMPTY = "action_full_info_empty";

    public static final String ACTION_FULL_INFO_EMPTY_MORE_THAN_THIRTY_DAY = "moreThanThirtyDay";
    public static final String ACTION_FULL_INFO_EMPTY_IN_THIRTY_DAY = "inThirtyDay";

    public static final String CHAT_NLP_NULL_EXCEPTION = "chat_nlp_null_exception";

    public static final String CHAT_SEND_ACTION_SAME_FROM_TO_UID = "sendActionSameFromToUID";

    /**
     * 旧版牛人创建面试
     */
    public static final String ACTION_INTERVIEW_CREATE_BY_GEEK = "geekCreateInterview";
    public static final String ACTION_SHARE_JOB_TEXT_BANNER_FRAGMENT_ERROR = "action_share_job_text_banner_fragment_error";//新职位分享，banner文案图页面加载异常统计
    public static final String ACTION_GEEK_FILTER = "action_geek_filter";
    public static final String ACTION_GEEK_STAGGERED_GRID_ERROR = "action_geek_staggered_grid_error";
    /**
     * 修复apm的bug埋点观察，观察几个版本后下掉无用点
     */
    public static final String ACTION_FIX_APM_BUG = "action_fix_apm_bug";

    /**
     * 语音录制阻塞埋点
     */
    public static final String ACTION_FIX_AUDIO_RECORD_BLOCK = "action_fix_audio_record_block";
    /**
     * 异常问题捕获
     */
    public static final String ACTION_CATCH_EXCEPTION = "action_catch_exception";

    public static final String ACTION_MINI_PROGRAM = "action_mini_program";

    public static final String ACTION_REPORT_SOUND_DOWNLOAD_TOO_MORE = "action_report_sound_download_too_more";

    public static final String ACTION_BOSS_COMPLETE_MONITOR = "action_boss_complete_monitor";
    public static final String ACTION_LOTTIE_ERROR = "action_lottie_error";

    /**
     * 观察F1列表接口非法调用时的打点
     */
    public static final String ACTION_BOSS_F1_INVALID_REQUEST = "action_boss_f1_invalid_request";

    public static final String TYPE_CONDITION_CHANGED = "type_condition_changed";
    public static final String TYPE_OVER_20_MINS = "type_over_20_mins";
    public static final String TYPE_LAST_REQUEST_FAILED = "type_last_request_failed";
    public static final String TYPE_GEEK_SELECTED_ADDRESS_MAP_THROW = "type_geek_selected_address_map_throw";

    /**
     * videoinfo 信息异常打点
     */
    public static final String TYPE_VIDEO_INFO_NULL = "video_info_null";
    public static final String TYPE_TX_EXCEPTION_NULL = "tx_exception_null";

    public static final String TYPE_WEB_VIEW_POST_MESSAGE = "webview_post_message";

    public static final String ACTION_BOSS_F1_JOB_MATCH = "action_boss_f1_job_match";

}
