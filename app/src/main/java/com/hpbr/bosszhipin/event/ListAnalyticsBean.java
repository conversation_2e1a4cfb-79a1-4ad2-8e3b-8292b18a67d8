package com.hpbr.bosszhipin.event;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.monch.lbase.orm.db.annotation.Table;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Map;

@Table("ListAnalyticsBean")
public class ListAnalyticsBean extends BaseEntityAuto {
    private static final long serialVersionUID = -1;
    public String key;
    public Map<String, String> params;
    public long updatetime;
    public int viewCount;

    @Override
    public String toString(){
        StringBuilder sb = new StringBuilder();
        sb.append("action:").append(key).append(" ");
        sb.append("updatetime:").append(updatetime).append(" ");
        sb.append("viewCount:").append(viewCount).append(" ");
        if(params != null){
            for (Map.Entry<String,String> e:params.entrySet()) {
                sb.append(e.getKey()).append(":").append(e.getValue()).append(" ");
            }
        }

        return sb.toString();
    }

    public void merge(ListAnalyticsBean bean){
        this.viewCount += bean.viewCount;
        this.updatetime = bean.updatetime;
    }


    public JSONObject toUploadJson(){
        JSONObject jo = new JSONObject();
        if(this.params != null){
            if(viewCount != 0){
                this.params.put("viewCount",viewCount+"");
            }

            if(updatetime != 0){
                this.params.put("updatetime",updatetime+"");
            }


            Iterator itor = params.entrySet().iterator();
            while(itor.hasNext()){
                Map.Entry<String,String> entry = (Map.Entry<String, String>) itor.next();
                try {
                    jo.put(entry.getKey(),entry.getValue()+"");
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return jo;
    }
}
