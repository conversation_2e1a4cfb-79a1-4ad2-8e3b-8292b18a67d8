package com.hpbr.bosszhipin.event;

import android.text.TextUtils;
import android.widget.AbsListView;
import android.widget.ListView;

import com.bszp.kernel.logic.db.entitiy.ListAnalyticsBean;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.monch.lbase.util.L;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wang<PERSON> on 16/9/18.
 */
public class ListAnalyticsChangeListener implements ListView.OnScrollListener {
    private ListView.OnScrollListener mOldScrollListener;
    private int mScrollState = AbsListView.OnScrollListener.SCROLL_STATE_IDLE;
    private Set<Object> mIdSet = new HashSet<>();
    private Map<String, String> extras;
    private String mPrefix;
    private boolean mDistinct;
    private boolean mUpdate;
    public static final String TAG = "list";

    private ListAnalyticsJustinTime justinTime;

    public ListAnalyticsChangeListener(AbsListView.OnScrollListener listener, String prefix, Map<String, String> extras, boolean distinct, boolean update) {
        this.mOldScrollListener = listener;
        this.mDistinct = distinct;
        this.mPrefix = prefix;
        this.mUpdate = update;
        this.extras = extras;

        justinTime = new ListAnalyticsJustinTime();
    }


    @Override
    public void onScrollStateChanged(AbsListView view, int scrollState) {
        this.mScrollState = scrollState;
        if (mOldScrollListener != null) {
            mOldScrollListener.onScrollStateChanged(view, scrollState);
        }

        if (scrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE) {
            onScroll(view, view.getFirstVisiblePosition(), view.getLastVisiblePosition() - view.getFirstVisiblePosition() + 1, view.getAdapter().getCount());
        }
    }

    @Override
    public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
        if (mOldScrollListener != null) {
            mOldScrollListener.onScroll(view, firstVisibleItem, visibleItemCount, totalItemCount);
        }
        if (this.mScrollState == AbsListView.OnScrollListener.SCROLL_STATE_IDLE && totalItemCount > 0) {
            dealAnalytics(view, firstVisibleItem, firstVisibleItem + visibleItemCount - 1, totalItemCount);
        }
    }


    private Set<Object> lastScreenData = new HashSet<>();


    private boolean isF2(){
        if(extras != null && "1".equals(extras.get("pagesource"))){
            return true;
        }
        return false;
    }


    private void dealAnalytics(AbsListView view, int firstVisibleItem, int lastVisibleItem, int totalItemCount) {
        try {
            List<ListAnalyticsBean> list = new ArrayList<>();
            ListAnalyticsBean bean;
            List<Object> datas = new ArrayList<>();
            Map<Object,String> positionMap = new HashMap<>();
            Set<Object> currentScreenData = new HashSet<>();
            for (int i = firstVisibleItem; i <= lastVisibleItem; i++) {
                Object o = null;
                try { //部分情况会出现IndexOutOfBoundsException
                    o = view.getAdapter().getItem(i);
                } catch (Exception e) {
                    L.d(TAG, "======list====get o error:"+e.toString());

                }
                if (o == null) {
                    L.d(TAG, "======list====o is null  i:"+ i + " type:" + view.getAdapter().getItemViewType(i));
                    continue;
                }

                if(isF2()){//针对F2的曝光，过滤掉red_num<0的数据
                    try {
                        if(ReflectionUtils.getFieldValue(o, "noneReadCount") == null){
                            continue;
                        }
                        int noneReadCount = Integer.valueOf(ReflectionUtils.getFieldValue(o, "noneReadCount").toString());
                        L.d(TAG, "======list====noneReadCount"+noneReadCount);
                        if(noneReadCount < 1){
                            continue;
                        }
                    }catch (Exception e){
                        L.d(TAG, "======list====noneReadCount"+e.toString());
                    }
                }
                if(!lastScreenData.contains(o)){
                    datas.add(o);
                    positionMap.put(o,String.valueOf(i));
                }
                currentScreenData.add(o);
            }
            lastScreenData.clear();
            lastScreenData.addAll(currentScreenData);

            if(datas.size() <= 0){
                L.d(TAG, "======list====new data is null");
                return;
            }

            int firstNeedExposureIndex = 0;
            if(isF2()){
                try { //部分情况会出现IndexOutOfBoundsException
                int adapterCount = view.getAdapter().getCount();
                for(int i=0;i<adapterCount;i++){
                        Object o = view.getAdapter().getItem(i);
                        if(o!= null){
                            break;
                        }
                        firstNeedExposureIndex ++;
                    }
                } catch (Exception e) {

                }
            }

            for (int i = 0; i < datas.size(); i++) {
                Object o = datas.get(i);

                L.d(TAG, "======list====position:" + i + " type:" + view.getAdapter().getItemViewType(i));
                Object exposureAction = ReflectionUtils.getFieldValue(o, "exposureAction");
                if (exposureAction != null) {
                    String action = String.valueOf(exposureAction);
                    if (!TextUtils.isEmpty(action)) {
                        justinTime.exposureActionRequest(action);
                        L.d(TAG, "=====exposureAction:" + action);
                        Map map = new HashMap();
                        map.put("p3", i + "");
                        AnalyticsFactory.bgAction(action, map);
                    }
                }

                L.d(TAG, "=====list=====position:" + i + " o" + o.toString());
                ListAnalyticsSwitch analyticsSwitch = o.getClass().getAnnotation(ListAnalyticsSwitch.class);
                if (analyticsSwitch != null) {
                    String keys[] = analyticsSwitch.key();
                    if (keys != null) {
                        int keySize = keys.length;
                        String key = "";
                        for (int j = 0; j < keySize; j++) {
                            key += ReflectionUtils.getFieldValue(o, keys[j]);
                            if (j != keySize - 1) {
                                key += "_";
                            }
                        }
                        bean = new ListAnalyticsBean();
                        bean.key = key;
                        String params[] = analyticsSwitch.params();
                        bean.params = new HashMap<>();
                        bean.params.put("identity", UserManager.getUserRole() == ROLE.BOSS ? "1" : "0");
                        if (params != null) {
                            int paramsSize = params.length;
                            for (int j = 0; j < paramsSize; j++) {
                                String[] ss = params[j].split(":");
                                if (ss.length != 2) {
                                    continue;
                                }
                                bean.params.put(ss[0], ReflectionUtils.getFieldValue(o, ss[1]) + "");
                            }
                        }

                        if (this.extras != null && this.extras.size() > 0) {
                            bean.params.putAll(extras);
                        }
                        int postion = Integer.valueOf(positionMap.get(o))- firstNeedExposureIndex;
                        bean.params.put("exposure_position",String.valueOf(postion));
                        bean.viewCount = 1;
                        bean.updatetime = System.currentTimeMillis();
                        list.add(bean);
                    }
                    //是否去重
                    //如果不去重复，判断是否更新时间
                } else {

                }
            }
            if (list.size() > 0) {
                if (mDistinct) {
                    if (mUpdate) {
                        ListAnalyticsFactory.getInstance().insertDistinctAndUpdate(list);
                    } else {
                        ListAnalyticsFactory.getInstance().insertDistinctNoUpdate(list);
                    }
                } else {
                    ListAnalyticsFactory.getInstance().insert(list);
                }

            }
        }catch (Exception e){
            L.d(TAG, "======list====dealAnalytics error:"+e.toString());
        }

    }
}
