package com.hpbr.bosszhipin.event;

import android.widget.AbsListView;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bszp.kernel.logic.db.AppDatabase;
import com.bszp.kernel.logic.db.dao.ListAnalyticsDao;
import com.bszp.kernel.logic.db.entitiy.ListAnalyticsBean;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.db.async.DbOpCallback;
import com.hpbr.bosszhipin.data.db.async.DbOpParam;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.exception.ListAnalyticsException;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.hpbr.bosszhipin.views.listview.F2SwipeRefreshListView;
import com.hpbr.bosszhipin.views.swipe.listview.SwipeRefreshListView;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.SP;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ListAnalyticsRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONArray;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 作者：wangtian
 * 日期:16/9/18
 */
public class ListAnalyticsFactory implements ForegroundUtils.ForegroundListener {
    private static String KEY_ACTIVE_TIME = "key_active_time";
    private static String TAG = "upload";
    private static ListAnalyticsFactory mInstance = new ListAnalyticsFactory();
    private final ListAnalyticsDao analyticsDao;

    private ListAnalyticsFactory() {
        analyticsDao = AppDatabase.get().getListAnalyticsDao();
    }

    private volatile AtomicBoolean isUploading = new AtomicBoolean(false);
    private List<ListAnalyticsRecord> records = new ArrayList<>();//都是ui线程操作，不需要同步

    private final int ACTIVIE_CHECK_DURATION = 5000;  //更新活跃时间的任务定时器间隔

    public static ListAnalyticsFactory getInstance() {
        return mInstance;
    }

    /**
     * 统计配置通过参数传递
     *
     * @param lv 需要统计的item展现的listview，必须已经设置了adapter
     *           android 10
     *           Accessing hidden field Landroid/widget/AbsListView;->mOnScrollListener:Landroid/widget/AbsListView$OnScrollListener; (greylist-max-p, reflection, denied)
     *           java.lang.NoSuchFieldException: No field mOnScrollListener in class Landroid/widget/AbsListView
     *           <p>
     *           使用该该方法，请注意 如果业务 lv.setOnScrollListener 使用该方法之后 会导致业务方法失效
     *           <p>
     *           Use {@link #observer(ListView, AbsListView.OnScrollListener, Map<String, String>)} instead.
     **/
    @Deprecated
    public void observer(ListView lv, Map<String, String> extras) throws ListAnalyticsException {
        if (lv == null) {
            throw new ListAnalyticsException("SwipeRefreshListView is null");
        }

        if (lv.getAdapter() == null) {
            throw new ListAnalyticsException("adapter is null");
        }

        ListView.OnScrollListener listener = null;
        try {
            Field field = ReflectionUtils.getDeclaredField(lv, "mOnScrollListener");
            field.setAccessible(true);
            listener = (ListView.OnScrollListener) field.get(lv);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        lv.setOnScrollListener(new ListAnalyticsChangeListener(listener, "", extras, true, true));
    }

    /**
     * 统计配置通过参数传递
     *
     * @param lv 需要统计的item展现的listview，必须已经设置了adapter
     **/
    public void observer(ListView lv, AbsListView.OnScrollListener onScrollListener, Map<String, String> extras) {
        if (lv != null && lv.getAdapter() != null) {
            lv.setOnScrollListener(new ListAnalyticsChangeListener(onScrollListener, "", extras, true, true));
        } else {
            TLog.print("ListAnalyticsFactory", "ListView is NULL or adapter is NULL");
        }
    }

    public void observer(SwipeRefreshListView srl, Map<String, String> extras) {
        if (srl != null) {
            srl.setOnScrollListener(new ListAnalyticsChangeListener(srl.getOnScrollListener(), "", extras, true, true));
        } else {
            TLog.print("ListAnalyticsFactory", "SwipeRefreshListView is NULL");
        }
    }

    public void observer(F2SwipeRefreshListView srl, Map<String, String> extras) {
        if (srl != null) {
            srl.setOnScrollListener(new ListAnalyticsChangeListener(srl.getOnScrollListener(), "", extras, true, true));
        } else {
            TLog.print("ListAnalyticsFactory", "F2SwipeRefreshListView is NULL");
        }
    }


    /**
     * 统计配置通过参数传递
     * 默认去重，并执行合并
     **/
    public void observer(RecyclerView lv, Map<String, String> extras) throws ListAnalyticsException {
        observer(lv, extras, false, null);
    }

    /**
     * 统计配置通过参数传递
     * 默认去重，并执行合并
     **/
    public void observer(@NonNull RecyclerView lv, Map<String, String> extras, RecyclerViewAnalyticsChangeListener.ExposeListener exposeListener) {
        lv.addOnScrollListener(new RecyclerViewAnalyticsChangeListener(extras, true, true, exposeListener));
    }

    /**
     * @param justUseAnInter 只通过接口方式获取数据
     * @param interceptFun   不满足条件时则拦截，不再走曝光逻辑
     */
    public void observer(RecyclerView lv, Map<String, String> extras, boolean justUseAnInter, ZPFunction.FunR0<Boolean> interceptFun) throws ListAnalyticsException {
        if (lv == null) {
            throw new ListAnalyticsException("RecyclerView is null");
        }
        lv.addOnScrollListener(new RecyclerViewAnalyticsChangeListener(extras, true, true, justUseAnInter, interceptFun));
    }

    /***
     * 插入，不去重复
     * */
    public void insert(List<ListAnalyticsBean> list) {
        if (checkIsUploading(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT, list)) {
            return;
        }
        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT, new DbOpParam<List<ListAnalyticsBean>, Void>(list, null)).sendToTarget();
    }

    /***
     * 插入，去重复，覆盖不合并数据
     * */
    public void insertDistinctNoUpdate(List<ListAnalyticsBean> list) {
        if (checkIsUploading(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE, list)) {
            return;
        }
        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE, new DbOpParam<List<ListAnalyticsBean>, Void>(list, null)).sendToTarget();
    }

    /***
     * 插入，去重复，合并数据
     * */
    public void insertDistinctAndUpdate(List<ListAnalyticsBean> list) {
        if (checkIsUploading(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE, list)) {
            return;
        }
        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE, new DbOpParam<List<ListAnalyticsBean>, Void>(list, new DbOpCallback() {
            @Override
            public void success(Object o) {
                checkNeedUpload();
            }

            @Override
            public void error() {

            }
        })).sendToTarget();
    }


    private boolean checkIsUploading(int type, List<ListAnalyticsBean> list) {
        boolean result = false;
        if (isUploading.get()) {
            result = true;
            ListAnalyticsRecord record = new ListAnalyticsRecord(list, type);
            records.add(record);
        }

        return result;
    }

    private void dealReports() {
        int size = records.size();
        try {
            for (int i = 0; i < size; i++) {
                switch (records.get(i).getmOp()) {
                    case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT:
                        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT, new DbOpParam<List<ListAnalyticsBean>, Void>(records.get(i).getmList(), null)).sendToTarget();
                        break;
                    case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE:
                        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_NO_UPDATE, new DbOpParam<List<ListAnalyticsBean>, Void>(records.get(i).getmList(), null)).sendToTarget();
                        break;
                    case AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE:
                        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_INSERT_DISTINCT_AND_UPDATE, new DbOpParam<List<ListAnalyticsBean>, Void>(records.get(i).getmList(), null)).sendToTarget();
                        break;
                }
            }
        } catch (Exception e) {

        }
        records.clear();
    }

    private void query60(DbOpCallback<List<ListAnalyticsBean>> callback) {
        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_QUERY_60, new DbOpParam(null, callback)).sendToTarget();
    }

    private void doUpload() {
        if (isUploading.get()) {
            return;
        }
        if (!UserManager.isCurrentLoginStatus()) return;
        isUploading.set(true);
        query60(new DbOpCallback<List<ListAnalyticsBean>>() {
            @Override
            public void success(List<ListAnalyticsBean> listAnalyticsBeen) {

                if (listAnalyticsBeen == null || listAnalyticsBeen.size() == 0) {
                    dealUploadSuccess(null);
                    return;
                }
                JSONArray data = new JSONArray();
                for (int i = 0; i < listAnalyticsBeen.size(); i++) {
                    data.put(listAnalyticsBeen.get(i).toUploadJson());
                }

                ListAnalyticsRequest request = new ListAnalyticsRequest(new ApiRequestCallback<SuccessResponse>() {
                    @Override
                    public void onSuccess(ApiData<SuccessResponse> data) {
                        dealUploadSuccess(listAnalyticsBeen);
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        dealUploadFail();
                    }
                });
                request.data = data.toString();
                HttpExecutor.execute(request);

//                String url = URLConfig.URL_LOG_COLLECTOR;
//                Params params = new Params();
//                params.put("data", data.toString());
//                new Request().post(url, Request.getParams(url, params), new JSONCallback2() {
//                    @Override
//                    protected ApiResult onParseByChildThread(JSONObject jsonObject) throws JSONException, AutoLoginException {
//                        if (jsonObject == null) return null;
//                        ApiResult result = Request.parseApiResult(jsonObject);
//                        return result;
//                    }
//
//                    @Override
//                    protected void onFailed(Failed error) {
//                        dealUploadFail();
//                    }
//
//                    @Override
//                    protected void onComplete(ApiResult result) {
//                        if (result.isNotError()) {
//                            dealUploadSuccess();
//                        } else {
//                            dealUploadFail();
//                        }
//                    }
//                });
            }

            @Override
            public void error() {
                dealUploadFail();
            }
        });
    }

    private void dealUploadSuccess(List<ListAnalyticsBean> listAnalyticsBeans) {
        isUploading.set(false);
        SP.get().putLong(KEY_ACTIVE_TIME, 0);
        if (listAnalyticsBeans != null) {
            analyticsDao.delete(listAnalyticsBeans);
//            App.get().db().delete(listAnalyticsBeans);
        }
        if (BuildInfoUtils.isDebug()) {
            L.i(TAG, "dealUploadSuccess after delete count:" + analyticsDao.getCount());
        }
        dealReports();

    }

    private void dealUploadFail() {
        isUploading.set(false);
        dealReports();
    }

    @Override
    public void onForeground() {
        checkNeedUpload();
        activeStartTime = System.nanoTime();
        App.get().getMainHandler().postDelayed(activeTimeTask, ACTIVIE_CHECK_DURATION);
    }

    @Override
    public void onBackground() {
        saveActiveTime();
        checkNeedUpload();
        App.get().getMainHandler().removeCallbacks(activeTimeTask);
    }

    private long activeStartTime;

    private void checkNeedUpload() {
        L.i(TAG, "==============checkNeedUpload isUploading:" + isUploading.get());
        if (isUploading.get()) {
            return;
        }
        long activeTime = SP.get().getLong(KEY_ACTIVE_TIME, -1);
        L.i(TAG, "==============checkNeedUpload activeTime:" + activeTime + " limit：" + CommonConfigManager.getInstance().getListTimeLimit());
        if (activeTime >= CommonConfigManager.getInstance().getListTimeLimit()) {  //时间阈值 90s
            doUpload();
            return;
        }

        AnalyticsHandlerThread.getInstance().obtainMessage(AnalyticsHandlerThread.OP_LIST_ANALYTICS_QUERY_COUNT, new DbOpParam(null, new DbOpCallback() {
            @Override
            public void success(Object o) {
                try {
                    long count = (Long) o;
                    L.i(TAG, "==========QUERY_COUN====count:" + count + " limit:" + CommonConfigManager.getInstance().getListItemLimit());
                    if (count >= CommonConfigManager.getInstance().getListItemLimit()) { //数量阈值 60条
                        doUpload();
                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void error() {

            }
        })).sendToTarget();
    }

    Runnable activeTimeTask = new Runnable() {
        @Override
        public void run() {
            saveActiveTime();
            activeStartTime = System.nanoTime();
            checkNeedUpload();
            App.get().getMainHandler().postDelayed(activeTimeTask, ACTIVIE_CHECK_DURATION);
        }
    };

    private void saveActiveTime() {
        long time = System.nanoTime() - activeStartTime;
        long activeTime = SP.get().getLong(KEY_ACTIVE_TIME, -1);
        if (activeTime == -1) {
            activeTime = time / (1000 * 1000 * 1000);
        } else {
            activeTime += time / (1000 * 1000 * 1000);
        }
        SP.get().putLong(KEY_ACTIVE_TIME, activeTime);
    }


}
