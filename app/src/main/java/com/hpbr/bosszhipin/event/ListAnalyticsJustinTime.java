package com.hpbr.bosszhipin.event;

import android.text.TextUtils;

import com.hpbr.bosszhipin.data.manager.UserManager;

import net.BusinessUrlConfig;
import net.bosszhipin.base.SimpleApiRequest;

import org.json.JSONException;
import org.json.JSONObject;

import androidx.annotation.NonNull;

/**
 * Author: zhouyou
 * Date: 2019-11-28
 * 业务层差异化曝光处理，要求实时上报
 */
class ListAnalyticsJustinTime {

    private static final String ACTION_EXPOSURE_SCENE_CARD = "entrance-exposure";

    /**
     * 实时曝光
     *
     * @param exposureActionString
     */
    void exposureActionRequest(@NonNull String exposureActionString) {
        if (!UserManager.isBossRole()) {
            return;
        }
        if (isFitRegulation(exposureActionString)) {
            SimpleApiRequest.GET(BusinessUrlConfig.URL_CARD_EXPOSURE_STATISTIC)
                    .addParam("exposureAction", exposureActionString)
                    .execute();
        }
    }

    /**
     * 符合条件
     *
     * @param exposureActionString 符合实时曝光的业务场景
     * @return
     */
    private boolean isFitRegulation(@NonNull String exposureActionString) {
        String action = "";
        try {
            JSONObject jo = new JSONObject(exposureActionString);
            Object o = jo.get("action");
            if (o instanceof String) {
                action = String.valueOf(o);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return TextUtils.equals(action, ACTION_EXPOSURE_SCENE_CARD);
    }
}
