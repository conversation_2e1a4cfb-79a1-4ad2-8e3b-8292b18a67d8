package com.hpbr.bosszhipin.event;

import com.bszp.kernel.logic.db.entitiy.ListAnalyticsBean;
import com.hpbr.bosszhipin.base.BaseEntityAuto;

import java.util.List;

public class ListAnalyticsRecord extends BaseEntityAuto {
    private List<ListAnalyticsBean> mList;
    private int mOp;


    public ListAnalyticsRecord(List<ListAnalyticsBean> list, int op){
        mList = list;
        mOp = op;
    }

    public List<ListAnalyticsBean> getmList() {
        return mList;
    }

    public int getmOp() {
        return mOp;
    }
}
