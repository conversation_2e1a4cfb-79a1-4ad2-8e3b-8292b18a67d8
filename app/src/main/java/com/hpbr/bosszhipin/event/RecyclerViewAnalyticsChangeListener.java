package com.hpbr.bosszhipin.event;

import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bszp.kernel.logic.db.entitiy.ListAnalyticsBean;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.utils.ReflectionUtils;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.monch.lbase.util.LList;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by wangtian on 16/9/18.
 * 方案一：extends RecyclerView.Adapter  + holder.item.setTag(itemBean)
 * 方案二：extends com.hpbr.bosszhipin.recycleview.BaseRvAdapter
 */
public class RecyclerViewAnalyticsChangeListener extends RecyclerView.OnScrollListener {

    public static final String TAG = "rvListAnalytics";

    protected final Set<String> mIdSet = new HashSet<>();

    protected final Map<String, String> extras;


    protected final boolean mDistinct;

    protected final boolean mUpdate;

    protected ListAnalyticsJustinTime justinTime = new ListAnalyticsJustinTime();

    /*不满足条件时则拦截，不再走曝光逻辑*/
    protected final ZPFunction.FunR0<Boolean> interceptFun;

    /*接口方式获取数据*/
    protected final boolean justUseAnInter;

    private final ExposeListener exposeListener;
    public RecyclerViewAnalyticsChangeListener(Map<String, String> extras, boolean distinct, boolean update, boolean justUseAnInter, ZPFunction.FunR0<Boolean> interceptFun) {
        this.mDistinct = distinct;
        this.mUpdate = update;
        this.extras = extras;
        this.interceptFun = interceptFun;
        this.justUseAnInter = justUseAnInter;
        this.exposeListener = null;
    }

    public RecyclerViewAnalyticsChangeListener(Map<String, String> extras, boolean distinct, boolean update, ExposeListener exposeListener) {
        this.mDistinct = distinct;
        this.mUpdate = update;
        this.extras = extras;
        this.exposeListener = exposeListener;
        this.interceptFun = null;
        this.justUseAnInter = false;
    }

    @Override
    public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);
        RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        if (recyclerView.getScrollState() == RecyclerView.SCROLL_STATE_IDLE && null != adapter && adapter.getItemCount() > 0) {
            dealStateChanged(recyclerView);
        }
    }

    @Override
    public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
        super.onScrollStateChanged(recyclerView, newState);
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            dealStateChanged(recyclerView);
        }
    }

    private void dealStateChanged(RecyclerView recyclerView) {
        RecyclerView.Adapter<?> adapter = recyclerView.getAdapter();
        RecyclerView.LayoutManager layoutManager = recyclerView.getLayoutManager();
        if (layoutManager instanceof LinearLayoutManager && null != adapter) {
            LinearLayoutManager linearManager = (LinearLayoutManager) layoutManager;
            //获取最后一个可见view的位置
            int lastItemPosition = linearManager.findLastVisibleItemPosition();
            //获取第一个可见view的位置
            int firstItemPosition = linearManager.findFirstVisibleItemPosition();
            dealAnalytics(recyclerView, adapter, firstItemPosition, lastItemPosition, adapter.getItemCount());
        }
    }

    /**
     * 1303.501【意】BOSS 端-增加推荐卡片真实曝光埋点：判断列表是否支持，微小抖动触发曝光打点，新增意向沟通列表判断逻辑
     *
     * @return boolean
     */
    private boolean isSupportSlightScroll() {
        String pageSource = extras == null ? null : extras.get("pagesource");
        boolean isF2 = "1".equals(pageSource);
        boolean isContactIntentionList = "101".equals(pageSource) || "102".equals(pageSource);
        return isF2 || isContactIntentionList;
    }

    private void dealAnalytics(@NonNull RecyclerView view, @NonNull RecyclerView.Adapter<?> adapter, int firstVisibleItem, int lastVisibleItem, int totalItemCount) {
        /*不满足前提条件就不再曝光了*/
        if (null != interceptFun && interceptFun.call()) {
            return;
        }

        int firstNeedExposureIndex = 0;
        boolean ssScroll = isSupportSlightScroll();

        List<ListAnalyticsBean> list = null;

        for (int i = firstVisibleItem; i <= lastVisibleItem; i++) {
            if (!ssScroll && !checkPositionSlop(i, firstVisibleItem, lastVisibleItem, totalItemCount)) { // 这个会导致 expPosition从1 开始
                continue;
            }

            int expPosition = i - firstNeedExposureIndex;

            Object o = null;
            try { //部分情况会出现IndexOutOfBoundsException
                o = getObject(view, i);
            } catch (Exception ignore) {

            }

            if (o == null) {
                if (ssScroll) { // 针对F2的曝光，过滤掉无需曝光数据的数据 但曝光 index
                    firstNeedExposureIndex++;
                }
                continue;
            }

            /*接口方式获取数据*/
            boolean useAnInter = o instanceof IListAnalyticsInter || justUseAnInter;

            /*曝光埋点*/
            Object exposureAction = null;
            if (useAnInter) {
                if (o instanceof IListAnalyticsInter) {
                    exposureAction = ((IListAnalyticsInter) o).getExposureAction();
                }
            } else {
                exposureAction = ReflectionUtils.getFieldValue(o, "exposureAction");
            }
            if (exposureAction != null) {
                String action = String.valueOf(exposureAction);
                if (!TextUtils.isEmpty(action)) {
                    justinTime.exposureActionRequest(action);
                    AnalyticsFactory.bgAction(action, null);
                }
            }

            /*初始化list*/
            list = LList.obtainIfNull(list);

            if (useAnInter) {
                LList.addElement(list, interListAnalytics(o, expPosition));
            } else {
                LList.addElement(list, reflectionListAnalytics(o, expPosition));
            }
        }

        if (null != list && list.size() > 0) {
            if (mDistinct) {
                if (mUpdate) {
                    ListAnalyticsFactory.getInstance().insertDistinctAndUpdate(list);
                } else {
                    ListAnalyticsFactory.getInstance().insertDistinctNoUpdate(list);
                }
            } else {
                ListAnalyticsFactory.getInstance().insert(list);
            }
        }
    }

    private ListAnalyticsBean interListAnalytics(Object obj, int expPosition) {
        ListAnalyticsBean result = null;
        if (obj instanceof IListAnalyticsInter) {
            IListAnalyticsInter inter = (IListAnalyticsInter) obj;
            if (!inter.isJumpExpose() && null != inter.getKeys()) {
                result = obtainAnalyticsBean(inter.getKeys(), inter.getParamsMap(), expPosition);
            }
        }
        return result;
    }


    private ListAnalyticsBean reflectionListAnalytics(Object o, int expPosition) {
        ListAnalyticsBean result = null;

        ListAnalyticsSwitch analyticsSwitch = o.getClass().getAnnotation(ListAnalyticsSwitch.class);
        Object isJumpExposeObject = ReflectionUtils.getFieldValue(o, "isJumpExpose");
        boolean isJumpExpose = ((isJumpExposeObject instanceof Boolean) && (((Boolean) isJumpExposeObject)));//是否跳过曝光
        if (exposeListener != null) {
            isJumpExpose = exposeListener.isJumpExpose(o);
        }
        if (analyticsSwitch != null && !isJumpExpose) {
            String[] keys = analyticsSwitch.key();
            if (keys != null) {

                int keySize = keys.length;
                StringBuilder key = new StringBuilder();
                for (int j = 0; j < keySize; j++) {
                    key.append(ReflectionUtils.getFieldValue(o, keys[j]));
                    if (j != keySize - 1) {
                        key.append("_");
                    }
                }

                String[] params = analyticsSwitch.params();
                HashMap<String, String> tpParamMap = null;
                if (params != null) {
                    tpParamMap = new HashMap<>();
                    for (String param : params) {
                        if (null == param) continue;
                        String[] ss = param.split(":");
                        if (ss.length != 2) continue;
                        tpParamMap.put(ss[0], ReflectionUtils.getFieldValue(o, ss[1]) + "");
                    }
                }

                result = obtainAnalyticsBean(key.toString(), tpParamMap, expPosition);
            }
        }

        return result;
    }

    public ListAnalyticsBean obtainAnalyticsBean(String[] keyArray, HashMap<String, String> paramMap, int expPosition) {
        StringBuilder keys = new StringBuilder();
        if (null != keyArray) {
            int length = keyArray.length;
            for (int j = 0; j < length; j++) {
                keys.append(keyArray[j]);
                if (j != length - 1) {
                    keys.append("_");
                }
            }
        }
        return obtainAnalyticsBean(keys.toString(), paramMap, expPosition);
    }

    public ListAnalyticsBean obtainAnalyticsBean(@NonNull String key, HashMap<String, String> paramMap, int expPosition) {
        ListAnalyticsBean bean = new ListAnalyticsBean();
        bean.key = key;
        bean.viewCount = 1;
        bean.updatetime = System.currentTimeMillis();

        bean.params = new HashMap<>();
        bean.params.put("exposure_position", String.valueOf(expPosition));
        bean.params.put("identity", UserManager.getUserRole() == ROLE.BOSS ? "1" : "0");
        if (this.extras != null && this.extras.size() > 0) {
            bean.params.putAll(extras);
        }

        if (paramMap != null && !paramMap.isEmpty()) {
            Set<String> keySet = paramMap.keySet();
            for (String keyStr : keySet) {
                String value = paramMap.get(keyStr);
                bean.params.put(keyStr, null != value ? value : "");
            }
        }
        return bean;
    }

    private Object getObject(@NonNull RecyclerView view, int i) {
        RecyclerView.Adapter<?> adapter = view.getAdapter();
        RecyclerView.LayoutManager layoutManager = view.getLayoutManager();
        Object object = null;
        if (adapter instanceof BaseRvAdapter) {
            BaseRvAdapter<?, ?> rvAdapter = (BaseRvAdapter<?, ?>) adapter;
            object = rvAdapter.getItem(i - rvAdapter.getHeaderLayoutCount());
        }else if (null != layoutManager) {
            View viewByPosition = layoutManager.findViewByPosition(i);
            if (null != viewByPosition) {
                object =  viewByPosition.getTag();
            }
        }
        if (exposeListener != null) {
            object = exposeListener.isExpose(object);
        }
        return object;
    }


    /**
     * 过滤轻微滑动
     **/
    private boolean checkPositionSlop(int current, int firstVisibleItem, int lastVisibleItem, int totalItemCount) {
        boolean result = true;
        for (int i = 0; i < firstVisibleItem; i++) {
            mIdSet.remove(String.valueOf(i));
        }
        for (int i = lastVisibleItem + 1; i < totalItemCount; i++) {
            mIdSet.remove(String.valueOf(i));
        }

        if (mIdSet.contains(String.valueOf(current))) {
            result = false;
        } else {
            mIdSet.add(String.valueOf(current));
        }

        return result;
    }

    public interface ExposeListener {
        Object isExpose(Object o);
        boolean isJumpExpose(Object o);
    }
}
