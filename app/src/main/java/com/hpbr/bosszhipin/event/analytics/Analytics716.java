package com.hpbr.bosszhipin.event.analytics;

import android.util.Log;

import com.hpbr.bosszhipin.event.AnalyticsFactory;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BRAND_OPERATION;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BRAND_WORK_TASTE_POPUP;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BRAND_WORK_TASTE_WILLUP;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_DETAIL_WORK_TASTE_PICUTER;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_EXP_ADDRESS_MAP;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_EXP_ADDRESS_MAP_HOME;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_EXP_ADDRESS_MAP_OUT;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_EXP_ADDRESS_MAP_SAVE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_EXP_ADDRESS_MAP_SEARCH;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_LIST_MASK;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_MASK_BRAND;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_MASK_BRAND_CONFIRM;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_WORK_TASTE_WILLDEL;


public class Analytics716 {

    /**
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2140
     */
    public static void clickMoreAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_BRAND_OPERATION)
                .build();
    }


    /**
     * boss进入公司主页出现添加工作体验弹窗
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2182
     */
    public static void showPopanalytics(String brandId) {
        AnalyticsFactory.create()
                .action(ACTION_BRAND_WORK_TASTE_POPUP)
                .param("p", brandId)
                .build();
    }

    /**
     * 点击删除工作体验
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2184
     * <p>
     * deleteSource: 删除来源：1 公司主页点击删除 2 工作体验详情页点击删除 3 公司主页工作详情查看更多列表
     */
    public static void deleteWorkExpAnalytics(String brandId, long brandWorkTasteId, int deleteSource) {
        AnalyticsFactory.create()
                .action(ACTION_WORK_TASTE_WILLDEL)
                .param("p", brandId)
                .param("p2", brandWorkTasteId)
                .param("p3", deleteSource)
                .build();

    }

    /**
     * 点击修改工作体验入职时间
     * @param brandId
     * @param brandWorkTasteId
     * @param editTimeSource
     */
    public static void editTimeWorkExpAnalytics(String brandId, long brandWorkTasteId, int editTimeSource) {
        AnalyticsFactory.create()
                .action(ACTION_BRAND_WORK_TASTE_WILLUP)
                .param("p", brandId)
                .param("p2", brandWorkTasteId)
                .param("p3", editTimeSource)
                .build();
    }

    /**
     * 点击查看工作体验图片大图
     *
     * url:https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2494
     *
     * @param brandId
     * @param brandWorkTasteId
     * @param source 点击来源：1.公司主页 2.工作体验详情页点击banner 3.工作体验详情页点击更多体验的图片
     */
    public static void workTastePicuterAnalytics(String brandId, int brandWorkTasteId, int source) {
        AnalyticsFactory.create()
                .action(ACTION_DETAIL_WORK_TASTE_PICUTER)
                .param("p", brandId)
                .param("p2", brandWorkTasteId)
                .param("p3", source)
                .build();
    }

    /**
     * 牛人进入地图选点页面
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2177
     * <p>
     * "1 该城市已有地址 0 该城市没有地址"
     */
    public static void enterSeleAddressAnalytics(String cityCode, boolean hasAddress) {
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP)
                .param("p", cityCode)
                .param("p2", hasAddress ? 1 : 0)
                .build();
    }

    public static void enterSeleAddressAnalytics(String cityCode, boolean hasAddress, String expectCityCode) {
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP)
                .param("p", cityCode)
                .param("p2", hasAddress ? 1 : 0)
                .param("p3", expectCityCode)
                .build();
    }

    /**
     * 牛人在地图选点页面点击设为住址
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2180
     */
    public static void saveHomeAddressAnalytics(String cityCode) {
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP_HOME)
                .param("p", cityCode)
                .build();
    }

    /**
     * 牛人在地图选点页面点击输入框
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2179
     */
    public static void clickInputEtAnalytics(String cityCode) {
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP_SEARCH)
                .param("p", cityCode)
                .build();
    }

    /**
     * 选择的city_code
     */
    public static void expAddressMapOutAnalytics(String cityCode, String selectCityCode) {
        Log.e("MapSave", "cityCode: " + cityCode + " saveSource: " + selectCityCode);
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP_OUT)
                .param("p", cityCode)
                .param("p2", selectCityCode)
                .build();
    }

    /**
     * 牛人在地图选点页面点击完成
     * "保存的地址来源： 1 搜索 2 拖动 3 gps定位"
     */
    public static void expAddressMapSaveAnalytics(String cityCode, String saveSource) {
        Log.e("MapSave", "cityCode: " + cityCode + " saveSource: " + saveSource);
        AnalyticsFactory.create()
                .action(ACTION_EXP_ADDRESS_MAP_SAVE)
                .param("p", cityCode)
                .param("p2", saveSource)
                .build();
    }

    /**
     * 在公司主页内点击“屏蔽公司”按钮
     */
    public static void maskBrandAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_MASK_BRAND)
                .build();
    }

    /**
     * 在公司主页内点击“去屏蔽”
     * 牛人添加屏蔽公司/解除屏蔽公司
     * 1 屏蔽公司 0 解除屏蔽
     */
    public static void maskBrandConfirmAnalytics(String type) {
        AnalyticsFactory.create()
                .action(ACTION_MASK_BRAND_CONFIRM)
                .param("p", type)
                .build();
    }

    /**
     * 牛人获取屏蔽的公司列表
     * <p>
     * 来源： 0: 隐私设置 1: 公司主页 （716）
     */
    public static void listMaskAnalytics(int maskCount, boolean isFromCompanySource) {
        AnalyticsFactory.create()
                .action(ACTION_LIST_MASK)
                .param("p", maskCount)
                .param("p2", isFromCompanySource ? "1" : "0")
                .build();
    }


}
