package com.hpbr.bosszhipin.event.analytics;

import android.text.TextUtils;

import com.hpbr.bosszhipin.event.AnalyticsFactory;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_DETAIL_BOSS_OTHER_JOBS;

import static com.hpbr.bosszhipin.event.AnalyticsAction.DETAIL_BOSS_CARD_CLICK;


public class Analytics717 {

    /**
     * 牛人在职位详情页点击同城招聘模块
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2287
     */
    public static void detailBossOtherJobsAnalytics(long bossId, long jobId, long expectId, String lid) {
        AnalyticsFactory.create()
                .action(ACTION_DETAIL_BOSS_OTHER_JOBS)
                .param("p", String.valueOf(bossId))
                .param("p2", String.valueOf(jobId))
                .param("p3", String.valueOf(expectId))
                .param("p4",  TextUtils.isEmpty(lid) ? "0" : lid)
                .build();
    }


    /**
     * 牛人在门店信息页中点击职位列表卡片
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2289
     */
    public static void detailBossardClickAnalytics(long bossId, long jobId, long expectId, String lid) {
        AnalyticsFactory.create()
                .action(DETAIL_BOSS_CARD_CLICK)
                .param("p", String.valueOf(bossId))
                .param("p2", String.valueOf(jobId))
                .param("p3", String.valueOf(expectId))
                .param("p4", TextUtils.isEmpty(lid) ? "0" : lid)
                .build();
    }


}
