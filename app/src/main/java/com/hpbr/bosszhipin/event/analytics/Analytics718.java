package com.hpbr.bosszhipin.event.analytics;

import com.hpbr.bosszhipin.event.AnalyticsFactory;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BOSS_CLICK_SCHEDULE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BOSS_CLICK_SCHEDULE_BAR;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BOSS_MARK_SCHEDULE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_BOSS_SCHEDULE_POP_SHUT;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_COMPANY_SCREEN_CLICK;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_GEEK_CLICK_SCHEDULE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_GEEK_MARK_SCHEDULE;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_GEEK_SCHEDULE_POP_BUTTON;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_GEEK_SCHEDULE_POP_SHUT;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_SCHEDULE_DETAIL_BOSS;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_SCHEDULE_DETAIL_GEEK;


public class Analytics718 {

    /**
     * 招聘反馈闭环埋点
     */

    /**
     * boss在F2点击进度列表标签
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2429
     */
    public static void bossClickScheduleAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_BOSS_CLICK_SCHEDULE)
                .build();
    }

    /**
     * boss点击“近七天招聘进度”
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2421
     */
    public static void bossClickScheduleBareAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_BOSS_CLICK_SCHEDULE_BAR)
                .build();
    }


    /**
     * boss点击牛人卡片标记进度
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2411
     *
     * @param expectId
     * @param progressContent   当前进度内容：已交换微信+未分类，已交换电话+未分类，已收到简历+未分类，刚刚/今天/X天前+已约面试/到面/面试通过/爽约
     * @param progressLableName 点击进度内容：1.已约面试2.到面3.面试通过4.不适合5.爽约
     * @param isExternalLable   1：外露标签 2：面板标签
     * @param isGuidePage       1：F2列表 2：F1引导弹窗
     */
    public static void bossMarkScheduleAnalytics(long expectId, String progressContent, int progressLableName, boolean isExternalLable, boolean isGuidePage) {
        AnalyticsFactory.create()
                .action(ACTION_BOSS_MARK_SCHEDULE)
                .param("p", String.valueOf(expectId))
                .param("p2", progressContent)
                .param("p3", progressLableName)
                .param("p4", isExternalLable ? "1" : "2")
                .param("p5", isGuidePage ? "2" : "1")
                .build();
    }

    /**
     * boss进度列表引导弹窗-关闭
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2424
     */
    public static void bossSchedulePopShutAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_BOSS_SCHEDULE_POP_SHUT)
                .build();
    }

    /**
     * boss进度卡片上点击查看牛人详情
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2418
     * isFromGuidePage: 来源：1：F2列表 2：F1引导弹窗
     */
    public static void scheduleDetailGeekAnalytics(String geekId, long expectId, boolean isFromGuidePage, String lid) {
        AnalyticsFactory.create()
                .action(ACTION_SCHEDULE_DETAIL_GEEK)
                .param("p", String.valueOf(geekId))
                .param("p2", String.valueOf(expectId))
                .param("p3", isFromGuidePage ? "2" : "1")
                .param("p4", lid)
                .build();
    }

    /**
     * 求职反馈闭环埋点
     */
    /**
     * 牛人在F2点击进度列表标签
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2431
     */
    public static void geekClickScheduleAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_GEEK_CLICK_SCHEDULE)
                .build();
    }

    /**
     * 牛人点击职位卡片标记进度
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2408
     *
     * @param jobId
     * @param oldProgressContent   当前进度内容：1.已发简历2.交换微信3.交换电话4.约面试5.已面试6.offer7.无结果
     * @param progressContent 点击进度内容：1.已发简历2.交换微信3.交换电话4.约面试5.已面试6.offer7.无结果
     * @param isExternalLable   1：外露标签2：面板标签
     * @param isGuidePage       1：F2列表2：F1引导弹窗
     */
    public static void actionGeekMarkScheduleAnalytics(long jobId, int oldProgressContent, int progressContent, boolean isExternalLable, boolean isGuidePage) {
        AnalyticsFactory.create()
                .action(ACTION_GEEK_MARK_SCHEDULE)
                .param("p", String.valueOf(jobId))
                .param("p2", oldProgressContent)
                .param("p3", progressContent)
                .param("p4", isExternalLable ? "1" : "2")
                .param("p5", isGuidePage ? "2" : "1")
                .build();
    }

    //牛人进度列表引导弹窗-关闭
    public static void geekSchedulePopShutAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_GEEK_SCHEDULE_POP_SHUT)
                .build();
    }

    /**
     * 牛人进度列表引导弹窗-点击以上暂无进展
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2422
     */
    public static void geekSchedulePopButtonAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_GEEK_SCHEDULE_POP_BUTTON)
                .build();
    }

    /**
     * 牛人进度卡片上点击查看职位详情
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2417
     * isFromGuidePage: 来源：1：F2列表 2：F1引导弹窗
     */
    public static void scheduleDetailBossAnalytics(String bossId, long jobId, boolean isFromGuidePage, String lid) {
        AnalyticsFactory.create()
                .action(ACTION_SCHEDULE_DETAIL_BOSS)
                .param("p", String.valueOf(bossId))
                .param("p2", String.valueOf(jobId))
                .param("p3", isFromGuidePage ? "2" : "1")
                .param("p4", lid)
                .build();
    }


    /**
     * 公司优化
     * 牛人在F4推荐行业公司列表点击筛选
     * https://etp.weizhipin.com/point-detail?SYS_CODE=2&docId=2409
     */
    public static void companyScreenClickAnalytics() {
        AnalyticsFactory.create()
                .action(ACTION_COMPANY_SCREEN_CLICK)
                .build();
    }
}
