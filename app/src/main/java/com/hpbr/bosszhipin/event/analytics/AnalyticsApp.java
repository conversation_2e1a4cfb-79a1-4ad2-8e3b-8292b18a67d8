package com.hpbr.bosszhipin.event.analytics;

import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;

/**
 * Created by <PERSON><PERSON> on 2023/6/14
 */
public class AnalyticsApp {

    /**
     * APP B/C主要页面停留时长
     *
     * @param pageType 页面位置（分B/C两端）：
     *                 C端：1-F1推荐 2-F2消息列表 3-我的页面 4-职位详情页 5-1V1 聊天详情页
     *                 B端：1-F1推荐牛人页面 2-搜索页 3-F2消息列表 4-我的页面 5-1V1聊天详情页 6-牛人简历详情页
     * @param timeMilliSeconds 停留时长(ms)
     */
    public static void appPageDwellTime(int pageType, long timeMilliSeconds) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_APP_PAGE_DWELLTIME)
                .param("p", pageType)
                .param("p2", timeMilliSeconds)
                .build();
    }

    public static void appPageDwellTime(int pageType, String securityId, long timeMilliSeconds) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_APP_PAGE_DWELLTIME)
                .param("p", pageType)
                .param("p2", timeMilliSeconds)
                .secId(securityId)
                .debug()
                .build();
    }
}
