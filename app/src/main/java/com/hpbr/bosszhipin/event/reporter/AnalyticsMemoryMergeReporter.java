package com.hpbr.bosszhipin.event.reporter;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;

import com.bszp.kernel.logic.db.entitiy.AnalyticsBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ApmActions;
import com.hpbr.bosszhipin.gray.bean.AnalyticsStrategyBean;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.StatisticsRequest;
import net.bosszhipin.api.StatisticsResponse;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class AnalyticsMemoryMergeReporter implements IAnalyticsReporter {
    private static final String TAG = "AnalyticsMergeReporter";
    private static final int REPORT_LIMITATION = 50; // 条数
    private static final int MAX_BEAN_SIZE = 10_000; // 20kb

    private final long mergeInterval; //任务延迟毫秒数，后期会改为动态
    private final LinkedBlockingDeque<AnalyticsBean> cachedData;
    private final int capacityThresholdMin; // 连续上报 处理下限
    private final int capacityThresholdMax; // 连续上报 触发上限
    private final int finalCapacity;

    private final AtomicBoolean isDelaying = new AtomicBoolean(false);
    private final AtomicBoolean isReportDropEvent = new AtomicBoolean(true);
    private final AtomicBoolean isReportBigAction = new AtomicBoolean(true);
    private final AtomicBoolean isReportOverLimit = new AtomicBoolean(true);
    private final AtomicBoolean isReportError = new AtomicBoolean(true);

    private final List<AnalyticsBean> eventList = new ArrayList<>();
    private final StringBuilder bigAction = new StringBuilder();

    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
        @SuppressLint("BZL-Thread")
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "AnalyticsMemory");
        }
    });

    public AnalyticsMemoryMergeReporter(int analyticsMergeInterval, int analyticsCapacity, int reportThresholdMin, int reportThresholdMax) {
        this.mergeInterval = analyticsMergeInterval > 0 ? analyticsMergeInterval : AnalyticsStrategyBean.INTERVAL_DEFAULT;
        this.finalCapacity = analyticsCapacity > 0 ? analyticsCapacity : AnalyticsStrategyBean.CAPACITY_DEFAULT;
        int finalReportThresholdMin = reportThresholdMin > 0 ? reportThresholdMin : AnalyticsStrategyBean.REPORT_THRESHOLD_PERCENTAGE_MIN_DEFAULT;
        int finalReportThresholdMax = reportThresholdMax > 0 ? reportThresholdMax : AnalyticsStrategyBean.REPORT_THRESHOLD_PERCENTAGE_MAX_DEFAULT;
        this.capacityThresholdMin = (int) (finalCapacity / 100F * finalReportThresholdMin);
        this.capacityThresholdMax = (int) (finalCapacity / 100F * finalReportThresholdMax);
        this.cachedData = new LinkedBlockingDeque<>(finalCapacity);
    }


    @Override
    public void commitAction(@NonNull AnalyticsBean bean) {
        if (!cachedData.offerLast(bean)) { // 当队列已满时，丢弃最旧的元素
            cachedData.pollFirst();
            cachedData.offerLast(bean);
            if (isReportDropEvent.compareAndSet(true, false)) {
                //发生丢弃时，每次冷启动仅上报一次，除非线程池挂了，不应该发生，这里做个上报
                int currentSize = cachedData.size();
                TLog.error(TAG, "commitAction drop, finalCapacity =%d , currentSize = %d, isShutdown = %b", finalCapacity, currentSize, executor.isShutdown());
                ApmAnalyzer.create()
                        .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_DROP)
                        .p3(String.valueOf(finalCapacity))
                        .p4(String.valueOf(currentSize))
                        .p5(String.valueOf(executor.isShutdown()))
                        .report();
            }
        }
        dispatch(true);
    }

    @Override
    public void commitActionNow() {
        dispatch(false);
    }

    private void dispatch(boolean allowDelay) {
        if (isDelaying.get() || executor.isShutdown()) {
//            TLog.debug(TAG, "dispatch allowDelay = %b, ignore", allowDelay);
            return;
        }

        isDelaying.set(true);
        if (allowDelay) {
            executor.schedule(task, mergeInterval, TimeUnit.MILLISECONDS);
        } else {
            executor.submit(task);
        }
    }

    private final Runnable task = () -> {
        isDelaying.set(false);
        try {
            doReport();
        } catch (Exception e) {
            TLog.error(TAG, e, "report error");
        }
    };

    @SuppressLint("BZL-ThreadSleep")
    private void doReport() {
        //当堆积容量达到80%时，本次批量操作，一次性将容量降低到30%以下
        if (cachedData.size() > capacityThresholdMax) {
            if (isReportOverLimit.compareAndSet(true, false)) {
                //发生丢弃时，每次冷启动仅上报一次，除非线程池挂了，不应该发生，这里做个上报
                int currentSize = cachedData.size();
                TLog.error(TAG, "doReport over limit finalCapacity =%d , currentSize = %d", finalCapacity, currentSize);
                ApmAnalyzer.create()
                        .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_OVER_LIMIT)
                        .p3(String.valueOf(finalCapacity))
                        .p4(String.valueOf(currentSize))
                        .report();
            }

            do {
                safeRealReport();
            } while (cachedData.size() > capacityThresholdMin);
        } else {
            safeRealReport();
        }
    }


    private void safeRealReport() {
        try {
            doRealReport();
        } catch (Exception e) {
            TLog.error(TAG, e, "safeRealReport");
            if (isReportError.compareAndSet(true, false)) {
                ApmAnalyzer.create()
                        .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_ERROR)
                        .p3(Log.getStackTraceString(e))
                        .report();
            }
        } finally {
            eventList.clear();
            bigAction.setLength(0);
        }
    }

    @WorkerThread
    private void doRealReport() {
        TLog.debug(TAG, "doRealReport");
        cachedData.drainTo(eventList, REPORT_LIMITATION);
        if (eventList.isEmpty()) {
            return;
        }

        JSONArray jsonArray = new JSONArray();
        for (AnalyticsBean detailBean : eventList) {
            if (detailBean == null || TextUtils.isEmpty(detailBean.action)) continue;
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("action", detailBean.action);
                jsonObject.put("time", detailBean.time);
                Map<String, String> params = detailBean.params;
                int size = 0;
                if (params != null) {
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        jsonObject.put(entry.getKey(), entry.getValue());
                        if (entry.getValue() != null) {
                            size += entry.getValue().length();
                        }
                    }
                }

                //region 上报并跳过过大（超过10Kb）的打点数据
                try {
                    if (isReportBigAction.get() && size > MAX_BEAN_SIZE) {
                        bigAction.append(detailBean.action).append("=").append(size).append(",");
                    }
                } catch (Exception ignored) {

                }
                //endregion

                jsonArray.put(jsonObject);
            } catch (Exception ignored) {
            }
        }

        if (isReportBigAction.get() && bigAction.length() > 0) {
            isReportBigAction.set(false);
            ApmAnalyzer.create()
                    .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_TOO_LARGE_ANALYTICS_BEAN)
                    .param("p2", bigAction.toString())
                    .report();
        }
        final String dataJson = jsonArray.toString();
        StatisticsRequest request = new StatisticsRequest(new SimpleApiRequestCallback<StatisticsResponse>() {
        });
        request.data = dataJson;
        request.execute();
    }
}
