package com.hpbr.bosszhipin.event.reporter;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bszp.kernel.logic.db.entitiy.AnalyticsBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ApmActions;
import com.hpbr.bosszhipin.gray.bean.AnalyticsStrategyBean;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.StatisticsRequest;
import net.bosszhipin.api.StatisticsResponse;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class AnalyticsMemorySimpleMergeReporter implements IAnalyticsReporter {
    private static final String TAG = "AnalyticsMergeReporter";
    private static final int REPORT_LIMITATION = 50; // 条数
    private static final int MAX_BEAN_SIZE = 10_000; // 20kb

    private final long mergeInterval; //任务延迟毫秒数，后期会改为动态
    private List<AnalyticsBean> cachedData;

    private final AtomicBoolean isDelaying = new AtomicBoolean(false);
    private final AtomicBoolean isReportBigAction = new AtomicBoolean(true);

    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
        @SuppressLint("BZL-Thread")
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "AnalyticsMemory");
        }
    });

    public AnalyticsMemorySimpleMergeReporter(int analyticsMergeInterval) {
        this.mergeInterval = analyticsMergeInterval > 0 ? analyticsMergeInterval : AnalyticsStrategyBean.INTERVAL_DEFAULT;
    }


    @Override
    public void commitAction(@NonNull AnalyticsBean bean) {
        synchronized (this) {
            if (cachedData == null){
                cachedData = new ArrayList<>();
            }
            cachedData.add(bean);
        }
        TLog.debug(TAG, "commitAction action = %s", bean.action);
        dispatch(true);
    }

    @Override
    public void commitActionNow() {
        dispatch(false);
    }

    private void dispatch(boolean allowDelay) {
        if (isDelaying.get()) {
            TLog.debug(TAG, "dispatch allowDelay = %b, ignore", allowDelay);
            return;
        } else {
            TLog.debug(TAG, "dispatch allowDelay = %b", allowDelay);
        }
        if (executor.isShutdown()) {
            TLog.debug(TAG, "isShutdown, ignore");
            return;
        }

        isDelaying.set(true);
        if (allowDelay) {
            executor.schedule(task, mergeInterval, TimeUnit.MILLISECONDS);
        } else {
            executor.submit(task);
        }
    }

    private final Runnable task = () -> {
        isDelaying.set(false);
        try {
            doRealReport();
        } catch (Exception e) {
            TLog.error(TAG, e, "report error");
        }
    };


    private void doRealReport() {
        TLog.debug(TAG, "doRealReport");
        List<AnalyticsBean> eventList = null;
        synchronized (this) {
            eventList = cachedData;
            cachedData = new ArrayList<>();
        }

        if (LList.isEmpty(eventList)) {
            return;
        }

        final List<AnalyticsBean> realEventList = new ArrayList<>();
        StringBuilder bigAction = new StringBuilder();

        JSONArray jsonArray = new JSONArray();
        for (AnalyticsBean detailBean : eventList) {
            if (detailBean == null || TextUtils.isEmpty(detailBean.action)) continue;
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("action", detailBean.action);
                jsonObject.put("time", detailBean.time);
                Map<String, String> params = detailBean.params;
                int size = 0;
                if (params != null) {
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        jsonObject.put(entry.getKey(), entry.getValue());
                        if (entry.getValue() != null) {
                            size += entry.getValue().length();
                        }
                    }
                }

                //region 上报并跳过过大（超过10Kb）的打点数据
                try {
//                            int size = jsonObject.toString().getBytes(StandardCharsets.UTF_8).length;
                    if (isReportBigAction.get() && size > MAX_BEAN_SIZE) {
                        bigAction.append(detailBean.action).append("=").append(size).append(",");
                    }
                } catch (Exception ignored) {

                }
                //endregion

                realEventList.add(detailBean);
                jsonArray.put(jsonObject);
            } catch (Exception ignored) {
            }
        }

        if (isReportBigAction.get() && bigAction.length() > 0) {
            isReportBigAction.set(false);
            ApmAnalyzer.create()
                    .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_TOO_LARGE_ANALYTICS_BEAN)
                    .param("p2", bigAction.toString())
                    .report();
        }
        final String dataJson = jsonArray.toString();
        StatisticsRequest request = new StatisticsRequest(new SimpleApiRequestCallback<StatisticsResponse>() {

        });
        request.data = dataJson;
        request.execute();
    }
}
