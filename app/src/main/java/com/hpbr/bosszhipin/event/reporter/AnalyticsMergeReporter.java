package com.hpbr.bosszhipin.event.reporter;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bszp.kernel.logic.db.AppDatabase;
import com.bszp.kernel.logic.db.entitiy.AnalyticsBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ApmActions;
import com.hpbr.bosszhipin.gray.bean.AnalyticsStrategyBean;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.StatisticsRequest;
import net.bosszhipin.api.StatisticsResponse;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

public class AnalyticsMergeReporter implements IAnalyticsReporter {
    private static final String TAG = "AnalyticsMergeReporter";
    private static final int MAX_FAILURE_COUNT = 3;
    private static final int REPORT_LIMITATION = 50; // 条数
    private static final int MAX_BEAN_SIZE = 10_000; // 20kb
    private long mergeInterval = AnalyticsStrategyBean.INTERVAL_DEFAULT; //任务延迟毫秒数，后期会改为动态

    private final AtomicInteger failCount = new AtomicInteger(0);
    private final AtomicBoolean isDelaying = new AtomicBoolean(false);
    private final AtomicBoolean isReportBigAction = new AtomicBoolean(true);
    private final AtomicReference<String> jsonFail = new AtomicReference<>();
    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(new ThreadFactory() {
        @SuppressLint("BZL-Thread")
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "AnalyticsMerge");
        }
    });

    public AnalyticsMergeReporter(int analyticsMergeInterval){
        if (analyticsMergeInterval > 0){
            mergeInterval = analyticsMergeInterval;
        }
    }


    @Override
    public void commitAction(@NonNull AnalyticsBean bean) {
        AppDatabase.get().getAnalyticsDao().insert(bean);
        TLog.debug(TAG, "commitAction action = %s", bean.action);
        dispatch(true);
    }

    @Override
    public void commitActionNow() {
        dispatch(false);
    }

    private void dispatch(boolean allowDelay) {
        if (isDelaying.get()) {
            TLog.debug(TAG, "dispatch allowDelay = %b, ignore", allowDelay);
            return;
        } else {
            TLog.debug(TAG, "dispatch allowDelay = %b", allowDelay);
        }
        if (executor.isShutdown()) {
            TLog.debug(TAG, "isShutdown, ignore");
            return;
        }

        isDelaying.set(true);
        if (allowDelay) {
            executor.schedule(task, mergeInterval, TimeUnit.MILLISECONDS);
        } else {
            executor.submit(task);
        }
    }

    private final Runnable task = () -> {
        isDelaying.set(false);
        try {
            doRealReport();
        } catch (Exception e) {
            TLog.error(TAG, e, "report error");
        }
    };


    private synchronized void doRealReport() {
        TLog.debug(TAG, "doRealReport");
        List<AnalyticsBean> eventList = AppDatabase.get()
                .getAnalyticsDao()
                .getLimitAnalytics(REPORT_LIMITATION);

        if (eventList == null || eventList.isEmpty()) {
            return;
        }

        AppDatabase.get().getAnalyticsDao().delete(eventList);

        final List<AnalyticsBean> realEventList = new ArrayList<>();

        StringBuilder bigAction = new StringBuilder();

        JSONArray jsonArray = new JSONArray();
        for (AnalyticsBean detailBean : eventList) {
            if (detailBean == null || TextUtils.isEmpty(detailBean.action)) continue;
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("action", detailBean.action);
                jsonObject.put("time", detailBean.time);
                Map<String, String> params = detailBean.params;
                int size = 0;
                if (params != null) {
                    for (Map.Entry<String, String> entry : params.entrySet()) {
                        jsonObject.put(entry.getKey(), entry.getValue());
                        if (entry.getValue() != null) {
                            size += entry.getValue().length();
                        }
                    }
                }

                //region 上报并跳过过大（超过10Kb）的打点数据
                try {
//                            int size = jsonObject.toString().getBytes(StandardCharsets.UTF_8).length;
                    if (isReportBigAction.get() && size > MAX_BEAN_SIZE) {
                        bigAction.append(detailBean.action).append("=").append(size).append(",");
                    }
                } catch (Exception ignored) {

                }
                //endregion

                realEventList.add(detailBean);
                jsonArray.put(jsonObject);
            } catch (Exception ignored) {
            }
        }

        if (isReportBigAction.get() && bigAction.length() > 0) {
            isReportBigAction.set(false);
            ApmAnalyzer.create()
                    .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_TOO_LARGE_ANALYTICS_BEAN)
                    .param("p2", bigAction.toString())
                    .report();
        }
        final String dataJson = jsonArray.toString();
        StatisticsRequest request = new StatisticsRequest(new SimpleApiRequestCallback<StatisticsResponse>() {

            private void resetCount() {
                jsonFail.set(null);
                failCount.set(0);
            }

            @Override
            public void handleInChildThread(ApiData<StatisticsResponse> data) {
                resetCount();
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                TLog.error("AnalyticsFactory", "handleErrorInChildThread==== reason = [%s] length = %d  jsonFail = %s failCount = %d", reason, realEventList.size(), jsonFail.get(), failCount.get());
                if (failCount.get() < MAX_FAILURE_COUNT) {
                    if (reason.getErrCode() > 0) { // reason.getErrCode() > 0防止网络异常时过滤埋点，这里只处理服务端返回异常
                        String failKey = getFailKey(dataJson);
                        if (TextUtils.equals(failKey, jsonFail.get())) {
                            failCount.incrementAndGet();
                        } else {
                            resetCount();
                        }
                        jsonFail.set(failKey);
                    }
                    saveToDb(realEventList); // 上报失败的打点数据重新回滚入库
                } else {
                    resetCount();
                    ApmAnalyzer.create()
                            .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_FAILURE_ANALYTICS_BEAN)
                            .p2(dataJson)
                            .p3(String.valueOf(reason))
                            .report();
                }
            }
        });
        request.data = dataJson;
        request.execute();
    }


    private synchronized void saveToDb(List<AnalyticsBean> list) {
        AppDatabase.get().getAnalyticsDao().inserts(list);
    }


    private String getFailKey(String string) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            // 计算字符串的哈希值
            return Arrays.toString(md.digest(string.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Arrays.toString(string.getBytes(StandardCharsets.UTF_8));
    }
}
