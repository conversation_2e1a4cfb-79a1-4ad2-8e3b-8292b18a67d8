package com.hpbr.bosszhipin.event.reporter;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bszp.kernel.logic.db.AppDatabase;
import com.bszp.kernel.logic.db.entitiy.AnalyticsBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.ApmActions;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.StatisticsRequest;
import net.bosszhipin.api.StatisticsResponse;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;

public class DefaultAnalyticsReporter implements IAnalyticsReporter {

    private static final int MAX_FAILURE_COUNT = 3;
    private static final int REPORT_LIMITATION = 50; // 条数
    private static final int MAX_BEAN_SIZE = 10_000; // 20kb

    private boolean isReportBigAction = true;
    private String jsonFail; //埋点记录Key
    private int failCount = 0;
    private final AtomicBoolean isPosting = new AtomicBoolean(false);
    private final List<AnalyticsBean> cachedData = new ArrayList<>();

    private ExecutorService executor = Executors.newSingleThreadExecutor(new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "Analytics");
        }
    });


    @Override
    public void commitAction(@NonNull AnalyticsBean bean) {
        if (isPosting.get()) {
            cachedData.add(bean);
        } else {
            AppDatabase.get().getAnalyticsDao().insert(bean);
            commitActionNow();
        }
    }

    @Override
    public void commitActionNow() {
        if (executor.isShutdown()) return;
        isPosting.set(true);
        executor.submit(new Runnable() {
            @Override
            public void run() {
                List<AnalyticsBean> eventList = AppDatabase.get()
                        .getAnalyticsDao()
                        .getLimitAnalytics(REPORT_LIMITATION);

                if (eventList == null || eventList.isEmpty()) {
                    isPosting.set(false);
                    return;
                }

//                db.delete(eventList);
                AppDatabase.get().getAnalyticsDao().delete(eventList);

                final List<AnalyticsBean> realEventList = new ArrayList<>();

                StringBuilder bigAction = new StringBuilder();

                JSONArray jsonArray = new JSONArray();
                for (AnalyticsBean detailBean : eventList) {
                    if (detailBean == null || TextUtils.isEmpty(detailBean.action)) continue;
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.put("action", detailBean.action);
                        jsonObject.put("time", detailBean.time);
                        Map<String, String> params = detailBean.params;
                        int size = 0;
                        if (params != null) {
                            for (Map.Entry<String, String> entry : params.entrySet()) {
                                jsonObject.put(entry.getKey(), entry.getValue());
                                if (entry.getValue() != null) {
                                    size += entry.getValue().length();
                                }
                            }
                        }

                        //region 上报并跳过过大（超过10Kb）的打点数据
                        try {
//                            int size = jsonObject.toString().getBytes(StandardCharsets.UTF_8).length;
                            if (isReportBigAction && size > MAX_BEAN_SIZE) {
                                bigAction.append(detailBean.action).append("=").append(size).append(",");
                            }
                        } catch (Exception ignored) {

                        }
                        //endregion

                        realEventList.add(detailBean);
                        jsonArray.put(jsonObject);
                    } catch (Exception ignored) {
                    }
                }

                if (isReportBigAction && bigAction.length() > 0) {
                    isReportBigAction = false;
                    ApmAnalyzer.create()
                            .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_TOO_LARGE_ANALYTICS_BEAN)
                            .param("p2", bigAction.toString())
                            .report();
                }
                final String dataJson = jsonArray.toString();
                StatisticsRequest request = new StatisticsRequest(new SimpleApiRequestCallback<StatisticsResponse>() {

                    /**
                     * 触发下一次上传逻辑
                     */
                    public void executeNext() {
                        isPosting.set(false);
                        dealCacheData();
                    }

                    private void resetCount() {
                        jsonFail = null;
                        failCount = 0;
                    }

                    @Override
                    public void handleInChildThread(ApiData<StatisticsResponse> data) {
                        resetCount();
                        executeNext();
                    }

                    @Override
                    public void handleErrorInChildThread(ErrorReason reason) {
                        TLog.error("AnalyticsFactory", "handleErrorInChildThread==== reason = [%s] length = %d  jsonFail = %s failCount = %d", reason, realEventList.size(), jsonFail, failCount);
                        if (failCount < MAX_FAILURE_COUNT) {
                            if (reason.getErrCode() > 0) { // reason.getErrCode() > 0防止网络异常时过滤埋点，这里只处理服务端返回异常
                                String failKey = getFailKey(dataJson);
                                if (TextUtils.equals(failKey, jsonFail)) {
                                    failCount++;
                                } else {
                                    resetCount();
                                }
                                jsonFail = failKey;
                            }
                            saveToDb(realEventList); // 上报失败的打点数据重新回滚入库
                        } else {
                            resetCount();
                            ApmAnalyzer.create()
                                    .action(ApmActions.ACTION_STATISTICS, ApmActions.TYPE_FAILURE_ANALYTICS_BEAN)
                                    .p2(dataJson)
                                    .p3(String.valueOf(reason))
                                    .report();
                        }
                        executeNext();
                    }
                });
                request.data = dataJson;
                request.execute();
            }
        });
    }


    private synchronized void dealCacheData() {
        List<AnalyticsBean> tempList = null;
        if (!cachedData.isEmpty()) {
            tempList = new ArrayList<>(cachedData);
            cachedData.clear();
        }
        if (tempList != null && !tempList.isEmpty()) {
            saveToDb(tempList);
            commitActionNow();
        }
    }

    private synchronized void saveToDb(List<AnalyticsBean> list) {
        LList.removeAllNullElements(list);
        if (LList.isNotEmpty(list)){
            AppDatabase.get().getAnalyticsDao().inserts(list);
        }
    }


    private String getFailKey(String string) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            // 计算字符串的哈希值
            return Arrays.toString(md.digest(string.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Arrays.toString(string.getBytes(StandardCharsets.UTF_8));
    }
}
