package com.hpbr.bosszhipin.eventbus;

/**
 * Created by wa<PERSON><PERSON> on 16/9/26.
 */
public class EventBusMessage {

    private int type;

    private int errorCode;

    private String errorMsg;

    private Object data;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public static EventBusMessage obtain(int type){
        return obtain(type,null,0,null);
    }

    public static EventBusMessage obtain(int type,Object data){
        return obtain(type,data,0,null);
    }

    public static EventBusMessage obtain(int type,int errorCode,String errorMsg){
        return obtain(type,null,errorCode,errorMsg);
    }


    public static EventBusMessage obtain(int type,Object data,int errorCode,String errorMsg){
        EventBusMessage message = new EventBusMessage();
        message.setData(data);
        message.setErrorCode(errorCode);
        message.setType(type);
        message.setErrorMsg(errorMsg);
        return message;

    }
}
