package com.hpbr.bosszhipin.eventbus.eventmanager;


import android.os.Looper;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

/**
 * YaLin
 * 2016/11/16.
 */

public class EventManager {
    public interface EventListener<T> {
        void onEventInMainThread(T event);
    }

    private static EventManager instance;

    public static EventManager getInstance() {
        if (instance == null) {
            instance = new EventManager();
        }
        return instance;
    }

    private Map<Class, HashSet<EventListener>> listenerMap = new HashMap<>();

    /**
     * 注册事件，必须在主线程中调用
     *
     * @param eventClass 事件类型class
     * @param listener   事件监听
     * @param <T>        事件类型
     */
    public <T> void registerEvent(Class<T> eventClass, EventListener<T> listener) {
        checkMainThread();
        HashSet<EventListener> set;
        if (listenerMap.containsKey(eventClass)) {
            set = listenerMap.get(eventClass);
        } else {
            set = new HashSet<>();
            listenerMap.put(eventClass, set);
        }
        set.add(listener);
    }

    /**
     * 取消注册事件，必须在主线程中调用
     */
    public <T> void unregisterEvent(Class<T> eventClass, EventListener<T> listener) {
        checkMainThread();
        if (listenerMap.containsKey(eventClass)) {
            HashSet<EventListener> set = listenerMap.get(eventClass);
            set.remove(listener);
            if (set.size() == 0) {
                listenerMap.remove(eventClass);
            }
        }
    }

    /**
     * 通知事件，通知结果也会在主线程中执行。必须在主线程中调用
     *
     * @param event 事件对象
     */
    @SuppressWarnings("unchecked")
    public <T> void notifyFromMainThread(T event) {
        checkMainThread();
        Class clazz = event.getClass();
        HashSet<EventListener> set = listenerMap.get(clazz);
        if (set != null) {
            for (EventListener listener : set) {
                if (listener != null) {
                    listener.onEventInMainThread(event);
                }
            }
        }
    }

    private static void checkMainThread() {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            throw new IllegalStateException("You must use EventManager in main thread.");
        }
    }

    private EventManager() {
    }
}
