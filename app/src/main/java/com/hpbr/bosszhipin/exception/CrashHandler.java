package com.hpbr.bosszhipin.exception;

import android.content.Context;
import android.util.Log;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.monch.lbase.util.L;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;

import java.util.concurrent.TimeoutException;

/**
 * Created by monch on 15/4/24.
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

    private static final String TAG = "CrashHandler";

    private CrashHandler() {
    }

    private static CrashHandler instance = new CrashHandler();

    public static CrashHandler getInstance() {
        return instance;
    }

    private Thread.UncaughtExceptionHandler uncaughtExceptionHandler;

    private Context context;

    public void init(Context context) {
        this.context = context;
        uncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        L.e(TAG, "程序异常", ex);
        TLog.important(TAG, ex, "UserId=%d, UserRole=%d", UserManager.getUID(),
                UserManager.getUserRole().get());
        CrashBean bean = new CrashBean();
        bean.time = System.currentTimeMillis();
        bean = handlerInfo(bean);
        bean = handlerException(bean, thread, ex);
        App.get().db().save(bean);
        if (uncaughtExceptionHandler != null && thread != null && ex != null) {
            //忽略 finalize() timed out after 10 seconds 问题
            //https://cloud.tencent.com/developer/article/1637181
            if (thread.getName().equals("FinalizerWatchdogDaemon") && ex instanceof TimeoutException) {
                ApmAnalyzer.create().action("action_error", "FinalizerWatchdogDaemonError")
                        .p2(ex.getMessage())
                        .p3(Log.getStackTraceString(ex))
                        .report();
            } else {
                uncaughtExceptionHandler.uncaughtException(thread, ex);
            }
        } else {
            T.ss("程序出现未知异常，即将自动退出");
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }

    private CrashBean handlerInfo(CrashBean bean) {
        bean.appVersionName = MobileUtil.getShowVersionName();
        bean.systemDevice = MobileUtil.getDeviceType();
        bean.systemVersion = MobileUtil.getSystemVersion();
        return bean;
    }

    private CrashBean handlerException(CrashBean bean, Thread thread, Throwable ex) {
        String threadName = Thread.currentThread().getName();
        if (thread != null) {
            threadName = thread.getName();
        }
        bean.currentThreadName = threadName;
        bean.crashContent = MException.getErrorLog(ex);
        return bean;
    }

}
