package com.hpbr.bosszhipin.get.net.bean;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.annotations.Expose;
import com.monch.lbase.util.LText;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.bean.BaseServerBean;

/**
 * created by tong<PERSON><PERSON><PERSON>.
 * date: 2020-11-02
 * time: 13:52
 * description:推荐搜索词【816】
 */
public class SearchKeywordBean extends BaseServerBean {
    private static final long serialVersionUID = -7506846877247457605L;
    public String formId;
    public String showWord;
    public String searchWord;
    public int tab;//1 职位，2 公司，3 内容
    public int type;//预置词类型

    @Expose(serialize = false, deserialize = false)
    public static final int TYPE_POSITION = 1;/*职位*/
    @Expose(serialize = false, deserialize = false)
    public static final int TYPE_COMPANY = 2;/*公司*/
    @Expose(serialize = false, deserialize = false)
    public static final int TYPE_CONTENT = 3;/*内容*/

    @Nullable
    public String getRealShowWord() {
        String realShowWord = null;
        if (!LText.empty(showWord)) {
            realShowWord = showWord;
        } else if (!LText.empty(searchWord)) {
            realShowWord = searchWord;
        }
        return realShowWord;
    }

    public String toJson() {
        return GsonUtils.getGson().toJson(this);
    }

    @Nullable
    public static SearchKeywordBean fromJson(@NonNull String json) {
        return GsonUtils.getGson().fromJson(json, SearchKeywordBean.class);
    }

}
