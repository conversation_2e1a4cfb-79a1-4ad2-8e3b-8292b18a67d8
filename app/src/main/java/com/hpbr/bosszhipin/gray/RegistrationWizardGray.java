package com.hpbr.bosszhipin.gray;

import android.app.Activity;
import android.content.Context;

import com.hpbr.bosszhipin.module.position.entity.post.JobExtraParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;

/**
 * Author: <PERSON><PERSON>ou
 * Date: 2018/9/29.
 * 【6.14】注册流程灰度跳转管理
 * 【6.15】选择求职意向的职位类型支持多选的灰度策略
 */
public class RegistrationWizardGray {

    /**
     * 开启Boss注册流程
     */
    public static void startBossRegistration(Activity activity) {
        BossPageRouter.jumpToBossCreateInfoActivity(activity);
    }

    /**
     * 开启Boss完善首个职位流程
     */
    public static void startBossFirstJobCompletion(Context context, JobExtraParamBean bean) {
        BossPageRouter.jumpToFirstPosition(context, bean);
    }
}
