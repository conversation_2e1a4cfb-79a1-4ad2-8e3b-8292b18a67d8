package com.hpbr.bosszhipin.imageloader;

import android.net.Uri;

import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;

import com.facebook.common.internal.Objects;
import com.facebook.common.time.MonotonicClock;
import com.facebook.common.time.RealtimeSinceBootClock;
import com.facebook.common.util.UriUtil;
import com.facebook.imagepipeline.image.EncodedImage;
import com.facebook.imagepipeline.producers.BaseNetworkFetcher;
import com.facebook.imagepipeline.producers.BaseProducerContextCallbacks;
import com.facebook.imagepipeline.producers.Consumer;
import com.facebook.imagepipeline.producers.FetchState;
import com.facebook.imagepipeline.producers.NetworkFetcher;
import com.facebook.imagepipeline.producers.ProducerContext;
import com.techwolf.lib.tlog.TLog;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * copy
 * com.facebook.imagepipeline.producers.HttpUrlConnectionNetworkFetcher
 */
public class BzlCustomNetworkFetcher extends BaseNetworkFetcher<BzlCustomNetworkFetcher.BzlCustomNetworkFetchState> {

    public static final String TAG = "BzlCustomNetworkFetcher";
    private static final String QUEUE_TIME = "queue_time";
    private static final String FETCH_TIME = "fetch_time";
    private static final String TOTAL_TIME = "total_time";
    private static final String IMAGE_SIZE = "image_size";
    private static final int NUM_NETWORK_THREADS = 3;
    private static final int MAX_REDIRECTS = 5;
    public static final int HTTP_TEMPORARY_REDIRECT = 307;
    public static final int HTTP_PERMANENT_REDIRECT = 308;
    public static final int HTTP_DEFAULT_TIMEOUT = 30000;
    private static final int MAX_RETRY_COUNT = 1;

    private int mHttpConnectionTimeout;
    @Nullable
    private String mUserAgent;
    @Nullable
    private final Map<String, String> mRequestHeaders;
    private final ExecutorService mExecutorService;
    private final MonotonicClock mMonotonicClock;

    private final RequestImageUrlCallback requestImageUrlCallback;

    public BzlCustomNetworkFetcher() {
        this(null, null, RealtimeSinceBootClock.get(), null);
    }

    public BzlCustomNetworkFetcher(int httpConnectionTimeout) {
        this(null, null, RealtimeSinceBootClock.get(), null);
        this.mHttpConnectionTimeout = httpConnectionTimeout;
    }

    public BzlCustomNetworkFetcher(RequestImageUrlCallback requestImageUrlCallback) {
        this(null, null, RealtimeSinceBootClock.get(), requestImageUrlCallback);
    }

    @VisibleForTesting
    BzlCustomNetworkFetcher(@Nullable String userAgent, @Nullable Map<String, String> requestHeaders, MonotonicClock monotonicClock, RequestImageUrlCallback requestImageUrlCallback) {
        this.mExecutorService = Executors.newFixedThreadPool(3);
        this.mMonotonicClock = monotonicClock;
        this.mRequestHeaders = requestHeaders;
        this.mUserAgent = userAgent;
        this.requestImageUrlCallback = requestImageUrlCallback;
    }

    @Override
    public BzlCustomNetworkFetchState createFetchState(Consumer<EncodedImage> consumer, ProducerContext context) {
        return new BzlCustomNetworkFetchState(consumer, context);
    }

    @Override
    public void fetch(final BzlCustomNetworkFetchState fetchState, final NetworkFetcher.Callback callback) {
        fetchState.submitTime = this.mMonotonicClock.now();
        final Future<?> future = this.mExecutorService.submit(new Runnable() {
            public void run() {
                BzlCustomNetworkFetcher.this.fetchSync(fetchState, callback);
            }
        });
        fetchState.getContext().addCallbacks(new BaseProducerContextCallbacks() {
            public void onCancellationRequested() {
                if (future.cancel(false)) {
                    callback.onCancellation();
                }

            }
        });
    }

    @VisibleForTesting
    void fetchSync(BzlCustomNetworkFetchState fetchState, NetworkFetcher.Callback callback) {
        HttpURLConnection connection = null;
        InputStream is = null;
        try {
            connection = this.downloadFrom(fetchState.getUri(), MAX_RETRY_COUNT, MAX_REDIRECTS);
            fetchState.responseTime = this.mMonotonicClock.now();
            if (connection != null) {
                is = connection.getInputStream();
                callback.onResponse(is, -1);
            }
        } catch (IOException var14) {
            callback.onFailure(var14);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException var13) {
                }
            }

            if (connection != null) {
                connection.disconnect();
            }

        }

    }

    private HttpURLConnection downloadFrom(Uri uri, int retryCount, int maxRedirects) throws IOException {
        HttpURLConnection connection = openConnectionTo(uri);
        if (this.mUserAgent != null) {
            connection.setRequestProperty("User-Agent", this.mUserAgent);
        }

        if (this.mRequestHeaders != null) {
            Iterator var4 = this.mRequestHeaders.entrySet().iterator();

            while (var4.hasNext()) {
                Map.Entry<String, String> entry = (Map.Entry) var4.next();
                connection.setRequestProperty((String) entry.getKey(), (String) entry.getValue());
            }
        }

        connection.setConnectTimeout(this.mHttpConnectionTimeout);
        int responseCode = connection.getResponseCode();
        if (isHttpSuccess(responseCode)) {
            return connection;
        } else if (isHttpRedirect(responseCode)) {
            String nextUriString = connection.getHeaderField("Location");
            connection.disconnect();
            Uri nextUri = nextUriString == null ? null : Uri.parse(nextUriString);
            String originalScheme = uri.getScheme();
            if (maxRedirects > 0 && nextUri != null && !Objects.equal(nextUri.getScheme(), originalScheme)) {
                return this.downloadFrom(nextUri, retryCount, maxRedirects - 1);
            } else {
                String message = maxRedirects == 0 ? error("URL %s follows too many redirects", uri.toString()) : error("URL %s returned %d without a valid redirect", uri.toString(), responseCode);
                throw new IOException(message);
            }
        } else if (requestImageUrlCallback != null && responseCode == HttpURLConnection.HTTP_FORBIDDEN && retryCount > 0) {
            //这块是新增的 start
            TLog.error(TAG, "Received 403 error. Retrying with new URL... %s", uri);
            connection.disconnect();
            String newUrl = requestImageUrlCallback.requestNewUrl(uri.toString());
            if (newUrl != null && uri != Uri.parse(newUrl)) {
                return this.downloadFrom(Uri.parse(newUrl), retryCount - 1, maxRedirects);
            } else {
                throw new IOException(String.format("Image URL %s returned HTTP code %d", uri.toString(), responseCode));
            }
            //这块是新增的 start
        } else {
            connection.disconnect();
            throw new IOException(String.format("Image URL %s returned HTTP code %d", uri.toString(), responseCode));
        }
    }

    @VisibleForTesting
    static HttpURLConnection openConnectionTo(Uri uri) throws IOException {
        URL url = UriUtil.uriToUrl(uri);
        return (HttpURLConnection) url.openConnection();
    }

    public void onFetchCompletion(BzlCustomNetworkFetchState fetchState, int byteSize) {
        fetchState.fetchCompleteTime = this.mMonotonicClock.now();
    }

    private static boolean isHttpSuccess(int responseCode) {
        return responseCode >= 200 && responseCode < 300;
    }

    private static boolean isHttpRedirect(int responseCode) {
        switch (responseCode) {
            case 300:
            case 301:
            case 302:
            case 303:
            case 307:
            case 308:
                return true;
            case 304:
            case 305:
            case 306:
            default:
                return false;
        }
    }

    private static String error(String format, Object... args) {
        return String.format(Locale.getDefault(), format, args);
    }

    public Map<String, String> getExtraMap(BzlCustomNetworkFetchState fetchState, int byteSize) {
        Map<String, String> extraMap = new HashMap(4);
        extraMap.put(QUEUE_TIME, Long.toString(fetchState.responseTime - fetchState.submitTime));
        extraMap.put(FETCH_TIME, Long.toString(fetchState.fetchCompleteTime - fetchState.responseTime));
        extraMap.put(TOTAL_TIME, Long.toString(fetchState.fetchCompleteTime - fetchState.submitTime));
        extraMap.put(IMAGE_SIZE, Integer.toString(byteSize));
        return extraMap;
    }

    Uri getNewUri(Uri uri) {
        return uri;
    }


    public static class BzlCustomNetworkFetchState extends FetchState {
        public long submitTime;
        public long responseTime;
        public long fetchCompleteTime;


        public BzlCustomNetworkFetchState(Consumer<EncodedImage> consumer, ProducerContext producerContext) {
            super(consumer, producerContext);
        }

    }
}
