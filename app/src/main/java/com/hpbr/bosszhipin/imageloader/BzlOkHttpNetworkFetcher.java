package com.hpbr.bosszhipin.imageloader;

import android.os.Looper;
import android.os.SystemClock;

import androidx.annotation.NonNull;

import com.facebook.imagepipeline.backends.okhttp3.OkHttpNetworkFetcher;
import com.facebook.imagepipeline.common.BytesRange;
import com.facebook.imagepipeline.producers.BaseProducerContextCallbacks;
import com.facebook.imagepipeline.producers.Consumer;
import com.facebook.imagepipeline.producers.NetworkFetcher;
import com.techwolf.lib.tlog.TLog;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.util.concurrent.Executor;

import javax.net.ssl.SSLHandshakeException;

import okhttp3.CacheControl;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * com.facebook.imagepipeline.backends.okhttp3.OkHttpNetworkFetcher
 */
public class BzlOkHttpNetworkFetcher extends OkHttpNetworkFetcher {
    public static final String TAG = "BzlOkHttpNetworkFetcher";
    private final Call.Factory mCallFactory;
    private final CacheControl mCacheControl;
    private final RequestImageUrlCallback mRequestImageUrlCallback;
    private Executor mCancellationExecutor;
    private boolean isSupportWebp = false;
    private static final int MAX_RETRY_COUNT = 1;

    public BzlOkHttpNetworkFetcher(OkHttpClient okHttpClient) {
        this(okHttpClient, okHttpClient.dispatcher().executorService(), null);
    }

    public BzlOkHttpNetworkFetcher(OkHttpClient okHttpClient, RequestImageUrlCallback requestImageUrlCallback) {
        this(okHttpClient, okHttpClient.dispatcher().executorService(), requestImageUrlCallback);
    }

    /**
     * @param callFactory          custom {@link Call.Factory} for fetching image from the network
     * @param cancellationExecutor executor on which fetching cancellation is performed if
     *                             cancellation is requested from the UI Thread
     */
    public BzlOkHttpNetworkFetcher(Call.Factory callFactory, Executor cancellationExecutor, RequestImageUrlCallback requestImageUrlCallback) {
        this(callFactory, cancellationExecutor, true, requestImageUrlCallback);
    }

    /**
     * @param callFactory          custom {@link Call.Factory} for fetching image from the network
     * @param cancellationExecutor executor on which fetching cancellation is performed if
     *                             cancellation is requested from the UI Thread
     * @param disableOkHttpCache   true if network requests should not be cached by OkHttp
     */
    public BzlOkHttpNetworkFetcher(
            Call.Factory callFactory, Executor cancellationExecutor, boolean disableOkHttpCache, RequestImageUrlCallback requestImageUrlCallback) {
        super(callFactory, cancellationExecutor, disableOkHttpCache);
        mCallFactory = callFactory;
        mCancellationExecutor = cancellationExecutor;
        mCacheControl = disableOkHttpCache ? new CacheControl.Builder().noStore().build() : null;
        this.mRequestImageUrlCallback = requestImageUrlCallback;
    }

    public BzlOkHttpNetworkFetcher setSupportWebp(boolean supportWebp) {
        isSupportWebp = supportWebp;
        return this;
    }


    @Override
    public void fetch(OkHttpNetworkFetchState fetchState, Callback callback) {
        super.fetch(fetchState, callback);
    }

    @Override
    protected void fetchWithRequest(
            final OkHttpNetworkFetchState fetchState,
            final NetworkFetcher.Callback callback,
            final Request request) {
        fetchWithRequestTry(fetchState, callback, request, MAX_RETRY_COUNT);
    }

    protected void fetchWithRequestTry(
            final OkHttpNetworkFetchState fetchState,
            final NetworkFetcher.Callback callback,
            final Request request,
            final int retryCount
    ) {

        final Call call;
        if (isSupportWebp) {
            Request newRequest = request.newBuilder().addHeader("Accept", "image/webp,image/*").build();
            call = mCallFactory.newCall(newRequest);
        } else {
            call = mCallFactory.newCall(request);
        }

        fetchState.getContext().addCallbacks(
                new BaseProducerContextCallbacks() {
                    @Override
                    public void onCancellationRequested() {
                        if (Looper.myLooper() != Looper.getMainLooper()) {
                            call.cancel();
                        } else {
                            mCancellationExecutor.execute(
                                    new Runnable() {
                                        @Override
                                        public void run() {
                                            call.cancel();
                                        }
                                    });
                        }
                    }
                });

        call.enqueue(new OkhttpCallback(fetchState, callback, request, retryCount));
    }

    private void handleException(final Call call, final Exception e, final Callback callback) {
        if (call.isCanceled()) {
            callback.onCancellation();
        } else {
            callback.onFailure(e);
        }
        if (mRequestImageUrlCallback != null) {
            mRequestImageUrlCallback.requestFailure(call, e);
        }
    }

    class OkhttpCallback implements okhttp3.Callback {
        final Request request;
        final OkHttpNetworkFetchState fetchState;
        final NetworkFetcher.Callback callback;
        final int retryCount;

        public OkhttpCallback(OkHttpNetworkFetchState fetchState, NetworkFetcher.Callback callback, Request request, int retryCount) {
            this.request = request;
            this.fetchState = fetchState;
            this.callback = callback;
            this.retryCount = retryCount;
        }

        @Override
        public void onResponse(@NonNull Call call, Response response) throws IOException {
            fetchState.responseTime = SystemClock.elapsedRealtime();
            final ResponseBody body = response.body();
            try {
                if (!response.isSuccessful()) {
                    //这块是新增的 start
                    if (mRequestImageUrlCallback != null && response.code() == HttpURLConnection.HTTP_FORBIDDEN) {
                        if (retryCount > 0) {
                            HttpUrl url = request.url();
                            TLog.error(TAG, "Received 403 error. Retrying with new URL... %s", url);
                            String newUrl = mRequestImageUrlCallback.requestNewUrl(url.toString());
                            if (newUrl != null && url != HttpUrl.get(newUrl)) {
                                Request newRequest = request.newBuilder().url(newUrl).build();
                                fetchWithRequestTry(fetchState, callback, newRequest, retryCount - 1);
                                return;
                            }
                        } else {
                            TLog.error(TAG, "Received 403 error. Retrying with new URL error   %s", call.request());
                        }
                    }
                    //这块是新增的 end

                    handleException(
                            call, new IOException("Unexpected HTTP code " + response), callback);
                    return;
                }

                BytesRange responseRange =
                        BytesRange.fromContentRangeHeader(response.header("Content-Range"));
                if (responseRange != null
                        && !(responseRange.from == 0
                        && responseRange.to == BytesRange.TO_END_OF_CONTENT)) {
                    // Only treat as a partial image if the range is not all of the content
                    fetchState.setResponseBytesRange(responseRange);
                    fetchState.setOnNewResultStatusFlags(Consumer.IS_PARTIAL_RESULT);
                }
//                String contentType = response.header("Content-Type");
//                TLog.debug(TAG, "onResponse====" + "call = [" + call.request().url().toString() + "], response = [" +contentType + "]");
                long contentLength = body.contentLength();
                if (contentLength < 0) {
                    contentLength = 0;
                }
                callback.onResponse(body.byteStream(), (int) contentLength);
            } catch (Exception e) {
                handleException(call, e, callback);
            } finally {
                body.close();
            }
        }

        @Override
        public void onFailure(Call call, IOException e) {
            handleException(call, e, callback);
        }
    }

    HttpUrl getNewUri(HttpUrl uri) {
        return uri;
    }
}
