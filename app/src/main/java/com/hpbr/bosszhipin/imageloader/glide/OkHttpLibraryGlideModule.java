package com.hpbr.bosszhipin.imageloader.glide;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.Registry;
import com.bumptech.glide.annotation.GlideModule;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.load.model.GlideUrl;
import com.bumptech.glide.module.AppGlideModule;
import com.bumptech.glide.request.target.Target;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.imageloader.RequestImageUrlCallback;
import com.monch.lbase.util.LText;
import com.twl.http.Util;

import java.io.InputStream;

/**
 * Registers OkHttp related classes via Glide's annotation processor.
 *
 * <p>For Applications that depend on this library and include an {@link AppGlideModule} and G<PERSON>'s
 * annotation processor, this class will be automatically included.
 */
@GlideModule
public class OkHttpLibraryGlideModule extends AppGlideModule {

    public static RequestImageUrlCallback mRequestImageUrlCallback;

    @Override
    public void applyOptions(@NonNull Context context, @NonNull GlideBuilder builder) {
        builder.addGlobalRequestListener(new com.bumptech.glide.request.RequestListener<Object>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Object> target, boolean isFirstResource) {
                final String stackTraceString = Util.getStackTraceString(e);
                if (!LText.empty(stackTraceString)) {
                    ApmAnalyzer.create()
                            .action("action_fresco_image_request_failure","glide")
                            .p2(String.valueOf(model))
                            .p3(stackTraceString)
                            .report();
                }
                return false;
            }

            @Override
            public boolean onResourceReady(Object resource, Object model, Target<Object> target, DataSource dataSource, boolean isFirstResource) {
                return false;
            }
        });
    }

    @Override
    public void registerComponents(
            @NonNull Context context, @NonNull Glide glide, @NonNull Registry registry) {
        registry.replace(GlideUrl.class, InputStream.class, new OkHttpUrlLoader.Factory().setRequestImageUrlCallback(mRequestImageUrlCallback));
    }

    @Override
    public boolean isManifestParsingEnabled() {
        return false;
    }
}