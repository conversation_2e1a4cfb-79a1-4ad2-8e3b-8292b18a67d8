package com.hpbr.bosszhipin.live.util;

import com.hpbr.bosszhipin.module.interview.entity.ServerInterviewDetailBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.techwolf.lib.tlog.TLog;

/**
 * author: dubojun
 * date: 2024/11/26
 * description:
 **/
public class InterviewSpUtils {
    private static final String TAG = "InterviewSpUtils";

    public static final String KEY_SHOW_NET_QUALITY_DIALOG = "showNetQualityDialog";
    public static final String KEY_FIRST_SHOW_NET_QUALITY_DIALOG = "firstShowNetQualityDialog";

    public static void setIgnoreNetQualityDialog(String interviewId, boolean isIgnore) {
        SpManager.get().user().edit().putBoolean(StringUtil.connectTextWithChar("_", KEY_SHOW_NET_QUALITY_DIALOG, interviewId), isIgnore).apply();
    }

    public static boolean isIgnoreNetDialog(String interviewId){
        return SpManager.get().user().getBoolean(StringUtil.connectTextWithChar("_", KEY_SHOW_NET_QUALITY_DIALOG, interviewId), false);
    }

    public static void setFirstShow(String interviewId, boolean isFirst) {
        SpManager.get().user().edit().putBoolean(StringUtil.connectTextWithChar("_", KEY_FIRST_SHOW_NET_QUALITY_DIALOG, interviewId), isFirst).apply();
    }

    public static boolean isFirstShow(String interviewId) {
        return SpManager.get().user().getBoolean(StringUtil.connectTextWithChar("_", KEY_FIRST_SHOW_NET_QUALITY_DIALOG, interviewId), true);
    }

    public static void clearInterviewNetDialogCache(ServerInterviewDetailBean interviewDetailBean) {
        try {
            if (interviewDetailBean == null || interviewDetailBean.finishedInterview == 0) {
                return;
            }

            SpManager.get().user().edit().remove(StringUtil.connectTextWithChar("_", KEY_SHOW_NET_QUALITY_DIALOG, String.valueOf(interviewDetailBean.interviewId))).apply();
            SpManager.get().user().edit().remove(StringUtil.connectTextWithChar("_", KEY_FIRST_SHOW_NET_QUALITY_DIALOG, String.valueOf(interviewDetailBean.interviewId))).apply();
        } catch (Exception e) {
            TLog.error(TAG, "clearInterviewNetDialogCache failed. error msg = %s", e);
        }
    }
}
