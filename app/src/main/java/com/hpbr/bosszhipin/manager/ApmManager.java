package com.hpbr.bosszhipin.manager;

import android.content.Context;

import androidx.annotation.AnyThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.hpbr.apm.Apm;
import com.hpbr.apm.common.net.UrlConfig;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.SupplierKey;
import com.hpbr.apm.config.content.ContentSupplier;
import com.hpbr.apm.config.content.bean.pri.PrivateConfig;
import com.hpbr.apm.config.content.bean.pub.ExtendInfo;
import com.hpbr.apm.config.content.bean.pub.PublicConfig;
import com.hpbr.apm.log.HostUserSpec;
import com.hpbr.apm.log.LogFileType;
import com.hpbr.apm.log.LogUploadCallback;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseApplication;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.apm.func.ApmConsumer;
import com.hpbr.bosszhipin.manager.apm.func.ApmFuncRegister;
import com.hpbr.bosszhipin.manager.apm.func.ApmPredicate;
import com.hpbr.bosszhipin.manager.apm.func.ApmRunnable;
import com.hpbr.bosszhipin.manager.apm.func.ApmSupplier;
import com.hpbr.bosszhipin.module.workorder.LogDiagnoseManager;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.beta.tinker.TinkerManager;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.anti.AntiAnalyzer;
import com.twl.utils.KitUtils;

import net.bosszhipin.base.HttpLogInterceptor;
import net.bosszhipin.base.HttpParameters;
import net.bosszhipin.base.SafeGuardInterceptor;

import org.json.JSONObject;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import okhttp3.Interceptor;

/**
 * Created by zhangxiangdong on 2019/4/17 14:45.
 */
public class ApmManager {

    private static final String TAG = "ApmManager";

    private static boolean sInitialized = false;
    public static final boolean sApmTestMode = false;

    private static List<Interceptor> debugInterceptors;

    public static boolean hasInitialized() {
        return sInitialized;
    }

    public static void start(@NonNull Context c) {
        final Context context = c.getApplicationContext();

        boolean isDebug = BuildInfoUtils.isDebug();

        // 使用后台配置获取的sKey和ivParameter，以及宿主的用户id
        Config.Builder builder = Config.newBuilder()
                .authority(context.getString(R.string.file_provider_authorities))

                .sKey(context.getString(isDebug ? R.string.aes_key_dev : R.string.aes_key)) // Boss-Android
                .ivParameter(context.getString(isDebug ? R.string.aes_iv_dev : R.string.aes_iv)) // Boss-Android

//                .sKey("TrbrMCt5WmaK9uZJ") // Boss-Android_dev
//                .ivParameter("ndjXM7LtUBbtl4Lr") // Boss-Android_dev

                .addInterceptor(isDebug ? null : new SafeGuardInterceptor())
                .identitySupplier(() -> UserManager.getUserRole().get()) // 用户身份
                .clientInfoSupplier(HttpParameters::getApmClientInfo) // 设备信息
                .userIdSupplier(() -> {
                    long uid = UserManager.getUID();
                    return Math.max(uid, 0); // 0: 表示未登录
                }) // 登录用户id
                .uniqueIdSupplier(() -> MobileUtil.getUniqId(context))
                .tinkerIdSupplier(() -> {
                    try {
                        return TinkerManager.getTinkerId();
                    } catch (Exception e) {
                        return "";
                    }
                })
                .apmRoleSupplier(() -> {
                    // https://zhishu.zhipin.com/wiki/mda4Qk7hFNv
                    // GEEK(0), BOSS(1)
                    /*
                        全部。默认选项，接口传0
                        身份A。接口传1的用户。直聘线的Geek角色
                        身份B。接口传2的用户。直聘线的Boss角色
                     */
                    long uid = UserManager.getUID();
                    if (uid > 0) {
                        return UserManager.getUserRole().get() + 1; // ROLE_A = 1、ROLE_B = 2、ROLE_DEFAULT=0
                    } else {
                        return 0;
                    }
                })
                .newTinkerIdSupplier(() -> {
                    try {
                        return TinkerManager.getNewTinkerId();
                    } catch (Exception e) {
                        return "";
                    }
                })
                .putSupplier(SupplierKey.KEY_LOG_UPLOAD_USER_SPEC, () -> new HostUserSpec() {
                    @NonNull
                    @Override
                    public String note() {
                        return "被动上报日志（来自客户端的备注）"; // 捞取的备注，apm 后台暂不支持
                    }

                    @NonNull
                    @Override
                    public LogUploadCallback callback() {
                        return new LogUploadCallback() {
                            @Override
                            public void onLogUploadStart(@NonNull LogFileType logFileType) {
                                try {
                                    LogUploadCallback.super.onLogUploadStart(logFileType);
                                    onReportLogDiagnose();
                                    TLog.flush();
                                } catch (Exception e) {
                                    TLog.error(TAG, "error msg = %s", e);
                                }
                            }
                        };
                    }
                });

        if (isDebug) {
            builder.addInterceptor(new HttpLogInterceptor());
            if (!LList.isEmpty(debugInterceptors)) {
                for (Interceptor interceptor : debugInterceptors) {
                    builder.addInterceptor(interceptor);
                }
            }
        }

        ApmFuncRegister
                .ApmFuncRegisterHelper
                .of(context, builder)
                .put(
                        new ApmConsumer(),
                        new ApmPredicate(),
                        new ApmSupplier(),
                        new ApmRunnable()
                );

        Config config = builder.build();

        Apm.get()
                .config(config)
                .debug(isDebug)
                .enableApiEncryption(true)
                .enableLog(isDebug && KitUtils.getAppLogCatEnable())
                .start();
        setAntiReportImpl();
//        initTraceCanaryPlus();

        sInitialized = true;
    }

    public static void addDebugInterceptor(Interceptor interceptor) {
        if (debugInterceptors == null) {
            debugInterceptors = new CopyOnWriteArrayList<>();
        }
        if (!debugInterceptors.contains(interceptor)) {
            debugInterceptors.add(interceptor);
        }
    }

    private static void initTraceCanaryPlus() {
        if (BuildInfoUtils.isDebug()) {
            try {
                final String defaultHost = UrlConfig.Host.QA;
                L.debug(TAG, "default host based on tracecanaryplus status: %s", defaultHost.toUpperCase(Locale.getDefault()));

                final Class<?> clazz = Class.forName("com.hpbr.apm.settings.Settings");
                final Method method = clazz.getMethod("setReportHost", Context.class, String.class);
                method.invoke(null, App.get(), defaultHost);

                Class<?> signalAnrTracerCls = Class.forName("com.tencent.matrix.trace.tracer.SignalAnrTracer");
                Field sAnrTraceFilePathField = signalAnrTracerCls.getDeclaredField("sAnrTraceFilePath");
                Field sPrintTraceFilePathField = signalAnrTracerCls.getDeclaredField("sPrintTraceFilePath");

                Class<?> sAnrTraceFilePathFieldType = sAnrTraceFilePathField.getType();
                Class<?> sPrintTraceFilePathFieldType = sPrintTraceFilePathField.getType();
                if (sAnrTraceFilePathFieldType == String.class && sPrintTraceFilePathFieldType == String.class) {
                    sAnrTraceFilePathField.setAccessible(true);
                    sPrintTraceFilePathField.setAccessible(true);

                    Method nativeInitSignalAnrDetectiveMethod = signalAnrTracerCls.getDeclaredMethod("nativeInitSignalAnrDetective", String.class, String.class);
                    nativeInitSignalAnrDetectiveMethod.setAccessible(true);

                    nativeInitSignalAnrDetectiveMethod.invoke(null, String.valueOf(sAnrTraceFilePathField.get(null)), String.valueOf(sPrintTraceFilePathField.get(null)));
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }

    @Nullable
    public static ExtendInfo getExtendInfo() {
        return ContentSupplier.get().getExtendInfo();
    }

    @Nullable
    public static JSONObject getPrivateConfigJsonObject() {
        try {
            PrivateConfig privateConfig = ContentSupplier.get().getPrivateConfig();
            if (privateConfig != null) {
                String jsonObjectString = privateConfig.jsonObjectString;
                if (jsonObjectString != null) {
                    return new JSONObject(jsonObjectString);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    @Nullable
    public static JSONObject getPublicConfigJsonObject() {
        try {
            PublicConfig publicConfig = ContentSupplier.get().getPublicConfig();
            if (publicConfig != null) {
                String jsonObjectString = publicConfig.jsonObjectString;
                if (jsonObjectString != null) {
                    return new JSONObject(jsonObjectString);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    @AnyThread
    public static void sync() { // 手动触发同步配置、升级信息获取等逻辑
        try {
            Apm.get().sync();
        } catch (Exception e) {
            if (BuildInfoUtils.isDebug()) {
                throw e;
            }
            L.e("ApmManager", e.getMessage());
        }
    }

    public static void checkForUpgrade(@NonNull AppCompatActivity host) {
        Apm.checkForUpgrade(host, new CustomUpgradeDialog());
    }

    public static void doWorks() {
        uploadAnrTraceFiles();
    }

    private static void uploadAnrTraceFiles() {
        try {
            Class<?> clazz = Class.forName("com.hpbr.apm.settings.Settings");
            Method method = clazz.getMethod("uploadAnrTraceFiles");
            method.invoke(null);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static void setAntiReportImpl() {
        AntiAnalyzer.setReporter(new AntiAnalyzer.Reporter() {
            @Override
            public void onCallback(String action, String type, Map<String, String> params) {
                TLog.debug(TAG, "[%s] [%s] [%s]", action, type, params);
                AnalyticsFactory.create().action(action).param("p", type).param(params).build();
            }

            @Override
            public void postCatchedException(Throwable throwable, Map<String, String> params) {
                Context context = BaseApplication.getApplication();
                for (String key : params.keySet()) {
                    CrashReport.putUserData(context, key, params.get(key));
                }
                CrashReport.postCatchedException(throwable, Thread.currentThread(), true);
                Apm.reportError(throwable,params);
            }

        });
    }

    private static void onReportLogDiagnose() {
        LogDiagnoseManager.getInstance().logDrawerContacts();
        LogDiagnoseManager.getInstance().logMsgLost();
    }
}
