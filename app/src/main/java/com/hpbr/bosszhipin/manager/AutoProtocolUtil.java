package com.hpbr.bosszhipin.manager;

import android.content.Context;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.MapUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.Map;


public class AutoProtocolUtil {

    public static final String PROTOCOL_KEY = "AutoProtocol";
    public final static long DELAY_TIME = 5000L;

    public static void updateProtocol(String protocol) {
        SpManager.get().user().edit().putString(PROTOCOL_KEY, protocol).apply();
    }

    public static void checkProtocol(Context context) {
        //安全框架
        if (!TextUtils.isEmpty(UserManager.getSecurityUrl())) return;
        String protocol = SpManager.get().user().getString(PROTOCOL_KEY, null);
        if (TextUtils.isEmpty(protocol)) return;  // F1引导弹窗当次显示
        if (NotificationCheckUtils.dialogIsShowing) return; //F1弹窗显示中
        Map<String, String> params = ZPManager.UrlHandler.getParams(protocol);
        long delay_time = LText.getLong(MapUtils.get(params, "delayTime"), DELAY_TIME);
        TLog.info(PROTOCOL_KEY, "protocol = %s : %s", protocol, delay_time);
        Utils.runOnUiThreadDelayed(() -> {
            String protocol2 = SpManager.get().user().getString(PROTOCOL_KEY, null);
            if (!TextUtils.isEmpty(protocol2) && App.get().isForeground()) { //前台时执行
                if (NotificationCheckUtils.dialogIsShowing) return; //F1弹窗显示中
                SpManager.get().user().edit().remove(PROTOCOL_KEY).apply();
                new ZPManager(context, protocol2).handler();
            }
        }, delay_time);

    }
}
