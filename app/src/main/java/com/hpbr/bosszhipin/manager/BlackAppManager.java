package com.hpbr.bosszhipin.manager;

import androidx.annotation.WorkerThread;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.gray.DynamicExecData;
import com.hpbr.bosszhipin.utils.AppInstallUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.anti.*;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.DeviceUtils;
import com.twl.utils.GsonUtils;
import com.twl.utils.process.ProcessUtil;

import net.bosszhipin.api.*;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.hpbr.bosszhipin.event.ApmAnalyticsAction.*;

import android.text.TextUtils;

public class BlackAppManager {
    private static final String TAG = "BlackAppManager";

    //是否能检测标记，避免短时间内重复检测
    private static AtomicBoolean gCanChcek = new AtomicBoolean(true);
    private static AtomicBoolean gCanEnvChcek = new AtomicBoolean(true);

    private static volatile boolean gChecked = false;

    public static void check() {
        gChecked = true;
        if (!UserManager.isCurrentLoginStatus()) {
            return;
        }

        AppThreadFactory.POOL.execute(() -> {
            try {
                Monitor monitor = new Monitor(ApmAnalyticsAction.TYPE_SEC_ENV);
                monitor.start();
                DacManager.recordDiskInfo();
                monitor.log("RecordDiskInfo execution completed.");
                if (gCanChcek.getAndSet(false)) {
                    shouldReportUnsecureEnv();
                    monitor.log("ShouldReportUnsecureEnv execution completed.");
                    gCanChcek.set(true);
                }
                monitor.stop();
            } catch (Throwable e) {
                CrashReport.postCatchedException(e);
            }
        });

        new GetBlackListRequest(new ApiRequestCallback<GetBlackAppListResponse>() {
            @Override
            public void handleInChildThread(ApiData<GetBlackAppListResponse> data) {
                super.handleInChildThread(data);
                dealDetection(data.resp);
            }

            @Override
            public void onSuccess(ApiData<GetBlackAppListResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);
                AppThreadFactory.POOL.execute(gCheatCheck);
            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        }).execute();
    }

    /**
     * 确保登录会走安全检测
     * 安全检测触发逻辑在MainActivity，目前发现有部分用户一登录就是安全框（WebviewActivity），导致没有安全检测，可以通过作弊的方式通过环境认证
     */
    public static void ensureCheck() {
        gChecked = false;
        AppThreadFactory.POOL.schedule(() -> {
            if (!gChecked) {
                TLog.info(TAG, "ensureCheck() called");
                check();
            }
        }, 5, TimeUnit.SECONDS);
    }

    @WorkerThread
    private static void shouldReportUnsecureEnv() {
        if (!ProcessUtil.isMainProcess(App.getAppContext())) {
            return;
        }

        List<String> secValues = new ArrayList<>();
        if (Checker.isHook()) {
            secValues.add("hook=1");
            ApmAnalyzer.create().action(ACTION_SECURITY_HOOK).report();
        }

        String appUid = BrandUtils.computeUid();
        boolean isRoot = DeviceUtils.isRoot();
        if (isRoot) {
            secValues.add("root=1");
            ApmAnalyzer.create().action(ACTION_SECURITY_ROOT)
                    .param("p2", appUid)
                    .param("p3", BrandUtils.getDeviceType())
                    .param("p4", BrandUtils.getSameBrand()).report();
        }

//        if (Checker.hasExposed()) {
//            secValues.add("xposed=1");
//        }

        if (Checker.hasSuspiciousDex()) {
            secValues.add("dex=1");
        }

//        if (Checker.isCheat(App.getAppContext())) {
//            secValues.add("emulator=1");
//            ApmAnalyzer.create().action(ACTION_SECURITY_SIMULATOR).report();
//        }

        if (Checker.isRepack(App.getAppContext())) {
            secValues.add("repack=1");
            ApmAnalyzer.create().action(ACTION_SECURITY_REPACKAGE).report();
        }

        if (secValues.size() > 0) {
            String value = StringUtil.connectTextWithChar("&", secValues.toArray(new String[0]));
            L.d(TAG, "Report Unsecure Env -> " + value);
            if (!LText.empty(value)) {
                ApmAnalyzer.create()
                        .action(ApmAnalyticsAction.ACTION_SECURITY, ApmAnalyticsAction.TYPE_SECURITY_ENV)
                        .param("p2", value)
                        .report();
            }
        }

        if ("1".equals(appUid) || "1".equals(BrandUtils.computeInstallTime())) {
            reportDeviceClone(isRoot);
        }
    }

    private static void dealDetection(GetBlackAppListResponse response) {
        new Thread(() -> {
            try {
                AppRuninfoModel runinfoModel = new AppRuninfoModel();
                List<String> blackAppList = response.blackApkNameList;
                if (blackAppList != null && blackAppList.size() > 0) {//apk检测
                    for (String appName : blackAppList) {
                        if (AppInstallUtils.checkApkExist(App.get(), appName)) {
                            if (runinfoModel.blackApkNameList == null) {
                                runinfoModel.blackApkNameList = new ArrayList<>();
                            }
                            runinfoModel.blackApkNameList.add(appName);
                        }
                    }
                }
                runinfoModel.dzt = Inspector.getInstance(App.getAppContext()).getDeviceType();//模拟器检测
                runinfoModel.isRoot = DeviceUtils.isRoot() ? 1 : 0;
                runinfoModel.isHook = DeviceUtils.isHook() ? 1 : 0;
                uploadRunningInfo(runinfoModel);

                AccessibilityChecker.isUseAccessibility(App.getAppContext());
                cheatCheck();

                String result = new Defender().check(CheatChecker.class, ApmAnalyzer.class, AntiAnalyzer.class);
                if (!TextUtils.isEmpty(result)) {
                    AnalyticsFactory.create().action("action_reporting_exception").param("p", "type_app").param("p2", result).build();
                    CrashReport.putUserData(App.getAppContext(), "info", result);
                    CrashReport.postCatchedException(new RuntimeException("App reporting exception"), Thread.currentThread(), true);
                    ApmAnalyzer.create(true)
                            .action("app_2_exception", "type_app")
                            .report();
                }
            } catch (Throwable e) {
                CrashReport.postCatchedException(e);
            }

        }).start();
    }

    private static boolean gCanCheatCheck = true;
    private static Runnable gCheatCheck = () -> {
        if (gCanCheatCheck) {
            cheatCheck();
        }
    };

    private static void cheatCheck() {
        if (gCanEnvChcek.getAndSet(false)) {
            gCanCheatCheck = false;
            Monitor monitor = new Monitor(TYPE_SEC_CHECKER);
            monitor.start();
            loadApmExtInfoConfig();
            monitor.log("loadApmExtInfoConfig execution completed.");
            EnvLogReporter.uploadLogThenReport();
            monitor.log("uploadLogThenReport execution completed.");
            loadDynamicExecData();
            monitor.log("loadDynamicExecData execution completed.");
            monitor.stop();
            gCanEnvChcek.set(true);
        }
    }

    private static void uploadRunningInfo(AppRuninfoModel runinfoModel) {
        UploadRuningInfoRequest uploadRuningInfoRequest = new UploadRuningInfoRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        uploadRuningInfoRequest.data = GsonUtils.getGson().toJson(runinfoModel);
        //noinspection unchecked
        HttpExecutor.execute(uploadRuningInfoRequest);
    }

    private static void loadApmExtInfoConfig() {
        // 载入Apm配置
        new CheatChecker(App.getAppContext(), ApmManager.getExtendInfo()).start();
    }


    private static void loadDynamicExecData() {
        if (!AndroidDataStarGray.getInstance().safeDynamicAntiControl()) return;
        new DynamicExecData().initDynamicExecData(new DynamicExecData.OnDynamicExec() {
            @Override
            public void executeDynamicCheck(String checkData) {
                if (!TextUtils.isEmpty(checkData)) {
                    Inspector.getInstance(App.getAppContext()).execTask(checkData);
                }
            }
        });
    }

    /**
     * 上报用户数据是否克隆
     *
     * @param isRoot
     */
    private static void reportDeviceClone(boolean isRoot) {
        String root = isRoot ? "1" : "0";
        DeviceCloneRequest deviceCloneRequest = new DeviceCloneRequest(new ApiRequestCallback<DeviceCloneResponse>() {

            @Override
            public void onSuccess(ApiData<DeviceCloneResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }

            @Override
            public void onLoginError(String msg) {
                super.onLoginError(msg);
                BrandUtils.reset();
                CrashReport.postCatchedException(new Exception("The user was kicked out because of device clone."));
            }
        });
        deviceCloneRequest.isRoot = root;
        HttpExecutor.execute(deviceCloneRequest);

        //上报到apm，由NLP处理
        ApmAnalyzer.create(true)
                .action(ApmAnalyticsAction.ACTION_NLP, ApmAnalyticsAction.TYPE_APP_CLONE)
                .p2(root)
                .p3(BrandUtils.getDeviceType())
                .p4(BrandUtils.getInstallInfo())
                .p5(String.valueOf(BrandUtils.getCloneTime()))
                .report();
    }

    /**
     * 上报did 变更
     *
     * @param preDid 前一次did
     * @param did    当前获取did
     */
    public static void reportDidChanage(String preDid, String did) {
        ApmAnalyzer.create(true)
                .action(ApmAnalyticsAction.ACTION_NLP, ApmAnalyticsAction.TYPE_DID_CHANAGE)
                .p2(BrandUtils.computeUid())
                .p3(preDid)
                .p4(did)
                .report();
    }

    private static class Monitor implements Runnable {
        private static final int HANG_TIME = 2 * 60 * 1000;
        private StringBuffer mLogBuffer = new StringBuffer();
        private Thread mMonitorThread = null;
        private String type;

        private Monitor(String type) {
            this.type = type;
        }

        @Override
        public void run() {
            StackTraceElement[] stacks = null;
            String threadName = null;
            if (mMonitorThread != null) {
                stacks = mMonitorThread.getStackTrace();
                threadName = mMonitorThread.getName();
            }
            ApmAnalyzer.create()
                    .action(ApmAnalyticsAction.ACTION_SECURITY_ERR, type)
                    .p2(mLogBuffer.toString())
                    .p3(Arrays.toString(stacks))
                    .p4(threadName)
                    .report();
        }

        public void start() {
            mMonitorThread = Thread.currentThread();
            AppThreadFactory.getMainHandler().postDelayed(this, HANG_TIME);
        }

        public void stop() {
            AppThreadFactory.getMainHandler().removeCallbacks(this);
        }

        public void log(String msg) {
            mLogBuffer.append(msg).append("\n");
        }
    }
}
