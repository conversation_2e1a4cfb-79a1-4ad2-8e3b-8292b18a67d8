package com.hpbr.bosszhipin.manager;

import android.content.Context;
import android.os.Build;
import android.os.Process;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;

final class BrandUtils {
    private static final String DAC_UID_KEY = "dac_report_time";
    private static final String DAC_BRAND_KEY = "r_brand";
    private static final String DAC_MODEL_KEY = "r_model";
    private static final String DAC_FIRST_INSTALL_TIME = "r_time";
    private static final String DAC_CLONE_TIME = "r_cl_time";


    private static final long BASELINE_VALUE = 1620459160940l;//写代码时的时间戳，值没有意义，就是为了混淆用

    /**
     * 计算uid有没有发生变化
     *
     * @return
     */
    static String computeUid() {
        String state = "0";
        long uid = SpManager.get().global().getLong(DAC_UID_KEY, -1);
        if (uid == -1) {
            uid = BASELINE_VALUE + Process.myUid();
            SpManager.get().global().edit().putLong(DAC_UID_KEY, uid)
                    .putString(DAC_BRAND_KEY, Build.BRAND)
                    .putString(DAC_MODEL_KEY, Build.MODEL)
                    .putLong(DAC_CLONE_TIME, System.currentTimeMillis())
                    .putLong(DAC_FIRST_INSTALL_TIME,  getSaveTime()).apply();
        } else {
            if ((uid - BASELINE_VALUE) != Process.myUid()) {
                state = "1";
            }
        }
        return state;
    }

    /**
     * 返回存储的devicetype
     *
     * @return
     */
    static String getDeviceType() {
        String deveceType = String.format("%s-%s",
                SpManager.get().global().getString(DAC_BRAND_KEY, ""),
                SpManager.get().global().getString(DAC_MODEL_KEY, ""));
        return deveceType;
    }

    /**
     * 是否是一样的品牌
     *
     * @return
     */
    static String getSameBrand() {
        String spBrand = SpManager.get().global().getString(DAC_BRAND_KEY, "");
        if (TextUtils.equals(Build.BRAND, spBrand)
                || Build.BRAND.endsWith(spBrand)
                || Build.BRAND.startsWith(spBrand)) {
            return "1";
        }
        return "0";
    }

    /**
     * 两次安装信息
     *
     * @return
     */
    static String getInstallInfo() {
        String info = String.format("pre: %d-%d \n cur: %d-%d",
                SpManager.get().global().getLong(DAC_UID_KEY, -1) - BASELINE_VALUE,
                SpManager.get().global().getLong(DAC_FIRST_INSTALL_TIME, 0) + BASELINE_VALUE,
                Process.myUid(),
                getPackageFirstInstallTime(App.getAppContext()));
        return info;
    }

    /**
     * 应用克隆发生时间
     *
     * @return
     */
    static long getCloneTime() {
        return SpManager.get().global().getLong(DAC_CLONE_TIME, -1);
    }

    /**
     * 判断首次安装时间与保存在sp中的是否相等
     *
     * @return
     */
    static String computeInstallTime() {
        String state = "0";
        long saveTime = SpManager.get().global().getLong(DAC_FIRST_INSTALL_TIME, -1);
        if (saveTime == -1) {
            SpManager.get().global().edit().putLong(DAC_FIRST_INSTALL_TIME, getSaveTime()).apply();
        } else {
            long firstTime = getPackageFirstInstallTime(App.getAppContext());
            //判断保存是时间与pm获取的是否一致
            if ((firstTime - BASELINE_VALUE) != saveTime) {
                state = "1";
            }
        }
        return state;
    }

    private static long getSaveTime() {
        return getPackageFirstInstallTime(App.getAppContext()) - BASELINE_VALUE;
    }

    /**
     * 重置数据
     */
    static void reset() {
        SpManager.get().global().edit()
                .remove(DAC_UID_KEY)
                .remove(DAC_BRAND_KEY)
                .remove(DAC_MODEL_KEY)
                .remove(DAC_FIRST_INSTALL_TIME).apply();
    }

    private static long getPackageFirstInstallTime(Context context) {
        String name = context.getPackageName();
        long time = 0;
        try {
            time = context.getPackageManager().getPackageInfo(name, 0).firstInstallTime;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return time;
    }
}
