package com.hpbr.bosszhipin.manager;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Environment;
import android.os.StatFs;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.crash.bugly.BuglyCrashReporter;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.apm.lifecycle.AppLifecycleCaptor;
import com.hpbr.apm.toppageinfo.PageInfoFinder;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.utils.AppStatusUtil;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.BuglyStrategy;
import com.twl.utils.file.PathUtils;
import com.twl.utils.process.ProcessUtil;

import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;

public class CrashReporter {

    private static final String TAG = "CrashReporter";

    private static final String MEMORY_SIZE_ERROR = "-1";
    private static final String MEMORY_SIZE_ERROR_CRASH = "-2";

    public static void crashAction(int crashType, String errorType, String errorMessage, String errorStack) {
        try {
            String type = convertType(crashType);
            if (ApmManager.hasInitialized()) {
                StringBuilder sb = new StringBuilder();
                sb.append("crashType:").append(crashType).append("\n");
                sb.append("errorType:").append(errorType).append("\n");
                sb.append("errorMessage:").append(errorMessage).append("\n");
                sb.append("errorStackV2:").append(insertAt(errorStack)).append("\n");

                TLog.info(TAG, "crashinfo  :%s", sb.toString());
                AnalyticsFactory.create()
                        .action(AnalyticsAction.ACTION_ANDROID_CRASH)
                        .param("p", String.valueOf(UserManager.getUID()))
                        .param("p2", sb.toString())
                        .build();

                if (ApmAnalyticsAction.TYPE_ANR.equals(type)) { // 用于 apm 后台的 崩溃监控-ANR分析功能
                    ApmAnalyzer.create()
                            .action(ApmAnalyticsAction.ACTION_APP_CRASH, type)
                            .param("p2", String.valueOf(UserManager.getUID())) // 用户id
                            .param("p3", String.valueOf(UserManager.getUserRole().get())) // 用户身份
                            .param("p4", sb.toString()) // 异常堆栈
                            .param("p5", convertCrashType(crashType)) // 完整的 Crash 类型
                            .p6(AppLifecycleCaptor.INSTANCE.getStates()) // 页面跟踪
                            .p7(PageInfoFinder.getInstance().findTopPageInfo()) // 当前页面详情
                            .p8(extraInfo()) // json 字符串
                            .p9(generateSignature(errorStack))
                            .debug()
                            .report();
                }

                // 采集 bugly 回调的所有异常数据
                ApmAnalyzer.create()
                        .action(ApmAnalyticsAction.ACTION_BUGLY_ERROR, type)
                        .p2(String.valueOf(UserManager.getUID())) // 用户id
                        .p3(String.valueOf(UserManager.getUserRole().get())) // 用户身份
                        .p4(sb.toString()) // 异常堆栈
                        .p5(convertCrashType(crashType)) // 完整的 Crash 类型
                        .p6(AppLifecycleCaptor.INSTANCE.getStates()) // 页面跟踪
                        .p7(PageInfoFinder.getInstance().findTopPageInfo()) // 当前页面详情
                        .p8(extraInfo()) // json 字符串
                        .p9(generateSignature(errorStack))
                        .debug()
                        .report();

                // 阻塞上报 bugly 回调异常到 apm 后台
                BuglyCrashReporter.INSTANCE.reportSyncNow(
                        crashType,
                        errorType,
                        errorMessage,
                        errorStack,
                        apmAnalyzer -> apmAnalyzer
                                .p7(PageInfoFinder.getInstance().findTopPageInfo())
                                .p8(extraInfo())
                                .p9(generateSignature(errorStack))
                );
            }
        } catch (Exception e) {
            TLog.error(TAG, "crashAction error: %s", e.getMessage());
        }
    }

    @NonNull
    private static String insertAt(@NonNull String errorStack) {
        String[] esLines = errorStack.split("\n");
        StringBuilder sb = new StringBuilder();
        int length = esLines.length;
        for (int i = 0; i < length; i++) {
            String esLine = esLines[i];
            if (i < length - 1) {
                sb.append("at ").append(esLine).append("\n");
            }
        }
        return sb.toString();
    }

    @Nullable
    private static String generateSignature(@Nullable String errorStack) {
        if (LText.empty(errorStack)) {
            return "";
        }

        /*

            crashType:0

            errorType:java.lang.NullPointerException

            errorMessage:Attempt to invoke virtual method 'boolean java.lang.Object.equals(java.lang.Object)' on a null object reference

            errorStack:
                        at com.tencent.mtt.external.reader.a.k.b(Unknown Source:37)
                        at com.tencent.mtt.external.reader.a.r.b(Unknown Source:0)
                        at com.tencent.mtt.external.reader.a.h.a(Unknown Source:185)
                        at com.tencent.mtt.external.reader.a.i.b(Unknown Source:54)
                        at com.tencent.mtt.external.reader.a.i.a(Unknown Source:40)
                        at com.tencent.mtt.external.reader.a.g$1.a(Unknown Source:84)
                        at com.tencent.mtt.external.reader.internal.l.a(Unknown Source:126)
                        at com.tencent.mtt.external.reader.internal.l$2.a(Unknown Source:240)
                        at com.tencent.mtt.external.reader.a.p$2.handleMessage(Unknown Source:17)
                        at android.os.Handler.dispatchMessage(Handler.java:110)
                        at android.os.Looper.loop(Looper.java:219)
                        at com.hpbr.bosszhipin.common.a.a$a.run(SourceFile:31)
                        at android.os.Handler.handleCallback(Handler.java:900)
                        at android.os.Handler.dispatchMessage(Handler.java:103)
                        at android.os.Looper.loop(Looper.java:219)
                        at android.app.ActivityThread.main(ActivityThread.java:8668)
                        at java.lang.reflect.Method.invoke(Native Method)
                        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:513)
                        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1109);

         */
        // TODO: 2022/10/10 计算堆栈签名

        return null;
    }

    @NonNull
    public static String extraInfo() {
        JSONObject extraInfo = new JSONObject();

        /*
            1、屏幕是否亮着（可交互）
            2、补充进程名、线程名
            3、使用时长
            4、前后台
            5、可用内存大小
            6、是否ROOT
            7、可用存储大小
            8、可用sdcard大小
            9、App渠道
         */
        try {
            ActivityManager manager = (ActivityManager) App.get().getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo info = new ActivityManager.MemoryInfo();
            manager.getMemoryInfo(info);

            extraInfo.putOpt("isInteractive", AppStatusUtil.isInteractive()); // 亮屏可交互状态
            extraInfo.putOpt("processName", ProcessUtil.getCurProcessName(Utils.getApp()));
            // TODO: 2022/10/10 获取app使用时长
            // extraInfo.putOpt("runningTime", )
            extraInfo.putOpt("foreground", AppStatusUtil.getAppStatus() == 1); // 1为前台，2为后台
            extraInfo.putOpt("availableMemory", formatSizeKB(info.availMem)); // kB
            extraInfo.putOpt("rooted", RootUtil.isDeviceRooted());
            extraInfo.putOpt("availableInternalStorage", getAvailableInternalMemorySize()); // bytes
            extraInfo.putOpt("availableExternalStorage", getAvailableExternalMemorySize()); // bytes
            extraInfo.putOpt("appChannel", MobileUtil.getChannel());
            // TODO: 2022/11/7 补充ROM信息
            // extraInfo.putOpt("rom", )
        } catch (Throwable e) {
            TLog.error(TAG, "extraInfo error: %s", e.getMessage());
        }

        String eis = extraInfo.toString();

        L.debug(TAG, "ExtraInfo: %s", eis);

        return eis;
    }

    @NonNull
    private static String convertType(int crashType) {
        String type = ApmAnalyticsAction.TYPE_OTHER;
        switch (crashType) {
            case BuglyStrategy.a.CRASHTYPE_JAVA_CRASH:
                type = ApmAnalyticsAction.TYPE_JAVA_CRASH;
                break;
            case BuglyStrategy.a.CRASHTYPE_NATIVE:
                type = ApmAnalyticsAction.TYPE_NATIVE;
                break;
            case BuglyStrategy.a.CRASHTYPE_ANR:
                type = ApmAnalyticsAction.TYPE_ANR;
                break;
        }
        return type;
    }

    @Nullable
    private static String convertCrashType(int crashType) {
        String crashTypeName = null;

        final Class<BuglyStrategy.a> aClass = BuglyStrategy.a.class;
        final Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            try {
                declaredField.setAccessible(true);
                if (Modifier.isStatic(declaredField.getModifiers())
                        && declaredField.getName().startsWith("CRASHTYPE_")
                        && declaredField.getInt(null) == crashType) {
                    crashTypeName = declaredField.getName();
                    L.d(TAG, "convertCrashType: " + crashTypeName);
                }
            } catch (Throwable e) {
                TLog.error(TAG, "convertCrashType error: %s", e.getMessage());
            }
        }

        if (crashTypeName == null) {
            crashTypeName = getCrashTypeName(crashType);
        }

        return crashTypeName;
    }

    @Nullable
    private static String getCrashTypeName(int crashType) {
        String crashTypeName = null;
        switch (crashType) {
            case BuglyStrategy.a.CRASHTYPE_JAVA_CRASH:
                crashTypeName = "CRASHTYPE_JAVA_CRASH";
                break;
            case BuglyStrategy.a.CRASHTYPE_JAVA_CATCH:
                crashTypeName = "CRASHTYPE_JAVA_CATCH";
                break;
            case BuglyStrategy.a.CRASHTYPE_NATIVE:
                crashTypeName = "CRASHTYPE_NATIVE";
                break;
            case BuglyStrategy.a.CRASHTYPE_U3D:
                crashTypeName = "CRASHTYPE_U3D";
                break;
            case BuglyStrategy.a.CRASHTYPE_ANR:
                crashTypeName = "CRASHTYPE_ANR";
                break;
            case BuglyStrategy.a.CRASHTYPE_COCOS2DX_JS:
                crashTypeName = "CRASHTYPE_COCOS2DX_JS";
                break;
            case BuglyStrategy.a.CRASHTYPE_COCOS2DX_LUA:
                crashTypeName = "CRASHTYPE_COCOS2DX_LUA";
                break;
            case BuglyStrategy.a.CRASHTYPE_BLOCK:
                crashTypeName = "CRASHTYPE_BLOCK";
                break;
        }
        return crashTypeName;
    }


    private static String getAvailableInternalMemorySize() {
        try {
            File path = Environment.getDataDirectory();
            StatFs stat = new StatFs(path.getPath());
            long blockSize = stat.getBlockSizeLong();
            long availableBlocks = stat.getAvailableBlocksLong();
            return String.valueOf(availableBlocks * blockSize);
        } catch (Throwable t) {
            return MEMORY_SIZE_ERROR_CRASH;
        }

    }

    private static String getAvailableExternalMemorySize() {
        if (PathUtils.isSDCardEnableByEnvironment()) {
            try {
                File path = PathUtils.getFilesDirExternalFile(null);
                StatFs stat = new StatFs(path.getPath());
                long blockSize = stat.getBlockSizeLong();
                long availableBlocks = stat.getAvailableBlocksLong();
                return String.valueOf(availableBlocks * blockSize);
            } catch (Throwable t) {
                return MEMORY_SIZE_ERROR_CRASH;
            }
        } else {
            return MEMORY_SIZE_ERROR;
        }
    }

    private static String formatSizeKB(long size) {
        String suffix = " kB";
        StringBuilder resultBuffer = new StringBuilder(Long.toString(size / 1024));
        resultBuffer.append(suffix);
        return resultBuffer.toString();
    }

}

