package com.hpbr.bosszhipin.manager;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;

import androidx.annotation.IdRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.upgrade.rollout.UpgradeDialog;
import com.hpbr.bosszhipin.app.R;

public class CustomUpgradeDialog extends UpgradeDialog {

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        Dialog dialog = getDialog();
        if (dialog != null) {
            Window window = dialog.getWindow();
            if (window != null) {
                window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            }
        }
        super.onViewCreated(view, savedInstanceState);
    }

    @Override
    @LayoutRes
    public int getLayoutIdRes() {
        return R.layout.dialog_apm_custom_upgrade;
    }

    @Override
    @IdRes
    public int getTitleIdRes() {
        return R.id.title;
    }

    @Override
    @IdRes
    public int getVersionIdRes() {
        return R.id.version;
    }

    @NonNull
    @Override
    public CharSequence decorateVersion(@NonNull String version) {
        return "V" + version;
    }

    @Override
    @IdRes
    public int getDescIdRes() {
        return R.id.desc;
    }

    @NonNull
    @Override
    public CharSequence decorateDesc(@NonNull String desc) {
//        desc = "优化求职记录使用体验\n" +
//                "改进岗位发布使用体验\n" +
//                "改进安全规则与验证机制\n" +
//                "改善视觉设计和交互体验\n";
        desc = desc.trim();

//        // 根据\n换行展示，每行首部显示 “·” 号
//        String[] lines = desc.split("\\r?\\n");
//
//        if (lines.length > 1) {
//            String commonBigDot = StringUtil.COMMON_BIG_DOT;
//            SpannableStringBuilder ssb = new SpannableStringBuilder();
//            for (int i = 0; i < lines.length; i++) {
//                ssb.append(
//                        commonBigDot,
//                        new ForegroundColorSpan(ResourcesCompat.getColor(getResources(), R.color.color_FF15B3B3, null)),
//                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
//                );
//                ssb.append(lines[i]);
//                if (i < lines.length - 1) {
//                    ssb.append("\n");
//                }
//            }
//            return ssb;
//        }

        return desc;
    }

    @Override
    @IdRes
    public int getPositiveButtonIdRes() {
        return R.id.positive;
    }

    @Override
    @IdRes
    public int getNegativeButtonIdRes() {
        return R.id.negative;
    }

}
