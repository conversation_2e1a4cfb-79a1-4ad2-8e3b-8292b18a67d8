package com.hpbr.bosszhipin.manager;

import android.Manifest;
import android.os.Process;

import androidx.annotation.WorkerThread;

import com.hpbr.apm.common.utils.DeviceUtil;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.monitor.AnrReporter;
import com.hpbr.bosszhipin.utils.BuildInfoHelper;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.hpbr.bosszhipin.utils.permission.PermissionManager;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.DeviceInfo;
import com.twl.utils.NetworkUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class DacManager {

    private static final String LOG_TAG = DacManager.class.getSimpleName();

    /**
     * 是否当前pid首次上报，用于记录dac的点是否是同一个pid
     */
    private static boolean sFristReport = true;
    private static Map<String, Object> mapP7;

    @WorkerThread
    public static void recordDiskInfo() {

        /*
         * {
         *  "inner_total":"256", // MB
         *  "inner_available":"52", // MB
         *  "exter_total":"128", // MB
         *  "exter_available":"22" // MB
         * }
         */

        JSONObject object = new JSONObject();
        try {
            String totalInternalMemorySize = null;
            try {
                totalInternalMemorySize = DeviceInfo.getTotalInternalMemorySize();
            } catch (Exception ignored) {

            }

            String availableInternalMemorySize = null;
            try {
                availableInternalMemorySize = DeviceInfo.getAvailableInternalMemorySize();
            } catch (Exception ignored) {

            }

            String totalExternalMemorySize = null;
            try {
                totalExternalMemorySize = DeviceInfo.getTotalExternalMemorySize();
            } catch (Exception ignored) {

            }

            String availableExternalMemorySize = null;
            try {
                availableExternalMemorySize = DeviceInfo.getAvailableExternalMemorySize();
            } catch (Exception ignored) {

            }

            if (totalInternalMemorySize != null) {
                object.put("inner_total", totalInternalMemorySize);
            }

            if (availableInternalMemorySize != null) {
                object.put("inner_available", availableInternalMemorySize);
            }

            if (totalExternalMemorySize != null) {
                object.put("exter_total", totalExternalMemorySize);
            }

            if (availableExternalMemorySize != null) {
                object.put("exter_available", availableExternalMemorySize);
            }


            ApmAnalyzer.create()
                    .action(ApmAnalyticsAction.ACTION_INFO_DAC)
                    .param("p2", MobileUtil.getDid()) // 数盟id
                    .param("p3", object.toString()) // DAC需要的信息
                    .param("p4", BrandUtils.computeUid()) // 上报用户是否存在克隆数据登录;如果uid不一致的就上报1
                    .param("p5", BrandUtils.computeInstallTime()) // 上报用户是否存在克隆数据登录;如果首次安装不一致的就上报1
                    .p6(String.format("%b-%d", sFristReport, Process.myPid()))//进程信息
                    .p7(String.valueOf(getP7()))
                    .p8(DeviceUtil.getDevicesInfo())
                    .p9(BuildInfoHelper.getTinkerInfo())
                    .report();

            LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.RUN_TIME_ENV)
                    .put("GPSService", LocationPermissionHelper.isOpenGpsService())
                    .put("LocPer", PermissionManager.checkAllSelfPermissions(Utils.getApp(), Manifest.permission.ACCESS_FINE_LOCATION) ? 1 : 0)
                    .put("VPN", NetworkUtils.isOpenVpn())
                    .put("IPV6Only", NetworkUtils.isSupportOnlyIPv6(Utils.getApp()))
                    .put("NetProxy", NetworkUtils.isProxyNetwork())
                    .put("AlwaysFinishActivities", NetworkUtils.alwaysFinishActivities())
                    .put("AutoTime", NetworkUtils.autoTimeEnabled())
                    .put("SysDark", NightUtil.isDarkMode(Utils.getApp()))
                    .put("AppDark", NightUtil.isAppDarkMode())
                    .put("InnerStorage", availableInternalMemorySize + "/" + totalInternalMemorySize)
                    .put("ExternalStorage", availableExternalMemorySize + "/" + totalExternalMemorySize)
                    .info();

            NotificationCheckUtils.notificationChannelInfo();
            if (sFristReport) {
                sFristReport = false;
                AnrReporter.tryReport();
            }
        } catch (JSONException ignored) {

        }
    }

    private static Map<String, Object> getP7() {
        try {
            if (null != mapP7) return mapP7;
            mapP7 = new HashMap<>();
            mapP7.put("dotInfo", BuildInfoUtils.isDebug() ? BuildInfoHelper.getDotInfo().toString() : "");
            mapP7.put("crashProtectInit", CrashProtectInitHelper.isInitSuccess());
            mapP7.put("exitInfo", AnrReporter.getExitInfoSummary());
        } catch (Exception e) {
            TLog.error(LOG_TAG, e, "get p7 error ");
        }
        return mapP7;
    }
}
