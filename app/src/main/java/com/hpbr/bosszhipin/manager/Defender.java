package com.hpbr.bosszhipin.manager;

import android.os.Build;

import java.lang.reflect.Executable;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class Defender {
    private HookChecker hookChecker = new HookChecker();

    public String check(Class... clazzList) {
        StringBuilder result = new StringBuilder();
        for (Class aClass : clazzList) {
            loopMethods(aClass, result);
        }
        return result.toString();
    }

    private void loopMethods(Class clazz, StringBuilder result) {
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            int flags = -1;
            if (Modifier.isNative(method.getModifiers())
                    || (flags = hookChecker.getHookFlag(method)) > 0) {
                result.append(method).
                        append('#').append(getFlagTag(flags)).
                        append("\r\n");
            }
        }
    }

    private static String getFlagTag(int flags){
        String tag = "";
        switch (flags) {
            case HookChecker.COMPILE_DONT_BOTHER:
                tag = "COMPILE_DONT_BOTHER";
                break;
            case HookChecker.NATIVE:
                tag = "NATIVE2";
                break;
            default:
                tag ="NATIVE";
                break;
        }
        return tag;
    }

    final class HookChecker {
        public static final int NONE = 0;
        public static final int COMPILE_DONT_BOTHER = 1;
        public static final int NATIVE = 2;

        private boolean mCanReflect = true;
        private Method mMethod;
        private final int kAccCompileDontBother;
        private final int kAccNative = 0x0100;


        public HookChecker() {
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O_MR1) {
                kAccCompileDontBother = 1 << 25;//0x02000000
            } else {
                kAccCompileDontBother = 1 << 24;//0x01000000
            }
        }

        int getHookFlag(Method method) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                int accessFlag = getAccessFlags(method);
                if ((kAccCompileDontBother & accessFlag) == kAccCompileDontBother) {
                    return COMPILE_DONT_BOTHER;
                } else if ((kAccNative & accessFlag) == kAccNative) {
                    return NATIVE;
                }
            }
            return 0;
        }

        private int getAccessFlags(Method method) {
            try {
                Method afMethod = getAccessFlagsMethod();
                if (afMethod != null) {
                    int accessFlags = (int) afMethod.invoke(method);
                    return accessFlags;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return 0;
        }

        private Method getAccessFlagsMethod() {
            if (mMethod == null && mCanReflect) {
                try {
                    Class clazz = null;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        clazz = Executable.class;
                    } else {
                        Class tempClass = Method.class.getSuperclass();
                        if (tempClass != null &&
                                "java.lang.reflect.AbstractMethod".equals(tempClass.getName())) {
                            clazz = tempClass;
                        } else {
                            mCanReflect = false;
                        }
                    }
                    if (clazz != null) {
                        Method methodAccess = clazz.getMethod("getAccessFlags");
                        methodAccess.setAccessible(true);
                        mMethod = methodAccess;
                    }
                } catch (Throwable e) {
                    mCanReflect = false;
                }
            }

            return mMethod;
        }
    }
}
