package com.hpbr.bosszhipin.manager;

import static com.hpbr.bosszhipin.report.ReportContextUtils.realUploadFile;

import android.content.Context;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.report.McpTxApiRequestCallback;
import com.hpbr.bosszhipin.report.processor.Config;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.PrivacyAnalytics;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.techwolf.lib.tlog.TLog;
import com.twl.anti.DeviceInfo;
import com.twl.anti.EnvironmentCollect;
import com.twl.anti.InfoCollect;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.FileUploadResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

import okio.BufferedSink;
import okio.GzipSink;
import okio.Okio;

/**
 * 上报环境日志
 */
public final class EnvLogReporter {

    private static final String TAG = "EnvLogReporter";

    private static final int ONE_DAY_IN_MILLIS = 24 * 60 * 60 * 1000;
    public static final String PREFIX = "boss_";
    public static final String SUFFIX = ".txt.gz";

    //region 上传环境日志
    public static void uploadLogThenReport() {
        shouldMigrateLastReportTime();

        boolean shouldReport = shouldDoReport();
        // 用户从未上报过
        boolean neverReport = getLastReportTimeByUser() == 0;
        TLog.info(TAG, "uploadLogThenReport shouldReport=[%b], neverReport=[%b]", shouldReport, neverReport);
        if (shouldReport || neverReport /* 从未上报过的用户，执行上报 */) {
            innerUpload(neverReport);
            //nlp 打点全量，一天一次
            reportNlp(ApmAnalyticsAction.TYPE_ENV_INFO);
        } else {
            //nlp 打点可变的，不传TYPE_ENV_INFO的话，每次都传
            reportNlp(ApmAnalyticsAction.TYPE_VAR_INFO);
        }
    }

    private static void innerUpload(boolean neverReport) {
        try {
            Context context = App.getAppContext();
            DeviceInfo.setDeviceId(MobileUtil.getRealImei(context));
            File cacheDir = PathUtils.getCacheDirInternalFile(); // 使用 App 私有缓存目录
            File file = File.createTempFile(PREFIX, SUFFIX, cacheDir);
            //noinspection ResultOfMethodCallIgnored
            file.createNewFile();

            // 使用 Gzip 压缩，减小环境日志文件体积
            try (BufferedSink bufferedSink = Okio.buffer(new GzipSink(Okio.sink(file)))) {
                new EnvironmentCollect(App.getAppContext(), ApmManager.getExtendInfo()).printAll(bufferedSink);
            }

            /* 上传、打点 */
            realUploadFile(file, new McpTxApiRequestCallback() {
                @Override
                public void handleInChildThread(ApiData<FileUploadResponse> data) {
                    super.handleInChildThread(data);
                    FileUploadResponse resp = data.resp;
                    String fileUrl = resp.url;

                    if (!LText.empty(fileUrl)) {

                        //region 构建log地址参数并上报
                        JSONObject object = new JSONObject();
                        try {
                            JSONArray a = new JSONArray();
                            a.put(fileUrl);
                            object.put(ApmAnalyticsAction.BizKey.KEY_LOG_ADDRESS, a);
                        } catch (JSONException ignored) {

                        }

                        ApmAnalyzer.create()
                                .action(ApmAnalyticsAction.ACTION_SECURITY_LOG)
                                .param("p2", object.toString())
                                .param("p3", String.valueOf(neverReport ? 0 : 1)) // 是否是首次上传环境数据。0 没传过，1 传过
                                .p4("2")//环境数据版本号
                                .report();
                        //endregion

                        setLastReportTimeByUser();
                    }
                    deleteTmpFile();
                }

                @Override
                public void onSuccess(ApiData<FileUploadResponse> data) {

                }

                @Override
                public void onComplete() {

                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    deleteTmpFile();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                }

                private void deleteTmpFile() {
                    try {
                        //noinspection ResultOfMethodCallIgnored
                        file.delete();
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public boolean enableApmReport() {
                    return false; // 关闭 APM 打点
                }

                @Override
                @Config
                public int configFileProcessor() {
                    // 环境日志数据已压缩，直接加密
                    return Config.ENABLE_ENCRYPTION;
                }
            });
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static boolean gNlpReport = true;
    /**
     * 上报nlp需要打点信息
     *
     * @param type
     */
    private static void reportNlp(String type) {
        try {
            if (!gNlpReport) {
                return;
            }
            gNlpReport = false;
            InfoCollect infoCollect = new InfoCollect(App.get(), ApmManager.getExtendInfo());
            JSONObject jsonObject = null;
            switch (type) {
                case ApmAnalyticsAction.TYPE_ENV_INFO:
                    jsonObject = infoCollect.getInfoCollect();
                    privacyAnalytics(jsonObject);
                    break;
                case ApmAnalyticsAction.TYPE_VAR_INFO:
                    jsonObject = infoCollect.getVariableInfo();
                    break;
            }
            if (jsonObject != null) {
                jsonObject.put("did", MobileUtil.getDid());
                jsonObject.put("deviceid", MobileUtil.getUniqId(App.get()));
                ApmAnalyzer.create(true)
                        .action(ApmAnalyticsAction.ACTION_NLP, type)
                        .param("p2", jsonObject.toString())
                        .report();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 隐私统计
     *
     * @param jsonObject
     */
    private static void privacyAnalytics(JSONObject jsonObject) {
        PrivacyAnalytics.wifiInfo(jsonObject.optString("ssi", PrivacyAnalytics.NONE), null).report();
        PrivacyAnalytics.mac(jsonObject.optString("bss", "")).report();
    }

    private static void shouldMigrateLastReportTime() {
        // 904及以后版本，会根据当前登录用户记录最近一次上报时间
        // 将904之前版本记录的最近上报时间，迁移到904及以后版本
        long lastReportTime = getLastReportTime();
        if (lastReportTime > 0) {
            setLastReportTimeByUser(lastReportTime);
            // 清除老数据
            setLastReportTime(-1);
        }
    }

    private static boolean shouldDoReport() { // 上报策略
        // 按天上报
        boolean isMoreThanOneDay = isMoreThanOneDay();

        // 高峰时间段
        boolean isFastigium = isFastigium();

        // 50%几率的上报机会（减半上报）
        boolean isSelectedUser = isSelectedUser();

        if (isMoreThanOneDay) { // 上报前提是距离上次上报超过1天
            if (isFastigium) { // 高峰期减半上报
                return isSelectedUser;
            }
            return true;
        }
        return false;
    }

    private static boolean isMoreThanOneDay() {
        long lastTime = getLastReportTimeByUser();
        return System.currentTimeMillis() - lastTime > ONE_DAY_IN_MILLIS;
    }

    private static long getLastReportTime() {
        return SP.get().getLong(Constants.SP_UPLOAD_SECTURITY_LOG_FREQUENCY, 0);
    }

    private static long getLastReportTimeByUser() {
        return SpManager.get().user().getLong(Constants.SP_UPLOAD_SECTURITY_LOG_FREQUENCY, 0);
    }

    @SuppressWarnings({"unused", "RedundantSuppression"})
    private static void setLastReportTime() {
        setLastReportTime(System.currentTimeMillis());
    }

    private static void setLastReportTime(long lastReportTime) {
        SP.get().putLong(Constants.SP_UPLOAD_SECTURITY_LOG_FREQUENCY, lastReportTime);
    }

    /**
     * 重置环境数据上传标记
     */
    public static void resetLastReportTime() {
        TLog.info(TAG, "resetLastReportTime");
        setLastReportTimeByUser(0);
        BrandUtils.reset();
        BlackAppManager.ensureCheck();
    }

    private static void setLastReportTimeByUser() {
        setLastReportTimeByUser(System.currentTimeMillis());
    }

    private static void setLastReportTimeByUser(long lastReportTime) {
        SpManager.get().user().edit().putLong(Constants.SP_UPLOAD_SECTURITY_LOG_FREQUENCY, lastReportTime).apply();
    }

    private static boolean isFastigium() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        // 根据 APM 后台数据，优化高峰时段
        return hour >= 7 && hour <= 23;
    }

    private static boolean isSelectedUser() {
        // 获取动态下发的采样基数配置
        int sampleBase = getSampleBase();
        
        // 负数表示禁止上传
        if (sampleBase < 0) {
            return false;
        }
        
        Random random = new Random(System.currentTimeMillis());
        int ret = random.nextInt(sampleBase); // 0 to sampleBase-1
        return UserManager.getUID() % sampleBase == ret;
    }

    /**
     * 返回非0整数，可能是负数
     * 例如 -1,1,2,3,4
     */
    private static int getSampleBase() {
        int defaultSimpleBase = 2;
        int sampleBase = defaultSimpleBase;
        JSONObject configJsonObject = ApmManager.getPublicConfigJsonObject();
        if (configJsonObject != null) {
            sampleBase = configJsonObject.optInt("env_log_sample", defaultSimpleBase);
        }
        
        // 0时使用默认值
        if (sampleBase == 0) {
            sampleBase = defaultSimpleBase;
        }
        return sampleBase;
    }
    //endregion

}
