package com.hpbr.bosszhipin.manager;

import android.text.TextUtils;

import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.LoginDeviceConfirmRequest;
import net.bosszhipin.api.LoginDeviceConfirmResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * @author: zhoulei
 * @create: 2020-08-24 18:13
 * @description: 常用设备管理
 */
public class LoginDeviceManager {
    /**
     * 确认登录
     */
    public static void confirmLogin() {
        LoginDeviceConfirmRequest request = new LoginDeviceConfirmRequest(new ApiRequestCallback<LoginDeviceConfirmResponse>() {
            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onSuccess(ApiData<LoginDeviceConfirmResponse> data) {
                if (data != null && data.resp != null) {
                    LoginDeviceConfirmResponse resp = data.resp;
                    if (resp.source > 0 && !TextUtils.isEmpty(resp.uuid)) {
//                        LoginDeviceMsgLiveData.getInstance().postValue(resp);
                        LiveBus.with(ChannelConstants.APP_LOGIN_DEVICE_CONFIRM).postValue(resp);
                    }
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        HttpExecutor.execute(request);
    }
}
