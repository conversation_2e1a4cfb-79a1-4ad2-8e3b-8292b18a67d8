package com.hpbr.bosszhipin.manager;

import android.text.TextUtils;

import com.airbnb.lottie.LottieListener;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.custom.core.source.Source;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.techwolf.lib.tlog.TLog;

public class LottieErrorManager {
    public static final String TAG = "LottieErrorManager";
    private static volatile LottieErrorManager instance;
    private String errorMsg = "";
    LottieListener<Throwable> lottieListener;
    private LottieErrorManager() {
    }

    public static LottieErrorManager getInstance() {
        if (instance == null) {
            synchronized (LottieErrorManager.class) {
                if (instance == null) {
                    instance = new LottieErrorManager();
                }
            }
        }
        return instance;
    }
    public  void  reportError(String currentErrorMsg,String path,long fileSize){
        if (!TextUtils.equals(currentErrorMsg, errorMsg)) {
            TLog.info(TAG, "currentErrorMsg =%s,path =%s,fileSize=%s",currentErrorMsg,path,fileSize);
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_LOTTIE_ERROR)
                    .p2(currentErrorMsg)
                    .p3(TextUtils.isEmpty(path)?"":path)
                    .p4(String.valueOf(fileSize))
                    .report();
            errorMsg = currentErrorMsg;
        }
    }



    Source source ;
    public LottieListener<Throwable> getLottieListener(Source source) {
        this.source =source;
        if (lottieListener == null) {
            lottieListener = result -> {
                String currentErrorMsg = result.toString();
                if (!TextUtils.equals(errorMsg, currentErrorMsg)) {
                    ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_LOTTIE_ERROR)
                            .p2(currentErrorMsg)
                            .p3(source!=null?source.name():"")
                            .report();
                    errorMsg = currentErrorMsg;
                }

            };
        }
        return lottieListener;
    }

}
