package com.hpbr.bosszhipin.manager;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.twl.anti.DeviceInfo;

public class MemoryManager {

    private static boolean IS_REPORT_MEMORY_APM = false;

    /**
     * 上报可用内存
     * <p>
     * IS_REPORT_MEMORY_APM 只上报一次
     */
    public static void reportMemoryApm() {
        if (IS_REPORT_MEMORY_APM) return;
        IS_REPORT_MEMORY_APM = true;
        AppThreadFactory.POOL.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                String availableExternalMemorySize = DeviceInfo.getAvailableExternalMemorySize();
                String availableInternalMemorySize = DeviceInfo.getAvailableInternalMemorySize();

                int availableExternalSize = DeviceInfo.strToInt(availableExternalMemorySize);
                int availableInternalSize = DeviceInfo.strToInt(availableInternalMemorySize);
                long endTime = System.currentTimeMillis();

                long diffTime = (endTime - startTime) / 1000;

                //外置存储小于100M 或者 内置存储小于100M 或者 耗时超过1s
                if (availableExternalSize < 100 || availableInternalSize < 100 || diffTime > 1) {
                    ApmAnalyzer.create()
                            .action("action_temp", "memory_available")
                            .param("p2", availableInternalMemorySize)
                            .param("p3", availableExternalMemorySize)
                            .param("p4", getLevelByMemory(availableInternalSize))
                            .param("p5", getLevelByMemory(availableExternalSize))
                            .param("p6", String.valueOf(diffTime))
                            .reportNow();
                }

            } catch (Exception e) {
                e.printStackTrace();
                ApmAnalyzer.create()
                        .action("action_temp", "memory_catch")
                        .param("p2", "catch：" + e.getMessage())
                        .reportNow();
            }
        });
    }

    /**
     * 内存大小分级别
     */
    private static String getLevelByMemory(int availableExternalSize) {
        if (availableExternalSize < 10) {
            return "1";
        }
        if (availableExternalSize < 20) {
            return "2";
        }
        if (availableExternalSize < 30) {
            return "3";
        }
        if (availableExternalSize < 40) {
            return "4";
        }
        if (availableExternalSize < 50) {
            return "5";
        }
        if (availableExternalSize < 60) {
            return "6";
        }
        if (availableExternalSize < 70) {
            return "7";
        }
        if (availableExternalSize < 80) {
            return "8";
        }
        if (availableExternalSize < 90) {
            return "9";
        }
        return "10";
    }

}
