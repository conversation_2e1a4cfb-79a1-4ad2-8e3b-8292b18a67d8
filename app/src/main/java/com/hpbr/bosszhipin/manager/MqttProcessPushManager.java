package com.hpbr.bosszhipin.manager;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatNotifyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatProtocol;
import com.hpbr.bosszhipin.module.contacts.manager.MessageDecoder;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;

import java.util.LinkedList;
import java.util.List;

import message.handler.MessageUtils;

/**
 * Created by yuchaofei on 16/12/28.
 */

public class MqttProcessPushManager {
    private static final String TAG = "ReceiveMessageTask2";

    public static void showPush(byte[] data) {
        ChatProtocol.TechwolfChatProtocol protocol;
        try {
            protocol = ChatProtocol.TechwolfChatProtocol.parseFrom(data);
        } catch (Exception e) {
            MException.printError(e);
            L.d(TAG, "消息转换为ChatProtocol时异常", e);
            protocol = null;
        }
        if (protocol == null) return;
        TLog.info(TAG, "messageArrived data size = [%d] type = [%d]", data.length,protocol.getType());
        switch (protocol.getType()) {
            case MqttConfig.CHAT_TYPE_MESSAGE:
                LinkedList<ChatBean> messageList =
                        MessageDecoder.getInstance().builderMessage(protocol);
                if (messageList != null) {
                    handlerChatMessageList(messageList);
                }
                break;
            default:
                break;
        }
    }

    private static void handlerChatMessageList(List<ChatBean> list) {
        long currentUid = UserManager.getUID();
        for (ChatBean tempBean : list) {

            if (tempBean != null
                    && tempBean.message != null
                    && tempBean.message.messageBody != null
                    && (tempBean.message.fromUser != null && tempBean.message.fromUser.id != currentUid)
                    && tempBean.message.status != 2) {//如果是已读消息，不弹框
                ChatMessageBean messageBean = tempBean.message;
                switch (messageBean.messageBody.type) {
                    case 6:
                        ChatNotifyBean bean = messageBean.messageBody.notify;
                        String text = bean.title;
                        String url = bean.url;
                        NotifyUtils.handleArrivedMessageNotification(App.getAppContext(), "mqtt", text, url, tempBean.msgId, messageBean.fromUser.id, null, null);
                        break;
                    case 4:

                        final ChatMessageBean message = tempBean.message;
                        final ChatMessageBodyBean body = message.messageBody;
                        final ChatActionBean action = body.action;
                        if (body.action == null) {
                            return;
                        }
                        final int index = action.type;
                        if (index != 27 && index != 32 && index != 37 && index != 40) {
                            return;
                        }
                    case 7:
                        if (messageBean.messageBody.dialog != null) {
                            if (messageBean.messageBody.dialog.type == 3
                                    || messageBean.messageBody.dialog.type == 5
                                    || messageBean.messageBody.dialog.type == 7
                                    || messageBean.messageBody.dialog.type == 9
                                    || messageBean.messageBody.dialog.type == 10) {
                                return;
                            }
                        }
                    case 12:
                        if (messageBean.messageBody.hyperLinkBean != null) {
                            if (messageBean.messageBody.hyperLinkBean.templateId == 4) {
                                return;
                            }
                        }
                    default:
                        String desc = messageBean.pushText;
                        if (LText.empty(desc)) {
                            desc = MessageUtils.getMessageDescText(tempBean,"您有一条新消息");
                        }
                        NotifyUtils.handleArrivedMessageNotification(App.getAppContext(), "mqtt", desc, null, tempBean.msgId, tempBean.fromUserId, tempBean.message.soundUri, null);
                        break;
                }
            }
        }
    }


}
