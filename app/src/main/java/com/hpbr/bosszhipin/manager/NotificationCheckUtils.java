package com.hpbr.bosszhipin.manager;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_PUSH_CLOSED;

import android.app.Activity;
import android.app.NotificationChannel;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationManagerCompat;
import androidx.fragment.app.FragmentActivity;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.push.PushSdkManager;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.log.AnalyticLog;
import com.hpbr.bosszhipin.utils.log.LogWise;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LText;
import com.twl.daemon.utils.NotificationsUtil;
import com.twl.ui.ToastUtils;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NotificationCheckUtils {
    public static boolean dialogIsShowing; // F1引导弹窗是否显示
    static String brand = Build.BRAND;
    static String manu = Build.MANUFACTURER;

    public static void checkPermission(Activity context) {
        if (!LText.empty(brand)) brand = brand.toUpperCase();
        if (!LText.empty(manu)) manu = manu.toUpperCase();
        if (LText.equal(brand, "OPPO") || LText.equal(manu, "OPPO")) {//小米手机或者是miui用小米Push
            checkNotificationOpend(context);
        }

        reportNotificationClosed(context);
    }

    private static void reportNotificationClosed(Activity context) {
        // 埋点，判断用户手机的通知是否关闭了
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            if (!areNotificationsEnabled(context)) {
                // 通知未开启，上报
                AnalyticsFactory.create().action(ACTION_PUSH_CLOSED).build();
            }
        }
    }

    private static final String CHECK_OP_NO_THROW = "checkOpNoThrow";
    private static final String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";

    static SpImpl sp = SpFactory.create(App.get(), "open_notification");
    static String NOTIFICATION_TIME = "notification_time_key";
    static String NOTIFICATION_CANCEL_COUNT = "notification_cancel_count";

    private static void checkNotificationOpend(final Activity context) {
        PushSdkManager.getInstance().requestPermission();
    }

    /**
     * 打开设置通知权限页面(Android 13的Settings有专门针对Notification的页面)
     */
    public static void openNotificationSetting(Context context) {
        try {
            Intent intent = new Intent();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
                intent.putExtra(Settings.EXTRA_CHANNEL_ID, context.getApplicationInfo().uid);
            } else {
                intent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
                intent.putExtra("app_package", context.getPackageName());
                intent.putExtra("app_uid", context.getApplicationInfo().uid);
            }
            AppUtil.startActivity(context, intent);
        } catch (Exception e) {
            e.printStackTrace();

            Intent intent = new Intent();
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", context.getPackageName(), null);
            intent.setData(uri);
            AppUtil.startActivity(context, intent);
        }
    }

    public static FragmentActivity unwrap(Context context) {
        FragmentActivity targetActivity = null;
        if (context instanceof FragmentActivity) {
            targetActivity = (FragmentActivity) context;
        } else  {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if(topActivity instanceof FragmentActivity){
                targetActivity = (FragmentActivity) topActivity;
            }
        }
        return targetActivity;
    }
    /**
     * 请求通知权限
     *
     * @param activity
     * @param runnable
     */
    public static void requestNotificationPermission(Activity activity, @Nullable Runnable runnable) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!(activity instanceof FragmentActivity)) return;
            PermissionHelper.getPostNotificationHelper((FragmentActivity) activity).setPermissionCallback((yes, permission) -> {
                if (yes) {
                    if (runnable != null) runnable.run();
                } else {
                    ToastUtils.showText("请开启通知权限");
                }
                NotificationCheckUtils.dialogIsShowing = false;
            }).requestPermission();
        } else {
            if (!areNotificationsEnabled(activity)) {
                openNotificationSetting(activity);
            } else {
                if (runnable != null) runnable.run();
            }
            NotificationCheckUtils.dialogIsShowing = false;
        }
    }

    /**
     * 请求通知权限
     *
     * @param activity
     */
    public static void requestNotificationPermissionToSet(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!(activity instanceof FragmentActivity)) return;
            PermissionHelper.getPostNotificationHelper((FragmentActivity) activity,true,true).setPermissionCallback(new PermissionCallback<PermissionData>() {
                @Override
                public void onResult(boolean yes, @NonNull PermissionData permission) {
                    if (!yes) {
                        ToastUtils.showText("请开启通知权限");
                    }
                }

                @Override
                public void onNoRemindersPermission(@NonNull PermissionData permission) {
                    openNotificationSetting(activity); // 不出提示直接跳转通知设置
                }
            }).requestPermission();
        } else {
            if (!areNotificationsEnabled(activity)) {
                openNotificationSetting(activity);
            }
        }
    }

    public static boolean isShowNewGuideCloseNotification() {
        if (areNotificationsEnabled(Utils.getApp())) return false;
        return true;
    }


    public static void requestNotificationPermissionABTest2(Activity activity, PermissionCallback<PermissionData> permissionCallback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!(activity instanceof FragmentActivity)) return;
            PermissionHelper.getPostNotificationHelper((FragmentActivity) activity).setPermissionCallback((yes, permission) -> {
                NotificationCheckUtils.dialogIsShowing = false;
                if (permissionCallback!=null){
                    permissionCallback.onResult(yes,permission);
                }
            }).requestPermission();
        } else {
            if (!areNotificationsEnabled(activity)) {
                showPushNotifyToSetGuide(activity,permissionCallback);
            } else {
                if (permissionCallback!=null){
                    permissionCallback.onResult(true,null);
                }
            }
            NotificationCheckUtils.dialogIsShowing = false;
        }
    }

    public static void showPushNotifyToSetGuide(Activity activity, PermissionCallback<PermissionData> permissionCallback) {
        boolean isLandscape=false;
        if (activity != null) {
            isLandscape = activity.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE;
        }
        DialogUtils dialogUtils = new DialogUtils.Builder(activity)
                .setDoubleButton()
                .setCancelable(false)
                .setTitle("请在手机的“设置”中允许BOSS直聘访问您的通知")
                .setDesc("用于接收推送通知消息")
                .setLandscape(isLandscape)
                .setNegativeAction(twl.lib.common.R.string.string_location_permission_dialog2_negative_action,view -> {
                    if (permissionCallback!=null){
                        permissionCallback.onRemindersClick(PermissionCallback.REMINDERS_CLICK_NO);
                    }

                })
                .setPositiveAction(twl.lib.common.R.string.string_location_permission_dialog2_positive_action, v -> {
                    openNotificationSetting(activity);
                    if (permissionCallback!=null){
                        permissionCallback.onRemindersClick(PermissionCallback.REMINDERS_CLICK_OK);
                    }
                })
                .build();
        dialogUtils.show();
    }

    /**
     * 通知权限是否已开启
     *
     * @param context
     * @return
     */
    public static boolean areNotificationsEnabled(Context context) {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    public static void notificationChannelInfo() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            List<NotificationChannel> notificationAllChannel = NotificationsUtil.getNotificationAllChannel(Utils.getApp());
            Map<String, Object> jsonObjectMap = new HashMap<>();
            if (notificationAllChannel!=null) {
                for (NotificationChannel notificationChannel : notificationAllChannel) {
                    Map<String, String> stringStringMap = new HashMap<>();
                    stringStringMap.put("name", String.valueOf(notificationChannel.getName()));
                    stringStringMap.put("importance", String.valueOf(notificationChannel.getImportance()));
                    stringStringMap.put("sound", String.valueOf(notificationChannel.getSound()));
                    jsonObjectMap.put(notificationChannel.getId(), new JSONObject(stringStringMap));
                }
                LogWise.appInfo(AnalyticLog.BaseInfoAnalytic.PUSH_ENV)
                        .put("notifyVibrateAndSound", NotifyUtils.getNotifyVibrateAndSound())
                        .put("pushMsg", NotifyUtils.isPushMsg()) //设置-消息推送设置-聊天消息接收
                        .put("noDisturbing", NotifyUtils.isNoDisturbingEnabled()) //设置-消息推送设置-消息免打扰 是否开启
                        .put("noDisturbingTime", NotifyUtils.getNoDisturbingTimeRange()) //设置-消息推送设置-消息免打扰 是否开启
                        .put("importance", NotificationManagerCompat.from(Utils.getApp()).getImportance())
                        .put("pushPer", NotificationsUtil.isNotificationEnabled(Utils.getApp()))
                        .put("channel", new JSONObject(jsonObjectMap)).info();
            }
        }
    }
}
