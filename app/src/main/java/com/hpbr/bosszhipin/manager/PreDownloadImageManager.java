package com.hpbr.bosszhipin.manager;

import android.content.Context;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseApplication;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.MD5;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.CompanyUrlConfig;
import net.bosszhipin.api.VrGuideImageResponse;
import net.bosszhipin.base.BaseFileDownloadCallback;
import net.bosszhipin.base.FileDownloadRequest;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * @ClassName ：PreDownloadImageManager
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/8/4  6:03 下午
 */
public class PreDownloadImageManager {

    /*VR 引导图缓存文件夹名称*/
    public static final String VR_GUIDE_CACHE_DIR = "vr_guide_cache";
    /*图片存储地址*/
    public static final String IMAGE_CACHE_DIR = "image_cache_dir";

    private static VrGuideImageResponse vrGuideImageResponse;

    public static void preLoadImage() {
        if (UserManager.isBossRole()) {
            downloadVrGuideImge();
        }
    }

    @Nullable
    public static VrGuideImageResponse getVrGuideImageResponse() {
        return vrGuideImageResponse;
    }

    private static void downloadVrGuideImge() {
        SimpleApiRequest.GET(CompanyUrlConfig.URL_BRAND_COMPLETE_PANORAMIC_PICTURE_STATIC_IMAGE)
                .setRequestCallback(new SimpleCommonApiRequestCallback<VrGuideImageResponse>() {
                    @Override
                    public void onSuccess(ApiData<VrGuideImageResponse> data) {
                        super.onSuccess(data);
                        if (data == null || data.resp == null) return;
                        cacheData(data.resp);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                }).execute();
    }

    /**
     * 缓存数据
     *
     * @param response
     */
    public static void cacheData(@NonNull VrGuideImageResponse response) {
        vrGuideImageResponse = response;
        if (vrGuideImageResponse.pageAddress != null) {//图一信息
            getVrGuideFileByUrl(vrGuideImageResponse.pageAddress.vrGuideVideoUrl, DownLoadFileType.VIDEO);
        }

        if (vrGuideImageResponse.pageAnimate != null) {//图二信息
            getVrGuideFileByUrl(vrGuideImageResponse.pageAnimate.vrGuideVideoUrl, DownLoadFileType.VIDEO);
        }
    }

    /**
     * 获取预览用h5页面url
     */
    @Nullable
    public static String getPreviewH5PageUrl() {
        return vrGuideImageResponse != null ? vrGuideImageResponse.previewH5PageUrl : null;
    }

    /**
     * 获取拍摄用h5页面url
     *
     * @return
     */
    public static String getShotH5PageUrl() {
        return vrGuideImageResponse != null ? vrGuideImageResponse.shotH5PageUrl : null;
    }

    /**
     * 通过VR资源的Url地址，获取对应的File
     *
     * @param url
     * @return
     */
    public static File getVrGuideFileByUrl(String url, @DownLoadFileType int fileType) {
        return getFileByImageUrl(BaseApplication.getApplication(), url, VR_GUIDE_CACHE_DIR, fileType);
    }

    /**
     * 通过图片地址，获取对应的File（如果本地已经有此File，那么直接返回本地的File。如果本地没有需要网络下载图片）
     *
     * @param context
     * @param imageUrl
     * @return
     */
    public static File getFileByImageUrl(Context context, String imageUrl, @NonNull String childDir, @DownLoadFileType int fileType) {
        if (LText.isEmptyOrNull(imageUrl) || imageUrl.length() <= 1) {
            return null;
        }
        final String path = PathUtils.getCacheDirChildPathExternalFirst(childDir);
        String fileName = MD5.convert(imageUrl);
        String newFileName;
        if (fileType == DownLoadFileType.VIDEO) {
            newFileName = TextUtils.isEmpty(fileName) ? imageUrl.substring(imageUrl.lastIndexOf("/") + 1).concat(".mp4") : fileName.concat(".mp4");//由于视频资源通过server端下发过来的也是以.jpg结尾的，所以这里判断如果是视频格式的需要改下后缀名
        } else {
            newFileName = TextUtils.isEmpty(fileName) || !imageUrl.contains(".") ? imageUrl.substring(imageUrl.lastIndexOf("/") + 1) : fileName.concat(imageUrl.substring(imageUrl.lastIndexOf(".")));
        }

        File fileReal = FileUtils.getFileByPath(path, newFileName);
        //如果文件已存在，那么返回
        if (fileReal.exists()) {
            return fileReal;
        }

        FileDownloadRequest request = new FileDownloadRequest(imageUrl, path, fileName + ".tmp", new BaseFileDownloadCallback() {
            @Override
            public void onFail(String url, ErrorReason reason) {
            }

            @Override
            public void onSuccess(String url, File file) {
                try {
                    file.renameTo(fileReal);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        HttpExecutor.download(request);
        return null;
    }

    /**
     * 判断是否已经下载了所有的图片资源
     */
    public static boolean isHaveDownloadAllImageResource() {
        File firstVrGuideVideo = null;
        File secondVrGuideVideo = null;

        VrGuideImageResponse vrGuideImageResponse = PreDownloadImageManager.getVrGuideImageResponse();
        if (vrGuideImageResponse == null) return false;
        if (vrGuideImageResponse.pageAddress != null) {
            firstVrGuideVideo = PreDownloadImageManager.getVrGuideFileByUrl(vrGuideImageResponse.pageAddress.vrGuideVideoUrl, DownLoadFileType.VIDEO);
        }
        if (vrGuideImageResponse.pageAnimate != null) {
            secondVrGuideVideo = PreDownloadImageManager.getVrGuideFileByUrl(vrGuideImageResponse.pageAnimate.vrGuideVideoUrl, DownLoadFileType.VIDEO);
        }

        return FileUtils.isFileExists(firstVrGuideVideo) &&
                FileUtils.isFileExists(secondVrGuideVideo);
    }

    /**
     * 下载类型
     */
    @IntDef({DownLoadFileType.IMAGE, DownLoadFileType.VIDEO})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DownLoadFileType {
        /*图片*/
        int IMAGE = 1;
        /*视频*/
        int VIDEO = 2;
    }


}
