package com.hpbr.bosszhipin.manager;


import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.exception.MException;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.module.announce.NoticeInfo;
import com.hpbr.bosszhipin.module.webview.SingleWebPage2Activity;
import com.hpbr.bosszhipin.module.webview.WebViewActivity;
import com.hpbr.bosszhipin.protocol.ProtocolAction;
import com.hpbr.bosszhipin.protocol.ProtocolActionHandler;
import com.hpbr.bosszhipin.protocol.ProtocolConstants;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.MapUtils;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.sankuai.waimai.router.Router;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.ui.ToastUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by monch on 15/11/13.
 */
public class ZPManager {

    protected final String TAG = getClass().getSimpleName();

    /**
     * 接收到消息的默认直聘协议
     */
    public static final String PUSH_MANAGER_DEFAULT = "bosszp://bosszhipin.app/openwith?type=opencontact";
    /**
     * 接收到系统消息的直聘协议
     */
    public static final String PUSH_SYSTEM_MESSAGE = "bosszp://bosszhipin.app/openwith?type=opennotice";
    /**
     * 打开个人中心页面
     */
    public static final String PUSH_SYSTEM_USER_CENTER = "bosszp://bosszhipin.app/openwith?type=openpersonal";

    private static final String LOG_TAG = "ZPManager";
    // URL参数
    public Map<String, String> params;
    private final Context context;
    private final String url;
    // URL类型，-1为未知，0为http协议，1为bosszp协议，2为手机协议
    private int urlType;
    // 直聘协议类型
    private String protocolType;

    @Nullable
    private String getParams(@NonNull String key) {
        return MapUtils.get(params, key);
    }

    public ZPManager(Context context, String url) {
        Activity activity = null;
        try {
            activity = unwrap(context);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (activity != null) {
            this.context = activity;
        } else {
            this.context = context;
        }

        this.url = url;
        TLog.info(LOG_TAG, "======protocolUrl：%s", url);
        if (!TextUtils.isEmpty(url)) {
            UrlHandler.handler(this, url);
        }
    }

    private static Activity unwrap(Context context) {
        while (!(context instanceof Activity) && context instanceof ContextWrapper) {
            context = ((ContextWrapper) context).getBaseContext();
        }

        return (Activity) context;
    }

    public int getUrlType() {
        return urlType;
    }

    /***
     * 1、 （750-800）|| 写死的5个通知ID  到 通知
     * 2、（700-750）||（微jd，boss周报）到 f3
     * 3、  默认 到 聊天
     * **/
    public static String getPushDefaultUrl(long fromUserId, String defaultUrl) {
        if (!TextUtils.isEmpty(defaultUrl)) return defaultUrl;


        //通知
        if ((fromUserId >= 750 && fromUserId < 800) || NoticeInfo.isToNoticeActivity(fromUserId)) {
            return PUSH_SYSTEM_MESSAGE;
        }

        //跳转到f3
        if ((fromUserId >= 700 && fromUserId < 750) || fromUserId == MqttConfig.CHAT_SYSTEM_SMALL_USER_ID || fromUserId == MqttConfig.CHAT_SYSTEM_WEEKLY_USER_ID) {
            return PUSH_SYSTEM_USER_CENTER;
        }

        return PUSH_MANAGER_DEFAULT;
    }

    public static String handlerWebUrlRule(String url) {
        // 适配钉钉
        if (url.startsWith(UrlHandler.URL_TYPE_DINGDING)) {
            return url;
        }

        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = (AndroidDataStarGray.getInstance().isWebUrlForceHttps() ? "https://" : "http://") + url;
        }
        return url;
    }

    public void showProgressDialog() {
        if (context instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) context;
            activity.showProgressDialog("");
        }
    }

    public void dismissProgressDialog() {
        if (context instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) context;
            activity.dismissProgressDialog();
        }
    }

    /**
     * URL是否为系统协议
     *
     * @return
     */
    public boolean isSystemUrl() {
        return urlType == 2;
    }

    /**
     * URL是否为直聘协议类型
     *
     * @return
     */
    public boolean isZPUrl() {
        return urlType == 1;
    }

    /**
     * URL是否为Web类型
     *
     * @return
     */
    public boolean isWebUrl() {
        return urlType == 0;
    }

    public void handler() {
        if (context == null || TextUtils.isEmpty(url)) return;
        try {
            if (urlType == 0) {
                // 处理WebURL
                handlerWebUrl(url);
            } else if (urlType == 1) {
                // 处理直聘协议URL
                handlerProtocolUrl();
            } else if (urlType == 2) {
                // 处理系统协议
                handlerSystemUrl();
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (BuildInfoUtils.isDebug()) {
                throw new IllegalStateException(e);
            } else {
                CrashReport.postCatchedException(new IllegalStateException("handler=" + url, e));
            }
        }
        L.d("handler", "handler=url:" + url);
    }

    private void handlerWebUrl(String url) {
        int fullScreen = isZhiPinHtmlFullScreen(url) ? 1 : 0; //全屏
        handlerWebUrl(url, "", "", fullScreen, fullScreen, 0);
    }

    /**
     * 支持url加参数fullScreen=1，新打开全屏webview，例如：zhipin.com/mpa/html/xxx?fullScreen=1
     * 新项目 zhipin.com/mpa/html
     * 老项目里没有 /mpa/html
     */
    public static boolean isZhiPinHtmlFullScreen(String url) {
        return !LText.isEmptyOrNull(url) && url.contains("fullScreen=1") && (url.contains("zhipin.com") || UrlHandler.isBossZPUrl(url));
    }

    private boolean isWhiteSystemBroswerUrl(@NonNull String url) {
        List<String> systemBrowser = Arrays.asList("meeting.tencent.com", "meeting.dingtalk.com", "dingtalk://", // 适配新版钉钉协议: dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true&_dt_no_comment=false
                "vc.feishu.cn", "zoom.com.cn");
        for (String sysUrl : systemBrowser) {
            if (url.contains(sysUrl)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理WebURL
     *
     * @param url
     * @param noHeader   1 - 无头
     * @param fullScreen
     */
    public void handlerWebUrl(String url, String title, String subtitle, int fullScreen, int noHeader, int isSupportNebulaH5Bridge) {
        url = handlerWebUrlRule(url);
        //指定的http地址,点击需要外部浏览器打开
        if (isWhiteSystemBroswerUrl(url)) {
            handlerSystemWebUrl(url);
        } else {
            Intent intent = new Intent(context, WebViewActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.putExtra(Constants.DATA_URL, url);
            intent.putExtra(WebViewActivity.WEB_VIEW_TITLE, title);
            intent.putExtra(WebViewActivity.WEB_VIEW_SUB_TITLE, subtitle);
            intent.putExtra(WebViewActivity.WEB_VIEW_NO_TITLE_HEADER, noHeader);
            intent.putExtra(WebViewActivity.WEB_VIEW_FULL_SCREEN, fullScreen);
            intent.putExtra(WebViewActivity.WEB_VIEW_IS_SUPPORT_NEBULA_H5_BRIDGE, isSupportNebulaH5Bridge);
            AppUtil.startActivity(context, intent);
        }
        if (params.get("ba") != null) {
            AnalyticsFactory.bgAction(params.get("ba"));
        }
    }


    private void handlerSystemWebUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri uri = Uri.parse(url);
        intent.setData(uri);
        AppUtil.startActivity(App.getAppContext(), intent);
    }

    /**
     * 处理系统URL
     */
    private void handlerSystemUrl() {
        try {
            if (url.startsWith(UrlHandler.URL_TYPE_SYSTEM_SMS_TO)) {
                handleSystemUri();
                return;
            }
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_DIAL);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse(url));
            AppUtil.startActivity(context, intent);
        } catch (Exception e) {
            MException.printError("处理系统URL异常", e);
        }
    }

    private void handleSystemUri() {
        try {
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            intent.addCategory(Intent.CATEGORY_DEFAULT);
            intent.setData(Uri.parse(url));
            AppUtil.startActivity(context, intent);
        } catch (Exception e) {
            MException.printError("处理系统URL异常", e);
        }
    }

    /**
     * 处理直聘协议URL
     */
    private void handlerProtocolUrl() {
        if (params == null || TextUtils.isEmpty(protocolType)) {
            return;
        }
        if (params.get("ba") != null) {
            AnalyticsFactory.bgAction(params.get("ba"));
        }


        String closeWebViewStr = getParams("closeWebView");
        int closeWebView = LText.getInt(closeWebViewStr, 0);
        Context realHandlerContext = context;
        if (closeWebView == 1) {
            //销毁当前的activity
            AppUtil.finishActivity(context);
            realHandlerContext = App.getAppContext();
        } else if (closeWebView == 2) {
            //销毁所有的webview
            if (context instanceof SingleWebPage2Activity) {
                ForegroundUtils.get().finishActivity(SingleWebPage2Activity.class, true, true);
                realHandlerContext = App.getAppContext();
            } else if (context instanceof WebViewActivity) {
                ForegroundUtils.get().finishActivity(WebViewActivity.class, true, true);
                realHandlerContext = App.getAppContext();
            }
        }

        boolean isRunGroupProtocol = false;//是否走了拆分协议
        boolean isRunNotSupportProtocol = false;//是否是不支持的协议
        if (ProtocolActionHandler.handler(protocolType, realHandlerContext, params)) {
            isRunGroupProtocol = true;
        } else {
            isRunNotSupportProtocol = true;
        }
        // 所有协议统计埋点
        final String sid = MapUtils.get(params, "sid");
        final String type = protocolType;
        if (!TextUtils.isEmpty(sid)) {
            AnalyticsFactory.create().action("boss_protocol").param("p", sid).param("p2", type).build();
        }

        if (isRunNotSupportProtocol) {
            // 对于不支持的协议，增加toast提示
            ToastUtils.showText(context.getString(R.string.string_please_upgrade_and_try));
        }

        reportApmProtocolExpose(protocolType, url, isRunGroupProtocol, isRunNotSupportProtocol);
    }

    private static void reportApmProtocolExpose(String protocol, String url, boolean isRunGroupProtocol, boolean isRunNotSupportProtocol) {
        try {
            ProtocolAction service = Router.getService(ProtocolAction.class, protocol);
            String serviceName = null != service ? service.getClass().getName() : "";
            ApmAnalyzer.create()
                    .action("action_protocol", isRunNotSupportProtocol ? "notSupportProtocol" : "success")
                    .param("p2", protocol)//协议类型
                    .param("p3", url)//协议全拼
                    .param("p4", isRunGroupProtocol + "")//是否走了拆分协议
                    .param("p5", serviceName)//如果走了拆分协议，所属类名
                    .reportNow();

        } catch (Exception e) {
            ApmAnalyzer.create()
                    .action("action_protocol", "catch")
                    .param("p2", protocol)
                    .param("p3", url)
                    .param("p4", isRunGroupProtocol + "")
                    .reportNow();
        }
    }

    public static void autoReportLog(int source) {
        new ZPManager(Utils.getApp(), "bosszp://bosszhipin.app/openwith?type=autoReport&source" + source).handler();
    }

    /**
     * 判断类型是否与参数相同
     *
     * @param typeValue
     * @return
     */
    public boolean isType(String typeValue) {
        return protocolType != null && protocolType.equals(typeValue);
    }

    /**
     * 类型为发送事件动作协议
     *
     * @return
     */
    public boolean isTypeActionSend() {
        return isType(ProtocolConstants.Chat.TYPE_ACTION_SEND);
    }

    /**
     * 类型为打开Boss职位详情页面
     *
     * @return
     */
    public boolean isTypeStartJobDefault() {
        return isType(ProtocolConstants.App.TYPE_OPEN_JOB_DETAIL);
    }

    public boolean isUpdateJobStatus() {
        return isType(ProtocolConstants.Boss.TYPE_UPDATE_JOB_STATUS);
    }

    /**
     * 是否打开举报对话框
     *
     * @return
     */
    public boolean isOpenReportDialog() {
        return isType(ProtocolConstants.Chat.TYPE_OPEN_REPORT_DIALOG);
    }

    public boolean isRefreshPage() {
        return isType(ProtocolConstants.Boss.TYPE_REFRESH_PAGE);
    }

    /**
     * 是否为支付阻断
     */
    public boolean isBlockPay() {
        return isType(ProtocolConstants.Business.TYPE_BLOCK_PRE_ORDER);
    }

    /**
     * 是否为新支付阻断
     */
    public boolean isZPBlockV2Pay() {
        return isType(ProtocolConstants.Business.TYPE_ZP_BLOCK_V2_PRE_ORDER);
    }

    /**
     * 是否为道具预下单协议
     */
    public boolean isItemBzbPay() {
        return isType(ProtocolConstants.Business.TYPE_ITEM_BZB);
    }

    /**
     * 1011.254【商业】畅聊版达上限引导线下道具消耗
     */
    public boolean isItemOfflineItemUse() {
        return isType(ProtocolConstants.Business.TYPE_ITEM_OFFLINE_ITEM_USE);
    }

    public boolean isBackToChat() {
        return isType(ProtocolConstants.Chat.TYPE_BACK_TO_CHAT);
    }

    /*购买成功后原路径返回*/
    public boolean isBackToOriginalPath() {
        return isType(ProtocolConstants.Business.TYPE_BZB_BACK_TO_ORIGINAL_PATH);
    }

    /**
     * 【6.13 统一处理】处理完协议之后判断是否销毁WebView
     *
     * @return
     */
    public boolean needCloseWebViewAfterHandleZPUrl() {
        String closePage = MapUtils.get(params, "closepage");
        int close = LText.getInt(closePage);
        return close == 1;
    }

    public boolean isNormalJobUpgradePage() {
        return isType(ProtocolConstants.Business.TYPE_NORMAL_JOB_UPGRADE);
    }

    public boolean isPromoteWaitOpenJobListPage() {
        return isType(ProtocolConstants.Boss.TYPE_PROMOTE_WAIT_OPEN_JOB_LIST);
    }

    public boolean isShowEmergencyDialog() {
        return isType(ProtocolConstants.Boss.TYPE_EMERGENCY_DIALOG);
    }

    public boolean isGovernmentService() {
        return isType(ProtocolConstants.Settings.TYPE_GOVERNMENT_SERVICE);
    }

    public boolean isTypeJobQuesAnswer() {
        return isType(ProtocolConstants.Chat.TYPE_JOB_QUES_ANSWER);
    }

    public boolean isTypeBluePostExp() {
        return isType(ProtocolConstants.App.TYPE_BLUE_POST_EXP);
    }

    public boolean isTypeF1RelativeRecommendGuide() {
        return isType(ProtocolConstants.Boss.TYPE_F1_RELATIVE_RECOMMEND_QUERY_WORD);
    }

    public boolean isTelCall() {
        return isType(ProtocolConstants.App.TYPE_TEL_CALL);
    }

    public boolean isTypeSendTextMessage() {
        return isType(ProtocolConstants.Chat.TYPE_SEND_TEXT_MESSAGE);
    }

    public static class UrlHandler {
        private static final String URL_TYPE_DINGDING = "dingtalk://";
        private static final String URL_TYPE_SYSTEM_TEL = "tel:";
        private static final String URL_TYPE_SYSTEM_MAIL_TO = "mailto:";
        private static final String URL_TYPE_SYSTEM_SMS_TO = "sms:";
        public static final String URL_TYPE_ZHIPIN = "bosszp://";
        private static final String URL_TYPE_ZHIPIN2 = "bossz2p://";

        static void handler(ZPManager manager, String url) {
            manager.urlType = isBossZPUrl(url) ? 1 : isWebUrl(url) ? 0 : isSystemUrl(url) ? 2 : -1;
            Map<String, String> params = getParams(url);
            manager.params = params;
            if (params != null && params.containsKey("type")) {
                manager.protocolType = params.get("type");
            }
        }

        public static boolean isBossZPUrl(String url) {
            return url.startsWith(URL_TYPE_ZHIPIN) || url.startsWith(URL_TYPE_ZHIPIN2);
        }

        static boolean isWebUrl(String url) {
            return LText.isWebSite(url) || isWhiteUrl(url);
        }

        /**
         * 外部白名单协议, 复用跳转h5流程
         *
         * @param url
         * @return
         */
        static boolean isWhiteUrl(String url) {
            String u = url.toLowerCase();
            return u.startsWith(URL_TYPE_DINGDING);
        }

        static boolean isSystemUrl(String url) {
            String u = url.toLowerCase();
            return u.startsWith(URL_TYPE_SYSTEM_TEL) || u.startsWith(URL_TYPE_SYSTEM_MAIL_TO) || u.startsWith(URL_TYPE_SYSTEM_SMS_TO);
        }

        public static Map<String, String> getParams(@NonNull String url) {
            Map<String, String> map = new HashMap<>();
            if ((url.contains("bosszp://") || url.contains("bosszp2://"))) {
                String[] str = url.split("\\?");
                if (str.length > 1) {
                    String[] stri = str[1].split("&");
                    for (String s : stri) {
                        String[] strin = s.split("=");
                        if (strin.length == 2) {
                            map.put(strin[0], strin[1]);
                        }
                    }
                }
            }
            return map;
        }

        public static String makeProtocol(String type, Map<String, String> params) {
            if (TextUtils.isEmpty(type)) return null;
            StringBuilder sb = new StringBuilder();
            sb.append("bosszp://bosszhipin.app/openwith?type=").append(type);
            if (params != null && !params.isEmpty()) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
            return sb.toString();
        }

        public static String addParams(String url, String... value) {
            StringBuilder stringBuilder = new StringBuilder(url);
            Map<String, String> params = getParams(url);
            for (int i = 0; i < value.length; ) {
                if (value.length > i + 1) {
                    String key = value[i];
                    if (!params.containsKey(key) && !TextUtils.isEmpty(key)) {
                        stringBuilder.append("&").append(key).append("=").append(value[i + 1]);
                    }
                }
                i += 2;
            }
            return stringBuilder.toString();
        }

    }
}
