package com.hpbr.bosszhipin.manager;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.monch.lbase.util.LText;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class ZPProtocolUtil {

    private Map<String, String> params;// URL参数
    private final String url;
    private int urlType;// URL类型，-1为未知，0为http协议，1为bosszp协议，2为手机协议
    private String protocolType;// 直聘协议类型


    public ZPProtocolUtil(@NonNull String url) {
        this(url, null);
    }

    public ZPProtocolUtil(@NonNull String url, @Nullable HandleCallback handleCallback) {
        this.url = url;
        if (!TextUtils.isEmpty(url)) {
            UrlHandler.handler(this, url, handleCallback);
        }
    }

    public Map<String, String> getParams() {
        return params;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public interface HandleCallback {
        void handle(String protocolType, Map<String, String> params);
    }

    public static class UrlHandler {

        private static final String TYPE_OPEN_NOTICE = "openNotifications";
        private static final String URL_TYPE_SYSTEM_TEL = "tel:";
        private static final String URL_TYPE_SYSTEM_MAIL_TO = "mailto:";
        private static final String URL_TYPE_SYSTEM_SMS_TO = "sms:";
        public static final String URL_TYPE_ZHIPIN = "bosszp://";
        private static final String URL_TYPE_ZHIPIN2 = "bossz2p://";

        static void handler(@NonNull ZPProtocolUtil util, @NonNull String url, @Nullable HandleCallback handleCallback) {
            util.urlType = isBossZPUrl(url) ? 1 : isWebUrl(url) ? 0 : isSystemUrl(url) ? 2 : -1;
            Map<String, String> params = getParams(url);
            util.params = params;
            if (params.containsKey("type")) {
                util.protocolType = params.get("type");
            }
            if (handleCallback != null) {
                handleCallback.handle(util.protocolType, util.params);
            }
        }

        static boolean isBossZPUrl(String url) {
            return url.startsWith(URL_TYPE_ZHIPIN) || url.startsWith(URL_TYPE_ZHIPIN2);
        }

        static boolean isWebUrl(String url) {
            return LText.isWebSite(url);
        }

        static boolean isSystemUrl(String url) {
            String u = url.toLowerCase(Locale.getDefault());
            return u.startsWith(URL_TYPE_SYSTEM_TEL) || u.startsWith(URL_TYPE_SYSTEM_MAIL_TO) || u.startsWith(URL_TYPE_SYSTEM_SMS_TO);
        }

        public static Map<String, String> getParams(String url) {
            Map<String, String> map = new HashMap<>();
            if ((url.contains("bosszp://") || url.contains("bosszp2://"))) {
                String[] str = url.split("\\?");
                if (str.length > 1) {
                    String[] stri = str[1].split("&");
                    for (String s : stri) {
                        String[] strin = s.split("=");
                        if (strin.length == 2) {
                            map.put(strin[0], strin[1]);
                        }
                    }
                }
            }
            return map;
        }

    }
}
