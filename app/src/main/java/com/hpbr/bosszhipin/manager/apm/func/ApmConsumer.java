package com.hpbr.bosszhipin.manager.apm.func;

import android.content.Context;
import android.util.Pair;

import androidx.annotation.NonNull;

import com.hpbr.apm.common.Consumer;
import com.hpbr.apm.config.ActionPExtraInfoMap;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.ConsumerKey;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.apm.upgrade.exp.ExceptionUtil;
import com.hpbr.apm.upgrade.patch.bean.PatchFileParams;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.patch.PatchLogger;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.tencent.bugly.beta.tinker.TinkerManager;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.tinker.loader.shareutil.SharePatchFileUtil;
import com.twl.utils.file.FileUtils;

import org.json.JSONObject;

import java.io.File;

import okhttp3.Request;

public class ApmConsumer implements ApmFuncRegister {

    private static final String TAG = "ApmConsumer";

    @Override
    public void put(@NonNull Context context, @NonNull Config.Builder builder) {
        builder
                .putConsumer(ConsumerKey.KEY_ON_INSTALL_PATCH, (Consumer<File>) patchFile -> {
                    if (patchFile == null) return;
                    PatchLogger.getInstance().i(PatchLogger.TAG, "apm download a patch");
                    ApmAnalyzer.create()
                            .action(AnalyticsAction.ACTION_ZP_TINKER_REPORT, AnalyticsAction.ACTION_ZP_TINKER_REPORT_RECEIVE)
                            .report();
                    try {
                        TinkerManager.getInstance().applyPatch(patchFile.getAbsolutePath(), true);
                        CrashProtectInitHelper.cleanForceHotFixFlag();
                    } catch (Exception e) {
                        PatchLogger.getInstance().printErrStackTrace(PatchLogger.TAG, e, "applyPatch ex");
                        ExceptionUtil.reportPatchException(String.valueOf(e));
                    }
                })
                .putConsumer(ConsumerKey.KEY_ON_REMOVE_PATCH, o -> {
                    PatchLogger.getInstance().i(PatchLogger.TAG, "apm request clean patch");
                    ApmAnalyzer.create()
                            .action(AnalyticsAction.ACTION_ZP_TINKER_REPORT, AnalyticsAction.ACTION_ZP_TINKER_REPORT_CLEAN)
                            .report();
                    TinkerManager.getInstance().cleanPatch(false);
                    CrashProtectInitHelper.cleanForceHotFixFlag();
                })
                .putConsumer(ConsumerKey.KEY_ON_APM_CONNECT_ERROR, (Consumer<String>) s -> {
                    // 上报到 bugly
                    CrashReport.putUserData(context, "key_apm_connect_error", "APM CONNECT ERROR: " + s);
                    CrashReport.postCatchedException(new Exception(), Thread.currentThread(), true);

                    // 上报 bgaction
                    AnalyticsFactory.create()
                            .action("action_on_apm_connect_error")
                            .param("p", s)
                            .build();
                })
                .putConsumer(ConsumerKey.KEY_ON_NO_PATCH, o -> {
                    CrashProtectInitHelper.cleanForceHotFixFlag();
                    PatchLogger.getInstance().i(PatchLogger.TAG, "apm no patch");
                })
                .putConsumer(ConsumerKey.KEY_ON_APM_BIZ_ERROR, (Consumer<Pair<Request, JSONObject>>) cprj -> {
                    try {
                        if (cprj != null) {
                            Request request = cprj.first;
                            JSONObject responseJo = cprj.second;

                            if (request != null && responseJo != null) {
                                // 上报到 bugly
                                String data = request.url() + " : " + responseJo;

                                int _1MB = 1024 * 1024; // 1MB
                                if (data.length() > _1MB) {
                                    data = data.substring(0, _1MB);
                                }

                                L.d(TAG, data);

                                CrashReport.putUserData(context, ConsumerKey.KEY_ON_APM_BIZ_ERROR, "APM BIZ ERROR >>> " + data);
                                CrashReport.postCatchedException(new Exception(), Thread.currentThread(), true);

                                // 上报 bgaction
                                AnalyticsFactory.create()
                                        .action(ConsumerKey.KEY_ON_APM_BIZ_ERROR)
                                        .param("p", data)
                                        .build();
                            }
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }
                })
                .putConsumer(ConsumerKey.KEY_ON_SAME_PATCH_FILE_RETURN, (Consumer<PatchFileParams>) patchFileParams -> {
                    PatchLogger.getInstance().i(PatchLogger.TAG, "apm same patch return");
                    if (patchFileParams == null
                            || LText.empty(patchFileParams.getChecksum())
                            || !FileUtils.isFileExists(patchFileParams.getLocalFilePath())) {
                        return;
                    }

                    // com.hpbr.apm.upgrade.patch.bean.PatchFileParams.getLocalFilePath 获取补丁文件本地地址
                    // com.hpbr.apm.upgrade.patch.bean.PatchFileParams.getChecksum 获取补丁文件md5值
                    try {
                        File tempFile = new File(SharePatchFileUtil.getPatchTempDirectory(context), "temp.apk");//"temp.apk" 为UpgradePatchRetry.TEMP_PATCH_NAME
                        String tempPatchMd5 = SharePatchFileUtil.getMD5(tempFile);
                        boolean needRetry = !FileUtils.isFileExists(tempFile) || !patchFileParams.getChecksum().equalsIgnoreCase(tempPatchMd5);
                        if (needRetry) {
                            TinkerManager.getInstance().applyPatch(patchFileParams.getLocalFilePath(), true);
                        }

                        ApmAnalyzer.create()
                                .action(AnalyticsAction.ACTION_ZP_TINKER_REPORT, needRetry ? AnalyticsAction.ACTION_ZP_TINKER_REPORT_RETRY : AnalyticsAction.ACTION_ZP_TINKER_REPORT_TEMP)
                                .param("p2", TinkerManager.getTinkerId())
                                .param("p3", TinkerManager.getNewTinkerId())
                                .param("p4", patchFileParams.getChecksum())
                                .param("p5", String.valueOf(tempPatchMd5))
                                .param("p6", String.valueOf(!FileUtils.isFileExists(tempFile)))
                                .report();
                    } catch (Exception e) {
                        PatchLogger.getInstance().printErrStackTrace(PatchLogger.TAG, e, "dot temp diff ex");
                    }
                })
                .putConsumer(ConsumerKey.KEY_ACTIONP_EXTRA_INFO, (Consumer<ActionPExtraInfoMap>) actionPExtraInfoMap -> {
                    //noinspection StatementWithEmptyBody
                    if (actionPExtraInfoMap != null) {
//                        actionPExtraInfoMap.putOrReplace("dnsResolver", String.valueOf(UserGrayFeatureManager.getInstance().getDnsFeature()));
                    }
                });
    }

}
