package com.hpbr.bosszhipin.manager.apm.func;

import android.content.Context;

import androidx.annotation.NonNull;

import com.hpbr.apm.config.Config;

public interface ApmFuncRegister {

    void put(@NonNull Context context, @NonNull Config.Builder builder);

    class ApmFuncRegisterHelper {

        private final @NonNull
        Context context;
        private final @NonNull
        Config.Builder builder;

        public static ApmFuncRegisterHelper of(@NonNull Context context, @NonNull Config.Builder builder) {
            return new ApmFuncRegisterHelper(context, builder);
        }

        private ApmFuncRegisterHelper(@NonNull Context context, @NonNull Config.Builder builder) {
            this.context = context;
            this.builder = builder;
        }

        public void put(ApmFuncRegister... registers) {
            for (ApmFuncRegister register : registers) {
                register.put(context, builder);
            }
        }

    }

}
