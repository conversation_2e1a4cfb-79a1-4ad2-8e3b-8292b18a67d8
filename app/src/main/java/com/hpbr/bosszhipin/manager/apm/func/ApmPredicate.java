package com.hpbr.bosszhipin.manager.apm.func;

import android.content.Context;

import androidx.annotation.NonNull;

import com.hpbr.apm.common.Predicate;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.PredicateKey;
import com.hpbr.bosszhipin.patch.PatchHelper;

import okhttp3.Call;

public class ApmPredicate implements ApmFuncRegister {

    @Override
    public void put(@NonNull Context context, @NonNull Config.Builder builder) {
        builder
                .putPredicate(PredicateKey.KEY_SHOULD_REPORT_CALL_EXCEPTION, (Predicate<Call>) call -> {
                    boolean shouldReportCallException = true;
                    try {
                        if (call != null) {
                            shouldReportCallException = !call.request().url().host().contains("api-ipv6.zhipin.com") && !call.request().url().host().contains("img.bosszhipin.com");
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }

                    return shouldReportCallException;
                })
                .putPredicate(PredicateKey.KEY_TINKER_ID_IN_BLACKLIST, (Predicate<String>) PatchHelper::isPatchInBlackList);
    }

}
