package com.hpbr.bosszhipin.manager.apm.func;

import static com.hpbr.bosszhipin.report.ReportContextUtils.realUploadFile;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.apm.Apm;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.RunnableKey;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.apm.upgrade.sec.SecApkStore;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.helper.WebViewCrashFixer;
import com.hpbr.bosszhipin.config.NightModeConfig;
import com.hpbr.bosszhipin.data.db.dao.ContactDoctorFactory;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactSyncManager;
import com.hpbr.bosszhipin.data.manager.contact.IRefreshStrategy;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.helper.gray.BZLCrashProtectGrayHelper;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.report.McpTxApiRequestCallback;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.anti.ExtendExecutor;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.FileUploadResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.Map;

import message.handler.dao.MessageDaoFactory;

public class ApmRunnable implements ApmFuncRegister {
    private static final String UPLOAD_FILE = "uploadFile";
    private static final String FORCE_DOWNLOAD_X5 = "forceDownloadX5";
    private static final String DB_REPAIR = "dbRepair";
    private static final String MSG_REPAIR = "msgRepair";
    private static final String DARK_MODE = "darkMode";
    private static final String CONTACT_SYNC = "contactSync";
    private static final String RUN_PROTOCOL = "runProtocol";
    private static final String RUN_TIME_DELAY = "runTimeDelay";
    private static final String CLEAN_WEB_VIEW_CACHE = "clean_web_view_cache";
    private static final String CONTACT_FIX_ALL_REPAIR = "contact_fix_all_repair";
    private static final String CONTACT_FIX_CLEAR_REPAIR_SP = "clear_repair_contact_sp";
    private static final String BASIC_DATA_RESET = "basic_data_reset";

    private static final String TAG = "ApmRunnable";

    @Override
    public void put(@NonNull Context context, @NonNull Config.Builder builder) {
        builder
                .putRunnable(RunnableKey.KEY_SEC_APK_EXECUTOR, () -> SecApkStore.loadLocalVerifiedSecApkClassLoader(secInfo -> {
                    try {
                        new ExtendExecutor(context, secInfo.getClassLoader(), secInfo.getPackageInfo()).run();
                        return true;
                    } catch (Throwable e) {
                        CrashReport.postCatchedException(e);
                        return false; // 运行失败
                    }
                }))
                .putRunnable(RunnableKey.KEY_GET_USER_CONFIG_DONE, () -> {
                    try {
                        Map<String, Object> disposableConfig = Apm.getDisposableConfig();
                        handleDisposable(disposableConfig);

                        //安全气垫灰度
                        BZLCrashProtectGrayHelper.syncApmGrayConfig(Apm.getPrivateConfigJsonObject(), Apm.getPublicConfigJsonObject());

                        //获取crash防护参数
                        //--私有配置
                        if (null != Apm.getPrivateConfigJsonObject() && CrashProtectInitHelper.checkConfig(Apm.getPrivateConfigJsonObject())) {
                            CrashProtectInitHelper.updateConfigParent(Apm.getPrivateConfigJsonObject());
                        }
                        //--公共配置
                        if (null != Apm.getPublicConfigJsonObject() && CrashProtectInitHelper.checkConfig(Apm.getPublicConfigJsonObject())) {
                            CrashProtectInitHelper.updateConfigParent(Apm.getPublicConfigJsonObject());
                        }
                        //--一次行配置
                        if (null!=Apm.getDisposableConfig() && CrashProtectInitHelper.checkDisposableConfig(Apm.getDisposableConfig())) {
                            CrashProtectInitHelper.executeActionsDisposableParent(Apm.getDisposableConfig());
                        }

                    } catch (Throwable e) {
                        CrashReport.postCatchedException(e);
                    }
                });
    }

    public static void handleDisposable(@Nullable Map<String, Object> disposableConfig) {
        if (null == disposableConfig) return;
        Object path = disposableConfig.get(UPLOAD_FILE);
        if (path instanceof String) {
            uploadFile((String) path);
        }
        if (disposableConfig.containsKey(CLEAN_WEB_VIEW_CACHE)) {
            WebViewCrashFixer.deleteWebViewCache(Utils.getApp(), false);
        }
        if (disposableConfig.containsKey(DB_REPAIR)) { // 11.20
            MessageDaoFactory.submitDbRepairDialog();
        }

        if (disposableConfig.containsKey(MSG_REPAIR)) { // 12.07
            long min_mid = LText.getLong(String.valueOf(disposableConfig.get(MSG_REPAIR)), -1);
            MessageDaoFactory.midRepair(min_mid, -1);
        }

        if (disposableConfig.containsKey(CONTACT_SYNC)) { // 1203
            IRefreshStrategy iRefreshStrategy = ContactManager.getInstance().obtainRefreshStrategy();
            if (iRefreshStrategy.isRefreshing()) { //正在加载中
                return;
            }
            if (!iRefreshStrategy.isNeedRefresh()) {//不需要加载
                iRefreshStrategy.initRefreshTime();
                ContactSyncManager.getInstance().syncAll();
            }
        }

        if (disposableConfig.containsKey(DARK_MODE)) { // 1203
            int darkMode = LText.getInt(String.valueOf(disposableConfig.get(DARK_MODE)), 0);
            NightModeConfig.getInstance().setNightMode(darkMode);
        }

        if (disposableConfig.containsKey(RUN_PROTOCOL)) { // 1216
            long runTimeDelay = LText.getLong(String.valueOf(disposableConfig.get(RUN_TIME_DELAY)), 0L);
            Utils.runOnUiThreadDelayed(() -> {
                String runProtocol = (String) disposableConfig.get(RUN_PROTOCOL);
                new ZPManager(App.getAppContext(), runProtocol).handler();
            }, runTimeDelay);
        }

        if (disposableConfig.containsKey(CONTACT_FIX_ALL_REPAIR)) { // 1217
            SpManager.get().user().edit().putBoolean(CONTACT_FIX_ALL_REPAIR, true).apply();
        }

        if (disposableConfig.containsKey(CONTACT_FIX_CLEAR_REPAIR_SP)) { // 1302 清理历史修复缓存数据
            ContactDoctorFactory.getContactMessageHandle().clearData();
        }
        if (disposableConfig.containsKey(BASIC_DATA_RESET)) { // 1311
            VersionAndDatasCommon.tryResetBasicData();
        }
    }

    static void uploadFile(String path) {
        if (!TextUtils.isEmpty(path)) {
            //为了安全,只上传系统文件
            if (isSysDir(path)) {
                File file = new File(path);
                if (file.exists() && file.isFile()) {
                    realUploadFile(file, new McpTxApiRequestCallback() {
                        @Override
                        public void handleInChildThread(ApiData<FileUploadResponse> data) {
                            FileUploadResponse resp = data.resp;
                            String fileUrl = resp.url;

                            if (!TextUtils.isEmpty(fileUrl)) {

                                //region 构建log地址参数并上报
                                JSONObject object = new JSONObject();
                                try {
                                    JSONArray a = new JSONArray();
                                    a.put(fileUrl);
                                    object.put(ApmAnalyticsAction.BizKey.KEY_LOG_ADDRESS, a);
                                } catch (JSONException ignored) {

                                }

                                ApmAnalyzer.create()
                                        .action(ApmAnalyticsAction.ACTION_SYS_FILE)
                                        .param("p2", object.toString())
                                        .report();
                                //endregion
                            }
                        }

                        @Override
                        public void handleErrorInChildThread(ErrorReason reason) {
                            ApmAnalyzer.create()
                                    .action(ApmAnalyticsAction.ACTION_SECURITY_ERR, ApmAnalyticsAction.TYPE_SYS_FILE)
                                    .p2(reason.getErrReason())
                                    .p3(reason.getException().toString())
                                    .report();
                        }

                        @Override
                        public void onSuccess(ApiData<FileUploadResponse> data) {
                        }

                        @Override
                        public void onComplete() {

                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                        }
                    });
                } else {
                    TLog.info(TAG, "file is not exists path = %s", path);
                }
            }
        }
    }

    private static boolean isSysDir(String path){
        String[] sysPaths = {"/system/","/sys/", "/sbin/","/oem/","/odm/"};
        for (String sysPath : sysPaths) {
            if (path.startsWith(sysPath)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isContactFixAllRepair(){
        boolean aBoolean = SpManager.get().user().getBoolean(CONTACT_FIX_ALL_REPAIR, false);
        if (aBoolean) {
            SpManager.get().user().edit().remove(CONTACT_FIX_ALL_REPAIR).apply();
        }
        return aBoolean;
    }
}
