package com.hpbr.bosszhipin.manager.apm.func;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.bszp.kernel.DataKernel;
import com.bszp.kernel.chat.db.ChatDatabaseHelper;
import com.bszp.kernel.logic.db.AppDatabase;
import com.hpbr.apm.common.Supplier;
import com.hpbr.apm.common.net.UrlConfig;
import com.hpbr.apm.config.Config;
import com.hpbr.apm.config.SupplierKey;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.helper.CrashProtectInitHelper;
import com.hpbr.bosszhipin.common.dns.DNSCommon;
import com.hpbr.bosszhipin.config.DBConfig;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.manager.ApmManager;
import com.hpbr.bosszhipin.patch.PatchHelper;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.monch.lbase.util.L;
import com.twl.anti.CheatChecker;
import com.twl.utils.file.FileUtils;

import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.List;

import okhttp3.Call;
import okhttp3.EventListener;
import okhttp3.Protocol;

public class ApmSupplier implements ApmFuncRegister {

    private static final String TAG = "ApmSupplier";

    @Override
    public void put(@NonNull Context context, @NonNull Config.Builder builder) {
        builder
                .putSupplier(UrlConfig.KEY_HOST, () -> {
                    if (ApmManager.sApmTestMode){
                        // 动态选择APM网络环境（目前有：线上、预发、QA、RD）
                        String hostType = mapHostBasedConfig(UrlConfig.Host.APM_AND, UrlConfig.Host.PRE_ONLINE, UrlConfig.Host.QA);
                        L.d(TAG, "Host Type: " + hostType);
                        return hostType;
                    } else {
                        return UrlConfig.Host.APM_AND;
                    }
                })
                .putSupplier(SupplierKey.DID, () -> MobileUtil.getUniqId(App.getAppContext()))
//                .putSupplier(SupplierKey.UMID, () -> {
//                    String umid = MobileUtil.getUmid();
//                    return umid == null ? "" : umid;
//                }) // umeng ID
                .putSupplier(SupplierKey.KEY_SEC_APK_VERSION_CODE, () -> {
                    // 宿主初始安全补丁版本，用于 APM 首次检测获取升级安全补丁
                    return String.valueOf(CheatChecker.VERSION_CODE);
                })
                .putSupplier(SupplierKey.KEY_APM_SECRET_KEY, () -> {
                    final String YNEIVsMlw7bp7pfd = "YNEIVsMlw7bp7pfd";
                    final String sKey = mapHostBasedConfig(YNEIVsMlw7bp7pfd, YNEIVsMlw7bp7pfd, YNEIVsMlw7bp7pfd);
                    L.d(TAG, "sKey: " + sKey);
                    return sKey; // 接口解密用
                })
                .putSupplier(SupplierKey.KEY_TINKER_ENABLED, PatchHelper::isTinkerEnable)
//                .putSupplier(SupplierKey.KEY_DNS_RESOLVER, (Supplier<Integer>) () -> UserGrayFeatureManager.getInstance().getDnsFeature() + (DNSCommon.isIsUseSystem() ? 10 : 0))
                .putSupplier(SupplierKey.KEY_EVENT_LISTENER, () -> new EventListener() {
                    @Override
                    public void connectStart(@NonNull Call call, @NonNull InetSocketAddress inetSocketAddress, @NonNull Proxy proxy) {
                        super.connectStart(call, inetSocketAddress, proxy);
                    }

                    @Override
                    public void connectEnd(@NonNull Call call, @NonNull InetSocketAddress inetSocketAddress, @NonNull Proxy proxy, @Nullable Protocol protocol) {
                        super.connectEnd(call, inetSocketAddress, proxy, protocol);
                    }

                    @Override
                    public void connectFailed(@NonNull Call call, @NonNull InetSocketAddress inetSocketAddress, @NonNull Proxy proxy, @Nullable Protocol protocol, @NonNull IOException ioe) {
                        super.connectFailed(call, inetSocketAddress, proxy, protocol, ioe);
                        DNSCommon.onNetError(inetSocketAddress, ioe);
                    }
                })
                .putSupplier(SupplierKey.KEY_APP_DB_PATHS, (Supplier<List<File>>) () -> {
                    final List<File> dbList = new ArrayList<>();

                    //region ①
                    File databasePath = context.getDatabasePath(AppDatabase.DATABASE_NAME);
                    if (FileUtils.isFileExists(databasePath)) {
                        dbList.add(databasePath);

                        L.d(TAG, "databasePath: " + databasePath.getAbsolutePath());
                    }
                    //endregion

                    //region ②
                    File chatDBPath = context.getDatabasePath(DBConfig.DB_NAME);
                    if (FileUtils.isFileExists(chatDBPath)) {
                        dbList.add(chatDBPath);
                        L.d(TAG, "chatDBPath: " + chatDBPath.getAbsolutePath());
                    }
                    //endregion

                    //region ③
                    if (DataKernel.isUserChatService()) {
                        String path = ChatDatabaseHelper.getPath();
                        if (path != null && !"".equals(path.trim())) {
                            File e = new File(path);
                            if (FileUtils.isFileExists(e)) {
                                dbList.add(e);

                                L.d(TAG, "Path: " + path);
                            }
                        }
                    }
                    //endregion

                    return dbList;
                })
                .putSupplier(SupplierKey.KEY_HOT_FIX_FORCE, new Supplier<String>() {
                    @NonNull
                    @Override
                    public String get() {
                        return CrashProtectInitHelper.checkForceHotFixFlag() ? "1" : "";
                    }
                });
    }

    public static <T> T mapHostBasedConfig(T online, T preOnline, T qa) { // APM 网络配置映射 BOSS 网络环境
        T t;

        HostConfig.Addr currentConfig = HostConfig.CONFIG;
        if (currentConfig == HostConfig.Addr.ONLINE) { // 线上
            t = online;
        } else if (currentConfig == HostConfig.Addr.PRE) {
            t = preOnline;
        } else if (currentConfig == HostConfig.Addr.QA
                || currentConfig == HostConfig.Addr.QA2
                || currentConfig == HostConfig.Addr.QA3
                || currentConfig == HostConfig.Addr.QA4) { // QA
            t = qa;
        } else { // 兜底，线上
            t = online;
        }

        return t;
    }

}
