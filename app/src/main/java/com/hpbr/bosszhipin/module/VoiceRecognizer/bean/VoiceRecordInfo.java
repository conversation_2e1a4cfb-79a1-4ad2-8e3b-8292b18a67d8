package com.hpbr.bosszhipin.module.VoiceRecognizer.bean;

import com.hpbr.bosszhipin.module.VoiceRecognizer.callback.VoiceRecorderListener;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceSdkType;

import java.io.Serializable;

/**
 * @ClassName ：VoiceRecordInfo
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/25  11:54 AM
 */
public class VoiceRecordInfo implements Serializable {
    private static final long serialVersionUID = -4508838776572295194L;

    public VoiceSdkType voiceSdkType;
    public String mAmrPath;
    public VoiceRecorderListener mRecorderListener;
    public boolean mError;
    /*是否需要实时语音转文字（即 「边说边识别模式」）*/
    public boolean isEnableIntermediateResult;

    public VoiceRecordInfo(VoiceSdkType voiceSdkType, String path, VoiceRecorderListener recorderListener, boolean isEnableIntermediateResult) {
        this.voiceSdkType = voiceSdkType;
        this.mAmrPath = path;
        this.mRecorderListener = recorderListener;
        this.isEnableIntermediateResult = isEnableIntermediateResult;
    }
}
