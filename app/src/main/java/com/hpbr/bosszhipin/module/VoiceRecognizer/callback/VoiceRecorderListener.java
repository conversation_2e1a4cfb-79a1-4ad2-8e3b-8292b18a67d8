package com.hpbr.bosszhipin.module.VoiceRecognizer.callback;

/**
 * @ClassName ：VoiceRecorderListener
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/25  11:55 AM
 */
public interface VoiceRecorderListener {

    /**
     * 开始录音
     */
    default void onRecordStart() {}

    /**
     * 录音结束
     *
     * @param orderId
     * @param userTriggerAudioToText 用户是否手动触发了语音转文字
     */
    void onRecordFinish(int code, String orderId, boolean userTriggerAudioToText);

    /**
     * 用户手动取消录音
     *
     * @param orderId
     */
    default void onUserCancel(String orderId) {
    }

    /**
     * 识别过程中间结果的回调（边说边识别结果）
     *
     * @param orderId
     * @param result
     */
    default void onMiddleResult(String orderId, String result, boolean isSectionFinish) {
    }

    /**
     * 最终结果的回调
     *
     * @param userTriggerAudioToText
     * @param orderId
     * @param result
     */
    void onFinalResult(int code, boolean userTriggerAudioToText, String orderId, String result);

    /**
     * 音量变化
     *
     * @param db
     */
    void onVolumeChange(int db);


    /**
     * NLP识别到音频录制结束的回调（当用户开启了「自动识别音频结束功能」时会有此回调）
     *
     * @param audioOrderId
     */
    default void onNlpAudioEnd(String audioOrderId) {

    }

}
