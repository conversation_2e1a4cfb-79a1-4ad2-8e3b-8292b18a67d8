package com.hpbr.bosszhipin.module.VoiceRecognizer.constant;

/**
 * @ClassName ：VoiceSceneInfoFetch
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/17  5:46 PM
 */
public class VoiceSceneInfoFetch {

    /*C端F1语音搜索场景*/
    public final static String GEEK_SEARCH_SCENE_NAME = "search";
    public final static String GEEK_SEARCH_SCENE_ID = "1";

    /*BOSS-牛人 聊天语音转文字*/
    public final static String CHAT_SCENE_NAME = "chat-boss";
    public final static String CHAT_SCENE_ID = "2";

    /*「个人优势」-语音转文字*/
    public final static String MY_ADVANTAGE_SCENE_NAME = "person-adv";
    public final static String MY_ADVANTAGE_SCENE_ID = "5";
    /*「个人优势」-语音转文字*/
    public final static String PUBLISH_JOB_SCENE_NAME = "zhipin-jd";
    public final static String PUBLISH_JOB_SCENE_ID = "10";

    public static final String CHAT_F3_GREET_ID = "14";
    public static final String CHAT_F3_GREET_NAME = "chat-f3-greet";

    public static final String CHAT_F1_FILTER_ID = "15";
    public static final String CHAT_F1_FILTER_NAME = "chat-f1-filter";
    //1206.166
    public static final String CHAT_F3_ROBOT_ID = "19";
    public static final String CHAT_F3_ROBOT_NAME = "chat-f3-robot";

    //1206.167
    public static final String CHAT_F2_SEARCH_ID = "20";
    public static final String CHAT_F2_SEARCH_NAME = "chat-f2-search";

    /**
     * 1207.601 牛人 F1 AI求职助手
     */
    public static final String CHAT_F1_JOB_ID = "21";
    public static final String CHAT_F1_JOB_NAME = "chat-f1-job";


    public static final String CHAT_GEEK_SEARCH_ID = "24";
    public static final String CHAT_GEEK_SEARCH_NAME = "zhipin-ai-search";
    //*********
    public static final String CHAT_BLUE_AGENT_BOT = "29";
    public static final String CHAT_BLUE_AGENT_BOT_SEARCH_NAME = "job-helper";


    /**
     * 获取场景名称
     *
     * @param useScene
     * @return
     */
    public static String getSceneName(@VoiceUseScene int useScene) {
        if (useScene == VoiceUseScene.MY_ADVANTAGE) {
            return MY_ADVANTAGE_SCENE_NAME;
        } else if (useScene == VoiceUseScene.CHAT) {
            return CHAT_SCENE_NAME;
        } else if (useScene == VoiceUseScene.PUBLISH_JOB) {
            return PUBLISH_JOB_SCENE_NAME;
        } else if (useScene == VoiceUseScene.CHAT_F3_GREET) {
            return CHAT_F3_GREET_NAME;
        } else if (useScene == VoiceUseScene.CHAT_F1_FILTER) {
            return CHAT_F1_FILTER_NAME;
        } else if (useScene == VoiceUseScene.CHAT_GEEK_F1_JOB) {
            return CHAT_F1_JOB_NAME;
        }
        else if (useScene == VoiceUseScene.CHAT_F2_FILTER) {
            return CHAT_F2_SEARCH_NAME;
        }
        else if (useScene == VoiceUseScene.CHAT_CHAT_SECURITY) {
            return CHAT_F3_ROBOT_NAME;
        }
        else if (useScene == VoiceUseScene.CHAT_GEEK_SEARCH) {
            return CHAT_GEEK_SEARCH_NAME;
        }
        else if (useScene == VoiceUseScene.BLUE_AGENT_BOT) {
            return CHAT_BLUE_AGENT_BOT_SEARCH_NAME;
        }
        else {
            return GEEK_SEARCH_SCENE_NAME;
        }
    }

    /**
     * 获取场景id
     *
     * @param useScene
     * @return
     */
    public static String getSceneId(@VoiceUseScene int useScene) {
        if (useScene == VoiceUseScene.MY_ADVANTAGE) {
            return MY_ADVANTAGE_SCENE_ID;
        } else if (useScene == VoiceUseScene.CHAT) {
            return CHAT_SCENE_ID;
        } else if (useScene == VoiceUseScene.PUBLISH_JOB) {
            return PUBLISH_JOB_SCENE_ID;
        } else if (useScene == VoiceUseScene.CHAT_F3_GREET) {
            return CHAT_F3_GREET_ID;
        } else if (useScene == VoiceUseScene.CHAT_F1_FILTER) {
            return CHAT_F1_FILTER_ID;
        } else if (useScene == VoiceUseScene.CHAT_GEEK_F1_JOB) {
            return CHAT_F1_JOB_ID;
        } else if (useScene == VoiceUseScene.CHAT_F2_FILTER) {
            return CHAT_F2_SEARCH_ID;
        }
        else if (useScene == VoiceUseScene.CHAT_CHAT_SECURITY) {
            return CHAT_F3_ROBOT_ID;
        }
        else if (useScene == VoiceUseScene.CHAT_GEEK_SEARCH) {
            return CHAT_GEEK_SEARCH_ID;
        }
        else if (useScene == VoiceUseScene.BLUE_AGENT_BOT) {
            return CHAT_BLUE_AGENT_BOT;
        }
        else {
            return GEEK_SEARCH_SCENE_ID;
        }
    }
}
