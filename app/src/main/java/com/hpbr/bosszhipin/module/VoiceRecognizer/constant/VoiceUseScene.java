package com.hpbr.bosszhipin.module.VoiceRecognizer.constant;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @ClassName ：VoiceUseScene
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/28  10:42 AM
 */
@IntDef({VoiceUseScene.GEEK_SEARCH, VoiceUseScene.CHAT,
        VoiceUseScene.MY_ADVANTAGE, VoiceUseScene.PUBLISH_JOB,
        VoiceUseScene.CHAT_F3_GREET,
        VoiceUseScene.CHAT_F1_FILTER,
        VoiceUseScene.CHAT_GEEK_F1_JOB,
        VoiceUseScene.CHAT_CHAT_SECURITY,
        VoiceUseScene.CHAT_GEEK_SEARCH,
        VoiceUseScene.BLUE_AGENT_BOT,
        VoiceUseScene.CHAT_F2_FILTER,
        VoiceUseScene.CHAT_ZHIPIN_JOB_SUB})
@Retention(RetentionPolicy.SOURCE)
public @interface VoiceUseScene {
    /*牛人搜索*/
    int GEEK_SEARCH = 1;
    /*chat 聊天页*/
    int CHAT = 2;
    /*个人优势*/
    int MY_ADVANTAGE = 3;
    /*1115.105 语音发布职位*/
    int PUBLISH_JOB = 4;
    /*f3新招呼*/
    int CHAT_F3_GREET = 5;
    /*F1筛选*/
    int CHAT_F1_FILTER = 6;
    /*1206.167*/
    int CHAT_F2_FILTER = 7;
    /*1206.166*/
    int CHAT_CHAT_SECURITY= 8;
    /*牛人F1 求职助手*/
    int CHAT_GEEK_F1_JOB = 9;
    /*牛人搜索*/
    int CHAT_GEEK_SEARCH = 10;
    /*1303.1.1*/
    int BLUE_AGENT_BOT= 11;

    int CHAT_ZHIPIN_JOB_SUB = 32;


}
