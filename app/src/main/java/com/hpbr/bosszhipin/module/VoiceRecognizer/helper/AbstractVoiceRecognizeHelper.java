package com.hpbr.bosszhipin.module.VoiceRecognizer.helper;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.module.VoiceRecognizer.manager.VoiceRecognizeManager;

/**
 * @ClassName ：AbstractVoiceRecognizeHelper
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/24  9:42 AM
 */
public abstract class AbstractVoiceRecognizeHelper {

    /**
     * 开始录制
     *
     * @param builder
     */
    public abstract void start(@NonNull VoiceRecognizeManager.Builder builder);

    /**
     * 停止录制
     */
    public abstract void stop();

    /**
     * 停止录音
     *
     * @param sendFinishMsg 是否发送完成的消息给socket服务端
     */
    public abstract void stop(boolean sendFinishMsg);

    /**
     * 取消录制
     */
    public abstract void cancel();

    /**
     * 释放资源
     */
    public abstract void release();

    /**
     * 设置在收到录制完成的回调后，只做语音转文字，不发送语音
     */
    public abstract void audioToText();

    /**
     * 是否正在录制中
     *
     * @return
     */
    public abstract boolean isRecording();

}
