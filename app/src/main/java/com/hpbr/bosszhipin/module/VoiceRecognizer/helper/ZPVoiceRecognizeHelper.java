package com.hpbr.bosszhipin.module.VoiceRecognizer.helper;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.bzl.sdk.voice.VoiceManager;
import com.bzl.sdk.voice.bean.SceneInfoBean;
import com.bzl.sdk.voice.constant.VoiceHostType;
import com.bzl.sdk.voice.interfaces.IVoiceSearchCallback;
import com.bzl.sdk.voice.internal.consts.LibVoiceConstant;
import com.bzl.sdk.voice.internal.net.bean.StartParamBean;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module.VoiceRecognizer.bean.VoiceRecordInfo;
import com.hpbr.bosszhipin.module.VoiceRecognizer.callback.VoiceRecorderListener;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceSceneInfoFetch;
import com.hpbr.bosszhipin.module.VoiceRecognizer.manager.VoiceRecognizeManager;
import com.hpbr.bosszhipin.module.VoiceRecognizer.recorder.VoiceRecorderOutputStream;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.file.FileIOUtils;
import com.twl.utils.file.FileUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ：ZPVoiceRecognizeHelper
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/21  6:37 PM
 */
public class ZPVoiceRecognizeHelper extends AbstractVoiceRecognizeHelper implements IVoiceSearchCallback {

    private final String TAG = ZPVoiceRecognizeHelper.class.getSimpleName();

    private VoiceRecorderListener voiceRecorderListener;
    private String voiceFileSavePath;
    private boolean isNeedSaveAudio;
    private VoiceRecorderOutputStream recorderOutputStream;

    @Override
    public void start(@NonNull VoiceRecognizeManager.Builder builder) {
        this.voiceRecorderListener = builder.voiceRecorderListener;
        this.voiceFileSavePath = builder.voiceFileSavePath;

        //是否需要存储音频文件
        isNeedSaveAudio = (!TextUtils.isEmpty(builder.voiceFileSavePath)) && (FileUtils.isFileExists(builder.voiceFileSavePath));
        try {
            if (isNeedSaveAudio) {
                VoiceRecordInfo voiceRecordInfo = new VoiceRecordInfo(builder.voiceSdkType, builder.voiceFileSavePath, builder.voiceRecorderListener, builder.isEnableIntermediateResult);
                recorderOutputStream = new VoiceRecorderOutputStream(voiceRecordInfo);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, String> searchParams = (builder.inputParams == null) ? new HashMap<>() : builder.inputParams;
        searchParams.put(LibVoiceConstant.ENABLE_INTERMEDIATE_RESULT, String.valueOf(builder.isEnableIntermediateResult));
        searchParams.put(LibVoiceConstant.ENABLE_AUTO_DETECT_AUDIO_END, String.valueOf(builder.isAutoDetectAudioEnd));

        Map<String, String> propertiesParams = (builder.properitesParams == null) ? new HashMap<>() : builder.properitesParams;
        propertiesParams.put(LibVoiceConstant.PropertiesConstant.USER_TOLE, UserManager.isBossRole() ? "boss" : "geek");

        TLog.info(TAG, "===调用了start方法===是否使用V组音频采集：%s", builder.audioGatherIsUseVGroup);
        boolean isOnLine = HostConfig.CONFIG == HostConfig.Addr.ONLINE;
        SceneInfoBean sceneInfoBean = new SceneInfoBean(builder.zpSdkVersionName, VoiceSceneInfoFetch.getSceneId(builder.useScene), VoiceSceneInfoFetch.getSceneName(builder.useScene));
        VoiceManager.get().start(Utils.getApp(), searchParams, propertiesParams,
                new StartParamBean(isOnLine ? VoiceHostType.ON_LINE : VoiceHostType.DEV, sceneInfoBean).setVoiceFileSavePath(builder.voiceFileSavePath).setUseVGroupAudioGather(builder.audioGatherIsUseVGroup),
                this);
    }

    @Override
    public void stop() {
        VoiceManager.get().stop();
    }

    @Override
    public void stop(boolean sendFinishMsg) {
        VoiceManager.get().stop(sendFinishMsg);
    }

    @Override
    public void cancel() {
        VoiceManager.get().cancel();

        /*当用于取消时，删除本地缓存的音频文件*/
        if (!TextUtils.isEmpty(voiceFileSavePath)) {
            FileUtils.delete(voiceFileSavePath);
        }
    }

    @Override
    public void release() {
        VoiceManager.get().onDestroy();
    }

    @Override
    public void audioToText() {
        VoiceManager.get().audioToText();
    }

    @Override
    public void onRecordStart() {
        Utils.runOnUiThread(() -> {
            if (voiceRecorderListener != null) {
                voiceRecorderListener.onRecordStart();
            }
        });
    }

    @Override
    public void onRecordFinish(int code, String orderId, boolean userTriggerAudioToText) {
        Utils.runOnUiThread(() -> {
            if (isNeedSaveAudio && recorderOutputStream != null) {
                FileIOUtils.closeCloseable(recorderOutputStream);
            }

//            if (code == RecordCode.NO_BLUETOOTH_PERMISSION) {
//                Context topContext = Utils.getTopActivityOrApp();
//                if (!(topContext instanceof FragmentActivity)) return;
//                FragmentActivity topActivity = (FragmentActivity) topContext;
//                if (!ActivityUtils.isValid(topActivity)) return;
//                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) return;
//                PermissionHelper.getBluetoothHelper(topActivity).setPermissionCallback((yes, permission) -> {
//                    if (yes) {
//                    } else {
//                        ToastUtils.showText("请开启连接附近的设备权限");
//                    }
//                }).requestPermission();
//            }

            if (voiceRecorderListener != null) {
                voiceRecorderListener.onRecordFinish(code, orderId, userTriggerAudioToText);
            }
        });
    }

    @Override
    public void onMiddleText(String orderId, String middleText, boolean isSectionFinish) {
        Utils.runOnUiThread(() -> {
            if (voiceRecorderListener != null) {
                voiceRecorderListener.onMiddleResult(orderId, middleText, isSectionFinish);
            }
        });
    }

    @Override
    public void onFinalResult(int code, String orderId, String result, boolean userTriggerAudioToText) {
        Utils.runOnUiThread(() -> {
            if (voiceRecorderListener != null) {
                voiceRecorderListener.onFinalResult(code, userTriggerAudioToText, orderId, result);
            }
        });
    }

    @Override
    public void onUserCancel(String orderId) {
        IVoiceSearchCallback.super.onUserCancel(orderId);
        Utils.runOnUiThread(() -> {
            if (voiceRecorderListener != null) {
                voiceRecorderListener.onUserCancel(orderId);
            }
        });
    }

    @Override
    public void onVolumeChange(int db) {
        Utils.runOnUiThread(() -> {
            if (voiceRecorderListener != null) {
                int finalDb = Math.max(db - 30, 0);
                voiceRecorderListener.onVolumeChange(finalDb * 3);
            }
        });
    }

    @Override
    public void onAudioRecord(byte[] bytes, int readSize) {
        if (isNeedSaveAudio && recorderOutputStream != null) {
            try {
                recorderOutputStream.write(bytes, 0, readSize);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    @Override
    public void onNlpAudioEnd(String audioOrderId) {
        IVoiceSearchCallback.super.onNlpAudioEnd(audioOrderId);
    }

    @Override
    public void onDebugInfo(String id) {

    }

    @Override
    public void onStatisticsInfo(String info, boolean isVGroup) {
        apmReport(info, isVGroup);
    }

    @Override
    public boolean isRecording() {
        return VoiceManager.get().isRecording();
    }

    /**
     * Apm上报V组音频采集回调信息 || 语音录制阻塞上报
     */
    private void apmReport(String info, boolean isVGroup) {
        TLog.info(TAG, "apmReport: %s, isVGroup : %s", info, isVGroup);
        ApmAnalyzer.create().action(isVGroup ? ApmAnalyticsAction.ACTION_AUDIO_CAPTURE_BY_V_GROUP_INFO : ApmAnalyticsAction.ACTION_FIX_AUDIO_RECORD_BLOCK)
                .param("p2", info)
                .report();
    }
}
