package com.hpbr.bosszhipin.module.VoiceRecognizer.manager;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.module.VoiceRecognizer.callback.VoiceRecorderListener;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceSdkType;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceUseScene;
import com.hpbr.bosszhipin.module.VoiceRecognizer.helper.AbstractVoiceRecognizeHelper;
import com.hpbr.bosszhipin.module.VoiceRecognizer.helper.ZPVoiceRecognizeHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ：VoiceRecognizeManager
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/24  9:46 AM
 */
public class VoiceRecognizeManager {

    private final AbstractVoiceRecognizeHelper voiceRecognizeHelper;
    private Builder builder;


    private VoiceRecognizeManager(@NonNull Builder builder) {
        this.builder = builder;
        voiceRecognizeHelper = new ZPVoiceRecognizeHelper();
    }

    /**
     * 开始录音
     */
    public void start() {
        voiceRecognizeHelper.start(builder);
    }

    /**
     * 停止录音
     */
    public void stop() {
        voiceRecognizeHelper.stop();
    }

    /**
     * 停止录音
     *
     * @param sendFinishMsg 是否发送完成的消息给socket服务端
     */
    public void stop(boolean sendFinishMsg) {
        voiceRecognizeHelper.stop(sendFinishMsg);
    }

    /**
     * 取消录音
     */
    public void cancel() {
        voiceRecognizeHelper.cancel();
    }

    /**
     * 释放资源
     */
    public void release() {
        voiceRecognizeHelper.release();
    }

    /**
     * 是否在录制中
     *
     * @return
     */
    public boolean isRecording() {
        return voiceRecognizeHelper.isRecording();
    }

    /**
     * 停止录音 并且只做语音转文字，不发送语音
     */
    public void audioToText() {
        voiceRecognizeHelper.audioToText();
    }




    public static class Builder {

        /*sdk的类型（自研 or 讯飞）*/
        public VoiceSdkType voiceSdkType;
        /*使用场景*/
        public int useScene;
        public String voiceFileSavePath;
        public VoiceRecorderListener voiceRecorderListener;
        public HashMap<String, String> inputParams;
        public Map<String, String> properitesParams;
        public String zpSdkVersionName;
        public boolean isEnableIntermediateResult;
        public boolean isAutoDetectAudioEnd;
        public boolean audioGatherIsUseVGroup;

        public Builder(VoiceSdkType voiceSdkType, @VoiceUseScene int useScene) {
            this.voiceSdkType = voiceSdkType;
            this.useScene = useScene;
        }

        /**
         * 设置录音文件存储路径（如果不设置存储路径，则默认不保存声音文件）
         *
         * @return
         */
        public Builder setVoiceFileSavePath(String voiceFileSavePath) {
            this.voiceFileSavePath = voiceFileSavePath;
            return this;
        }

        /**
         * 设置回调监听器
         *
         * @param voiceRecorderListener
         * @return
         */
        public Builder setVoiceRecorderListener(VoiceRecorderListener voiceRecorderListener) {
            this.voiceRecorderListener = voiceRecorderListener;
            return this;
        }

        /**
         * 如需设置语音参数，可以调用此方法（自研sdk用）
         *
         * @param inputParams
         * @return
         */
        public Builder setVoiceInputParams(HashMap<String, String> inputParams) {
            this.inputParams = inputParams;
            return this;
        }

        /**
         * 如需设置语音properties参数，可以调用此方法（自研sdk用）
         *
         * @param properitesParams
         * @return
         */
        public Builder setVoicePropertiesParams(Map<String, String> properitesParams) {
            this.properitesParams = properitesParams;
            return this;
        }

        /**
         * 设置自研sdk版本号（如果不设置，SDK默认是v1）
         *
         * @param zpSdkVersionName
         * @return
         */
        public Builder setZpSdkVersionName(String zpSdkVersionName) {
            this.zpSdkVersionName = zpSdkVersionName;
            return this;
        }

        /**
         * 设置是否是「边说边识别」模式
         *
         * @param isEnableIntermediateResult
         * @return
         */
        public Builder setEnableIntermediateResult(boolean isEnableIntermediateResult) {
            this.isEnableIntermediateResult = isEnableIntermediateResult;
            return this;
        }

        /**
         * 设置是否开启「NLP自动识别音频结束」功能
         *
         * @param isAutoDetectAudioEnd
         * @return
         */
        public Builder setAutoDetectAudioEnd(boolean isAutoDetectAudioEnd) {
            this.isAutoDetectAudioEnd = isAutoDetectAudioEnd;
            return this;
        }

        /**
         * 是否使用V组提供音频采集功能
         *
         * @param audioGatherIsUseVGroup
         * @return
         */
        public Builder setUseVGroupAudioGather(boolean audioGatherIsUseVGroup) {
            this.audioGatherIsUseVGroup = audioGatherIsUseVGroup;
            return this;
        }


        public VoiceRecognizeManager build() {
            return new VoiceRecognizeManager(this);
        }
    }


}
