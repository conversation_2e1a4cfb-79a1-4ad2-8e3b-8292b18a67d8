package com.hpbr.bosszhipin.module.VoiceRecognizer.recorder;

import static com.hpbr.bosszhipin.module.contacts.sr.Constant.PLAY_FREQUENCY;
import static com.hpbr.bosszhipin.module.contacts.sr.Constant.RECORD_FREQUENCY;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.module.VoiceRecognizer.bean.VoiceRecordInfo;
import com.hpbr.bosszhipin.module.contacts.sr.Constant;
import com.hpbr.bosszhipin.module.contacts.sr.coder.AmrEncoder;
import com.hpbr.bosszhipin.module.contacts.sr.coder.Resampler;
import com.twl.utils.file.FileIOUtils;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;

import okio.BufferedSink;
import okio.Okio;

/**
 * @ClassName ：VoiceRecorderOutputStream
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/10/21  6:41 PM
 */
public class VoiceRecorderOutputStream extends OutputStream {

    private BufferedSink mBufferedSink;
    private AmrEncoder mArmEncoder;
    private VoiceRecordInfo mRecordInfo;
    private final Resampler mResampler = new Resampler(RECORD_FREQUENCY, PLAY_FREQUENCY);
    /**
     * 最低音量
     */
    private static final int MINUS_VOIVE = 0;

    /**
     * 最高音量
     */
    private static final int MAX_VOICE = 90;


    public VoiceRecorderOutputStream(@NonNull VoiceRecordInfo recordInfo) throws Exception {
        mRecordInfo = recordInfo;
        mArmEncoder = new AmrEncoder();
        mBufferedSink = Okio.buffer(Okio.sink(new File(recordInfo.mAmrPath)));

        mBufferedSink.write(new byte[]{0x23, 0x21, 0x41, 0x4D, 0x52, 0x0A});
        mArmEncoder.start();
    }

    @Override
    public void write(int b) throws IOException {

    }

    @Override
    public void write(@NonNull byte[] b, int off, int len) throws IOException {
        try {
            byte[] samples = mResampler.reSample(b);
            mArmEncoder.encode(samples, 0, samples.length, mBufferedSink);
        } catch (IOException e) {
            mRecordInfo.mError = true;
        }
    }

    @Override
    public void close() throws IOException {
        super.close();
        try {
            if (mBufferedSink != null) {
                mBufferedSink.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            FileIOUtils.closeCloseable(mArmEncoder);
        }
    }

    /**
     * 业务层需要实时返回声音振幅
     * 取值范围
     *
     * @param b
     * @return
     */
    private int getVoiceValue(byte[] b) {
        int amplitude = Math.abs((short) ((b[0] & 0xff) | (b[1] << 8)));
        int value;
        if (amplitude > Constant.MAX_AMPLITUDE_TO_DRAW) {
            value = MAX_VOICE;
        } else if (amplitude < Constant.MIN_AMPLITUDE_TO_DRAW) {
            value = MINUS_VOIVE;
        } else {
            value = amplitude * 9 / 800;
        }
        return value;
    }
}
