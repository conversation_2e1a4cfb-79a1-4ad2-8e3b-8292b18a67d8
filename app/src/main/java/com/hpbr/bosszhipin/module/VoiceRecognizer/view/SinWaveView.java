package com.hpbr.bosszhipin.module.VoiceRecognizer.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;

/**
 * 1115.105 录制语音 声波动画
 * 支持渐变色
 */public class SinWaveView extends View {

    private Paint mPaint;
    private Path mPath;

    private int mLineColor = Color.RED;
    private float mLineWidth = 5f;

    private int mWaveCount = 3; // 曲线数量
    private int mWaveLength = 200; // 波长
    private int mAmplitude = 50; // 振幅
    private int mOffset; // 偏移量

    public SinWaveView(Context context) {
        super(context);
        init();
    }

    public SinWaveView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public SinWaveView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(mLineWidth);
        mPaint.setColor(mLineColor);

        mPath = new Path();

        // 使用属性动画控制偏移量的变化
        ObjectAnimator animator = ObjectAnimator.ofInt(this, "offset", 0, mWaveLength);
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.setDuration(2000);
        animator.setInterpolator(new LinearInterpolator());
        animator.start();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        int width = getWidth();
        int height = getHeight();
        int centerX = width / 2;
        int centerY = height / 2;

        mPath.reset();

        for (int i = 0; i < mWaveCount; i++) {
            int startX = i * mWaveLength - mOffset;
            int endX = startX + mWaveLength;

            mPath.moveTo(startX, centerY);

            // 使用正弦函数创建波形
            for (int x = startX; x < endX; x++) {
                float y = (float) (Math.sin(Math.toRadians((x - startX) * 360f / mWaveLength)) * mAmplitude + centerY);
                mPath.lineTo(x, y);
            }
        }

        // 绘制曲线
        canvas.drawPath(mPath, mPaint);
    }

    public void setOffset(int offset) {
        mOffset = offset;
        invalidate();
    }

    public void setLineColor(int color) {
        mLineColor = color;
        mPaint.setColor(mLineColor);
        invalidate();
    }

    public void setLineWidth(float width) {
        mLineWidth = width;
        mPaint.setStrokeWidth(mLineWidth);
        invalidate();
    }

    public void setWaveCount(int count) {
        mWaveCount = count;
        invalidate();
    }

    public void setWaveLength(int length) {
        mWaveLength = length;
        invalidate();
    }

    public void setAmplitude(int amplitude) {
        mAmplitude = amplitude;
        invalidate();
    }
}
