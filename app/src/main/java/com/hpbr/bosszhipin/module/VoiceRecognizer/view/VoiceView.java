package com.hpbr.bosszhipin.module.VoiceRecognizer.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.bzl.sdk.voice.RecognizeCode;
import com.bzl.sdk.voice.RecordCode;
import com.bzl.sdk.voice.constant.AdsVersion;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.module.VoiceRecognizer.callback.IBVPPRecorderListener;
import com.hpbr.bosszhipin.module.VoiceRecognizer.callback.VoiceRecorderListener;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceSdkType;
import com.hpbr.bosszhipin.module.VoiceRecognizer.constant.VoiceUseScene;
import com.hpbr.bosszhipin.module.VoiceRecognizer.manager.VoiceRecognizeManager;
import com.hpbr.bosszhipin.module.videointerview.VideoUtil;
import com.hpbr.bosszhipin.utils.SystemUtils;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;

import java.util.Timer;
import java.util.TimerTask;

import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;

/**
 * 1115.105 录音控件
 * 自动转换成文本
 *
 * <AUTHOR>
 * @since 2023/7/21
 */
public class VoiceView extends ConstraintLayout {
    private static final String TAG = "VoiceView";

    private static final int PERIOD = 1000;

    private static final int MAX_DURATION = 60;

    private static final int LIMIT_NOTIFY_DURATION = 10;
    private static final int RECORD_STATUS_INIT = 0; // 录音初始状态

    private static final int RECORD_STATUS_RECORDING = 1; // 录音中

    private static final int RECORD_STATUS_CANCELING = 2; // 录制中, 且手指在取消录制上
    private static final int RECORD_STATUS_GO_TO_CANCEL = 3; // 录制中, 且手指在录制和取消录制中间
    private static final int RECORD_STATUS_CANCELING_MOVE_OTHER = 4; // 录制中, 且手指在除了录制和取消录制按钮的其他区域

    private static final int RECORD_STATUS_FINISH = 5; // 录制结束
    private static final int RECORD_STATUS_CANCELLED = 6; // 录制取消

    private GradientWaveView mWaveView;
    private ImageView mRecordView;
    private ZPUIFrameLayout mCancelView;
    private MTextView mCancelHintView;
    private MTextView mGenerateView;

    private VoiceRecognizeManager mVoiceRecognizeManager;

    // 当前录制状态
    private int mRecordStatus = RECORD_STATUS_INIT;

    private IBVPPRecorderListener mCallback;

    private Timer mTimer;

    private int mDuration; // 录制时间
    private int mCancelBtnStartColor = -1; // 取消按钮变化初始颜色
    private int mCancelBtnEndColor = -1; // 取消按钮变化结束颜色

    private boolean hasRecordSuccess = false; // 是否录制成功
    /**
     * 1115.105 录制过程中 动画参数范围
     */
    // 手指离开录制按钮并向上滑动过程区间
    float limitGoingToCancel = 150F;
    // 手指滑动到取消按钮上过程区间
    float limitCancel = 72F;
    // 手指划出录制按钮边界
    float limitRecord = 0F;

    public VoiceView(@NonNull Context context) {
        super(context);
        init(context);
    }

    public VoiceView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public VoiceView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public VoiceView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context);
    }

    @SuppressLint("ClickableViewAccessibility")
    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.view_voice_publish_job, this, true);
        mWaveView = findViewById(R.id.wave_view);
        mCancelView = findViewById(R.id.bg_cancel);
        mCancelHintView = findViewById(R.id.tv_cancel_hint);
        mRecordView = findViewById(R.id.iv_record);
        mGenerateView = findViewById(R.id.tv_generate);
        mGenerateView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                // todo: zjj 帮我生成
                if (null != mCallback) {
                    mCallback.onHelpMeGenerate();
                }
            }
        });
        mRecordView.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                // 相对录音布局的坐标
                int x = (int) event.getX();
                int y = (int) event.getY();
                // 手指是否移动到录制按钮外
                boolean isMovedOut = x < 0 || y < 0 || x > v.getRight() - v.getLeft() || y > v.getBottom() - v.getTop();

                // 相对整体自定义布局的坐标
                int rawX = v.getLeft() + x;
                int rawY = v.getTop() + y;
                boolean isShowingCancel = y < limitRecord && y >= -limitGoingToCancel;

                // 手指是否移动到取消录制按钮内
                boolean isInCancelView = rawX > mCancelView.getLeft() && rawX < mCancelView.getRight() && rawY > mCancelView.getTop() && rawY < mCancelView.getBottom();
                int action = event.getAction();
                switch (action) {
                    case MotionEvent.ACTION_DOWN:
                        handleDownAction();
                        break;
                    case MotionEvent.ACTION_UP:
                        handleUpAction(isInCancelView);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        handleMoveAction(isMovedOut, isShowingCancel, isInCancelView, y);
                        break;
                }

                return true;
            }
        });

    }

    private void handleMoveAction(boolean isMovedOut, boolean isShowingCancel, boolean isCanceling, int y) {
        if (mRecordStatus == RECORD_STATUS_FINISH || mRecordStatus < RECORD_STATUS_RECORDING) {
            return;
        }
        if (isMovedOut) {
            if (isShowingCancel) {
                mRecordStatus = RECORD_STATUS_GO_TO_CANCEL;
            } else if (isCanceling) {
                mRecordStatus = RECORD_STATUS_CANCELING;
            } else {
                mRecordStatus = RECORD_STATUS_CANCELING_MOVE_OTHER;
            }
        } else {
            if (mRecordStatus == RECORD_STATUS_RECORDING) {
                return;
            }
            mRecordStatus = RECORD_STATUS_RECORDING;
        }
        handleRecordStatusChange(y);
    }

    public void handleUpAction() {
        handleUpAction(false);
    }

    private void handleUpAction(boolean isInCancelView) {
        if (mRecordStatus < RECORD_STATUS_RECORDING) {
            return;
        }
        if (isInCancelView) {
            mRecordStatus = RECORD_STATUS_CANCELLED;
            cancelRecord();
        } else {
            mRecordStatus = RECORD_STATUS_FINISH;
            stopRecord();
        }
        handleRecordStatusChange(0);
        setStartRecordFlag(false);
    }

    private void handleDownAction() {
        mRecordStatus = RECORD_STATUS_INIT;
        preStartRecord();
    }

    /*录制开始标记*/
    protected boolean startRecordFlag = false;

    public void setStartRecordFlag(boolean startRecordFlag) {
        this.startRecordFlag = startRecordFlag;
    }

    public boolean isStartRecordFlag() {
        return startRecordFlag;
    }

    private void preStartRecord() {
        if (null == mVoiceRecognizeManager) {
            mVoiceRecognizeManager = new VoiceRecognizeManager.Builder(VoiceSdkType.ZP_SDK, VoiceUseScene.PUBLISH_JOB)
                    .setEnableIntermediateResult(true)//是否实时翻译
                    .setUseVGroupAudioGather(CommonConfigManager.getInstance().audioCaptureByVGroup())/*是否使用V组提供的音频采集*/
                    .setZpSdkVersionName(AdsVersion.SDK_VERSION_NAME_V1)
                    .setVoiceRecorderListener(mVoiceRecorderListener)
                    .build();
        }

        if (getContext() instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) getContext();
            boolean hasPermission = PermissionHelper.getMicroHelper(activity).checkPermission();
            if (hasPermission) {
                if (mCancelBtnStartColor == -1) {
                    mCancelBtnStartColor = ContextCompat.getColor(getContext(), R.color.color_FFF5F5F5_FF262629);
                }
                if (mCancelBtnEndColor == -1) {
                    mCancelBtnEndColor = ContextCompat.getColor(getContext(), R.color.color_FFE0E0E0_FF363638);
                }
                mCancelView.setBackgroundColor(mCancelBtnStartColor);
                startRecord();
            } else {
                PermissionHelper.getMicroHelper((FragmentActivity) getContext()).setPermissionCallback((yes, permission) -> {
                    if (!yes) {
                        ToastUtils.showText("您已拒绝语音相关权限(麦克风/存储)，此功能将无法使用");
                    }
                }).requestPermission();
            }
        }
    }

    private void startRecord() {
        // 判断网络是否可用
        if (!VideoUtil.checkNetIsAvailable()) {
            ToastUtils.showText("网络异常，请检查网络状况");
            return;
        }

        if (mCallback != null && mCallback.getMySendTextMsgCount() >= 10) {
            ToastUtils.showText("最多输入10条，请删除一些再继续讲话");
            return;
        }

        mRecordStatus = RECORD_STATUS_RECORDING;
        setStartRecordFlag(true);
        mVoiceRecognizeManager.start();
        handleRecordStatusChange(0);
        startTimer();

        SystemUtils.vibrator();//震动效果
    }


    private void stopRecord() {
        if (mVoiceRecognizeManager != null) {
            mVoiceRecognizeManager.stop();
        }
        cancelTimer();
    }

    private void cancelRecord() {
        if (mVoiceRecognizeManager != null) {
            mVoiceRecognizeManager.cancel();
        }
        cancelTimer();
    }

    public void setCallback(IBVPPRecorderListener callback) {
        mCallback = callback;
    }

    private int getMiddleColor(int startColor, int endColor, float ratio) {
        // 计算两个颜色的 RGB 值之间的插值
        int r = (int) (Color.red(startColor) * (1 - ratio) + Color.red(endColor) * ratio);
        int g = (int) (Color.green(startColor) * (1 - ratio) + Color.green(endColor) * ratio);
        int b = (int) (Color.blue(startColor) * (1 - ratio) + Color.blue(endColor) * ratio);
        // 使用插值后的 RGB 值构建中间颜色
        return Color.rgb(r, g, b);
    }

    private void handleRecordStatusChange(int y) {
        switch (mRecordStatus) {
            case RECORD_STATUS_INIT:
                mCancelHintView.setText("按住开始讲话");
                mCancelHintView.setVisibility(View.VISIBLE);
                mGenerateView.setVisibility(View.GONE);
                break;
            case RECORD_STATUS_RECORDING:
                mRecordView.setImageResource(R.mipmap.ic_audio_record_pressed);
                mWaveView.startAnim();
                mWaveView.setAlpha(0.07F);
                mCancelView.setVisibility(View.GONE);
                mGenerateView.setVisibility(View.GONE);
                mCancelHintView.setText("松开发送，上滑取消");
                mCancelHintView.setVisibility(View.VISIBLE);
                break;
            case RECORD_STATUS_CANCELING:
                // 手指滑动到取消录制按钮上, 曲线按钮逐渐变大
                mCancelHintView.setText("松手取消");
                mWaveView.setAlphaRadio(0.2F);
                if (y < -limitGoingToCancel && y >= -(limitCancel + limitGoingToCancel)) {
                    float cancelColorRatio = 0.4F * Math.abs((y + limitGoingToCancel) / limitCancel);
                    int cancelColor = getMiddleColor(mCancelBtnStartColor, mCancelBtnEndColor, cancelColorRatio);
                    float scaleSize = 1 + cancelColorRatio;
                    mCancelView.setScaleX(scaleSize);
                    mCancelView.setScaleY(scaleSize);
                    mCancelView.setBackgroundColor(cancelColor);
                }
                break;
            case RECORD_STATUS_GO_TO_CANCEL:
                // 当手指离开录制按钮并向上滑动, y坐标位于0 ～ -150时 取消按钮逐渐显示， 声波透明度逐渐变小
                mCancelHintView.setText("松开发送，上滑取消");
                mCancelView.setScaleX(1);
                mCancelView.setScaleY(1);
                mCancelView.setVisibility(View.VISIBLE);
                float waveAlpha = Math.abs(0.8F * y / limitGoingToCancel);
                float cancelAlpha = Math.abs(y / limitGoingToCancel);
                mWaveView.setAlphaRadio(1 - waveAlpha);
                mCancelView.setAlpha(cancelAlpha);
                break;
            case RECORD_STATUS_CANCELING_MOVE_OTHER:
                mCancelView.setVisibility(View.VISIBLE);
                mCancelHintView.setText("松开发送，上滑取消");
                break;
            case RECORD_STATUS_FINISH:
            case RECORD_STATUS_CANCELLED:
                mWaveView.setAlpha(1);
                mCancelView.setScaleX(1);
                mCancelView.setScaleY(1);
                mCancelView.setBackgroundColor(mCancelBtnStartColor);
                mWaveView.stopAnim();
                mCancelView.setVisibility(View.GONE);
                mRecordView.setImageResource(R.mipmap.ic_audio_record);
                break;
        }
    }

    private void refreshGenerateView() {
        if (hasRecordSuccess && isTxtMsgAvailable()) {
            mGenerateView.setVisibility(View.VISIBLE);
            mCancelHintView.setVisibility(View.GONE);
        } else {
            mGenerateView.setVisibility(View.GONE);
            mCancelHintView.setVisibility(View.VISIBLE);
            mCancelHintView.setText("按住开始讲话");
        }
    }

    /**
     * 消息列表清空消息后，重置帮我生成按钮
     */
    public void handleEmptyMsgListStatus() {
        refreshGenerateView();
    }

    public boolean isTxtMsgAvailable() {
        return null != mCallback && mCallback.getMySendTextMsgCount() > 0;
    }

    private final VoiceRecorderListener mVoiceRecorderListener = new VoiceRecorderListener() {
        @Override
        public void onRecordStart() {
            TLog.debug(TAG, "=====onRecordStart======");
            if (mCallback != null) {
                mCallback.onRecordStart(isStartRecordFlag());
            }
        }

        @Override
        public void onRecordFinish(int code, String orderId, boolean userTriggerAudioToText) {

            if (code == RecordCode.RECORDER_SUCCESS) {
                TLog.debug(TAG, "=====onRecordFinish====%s======%s=======", orderId, userTriggerAudioToText);
                hasRecordSuccess = true;
            } else {
                TLog.debug(TAG, "=====onRecorderError====errorCode：%s=====errorMsg：%s======orderId：%s", code, RecordCode.getErrorMsgByCode(code), orderId);
                ToastUtils.showText(RecordCode.getErrorMsgByCode(code));
            }
            if (mCallback != null) {
                mCallback.onRecordFinish(code, orderId, userTriggerAudioToText);
            }
        }

        @Override
        public void onMiddleResult(String orderId, String result, boolean isSectionFinish) {
            TLog.debug(TAG, "=====onMiddleResult====%s======%s=======", orderId, result);
            if (mCallback != null) {
                mCallback.onMiddleResult(orderId, result, isSectionFinish);
            }
        }

        @Override
        public void onFinalResult(int code, boolean userTriggerAudioToText, String orderId, String result) {
            if (code == RecognizeCode.SUCCESS) {
                TLog.debug(TAG, "=====onFinalResult====%s======%s=======", userTriggerAudioToText, result);
            } else {
                TLog.debug(TAG, "=====onRecognizerError====errorCode：%s=====errorMsg：%s======orderId：%s", code, RecordCode.getErrorMsgByCode(code), orderId);
            }
            if (mCallback != null) {
                mCallback.onFinalResult(code, userTriggerAudioToText, orderId, result);
            }
            refreshGenerateView();
        }

        @Override
        public void onVolumeChange(int db) {
            if (db > 70) {
                mWaveView.setVolume(100);
            } else if (db > 60) {
                mWaveView.setVolume(90);
            } else if (db > 50) {
                mWaveView.setVolume(75);
            } else if (db > 40) {
                mWaveView.setVolume(60);
            } else if (db > 30) {
                mWaveView.setVolume(45);
            } else {
                mWaveView.setVolume(20);
            }
            if (mCallback != null) {
                mCallback.onVolumeChange(db);
            }
        }

        @Override
        public void onUserCancel(String orderId) {
            if (mCallback != null) {
                mCallback.onUserCancel(orderId);
            }
            refreshGenerateView();
        }
    };

    private void startTimer() {
        mDuration = 0;
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                mDuration++;
                if (mDuration >= MAX_DURATION) {
                    TLog.debug(TAG, "run: 超时 终断");
                    // 取消定时器
                    cancelTimer();
                    // 超时停止录音 并且发送
                    App.get().getMainHandler().post(() -> {
                        handleUpAction(false);
                    });
                    return;
                }
                //超过50秒提示倒计时
                if (mDuration >= LIMIT_NOTIFY_DURATION) {
                    App.get().getMainHandler().post(() -> {
                        int second = MAX_DURATION - mDuration;
                        if (null != mCallback) {
                            mCallback.onRemainTimeTick(second * 1000L, LIMIT_NOTIFY_DURATION * 1000L);
                        }
                    });
                }
            }
        }, 0, PERIOD);
    }


    //取消定时器
    private void cancelTimer() {
        if (mTimer != null) {
            mTimer.cancel();
        }
        if (mDuration <= 1 && isStartRecordFlag()) {
            ToastUtils.showText("说话时间太短");
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelTimer();
        if (mVoiceRecognizeManager != null) {
            mVoiceRecognizeManager.release();
            mVoiceRecognizeManager = null;
        }
    }
}
