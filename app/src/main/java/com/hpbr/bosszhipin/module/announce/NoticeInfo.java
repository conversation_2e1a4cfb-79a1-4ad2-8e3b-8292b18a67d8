package com.hpbr.bosszhipin.module.announce;

import java.util.ArrayList;
import java.util.List;

import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_FINANCE_NOTICE;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_HUNTER_SERVICE;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_ITEM_ACTIVITY_USER_ID;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_ITEM_NOTIFY_USER_ID;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_MATE_RECOMMEND;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_POSITION_NOTICE;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_VIP_MONTH_REPORT;

/**
 * Created by guo<PERSON>
 * on 2017/11/3.
 */

public enum NoticeInfo {
    //所有通知和同事推荐id是虚拟的,其它id和服务器对应
    All_NOTICE(-100, "所有通知"),
    MATE_NOTICE(CHAT_MATE_RECOMMEND, "同事推荐"),
    HUNT_SERVICE(CHAT_HUNTER_SERVICE, "猎头服务"),
    POSITION_NOTICE(CHAT_POSITION_NOTICE, "职位通知"),
    ITEM_NOTICE(CHAT_ITEM_NOTIFY_USER_ID, "道具通知"),
    ACTIVE_NOTICE(CHAT_ITEM_ACTIVITY_USER_ID, "活动通知"),
    FINANCE_NOTICE(CHAT_FINANCE_NOTICE, "财务通知"),
    VIP_REPORT_NOTICE(CHAT_VIP_MONTH_REPORT, "VIP月报"),
    SYSTEM_NOTICE(CHAT_SYSTEM_MESSAGE_USER_ID, "系统通知");

    public long id;
    public String name;

    NoticeInfo(long id, String name) {
        this.id = id;
        this.name = name;
    }

    public static String getName(long id) {
        for (NoticeInfo noticeInfo : values()) {
            if (noticeInfo.id == id) {
                return noticeInfo.name;
            }
        }
        return "";
    }

    /**
     * 获得Boss通知列表
     */
    public static List<NoticeInfo> getBossNoticeList() {
        List<NoticeInfo> bossNotice = new ArrayList<>();
        bossNotice.add(POSITION_NOTICE);
        bossNotice.add(ITEM_NOTICE);
        bossNotice.add(MATE_NOTICE);
        bossNotice.add(ACTIVE_NOTICE);
        bossNotice.add(FINANCE_NOTICE);
        bossNotice.add(SYSTEM_NOTICE);
        bossNotice.add(VIP_REPORT_NOTICE);
        return bossNotice;
    }

    /**
     * 获得Boss通知列表
     */
    public static List<Long> getBossNoticeFriendId() {
        List<Long> bossNotice = new ArrayList<>();
        bossNotice.add(POSITION_NOTICE.id);
        bossNotice.add(ITEM_NOTICE.id);
        bossNotice.add(MATE_NOTICE.id);
        bossNotice.add(ACTIVE_NOTICE.id);
        bossNotice.add(FINANCE_NOTICE.id);
        bossNotice.add(SYSTEM_NOTICE.id);
        bossNotice.add(VIP_REPORT_NOTICE.id);
        return bossNotice;
    }

    /**
     * 获得牛人通知列表
     */
    public static List<NoticeInfo> getGeekNoticeList() {
        List<NoticeInfo> bossNotice = new ArrayList<>();
        bossNotice.add(ITEM_NOTICE);
        bossNotice.add(ACTIVE_NOTICE);
        bossNotice.add(FINANCE_NOTICE);
        bossNotice.add(SYSTEM_NOTICE);
        return bossNotice;
    }

    /**
     * 获得牛人通知列表
     */
    public static List<Long> getGeekNoticeFriendId() {
        List<Long> bossNotice = new ArrayList<>();
        bossNotice.add(ITEM_NOTICE.id);
        bossNotice.add(ACTIVE_NOTICE.id);
        bossNotice.add(FINANCE_NOTICE.id);
        bossNotice.add(SYSTEM_NOTICE.id);
        return bossNotice;
    }

    /**
     * 获得BOSS通知列表
     */
    public static List<Long> geBossNoticeFriendId() {
        List<Long> bossNotice = new ArrayList<>();
        bossNotice.add(POSITION_NOTICE.id);
        bossNotice.add(ITEM_NOTICE.id);
        bossNotice.add(MATE_NOTICE.id);
        bossNotice.add(ACTIVE_NOTICE.id);
        bossNotice.add(FINANCE_NOTICE.id);
        bossNotice.add(SYSTEM_NOTICE.id);
        bossNotice.add(VIP_REPORT_NOTICE.id);
        return bossNotice;
    }



    /**
     * 根据系统id判断是否跳转到通知页面
     *
     * @param systemId
     * @return
     */
    public static boolean isToNoticeActivity(long systemId) {
        for (NoticeInfo item : NoticeInfo.values()) {
            if (item.id == -100) continue;
            if (systemId == item.id) {
                return true;
            }
        }
        return false;
    }

}
