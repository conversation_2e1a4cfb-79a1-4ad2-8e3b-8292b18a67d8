package com.hpbr.bosszhipin.module.block.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.module.pay.entity.ItemPaySource;

/**
 * Author: zhouyou
 * Date: 2019/7/10
 * 道具使用或支付时的参数
 */
public class ItemParams extends BaseEntity {

    private static final long serialVersionUID = 2896638371381808462L;
    public String securityId;
    public long userId;
    public long jobId;
    public String encryptJobId;
    public long expectId;
    public long itemId;
    public String encryptItemId;
    public String lid;
    public long tsItem;

    public String secretUserId;

    public int itemSelection = ITEM_SELECTION_NONE;
    public static final int ITEM_SELECTION_NONE = 0;
    public static final int ITEM_SELECTION_CHAT_PRIVILEGE = 1;
    public static final int ITEM_SELECTION_GEEK_CALL = 2;

    public int source;
    public static final int FROM_SEARCH = 1;
    public static final int FROM_OTHER = 2;

    public String itemPaySourceEntrance = ItemPaySource.SUPER_SEARCH;

    /**
     * 916.243【商业】搜畅赠送免费开聊—场景化
     * 道具（搜畅畅聊卡）请求参数增加paramJson,字符串结构 securityId从详情的扩展信息牛人的信息里取对应字段
     */
    public String paramJson;
}
