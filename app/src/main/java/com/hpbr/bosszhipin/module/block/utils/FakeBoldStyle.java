package com.hpbr.bosszhipin.module.block.utils;

import android.annotation.SuppressLint;
import android.text.TextPaint;
import android.text.style.StyleSpan;

/**
 * Author: ZhouYou
 * Date: 2017/8/29.
 */
@SuppressLint("ParcelCreator")
public class FakeBoldStyle extends StyleSpan {
    public FakeBoldStyle(int style) {
        super(style);
    }

    @Override
    public void updateDrawState(TextPaint ds) {
        ds.setFakeBoldText(true);
        super.updateDrawState(ds);
    }

    @Override
    public void updateMeasureState(TextPaint paint) {
        paint.setFakeBoldText(true);
        super.updateMeasureState(paint);
    }
}
