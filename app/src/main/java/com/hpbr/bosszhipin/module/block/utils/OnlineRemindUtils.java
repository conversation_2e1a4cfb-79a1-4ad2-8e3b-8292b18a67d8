package com.hpbr.bosszhipin.module.block.utils;

import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.OnlineRemindSetupRequest;
import net.bosszhipin.api.OnlineRemindSetupResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Author: zhouyou
 * Date: 2020/7/15
 */
public class OnlineRemindUtils {

    private String securityId;
    public static final int SOURCE_CARD_WINDOW = 12;
    private BaseActivity activity;
    public static final int SOURCE_PLUS_ICON = 13;
    private String source;
    private String jobIdString;

    public OnlineRemindUtils(BaseActivity activity, String securityId, String source) {
        this.activity = activity;
        this.securityId = securityId;
        this.source = source;
    }

    public void setJobIdString(String jobIdString) {
        this.jobIdString = jobIdString;
    }

    public void onlineRemindSetup() {
        OnlineRemindSetupRequest request = new OnlineRemindSetupRequest(new ApiRequestCallback<OnlineRemindSetupResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog();
            }

            @Override
            public void onSuccess(ApiData<OnlineRemindSetupResponse> data) {
                OnlineRemindSetupResponse resp = data.resp;
                if (resp != null) {
                    if (!resp.hasVip) {
                        new ZPManager(activity, resp.vipUrl).handler();
                    } else {
                        if (resp.hasNoDisturb) {
                            showNoDisturbDialog();
                        }
                    }

                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_GEEK_VIP_CLICK)
                            .param("p", "geek-vip-" + source)
                            .param("p2", TextUtils.isEmpty(jobIdString) ? "" : jobIdString)
                            .param("p3", resp.hasVip ? 1 : 2)
                            .build();
                }
            }

            private void showNoDisturbDialog() {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_GEEK_VIP_CARD).build();
                DialogUtils d = new DialogUtils.Builder(activity)
                        .setDoubleButton()
                        .setTitle("订阅成功")
                        .setDesc("由于您已开启“夜间免打扰”，若Boss在免打扰时段上线可能无法收到推送")
                        .setNegativeAction("知道了", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_GEEK_VIP_CARD_CLICK)
                                        .param("p", 1)
                                        .build();
                            }
                        })
                        .setPositiveAction("去设置", new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_GEEK_VIP_CARD_CLICK)
                                        .param("p", 2)
                                        .build();
                                SettingRouter.jumpToMessagePushSettingActivity(activity, UserManager.isBossRole());
                            }
                        })
                        .build();
                d.show();
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        request.source = source;
        HttpExecutor.execute(request);
    }
}
