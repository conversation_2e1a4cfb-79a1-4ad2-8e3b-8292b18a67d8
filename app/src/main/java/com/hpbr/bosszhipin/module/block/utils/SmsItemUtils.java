package com.hpbr.bosszhipin.module.block.utils;

import android.content.Context;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.common.dialog.EditMessageContentDialog;
import com.monch.lbase.util.L;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.CommitMessageRequest;
import net.bosszhipin.api.SmsItemPreUseRequest;
import net.bosszhipin.api.SmsItemPreUseResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Author: zhouyou
 * Date: 2019/2/18
 */
public class SmsItemUtils {

    public static final int TYPE_PLUS = 1;
    public static final int TYPE_TIPS = 2;
    private Context context;

    public SmsItemUtils(Context context) {
        this.context = context;
    }

    /**
     * 短信通知预使用接口
     *
     * @param securityId
     */
    public void preUse(String securityId, int sourceType) {
        BaseActivity mActivity = null;
        if (context instanceof BaseActivity) {
            mActivity = (BaseActivity) context;
        }
        if (mActivity == null || mActivity.isFinishing() || mActivity.isDestroy) {
            L.e("请求失败");
            return;
        }

        BaseActivity activity = mActivity;
        SmsItemPreUseRequest request = new SmsItemPreUseRequest(new ApiRequestCallback<SmsItemPreUseResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<SmsItemPreUseResponse> data) {
                SmsItemPreUseResponse resp = data.resp;
                if (resp != null) {
                    if (resp.hasItem) {
                        showSmsSendDialog(activity, resp, securityId);
                    } else {
                        BusinessPageRouter.jumpToSmsNotifyGrayActivity(context, resp.itemType, sourceType, resp.lastSmsContent, resp.isItemBzbPageMergeGray());
                    }
                }
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = TextUtils.isEmpty(securityId) ? "" : securityId;
        request.sourceType = sourceType;
        HttpExecutor.execute(request);
    }

    /**
     * 展示短信发送对话框
     *
     * @param data 剩余道具数
     */
    private void showSmsSendDialog(BaseActivity activity, SmsItemPreUseResponse data, String securityId) {
        //短信弹窗
        EditMessageContentDialog editMessageContentDialog = new EditMessageContentDialog(activity, data);
        editMessageContentDialog.setCommitListener(input -> commitMessage(activity, input, securityId));
        editMessageContentDialog.show();
    }

    private void commitMessage(BaseActivity activity, String input, String securityId) {
        CommitMessageRequest request = new CommitMessageRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                ToastUtils.showText("提交成功");
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });

        request.securityId = securityId;
        request.smsContent = input;
        request.autoUse = 0;
        HttpExecutor.execute(request);
    }

    /**
     * 激活并发送
     *
     * @param activity
     * @param input
     * @param securityId
     */
    public void commitCustomMessage(BaseActivity activity, String securityId, String input, int sourceType) {
        if (TextUtils.isEmpty(input)) {
            preUse(securityId, sourceType);
        } else {
            CommitMessageRequest request = new CommitMessageRequest(new ApiRequestCallback<SuccessResponse>() {

                @Override
                public void onStart() {
                    activity.showProgressDialog("正在处理中");
                }

                @Override
                public void onSuccess(ApiData<SuccessResponse> data) {
                    ToastUtils.showText("提交成功");
                }

                @Override
                public void onComplete() {
                    activity.dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });

            request.securityId = securityId;
            request.smsContent = input;
            request.autoUse = 1;
            HttpExecutor.execute(request);
        }
    }
}
