package com.hpbr.bosszhipin.module.block.utils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.business_export.BusinessPageRouter;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.VirtualCallGeekUseBottomDialog;
import com.hpbr.bosszhipin.common.dialog.VirtualCallNightProtectionDialog;
import com.hpbr.bosszhipin.common.dialog.VirtualCallSimGeekDetailDialog;
import com.hpbr.bosszhipin.common.dialog.VirtualCallUseBottomDialog;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.pay.entity.ItemPaySource;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.LiveDataBus;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.DirectCallGeekPreUseRequest;
import net.bosszhipin.api.DirectCallGeekPreUseResponse;
import net.bosszhipin.api.DirectCallGeekUseRequest;
import net.bosszhipin.api.DirectCallGeekUseResponse;
import net.bosszhipin.api.DirectCallPreUseRequest;
import net.bosszhipin.api.DirectCallPreUseResponse;
import net.bosszhipin.api.DirectCallUseRequest;
import net.bosszhipin.api.DirectCallUseResponse;
import net.bosszhipin.api.ZPDirectCallPreUseRequest;
import net.bosszhipin.api.ZPDirectCallPreUseResponse;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.api.bean.ServerDirectCallSimGeekDetailBean;
import net.bosszhipin.base.ApiRequestCallback;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

/**
 * Author: zhouyou
 * Date: 2019-11-08
 * 虚拟电话工具
 */
public class VirtualCallUtils {

    public static final int TYPE_TIPS = 1;
    public static final int TYPE_DETAIL = 2;
    public static final int TYPE_GEEK_TIPS = 3;

    // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，区分打电话消耗的道具类型
    public static final int TYPE_CALL_CONNECT_KEY = 1001; // 使用沟通钥匙来拨打电话
    public static final int TYPE_CALL_PHONE = 29; // 使用电话直拨来拨打电话

    private final BaseActivity activity;

    private String securityId;
    private String source;
    private String encryptUserItemId;

    private int sourceType = TYPE_TIPS;

    public void setSecurityId(String securityId, String source) {
        setSecurityId(securityId, source, "");
    }

    public void setSecurityId(String securityId, String source, String encryptUserItemId) {
        this.securityId = securityId;
        this.source = source;
        this.encryptUserItemId = encryptUserItemId;
    }

    private long bossId;
    private long geekId;
    private long jobId;
    private long expectId;
    private int callType;
    private String encryptJobId; // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，添加 ZPDirectCallPreUseRequest 请求参数-加密的职位ID
    private String encryptGeekId; // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，添加 ZPDirectCallPreUseRequest 请求参数-加密的牛人ID

    public void setBossId(long bossId) {
        this.bossId = bossId;
    }

    public void setGeekId(long geekId) {
        this.geekId = geekId;
    }

    public void setJobId(long jobId) {
        this.jobId = jobId;
    }

    public void setExpectId(long expectId) {
        this.expectId = expectId;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * 1210.275【商业】沟通钥匙支持主动拨打电话消耗，设置本次打电话消耗的道具类型
     *
     * @param callType 29-电话直拨；1001-沟通钥匙
     */
    public void setCallType(int callType) {
        this.callType = callType;
    }

    /**
     * 1210.275【商业】沟通钥匙支持主动拨打电话消耗，传入加密的职位ID
     *
     * @param encryptJobId 加密的职位ID
     */
    public void setEncryptJobId(String encryptJobId) {
        this.encryptJobId  = encryptJobId;
    }

    /**
     * 1210.275【商业】沟通钥匙支持主动拨打电话消耗，传入加密的牛人ID
     *
     * @param encryptGeekId 加密的牛人ID
     */
    public void setEncryptGeekId(String encryptGeekId){
        this.encryptGeekId = encryptGeekId;
    }

    public VirtualCallUtils(BaseActivity activity) {
        this.activity = activity;
    }

    /**
     * 打电话权限授权
     */
    public void start() {
        if (UserManager.isBossRole()) {
            preUse();
        } else {
            geekPreUse();
        }
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_PHONE_AUTHORIZATION_POP_UP)
                .param("p", bossId)
                .param("p2", geekId)
                .param("p3", jobId)
                .param("p4", expectId)
                .param("p5", 0)
                .param("p6", 0)
                .secId(securityId)
                .build();
    }

    /**
     * 预使用
     */
    public void preUse() {
        DirectCallPreUseRequest request = new DirectCallPreUseRequest(new ApiRequestCallback<DirectCallPreUseResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<DirectCallPreUseResponse> data) {
                DirectCallPreUseResponse resp = data.resp;
                if (resp != null) {
                    if (resp.canCall) {
                        if (resp.hasItem) {
                            // 记录来源
                            if (resp.simGeekDetail != null) {
                                resp.simGeekDetail.directCallGeekDetailSource = resp.directCallGeekDetailSource;
                            }
                            if (TextUtils.equals(source, ItemPaySource.VIRTUAL_SIM_GEEK_CALL)) {
                                use(null);
                            } else {
                                showVirtualCallDialDialog(resp);
                            }
                        } else {
                            registerH5CallBack(activity, resp.detailUrl);
                            BusinessPageRouter.jumpToVirtualCallActivity(activity, securityId, resp.itemType, source, resp.detailUrl);
                        }
                    } else {
                        showVirtualCallNightProtectionDialog(resp.notCallDialog);
                    }
                }
            }

            /**
             *  1125.292【商业】电话直拨商详页改H5 - 购买成功回调
             */
            public void registerH5CallBack(Activity activity, String detailUrl) {
                if (activity instanceof LifecycleOwner && !LText.isEmptyOrNull(detailUrl)) {
                    LiveDataBus.singleObserver(ChannelConstants.BOSS_VIRTUAL_CALL_H5_BZB_RESULT_CALL_BACK)
                            .observe((LifecycleOwner) activity, obj -> {
                                if (null != obj) {
                                    start();
                                }
                            });
                }
            }


            /**
             * 展示弹框
             * 1206.276【商业】商业侧虚拟电话流程增加本机提示
             * 老弹窗：VirtualCallConfirmUsingDialog
             */
            private void showVirtualCallDialDialog(@NonNull DirectCallPreUseResponse resp) {
                VirtualCallUseBottomDialog ins = VirtualCallUseBottomDialog.getInstance(resp, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，当打电话消耗的道具为 沟通钥匙 时，需要调用沟通钥匙的 PreUse 接口
                        if (resp.itemType == TYPE_CALL_CONNECT_KEY) {
                            directCallPreUse(resp.simGeekDetail);
                        } else {
                            use(resp.simGeekDetail);
                        }
                    }
                });

                if (ActivityUtils.isValid(activity)) {
                    VirtualCallUseBottomDialog.showFragment(activity, ins);
                }
            }

            private void showVirtualCallNightProtectionDialog(ServerDialogBean dialog) {
                if (dialog == null) {
                    return;
                }
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_NIGHT_POP)
                        .param("p", TextUtils.isEmpty(source) ? "" : source)
                        .build();
                VirtualCallNightProtectionDialog d = new VirtualCallNightProtectionDialog(activity, dialog.title, dialog.content);
                d.show();
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (reason.getErrCode() == 600140) {
                    DialogUtils d = new DialogUtils.Builder(activity)
                            .setSingleButton()
                            .setTitle("无法拨打")
                            .setDesc(reason.getErrReason())
                            .setPositiveAction(R.string.string_confirm)
                            .build();
                    d.show();
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
        request.securityId = securityId;
        request.source = source;
        request.encryptUserItemId = encryptUserItemId;
        // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，使用沟通钥匙的时候，传入新的 businessType 300
        if (callType == TYPE_CALL_CONNECT_KEY) {
            request.businessType = DirectCallPreUseRequest.CALL_BUSINESS_TYPE_CONNECT_KEY;
        }
        HttpExecutor.execute(request);
    }

    private void geekPreUse() {
        DirectCallGeekPreUseRequest request = new DirectCallGeekPreUseRequest(new ApiRequestCallback<DirectCallGeekPreUseResponse>() {
            @Override
            public void onSuccess(ApiData<DirectCallGeekPreUseResponse> data) {
                DirectCallGeekPreUseResponse resp = data.resp;
                if (resp != null) {
                    if (resp.canCall) {
                        showGeekVirtualCallDialDialog(resp);
                    } else {
                        showVirtualCallNightProtectionDialog(resp.title, resp.content);
                    }
                }
            }

            /**
             * 展示弹框
             */
            private void showGeekVirtualCallDialDialog(@NonNull DirectCallGeekPreUseResponse resp) {
                VirtualCallGeekUseBottomDialog ins = VirtualCallGeekUseBottomDialog.getInstance(resp, new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        geekUse();
                    }
                });

                if (ActivityUtils.isValid(activity)) {
                    VirtualCallUseBottomDialog.showFragment(activity, ins);
                }
            }

            private void showVirtualCallNightProtectionDialog(String title, String content) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_BIZ_ITEM_DIRECT_CALL_NIGHT_POP)
                        .param("p", TextUtils.isEmpty(source) ? "" : source)
                        .build();
                VirtualCallNightProtectionDialog d = new VirtualCallNightProtectionDialog(activity, title, content);
                d.show();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (reason.getErrCode() == 600140) {
                    DialogUtils d = new DialogUtils.Builder(activity)
                            .setSingleButton()
                            .setTitle("无法拨打")
                            .setDesc(reason.getErrReason())
                            .setPositiveAction(R.string.string_confirm)
                            .build();
                    d.show();
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
        request.securityId = securityId;
        request.source = source;
        request.encryptUserItemId = encryptUserItemId;
        HttpExecutor.execute(request);
    }

    /**
     * 1210.275【商业】沟通钥匙支持主动拨打电话消耗，沟通钥匙使用前的 PreUse 接口
     *
     * @param simGeekDetail 透传给 use 接口的参数
     */
    private void directCallPreUse(ServerDirectCallSimGeekDetailBean simGeekDetail) {
        ZPDirectCallPreUseRequest request = new ZPDirectCallPreUseRequest(new ApiRequestCallback<ZPDirectCallPreUseResponse>() {
            @Override
            public void onStart() {
                activity.showProgressDialog();
            }

            @Override
            public void onSuccess(ApiData<ZPDirectCallPreUseResponse> data) {
                ZPDirectCallPreUseResponse response = data == null ? null : data.resp;
                if (response == null) {
                    return;
                }
                if (response.preUseRes) {
                    use(simGeekDetail);
                } else if(!LText.isEmptyOrNull(response.failTip)) {
                    ToastUtils.showText(response.failTip);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        request.encryptJobId = encryptJobId;
        request.encryptGeekId = encryptGeekId;
        request.execute();
    }

    /**
     * 使用
     */
    private void use(ServerDirectCallSimGeekDetailBean simGeekDetail) {
        DirectCallUseRequest request = new DirectCallUseRequest(new ApiRequestCallback<DirectCallUseResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog();
            }

            @Override
            public void onSuccess(ApiData<DirectCallUseResponse> data) {
                DirectCallUseResponse resp = data.resp;
                if (resp != null && !TextUtils.isEmpty(resp.telX)) {
                    dial(resp.telX);
                }
                showSimGeekDetailDialog(simGeekDetail);
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        request.source = sourceType;
        request.encryptUserItemId = encryptUserItemId;
        // 1210.275【商业】沟通钥匙支持主动拨打电话消耗，使用沟通钥匙的时候，传入新的 businessType 300
        if (callType == TYPE_CALL_CONNECT_KEY) {
            request.businessType = DirectCallUseRequest.CALL_BUSINESS_TYPE_CONNECT_KEY;
        }
        HttpExecutor.execute(request);
    }

    /**
     * 展示推荐牛人的弹框
     *
     * @param simGeekDetail
     */
    private void showSimGeekDetailDialog(ServerDirectCallSimGeekDetailBean simGeekDetail) {
        if (simGeekDetail == null) return;

        AnalyticsFactory.create().action(AnalyticsAction.ACTION_DIRECT_PHONE_BACKUP_EXP)
                .param("p", simGeekDetail.securityId)
                .secId(simGeekDetail.securityId)
                .build();
        VirtualCallSimGeekDetailDialog d = new VirtualCallSimGeekDetailDialog(activity, simGeekDetail, new VirtualCallSimGeekDetailDialog.OnSimGeekDetailActionCallback() {
            @Override
            public void onViewGeekDetail() {
                ParamBean paramBean = new ParamBean();
                paramBean.geekName = simGeekDetail.name;
                paramBean.securityId = simGeekDetail.securityId;
                paramBean.geekGender = simGeekDetail.gender;
                paramBean.geekAvatar = simGeekDetail.headUrl;
                paramBean.directCallGeekDetailSource = simGeekDetail.directCallGeekDetailSource;
                BossPageRouter.jumpResumeActivity(activity, paramBean);
            }

            @Override
            public void onGeekCall() {
                VirtualCallUtils utils = new VirtualCallUtils(activity);
                utils.setSecurityId(simGeekDetail.securityId, ItemPaySource.VIRTUAL_SIM_GEEK_CALL);
                utils.preUse();
            }
        });
        d.show();
    }

    /**
     * 使用
     */
    private void geekUse() {
        DirectCallGeekUseRequest request = new DirectCallGeekUseRequest(new ApiRequestCallback<DirectCallGeekUseResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<DirectCallGeekUseResponse> data) {
                DirectCallGeekUseResponse resp = data.resp;
                if (resp != null && !TextUtils.isEmpty(resp.telX)) {
                    dial(resp.telX);
                }
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        request.source = sourceType;
        request.encryptUserItemId = encryptUserItemId;
        HttpExecutor.execute(request);
    }

    private void dial(String tel) {
        Intent intent = new Intent();
        Uri mobileUri = Uri.parse("tel:" + tel);
        intent.setAction(Intent.ACTION_DIAL);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.setData(mobileUri);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
        }
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == BusinessPageRouter.IReqCode.REQ_VIRTUAL_CALL_PURCHASE && resultCode == Activity.RESULT_OK && data != null) {
            preUse();
        }
    }
}
