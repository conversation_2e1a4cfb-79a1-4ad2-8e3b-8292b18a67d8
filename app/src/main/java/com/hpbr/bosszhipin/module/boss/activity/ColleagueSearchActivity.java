package com.hpbr.bosszhipin.module.boss.activity;

import android.content.Intent;
import android.os.Bundle;
import android.widget.ImageView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;
import com.hpbr.bosszhipin.module.contacts.fragment.MateEmptyFragment;
import com.hpbr.bosszhipin.module.contacts.fragment.RecentActMateFragment;
import com.hpbr.bosszhipin.utils.PinYinUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetMateListV2Request;
import net.bosszhipin.api.GetMateListV2Response;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索同事页面，以startActivityForResult的方式调用，点击列表项返回同事对象
 * 不要在此页面写与具体业务相关的代码
 *
 * Created by quzhiyong on 2018/11/21
 */
public class ColleagueSearchActivity extends BaseActivity implements RecentActMateFragment.OnMateItemClickCallBack {

    public static final int REQ_CHOOSE_MATE = 100;

    // 全部同事集合
    protected final List<CompanyMateBean> allMatesList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_send_resume_to_mate);
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setBackClickListener();

        getMate();
    }


    /**
     * 点击同事列表回调
     *
     * @param companyMateBean 同事数据格式
     */
    @Override
    public void onMateListener(CompanyMateBean companyMateBean) {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        bundle.putParcelable(Constants.DATA_ENTITY, companyMateBean);
        intent.putExtras(bundle);
        setResult(RESULT_OK, intent);
        AppUtil.finishActivity(ColleagueSearchActivity.this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            if (requestCode == ColleagueSearchActivity.REQ_CHOOSE_MATE) {
                CompanyMateBean mateBean = data.getParcelableExtra(Constants.DATA_ENTITY);
                if (mateBean != null) {
                    onMateListener(mateBean);
                }
            }
        }
    }

    /**
     * 网络请求获得同事数据
     */
    protected void getMate() {
        GetMateListV2Request request = new GetMateListV2Request(new ApiRequestCallback<GetMateListV2Response>() {
            @Override
            public void onStart() {
                super.onStart();
                showProgressDialog("加载中", false);
            }

            @Override
            public void onSuccess(ApiData<GetMateListV2Response> data) {

                GetMateListV2Response.MateResult result = data.resp.result;
                if (result != null) {
                    List<CompanyMateBean> mateList = result.mateList;
                    if (mateList != null && mateList.size() > 0) {
                        allMatesList.addAll(mateList);
                        generatePinYinList(allMatesList);// 生成拼音查询
                    }
                }

                // 隐藏默认占位图
                hideLoadingEmptyImage();

                // 检测是否显示无同事View
                if (checkIsShowNoneMateView()) return;

                // 初始化选择同事或转发同事Fragment
                initChooseOrSendFragment();
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });

        request.page = 1;
        request.pageSize = 20;
        request.queryParams = "";

        HttpExecutor.execute(request);
    }

    protected void initChooseOrSendFragment() {
        initChooseMate();
    }

    /**
     * 初始化选择同事
     */
    protected void initChooseMate() {
        addFragment(RecentActMateFragment.getInstance(allMatesList));
    }

    protected boolean checkIsShowNoneMateView() {
        // 没有同事,显示空页面
        if (LList.getCount(allMatesList) == 0) {
            addFragment(new MateEmptyFragment());
            return true;
        }
        return false;
    }

    protected void hideLoadingEmptyImage() {
        // 隐藏占位图
        findViewById(R.id.iv_empty).setVisibility(ImageView.GONE);
    }

    protected void addFragment(Fragment fragment) {
        if (isFinishing()) {
            return;
        }
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.ll_container, fragment);
        transaction.commitAllowingStateLoss();
    }

    /**
     * 本地把文字转拼音
     */
    protected void generatePinYinList(List<CompanyMateBean> allMates) {
        for (CompanyMateBean searchResultMate : allMates) {
            List<String> namePinYin = PinYinUtil.makePinYin(searchResultMate.name);
            if (namePinYin != null) {
                searchResultMate.namePinYin.addAll(namePinYin);
            }
        }
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }
}
