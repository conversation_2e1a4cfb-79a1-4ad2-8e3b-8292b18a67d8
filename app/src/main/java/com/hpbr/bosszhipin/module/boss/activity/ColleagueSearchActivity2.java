package com.hpbr.bosszhipin.module.boss.activity;

import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;
import com.hpbr.bosszhipin.module.contacts.fragment.RecentActMateFragment;
import com.hpbr.bosszhipin.module.contacts.fragment.RecentActMateFragment2;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetMateBrandListRequest;
import net.bosszhipin.api.GetMateBrandListResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

/***
 *
 * 原因：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=58658669
 * 改换搜索同事接口
 * 由于完善公司主页信息及工作体验页面中的搜索同事接口变更，
 * 该activity在完善公司主页信息及工作体验中搜索同事时使用
 *
 * 搜索同事页面，以startActivityForResult的方式调用，点击列表项返回同事对象
 * 不要在此页面写与具体业务相关的代码
 *
 *
 * change 2022/1/14
 */
public class ColleagueSearchActivity2 extends ColleagueSearchActivity implements RecentActMateFragment.OnMateItemClickCallBack {


    /**
     * 更换后的获得同事数据接口
     */

    protected void getMate() {
        GetMateBrandListRequest request = new GetMateBrandListRequest(new ApiRequestCallback<GetMateBrandListResponse>() {

            public void onStart() {
                super.onStart();
                showProgressDialog("加载中", false);
            }

            @Override
            public void onSuccess(ApiData<GetMateBrandListResponse> data) {

                List<CompanyMateBean> mateList = null;

                if (null != data && null != data.resp && null != data.resp.result) {
                    mateList = data.resp.result.getMateList();
                }

                if (mateList != null && mateList.size() > 0) {
                    allMatesList.addAll(mateList);
                    generatePinYinList(allMatesList);// 生成拼音查询
                }


                // 隐藏默认占位图
                hideLoadingEmptyImage();

                // 检测是否显示无同事View
                if (checkIsShowNoneMateView()) return;

                // 初始化选择同事或转发同事Fragment
                initChooseOrSendFragment();
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.page = 1;
        request.pageSize = 20;
        request.queryParams = "";
        HttpExecutor.execute(request);
    }

    protected void initChooseOrSendFragment() {
        this.initChooseMate();
    }


    /**
     * 初始化选择同事
     */
    protected void initChooseMate() {
        addFragment(RecentActMateFragment2.getInstance(allMatesList));
    }
}
