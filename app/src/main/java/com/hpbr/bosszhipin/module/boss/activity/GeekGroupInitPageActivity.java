package com.hpbr.bosszhipin.module.boss.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.db.entry.GroupUserCardBean;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.IRefreshCallback;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.my.activity.InputActivity;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GeekHomeProfileSaveOrUpdateRequest;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;

import static com.hpbr.bosszhipin.common.app.ActivityAnimType.UP_GLIDE;
import static com.hpbr.bosszhipin.module.my.activity.information.EditUserInfoManager.USER_NAME_MAX_INPUT;

/**
 * Geek进入群聊之前设置昵称页面
 */
public class GeekGroupInitPageActivity extends BaseActivity implements View.OnClickListener {

    private static final int REQ_NAME = 0x01;
    private static final int REQ_TITLT = 0x02;
    private static final int MAX_LENGTH_GEEK_TITLE = 15;

    private boolean isEditNickName = false;
    private boolean isEditTitle = false;

    private TextView nickNameTv, titleTv;

    public static void jumpForResult(Context context) {
        Intent intent = new Intent(context, GeekGroupInitPageActivity.class);
        AppUtil.startActivity(context, intent, UP_GLIDE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_geek_group_init_page);
        initView();
        initData();
        setData();
    }

    private void initView() {
        nickNameTv = findViewById(R.id.nickname);
        titleTv = findViewById(R.id.title);
    }

    private void initData() {
        findViewById(R.id.backButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        findViewById(R.id.nickname_ll).setOnClickListener(this);
        findViewById(R.id.title_ll).setOnClickListener(this);
        findViewById(R.id.tv_next_step).setOnClickListener(this);
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    private void setData() {

        GroupManager.getInstance().syncGroupCard(new IRefreshCallback<GroupUserCardBean>() {
            @Override
            public void onRefreshFail(ErrorReason errorMsg) {
                T.ss(errorMsg.getErrReason());
            }

            @Override
            public void onRefreshSuccess(GroupUserCardBean userCardBean) {
                if(userCardBean!=null) {
                    nickNameTv.setText(userCardBean.name);
                    titleTv.setText(userCardBean.position);
                }
            }
        });
    }

    @Override
    public void onBackPressed() {

        if (isEditTitle || isEditNickName) {
            DialogUtils d = new DialogUtils.Builder(this)
                    .setDoubleButton()
                    .setTitle(R.string.warm_prompt)
                    .setDesc(R.string.string_content_has_not_save)
                    .setPositiveAction(R.string.string_wrong_click)
                    .setNegativeAction(R.string.string_give_up, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            finish();
                        }
                    })
                    .build();
            d.show();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    public void onClick(View v) {
        Intent intent;
        int i = v.getId();
        if (i == R.id.nickname_ll) {
            intent = new Intent(this, InputActivity.class);
            intent.putExtra(InputActivity.INPUT_TITLE, getString(R.string.geek_home_page_nickname));
            intent.putExtra(InputActivity.IS_INPUT_SAVE, false);
            intent.putExtra(InputActivity.INPUT_DATA, nickNameTv.getText().toString().trim());
            intent.putExtra(InputActivity.INPUT_MAX_LENGTH, USER_NAME_MAX_INPUT);
            intent.putExtra(InputActivity.IS_INPUT_MORE, false);
            AppUtil.startActivityForResult(this, intent, REQ_NAME, ActivityAnimType.UP_GLIDE);

        } else if (i == R.id.title_ll) {
            intent = new Intent(this, InputActivity.class);
            intent.putExtra(InputActivity.INPUT_TITLE, getString(R.string.geek_home_page_title));
            intent.putExtra(InputActivity.IS_INPUT_SAVE, false);
            intent.putExtra(InputActivity.INPUT_DATA, titleTv.getText().toString().trim());
            intent.putExtra(InputActivity.INPUT_MAX_LENGTH, MAX_LENGTH_GEEK_TITLE);
            intent.putExtra(InputActivity.IS_INPUT_MORE, false);
            AppUtil.startActivityForResult(this, intent, REQ_TITLT, ActivityAnimType.UP_GLIDE);

        } else if (i == R.id.tv_next_step) {
            GeekHomeProfileSaveOrUpdateRequest request = new GeekHomeProfileSaveOrUpdateRequest(new ApiRequestCallback<HttpResponse>() {

                @Override
                public void onStart() {
                    showProgressDialog("信息保存中，请稍候");
                }

                @Override
                public void onSuccess(ApiData<HttpResponse> data) {

                    editUserCard(nickNameTv.getText().toString(), titleTv.getText().toString());
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    T.ss(reason.getErrReason());
                }
            });
            request.geekId = UserManager.getUID();
            request.nickname = nickNameTv.getText().toString();
            request.geekTitle = titleTv.getText().toString();
            HttpExecutor.execute(request);


        } else {
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case REQ_NAME:
                    if (data == null) return;
                    String name = data.getStringExtra(InputActivity.INPUT_DATA);
                    if (LText.empty(name)) return;

                    if (!nickNameTv.getText().toString().equals(name))
                        isEditNickName = true;

                    nickNameTv.setText(name);
                    break;
                case REQ_TITLT:
                    if (data == null) return;
                    String title = data.getStringExtra(InputActivity.INPUT_DATA);
                    if (LText.empty(title)) return;

                    if (!titleTv.getText().toString().equals(title))
                        isEditTitle = true;

                    titleTv.setText(title);
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return isEditNickName || isEditTitle;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 编辑群名片
     *
     * @param name
     * @param position
     */
    public void editUserCard(final String name, final String position) {

        GroupUserCardBean bean = GroupManager.getInstance().getUserCard();
        if (bean == null) {
            bean = new GroupUserCardBean();
        }

        if (!TextUtils.isEmpty(name)) {
            bean.name = name;
        }
        if (!TextUtils.isEmpty(position)) {
            bean.position = position;
        }
        GroupManager.getInstance().saveUserCard(bean);
        T.ss("保存成功");
        AppUtil.finishActivity(GeekGroupInitPageActivity.this);
    }

}
