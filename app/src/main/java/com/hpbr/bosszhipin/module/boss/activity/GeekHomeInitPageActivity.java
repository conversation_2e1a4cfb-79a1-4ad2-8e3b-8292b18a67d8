package com.hpbr.bosszhipin.module.boss.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.my.activity.InputActivity;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GeekHomeProfileSaveOrUpdateRequest;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;

import java.util.List;

import static com.hpbr.bosszhipin.common.app.ActivityAnimType.UP_GLIDE;
import static com.hpbr.bosszhipin.module.my.activity.information.EditUserInfoManager.USER_NAME_MAX_INPUT;
import static com.hpbr.bosszhipin.module.my.activity.information.EditUserInfoManager.USER_NAME_MIN_INPUT;

/**
 * Geek进入主页之前设置昵称页面
 */
public class GeekHomeInitPageActivity extends BaseActivity implements View.OnClickListener {

    private static final int REQ_NAME = 0x01;
    private static final int REQ_TITLT = 0x02;
    private static final int MAX_LENGTH_GEEK_TITLE = 16;
    public static final int GEEK_HOME_ORIGIN_BOSS_HOME = 1001;
    public static final int GEEK_HOME_ORIGIN_TREND_LIST = 1002;
    public static final int GEEK_HOME_ORIGIN_GEEK_HOME = 1003;
    public static final int GEEK_HOME_INIT_NOT_COMPLETE = 1004;
    /**
     * 统计打点是否返回 是否下一步
     */
    public static final int CLOSE_GUIDE_WINDOW = 0;
    public static final int NEXT_STEP = 1;

    private boolean isEditNickName = false;
    private boolean isEditTitle = false;

    private TextView nickNameTv, titleTv;
    private GeekInfoBean geekInfo;
    UserBean user;

    public static void jump(Context context, int requestCode) {
        Intent intent = new Intent(context, GeekHomeInitPageActivity.class);
        AppUtil.startActivityForResult(context, intent, requestCode, UP_GLIDE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_geek_home_init_page);
        initView();
        initData();
        setData();
    }


    private void initView() {
        nickNameTv = findViewById(R.id.nickname);
        titleTv = findViewById(R.id.title);
    }

    private void initData() {
        findViewById(R.id.backButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        findViewById(R.id.nickname_ll).setOnClickListener(this);
        findViewById(R.id.title_ll).setOnClickListener(this);
        findViewById(R.id.tv_next_step).setOnClickListener(this);
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    private void setData() {
        user = UserManager.getLoginUser();
        if (user == null || (geekInfo = user.geekInfo) == null) {
            T.ss("数据异常");
            AppUtil.finishActivity(this);
            return;
        }
        nickNameTv.setText(user.name);

        List<JobIntentBean> list = GeekExpectManager.getGeekExpectList();
        if (!LList.isEmpty(list)) {
            JobIntentBean bean = LList.getElement(list, list.size() - 1);
            if (bean != null) {
                titleTv.setText(bean.positionClassName);
            }
        }
    }

    @Override
    public void onBackPressed() {

        if (isEditTitle || isEditNickName) {
            DialogUtils d = new DialogUtils.Builder(this)
                    .setDoubleButton()
                    .setTitle(R.string.warm_prompt)
                    .setDesc(R.string.string_content_has_not_save)
                    .setPositiveAction(R.string.string_wrong_click)
                    .setNegativeAction(R.string.string_give_up, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            AnalyticsFactory.create()
                                    .action(AnalyticsAction.ACTION_GEEK_NICKNAME_SET)
                                    .param("p", String.valueOf(CLOSE_GUIDE_WINDOW))
                                    .param("p2", String.valueOf(isEditNickName ? 1 : 0))
                                    .param("p3", String.valueOf(isEditTitle ? 1 : 0))
                                    .build();

                            Intent intent = new Intent();
                            intent.putExtra("NOT_COMPLETE", GEEK_HOME_INIT_NOT_COMPLETE);
                            setResult(RESULT_OK, intent);
                            finish();
                        }
                    })
                    .build();
            d.show();
        } else {

            AnalyticsFactory.create()
                    .action(AnalyticsAction.ACTION_GEEK_NICKNAME_SET)
                    .param("p", String.valueOf(CLOSE_GUIDE_WINDOW))
                    .param("p2", String.valueOf(isEditNickName ? 1 : 0))
                    .param("p3", String.valueOf(isEditTitle ? 1 : 0))
                    .build();

            Intent intent = new Intent();
            intent.putExtra("NOT_COMPLETE", GEEK_HOME_INIT_NOT_COMPLETE);
            setResult(RESULT_OK, intent);
            super.onBackPressed();
        }
    }

    @Override
    public void onClick(View v) {
        Intent intent;
        int i = v.getId();
        if (i == R.id.nickname_ll) {
            intent = new Intent(this, InputActivity.class);
            intent.putExtra(InputActivity.INPUT_TITLE, getString(R.string.geek_home_page_nickname));
            intent.putExtra(InputActivity.IS_INPUT_SAVE, false);
            intent.putExtra(InputActivity.INPUT_DATA, nickNameTv.getText().toString().trim());
            intent.putExtra(InputActivity.INPUT_MAX_LENGTH, USER_NAME_MAX_INPUT);
            intent.putExtra(InputActivity.INPUT_MIN_LENGTH, USER_NAME_MIN_INPUT);
            intent.putExtra(InputActivity.IS_INPUT_MORE, false);
            AppUtil.startActivityForResult(this, intent, REQ_NAME, ActivityAnimType.UP_GLIDE);

        } else if (i == R.id.title_ll) {
            intent = new Intent(this, InputActivity.class);
            intent.putExtra(InputActivity.INPUT_TITLE, getString(R.string.geek_home_page_title));
            intent.putExtra(InputActivity.IS_INPUT_SAVE, false);
            intent.putExtra(InputActivity.INPUT_DATA, titleTv.getText().toString().trim());
            intent.putExtra(InputActivity.INPUT_MAX_LENGTH, MAX_LENGTH_GEEK_TITLE);
            intent.putExtra(InputActivity.IS_INPUT_MORE, false);
            AppUtil.startActivityForResult(this, intent, REQ_TITLT, ActivityAnimType.UP_GLIDE);

        } else if (i == R.id.tv_next_step) {
            GeekHomeProfileSaveOrUpdateRequest request = new GeekHomeProfileSaveOrUpdateRequest(new ApiRequestCallback<HttpResponse>() {

                @Override
                public void onStart() {
                    showProgressDialog("信息保存中，请稍候");
                }

                @Override
                public void onSuccess(ApiData<HttpResponse> data) {

                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_GEEK_NICKNAME_SET)
                            .param("p", String.valueOf(NEXT_STEP))
                            .param("p2", String.valueOf(isEditNickName ? 1 : 0))
                            .param("p3", String.valueOf(isEditTitle ? 1 : 0))
                            .build();

                    setResult(RESULT_OK);
                    GeekHomeInitPageActivity.this.finish();
                }

                @Override
                public void onComplete() {
                    dismissProgressDialog();
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    T.ss(reason.getErrReason());
                }
            });
            request.geekId = UserManager.getUID();
            request.nickname = nickNameTv.getText().toString();
            request.geekTitle = titleTv.getText().toString();
            HttpExecutor.execute(request);


        } else {
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case REQ_NAME:
                    if (data == null) return;
                    String name = data.getStringExtra(InputActivity.INPUT_DATA);
                    if (LText.empty(name)) return;

                    if (!nickNameTv.getText().toString().equals(name))
                        isEditNickName = true;

                    nickNameTv.setText(name);
                    break;
                case REQ_TITLT:
                    if (data == null) return;
                    String title = data.getStringExtra(InputActivity.INPUT_DATA);
                    if (LText.empty(title)) return;

                    if (!titleTv.getText().toString().equals(title))
                        isEditTitle = true;

                    titleTv.setText(title);
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return isEditNickName || isEditTitle;
        }
        return super.onKeyDown(keyCode, event);
    }


}
