package com.hpbr.bosszhipin.module.boss.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import com.github.piasy.biv.view.BigImageView;
import com.github.piasy.biv.view.FrescoImageViewFactory;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhangxiangdong on 2018/8/7 10:58.
 */
public class SnsUploadBrowseActivity extends BaseActivity {

    public static final String KEY_IMAGE_LOCAL_PATHS = "key_image_local_paths";
    private static final String KEY_INITIAL_INDEX = "key_initial_index";

    @Nullable
    private ArrayList<String> selectedImageList; // 已选本地图片地址集合

    private ViewPager pager;
    private ImagePagerAdapter adapter;
    private MTextView indicatorText;

    public static void previewImageForResult(Activity source, ArrayList<String> localImagePaths, int previewIndex, int requestCode) {
        Intent intent = new Intent(source, SnsUploadBrowseActivity.class);
        intent.putExtra(KEY_IMAGE_LOCAL_PATHS, localImagePaths);
        intent.putExtra(KEY_INITIAL_INDEX, previewIndex);
        AppUtil.startActivityForResult(source, intent, requestCode, ActivityAnimType.UP_GLIDE);
    }

    public static void previewImageForResultByFragment(Context context, Fragment startFragment, ArrayList<String> localImagePaths, int previewIndex, int requestCode) {
        Intent intent = new Intent(context, SnsUploadBrowseActivity.class);
        intent.putExtra(KEY_IMAGE_LOCAL_PATHS, localImagePaths);
        intent.putExtra(KEY_INITIAL_INDEX, previewIndex);
        startFragment.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sns_upload_browse);

        //noinspection unchecked
        selectedImageList = (ArrayList<String>) getIntent().getSerializableExtra(KEY_IMAGE_LOCAL_PATHS);
        final int initialIndex = getIntent().getIntExtra(KEY_INITIAL_INDEX, 0);

        pager = findViewById(R.id.vp_pictures);
        adapter = new ImagePagerAdapter(getSupportFragmentManager(), selectedImageList);
        pager.setAdapter(adapter);
        pager.setCurrentItem(initialIndex);
        pager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                setCurrentIndicator(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        findViewById(R.id.backButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pressBack();
            }
        });
        findViewById(R.id.deleteButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onRemoveCurrentImage();
            }
        });

        indicatorText = findViewById(R.id.indicatorText);
        setCurrentIndicator(initialIndex);
    }

    private void setCurrentIndicator(int index /* 从0开始计 */) {
        if (selectedImageList == null) return;
        indicatorText.setText(String.format(Locale.getDefault(), "%d/%d", index + 1, selectedImageList.size()));
    }

    private void onRemoveCurrentImage() {
        if (selectedImageList == null) return;

        int currentItem = pager.getCurrentItem();
        if (currentItem >= selectedImageList.size()) return;

        selectedImageList.remove(currentItem);

        adapter.notifyChangeInPosition(currentItem);
        adapter.notifyDataSetChanged();
        setCurrentIndicator(pager.getCurrentItem()); // 刷新指示器

        if (selectedImageList.size() == 0) {
            pressBack();
        }
    }

    private void pressBack() {
        Intent data = new Intent();
        data.putExtra(KEY_IMAGE_LOCAL_PATHS, selectedImageList);
        setResult(RESULT_OK, data);
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            pressBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public static class ImagePagerAdapter extends FragmentPagerAdapter {

        private int baseId;

        @Nullable
        private List<String> imageList;

        ImagePagerAdapter(@NonNull FragmentManager fm, @Nullable List<String> imageList) {
            super(fm);
            this.imageList = imageList;
        }

        @Override
        public Fragment getItem(int position) {
            return ImageBrowseFragment.newInstance(LList.getElement(imageList, position));
        }

        @Override
        public int getCount() {
            return LList.getCount(imageList);
        }

        @Override
        public int getItemPosition(@NonNull Object object) {
            // Causes adapter to reload all Fragments when
            // notifyDataSetChanged is called
            return POSITION_NONE;
        }

        /* https://stackoverflow.com/a/26944013/3094830 */
        @Override
        public long getItemId(int position) {
            return baseId + position;
        }

        /**
         * Notify that the position of a fragment has been changed.
         * Create a new ID for each position to force recreation of the fragment
         *
         * @param n number of items which have been changed
         */
        @SuppressWarnings("WeakerAccess")
        public void notifyChangeInPosition(int n) {
            // shift the ID returned by getItemId outside the range of all previous fragments
            baseId += getCount() + n;
        }

    }

    public static class ImageBrowseFragment extends BaseFragment {

        private static final String KEY_IMAGE_LOCAL_PATH = "key_image_local_path";

        public static ImageBrowseFragment newInstance(@Nullable String imageLocalPath) {
            Bundle args = new Bundle();
            args.putString(KEY_IMAGE_LOCAL_PATH, imageLocalPath);
            ImageBrowseFragment fragment = new ImageBrowseFragment();
            fragment.setArguments(args);
            return fragment;
        }

        @Nullable
        @Override
        public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
            return inflater.inflate(R.layout.fragment_image_browse, container, false);
        }

        @Override
        public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
            super.onViewCreated(view, savedInstanceState);
            if (getArguments() == null) return;

            BigImageView bigImageView = view.findViewById(R.id.zdv_picture);
            bigImageView.setImageViewFactory(new FrescoImageViewFactory());

            String localImagePath = getArguments().getString(KEY_IMAGE_LOCAL_PATH);
            if (localImagePath == null) return;

            bigImageView.showImage(Uri.parse(localImagePath));
        }
    }

}
