package com.hpbr.bosszhipin.module.boss.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.VideoView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.window.FloatWindowManager;

/**
 * Created by zhangxiangdong on 2018/8/8 10:32.
 */
public class SnsUploadBrowseVideoActivity extends BaseActivity {

    public static final String KEY_VIDEO_LOCAL_PATH = "key_video_local_path";

    private VideoView videoPreviewView;
    private ImageView playButton;
    @Nullable
    private String videoPath;

    public static void previewVideoForResult(Activity source, @NonNull String videoPath, int requestCode) {
        Intent intent = new Intent(source, SnsUploadBrowseVideoActivity.class);
        intent.putExtra(KEY_VIDEO_LOCAL_PATH, videoPath);
        AppUtil.startActivityForResult(source, intent, requestCode, ActivityAnimType.UP_GLIDE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        setContentView(R.layout.activity_sns_upload_browse_video);
        FloatWindowManager.getInstance().resetWindow();

        videoPath = getIntent().getStringExtra(KEY_VIDEO_LOCAL_PATH);

        findViewById(R.id.backButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                pressBack();
            }
        });
        findViewById(R.id.deleteButton).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                removeVideo();
            }
        });
        videoPreviewView = findViewById(R.id.videoPreviewView);
        playButton = findViewById(R.id.playButton);
        ProgressBar prepareVideoProgress = findViewById(R.id.prepareVideoProgress);
        prepareVideoProgress.setVisibility(View.VISIBLE);

        videoPreviewView.setVideoPath(videoPath);
        videoPreviewView.setOnPreparedListener(mp -> {
            mp.setLooping(true); // 设置循环播放
            prepareVideoProgress.setVisibility(View.GONE);
            videoPreviewView.start(); // 自动播放
        });
        videoPreviewView.setOnCompletionListener(mp -> playButton.setVisibility(View.VISIBLE));
        videoPreviewView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (videoPreviewView.isPlaying()) { // 点击播放的视频，暂停播放
                    videoPreviewView.pause();
                    playButton.setVisibility(View.VISIBLE);
                    playButton.startAnimation(AnimationUtils.loadAnimation(playButton.getContext(), R.anim.zoom_out_fade_in_slow));
                } else { // 点击暂停的视频，继续播放
                    videoPreviewView.start();
                    playButton.setVisibility(View.GONE);
                    playButton.clearAnimation();
                }
            }
        });

    }

    private void pressBack() {
        Intent data = new Intent();
        data.putExtra(KEY_VIDEO_LOCAL_PATH, videoPath);
        setResult(RESULT_OK, data);
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            pressBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void removeVideo() {
        videoPath = null; // 标记移除视频
        pressBack();
    }

    private void stopVideoPlayingIfNeeded() {
        if (videoPreviewView != null && videoPreviewView.isPlaying()) {
            videoPreviewView.pause();
            videoPreviewView.stopPlayback();
        }
    }

    private void pausePlayingVideoIfNeeded() {
        if (videoPreviewView != null && videoPreviewView.isPlaying()) {
            videoPreviewView.pause();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        pausePlayingVideoIfNeeded();
    }

    @Override
    protected void onDestroy() {
        stopVideoPlayingIfNeeded();
        super.onDestroy();
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }
}
