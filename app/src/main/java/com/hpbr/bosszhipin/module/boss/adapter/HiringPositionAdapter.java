package com.hpbr.bosszhipin.module.boss.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.common.adapter.RendererRecyclerViewAdapter;
import com.hpbr.bosszhipin.module.boss.render.BossPositionRenderer;

import net.bosszhipin.api.bean.ServerJobItemBean;

import java.util.List;

/**
 * Created by zhangxiangdong on 2018/7/31 20:26.
 */
public class HiringPositionAdapter extends RendererRecyclerViewAdapter {

    public interface OnPositionItemClickListener {
        void onJobItemClick(@NonNull ServerJobItemBean jobItemBean);
    }

    public HiringPositionAdapter(@Nullable List<? extends ItemModel> items, Context context, OnPositionItemClickListener listener) {
        super(items, context);
        registerRenderer(new BossPositionRenderer(context, listener));
    }

}
