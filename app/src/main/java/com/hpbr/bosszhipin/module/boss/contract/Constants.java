package com.hpbr.bosszhipin.module.boss.contract;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/6/4 14:14.
 */
public final class Constants {

    private Constants() {
    }

    public static final int CONTEXT_C2B = 1; // 牛人看BOSS
    public static final int CONTEXT_B2B = 2; // BOSS看自己
    public static final int CONTEXT_FROM_LIST = 4; // 来自媒体列表
    public static final int CONTEXT_FROM_BOSS = 8; // 来自BOSS主页

    public static final int CONTEXT_C2B_FROM_LIST = CONTEXT_C2B | CONTEXT_FROM_LIST;
    public static final int CONTEXT_C2B_FROM_BOSS = CONTEXT_C2B | CONTEXT_FROM_BOSS;
    public static final int CONTEXT_B2B_FROM_LIST = CONTEXT_B2B | CONTEXT_FROM_LIST;
    public static final int CONTEXT_B2B_FROM_BOSS = CONTEXT_B2B | CONTEXT_FROM_BOSS;

    /**
     * 后台定义的媒体类型参数值
     */
    public static final int MEDIA_TYPE_VIDEO = 1; // 媒体类型，视频
    public static final int MEDIA_TYPE_IMAGE = 2; // 媒体类型，图片

    /**
     * 发布的媒体类型
     */
    public static final int POST_MEDIA_TYPE_TEXT = 0;
    public static final int POST_MEDIA_TYPE_IMAGE = 1;
    public static final int POST_MEDIA_TYPE_VIDEO = 2;
    public static final int POST_MEDIA_TYPE_LINK = 3;

    public interface Chat {

        /**
         * 悟空群聊 展示名称
         */
        String GROUP_NAME_WUKONG = "项目外包";
    }

}
