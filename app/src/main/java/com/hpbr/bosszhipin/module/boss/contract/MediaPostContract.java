package com.hpbr.bosszhipin.module.boss.contract;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;

/**
 * Created by <PERSON>hangxiangdong on 2018/8/6 15:03.
 */
public interface MediaPostContract {

    interface ImagePost {

        void onPreviewImage(ArrayList<String> images, int position);

        void onSelectImages(int selectCount);

        void onPostImage(@NonNull String imageUrls, @NonNull String thumbnailImageUrls);

    }

    interface LinkPost {

        void onAddLink();

        void onLinkRemoved();

        void onPostLink(@NonNull String link, @Nullable String linkTitle, @Nullable String linkIcon);

    }

}
