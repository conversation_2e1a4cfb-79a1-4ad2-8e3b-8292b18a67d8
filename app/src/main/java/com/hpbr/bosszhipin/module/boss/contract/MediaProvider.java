package com.hpbr.bosszhipin.module.boss.contract;

import androidx.annotation.IntDef;
import androidx.annotation.IntRange;
import androidx.annotation.NonNull;

import static com.hpbr.bosszhipin.module.boss.contract.Constants.POST_MEDIA_TYPE_IMAGE;
import static com.hpbr.bosszhipin.module.boss.contract.Constants.POST_MEDIA_TYPE_LINK;
import static com.hpbr.bosszhipin.module.boss.contract.Constants.POST_MEDIA_TYPE_TEXT;
import static com.hpbr.bosszhipin.module.boss.contract.Constants.POST_MEDIA_TYPE_VIDEO;

/**
 * Created by zhangxiangdong on 2018/8/10 11:58.
 */
public interface MediaProvider {

    @MediaType
    int getMediaType();

    void release();

    void onPreparePost(@NonNull PostCallback callback);

    @IntDef({POST_MEDIA_TYPE_TEXT,
            POST_MEDIA_TYPE_IMAGE,
            POST_MEDIA_TYPE_VIDEO,
            POST_MEDIA_TYPE_LINK})
    @interface MediaType {
    }

    interface PostCallback {

        void onStartPost();

        void onProgressUpdate(@IntRange(from = 0, to = 100) int progress);

        void onStopPost();

    }

}
