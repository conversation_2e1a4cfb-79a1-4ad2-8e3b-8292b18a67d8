package com.hpbr.bosszhipin.module.boss.entity;

import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.common.adapter.ItemModelHasSubItems;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerBossEduBean;

import java.util.List;

/**
 * Created by zhangxiangdong on 2018/5/10 15:53.
 */
public class EducationalBackgroundItemModel implements ItemModelHasSubItems {

    private final List<EBSubItemModel> subItemModelList;

    public EducationalBackgroundItemModel(List<EBSubItemModel> subItemModelList) {
        this.subItemModelList = subItemModelList;
    }

    public void setSubItemModelList(List<EBSubItemModel> subItemModelList) {
        if (LList.getCount(subItemModelList) == 0) return;

        this.subItemModelList.clear();
        this.subItemModelList.addAll(subItemModelList);
    }

    @Override
    public List<? extends ItemModel> getSubItemModels() {
        return subItemModelList;
    }

    @SuppressWarnings("WeakerAccess")
    public static class EBSubItemModel extends ServerBossEduBean implements ItemModel {

        private static final long serialVersionUID = 7456662230556538200L;

    }

}
