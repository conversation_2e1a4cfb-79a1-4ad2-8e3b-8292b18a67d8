package com.hpbr.bosszhipin.module.boss.entity;

import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.module.boss.entity.server.Labels;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/5/10 15:25.
 */
public class HeaderItemModel implements ItemModel {

    public String name;
    public String tiny;
    public String large;
    public String title;
    public String brandName;
    public Labels labels;

    private HeaderItemModel(Builder builder) {
        name = builder.name;
        tiny = builder.tiny;
        large = builder.large;
        title = builder.title;
        brandName = builder.brandName;
        labels = builder.labels;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static final class Builder {
        private String name;
        private String tiny;
        private String large;
        private String title;
        private String brandName;
        private Labels labels;

        private Builder() {
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder tiny(String tiny) {
            this.tiny = tiny;
            return this;
        }

        public Builder large(String large) {
            this.large = large;
            return this;
        }

        public Builder title(String title) {
            this.title = title;
            return this;
        }

        public Builder brandName(String brandName) {
            this.brandName = brandName;
            return this;
        }

        public Builder labels(Labels labels) {
            this.labels = labels;
            return this;
        }

        public HeaderItemModel build() {
            return new HeaderItemModel(this);
        }
    }
}
