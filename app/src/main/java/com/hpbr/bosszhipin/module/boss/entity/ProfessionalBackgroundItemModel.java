package com.hpbr.bosszhipin.module.boss.entity;

import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.common.adapter.ItemModelHasSubItems;

import net.bosszhipin.api.bean.ServerBossWorkBean;

import java.util.List;

/**
 * Created by <PERSON>hangxiangdong on 2018/5/10 15:51.
 */
public class ProfessionalBackgroundItemModel implements ItemModelHasSubItems {

    private final List<PBSubItemModel> subItemModelList;

    public ProfessionalBackgroundItemModel(List<PBSubItemModel> subItemModelList) {
        this.subItemModelList = subItemModelList;
    }

    @Override
    public List<? extends ItemModel> getSubItemModels() {
        return subItemModelList;
    }

    @SuppressWarnings("WeakerAccess")
    public static class PBSubItemModel extends ServerBossWorkBean implements ItemModel {
        private static final long serialVersionUID = -9079902143188560946L;
    }

}
