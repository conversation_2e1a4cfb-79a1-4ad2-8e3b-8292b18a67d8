package com.hpbr.bosszhipin.module.boss.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.utils.platform.Utils;
import com.twl.utils.DisplayHelper;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;

/**
 * Created by quzhiyong on 2018/12/7
 */
public class BaseBottomSheetFragment extends BottomSheetDialogFragment {

    @NonNull
    private final Handler handler = new Handler();

    /**
     * 期望高度高度
     */
    private int expectHeight;
    /**
     * 顶部向下偏移量
     */
    private int topOffset = 0;

    private float dimAmount = 0.6f;
    private BottomSheetBehavior<FrameLayout> behavior;
    private boolean heightWrapContent = false;

    /*下拉隐藏开关*/
    protected boolean enableDropDownDismiss = false;
    /*下拉隐藏偏移量*/
    protected float dropDownDismissOffset = 0.80f;
    /*下拉隐藏标志*/
    protected boolean dropDownDismissFlag = false;

     private boolean draggable = true;

    public void setDraggable(boolean draggable) {
        this.draggable = draggable;
    }

    public void setEnableDropDownDismiss(boolean enableDropDownDismiss) {
        this.enableDropDownDismiss = enableDropDownDismiss;
    }

    public void setDropDownDismissOffset(float dropDownDismissOffset) {
        this.dropDownDismissOffset = dropDownDismissOffset;
    }

    public void hideFragment() {
        if (getBehavior() != null) {
            getBehavior().setState(BottomSheetBehavior.STATE_HIDDEN);
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        if (getContext() == null) {
            return super.onCreateDialog(savedInstanceState);
        }
        @SuppressLint("BZL-FragmentUsage")
        BottomSheetDialog b = new BottomSheetDialog(getContext(), R.style.TransparentBottomSheetStyle);
        b.setCanceledOnTouchOutside(enableTouchOutSide());
        return b;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setWindowAnimations(R.style.Animation_Design_BottomSheetDialog);
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    protected boolean enableTouchOutSide() {
        return true;
    }

    @Override
    public void onStart() {
        super.onStart();
        // 设置软键盘不自动弹出
        getDialog().getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        getDialog().getWindow().setDimAmount(getDimAmount());
        final BottomSheetDialog dialog = (BottomSheetDialog) getDialog();
        FrameLayout bottomSheet = dialog.getDelegate().findViewById(R.id.design_bottom_sheet);
        if (bottomSheet != null) {
            CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) bottomSheet.getLayoutParams();
            if (!heightWrapContent) {
                layoutParams.height = getHeight();
            }
            bottomSheet.setLayoutParams(layoutParams);
            behavior = BottomSheetBehavior.from(bottomSheet);
            // 初始为展开状态
            behavior.setState(getInitialBottomSheetState());
            behavior.setPeekHeight(getInitialBottomSheetPeekHeight());
            behavior.setDraggable(draggable);
            behavior.setBottomSheetCallback(new BottomSheetBehavior.BottomSheetCallback() {
                @Override
                public void onStateChanged(@NonNull View bottomSheet, int newState) {
                    if (!isEnableDragDown()) {
                        if (newState == BottomSheetBehavior.STATE_HIDDEN || newState == BottomSheetBehavior.STATE_DRAGGING || newState == BottomSheetBehavior.STATE_SETTLING ||
                                newState == BottomSheetBehavior.STATE_EXPANDED || newState == BottomSheetBehavior.STATE_COLLAPSED) {
                            behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                        }
                        return;
                    }

                    if (newState == BottomSheetBehavior.STATE_HIDDEN) {
                        dialog.dismiss();
                    }
                    //下拉隐藏处理逻辑
                    if (enableDropDownDismiss && dropDownDismissFlag) {
                        hideFragment();
                    }
                    onStateChange(bottomSheet, newState);
                }

                @Override
                public void onSlide(@NonNull View bottomSheet, float slideOffset) {
                    //下拉隐藏处理逻辑
                    if (enableDropDownDismiss) {
                        dropDownDismissFlag = slideOffset <= dropDownDismissOffset;
                    }
                    onSlideListener(bottomSheet, slideOffset);
                }
            });
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        handler.postDelayed(() -> {
            if (getDialog() != null && getDialog().getWindow() != null) {
                getDialog().getWindow().setWindowAnimations(R.style.Animation_Design_BottomSheetDialog);
            }
        }, 300);
    }

    @Override
    public void onStop() {
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setWindowAnimations(0);
        }
        super.onStop();
    }

    @Override
    public void onDestroy() {
        handler.removeCallbacksAndMessages(null);
        super.onDestroy();
    }

    protected void onStateChange(@NonNull View bottomSheet, int newState) {

    }

    protected void onSlideListener(@NonNull View bottomSheet, float slideOffset) {

    }

    protected void setCanHidden(boolean hideable) {
        if (behavior != null) {
            behavior.setHideable(hideable);
        }
    }

    protected int getInitialBottomSheetState() {
        return BottomSheetBehavior.STATE_EXPANDED;
    }

    protected int getInitialBottomSheetPeekHeight() {
        return BottomSheetBehavior.PEEK_HEIGHT_AUTO;
    }

    protected float getDimAmount() {
        if (dimAmount < 0 || dimAmount > 1) {
            return 0.6f;
        }
        return dimAmount;
    }

    /**
     * 获取弹窗高度
     *
     * @return height
     */
    public int getHeight() {
        if (expectHeight > 0) {
            return getExpectHeight();
        }
        return DisplayHelper.getScreenHeight(Utils.getApp()) - getTopOffset() - ScreenUtil.getStatusBarHeight(Utils.getApp());
    }

    public int getTopOffset() {
        return topOffset;
    }

    public void setTopOffset(int topOffset) {
        this.topOffset = topOffset;
    }

    public void setHeightWrapContent(boolean heightWrapContent) {
        this.heightWrapContent = heightWrapContent;
    }

    public BottomSheetBehavior<FrameLayout> getBehavior() {
        return behavior;
    }

    public int getExpectHeight() {
        return expectHeight;
    }

    public void setExpectHeight(int expectHeight) {
        this.expectHeight = expectHeight;
    }

    /**
     * 设置BottomSheet是否可拖拽
     *
     * @return
     */
    protected boolean isEnableDragDown() {
        return true;
    }

    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        try {
            Class c = Class.forName("androidx.fragment.app.DialogFragment");
            Constructor con = c.getConstructor();
            Object obj = con.newInstance();
            Field dismissed = c.getDeclaredField("mDismissed");
            dismissed.setAccessible(true);
            dismissed.set(obj, false);
            Field shownByMe = c.getDeclaredField("mShownByMe");
            shownByMe.setAccessible(true);
            shownByMe.set(obj, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        FragmentTransaction ft = manager.beginTransaction();
        ft.add(this, tag);
        ft.commitAllowingStateLoss();
    }
}
