package com.hpbr.bosszhipin.module.boss.fragment;

import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.views.BossAssistantCompanyInfoItemView;
import com.hpbr.bosszhipin.module.boss.views.BossAssistantCompanyView;
import com.hpbr.bosszhipin.module.boss.views.BossAssistantFeedbackView;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import net.bosszhipin.api.BossCompanyInfoAssistantResponse;
import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;
import zpui.lib.ui.shadow.layout.ZPUILinearLayout;

/**
 * Boss查看牛人所在公司信息（NLP查询的数据）
 * <p>
 * 810修改交互逻辑，将BossCompanyInfoAssistantActivity中的内容拷贝至此
 */
public class BossCompanyInfoAssistantFragment extends BaseBottomSheetFragment {

    public static BossCompanyInfoAssistantFragment getInstance(BossCompanyInfoAssistantResponse data, long userId) {
        BossCompanyInfoAssistantFragment fragment = new BossCompanyInfoAssistantFragment();
        fragment.setHeightWrapContent(false);

        Bundle bundle = new Bundle();
        bundle.putSerializable(INTENT_DATA, data);
        bundle.putLong(USER_ID, userId);
        fragment.setArguments(bundle);

        return fragment;
    }

    public static void showFragment(FragmentManager manager, BossCompanyInfoAssistantFragment fragment) {
        if (null != fragment)
            fragment.show(manager, BossCompanyInfoAssistantFragment.class.getSimpleName());
    }

    private static final String INTENT_DATA = "intent_data";
    private static final String USER_ID = Constants.PREFIX + ".USER_ID";

    private ConstraintLayout clParent;

    private MTextView mTvCompanyName;
    private MTextView mTvCompanyNameTip;
    private MTextView mTvRelated;
    private LinearLayout llVipParent;
    private ImageView mIvVIPIcon;
    private MTextView mTvVIPTip;
    private MTextView mTvInfoSource;
    private SimpleDraweeView ivEnterpriseDataSource;
    private MTextView mTvAllBrands;
    private LinearLayout mLyCompanyContainer;

    private ConstraintLayout clGovInfo;
    private ZPUILinearLayout mLyGovCompanyInfoContainer;
    private BossAssistantFeedbackView feedbackView;

    protected ConstraintLayout clMaskParent;
    protected ImageView ivMaskLogo;
    protected MTextView tvMaskDesc;
    protected MTextView tvMaskTitle;
    protected ZPUIFrameLayout flMaskGoBtn;
    protected MTextView tvMaskCommit;

    private BossCompanyInfoAssistantResponse data;
    private long userId;

    private Context context;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.boss_fragment_assistant_company_nlp, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (!initArgumentsData()) return;

        initView(view);

        initData();

        initMaskLayout(view);
    }

    private boolean initArgumentsData() {
        Bundle arguments = getArguments();
        if (null != arguments && arguments.getSerializable(INTENT_DATA) instanceof BossCompanyInfoAssistantResponse) {
            data = (BossCompanyInfoAssistantResponse) arguments.getSerializable(INTENT_DATA);
            userId = arguments.getLong(USER_ID, 0);
            return true;
        } else {
            dismiss();
            return false;
        }
    }

    private int lastStatusBarColor = 0;
    private boolean flag = false;

    private void initStatusBarColor() {
        if (context instanceof BaseActivity && null != ((BaseActivity) context).getWindow()) {
            BaseActivity baseActivity = (BaseActivity) context;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!flag) {
                    flag = true;
                    lastStatusBarColor = baseActivity.getWindow().getStatusBarColor();
                }
            }
        }
    }

    private void initStatusBar() {
        if (context instanceof BaseActivity && null != ((BaseActivity) context).getWindow()) {
            BaseActivity baseActivity = (BaseActivity) context;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                baseActivity.getWindow().setStatusBarColor(ContextCompat.getColor(baseActivity, R.color.color_CC000000));
            }
        }
    }

    private void resetStatusBar() {
        if (context instanceof BaseActivity && null != ((BaseActivity) context).getWindow()) {
            BaseActivity baseActivity = (BaseActivity) context;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                int color = baseActivity.getWindow().getStatusBarColor();
                if (lastStatusBarColor != color) {
                    baseActivity.getWindow().setStatusBarColor(lastStatusBarColor);
                }
            }
        }
    }

    private void initView(View view) {
        AppTitleView titleView = view.findViewById(R.id.title_view);
        titleView.changeStyle(AppTitleView.SKIN_CUSTOM, Color.TRANSPARENT);
        titleView.setBackClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideFragment();
            }
        });

        clParent = view.findViewById(R.id.cl_parent);

        mTvAllBrands = view.findViewById(R.id.tv_all_brand);
        mTvInfoSource = view.findViewById(R.id.tv_info_source);
        ivEnterpriseDataSource = view.findViewById(R.id.iv_enterprise_data_source_logo);
        llVipParent = view.findViewById(R.id.ll_vip_tip_parent);
        mTvVIPTip = view.findViewById(R.id.tv_vip_tip);
        mIvVIPIcon = view.findViewById(R.id.iv_vip_icon);
        mTvRelated = view.findViewById(R.id.tv_related);
        mTvCompanyName = view.findViewById(R.id.tv_company_name);
        mTvCompanyNameTip = view.findViewById(R.id.tv_company_tip);
        mLyCompanyContainer = view.findViewById(R.id.ly_brand);

        clGovInfo = view.findViewById(R.id.cl_gov_info);
        mLyGovCompanyInfoContainer = view.findViewById(R.id.ly_gov_info_container);
        feedbackView = view.findViewById(R.id.ly_question);

        /*蒙层相关*/
        clMaskParent = view.findViewById(R.id.fl_mask_layout);
        ivMaskLogo = view.findViewById(R.id.iv_mask_logo);
        tvMaskTitle = view.findViewById(R.id.iv_mask_title);
        tvMaskDesc = view.findViewById(R.id.iv_mask_desc);
        flMaskGoBtn = view.findViewById(R.id.btn_go);
        tvMaskCommit = view.findViewById(R.id.tv_commit);
    }

    @Override
    public void onStart() {
        super.onStart();
        setBackground();
        initStatusBarColor();
        initStatusBar();
        if (null != getBehavior()) {
            getBehavior().setPeekHeight(0);
            setEnableDropDownDismiss(true);
            setDropDownDismissOffset(0.55f);
        }
    }

    private void setBackground() {
        if (null != getDialog() && null != getDialog().getWindow()) {
            Window window = getDialog().getWindow();
//            window.setBackgroundDrawableResource(R.color.color_com_info_dialog_bg);
            WindowManager.LayoutParams windowParams = window.getAttributes();
            windowParams.dimAmount = 0f;
            window.setAttributes(windowParams);
        }
    }

    private void initData() {
        // 设置品牌信息
        setBrandInfo();
        // 设置企查查数据
        setGovCompanyInfoView();
        // 设置反馈信息
        setFeedback();
    }

    private void setBrandInfo() {
        if (data == null) return;

        if (data.privilegeExplain != null) {
            llVipParent.setVisibility(View.VISIBLE);
            mIvVIPIcon.setVisibility(data.privilegeExplain.vipIcon ? View.VISIBLE : View.GONE);
            mTvVIPTip.setText(data.privilegeExplain.explain);
        } else {
            llVipParent.setVisibility(View.GONE);
        }

        mTvCompanyNameTip.setText(data.companyNameTip, View.GONE);
        mTvCompanyName.setText(data.comName, View.GONE);

        int size = LList.getCount(data.brandAssistantList);
        if (size > 0) {
            mTvRelated.setText(getString(R.string.boss_resume_nlp_company_detail_tip, size));
            if (size > 2) {
                mTvAllBrands.setVisibility(View.VISIBLE);
                mTvAllBrands.setText(getString(R.string.boss_resume_nlp_company_all_brand, size));
                mTvAllBrands.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mTvAllBrands.setVisibility(View.GONE);

                        for (int i = 2; i < size; i++) {
                            BossCompanyInfoAssistantResponse.BrandAssistant brandAssistant = LList.getElement(data.brandAssistantList, i);
                            BossAssistantCompanyView companyView = new BossAssistantCompanyView(context);
                            companyView.setData(brandAssistant);
                            mLyCompanyContainer.addView(companyView);
                        }
                    }
                });
            } else {
                mTvAllBrands.setVisibility(View.GONE);
            }
            mLyCompanyContainer.setVisibility(View.VISIBLE);
            mLyCompanyContainer.removeAllViews();
            for (int i = 0; i < Math.min(2, size); i++) {
                BossCompanyInfoAssistantResponse.BrandAssistant brandAssistant = LList.getElement(data.brandAssistantList, i);
                BossAssistantCompanyView companyView = new BossAssistantCompanyView(context);
                companyView.setData(brandAssistant);
                mLyCompanyContainer.addView(companyView);
            }
        } else {
            mLyCompanyContainer.setVisibility(View.GONE);
            mTvAllBrands.setVisibility(View.GONE);
        }
    }

    /**
     * 设置企查查数据
     */
    private void setGovCompanyInfoView() {
        if (data == null || data.companyAssistant == null) {
            clGovInfo.setVisibility(View.GONE);
        } else {
            BossCompanyInfoAssistantResponse.CompanyAssistant companyAssistant = data.companyAssistant;
            clGovInfo.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(companyAssistant.dataSource)) {
                mTvInfoSource.setVisibility(View.GONE);
            } else {
                mTvInfoSource.setVisibility(View.VISIBLE);
                mTvInfoSource.setText("数据来源：" + companyAssistant.dataSource);
            }
            if (!TextUtils.isEmpty(companyAssistant.dataSourceLogo)) {
                ivEnterpriseDataSource.setVisibility(View.VISIBLE);
                ivEnterpriseDataSource.setImageURI(companyAssistant.dataSourceLogo);
            } else {
                ivEnterpriseDataSource.setVisibility(View.GONE);
            }

            mLyGovCompanyInfoContainer.removeAllViews();
            if (!TextUtils.isEmpty(companyAssistant.startDate)) {
                BossAssistantCompanyInfoItemView itemView = new BossAssistantCompanyInfoItemView(context);
                itemView.setData("成立时间", companyAssistant.startDate, false);
                itemView.setMarginBottom(7);
                mLyGovCompanyInfoContainer.addView(itemView);
            }
            if (!TextUtils.isEmpty(companyAssistant.enterpriseType)) {
                BossAssistantCompanyInfoItemView itemView = new BossAssistantCompanyInfoItemView(context);
                itemView.setData("企业类型", companyAssistant.enterpriseType, false);
                itemView.setMarginBottom(7);
                mLyGovCompanyInfoContainer.addView(itemView);
            }
            if (!TextUtils.isEmpty(companyAssistant.regCapital)) {
                BossAssistantCompanyInfoItemView itemView = new BossAssistantCompanyInfoItemView(context);
                itemView.setData("注册资本", companyAssistant.regCapital, false);
                itemView.setMarginBottom(7);
                mLyGovCompanyInfoContainer.addView(itemView);
            }
            if (!TextUtils.isEmpty(companyAssistant.industry)) {
                BossAssistantCompanyInfoItemView itemView = new BossAssistantCompanyInfoItemView(context);
                itemView.setData("行业", companyAssistant.industry, false);
                itemView.setMarginBottom(7);
                mLyGovCompanyInfoContainer.addView(itemView);
            }
            if (!TextUtils.isEmpty(companyAssistant.operatingPeriod)) {
                BossAssistantCompanyInfoItemView itemView = new BossAssistantCompanyInfoItemView(context);
                itemView.setData("经营范围", companyAssistant.operatingPeriod, true);
                mLyGovCompanyInfoContainer.addView(itemView);
            }
        }

    }

    private void setFeedback() {
        if (data == null || data.isEmptyData()) {
            feedbackView.setVisibility(View.GONE);
        } else {
            feedbackView.setVisibility(View.VISIBLE);
            feedbackView.setData(data);
        }
    }


    private void initMaskLayout(View view) {
        if (null != data && data.isShowMask()) {

//            SimpleDraweeView ivBg = view.findViewById(R.id.iv_bg);
//            try {
//                Uri uri = StringUtil.getResouceUri(R.drawable.bg_diagram);
//                // 模糊背景大图
//                ImageRequest request = ImageRequestBuilder.newBuilderWithSource(uri)
//                        .setPostprocessor(new IterativeBoxBlurPostProcessor(16))
//                        .build();
//                DraweeController controller = Fresco.newDraweeControllerBuilder()
//                        .setImageRequest(request)
//                        .setOldController(ivBg.getController())
//                        .build();
//                ivBg.setController(controller);
//
//                ivBg.setImageURI(uri);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//
//            ivBg.setVisibility(View.VISIBLE);

            if (data.isEmptyData()) {
                clParent.setBackgroundColor(Color.TRANSPARENT);
            }

            tvMaskTitle.setText(data.trainUseInfo.limitTitle, View.GONE);

            tvMaskDesc.setText(data.trainUseInfo.limitTipText, View.GONE);

            flMaskGoBtn.setVisibility(View.GONE);
            if (!LText.isEmptyOrNull(data.trainUseInfo.trainUseLimitUrl)) {
                String btnText = "解锁使用限制";
                if (!LText.isEmptyOrNull(data.trainUseInfo.limitButtonText)) {
                    btnText = data.trainUseInfo.limitButtonText;
                }
                tvMaskCommit.setText(btnText);
                flMaskGoBtn.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        new ZPManager(context, data.trainUseInfo.trainUseLimitUrl).handler();
                    }
                });
                flMaskGoBtn.setVisibility(View.VISIBLE);
            }

            clMaskParent.setVisibility(View.VISIBLE);

            clMaskParent.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                }
            });
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        resetStatusBar();
        AnalyticsFactory factory = AnalyticsFactory.create();
        factory.action(AnalyticsAction.ACTION_CLOSE_COMPANY_MORE);
        factory.param("p", userId);
        if (getHelpfulSelection() > 0) {
            factory.param("p2", getHelpfulSelection());
        }
        factory.build();
    }


    private int getHelpfulSelection() {
        int selection = 0;
        if (feedbackView != null) {
            selection = feedbackView.getHelpfulSelection();
        }
        return selection;
    }

    @Override
    protected void onSlideListener(@NonNull View bottomSheet, float slideOffset) {
        super.onSlideListener(bottomSheet, slideOffset);
        if (slideOffset == 1) {
            initStatusBar();
        } else {
            resetStatusBar();
        }
    }
}
