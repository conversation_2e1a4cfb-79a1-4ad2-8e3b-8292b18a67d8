package com.hpbr.bosszhipin.module.boss.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.AbsListView;

import com.hpbr.bosszhipin.views.SearchMatchListView;

/**
 * Author: zhouyou
 * Date: 2023/3/6
 */
public class BottomSheetSearchMatchListView extends SearchMatchListView {

    public BottomSheetSearchMatchListView(Context context) {
        this(context, null);
    }

    public BottomSheetSearchMatchListView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomSheetSearchMatchListView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (canScrollVertically(this)) {
            getParent().requestDisallowInterceptTouchEvent(true);
        }
        return super.onInterceptTouchEvent(ev);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (canScrollVertically(this)) {
            getParent().requestDisallowInterceptTouchEvent(true);
        }
        return super.onTouchEvent(ev);
    }

    public boolean canScrollVertically(AbsListView view) {
        boolean canScroll = false;
        if (view != null && view.getChildCount() > 0) {
            boolean isOnTop = view.getFirstVisiblePosition() != 0 || view.getChildAt(0).getTop() != 0;
            boolean isAllItemsVisible = isOnTop && view.getLastVisiblePosition() == view.getChildCount();
            //noinspection ConstantConditions
            if (isOnTop || isAllItemsVisible) {
                canScroll = true;
            }
        }
        return canScroll;
    }
}
