package com.hpbr.bosszhipin.module.boss.fragment;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.helper.WebViewCrashFixer;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.webview.IWebView;
import com.hpbr.bosszhipin.module.webview.WebViewCommon;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.BaseBottomDialogFragment;
import com.hpbr.bosszhipin.views.BottomDialog;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.activity.LActivity;
import com.twl.utils.ActivityUtils;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class WebViewBottomDialogFragment extends BaseBottomDialogFragment implements IWebView {

    public static final String DATA_TITLE = "data_title";
    public static final String DATA_URL = "data_url";
    public static final String DATA_HEIGHT_RATIO = "data_height_Ratio";
    public static final String DATA_DISABLE_ANIM = "data_disable_anim";

    public static WebViewBottomDialogFragment getInstance(String title, String url, float heightRatio, boolean disableAnim) {
        WebViewBottomDialogFragment fragment = new WebViewBottomDialogFragment();
        Bundle bundle = new Bundle();
        bundle.putString(DATA_TITLE, title);
        bundle.putString(DATA_URL, url);
        bundle.putFloat(DATA_HEIGHT_RATIO, heightRatio);
        bundle.putBoolean(DATA_DISABLE_ANIM, disableAnim);
        fragment.setArguments(bundle);
        return fragment;
    }

    public static void showFragment(FragmentManager manager, WebViewBottomDialogFragment fragment) {
        if (null != fragment) {
            fragment.show(manager, WebViewBottomDialogFragment.class.getSimpleName());
        }
    }

    protected ZPUIConstraintLayout mClParent;
    protected MTextView mTvTitle;
    protected ImageView mMClose;
    protected WebView webView;
    protected ProgressBar progressBar;

    protected LActivity lActivity;
    protected WebViewCommon common;

    public String title;
    public String url;
    public float heightRatio = DEFAULT_HEIGHT_RATIO;
    public boolean disableAnim = false;

    public static final float DEFAULT_HEIGHT_RATIO = 0.82f;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (context instanceof LActivity) {
            lActivity = (LActivity) context;
        }
    }

    @Override
    public BottomDialog.Builder buildDialog(@NonNull BottomDialog.Builder builder) {
        BottomDialog.Builder builder1 = super.buildDialog(builder);
        if (disableAnim) {
            builder1.setAnimationStyle(0);
        }
        return builder1.setDimAmount(0.7f)
                .setContainerHeight((int) (ZPUIDisplayHelper.getScreenHeight(lActivity) * heightRatio));
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        WebViewCrashFixer.dotWebViewPageOpen();
        Bundle arguments = getArguments();
        if (null != arguments) {
            title = arguments.getString(DATA_TITLE);
            url = arguments.getString(DATA_URL);
            heightRatio = arguments.getFloat(DATA_HEIGHT_RATIO, DEFAULT_HEIGHT_RATIO);
            disableAnim = arguments.getBoolean(DATA_DISABLE_ANIM, false);
        }

    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.bottom_sheet_webview_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        common = new WebViewCommon();

        mClParent = view.findViewById(R.id.cl_parent);
        mTvTitle = view.findViewById(R.id.tv_title);
        mMClose = view.findViewById(R.id.mClose);
        webView = view.findViewById(R.id.webView);
        progressBar = view.findViewById(R.id.progress);

        mMClose.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismiss();
            }
        });

        mTvTitle.setText(title, View.GONE);

        startPage(url);
    }

    /**
     * 开启页面
     *
     * @param url
     */
    public void startPage(String url) {
        if (!TextUtils.isEmpty(url) && ActivityUtils.isValid(lActivity)) {
            Intent intent = new Intent();
            intent.putExtra(Constants.DATA_URL, url);
            common.create(lActivity, this, intent);
        }
    }

    @Override
    public void initContentView() {

    }

    @Override
    public AppTitleView getTitleView() {
        return null;
    }

    @Override
    public ProgressBar getProgressBar() {
        return progressBar;
    }

    @Override
    public LinearLayout getParentView() {
        return null;
    }

    @Override
    public WebView getWebView() {
        return webView;
    }

    @Override
    public void refreshBackStatus() {

    }

    @Override
    public void onWebviewLoadingError() {

    }

    @Override
    public void onResume() {
        super.onResume();
    }

    public void resume() {
        if (common != null) {
            common.resume();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (common != null) {
            common.destroy();
        }
    }
}
