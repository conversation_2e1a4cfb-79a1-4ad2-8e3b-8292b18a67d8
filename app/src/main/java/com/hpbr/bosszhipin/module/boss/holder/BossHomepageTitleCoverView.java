package com.hpbr.bosszhipin.module.boss.holder;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.postprocessors.IterativeBoxBlurPostProcessor;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.boss.entity.HeaderItemModel;
import com.hpbr.bosszhipin.module.boss.entity.server.Labels;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;
import java.util.List;

import static com.hpbr.bosszhipin.utils.StringUtil.connectTextWithChar;

/**
 * Author: ZhouYou
 * Date: 2018/5/19.
 */
public class BossHomepageTitleCoverView extends FrameLayout implements View.OnClickListener {
    private Context context;

    public BossHomepageTitleCoverView(@NonNull Context context) {
        this(context, null);
    }

    public BossHomepageTitleCoverView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossHomepageTitleCoverView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        init();
    }

    private ImageView ivEditIcon;
    private MTextView bossNameText;
    private MTextView bossTitleText;
    private SimpleDraweeView bossProfileImage;
    private SimpleDraweeView bossProfileBackgroundImage;
    private FlexboxLayout labelsLayout;

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.item_boss_header, this);
        ivEditIcon = (ImageView) view.findViewById(R.id.iv_edit_icon);
        ivEditIcon.setOnClickListener(this);
        bossNameText = (MTextView) view.findViewById(R.id.title_tv);
        bossNameText.setOnClickListener(this);
        bossTitleText = (MTextView) view.findViewById(R.id.bossTitleText);
        bossProfileImage = (SimpleDraweeView) view.findViewById(R.id.bossProfileImage);

        bossProfileBackgroundImage = (SimpleDraweeView) view.findViewById(R.id.bossProfileBackgroundImage);
        labelsLayout = (FlexboxLayout) view.findViewById(R.id.labelsLayout);
    }

    private OnBossHomepageCoverActionClickListener listener;

    public void setOnBossHomepageCoverActionClickListener(OnBossHomepageCoverActionClickListener listener) {
        this.listener = listener;
    }

    public void setData(HeaderItemModel model) {
        bossNameText.setText(model.name);
        bossNameText.setAlpha(0);
        bossNameText.setMaxWidth(getTitleDisplayMaxWidth());
        bossTitleText.setText(connectTextWithChar(" · ", model.brandName, model.title));
        ivEditIcon.setVisibility(VISIBLE);

        String tiny = model.tiny;
        bossProfileImage.setImageURI(tiny);

        final String largeUrl = model.large;
        if (!TextUtils.isEmpty(largeUrl)) {
            // 模糊背景大图
            ImageRequest request = ImageRequestBuilder.newBuilderWithSource(Uri.parse(largeUrl))
                    .setPostprocessor(new IterativeBoxBlurPostProcessor(16))
                    .build();
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(request)
                    .setOldController(bossProfileBackgroundImage.getController())
                    .build();
            bossProfileBackgroundImage.setController(controller);
            // 调暗背景大图
            bossProfileBackgroundImage.setColorFilter(0x40000000, PorterDuff.Mode.DARKEN);
        }

        bossProfileImage.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onAvatarClick(largeUrl, bossProfileImage);
                }
            }
        });

        labelsLayout.removeAllViews();
        Labels labels = model.labels;
        if (labels != null) {
            final List<String> labelList = new ArrayList<>();
            List<String> characters = labels.character;
            if (LList.getCount(characters) > 0) {
                labelList.addAll(characters);
            }
            List<String> hobbies = labels.hobby;
            if (LList.getCount(hobbies) > 0) {
                labelList.addAll(hobbies);
            }
            for (String label : labelList) {
                MTextView bossLabel = (MTextView) LayoutInflater.from(context).inflate(R.layout.item_my_personality_tag, labelsLayout, false);
                bossLabel.setText(label);
                labelsLayout.addView(bossLabel);
            }
        }
        labelsLayout.addView(newAddTag(labels));
    }

    private MTextView newAddTag(@Nullable final Labels labels) {
        MTextView addTag = (MTextView) LayoutInflater.from(context).inflate(R.layout.item_my_personality_tag, labelsLayout, false);
        addTag.setText("添加标签");
        addTag.setCompoundDrawablePadding(Scale.dip2px(getContext(), 3)); // 3dp
        addTag.setCompoundDrawablesWithIntrinsicBounds(R.drawable.wplus, 0, 0, 0);
        addTag.setTextColor(Color.parseColor("#AAEEEEEE"));
        addTag.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转添加标签页面，携带已选标签
                if (listener != null) {
                    listener.onTagAdd(labels);
                }
            }
        });
        return addTag;
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_edit_icon || i == R.id.title_tv) {
            if (listener != null) {
                listener.onEditBossInfo();
            }

        } else {
        }
    }

    public int getTitleStartMargin() {
        return bossNameText.getLeft();
    }

    public int getTitleBottomMargin() {
        return (int) (getMeasuredHeight() - bossNameText.getBottom() + bossNameText.getPaint().descent());
    }

    public int getTitleMaxLengthToRightEnd() {
        return App.get().getDisplayWidth() - getTitleDisplayMaxWidth() - bossNameText.getLeft();
    }

    private int getTitleDisplayMaxWidth() {
        return App.get().getDisplayWidth() - bossProfileImage.getWidth() - ivEditIcon.getWidth() - Scale.dip2px(context, 60);
    }

    public void setAlphaChange(float alpha) {
        ivEditIcon.setAlpha(alpha);
        bossTitleText.setAlpha(alpha);
        labelsLayout.setAlpha(alpha);
        bossProfileImage.setAlpha(alpha);
    }

    public interface OnBossHomepageCoverActionClickListener {
        void onAvatarClick(String avatarLargeUrl, View view);

        void onTagAdd(Labels labels);

        void onEditBossInfo();
    }
}
