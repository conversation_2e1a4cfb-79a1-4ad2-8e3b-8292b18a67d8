package com.hpbr.bosszhipin.module.boss.holder;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.hpbr.bosszhipin.app.R;

/**
 * Author: ZhouYou
 * Date: 2018/5/19.
 */
public class BossHomepageTitleCoverView2 extends FrameLayout {

    private Uri cachedUri;

    public BossHomepageTitleCoverView2(@NonNull Context context) {
        this(context, null);
    }

    public BossHomepageTitleCoverView2(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossHomepageTitleCoverView2(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private SimpleDraweeView bossProfileBackgroundImage;
    private View cover;

    private void init() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.item_boss_header_image, this);
        bossProfileBackgroundImage = view.findViewById(R.id.bossProfileBackgroundImage);
        cover = view.findViewById(R.id.cover);
    }

    public void setCoverVisibility(int visibility) {
        cover.setVisibility(visibility);
    }

    public void setData(String largeUrl) {
        bossProfileBackgroundImage.setImageURI(largeUrl);
    }

    /**
     * @param largeUrl 形象图
     * @param gender   0 女 1 男
     */
    public void setData(String largeUrl, int gender) {
        if (!TextUtils.isEmpty(largeUrl)) {
            this.cachedUri = Uri.parse(largeUrl);
        }

        GenericDraweeHierarchy hierarchy =
                GenericDraweeHierarchyBuilder.newInstance(getResources())
//                            .setActualImageColorFilter(colorFilter)
//                            .setActualImageFocusPoint(focusPoint)
//                            .setActualImageScaleType(scaleType)
//                            .setBackground(background)
//                            .setDesiredAspectRatio(desiredAspectRatio)
//                            .setFadeDuration(fadeDuration)
//                            .setFailureImage(failureImage)
//                            .setFailureImageScaleType(scaleType)
//                            .setOverlays(overlays)
                        .setPlaceholderImage(gender == 0 ? R.drawable.default_profile_woman : R.drawable.default_profile_man)
//                            .setPlaceholderImageScaleType(scaleType)
//                            .setPressedStateOverlay(overlay)
//                            .setProgressBarImage(progressBarImage)
//                            .setProgressBarImageScaleType(scaleType)
//                            .setRetryImage(retryImage)
//                            .setRetryImageScaleType(scaleType)
//                            .setRoundingParams(roundingParams)
                        .build();
        bossProfileBackgroundImage.setHierarchy(hierarchy);
        bossProfileBackgroundImage.setImageURI(largeUrl);
    }

    public void clearHeaderCache() {
        ImagePipeline imagePipeline = Fresco.getImagePipeline();
        Uri uri = cachedUri;
        if (uri == null) return;

        imagePipeline.evictFromMemoryCache(uri);
        imagePipeline.evictFromDiskCache(uri);

        // combines above two lines
        imagePipeline.evictFromCache(uri);
    }

}
