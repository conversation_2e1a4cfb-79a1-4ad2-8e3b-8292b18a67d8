package com.hpbr.bosszhipin.module.boss.holder;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.twl.ui.flexbox.StringTagAdapter;
import com.twl.ui.flexbox.callbacks.OnMultiItemSelectListener;
import com.twl.ui.flexbox.widget.TagFlowLayout;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/5/15.
 */
public class BossPersonalityView extends FrameLayout {
    private Context context;

    private int count;

    public BossPersonalityView(@NonNull Context context) {
        this(context, null);
    }

    public BossPersonalityView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossPersonalityView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        init();
    }

    private MTextView tvCategoryTitle;
    private TagFlowLayout flowLayout;
    private MTextView tvCreateTag;

    private OnBossPersonalityCustomListener listener;

    public void setOnBossPersonalityCustomListener(OnBossPersonalityCustomListener listener) {
        this.listener = listener;
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.item_personality_tag, this);
        tvCategoryTitle = (MTextView) view.findViewById(R.id.tv_category_title);
        flowLayout = (TagFlowLayout) view.findViewById(R.id.flow_layout);
        tvCreateTag = (MTextView) view.findViewById(R.id.tv_create_tag);
    }

    public void setData(String title, List<String> sourceStrings, List<String> selectStrings) {
        if (LList.isEmpty(sourceStrings)) {
            setVisibility(GONE);
            return;
        }
        setVisibility(VISIBLE);
        tvCategoryTitle.setText(title);
        refreshAdapter(sourceStrings, selectStrings);

        tvCreateTag.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                List<String> selectItems = getCurrentSelectItems();
                if (count != 0&&LList.getCount(selectItems) >= flowLayout.getMaxSelection()) {
                    if (count != 0)
                        T.ss("最多" + flowLayout.getMaxSelection() + "个标签");
                    else
                        T.ss("该问题最多" + flowLayout.getMaxSelection() + "个标签");
                } else {
                    if (listener != null) {
                        listener.onCustom();
                    }
                }
            }
        });
    }

    private void refreshAdapter(List<String> sourceStrings, final List<String> selectStrings) {
        StringTagAdapter adapter = new StringTagAdapter(context, sourceStrings, selectStrings);
        flowLayout.setAdapter(adapter);
        adapter.setOnMultiItemSelectListener(new OnMultiItemSelectListener<String>() {
            @Override
            public void onItemSelected(List<String> selectedList) {

            }

            @Override
            public void onMaxItemSelectedToast(int maxCount) {
                if (count != 0)
                    T.ss("最多" + maxCount + "个标签");
                else
                    T.ss("该问题最多" + maxCount + "个标签");
            }
        });
    }

    public List<String> getCurrentSelectItems() {
        if (flowLayout.getAdapter() != null && flowLayout.getAdapter() instanceof StringTagAdapter) {
            StringTagAdapter adapter = (StringTagAdapter) flowLayout.getAdapter();
            return adapter.getSelectedList();
        }
        return new ArrayList<>();
    }

    public interface OnBossPersonalityCustomListener {
        void onCustom();
    }

    public void setMaxSelection(int count) {
        this.count = count;
        flowLayout.getAdapter().setMaxSelection(count);
        flowLayout.setMaxSelection(count);
    }

}
