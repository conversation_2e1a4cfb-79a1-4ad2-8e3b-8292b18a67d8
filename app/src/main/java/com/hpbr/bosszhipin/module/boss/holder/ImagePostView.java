package com.hpbr.bosszhipin.module.boss.holder;

import android.content.Context;
import android.net.Uri;
import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.common.ResizeOptions;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.boss.contract.MediaPostContract;
import com.hpbr.bosszhipin.module.boss.contract.MediaProvider;
import com.hpbr.bosszhipin.utils.UploadFileUtil;
import com.hpbr.bosszhipin.views.decoration.GridSpacingItemDecoration;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.MultiFileUploadResponse;
import com.hpbr.bosszhipin.module.my.entity.ImageUrlBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

import top.zibin.luban.Luban;

import static com.hpbr.bosszhipin.module.boss.contract.Constants.POST_MEDIA_TYPE_IMAGE;

/**
 * Created by zhangxiangdong on 2018/8/6 14:50.
 */
public class ImagePostView extends FrameLayout implements MediaProvider {

    public static final int MAX_NUM_OF_IMAGES = 9;
    private static final String LOG_TAG = "ImagePostView";

    @Nullable
    private MediaPostContract.ImagePost contract;
    @Nullable
    private Adapter adapter;

    public ImagePostView(@NonNull Context context) {
        this(context, null);
    }

    public ImagePostView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ImagePostView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(getContext()).inflate(R.layout.layout_post_image, this);
        initViews();
    }

    private void initViews() {
        RecyclerView postImageRecyclerView = findViewById(R.id.postImageRecyclerView);
        postImageRecyclerView.setNestedScrollingEnabled(false);
        postImageRecyclerView.addItemDecoration(new GridSpacingItemDecoration(3, Scale.dip2px(getContext(), 6), false));
        postImageRecyclerView.setAdapter(adapter = new Adapter());
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (getContext() instanceof MediaPostContract.ImagePost) {
            this.contract = (MediaPostContract.ImagePost) getContext();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        contract = null;
    }

    public void addSelectedImages(@Nullable ArrayList<String> images) {
        if (adapter != null) {
            adapter.addImages(images);
            adapter.notifyDataSetChanged();
        }
    }

    public void setSelectedImages(@Nullable ArrayList<String> images) {
        if (adapter != null) {
            adapter.setImages(images);
            adapter.notifyDataSetChanged();
        }
    }

    /**
     * 还可以选择几张图片
     *
     * @return 剩余可选图片数量
     */
    public int getRemainingImagesCount() {
        return MAX_NUM_OF_IMAGES - (adapter != null ? adapter.getSelectedImagesCount() : 0);
    }

    public int getSelectedImagesCount() {
        return adapter != null ? adapter.getSelectedImagesCount() : 0;
    }

    @Override
    public int getMediaType() {
        return POST_MEDIA_TYPE_IMAGE;
    }

    @Override
    public void release() {
        if (getParent() == null) return;
        if (adapter != null) {
            adapter.clear();
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onPreparePost(@NonNull PostCallback callback) {
        List<String> selectedImages = adapter != null ? adapter.getSelectedImages() : null;
        if (LList.getCount(selectedImages) == 0) {
            L.d(LOG_TAG, "未选择图片，请检查原因");
            return;
        }

        // Step1 先压缩图片
        if (contract == null) return;
        if (selectedImages != null) {
            new CompressImageTask(selectedImages, callback, contract).execute();
        }
    }

    private static class CompressImageTask extends AsyncTask<Void, Void, List<Uri>> {

        private final List<String> imagePaths;
        private final PostCallback callback;
        private final WeakReference<MediaPostContract.ImagePost> contractReference;

        CompressImageTask(@NonNull List<String> imagePaths, @NonNull PostCallback callback, @NonNull MediaPostContract.ImagePost contract) {
            this.imagePaths = imagePaths;
            this.callback = callback;
            this.contractReference = new WeakReference<>(contract);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            callback.onStartPost();
        }

        @Nullable
        @Override
        protected final List<Uri> doInBackground(Void... lists) {
            try {
                return Luban.with(App.get()).load(imagePaths).getUrls(); // 压缩图片
            } catch (IOException e) {
                L.e(e.getMessage());
            }
            return null;
        }

        @Override
        protected void onPostExecute(@Nullable List<Uri> compressedImageFiles) {
            super.onPostExecute(compressedImageFiles);
            if (LList.getCount(compressedImageFiles) == 0) {
                publishFailed();
                callback.onStopPost();
                return;
            }

            MediaPostContract.ImagePost contract = contractReference.get();
            if (contract == null) return;

            // Step 2 上传图片
            UploadFileUtil.uploadMultiUri(UploadFileUtil.PROFILE_FEED, compressedImageFiles, new ApiRequestCallback<MultiFileUploadResponse>() {
                @Override
                public void onSuccess(ApiData<MultiFileUploadResponse> data) {
                    callback.onStopPost();
                    List<ImageUrlBean> urlList = data.resp.urlList;
                    if (LList.getCount(urlList) == 0) {
                        publishFailed();
                        return;
                    }

                    StringBuilder rawImageBuilder = new StringBuilder();
                    StringBuilder thumbnailBuilder = new StringBuilder();
                    final int count = urlList.size();
                    for (int i = 0; i < count; i++) {
                        ImageUrlBean bean = urlList.get(i);
                        if (bean == null) continue;

                        String url = bean.url;
                        String tinyUrl = bean.tinyUrl;

                        if (TextUtils.isEmpty(url) && TextUtils.isEmpty(tinyUrl))
                            continue; // 两个url都没有，跳过

                        if (TextUtils.isEmpty(url) && !TextUtils.isEmpty(tinyUrl)) { // 错误处理，确保两个url都有值
                            url = tinyUrl;
                        } else if (TextUtils.isEmpty(tinyUrl) && !TextUtils.isEmpty(url)) {
                            tinyUrl = url;
                        }

                        rawImageBuilder.append(url);
                        thumbnailBuilder.append(tinyUrl);
                        if (i < count - 1) { // 使用逗号分隔所有图片地址，跳过末尾逗号
                            rawImageBuilder.append(",");
                            thumbnailBuilder.append(",");
                        }
                    }

                    // Step 3 发帖
                    contract.onPostImage(rawImageBuilder.toString(), thumbnailBuilder.toString());
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    callback.onStopPost();
                    T.ss(reason.getErrReason());
                }
            });
        }
    }

    private static void publishFailed() {
        T.ss("发布失败，请重试");
    }

    @SuppressWarnings("WeakerAccess")
    class Adapter extends RecyclerView.Adapter<Adapter.VH> {

        final ArrayList<String> imageList = new ArrayList<>();

        public Adapter() {
            imageList.add(null); // 添加“添加”按钮
        }

        /**
         * 添加图片（用户通过添加按钮或选择图片入口添加图片）
         *
         * @param images 用户选择的图片
         */
        public void addImages(@Nullable ArrayList<String> images) {
            if (images != null && images.size() > 0) {
                imageList.remove(null);
                imageList.addAll(images);
                if (imageList.size() < MAX_NUM_OF_IMAGES) {
                    imageList.add(null); // 追加“添加”按钮
                }
            }
        }

        /**
         * 用户进入图片预览页面返回（可能删除了某些图片），刷新已选图片
         *
         * @param images 刷新后的图片
         */
        public void setImages(@Nullable ArrayList<String> images) {
            if (images != null && images.size() > 0) {
                imageList.clear();
                imageList.addAll(images);
                if (imageList.size() < MAX_NUM_OF_IMAGES) {
                    imageList.add(null); // 追加“添加”按钮
                }
            } else {
                clear();
            }
        }

        public void clear() {
            imageList.clear();
            imageList.add(null);
        }

        @NonNull
        @Override
        public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new VH(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_post_image, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull VH holder, int position) {
            String imagePathUri = imageList.get(position);
            SimpleDraweeView postImage = holder.postImage;
            if (imagePathUri != null) {
                ImageRequest request = ImageRequestBuilder.newBuilderWithSource(Uri.parse(imagePathUri))
                        .setResizeOptions(new ResizeOptions(256, 256))
                        .build();
                postImage.setController(
                        Fresco.newDraweeControllerBuilder()
                                .setOldController(postImage.getController())
                                .setImageRequest(request)
                                .build());
            } else {
                // 更多按钮
                postImage.setImageResource(R.mipmap.ic_images_add);
            }
        }

        /**
         * 获取已选图片数量
         *
         * @return 已经选择的图片数量
         */
        public int getSelectedImagesCount() {
            return getItemCount() - (imageList.contains(null) ? 1 : 0);
        }

        /**
         * 返回所有已选图片的本地地址
         *
         * @return 图片本地地址集合
         */
        @NonNull
        public List<String> getSelectedImages() {
            final List<String> selectedImages = new ArrayList<>();
            for (String imagePath : imageList) {
                if (imagePath == null) continue;
                selectedImages.add(imagePath);
            }
            return selectedImages;
        }

        @Override
        public int getItemCount() {
            return imageList.size();
        }

        @Nullable
        private String getItem(int position) {
            return imageList.get(position);
        }

        class VH extends RecyclerView.ViewHolder {

            final SimpleDraweeView postImage;

            public VH(View itemView) {
                super(itemView);
                postImage = itemView.findViewById(R.id.postImage);
                postImage.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (contract != null) {
                            if (getItem(getAdapterPosition()) == null) { // 用户点击“添加”按钮
                                // 跳转图库，选择图片
                                int maxCount = MAX_NUM_OF_IMAGES - (imageList.size() - 1); // 9宫格，除了“添加”按钮和已选图片外，用户还可以选择的图片张数
                                if (maxCount > 0) {
                                    contract.onSelectImages(maxCount);
                                }
                            } else {
                                ArrayList<String> validImageList = new ArrayList<>();
                                for (String image : imageList) {
                                    if (image == null) continue;
                                    validImageList.add(image);
                                }
                                contract.onPreviewImage(validImageList, getAdapterPosition());
                            }
                        }
                    }
                });
            }

        }

    }

}
