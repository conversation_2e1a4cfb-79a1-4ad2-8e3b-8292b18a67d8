package com.hpbr.bosszhipin.module.boss.homepage;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.get.export.GetConsts;
import com.hpbr.bosszhipin.get.export.GetRouter;
import com.hpbr.bosszhipin.get.net.bean.GetHomePageHunterGrayResponse;
import com.hpbr.bosszhipin.module_boss_export.BossUrlConfig;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.bean.ServerBossEduBean;
import net.bosszhipin.api.bean.ServerBossWorkBean;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.Serializable;
import java.util.List;

public class BossHomeManager {

    public static String ACTION_REFRESH_BOSS_INFO = "action_refresh_boss_base_info";
    /**
     * 去了H5 页面，操作了数据 需要刷新 个人主页，和其他的刷新广播比，这个只是在主页记录个标记，等resume的时候刷新
     * 避免在H5里操作 会刷新多次
     */
    public static String ACTION_NEED_REFRESH_BOSS_INFO = "action_need_refresh_boss_base_info";
    public static String ACTION_REFRESH_BOSS_INFO_TAG = "action_refresh_boss_base_info_tag";
    public static String ACTION_REFRESH_AVATAR_STICKER = "action_refresh_avatar_sticker";
    public static String DATA_AVATAR_STICKER_URL = "data_refresh_avatar_url";

    /**
     * 刷新基本信息页面广播
     */

    public static void sendBossInfoShowDialogReceiver(boolean change) {
        Intent intent = new Intent(ACTION_REFRESH_BOSS_INFO);
        intent.putExtra(ACTION_REFRESH_BOSS_INFO_TAG, change);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);

    }

    public static void sendBossInfoReceiver() {
        Intent intent = new Intent(ACTION_REFRESH_BOSS_INFO);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    public static void sendNeedRefreshBossInfoReceiver() {
        Intent intent = new Intent(ACTION_NEED_REFRESH_BOSS_INFO);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * 刷新头型挂件
     */
    public static void sendRefreshAvatarStickerReceiver(String url) {
        Intent intent = new Intent(ACTION_REFRESH_AVATAR_STICKER);
        intent.putExtra(DATA_AVATAR_STICKER_URL, url);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * 请不要再 新增其他到boss主页的跳转方法，除非 增加了变动比较大的打点。  如果有更多打点参数 请在{@link GetRouter.BHomePageParamsBean}  里加。
     * 相对于直接调用， 这里多了打点处理
     *
     * @param context 如果不需要埋 ACTION_DETAIL_PROFILE。P5 不传 即可.
     *                如果不需要埋 ACTION_GET_HOMEPAGE_SHOW. pageType，PageTypeString 不传即可     嫌这里麻烦，也可以这里不传，然后在调用处单独埋点，
     */
    public static void jump(Context context, GetRouter.BHomePageParamsBean param) {
        if (param == null) return;
//        根据pagetype确定来源页面
        String from = "";
        if (!LText.empty(param.getPageTypeString())) {
            from = param.getPageTypeString();
        } else {
            from = SwitchPageType2TextFrom.switchType(param.getPageType()); // 如过pagetype 为空就埋转换后的 默认值 customPage
        }
        String p8 = "";
        if (param.getCanAsk() == 1 || param.getCanAsk() == 0) {
            p8 = String.valueOf(param.getCanAsk());
        }
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_GET_HOMEPAGE_SHOW)
                .param("p", from)
                .param("p2", getParamForTab(param.getTabType()))
                .param("p5", "1")
                .param("p6", param.getSecurityId())
                .param("p8", p8)
                .param("p9", param.getJobId())
                .debug()
                .build();

        /*1206.500 猎头新主页改版，增加灰度*/
        SimpleApiRequest.GET(BossUrlConfig.URL_GET_HUNTER_HOME_PAGE_GRAY)
                .addParam("securityId", param.getSecurityId())
                .addParam("encryptUserId", param.getEncryptUserId())
                .setRequestCallback(new SimpleCommonApiRequestCallback<GetHomePageHunterGrayResponse>() {
                    @Override
                    public void onSuccess(ApiData<GetHomePageHunterGrayResponse> data) {
                        super.onSuccess(data);
                        boolean isRunHunterHomePageGray = null != data && null != data.resp && data.resp.gray && data.resp.hunterIdentity;
                        GetRouter.openBossHomePage(context, param
                                .setShowAskBtn(param.getPageType() == GetConsts.PageType.PAGE_TYPE_CHANGE_CAREERS), isRunHunterHomePageGray);
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        GetRouter.openBossHomePage(context, param
                                .setShowAskBtn(param.getPageType() == GetConsts.PageType.PAGE_TYPE_CHANGE_CAREERS), false);
                    }

                }).execute();

    }

    private static String getParamForTab(String tabType) {
        if (TextUtils.isEmpty(tabType)) return "info";
        switch (tabType) {
            case GetConsts.BOSS_HOMEPAGE_INDEX_PERSON_INFO:
                return "info";
            case GetConsts.BOSS_HOMEPAGE_INDEX_POSITION:
                return "job";
            case GetConsts.BOSS_HOMEPAGE_INDEX_DYNAMIC:
                return "dynamic";
            case GetConsts.BOSS_HOMEPAGE_INDEX_WORK_ENVIROMENT:
                return "environment";
//            case 5:  问答812 被删除了
//                return "environment";

        }
        return "info";
    }

    /**
     * 服务端还在使用int 来定位，转换成String
     * 1职位 2 个人信息  3 动态 4 问答  5工作环境  目前服务端返回 目前服务端返回  目前服务端返回
     *
     * @param tabIndex
     * @return
     */
    public static String getStringTab(int tabIndex) {
        switch (tabIndex) {

            case 1:
                return GetConsts.BOSS_HOMEPAGE_INDEX_POSITION;
            case 2:
                return GetConsts.BOSS_HOMEPAGE_INDEX_PERSON_INFO;
            case 3:// 动态问答 合并了。都叫动态了，所以 3 4 跳同一个
            case 4:
                return GetConsts.BOSS_HOMEPAGE_INDEX_DYNAMIC;
            case 5:
                return GetConsts.BOSS_HOMEPAGE_INDEX_WORK_ENVIROMENT;
            default:
                return GetConsts.BOSS_HOMEPAGE_INDEX_PERSON_INFO;
        }
    }

    /**
     * 这个应该是老代码，就不优化了 fromCircle source 这俩参数在页面里已经用不到了
     *  ========已经废弃========
     *
     * @param context
     * @param bossId
     * @param fromCircle
     * @param source
     */
    @Deprecated
    public static void jump(Context context, long bossId, boolean fromCircle, int source /* 7: 校友圈，8: 公司圈 */) {
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_GET_HOMEPAGE_SHOW)
                .debug()
                .param("p", GetConsts.TextFrom.TEXT_FROM_CUSTOM_PAGE)
                .build();
        GetRouter.openBossHomePage(context, bossId, fromCircle, source);
    }

    //boss个人主页 tab5 工作环境
    public static final int BOSS_HOME_TAB_ENVIRONMENT = 5;


    /**
     * 完善信息引导
     */
    public static boolean isBossCompleteGuideItemFirstShow() {
        return SP.get().getBoolean(Constants.PREFIX + "_SHOW_BOSS_HOME_COMPLETE_GUIDE_ITEM" + "_" + UserManager.getUID(), true);
    }

    public static void setBossCompleteGuideItemFirstShow() {
        SP.get().putBoolean(Constants.PREFIX + "_SHOW_BOSS_HOME_COMPLETE_GUIDE_ITEM" + "_" + UserManager.getUID(), false);
    }

    /**
     * 工作经历
     */
    public static String WORK_EXP_LIST_ADD_KEY = "WORK_EXP_LIST_ADD";

    public static String WORK_EXP_IS_DELETE_KEY = "WORK_EXP_IS_DELETE_KEY";
    public static String WORK_EXP_SINGLE_KEY = "WORK_EXP_SINGLE_KEY";

    /**
     * 教育经历
     */
    public static String EDU_EXP_LIST_ADD_KEY = "EDU_EXP_LIST_ADD_KEY";

    public static String EDU_IS_DELETE_KEY = "EDU_IS_DELETE_KEY";
    public static String EDU_SINGLE_KEY = "EDU_SINGLE_KEY";

    public static String INTEREST_REFRESH_KEY = "interest_refresh_key";

    public static String SOCIAL_REFRESH_KEY = "social_refresh_key";

    /**
     * BOSS添加 多个工作经历
     * @param context
     * @param list
     */
    public static void sendBossWorkList(Context context, List<ServerBossWorkBean> list) {
        if (null == context) return;
        Intent intent = new Intent();
        intent.setAction(GetConsts.GET_GEEK_HOMEPAGE_ADD_WORK_LIST);
        intent.putExtra(WORK_EXP_LIST_ADD_KEY, (Serializable) list);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }
    /**
     * BOSS添加 删除  单个工作经历
     * @param context
     * @param
     */
    public static void sendBossSingleWork(Context context, ServerBossWorkBean workExpBean, boolean delete) {
        if (null == context) return;
        Intent intent = new Intent();
        intent.setAction(GetConsts.GET_GEEK_HOMEPAGE_ADD_DELETE_WORK);
        intent.putExtra(WORK_EXP_SINGLE_KEY, workExpBean);
        intent.putExtra(WORK_EXP_IS_DELETE_KEY, delete);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * BOSS添加 多个教育经历
     * @param context
     * @param list
     */
    public static void sendBossEduList(Context context, List<ServerBossEduBean> list) {
        if (null == context) return;
        Intent intent = new Intent();
        intent.setAction(GetConsts.GET_GEEK_HOMEPAGE_ADD_EDU_LIST);
        intent.putExtra(EDU_EXP_LIST_ADD_KEY, (Serializable) list);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * BOSS添加 删除  单个教育经历
     * @param context
     * @param
     */
    public static void sendBossSingleEdu(Context context, ServerBossEduBean eduExpBean, boolean delete) {
        if (null == context) return;
        Intent intent = new Intent();
        intent.setAction(GetConsts.GET_GEEK_HOMEPAGE_ADD_DELETE_EDU);
        intent.putExtra(EDU_SINGLE_KEY, eduExpBean);
        intent.putExtra(EDU_IS_DELETE_KEY, delete);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }


}
