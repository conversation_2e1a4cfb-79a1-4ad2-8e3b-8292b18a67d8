package com.hpbr.bosszhipin.module.boss.homepage;

import android.content.Context;
import android.content.Intent;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.get.export.GetConsts;
import com.hpbr.bosszhipin.get.export.GetRouter;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import net.bosszhipin.api.GetBossOpenGeekHomePageRequest;
import net.bosszhipin.api.GetBossOpenGeekHomePageResponse;
import net.bosszhipin.base.ApiRequestCallback;

import static com.hpbr.bosszhipin.get.export.GetConsts.GEEK_HOMEPAGE_INDEX_PERSON_INFO;
import static com.hpbr.bosszhipin.get.export.GetConsts.GEEK_HOMEPAGE_INDEX_PUBLISH;

/**
 * Create by Chong
 * 2020-04-10
 * 参考类似 @BossHomeManager 管理个人主页的跳转
 */
public class GeekHomePageManager {
    /**
     * 去了H5 页面，操作了数据 需要刷新 个人主页，和其他的刷新广播比，这个只是在主页记录个标记，等resume的时候刷新
     * 避免在H5里操作 会刷新多次
     */
    public static void sendNeedRefreshHomePageInfo() {
        Intent intent = new Intent();
        intent.setAction(GetConsts.GET_NEED_HOMEPAGE_REFRESH);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }

    /**
     * 从简历详情进入
     *
     * @param context sourceType
     *                source:从简历过来的 在主页再次点击简历的时候  需要关闭主页 返回简历页
     */
    public static void jumpFromGeekResume(Context context, GetRouter.GHomePageParamsBean bean) {
        dealAnalytics(bean);
//        GetRouter.openGeekHomePage(context, securityGeekId, context, GetConsts.AvatarHomePage.AVATAR_GET, source);
        GetRouter.openGeekHomePage(context, bean);
    }

    public static void jump(Context context, GetRouter.GHomePageParamsBean bean) {
        if (bean == null) return;
        if (UserManager.isBossRole()) {
            GetBossOpenGeekHomePageRequest request = new GetBossOpenGeekHomePageRequest(
                    new ApiRequestCallback<GetBossOpenGeekHomePageResponse>() {
                        @Override
                        public void onSuccess(ApiData<GetBossOpenGeekHomePageResponse> data) {
                            if (context == null) return;
                            if (data != null && data.resp != null && data.resp.checkResult) {
                                dealAnalytics(bean);
                                GetRouter.openGeekHomePage(context, bean);
                            } else if (data != null && data.resp != null && !LText.isEmptyOrNull(data.resp.message)) {
                                ToastUtils.showText(data.resp.message);
                            }
                        }

                        @Override
                        public void onComplete() {

                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });

            request.securityId = bean.getSecurityId();
            request.source = bean.getSecuritySource();
            HttpExecutor.execute(request);
        } else {
            dealAnalytics(bean);
            GetRouter.openGeekHomePage(context, bean);
        }

    }

    /**
     * 处理埋点
     *
     * @param bean
     */
    private static void dealAnalytics(GetRouter.GHomePageParamsBean bean) {

        /**
         * get 个人主页新的埋点
         */
//        根据pagetype确定来源页面
        String from = "";
        if (!LText.empty(bean.getPageTypeString())) { // 有string的就不再转换了
            from = bean.getPageTypeString();
        } else {
            from = SwitchPageType2TextFrom.switchType(bean.getPageType());
        }
        String p8 = "";
        if (bean.getCanAsk() == 1 || bean.getCanAsk() == 0) {
            p8 = String.valueOf(bean.getCanAsk());
        }
        AnalyticsFactory.create()
                .action(AnalyticsAction.ACTION_GET_HOMEPAGE_SHOW)
                .param("p", from)
//                .param("p2", getTabParam(bean.getTabType()))
                .param("p5", "0")
                .param("p6", bean.getSecurityId())
                .param("p8", p8) //根据优化之前的代码来看  p8 是空的
                .debug()
                .build();
    }

    private static String getTabParam(String tabType) {
        switch (tabType) {
            case GEEK_HOMEPAGE_INDEX_PERSON_INFO:
                return "info";
            case GEEK_HOMEPAGE_INDEX_PUBLISH:
                return "dynamic";

            default:
                return "info";

        }
    }


    /**
     * 服务端还在使用int 来定位，转换成String
     * 0 个人信息  其他的都是发布
     *
     * @param tabIndex
     * @return
     */
    public static String getStringTab(int tabIndex) {
        switch (tabIndex) {

            case 0:
                return GEEK_HOMEPAGE_INDEX_PERSON_INFO;
            case 1:
            case 2:
                return GEEK_HOMEPAGE_INDEX_PUBLISH;
            default:
                return GEEK_HOMEPAGE_INDEX_PERSON_INFO;
        }
    }
}
