package com.hpbr.bosszhipin.module.boss.homepage;

import com.hpbr.bosszhipin.get.export.GetConsts;

import static com.hpbr.bosszhipin.get.export.GetConsts.TextFrom.TEXT_FROM_HOME_BOOK;
import static com.hpbr.bosszhipin.get.export.GetConsts.TextFrom.TEXT_FROM_HOME_HOBBY;

import android.annotation.SuppressLint;

/**
 * Create by Chong
 * 2020-04-21
 * <p>
 * {@link com.hpbr.bosszhipin.get.export.GetConsts.PageType} 转换为 {@link com.hpbr.bosszhipin.get.export.GetConsts.TextFrom}
 * 方便打点使用
 * 使用的时候注意是否有新增的点，记得增加一下转换
 */

//            String TEXT_FROM_PUSH = "push";
//                    String TEXT_FROM_NOTICE = "notice";
//                    String TEXT_FROM_MESSAGE = "message";
//                    String TEXT_FROM_NOTICE_MORE = "noticeMore";
//                    String TEXT_FROM_CONTENT_CARD = "card";
//                    String TEXT_FROM_TOPCARD = "topcard";
//                    String TEXT_FROM_BANNER = "banner";
//                    String TEXT_FROM_BANNERGROUP = "bannergroup";
//                    String TEXT_FROM_FOLLOW_QUESTION = "followQuestion";
//                    String TEXT_FROM_FOLLOW_TOPIC = "followTopic";
//                    String TEXT_FROM_MYGET = "myget";
//                    String TEXT_FROM_MYPUBLISH = "mypublish";
//                    String TEXT_FROM_EXPLORE = "explore";
//                    String TEXT_FROM_GETFEED = "getfeed";
//                    String TEXT_FROM_TOPICFEED = "topicfeed";
//                    String TEXT_FROM_ANSWERFEED = "answerfeed";
//                    String TEXT_FROM_COURSEDETAIL = "coursedetail";
//                    String TEXT_FROM_COURSEFEED = "coursefeed";
//                    String TEXT_FROM_HOT_TOPIC = "hotTopic";
//                    String TEXT_FROM_HOT_QUESTION = "hotQuestion";
//                    String TEXT_FROM_QUESTIONFEED = "questionfeed";
//                    String TEXT_FROM_CUSTOM_PAGE = "customPage";
//                    String TEXT_FROM_SEARCH_TOPIC = "searchTopic";
//                    String TEXT_FROM_SEARCH_CONTENT = "searchContent";
//                    String TEXT_FROM_SEARCH_COURSE = "searchCourse";
//                    String TEXT_FROM_SEARCH_QUESTION = "searchQuestion";
//                    String TEXT_FROM_MYCOURSE = "myCourse";
//                    String TEXT_FROM_AUDIOPLAY = "audioplay";
//                    String TEXT_FROM_VIDEOPLAY = "videoplay";
//                    String TEXT_FROM_GET_DYNAMICDETAIL = "dynamicdetail";
//                    String TEXT_FROM_GET_COMPANY_CIRCLE = "companycircle";
//                    String TEXT_FROM_GET_MY_HOME = "myhome";
//                    String TEXT_FROM_GET_COMPANY_HOME = "companyhome";
//                    String TEXT_FROM_HOT_QA_PEER = "peerask";//同行在问
//                    String TEXT_FROM_HOTQA_TAB = "tab";
//                    String TEXT_FROM_FEED_CARD = "feedcard";
//
//     p1：来源页面【1.发现页：explore 2.话题详情页：topicfeed 3.问题详情页：answerfeed 4.我的get：myget
//             5.我的发布：mypublish 6.我的关注：myfollow,7. dynamicdetail】

public class SwitchPageType2TextFrom {
    @SuppressLint("WrongConstant")
    public static @com.hpbr.bosszhipin.get.export.GetConsts.TextFrom
    String switchType(@GetConsts.PageType int type) {
        @GetConsts.TextFrom String result = GetConsts.TextFrom.TEXT_FROM_CUSTOM_PAGE;
        switch (type) {
            case GetConsts.PageType.PAGE_TYPE_GET:
                result = GetConsts.TextFrom.TEXT_FROM_GETFEED;
                break;
            case GetConsts.PageType.PAGE_TYPE_MY:
            case GetConsts.PageType.PAGE_TYPE_MY_POST_METHOD:
            case GetConsts.PageType.PAGE_TYPE_MYGET_COLUMN:
            case GetConsts.PageType.PAGE_TYPE_MY_GET_INTERVIEW:
                result = GetConsts.TextFrom.TEXT_FROM_MYGET;
                break;//2;
            case GetConsts.PageType.PAGE_TYPE_TOPIC:
                result = GetConsts.TextFrom.TEXT_FROM_TOPICFEED;
                break;// 3;
            case GetConsts.PageType.PAGE_TYPE_ANSWER:
                result = GetConsts.TextFrom.TEXT_FROM_ANSWERFEED;
                break;//4;
            case GetConsts.PageType.PAGE_TYPE_MY_POST:
                result = GetConsts.TextFrom.TEXT_FROM_MYGET;
                break;// 5;
            case GetConsts.PageType.PAGE_TYPE_COURSE:
                //todo 这里有课程详情 和 课程feed
                break;// 6;
            case GetConsts.PageType.PAGE_TYPE_COURSE_MANAGE:
                //todo 这里有课程问答管理
                break;// 7;
            case GetConsts.PageType.PAGE_TYPE_PLAYER:
                result = GetConsts.TextFrom.TEXT_FROM_AUDIOPLAY;
                break;// 8;
            case GetConsts.PageType.PAGE_TYPE_MY_COURSE:
                result = GetConsts.TextFrom.TEXT_FROM_MYCOURSE;
                break;// 9;
            case GetConsts.PageType.PAGE_TYPE_OTHER:
                result = GetConsts.TextFrom.TEXT_FROM_VIDEOPLAY;
                break;// 10;
            case GetConsts.PageType.PAGE_TYPE_F1_SEARCH:
                result = GetConsts.TextFrom.TEXT_FROM_SEARCH_CONTENT;
                break;// 11;
            case GetConsts.PageType.PAGE_TYPE_F4_FIND:
            case GetConsts.PageType.PAGE_TYPE_RECOMMEND:
                result = GetConsts.TextFrom.TEXT_FROM_RECOMMEND;
                break;//12;
            case GetConsts.PageType.PAGE_TYPE_CUSTOMIZE:
                result = GetConsts.TextFrom.TEXT_FROM_CUSTOM_PAGE;
                break;//13;
            case GetConsts.PageType.PAGE_TYPE_HOMEPAGE_DYNAMIC:
            case GetConsts.PageType.PAGE_TYPE_HOMEPAGE_DYNAMIC_FLOAT:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_BOSS_INVITE_FLOAT:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_BOSS_INVITE:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_SELECTION_FLOAT:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_SELECTION:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_ARTICLE:
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_ARTICLE_FLOAT:
            case GetConsts.PageType.PAGE_TYPE_HOMEPAGE_TEAM_DYNAMIC:  // 团队动态
            case GetConsts.PageType.PAGE_TYPE_HOMEPAGE_TEAM_DYNAMIC_FLOAT:  // 团队动态 悬浮
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_TEAM_SELECTION: //团队精选
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_TEAM_SELECTION_FLOAT://团队精选 悬浮
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_TEAM_ARTICLE: //团队好文
            case GetConsts.PageType.PAGE_TYPE_HOME_PAGE_TEAM_ARTICLE_FLOAT: //团队好文 悬浮
                result = GetConsts.TextFrom.TEXT_FROM_GET_MY_HOME;
                break;// 15;  // wo 的发布的动态
            case GetConsts.PageType.PAGE_TYPE_BOSS_CIRCLE_DYNAMIC:
                result = GetConsts.TextFrom.TEXT_FROM_GET_COMPANY_CIRCLE;
                break;// 16;//公司圈动态
            case GetConsts.PageType.PAGE_TYPE_BOSS_CIRCLE_QA_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_ANSWERFEED;
                break;//  17;//公司圈问答
            case GetConsts.PageType.PAGE_TYPE_HOMEPAGE_QA:
                result = GetConsts.TextFrom.TEXT_FROM_GET_MY_HOME;
                break;//  18;//个人主页的qa
            case GetConsts.PageType.PAGE_TYPE_MY_PUBLISH:
                result = GetConsts.TextFrom.TEXT_FROM_MYPUBLISH;
                break;//  19; // wo 的发布的动态/问答
            case GetConsts.PageType.PAGE_TYPE_DY_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_GET_DYNAMICDETAIL;
                break;
            case GetConsts.PageType.PAGE_TYPE_RANK_GEEK:
                result = GetConsts.TextFrom.TEXT_FROM_GEEK_LIST;
                break;
            case GetConsts.PageType.PAGE_TYPE_RANK_BOSS:
                result = GetConsts.TextFrom.TEXT_FROM_BOSS_LIST;
                break;
            case GetConsts.PageType.PAGE_TYPE_CHANGE_CAREERS:
                result = GetConsts.TextFrom.TEXT_FROM_TRANSFER;
                break;
            case GetConsts.PageType.PAGE_TYPE_ASK_GEEK:
                result = GetConsts.TextFrom.TEXT_FROM_PIONEER_LIST;
                break;
            case GetConsts.PageType.PAGE_TYPE_NOTICE810:
                result = GetConsts.TextFrom.TEXT_FROM_MESSAGE;
                break;
            case GetConsts.PageType.PAGE_TYPE_SOCIAL_CIRCLE:
                result = GetConsts.TextFrom.TEXT_FROM_SOCIAL_CIRCLE;
                break;
            case GetConsts.PageType.PAGE_TYPE_CIRCLE_FRIEND:
                result = GetConsts.TextFrom.TEXT_FROM_CIRCLE_FRIEND;
                break;
            case GetConsts.PageType.PAGE_TYPE_CARD_CONTENT:
                result = GetConsts.TextFrom.TEXT_FROM_CONTENT_CARD;
                break;
            case GetConsts.PageType.PAGE_TYPE_SYCC_HOME:
                result = GetConsts.TextFrom.TEXT_FROM_SYCC_USER_HOME;
                break;

            case GetConsts.PageType.PAGE_TYPE_FOLLOW:
                result = GetConsts.TextFrom.TEXT_FROM_FOCUS;
                break;
            case GetConsts.PageType.PAGE_TYPE_GET_TEST:
                result = GetConsts.TextFrom.TEXT_FROM_TEST;
                break;

            case GetConsts.PageType.PAGE_TYPE_QUESTION:
                result = GetConsts.TextFrom.TEXT_FROM_QUESTION;
                break;
            case GetConsts.PageType.PAGE_TYPE_HOME_COLUMN:
                result = GetConsts.TextFrom.TEXT_FROM_HOME_COLUMN;
                break;
            case GetConsts.PageType.PAGE_TYPE_VIDEO_LIST:
                result = GetConsts.TextFrom.TEXT_FROM_VIDEOLIST;
                break;
            case GetConsts.PageType.PAGE_TYPE_CARD_ALL_REPLAY:
                result = GetConsts.TextFrom.TEXT_FROM_CONTENT_CARD;
                break;
            case GetConsts.PageType.PAGE_TYPE_MY_POST_BOOK:
                result = TEXT_FROM_HOME_BOOK;
                break;
            case GetConsts.PageType.PAGE_TYPE_MY_POST_HOBBY:
                result = TEXT_FROM_HOME_HOBBY;
                break;
            case GetConsts.PageType.PAGE_TYPE_MY_POST_TOOLS:

                result = GetConsts.TextFrom.TEXT_FROM_HOME_TOOL;
                break;
            case GetConsts.PageType.PAGE_TYPE_MY_POST_PUBLIC:
                result = GetConsts.TextFrom.TEXT_FROM_HOME_PUBLIC;
                break;

            case GetConsts.PageType.PAGE_TYPE_BOOK_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_BOOK_DETAIL;
                break;

            case GetConsts.PageType.PAGE_TYPE_TOOL_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_TOOLS_DETAIL;
                break;
            case GetConsts.PageType.PAGE_TYPE_PUBLIC_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_PUBLIC_DETAIL;
                break;
            case GetConsts.PageType.PAGE_TYPE_HOBBY_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_HOBBY_DETAIL;
                break;
            case GetConsts.PageType.PAGE_TYPE_INTERVIEW_ANSWER:
                result = GetConsts.TextFrom.TEXT_FROM_INTERVIEW_ANSWER;
                break;
            case GetConsts.PageType.PAGE_TYPE_INTERVIEW_QUESTION:
                result = GetConsts.TextFrom.TEXT_FROM_INTERVIEW_QUESTION;
                break;
            case GetConsts.PageType.PAGE_TYPE_CONTENT_BE_INCLUDE:
                result = GetConsts.TextFrom.TEXT_FROM_BE_INCLUDE;
                break;

            case GetConsts.PageType.PAGE_TYPE_NOTICES:
                result = GetConsts.TextFrom.TEXT_FROM_NOTICES;
                break;
            case GetConsts.PageType.PAGE_HOMETAB_USUALLY:
                result = GetConsts.TextFrom.TEXT_FROM_USUALLY;
                break;
            case GetConsts.PageType.PAGE_HOMETAB_EXCEPT:
                result = GetConsts.TextFrom.TEXT_FROM_EXCEPT;
                break;
            case GetConsts.PageType.PAGE_INFORMATION:
                result = GetConsts.TextFrom.TEXT_FROM_INDUSTRY_NEWS;
                break;
            case GetConsts.PageType.PAGE_COMPANY_INFORMATION:
                result = GetConsts.TextFrom.TEXT_FROM_COMPANY_NEWS;
                break;

            case GetConsts.PageType.PAGE_DYNAMIC_DICTIONARY:
                result = GetConsts.TextFrom.TEXT_FROM_DICTIONARY_DYNAMIC;
                break;

            case GetConsts.PageType.PAGE_DICTIONARY_DETAIL:
                result = GetConsts.TextFrom.TEXT_FROM_DICTIONARY_CARD;
                break;
            case GetConsts.PageType.PAGE_MY_DYNAMIC_DICTIONARY:
                result = GetConsts.TextFrom.TEXT_FROM_MYGET;
                break;
            case GetConsts.PageType.PAGE_HOT_COMMENT:
                result = GetConsts.TextFrom.TEXT_FROM_HOT_COMMENT;
                break;

            case GetConsts.PageType.PAGE_WIKI_FEED:
                result = GetConsts.TextFrom.TEXT_FROM_CAREERENCYC;
                break;
            case GetConsts.PageType.PAGE_DICTIONARY_SEARCH:
            case GetConsts.PageType.PAGE_INFORMATION_SEARCH:
                result = GetConsts.TextFrom.TEXT_FROM_SEARCH_WIKI;
                break;
            case GetConsts.PageType.PAGE_TYPE_INTERVIEW_DISCUSS:
                result = GetConsts.TextFrom.TEXT_FROM_DISCUSS_INTERVIEW;
                break;
            case GetConsts.PageType.PAGE_INDUSTRY_INFORMATION:
                result = GetConsts.TextFrom.TEXT_FROM_INDUSTRY;
                break;
            case GetConsts.PageType.PAGE_INTERVIEW_BRAND_QUESTION:
                result = GetConsts.TextFrom.TEXT_FROM_BIG_NAME_INTERVIEW;
                break;
            case GetConsts.PageType.PAGE_INTERVIEW_POSITION_QUESTION:
                result = GetConsts.TextFrom.TEXT_FROM_JOB_INTERVIEW;
                break;
            case GetConsts.PageType.PAGE_PGC_HOME_PAGE:
                result = GetConsts.TextFrom.TEXT_FROM_PGC_HOME;
                break;
            case GetConsts.PageType.PAGE_VIDEO_TAB:
                result = GetConsts.TextFrom.TEXT_VIDEO_TAB;
                break;
            default:
                return result;

        }

        return result;
    }


    public static String switchCategory(int category) {
        String p = "";
        switch (category) {
            case 0:
                p = "content";
                break;
            case 1:
                p = "question";
                break;
            case 2:
                p = "dynamic";
                break;
            case 3:
                p = "answer";
                break;
            case 7:
                p = "method";
                break;
            case 8:
                p = "interview";
                break;
            case 9:
                p = "interview_answer";
                break;
            default:
                p = "";
                break;

        }
        return p;
    }

    public static String switchCardContentType(int category, int contentType, int pageType) {
        if (pageType == GetConsts.PageType.PAGE_TYPE_CARD_CONTENT || pageType == GetConsts.PageType.PAGE_TYPE_ANSWER) {
            if (contentType == GetConsts.CardContentType.CARD_EXPAND) {
                if (category == 3) {
                    return "moreanswer";
                } else if (category == 2 || category == 0) {
                    return "morecontent";
                }
            } else if (contentType == GetConsts.CardContentType.CARD_SIMILAR) {
                if (pageType == GetConsts.PageType.PAGE_TYPE_CARD_CONTENT) {
                    if (category == 3) {
                        return "similaranswer";
                    } else if (category == 2 || category == 0) {
                        return "similarcontent";
                    }
                } else {
                    return "similarquestion";
                }
            }

        }
        return SwitchPageType2TextFrom.switchType(pageType);
    }


}
