package com.hpbr.bosszhipin.module.boss.render;

import android.content.Context;

import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.common.adapter.AbstractViewRenderer;
import com.hpbr.bosszhipin.common.adapter.AbsHolder;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/8/2.
 */

public abstract class AbsViewRenderer<M extends ItemModel, VH extends AbsHolder<M>> extends AbstractViewRenderer<M, VH, OnComponentsClickListener> {

    public AbsViewRenderer(Context context, OnComponentsClickListener listener) {
        super(context, listener);
    }

}
