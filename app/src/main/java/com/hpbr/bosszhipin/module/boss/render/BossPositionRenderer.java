package com.hpbr.bosszhipin.module.boss.render;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.AbsHolder;
import com.hpbr.bosszhipin.common.adapter.AbstractViewRenderer;
import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.module.boss.adapter.HiringPositionAdapter.OnPositionItemClickListener;
import com.hpbr.bosszhipin.module.boss.data.PositionItemModel;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerJobItemBean;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>ng<PERSON> on 2018/8/14 15:17.
 */
public class BossPositionRenderer extends AbstractViewRenderer<PositionItemModel, AbsHolder<PositionItemModel>, OnPositionItemClickListener> {

    public BossPositionRenderer(Context context, OnPositionItemClickListener listener) {
        super(context, listener);
    }

    @NonNull
    @Override
    public AbsHolder<PositionItemModel> createViewHolder(@Nullable ViewGroup parent) {
        return new Holder(inflate(R.layout.item_hiring_position, parent, false));
    }

    @Override
    public boolean canUseThisRenderer(ItemModel model) {
        return model instanceof PositionItemModel;
    }

    private class Holder extends AbsHolder<PositionItemModel> {

        private final MTextView positionName;
        private final MTextView positionSalary;
        private final MTextView positionInfo;

        public Holder(View itemView) {
            super(itemView);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    getListener().onJobItemClick(getModel().jobItemBean);
                }
            });

            positionName = itemView.findViewById(R.id.positionName);
            positionSalary = itemView.findViewById(R.id.positionSalary);
            positionInfo = itemView.findViewById(R.id.positionInfo);
        }

        @Override
        public void bind(@NonNull PositionItemModel model) {
            super.bind(model);

            ServerJobItemBean bean = model.jobItemBean;
            positionName.setText(bean.jobName);
            positionSalary.setText(bean.salaryDesc);

            List<String> jobLabels = bean.jobLabels;
            if (LList.isEmpty(jobLabels)) {
                positionInfo.setText("");
            } else {
                StringBuilder sb = new StringBuilder();
                int size = jobLabels.size();
                for (int i = 0; i < size; i++) {
                    String string = jobLabels.get(i);
                    if (TextUtils.isEmpty(string)) continue;
                    if (i == size - 1) {
                        sb.append(string);
                    } else {
                        sb.append(string).append(" · ");
                    }
                }
                positionInfo.setText(sb.toString());
            }
        }
    }

}
