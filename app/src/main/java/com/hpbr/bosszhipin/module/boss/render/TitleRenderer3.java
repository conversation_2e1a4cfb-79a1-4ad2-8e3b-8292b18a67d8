package com.hpbr.bosszhipin.module.boss.render;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.adapter.AbsHolder;
import com.hpbr.bosszhipin.common.adapter.ItemModel;
import com.hpbr.bosszhipin.module.boss.entity.TitleItemModel;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Created by <PERSON>hangxiangdong on 2018/8/14 14:39.
 */
public class TitleRenderer3 extends AbsViewRenderer<TitleItemModel, AbsHolder<TitleItemModel>> {

    public TitleRenderer3(Context context) {
        super(context, null);
    }

    @NonNull
    @Override
    public AbsHolder<TitleItemModel> createViewHolder(@Nullable ViewGroup parent) {
        return new Holder(inflate(R.layout.item_title3, parent, false));
    }

    @Override
    public boolean canUseThisRenderer(ItemModel model) {
        return model instanceof TitleItemModel;
    }

    private class Holder extends AbsHolder<TitleItemModel> {

        private final MTextView titleText;

        public Holder(View itemView) {
            super(itemView);

            titleText = itemView.findViewById(R.id.titleText);
        }

        @Override
        public void bind(@NonNull TitleItemModel model) {
            super.bind(model);
            titleText.setText(model.title);
        }
    }

}
