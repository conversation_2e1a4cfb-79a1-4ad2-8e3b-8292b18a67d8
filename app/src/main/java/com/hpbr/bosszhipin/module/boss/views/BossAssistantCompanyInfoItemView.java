package com.hpbr.bosszhipin.module.boss.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.monch.lbase.util.Scale;
import com.twl.ui.CollapseTextView2;

/**
 * boss端查看牛人公司弹框中的公司卡片-企查查每行的信息
 *
 * <AUTHOR>
 * @version 713
 */
public class BossAssistantCompanyInfoItemView extends RelativeLayout {

    private Context mContext;
    private TextView mTvLeft;
    private CollapseTextView2 mCollapseTextView2;
    private View subView;

    public BossAssistantCompanyInfoItemView(Context context) {
        this(context, null);
    }

    public BossAssistantCompanyInfoItemView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossAssistantCompanyInfoItemView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        subView = inflate(mContext, R.layout.boss_layout_company_card_item, this);
        initView();
    }

    private void initView() {
        mTvLeft = findViewById(R.id.tv_left);
        mCollapseTextView2 = findViewById(R.id.tv_right);

        int width = App.get().getDisplayWidth() - Scale.dip2px(mContext, 80 + 80);
        mCollapseTextView2.initWidth(width);
    }

    public void setData(String left, String right, boolean expand) {
        mTvLeft.setText(left);
        mCollapseTextView2.setText(right);
        if (expand) {
            mCollapseTextView2.setShowCollapseFeature(true, 2);
            mCollapseTextView2.setMaxLines(2);
            mCollapseTextView2.setCloseText(right);
            mCollapseTextView2.setExpandText("查看全部");
        }
    }

    public void setMarginBottom(int px) {
        RelativeLayout.LayoutParams layoutParams = (LayoutParams) subView.getLayoutParams();
        if (null == layoutParams) {
            layoutParams = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        layoutParams.bottomMargin = Scale.dip2px(getContext(), px);
        subView.setLayoutParams(layoutParams);
    }
}
