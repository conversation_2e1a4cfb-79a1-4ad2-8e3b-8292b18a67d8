package com.hpbr.bosszhipin.module.boss.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.ui.CollapseTextView2;

import net.bosszhipin.api.BossCompanyInfoAssistantResponse;

/**
 * boss端查看牛人公司弹框中的公司卡片
 *
 * <AUTHOR>
 * @version 713
 */
public class BossAssistantCompanyView extends RelativeLayout {

    private Context mContext;
    private SimpleDraweeView mIvLogo;
    private MTextView mTvCompanyName;
    private MTextView mTvCompanyInfo;
    private CollapseTextView2 mCollapseTextView2;
    private LinearLayout mProductLl;
    //    private RecyclerView mProductRv;
    private TextView mProductTv;

    public BossAssistantCompanyView(Context context) {
        this(context, null);
    }

    public BossAssistantCompanyView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossAssistantCompanyView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        inflate(mContext, R.layout.boss_layout_company_card, this);
        initView();
    }

    private void initView() {
        mIvLogo = findViewById(R.id.iv_logo);
        mTvCompanyName = findViewById(R.id.tv_company_name);
        mTvCompanyInfo = findViewById(R.id.tv_com_info);
        mCollapseTextView2 = findViewById(R.id.collapseTextView);
        mProductLl = findViewById(R.id.brand_product_ll);
        mProductTv = findViewById(R.id.brand_product_tv);
//        mProductRv = findViewById(R.id.product_rv);
    }

    public void setData(BossCompanyInfoAssistantResponse.BrandAssistant data) {
        if (data != null) {
            mIvLogo.setImageURI(data.logo);
            mTvCompanyName.setText(data.name, View.GONE);
            //融资状况/规模/行业
            mTvCompanyInfo.setText(StringUtil.connectTextWithChar("·", data.stage, data.scale, data.industry));
            int width = App.get().getDisplayWidth() - Scale.dip2px(mContext, 80);
            mCollapseTextView2.initWidth(width);
            mCollapseTextView2.setShowCollapseFeature(true, 2);
            mCollapseTextView2.setMaxLines(2);
            mCollapseTextView2.setCloseText(data.introduce);
            mCollapseTextView2.setExpandText("查看详情");
            if (!LList.isEmpty(data.productList)) {
                mProductTv.setVisibility(VISIBLE);
                mProductLl.setVisibility(VISIBLE);
                for (int i = 0; i < data.productList.size(); i++) { //  没有使用recyclerView  会和 scrollview 外面的sheet 抢焦点，没有深究 先这样吧
                    if (data.productList.get(i) == null) continue;
                    View view = LayoutInflater.from(getContext()).inflate(R.layout.view_resume_assustant_product, null);

                    SimpleDraweeView logo = view.findViewById(R.id.iv_logo);
                    TextView name = view.findViewById(R.id.tv_company_name);
                    TextView desc = view.findViewById(R.id.tv_com_info);

                    logo.setImageURI(data.productList.get(i).logo);
                    name.setText(data.productList.get(i).name);
                    desc.setText(data.productList.get(i).desc);
                    mProductLl.addView(view);
                }
            } else {
                mProductTv.setVisibility(GONE);
                mProductLl.setVisibility(GONE);
            }
        }
    }
}
