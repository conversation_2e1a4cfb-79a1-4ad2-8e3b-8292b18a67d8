package com.hpbr.bosszhipin.module.boss.views;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.BossCompanyInfoAssistantResponse;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * boss端查看牛人公司弹框中的公司卡片
 *
 * <AUTHOR>
 * @version 713
 */
public class BossAssistantFeedbackView extends FrameLayout implements View.OnClickListener {

    private Context mContext;
    private MTextView tvFeedbackTitle;
    private LinearLayout llFeedbackStep1;
    private FlexboxLayout flFeedbackStep2;

    public BossAssistantFeedbackView(Context context) {
        this(context, null);
    }

    public BossAssistantFeedbackView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BossAssistantFeedbackView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        inflate(mContext, R.layout.boss_layout_assistant_feedback, this);
        initView();
    }

    private int step = 0;
    private int selectHelpful = 0;
    private BossCompanyInfoAssistantResponse data;

    public int getHelpfulSelection() {
        return selectHelpful;
    }

    private void initView() {
        tvFeedbackTitle = findViewById(R.id.tv_feedback_title);
        llFeedbackStep1 = findViewById(R.id.ll_feedback_step_1);
        flFeedbackStep2 = findViewById(R.id.fl_feedback_step_2);

        findViewById(R.id.ll_helpless).setOnClickListener(this);
        findViewById(R.id.ll_helpful).setOnClickListener(this);
    }

    public void setData(BossCompanyInfoAssistantResponse data) {
        this.data = data;
        setStep();
    }

    private void setFeedbackItems() {
        flFeedbackStep2.removeAllViews();
        if (!LList.isEmpty(data.brandAssistantList)) {
            flFeedbackStep2.addView(getText("品牌信息"));
        }

        if (data.companyAssistant != null) {
            flFeedbackStep2.addView(getText("企业工商信息"));
        }

        if (selectHelpful == 2) {
            flFeedbackStep2.addView(getText("都没用"));
        }
    }

    private MTextView getText(String string) {
        int paddingVertical = ZPUIDisplayHelper.dp2px(mContext, 8f);
        int paddingHorizontal = ZPUIDisplayHelper.dp2px(mContext, 12f);
        MTextView textView = new MTextView(mContext);
        textView.setText(string);
        textView.setTextColor(Color.WHITE);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 13f);
        textView.setBackgroundResource(R.drawable.bg_com_feedback);
        textView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                step = 2;
                setStep();
            }
        });
        return textView;
    }

    private void setStep() {
        if (step == 0) {
            tvFeedbackTitle.setText("以上信息是否对您有帮助？");
            llFeedbackStep1.setVisibility(VISIBLE);
            flFeedbackStep2.setVisibility(GONE);
        } else if (step == 1) {
            llFeedbackStep1.setVisibility(GONE);
            flFeedbackStep2.setVisibility(VISIBLE);
            if (selectHelpful == 1) {
                tvFeedbackTitle.setText("您觉得哪部分信息对您最有帮助？");
            } else if (selectHelpful == 2) {
                tvFeedbackTitle.setText("您觉得哪部分信息对您没有用？");
            }
            setFeedbackItems();
        } else if (step == 2) {
            tvFeedbackTitle.setText("谢谢您的反馈");
            llFeedbackStep1.setVisibility(GONE);
            flFeedbackStep2.setVisibility(GONE);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.ll_helpless) {
            selectHelpful = 2;
            step = 1;
            setStep();
        } else if (id == R.id.ll_helpful) {
            selectHelpful = 1;
            step = 1;
            setStep();
        }
    }
}
