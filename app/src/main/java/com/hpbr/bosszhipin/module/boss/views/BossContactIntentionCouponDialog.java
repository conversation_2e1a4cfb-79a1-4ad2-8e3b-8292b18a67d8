package com.hpbr.bosszhipin.module.boss.views;

import android.app.Dialog;
import android.content.Context;
import android.text.TextPaint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.hunter.CIClickSource;
import com.hpbr.bosszhipin.utils.ZPFunction;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.ActivityUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.bean.ServerCIChatCouponBean;
import net.bosszhipin.api.bean.ServerImageBtnBean;
import net.bosszhipin.base.SimpleApiRequest;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * 1221.501【意向沟通】BOSS端-畅聊版优惠券的下发和展示：优惠券提示弹窗
 */
public class BossContactIntentionCouponDialog {

    public static BossContactIntentionCouponDialog obj(@NonNull Context context, @NonNull ServerCIChatCouponBean couponBean) {
        return new BossContactIntentionCouponDialog(context, couponBean);
    }

    @NonNull
    private final ServerCIChatCouponBean couponBean;
    @NonNull
    private final Context context;
    @NonNull
    private final Dialog dialog;

    @Nullable
    private ZPFunction.Fun1<Integer> onDialogClickListener;
    @Nullable
    private ZPFunction.Fun0 onShowListener;
    @Nullable
    private String encCouponId;
    private int showPage;
    @CIClickSource
    private String clickSource;

    public BossContactIntentionCouponDialog(@NonNull Context context, @NonNull ServerCIChatCouponBean couponBean) {
        this.dialog = new Dialog(context, com.twl.ui.R.style.twl_ui_common_dialog);
        this.couponBean = couponBean;
        this.context = context;
    }

    /**
     * 设置加密的优惠券ID，用于关闭弹窗时，上报服务端接口
     *
     * @param encCouponId 加密的优惠券ID
     * @return BossContactIntentionCouponDialog
     */
    public BossContactIntentionCouponDialog setEncCouponId(@Nullable String encCouponId) {
        this.encCouponId = encCouponId;
        return this;
    }

    /**
     * 展示优惠券时，传入优惠券弹窗当前所在的界面
     *
     * @param showPage 1-发现列表；2-支付页面
     * @return BossContactIntentionCouponDialog
     */
    public BossContactIntentionCouponDialog setShowPage(int showPage) {
        this.showPage = showPage;
        return this;
    }

    /**
     * 在购买页面展示优惠券时，传入购买页面的打开来源
     *
     * @param clickSource 购买页面的打开来源
     * @return BossContactIntentionCouponDialog
     */
    public BossContactIntentionCouponDialog setClickSource(@CIClickSource String clickSource) {
        this.clickSource = clickSource;
        return this;
    }

    /**
     * 设置优惠券弹窗点击监听回调
     *
     * @param onDialogClickListener 点击监听回调：0 - 关闭按钮；1 - 使用按钮
     * @return BossContactIntentionCouponDialog
     */
    public BossContactIntentionCouponDialog setOnDialogClickListener(@Nullable ZPFunction.Fun1<Integer> onDialogClickListener) {
        this.onDialogClickListener = onDialogClickListener;
        return this;
    }

    /**
     * 设置优惠券弹窗展示监听
     *
     * @param onShowListener 展示监听回调
     * @return BossContactIntentionCouponDialog
     */
    public BossContactIntentionCouponDialog setOnDialogShowListener(@Nullable ZPFunction.Fun0 onShowListener) {
        this.onShowListener = onShowListener;
        return this;
    }

    public void show() {
        if (ActivityUtils.isValid(context)) {
            try {
                initDialogShow();
                dialog.show();
                tryReportHasShowRetain();
                reportDialogShow();
            } catch (Exception e) {
                TLog.error("", e.getMessage());
            }
        }
    }

    private void initDialogShow() {
        dialog.setCanceledOnTouchOutside(false);
        View contentView = LayoutInflater.from(context).inflate(R.layout.dialog_boss_contact_intention_coupon, null);
        initViewShow(contentView);
        ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        dialog.addContentView(contentView, layoutParams);

        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }

    private void initViewShow(@Nullable View contentView) {
        if (contentView == null) {
            return;
        }
        setTextShow(contentView.findViewById(R.id.tv_title), couponBean.title == null ? null : couponBean.title.name, false);
        MTextView tvAlert = contentView.findViewById(R.id.tv_alert);
        if (couponBean.subTitle == null || LText.isEmptyOrNull(couponBean.subTitle.name)) {
            tvAlert.setVisibility(View.INVISIBLE);
        } else {
            if (couponBean.subTitle.highlightList == null || couponBean.subTitle.highlightList.isEmpty()) {
                tvAlert.setText(couponBean.subTitle.name);
            } else {
                int highLightColor = ContextCompat.getColor(context, R.color.color_FFFF5D26_FFFF5D26);
                tvAlert.setText(ZPUtils.getHighlightedTextBuilder(couponBean.subTitle.name, couponBean.subTitle.highlightList, highLightColor));
            }
        }

        setTextShow(contentView.findViewById(R.id.tv_desc_title), couponBean.discountPercentDesc, true);
        setTextShow(contentView.findViewById(R.id.tv_desc_info), couponBean.scopeDesc, false);
        setTextShow(contentView.findViewById(R.id.tv_price), couponBean.discountAmountDesc, true);
        setTextShow(contentView.findViewById(R.id.tv_price_info), couponBean.discountDesc, false);

        setTextShow(contentView.findViewById(R.id.tv_time), couponBean.expireTimeDesc, false);
        setBtnUseShow(contentView.findViewById(R.id.iv_use), couponBean.button);

        View.OnClickListener clickListener = new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (v.getId() == R.id.iv_close) {
                    reportDialogClick(2);
                    if (onDialogClickListener != null) {
                        onDialogClickListener.call(0);
                    }
                } else if (v.getId() == R.id.iv_use) {
                    reportDialogClick(1);
                    if (onDialogClickListener != null) {
                        onDialogClickListener.call(1);
                    }
                }
                dialog.cancel();
            }
        };
        contentView.findViewById(R.id.iv_use).setOnClickListener(clickListener);
        contentView.findViewById(R.id.iv_close).setOnClickListener(clickListener);
    }

    private void setTextShow(@Nullable View view, @Nullable String textValue, boolean needBold) {
        if (!(view instanceof TextView)) {
            return;
        }
        TextView textView = (TextView) view;
        if (LText.isEmptyOrNull(textValue)) {
            textView.setVisibility(View.INVISIBLE);
            return;
        }
        if (needBold) {
            TextPaint textPaint = textView.getPaint();
            if (textPaint != null) {
                textPaint.setFakeBoldText(true);
            }
        }
        textView.setText(textValue);
    }

    private void setBtnUseShow(@Nullable SimpleDraweeView ivBtnToUse, @Nullable ServerImageBtnBean imageBean) {
        if (ivBtnToUse == null) {
            return;
        }
        if (imageBean == null || LText.isEmptyOrNull(imageBean.imgUrl)) {
            ivBtnToUse.setVisibility(View.GONE);
            return;
        }
        ivBtnToUse.setVisibility(View.VISIBLE);
        ivBtnToUse.setImageURI(imageBean.imgUrl);
        if (imageBean.width <= 0 || imageBean.height <= 0) {
            return;
        }
        int height = ZPUIDisplayHelper.dp2px(context, 50.0f);
        int width = (int) (height * (imageBean.width * 1.0f / imageBean.height));
        ViewGroup.LayoutParams imageParams = ivBtnToUse.getLayoutParams();
        imageParams.height = height;
        imageParams.width = width;
    }

    private void tryReportHasShowRetain() {
        if (couponBean.showRetain == 1 && !LText.isEmptyOrNull(encCouponId)) {
            SimpleApiRequest.POST(BusinessUrlConfig.URL_INTENTION_CONTACT_COUPON_HAS_RETAIN).addParam("encCouponId", encCouponId).execute();
        }
        if (onShowListener != null) {
            onShowListener.call();
        }
    }

    /**
     * 3.41.2【意】BOSS端-搜索收获差用户的引流：新增打点入参优惠券类型
     *
     * @return 优惠券类型
     */
    @NonNull
    private String getCouponType() {
        if (couponBean.pointsInfo == null) {
            return "";
        } else {
            return String.valueOf(couponBean.pointsInfo.sendType);
        }
    }

    /**
     * 优惠券弹窗展示上报
     */
    private void reportDialogShow() {
        if (couponBean.sendCoupon == 1) {
            AnalyticsFactory.create().action("extension-hunter-cvcustomer-APPcouponexpo")
                    .param("p", showPage)
                    .param("p2", clickSource)
                    .param("p4", getCouponType())
                    .build();
        }
        if (couponBean.showRetain == 1) {
            AnalyticsFactory.create().action("extension-hunter-cvcustomer-APPrecouponexpo")
                    .param("p", showPage)
                    .param("p2", clickSource)
                    .param("p4", getCouponType())
                    .build();
        }
    }

    /**
     * 优惠券弹窗点击事件上报
     *
     * @param clickType 1-点击「去使用」；2-点击「叉号」
     */
    private void reportDialogClick(int clickType) {
        if (couponBean.sendCoupon == 1) {
            AnalyticsFactory.create().action("extension-hunter-cvcustomer-APPcouponclick")
                    .param("p", showPage)
                    .param("p2", clickSource)
                    .param("p3", clickType)
                    .param("p4", getCouponType())
                    .build();
        }
        if (couponBean.showRetain == 1) {
            AnalyticsFactory.create().action("extension-hunter-cvcustomer-APPrecouponclick")
                    .param("p", showPage)
                    .param("p2", clickSource)
                    .param("p3", clickType)
                    .param("p4", getCouponType())
                    .build();
        }
    }
}
