package com.hpbr.bosszhipin.module.boss.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.IdRes;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.hpbr.bosszhipin.app.R;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofengm
 * on 2019/5/9.
 */

public class HunterTabView extends LinearLayout {

    private LinearLayout mTabContainer;

    //滑动TAB和ViewPager的index
    private int index = 0;

    //viewpager里面的fragment
    private final List<Fragment> fragments = new ArrayList<>();
    public ViewPager mViewPager;

    public HunterTabView(Context context) {
        super(context);
    }

    public HunterTabView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public HunterTabView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    //由于ViewPage不支持在列表中复用,原因是由于ViewPager的id一样,后面的卡片不会绘制,所以手动给ViewPager设置id即可
    public void initView(@IdRes int vpId) {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_target_customer_tab, null);
        LinearLayout mVpContainer = view.findViewById(R.id.mVpContainer);
        mTabContainer = view.findViewById(R.id.mTabContainer);
        mViewPager = new ViewPager(getContext());
        mViewPager.setId(vpId);
        mVpContainer.addView(mViewPager, new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        addView(view);
    }


    public void setFragmentList(FragmentManager fragmentManager, List<Fragment> fragmentList, List<String> tabList) {
        fragments.clear();
        if (fragmentList != null) {
            fragments.addAll(fragmentList);
        }
        //刷新TAB
        refreshTab(tabList);
        //刷新Adapter
        refreshAdapter(fragmentManager, tabList);
    }


    //刷新Adapter
    private void refreshAdapter(FragmentManager fragmentManager, List<String> tabList) {
        TabPagerAdapter adapter = new TabPagerAdapter(fragmentManager, fragments);
        mViewPager.setAdapter(adapter);
        mViewPager.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                index = position;
                refreshTab(tabList);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }


    //刷新TAB
    public void refreshTab(List<String> tabList) {
        mTabContainer.removeAllViews();
        if (tabList == null) return;
        int count = tabList.size();
        for (int i = 0; i < count; i++) {
            String tabName = LList.getElement(tabList, i);
            View view = LayoutInflater.from(getContext()).inflate(R.layout.view_hunter_tab, null);
            View mTabDivider = view.findViewById(R.id.mTabDivider);
            TextView mTabName = view.findViewById(R.id.mTabName);
            mTabName.setText(tabName);

            LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.rightMargin = Scale.dip2px(getContext(), 20);
            mTabContainer.addView(view, params);

            final int tempIndex = i;

            view.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    index = tempIndex;
                    refreshTab(tabList);
                    mViewPager.setCurrentItem(index);
                }
            });

            if (i == index) {
                mTabDivider.setVisibility(VISIBLE);
                mTabName.setTextColor(ContextCompat.getColor(getContext(), R.color.app_green));
                mTabName.getPaint().setFakeBoldText(true);
                mTabName.setTextSize(16);
            } else {
                mTabDivider.setVisibility(INVISIBLE);
                mTabName.setTextColor(ContextCompat.getColor(getContext(), R.color.text_c3));
                mTabName.setTextSize(14);
                mTabName.getPaint().setFakeBoldText(false);
            }
        }
    }

    private static class TabPagerAdapter extends FragmentPagerAdapter {

        private final List<Fragment> fragmentsList = new ArrayList<>();

        private TabPagerAdapter(FragmentManager fm, List<Fragment> fragments) {
            super(fm);
            fragmentsList.clear();
            if (fragments != null) {
                fragmentsList.addAll(fragments);
            }
        }

        @Override
        public float getPageWidth(int position) {
            return 0.99999f;
        }

        /**
         * Return the Fragment associated with a specified position.
         *
         * @param position
         */
        @Override
        public Fragment getItem(int position) {
            return LList.getElement(fragmentsList, position);
        }

        @Override
        public int getCount() {
            return LList.getCount(fragmentsList);
        }
    }

}
