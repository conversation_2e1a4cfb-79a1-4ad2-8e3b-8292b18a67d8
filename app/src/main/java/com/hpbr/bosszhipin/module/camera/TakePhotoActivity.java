package com.hpbr.bosszhipin.module.camera;

import com.hpbr.bosszhipin.base.BaseActivity;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;

import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.camera.core.*;
import androidx.camera.core.ImageCapture.OutputFileOptions;
import androidx.camera.core.ImageCapture.OutputFileResults;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.exifinterface.media.ExifInterface;
import androidx.lifecycle.LiveData;

import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.camera.views.FocusImageView;
import com.hpbr.bosszhipin.module.camera.views.PreviewTouchListener;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;

import com.hpbr.bosszhipin.app.R;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.file.PathUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @author: 冯智健
 * @date: 2025年02月12日 09:57
 * @description:
 */
public class TakePhotoActivity extends BaseActivity {
    private static final String TAG = TakePhotoActivity.class.getSimpleName();
    private final ExecutorService mExecutor = Executors.newSingleThreadExecutor();
    private CameraSelector mCameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;
    private ProcessCameraProvider mCameraProvider;
    private ImageCapture mImageCapture;
    private LiveData<ZoomState> zoomState;
    private CameraControl mCameraControl;
    private Preview mPreview;
    private PreviewView previewView;
    private View maskView;
    private FocusImageView focusImageView;
    private ImageView ivCameraCapture;
    private String tips;
    private boolean showPortrait;
    private String nextPageUrl;
    private String source;

    public static void start(Context context, String tips, boolean showPortrait, String nextPageUrl, String source) {
        Intent intent = new Intent(context, TakePhotoActivity.class);
        intent.putExtra(Constants.DATA_STRING, tips);
        intent.putExtra(Constants.DATA_BOOLEAN, showPortrait);
        intent.putExtra(Constants.DATA_STRING2, nextPageUrl);
        intent.putExtra(Constants.DATA_STRING3, source);
        AppUtil.startActivity(context, intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ImmersiveUtils.immersiveStyleBar(this, true, NightUtil.isDarkMode(this));
        setContentView(R.layout.activity_take_photo);
        initParams();
        initView();
        PermissionHelper.getCameraHelper(this).setPermissionCallback((yes, permission) -> {
            if (yes) {
                previewView.post(this::initCamera);
                if (LText.notEmpty(tips)) {
                    ToastUtils.showText(tips);
                }
            } else {
                ToastUtils.showText("没有拍照权限");
                AppUtil.finishActivity(TakePhotoActivity.this);
            }
        }).requestPermission();
    }

    private void initParams() {
        Intent intent = getIntent();
        tips = intent.getStringExtra(Constants.DATA_STRING);
        showPortrait = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
        nextPageUrl = intent.getStringExtra(Constants.DATA_STRING2);
        source = intent.getStringExtra(Constants.DATA_STRING3);
    }
    
    private void initView() {
        ivCameraCapture = findViewById(R.id.iv_camera_capture);
        previewView = findViewById(R.id.preview_view);
        focusImageView = findViewById(R.id.focus_image_view);
        maskView = findViewById(R.id.mask_view);
        ivCameraCapture.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                saveImage();
            }
        });
        ImageView ivPortrait = findViewById(R.id.iv_portrait);
        ivPortrait.setVisibility(showPortrait ? View.VISIBLE : View.GONE);
        ImageView ivCameraCancel = findViewById(R.id.iv_camera_cancel);
        ivCameraCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(TakePhotoActivity.this);
            }
        });
        ImageView ivCameraSwitch = findViewById(R.id.iv_camera_switch);
        ivCameraSwitch.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (mCameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA) {
                    mCameraSelector = CameraSelector.DEFAULT_BACK_CAMERA;
                } else {
                    mCameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA;
                }
                changeCameraConfig();
            }
        });
    }

    private void initCamera() {
        try {
            mCameraProvider = ProcessCameraProvider.getInstance(this).get();
            initUseCases();
            mCameraProvider.unbindAll();
            Camera camera = mCameraProvider.bindToLifecycle(this, mCameraSelector, mPreview, mImageCapture);
            mCameraControl = camera.getCameraControl();
            zoomState = camera.getCameraInfo().getZoomState();
            initCameraListener();
        } catch (Exception e) {
            TLog.error(TAG, e, "initCamera");
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initCameraListener() {
        PreviewTouchListener previewTouchListener = new PreviewTouchListener(this);
        previewTouchListener.setCustomTouchListener(new PreviewTouchListener.GestureListener() {
            @Override
            public void zoom(float out) {
                if (zoomState.getValue() != null) {
                    float maxZoomRatio = zoomState.getValue().getMaxZoomRatio();
                    float minZoomRatio = zoomState.getValue().getMinZoomRatio();
                    float currentZoomRatio = zoomState.getValue().getZoomRatio();
                    if (currentZoomRatio * out > minZoomRatio && currentZoomRatio * out < maxZoomRatio) {
                        mCameraControl.setZoomRatio(currentZoomRatio * out);
                    }
                }
            }

            @Override
            public void click(float x, float y) {
                //暂不支持点击对焦
//                MeteringPointFactory factory = previewView.getMeteringPointFactory();
//                MeteringPoint point = factory.createPoint(x, y);
//                FocusMeteringAction action = new FocusMeteringAction.Builder(point, FocusMeteringAction.FLAG_AF)
//                        // auto calling cancelFocusAndMetering in 3 seconds
//                        .setAutoCancelDuration(3, TimeUnit.SECONDS)
//                        .build();
//                focusImageView.startFocus(new Point((int) x, (int) y));
//                ListenableFuture<?> future = mCameraControl.startFocusAndMetering(action);
//                future.addListener(() -> {
//                    try {
//                        FocusMeteringResult result = (FocusMeteringResult) future.get();
//                        if (result.isFocusSuccessful()) {
//                            focusImageView.onFocusSuccess();
//                        } else {
//                            focusImageView.onFocusFailed();
//                        }
//                    } catch (Exception e) {
//                        LogUtils.e(TAG, e.getMessage());
//                    }
//                }, executor);
            }

            @Override
            public void doubleClick(float x, float y) {
                if (zoomState.getValue() != null) {
                    float currentZoomRatio = zoomState.getValue().getZoomRatio();
                    if (currentZoomRatio > zoomState.getValue().getMinZoomRatio()) {
                        mCameraControl.setLinearZoom(0f);
                    } else {
                        mCameraControl.setLinearZoom(0.5f);
                    }
                }
            }

            @Override
            public void longClick(float x, float y) {

            }
        });
        previewView.setOnTouchListener(previewTouchListener);
    }

    /**
     * 更改相机参数
     */
    private void changeCameraConfig() {
        try {
            initUseCases();
            mCameraProvider.unbindAll();
            Camera camera = mCameraProvider.bindToLifecycle(this, mCameraSelector, mPreview, mImageCapture);
            mCameraControl = camera.getCameraControl();
            zoomState = camera.getCameraInfo().getZoomState();
        } catch (Exception e) {
            TLog.error(TAG, e, "changeCameraConfig");
        }
    }

    private void initUseCases() {
        mPreview = new Preview.Builder()
                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                .build();
        mPreview.setSurfaceProvider(previewView.getSurfaceProvider());
        mImageCapture = new ImageCapture.Builder()
                .setFlashMode(ImageCapture.FLASH_MODE_OFF)
                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .build();
    }

    private void saveImage() {
        //画面黑屏闪烁一下，营造拍照的感觉
        maskView.setVisibility(View.VISIBLE);
        ivCameraCapture.setEnabled(false);
        String fileName = String.format("IMG_%s.jpg", new SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(new Date()));
        File photoFile = new File(PathUtils.getDeleteOnAppStartCacheDir(Utils.getApp()), fileName);
        Uri savedUri = Uri.fromFile(photoFile);
        OutputFileOptions outputFileOptions = new OutputFileOptions.Builder(photoFile).build();
        mImageCapture.takePicture(outputFileOptions, mExecutor, new ImageCapture.OnImageSavedCallback() {
            @Override
            public void onImageSaved(@NonNull OutputFileResults outputFileResults) {
                AppThreadFactory.POOL.execute(() -> {
                    String imagePath = photoFile.getAbsolutePath();
                    TLog.info(TAG, "onImageSaved, path: %s", imagePath);

                    Bitmap bitmap = BitmapFactory.decodeFile(imagePath);
                    Matrix matrix = new Matrix();
                    try {
                        ExifInterface exifInterface = new ExifInterface(imagePath);
                        int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
                        matrix.postRotate(getRotateDegree(orientation));
                    } catch (Exception e) {
                        TLog.error(TAG, e, "onImageSaved, getAttributeInt");
                    }
                    if (mCameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA) {
                        matrix.postScale(-1, 1);
                    }
                    Bitmap newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                    try (FileOutputStream out = new FileOutputStream(imagePath)) {
                        newBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
                    } catch (Exception e) {
                        TLog.error(TAG, e, "onImageSaved, compress");
                    } finally {
                        if (bitmap != null && !bitmap.isRecycled()) {
                            bitmap.recycle();
                        }
                        if (newBitmap != null && !newBitmap.isRecycled()) {
                            newBitmap.recycle();
                        }
                    }
                    runOnUiThread(() -> {
                        maskView.setVisibility(View.GONE);
                        ivCameraCapture.setEnabled(true);
                        if (LText.empty(nextPageUrl)) {
                            Intent intent = new Intent();
                            intent.putExtra(Constants.DATA_URL, savedUri);
                            intent.putExtra(Constants.DATA_STRING, source);
                            setResult(RESULT_OK, intent);
                            AppUtil.finishActivity(TakePhotoActivity.this);
                        } else {
                            Bundle data = new Bundle();
                            data.putParcelable(Constants.DATA_URL, savedUri);
                            data.putString(Constants.DATA_STRING, source);
                            AppUtil.startPage(TakePhotoActivity.this, nextPageUrl, data);
                        }
                    });
                });
            }

            @Override
            public void onError(@NonNull ImageCaptureException e) {
                TLog.error(TAG, e, "saveImage onError");
            }
        });
    }

    public static int getRotateDegree(int orientation) {
        int degree = 0;
        switch (orientation) {
            case ExifInterface.ORIENTATION_ROTATE_90:
                degree = 90;
                break;
            case ExifInterface.ORIENTATION_ROTATE_180:
                degree = 180;
                break;
            case ExifInterface.ORIENTATION_ROTATE_270:
                degree = 270;
                break;
        }
        return degree;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mExecutor.shutdown();
    }
}
