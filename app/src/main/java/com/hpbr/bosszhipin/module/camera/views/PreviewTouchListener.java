package com.hpbr.bosszhipin.module.camera.views;

import android.content.Context;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;

public class PreviewTouchListener implements View.OnTouchListener {
    private GestureListener mGestureListener;
    private final GestureDetector mGestureDetector;
    private final ScaleGestureDetector mScaleGestureDetector;

    public PreviewTouchListener(Context context) {
        mGestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {

            @Override
            public void onLongPress(MotionEvent e) {
                if (mGestureListener != null) {
                    mGestureListener.longClick(e.getX(), e.getY());
                }
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                return true;
            }

            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                if (mGestureListener != null) {
                    mGestureListener.click(e.getX(), e.getY());
                }
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (mGestureListener != null) {
                    mGestureListener.doubleClick(e.getX(), e.getY());
                }
                return true;
            }
        });
        mScaleGestureDetector = new ScaleGestureDetector(context, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float delta = detector.getScaleFactor();
                if (mGestureListener != null) {
                    mGestureListener.zoom(delta);
                }
                return true;
            }
        });
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        mScaleGestureDetector.onTouchEvent(event);
        if (!mScaleGestureDetector.isInProgress()) {
            mGestureDetector.onTouchEvent(event);
        }
        return true;
    }

    public void setCustomTouchListener(GestureListener gestureListener) {
        mGestureListener = gestureListener;
    }

    public interface GestureListener {
        /**
         * 放大
         */
        void zoom(float delta);

        /**
         * 点击
         */
        void click(float x, float y);

        /**
         * 双击
         */
        void doubleClick(float x, float y);

        /**
         * 长按
         */
        void longClick(float x, float y);
    }
}
