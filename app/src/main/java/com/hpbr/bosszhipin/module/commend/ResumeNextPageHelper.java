package com.hpbr.bosszhipin.module.commend;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;

import com.hpbr.bosszhipin.common.dictionary.StaticTransferParams;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.monch.lbase.util.LList;

import java.util.List;

/**
 * 作者：ZhouYou
 * 日期：2017/3/2.
 * 详情页翻页工具
 */
public class ResumeNextPageHelper {

    public static final String ERROR_REASON = "com.hpbr.bosszhipin.ERROR_REASON";

    private static final int ACTION_TYPE_REQUEST = 1;
    private static final int ACTION_TYPE_RESPONSE = 2;

    private Context context;

    public ResumeNextPageHelper(Context context) {
        this.context = context;
    }

    public void sendRequest(int from, String tag) {
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(Constants.DATA_FROM, from);
        intent.putExtra(Constants.DATA_TAG, tag);
        intent.putExtra(Constants.DATA_INT, ACTION_TYPE_REQUEST);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }

    /**
     * 发送成功获取到数据的广播
     * @param paramList
     * @param from
     * @param hasMore
     */
    public void sendResponse(List<ParamBean> paramList, String securityId, int from, boolean hasMore) {
        final String paramsToken = String.valueOf(System.currentTimeMillis());  // 生成Token
        StaticTransferParams.getInstance().setParams(paramsToken, paramList);   // 存储数据
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(Constants.DATA_INT, ACTION_TYPE_RESPONSE);
        intent.putExtra(Constants.DATA_ENTITY, paramsToken);    // 传递Token
        intent.putExtra(Constants.DATA_FROM, from);
        intent.putExtra(Constants.DATA_BOOLEAN, hasMore);
        intent.putExtra(Constants.DATA_SECURITY_ID, securityId);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }

    /**
     * 发送失败的广播
     * @param error
     */
    public void sendResponse(String error) {
        Intent intent = new Intent(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        intent.putExtra(ERROR_REASON, error);
        ReceiverUtils.sendBroadcastSystem(context, intent);
    }

    public ResumeNextPageHelper registerRequestReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        ReceiverUtils.registerSystem(context, intentFilter, receiverRequest);
        return this;
    }

    public ResumeNextPageHelper unregisterRequestReceiver() {
        ReceiverUtils.unregisterSystem(context, receiverRequest);
        return this;
    }

    /**
     * Boss身份列表页中需要注册的广播
     * 用来接收从详情页发来的请求下一页的数据通知
     */
    private BroadcastReceiver receiverRequest = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST)) {
                int actionType = intent.getIntExtra(Constants.DATA_INT, 0);
                if (actionType != ACTION_TYPE_REQUEST) return;
                if (requestListener == null) return;
                int from = intent.getIntExtra(Constants.DATA_FROM, ParamBean.FROM_NONE);
                String tag = intent.getStringExtra(Constants.DATA_TAG);
                requestListener.request(from, tag);
            }
        }
    };

    public ResumeNextPageHelper registerResponseReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST);
        ReceiverUtils.registerSystem(context, intentFilter, receiverResponse);
        return this;
    }

    public ResumeNextPageHelper unregisterResponseReceiver() {
        ReceiverUtils.unregisterSystem(context, receiverResponse);
        return this;
    }

    /**
     * Boss身份查看的简历详情页中需要注册的广播
     * 用来接收从列表页发来的各种数据参数
     */
    private BroadcastReceiver receiverResponse = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), Constants.RECEIVER_DETAIL_NEXT_PAGE_REQUEST)) {
                int actionType = intent.getIntExtra(Constants.DATA_INT, 0);
                if (actionType != ACTION_TYPE_RESPONSE) return;
                String paramsToken = intent.getStringExtra(Constants.DATA_ENTITY);  // 获取Token
                List<ParamBean> paramList = StaticTransferParams.getInstance().getParams(paramsToken);  // 获取数据

                if (responseListener == null) return;

                String errorReason = intent.getStringExtra(ERROR_REASON);
                if (LList.isEmpty(paramList)) {
                    responseListener.responseError(TextUtils.isEmpty(errorReason) ? "加载失败" : errorReason);
                    return;
                }

                int from = intent.getIntExtra(Constants.DATA_FROM, ParamBean.FROM_NONE);
//                long userId = intent.getLongExtra(Constants.DATA_ID, 0);
//                long expectId = intent.getLongExtra(Constants.DATA_LONG, 0);
//                String secretUid = intent.getStringExtra(Constants.DATA_STRING);
//                long jobId = intent.getLongExtra(Constants.DATA_JOB_ID, 0);
                boolean hasMore = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);
                errorReason = intent.getStringExtra(ERROR_REASON);
                String securityId = intent.getStringExtra(Constants.DATA_SECURITY_ID);
                if (TextUtils.isEmpty(errorReason)) {
                    responseListener.response(paramList, securityId, from, hasMore);
                } else {
                    responseListener.responseError(errorReason);
                }
            }
        }
    };

    private OnResumeNextPageRequestListener requestListener;

    public ResumeNextPageHelper setRequestListener(OnResumeNextPageRequestListener requestListener) {
        this.requestListener = requestListener;
        return this;
    }

    public interface OnResumeNextPageRequestListener {
        void request(int from, String tag);
    }

    private OnResumeNextPageResponseListener responseListener;

    public ResumeNextPageHelper setResponseListener(OnResumeNextPageResponseListener responseListener) {
        this.responseListener = responseListener;
        return this;
    }

    public interface OnResumeNextPageResponseListener {
        void response(List<ParamBean> paramList, String securityId, int from, boolean hasMore);

        void responseError(String error);
    }
}
