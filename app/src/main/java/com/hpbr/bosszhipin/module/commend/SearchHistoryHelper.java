package com.hpbr.bosszhipin.module.commend;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangxiangdong on 2017/11/2.
 */
public class SearchHistoryHelper {

    public static final String LOCAL_RECORD_IDENTIFY = "-#hp#-";

    public static final String LOCAL_RECORD_NLP_KEY = "-#hpbr#-";
    private static final int MAX_NUM_OF_HISTORIES = 5;

    private static final String SP_KEY_NLP_MAP = "sp_search_nlp_map";

    private static SearchHistoryHelper workLocation;
    private final List<String> mHistories = new ArrayList<>();

    private final Map<String, String> mHistoryNlp = new HashMap<>();
    private String KEY_HISTORY_RECORD;

    private SearchHistoryHelper() {
        this(UserManager.getUserRole().get());
    }

    private SearchHistoryHelper(int role) {
        this("com.hpbr.bosszhipin.HISTORY_RECORD_" + UserManager.getUID() + "_" + role);
    }

    private SearchHistoryHelper(String key) {
        KEY_HISTORY_RECORD = key;
    }

    public static SearchHistoryHelper getMakeWorkLocation() {
        if (workLocation == null) {
            workLocation = newSearchHistoryHelper();
            workLocation.KEY_HISTORY_RECORD = Constants.HISTORY_SEARCH_WORK_LOCATION;
        }
        return workLocation;
    }

    public static SearchHistoryHelper newSearchHistoryHelper() {
        return new SearchHistoryHelper();
    }

    public static SearchHistoryHelper newSearchHistoryHelper(@NonNull String keyForHistory) {
        return new SearchHistoryHelper(keyForHistory);
    }

    public static SearchHistoryHelper newSearchHistoryHelper(int role) {
        return new SearchHistoryHelper(role);
    }

    /**
     * 刷新历史搜索数据
     */
    @NonNull
    public synchronized List<String> refresh() {
        /* 从本地检索最新的数据 */
        String string = SpManager.get().user().getString(KEY_HISTORY_RECORD, "");
        mHistories.clear();
        List<String> c = StringUtil.splitKeywords(string);
        mHistories.addAll(c);
        String nlpString = SpManager.get().user().getString(SP_KEY_NLP_MAP, "");
        String[] wordWithNlps = nlpString.split(LOCAL_RECORD_NLP_KEY);
        for (String wordWithNlp : wordWithNlps) {
            String[] words = wordWithNlp.split(LOCAL_RECORD_IDENTIFY);
            if (words.length == 2) {
                mHistoryNlp.put(words[0], words[1]);
            }
        }
        return c;
    }

    public String getNlpBySearchWord(String word) {
        return mHistoryNlp.get(word);
    }

    /**
     * 保存搜索词到历史记录
     *
     * @param word 搜索词
     */
    @NonNull
    public synchronized List<String> save(String word) {
        return save(word, MAX_NUM_OF_HISTORIES);
    }

    @NonNull
    public synchronized List<String> save(String word, int maxNum) {
        return save(word, "", maxNum);
    }

    /**
     * 保存搜索词到历史记录
     * <p>
     * word中可能携带 自然语言参数(使用)
     * ex: java 和 java*#hp#*\{jaon\} 属于相同搜索历史数据
     * 移除时, 需要移除历史的java 或者 携带了不同自然语言参数的记录
     * 新增时, 携带了自然语言参数的数据优先级高于普通搜索词
     *
     * @param word 搜索词
     */
    @NonNull
    public synchronized List<String> save(String word,String nlpText, int maxNum) {
        // 1. 移除已经搜过的词（如果有）

        mHistories.remove(word);
        mHistoryNlp.remove(word);

        // 2. 最新搜索的放在第一位

        mHistories.add(0, word);
        if (!TextUtils.isEmpty(nlpText)) {
            mHistoryNlp.put(word, nlpText);
        }
        int count = LList.getCount(mHistories);
        /* 3. 最多保存10个搜索记录，超出则移除最后一个 */
        if (count > maxNum) {
            String removeSearchText = mHistories.remove(count - 1);
            if (!TextUtils.isEmpty(removeSearchText)) {
                mHistoryNlp.remove(removeSearchText);
            }
        }
        // 4. 保存搜索历史到本地
        String string = StringUtil.joinKeywords(mHistories);
        SpManager.get().user().edit().putString(KEY_HISTORY_RECORD, string).apply();
        String nlpString = nlpMapToString(mHistoryNlp);
        SpManager.get().user().edit().putString(SP_KEY_NLP_MAP, nlpString).apply();
        return mHistories;
    }

    private String nlpMapToString(Map<String, String> map) {
        if (map == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        boolean isFirst = true;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry != null && !TextUtils.isEmpty(entry.getKey()) && !TextUtils.isEmpty(entry.getValue())) {
                if (!isFirst) {
                    sb.append(LOCAL_RECORD_NLP_KEY);
                }
                sb.append(entry.getKey()).append(LOCAL_RECORD_IDENTIFY).append(entry.getValue());
                isFirst = false;
            }
        }
        return sb.toString();
    }

    /**
     * 从历史记录移除某个搜索词
     *
     * @param word 搜索词
     */
    @NonNull
    public synchronized List<String> remove(String word) {
        // 1. 移除已经搜过的词（如果有）
        mHistories.remove(word);
        mHistoryNlp.remove(word);
        // 4. 保存搜索历史到本地
        String string = StringUtil.joinKeywords(mHistories);
        SpManager.get().user().edit().putString(KEY_HISTORY_RECORD, string).apply();
        String nlpString = nlpMapToString(mHistoryNlp);
        SpManager.get().user().edit().putString(SP_KEY_NLP_MAP, nlpString).apply();
        return mHistories;
    }

    /**
     * 清空历史记录
     */
    @NonNull
    public synchronized List<String> clear() {
        SpManager.get().user().edit().putString(KEY_HISTORY_RECORD, "").apply();
        SpManager.get().user().edit().putString(SP_KEY_NLP_MAP, "").apply();
        mHistories.clear();
        mHistoryNlp.clear();
        return mHistories;
    }
}
