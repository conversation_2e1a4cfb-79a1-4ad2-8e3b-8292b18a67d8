package com.hpbr.bosszhipin.module.commend.activity.advanced.common;

import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.commend.entity.ExtraFiltersBean;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.BossSearchGetFilterConditionRequest;
import net.bosszhipin.api.BossSearchGetFilterConditionResponse;
import net.bosszhipin.api.GetJobChangeInfoResponse;
import net.bosszhipin.api.bean.SearchFilterSwitchInfoBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

public final class BossFilterConditionHelper {
    private BossSearchGetFilterConditionResponse condition;
    //是否启用 牛人职位要求 筛选项
    private boolean mEnableGeekJobRequirments;
    private boolean isPartTimeJob;
    private long currentJobId;

    private BossFilterConditionHelper() {
    }

    public static BossFilterConditionHelper getInstance() {
        return SingletonHolder.mInstance;
    }

    public BossFilterConditionHelper setEnableGeekJobRequirments(boolean enableGeekJobRequirments) {
        mEnableGeekJobRequirments = enableGeekJobRequirments;
        return this;
    }

    public BossFilterConditionHelper setCurrJobId(long jobId) {
        if (this.currentJobId != jobId) {
            this.isPartTimeJob = isPartTimeJob(jobId);
            this.currentJobId = jobId;
        }
        return this;
    }

    public boolean isPartTimeJob() {
        return this.isPartTimeJob;
    }

    public boolean isDisableGeekJobRequirement() {
        return LList.isEmpty(getGeekJobRequirements());
    }

    public List<FilterBean> getGeekJobRequirements() {
        if (condition != null && mEnableGeekJobRequirments) {
            return condition.geekJobRequirements;
        }
        return null;
    }

    public List<FilterBean> getSwitchFreq() {
        if (condition != null) {
            return condition.switchFreq;
        }
        return null;
    }

    public List<FilterBean> getGender() {
        if (condition != null) {
            return condition.gender;
        }
        return null;
    }

    public List<FilterBean> getViewResume() {
        if (condition != null) {
            return condition.viewResume;
        }
        return null;
    }

    public List<FilterBean> getExchangeResume() {
        if (condition != null) {
            return condition.exchangeResume;
        }
        return null;
    }

    public List<FilterBean> getManageExperience() {
        if (condition != null) {
            return condition.manageExperience;
        }
        return null;
    }

    public List<FilterBean> getWithDesignImgs() {
        if (condition != null) {
            return condition.withDesignImgs;
        }
        return null;
    }


    private static class SingletonHolder {
        private static final BossFilterConditionHelper mInstance = new BossFilterConditionHelper();
    }


    /**
     * 获取 新增筛选数据
     * 会前置获取筛选项目，尽可能保证 可用状态
     */
    public void requestFilterData() {
        if (condition != null) {
            return;
        }

        BossSearchGetFilterConditionRequest request = new BossSearchGetFilterConditionRequest(new ApiRequestCallback<BossSearchGetFilterConditionResponse>() {
            @Override
            public void onSuccess(ApiData<BossSearchGetFilterConditionResponse> data) {
                if (data != null && data.resp != null && condition == null) {
                    condition = data.resp;
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }

    public static String getFilterNameByCode(long code, List<FilterBean> beanList) {
        if (LList.isEmpty(beanList)) {
            return "";
        }
        String name = "";
        for (FilterBean bean : beanList) {
            if (bean != null && bean.code == code) {
                name = bean.name;
                break;
            }
        }
        return name;
    }

    private boolean isPartTimeJob(long jobId) {
        if (jobId == 0) {
            return false;
        }
        List<JobBean> validJobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
        int jobCount = LList.getCount(validJobList);
        if (jobCount == 0) {
            return false;
        }

        for (int i = 0; i < jobCount; i++) {
            JobBean bean = validJobList.get(i);
            if (bean != null && bean.id == jobId) {
                return JobStatusChecker.isPartTimeJob(bean.jobType);
            }
        }
        return false;
    }

    public List<FilterBean> schoolLevelListWithExt = new ArrayList<>();

    private List<ExtraFiltersBean> getExtraFilters() {
        return condition == null ? null : condition.extraFilters;
    }

    public static final String KEY_SCHOOL_LEVEL = "schoolLevel";
    public static final String NAME_OVER_SEA_WORK_EXP = "海外经历";
    public static final String KEY_OVER_SEA_WORK_EXP = "overSeaWorkExperience";
    public static final String NAME_OVER_SEA_WORK_LANG = "语言能力";
    public static final String KEY_OVER_SEA_WORK_LANG = "overSeaWorkLanguage";

    private List<FilterBean> getExtraFiltersByName(List<ExtraFiltersBean> sourceList, String name) {
        if (LText.empty(name) || LList.isEmpty(sourceList)) {
            return null;
        }
        for (ExtraFiltersBean item : sourceList) {
            if (item != null && LText.equal(name, item.filterName)) {
                return item.filterItems;
            }
        }
        return null;
    }


    /**
     * 返回所有的院校list
     */
    public List<FilterBean> getSchoolLevelWithExt() {
        if (schoolLevelListWithExt.isEmpty()) {
            synchronized (BossFilterConditionHelper.class) {
                if (schoolLevelListWithExt.isEmpty()) {
                    schoolLevelListWithExt.addAll(VersionAndDatasCommon.getInstance().getSchoolLevelList());
                    List<FilterBean> extSchoolLevel = getExtraFiltersByName(getExtraFilters(), KEY_SCHOOL_LEVEL);
                    if (LList.isNotEmpty(extSchoolLevel)) {
                        schoolLevelListWithExt.addAll(extSchoolLevel);
                    }
                }
            }
        }
        return schoolLevelListWithExt;
    }


    /**
     * 主要用于搜索结果页、筛选页面的一些状态控制
     */
    private GetJobChangeInfoResponse mGetJobChangeInfoResponseForCurrentJob;

    public void updateGetJobChangeInfoResponseForCurrentJob(GetJobChangeInfoResponse getJobChangeInfoResponse) {
        this.mGetJobChangeInfoResponseForCurrentJob = getJobChangeInfoResponse;
    }

    private SearchFilterSwitchInfoBean getSearchFilterSwitchInfoBeanForCurrentJob() {
        return mGetJobChangeInfoResponseForCurrentJob == null ? null : mGetJobChangeInfoResponseForCurrentJob.searchFilterCanShow;
    }

    private List<ExtraFiltersBean> getExtraFiltersBeanForCurrentJob() {
        return mGetJobChangeInfoResponseForCurrentJob == null ? null : mGetJobChangeInfoResponseForCurrentJob.extraFilters;
    }

    public boolean isEnableWithDesignImgsForCurrentJob() {
        SearchFilterSwitchInfoBean searchFilterSwitchInfoBeanForCurrentJob = getSearchFilterSwitchInfoBeanForCurrentJob();
        return searchFilterSwitchInfoBeanForCurrentJob != null && searchFilterSwitchInfoBeanForCurrentJob.withDesignImgs;
    }

    public boolean getManageExperienceForCurrentJob() {
        SearchFilterSwitchInfoBean searchFilterSwitchInfoBeanForCurrentJob = getSearchFilterSwitchInfoBeanForCurrentJob();
        return searchFilterSwitchInfoBeanForCurrentJob != null && searchFilterSwitchInfoBeanForCurrentJob.manageExperience;
    }


    public List<FilterBean> getSchoolLevelListWithExtForCurrentJob() {
        List<FilterBean> list = new ArrayList<>(VersionAndDatasCommon.getInstance().getSchoolLevelList());

        List<FilterBean> extSchoolLevelList = getExtraFiltersByName(getExtraFiltersBeanForCurrentJob(), KEY_SCHOOL_LEVEL);
        if (LList.isNotEmpty(extSchoolLevelList)) {
            list.addAll(extSchoolLevelList);
        }
        return list;
    }


    public List<FilterBean> getOverSeaExpWithExtForCurrentJob() {
        List<FilterBean> list = new ArrayList<>();

        List<FilterBean> extList = getExtraFiltersByName(getExtraFiltersBeanForCurrentJob(), KEY_OVER_SEA_WORK_EXP);
        if (LList.isNotEmpty(extList)) {
            list.addAll(extList);
        }
        return list;
    }

    public List<FilterBean> getOverSeaLangWithExtForCurrentJob() {
        List<FilterBean> list = new ArrayList<>();

        List<FilterBean> extList = getExtraFiltersByName(getExtraFiltersBeanForCurrentJob(), KEY_OVER_SEA_WORK_LANG);
        if (LList.isNotEmpty(extList)) {
            list.addAll(extList);
        }
        return list;
    }

}
