
package com.hpbr.bosszhipin.module.commend.activity.search;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.adapter.CityIndexAdapter;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.commend.view.MultiCityView;
import com.hpbr.bosszhipin.module.common.popup.dialog.ExpectCityModifyGuideDialog;
import com.hpbr.bosszhipin.module.main.adapter.AllServiceSelectedLabelAdapterCity;
import com.hpbr.bosszhipin.module.main.listener.AllServiceCheckedCityListener;
import com.hpbr.bosszhipin.module.my.activity.boss.location.CitySearchFragment;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek.view.NextButton;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.CitySortUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.ApiCallbackSafeCallback;
import net.bosszhipin.api.ExpectPositionChangeCityTipRequest;
import net.bosszhipin.api.GetAppCityListResponse;
import net.bosszhipin.api.GetAttrByIpRequest;
import net.bosszhipin.api.GetAttrByIpResponse;
import net.bosszhipin.api.GetGeekDirectionGuideResponse;
import net.bosszhipin.api.GetRecommendCityRequest;
import net.bosszhipin.api.GetRecommendCityResponse;
import net.bosszhipin.api.bean.ChangeExpectCityBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.text.HtmlCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Created by zhouyou on 2015/10/31.
 */
public class CitySelectActivity extends BaseActivity2 implements IOnCityClickListener, AllServiceCheckedCityListener, View.OnClickListener {

    public static final int REQ_CITY_SELECT = 10001;
    public static final int REQ_CITY_SELECT_2 = 999;
    public static final int REQ_CITY_SELECT_3 = 9999;
    public static final int REQ_CITY_SELECT_FOR_GET_LOCAL = 10002;
    public static final int REQ_CITY_SELECT_FOR_F1_SMALLCITY = 10003;
    public static final int REQ_CITY_SELECT_FOR_CHANGE_EXPECT_CITY = 10006;
    public static final int HANDLE_REFRESH_TITLE = 666;

    public static final String RESULT_PARAM_TO_IS_FINISH="result_param_to_is_finish";

    public static final String KEY_REQUEST_PARAMS = "KEY_REQUEST_PARAMS";

    public static final int FROM_F1_MIXED_EXPECT = 1; //F1综合推荐
    public static final int FROM_F1_SINGE_EXPECT = 2; //单期望推荐（含推荐、最新）
    public static final int FROM_SEARCH_PAGE = 3; //搜索
    public static final int FROM_EXPECT_PAGE_CITY = 4;// 3 期望选择城市
    public static final int FROM_OTHER_PAGE = 5; //4 其他
    public static final int FROM_INTEREST_LOCATION = 6; //5 学生选择感兴趣城市
    public static final int FROM_GEEK_F1_FILTER = 7; //F1 进入筛选逻辑
    public static final int FROM_GEEK_F1_MIXED_FILTER = 8; //F1 进入筛选逻辑
    public static final int FROM_GEEK_F1_CHANGE_EXPECT_CITY = 9; //F1场景进入修改期望城市
    public static final int FROM_GEEK_SEARCH_CHANGE_EXPECT_CITY = 10; //F1场景进入修改期望城市

    private static final String[] INDEXER = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    //region Views
    private ProgressBar progressBar;
    private ListView listView;
    private MTextView tvOverlay;
    private LinearLayout llIndex;
    private AppTitleView titleView;
    private NextButton btnJump;
    private MultiCityView multiCityView;
    private TextView tvHeaderCounter;
    private AppBarLayout appBarLayout;
    private LinearLayout headerLayout;
    private MTextView tvHeaderTitle;
    private MTextView subTitle;
    //endregion

    @NonNull
    private final Map<String, Integer> selector = new ArrayMap<>(); // 存放含有索引字母的位置
    private CityIndexAdapter adapter;

    private int startX = 0;
    private int startY = 0;
    private ZPUIConstraintLayout clbottom;
    private LinearLayoutManager layoutManager;
    private AllServiceSelectedLabelAdapterCity selectedLabelAdapter;
    private Button mBtnSubmit;

    @NonNull
    private final int[] locs = new int[2];

    @NonNull
    private final Handler handler = AppThreadFactory.createMainHandler(new Handler.Callback() {
        @SuppressLint("NotifyDataSetChanged")
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == 1000) {
                if (!ActivityUtils.isValid(CitySelectActivity.this)) {
                    return false;
                }

                progressBar.setVisibility(View.GONE);

                List<LevelBean> cityList;
                if (UserManager.isBossRole()) {
                    if (isShowClear()) {
                        cityList = VersionAndDatasCommon.getInstance().getBSortCityWithClearList(false);
                    } else {
                        cityList = VersionAndDatasCommon.getInstance().getBSortCityList(false);
                    }
                } else {
                    if (isFilterSubCity()) {
                        cityList = VersionAndDatasCommon.getInstance().getCNormalCitySortList();
                    } else {
                        cityList = VersionAndDatasCommon.getInstance().getSortCityList2();
                    }
                }
                if (!LList.isEmpty(cityList)) {

                    // 循环字母表，找出newPersons中对应字母的位置
                    int moveUpSize = 0;
                    for (String s : INDEXER) {
                        //noinspection ConstantConditions
                        int citySize = cityList.size();
                        boolean isEmptyCityList = true;
                        for (int i = 0; i < citySize; i++) {
                            LevelBean item = cityList.get(i);
                            if (item == null || TextUtils.isEmpty(item.name)) continue;
                            if (item.name.equals(s) &&
                                    item.subLevelModeList != null &&
                                    !item.subLevelModeList.isEmpty()) {
                                selector.put(s, i - moveUpSize);
                                isEmptyCityList = false;
                            }
                        }
                        if (isEmptyCityList) {
                            moveUpSize++;
                        }
                    }

                    drawIndexView();

                    //region 初始化已选择城市
                    final ArrayList<LevelBean> intentSelectedCities = getIntentSelectedCities();
                    //当第一个城市是主期望城市 因为数据不全 要重新替换主期望
                    //noinspection ConstantConditions
                    if (isFirstExpectation() && LList.getCount(intentSelectedCities) > 0 && intentSelectedCities.get(0) != null) {
                        for (LevelBean firstLevel : cityList) {
                            List<LevelBean> secondLevels = firstLevel.subLevelModeList;
                            if (secondLevels == null) continue;

                            for (LevelBean secondLevel : secondLevels) {
                                if (secondLevel.code == intentSelectedCities.get(0).code) {
                                    secondLevel.setChecked(true);
                                    intentSelectedCities.remove(0);
                                    intentSelectedCities.add(0, secondLevel);
                                    break;
                                }
                            }
                        }

                        multiCityView.setSelectedCities(getIntentSelectedCities());
                    }

                    handleRecommend(cityList);
                    adapter.setData(cityList);

                    if (isUseGray() && LList.getCount(intentSelectedCities) > 0 && intentSelectedCities.get(0) != null) {

                        if (isFreshData()) {
                            for (int i = 0; i < intentSelectedCities.size(); i++) {
                                for (LevelBean firstLevel : cityList) {
                                    List<LevelBean> secondLevels = firstLevel.subLevelModeList;
                                    if (secondLevels == null) continue;

                                    for (LevelBean secondLevel : secondLevels) {
                                        if (secondLevel.code == intentSelectedCities.get(i).code) {
                                            secondLevel.setChecked(true);
                                            intentSelectedCities.remove(i);
                                            intentSelectedCities.add(i, secondLevel);
                                        }
                                    }
                                }
                            }
                        }

                        mSelectedItem.addAll(intentSelectedCities);
                        selectedLabelAdapter.setData(mSelectedItem);
                        selectedLabelAdapter.notifyDataSetChanged();
                        notifyAllState();
                        refreshUiStatus();
                    }

                    if (intentSelectedCities != null && intentSelectedCities.size() > 0) {
                        adapter.syncCityCheckStatus(intentSelectedCities);
                    }
                    //endregion

                    adapter.notifyDataSetChanged();
                }
            }else if (msg.what ==HANDLE_REFRESH_TITLE){
                handleTitle();
            }
            return true;
        }
    });

    private GetRecommendCityResponse recommendCityResponse;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_city_select);

        initViews();

        initAppTitleView();

        initAppBarLayout();

        initHeaderLayout();

        initMultiSelection();

        initTitle();

        refreshUiStatus();

        initIndex();

        initSearchBox();

        initCityList();

        initBottomShowSelect();
    }

    private final ArrayList<LevelBean> mSelectedItem = new ArrayList<>();

    private void initBottomShowSelect() {
        if (!isUseGray()) {
            return;
        }
        clbottom.setVisibility(View.VISIBLE);
        mBtnSubmit = findViewById(R.id.btn_submit);
        mBtnSubmit.setOnClickListener(this);
        // 底部已选岗位
        RecyclerView rvServiceLabel = findViewById(R.id.rv_service_label);
        layoutManager = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);
        rvServiceLabel.setLayoutManager(layoutManager);
        selectedLabelAdapter = new AllServiceSelectedLabelAdapterCity(this);
        rvServiceLabel.setAdapter(selectedLabelAdapter);
    }

    //region Initialization
    private void initViews() {
        appBarLayout = findViewById(R.id.appBarLayout);
        headerLayout = findViewById(R.id.headerLayout);
        titleView = findViewById(R.id.title_view);
        listView = findViewById(R.id.list_view);
        tvOverlay = findViewById(R.id.tv_overlay);
        llIndex = findViewById(R.id.ll_index);
        btnJump = findViewById(R.id.btn_jump);
        multiCityView = findViewById(R.id.multi_city_view);
        tvHeaderTitle = findViewById(R.id.tv_header_title);
        subTitle = findViewById(R.id.subTitle);
        subTitle.setOnClickListener(this);
        tvHeaderCounter = findViewById(R.id.tv_header_counter);
        progressBar = findViewById(R.id.progress);
        clbottom = findViewById(R.id.rl_bottom);
    }

    private void initAppTitleView() {
        titleView.setBackClickListener(isUpGlide() ? R.mipmap.ic_action_close_black : R.mipmap.ic_action_back_black,
                new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        finishCurrentPage();
                    }
                });
        titleView.setDividerInvisible();

        if (isMultiSelectionEnabled() && !isUseGray()) {
            titleView.setActionButtonListener("确定", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    // 保存多选的城市
                    onConfirm();
                }
            });
        }
    }

    private void initHeaderLayout() {
        AppBarLayout.LayoutParams layoutParams = (AppBarLayout.LayoutParams) headerLayout.getLayoutParams();
        if (getRequestParams().isDisableNestedScrolling()) {
            // 清除 layout_scrollFlags
            layoutParams.setScrollFlags(0);
        } else {
            // 使用默认的 layout_scrollFlags
            layoutParams.setScrollFlags(AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL
                    | AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS
                    | AppBarLayout.LayoutParams.SCROLL_FLAG_SNAP);
        }
    }

    private void initAppBarLayout() {
        if (isMultiSelectionEnabled() || isEnableAppTitle()) {
            appBarLayout.addOnOffsetChangedListener((abl, verticalOffset) -> {
                int scrollRange = appBarLayout.getTotalScrollRange();
                int absVerticalOffset = Math.abs(verticalOffset);
                if (absVerticalOffset >= scrollRange) {
                    // 收起
                    final int count = LList.getCount(getSelectedCities());
                    titleView.setTitle(
                            count > 0 ? String.format(
                                    Locale.getDefault(),
                                    "选择城市%s%d",
                                    StringUtil.COMMON_SEPERATOR2,
                                    count) : "选择城市"
                    );
                } else if (absVerticalOffset <= 0) {
                    // 展开
                    titleView.setTitle(null);
                }
            });
        }
    }

    private void initCityList() {
        adapter = new CityIndexAdapter(this, getRequestParams().isShowAll());
        adapter.setOnCityClickListener(this);
        listView.setAdapter(adapter);

        getCurrentCityByIp();
    }

    private void initIndex() {
        tvOverlay.setVisibility(View.GONE);
        llIndex.setBackgroundColor(Color.WHITE);
        llIndex.getBackground().mutate().setAlpha(0);
    }

    private void initMultiSelection() {
        if (isMultiSelectionEnabled()) {
            multiCityView.setMaxCityCount(getRequestParams().getMaxCityCount(), getRequestParams().getOverrunToast());
            multiCityView.setCallback(cityItem -> {
                // 刷新列表选中状态
                adapter.syncCityCheckStatus(cityItem);
                adapter.notifyDataSetChanged();

                refreshUiStatus();
            });
            multiCityView.setAutoSetVisibility(getRequestParams().isEnableMultiSelectionIndicator());
            if (!isFirstExpectation()) {
                multiCityView.setSelectedCities(getIntentSelectedCities());
            }

            btnJump.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    onConfirm();
                }
            });
            btnJump.setEnable(false);
        }
    }

    private void initTitle() {

        tvHeaderTitle.setVisibility(getCityTitleVisibility());
        final boolean isMultiSelectionEnabled = isMultiSelectionEnabled();
        int maxCityCount = getRequestParams().getMaxCityCount();
        tvHeaderCounter.setVisibility(isMultiSelectionEnabled && maxCityCount > 1 ? View.VISIBLE : View.GONE);
    }

    private  ChangeExpectCityBean mChangeExpectCity;

    private void handleTitle() {
        if (ActivityUtils.isInvalid(CitySelectActivity.this)) {
            return;
        }
        String cityTitle;
        if (mChangeExpectCity != null) {
            cityTitle = mChangeExpectCity.title;

            String content = mChangeExpectCity.content;
            if (!LList.isEmpty(mChangeExpectCity.contentHighlight) && !TextUtils.isEmpty(content)) {
                SpannableStringBuilder builder = ViewCommon.setTextHighLight(content, mChangeExpectCity.contentHighlight, ContextCompat.getColor(this, R.color.app_green_dark));
                if (builder != null) {
                    subTitle.setText(builder, View.GONE);
                } else {
                    subTitle.setText(content, View.GONE);
                }
            } else {
                subTitle.setText(content, View.GONE);
            }
            handleShowAnalytics();
        } else {
            cityTitle = getCityTitle();
            subTitle.setText(getSubTitle(), View.GONE);
        }

        if (!LText.empty(cityTitle)) {
            tvHeaderTitle.setText(cityTitle);
        } else {
            tvHeaderTitle.setText("选择城市");
        }
    }

    private void handleShowAnalytics(){
        if (mChangeExpectCity!=null){
            if (!TextUtils.isEmpty(mChangeExpectCity.title) && TextUtils.isEmpty(mChangeExpectCity.content)){
                AnalyticsFactory.create().action("geek-expect-setcity-show").param("p",getAnalyticsSort()).build();
            }else if (!TextUtils.isEmpty(mChangeExpectCity.title) && !TextUtils.isEmpty(mChangeExpectCity.content)){
                AnalyticsFactory.create().action("geek-expect-set-show").param("p",getAnalyticsSort()).build();
            }
        }
    }
    //endregion

    private void onConfirm() {
        // 回传选中的多个城市（一个或者一个以上）
        final List<LevelBean> selectedCities = isUseGray() ? mSelectedItem : getSelectedCities();
        if (LList.hasElement(selectedCities)) {
            onCitiesSelected(new ArrayList<>(selectedCities));

            AnalyticsFactory.create().action("select_city_info_report")
                    .param("p", getGeekId())
                    .param("p2", getMgeParamBySource(getSourceFrom()))
                    .param("p3", getMgeParamCityList(selectedCities))
                    .build();
        } else if (isClearable()) {
            // 可以清空选择的城市
            onCitiesSelected(new ArrayList<>());
        } else {
            if (isUseGray()) {
                ToastUtils.showText("请至少选择一个你感兴趣的城市");
            } else {
                ToastUtils.showText("请选择城市");
            }
        }
    }

    private int getSortType(){
        CitySelectIntentParams params = getRequestParams();
        if (params!=null){
         return    params.getSortType();
        }
        return 0;
    }


    @NonNull
    private CitySelectIntentParams getRequestParams() {
        //noinspection ConstantConditions
        return (CitySelectIntentParams) getIntent().getSerializableExtra(KEY_REQUEST_PARAMS);
    }

    //region RequestParams
    private boolean isFilterSubCity() {
        return getRequestParams().isFilterSubCity();
    }

    private boolean isUpGlide() {
        return getRequestParams().isUpGlide();
    }

    private boolean isShowClear() {
        return getRequestParams().isShowClear();
    }

    private boolean showSearchBox() {
        return getRequestParams().isShowSearchBox();
    }

    private boolean showRecommend() {
        return getRequestParams().isSupportRecommendAndHistory();
    }

    private int getSourceFrom() {
        return getRequestParams().getSourceFrom();
    }

    private  boolean isChangeExpectCity(){
        int source=getSourceFrom();
        return (source ==FROM_GEEK_F1_CHANGE_EXPECT_CITY||source ==FROM_GEEK_SEARCH_CHANGE_EXPECT_CITY) && mChangeExpectCity!=null && !TextUtils.isEmpty(mChangeExpectCity.title);
    }

    @Nullable
    private String getCityTitle() {
        return getRequestParams().getCityTitle();
    }

    private int getCityTitleVisibility() {
        return getRequestParams().getCityTitleVisibility();
    }

    private boolean isUseGray() {
        return getRequestParams().isUseGray();
    }

    private boolean isFreshData() {
        return getRequestParams().isFreshData();
    }

    private boolean isFirstExpectation() {
        return getRequestParams().isFirstExpectation();
    }

    @Nullable
    private String getSubTitle() {
        return getRequestParams().getSubTitle();
    }

    // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=123350487
    // 选择多个城市
    @Override
    public boolean isMultiSelectionEnabled() {
        return getRequestParams().isEnableMultiSelection();
    }

    private boolean isEnableAppTitle() {
        return getRequestParams().isEnableAppTitle();
    }

    @Nullable
    private String getUserSelectedPositionCode() {
        return getRequestParams().getUserSelectedPositionCode();
    }

    @Nullable
    private ArrayList<LevelBean> getIntentSelectedCities() {
        return getRequestParams().getSelectedCities();
    }
    //endregion

    @MainThread
    private void getCurrentCityByIp() {
        new GetAttrByIpRequest(new ApiRequestCallback<GetAttrByIpResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                progressBar.setVisibility(View.VISIBLE);
            }

            @Override
            public void handleInChildThread(ApiData<GetAttrByIpResponse> data) {
                super.handleInChildThread(data);

                if (showRecommend()) {
                    requestRecommendAndHistoryCity(data.resp);
                } else {
                    handler.obtainMessage(HANDLE_REFRESH_TITLE).sendToTarget();
                    requestHotCities(data.resp);
                }


            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);

                if (showRecommend()) {
                    requestRecommendAndHistoryCity(null);
                } else {
                    handler.obtainMessage(HANDLE_REFRESH_TITLE).sendToTarget();
                    requestHotCities(null);
                }
            }

            @Override
            public void onSuccess(ApiData<GetAttrByIpResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        }).execute();
    }
    @MainThread
    private void getExpectPositionChangeCityTip(@NonNull LevelBean item) {
        AnalyticsFactory.create().action("geek-expect-setcity-click").param("p",item.name).param("p2",item.code).param("p3",item.cityType).param("p4",getAnalyticsSort()).build();
            ExpectPositionChangeCityTipRequest request = new ExpectPositionChangeCityTipRequest(new ApiCallbackSafeCallback<GetGeekDirectionGuideResponse>() {

                @Override
                public void onSuccess(ApiData<GetGeekDirectionGuideResponse> data) {
                    if (ActivityUtils.isInvalid(CitySelectActivity.this)){
                        return;
                    }
                    if (data!=null){
                        GetGeekDirectionGuideResponse response =data.resp;
                        if (response!=null&& response.extraMap!=null){
                            response.clickCloseNoF1Guide=true;
                            response.analyticsSortP =getAnalyticsSort();
                            showGuideChangeExpectCity(response);
                        }else {
                            ToastUtils.showText("您的期望工作城市跟所选城市重复，请选择其他城市进行替换");
                        }
                    }
                }

                @Override
                public void onComplete() {

                }
                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            request.cityCode =getCityCode(item);
            request.subLocation =getSubLocation(item);
            request.execute();
    }

    private long getCityCode(@NonNull LevelBean item){
        return  item.parentCode==0?item.code:item.parentCode;
    }

    private long getSubLocation(@NonNull LevelBean item){
        return  item.parentCode!=0?item.code:0;
    }


    private void showGuideChangeExpectCity(GetGeekDirectionGuideResponse resp){
        ExpectCityModifyGuideDialog expectCityModifyGuideDialog =ExpectCityModifyGuideDialog.getInstance(resp);
        expectCityModifyGuideDialog.setDialogListener(() -> {
            Intent intent = getIntent();
            setResult(RESULT_OK, intent);
            finishCurrentPage();
        });
        expectCityModifyGuideDialog.setFrom(ExpectCityModifyGuideDialog.FROM_SWITCH_CITY);
        expectCityModifyGuideDialog.show(getSupportFragmentManager(),ExpectCityModifyGuideDialog.class.getSimpleName());

    }
    private void requestRecommendAndHistoryCity(GetAttrByIpResponse response) {
        if (!ActivityUtils.isValid(this)) {
            return;
        }

        GetRecommendCityRequest request = new GetRecommendCityRequest(new ApiRequestCallback<GetRecommendCityResponse>() {


            @Override
            public void handleInChildThread(ApiData<GetRecommendCityResponse> data) {
                super.handleInChildThread(data);
                requestHotCities(response);
                recommendCityResponse = data.resp;
                if (data.resp != null) {
                    mChangeExpectCity = data.resp.changeExpectCity;
                }
            }

            @Override
            public void handleErrorInChildThread(ErrorReason reason) {
                super.handleErrorInChildThread(reason);
                requestHotCities(response);
                recommendCityResponse = null;
            }

            @Override
            public void onSuccess(ApiData<GetRecommendCityResponse> data) {

            }

            @Override
            public void onComplete() {
                    handleTitle();
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });

        int requestSource = 0;
        int source = getSourceFrom();
        if (source == FROM_EXPECT_PAGE_CITY) {
            requestSource = GetRecommendCityRequest.SOURCE_EXPECT_CITY_1;
        } else if (source == FROM_INTEREST_LOCATION) {
            requestSource = GetRecommendCityRequest.SOURCE_F1_CITY_3;
        } else if (source ==FROM_GEEK_F1_MIXED_FILTER || source ==FROM_GEEK_F1_FILTER){
            requestSource = GetRecommendCityRequest.SOURCE_F1_CITY_5;
        }else if (source ==FROM_GEEK_F1_CHANGE_EXPECT_CITY){
            requestSource = GetRecommendCityRequest.SOURCE_F1_CITY_4;
        }else if (source ==FROM_SEARCH_PAGE){
            requestSource = GetRecommendCityRequest.SOURCE_F1_CITY_7;
        }else if (source ==FROM_GEEK_SEARCH_CHANGE_EXPECT_CITY){
            requestSource = GetRecommendCityRequest.SOURCE_F1_CITY_6;
        }

        request.source = requestSource;
        request.execute();
    }

    @WorkerThread
    private synchronized void requestHotCities(@Nullable GetAttrByIpResponse getAttrByIpResponse) {
        if (!ActivityUtils.isValid(this)) {
            return;
        }

        // 尝试获取接口自定义的热门城市（https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=123350487）
        /*
            2、「热门城市」内容动态替换
            「热门城市」小标题不变、内容调整为一批针对普工推荐的城市，数量为3～12个
            @工程 根据普工职位量、当前城市附近/同省等计算出来下发
            若取不到定位、无法计算附近城市，则只做推荐城市
         */
        final String userSelectedPositionCode = getUserSelectedPositionCode();
        if (!LText.empty(userSelectedPositionCode) && (getAttrByIpResponse != null && getAttrByIpResponse.cityCode > 0) && isMultiSelectionEnabled()) {
            // 尝试根据定位城市和职类请求热门城市数据
            SimpleApiRequest.GET(GeekUrlConfig.URL_ZPGEEK_APP_CITYLIST)
                    .addParam("gpsCityCode", getAttrByIpResponse.cityCode)
                    .addParam("position", userSelectedPositionCode)
                    .setRequestCallback(new SimpleApiRequestCallback<GetAppCityListResponse>() {
                        @Override
                        public void handleInChildThread(ApiData<GetAppCityListResponse> data) {
                            super.handleInChildThread(data);
                            loadData(getAttrByIpResponse, data.resp.hotCityList);
                        }

                        @Override
                        public void handleErrorInChildThread(ErrorReason reason) {
                            super.handleErrorInChildThread(reason);
                            loadData(getAttrByIpResponse, null);
                        }
                    })
                    .execute();
        } else {
            loadData(getAttrByIpResponse, null);
        }
    }

    @WorkerThread
    private synchronized void loadData(@Nullable GetAttrByIpResponse getAttrByIpResponse, @Nullable List<LevelBean> hotCityList) {
        if (!ActivityUtils.isValid(this)) {
            return;
        }

        //region 本地没有定位数据时，接口返回的定位数据转化为 LocationBean
        if (getAttrByIpResponse != null && LocationService.location == null) {
            String cityName = getAttrByIpResponse.cityName;
            long cityCode = getAttrByIpResponse.cityCode;

            // 构建定位数据
            if (cityCode > 0 && !LText.empty(cityName)) {
                LocationService.LocationBean location = new LocationService.LocationBean();
                location.localCityCode = String.valueOf(cityCode);
                location.city = cityName;
                LocationService.location = location;
            }
        }
        //endregion

        List<LevelBean> data;
        if (UserManager.isBossRole()) {
            if (isShowClear()) {
                data = VersionAndDatasCommon.getInstance().getBSortCityWithClearList(false);
            } else {
                data = VersionAndDatasCommon.getInstance().getBSortCityList(false);
            }
        } else {
            if (isFilterSubCity()) {
                data = VersionAndDatasCommon.getInstance().getCNormalCitySortList();
            } else {
                data = VersionAndDatasCommon.getInstance().getSortCityList2();
            }

            //region 县级市
            if (showCountyLevelCity()) {
                // 定位城市，显示县级市（809）
                LevelBean parent = LList.getElement(data, 0);
                if (parent != null && CitySortUtil.LOCATION_CITY.equals(parent.name)) {
                    LevelBean locationCity = LList.getElement(parent.subLevelModeList, 0);
                    LocationService.LocationBean location = LocationService.location;

                    if (location != null && !TextUtils.isEmpty(location.city) && !TextUtils.isEmpty(location.localCityCode) && data != null) {
                        // 定位城市
                        for (LevelBean datum : data) {
                            List<LevelBean> cityLevels = datum.subLevelModeList;
                            if (LList.isEmpty(cityLevels)) continue;

                            for (LevelBean cityBean : cityLevels) {
                                if (locationCity == null
                                        || LText.empty(locationCity.name)
                                        || !locationCity.name.equals(cityBean.name))
                                    continue;

                                if (!TextUtils.isEmpty(location.subLocalCityCode) && !TextUtils.isEmpty(location.district) && !location.subLocalCityCode.equals("0")) {
                                    LevelBean subCity = new LevelBean(LText.getLong(location.subLocalCityCode), location.district);
                                    subCity.parentCode = cityBean.code;
                                    subCity.parentName = cityBean.name;
                                    subCity.cityType = 3;
                                    parent.subLevelModeList.clear();
                                    parent.subLevelModeList.add(locationCity);
                                    parent.subLevelModeList.add(subCity);
                                    AnalyticsFactory.create().action("action-search-expect-locationcity").param("p", location.subLocalCityCode).build();
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            //endregion

            //region 热门城市
            // 替换热门城市
            if (hotCityList != null && hotCityList.size() > 0) {
                if (data != null && data.size() > 0) {
                    for (LevelBean datum : data) {
                        if (Constants.HOT_CITY.equals(datum.name)) {
                            // 替换
                            for (LevelBean hotCity : hotCityList) {
                                hotCity.cityFlag = CitySortUtil.PARENT_GROUP_HOT;
                            }
                            datum.subLevelModeList = hotCityList;


                            break;
                        }
                    }
                }
            }


            //endregion
        }

        handler.obtainMessage(1000, data).sendToTarget();
    }

    public void handleRecommend(List<LevelBean> data) {
        if (showRecommend() && recommendCityResponse != null) {

            CitySortUtil.handleRecommendAndHistory(data, recommendCityResponse.historyCityList, recommendCityResponse.interestedCityList, null);

            AnalyticsFactory.create().action("city_select_exposure")
                    .param("p", getGeekId())
                    .param("p2", getMgeParamBySource(getSourceFrom()))
                    .param("p3", getMgeParamCityList(recommendCityResponse.historyCityList))
                    .param("p4", getMgeParamCityList(recommendCityResponse.interestedCityList))
                    .build();

            AnalyticsFactory.create().action("interest_city_select_exposure")
                    .param("p", getGeekId()) // geek_id
                    .param("p2", getRequestParams().getExpectId()) // expect_id（没有写0）
                    .param("p3", UserManager.isGeekStudent() ? 2 : 1) // 用户身份。1：职场人，2：学生
                    .param("p5", getMgeParamCityList(recommendCityResponse.interestedCityList)) // 页面展示的可选的感兴趣城市。city1,city2,,,
                    .param("p6", getMgeParamCityList(getSelectedCities())) // 用户实际选择了的感兴趣城市。city1,city2,,,
                    .build();
        }
    }

    /**
     * 是否在定位城市上显示县级市。如：廊坊（三河）
     */
    private boolean showCountyLevelCity() {
        return showSearchBox();
    }

    @NonNull
    @Override
    public List<LevelBean> getSelectedCities() {
        return multiCityView.getSelectedCities();
    }

    private void initSearchBox() {
        View searchBox = findViewById(R.id.search_box);
        searchBox.setVisibility(showSearchBox() ? View.VISIBLE : View.GONE);

        AnalyticsFactory.create()
                .action("city_select_searchbar_exposure")
                .param("p", getGeekId())
                .param("p2", showSearchBox() ? 1 : 0)
                .param("p3", getMgeParamBySource(getSourceFrom()))
                .build();

        searchBox.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                CitySearchFragment fragment = CitySearchFragment.newInstance();
                fragment.setOnChooseCityListener(CitySelectActivity.this::onSearchCitySelected);
                getSupportFragmentManager()
                        .beginTransaction()
                        .setCustomAnimations(R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide, R.anim.activity_new_enter_up_glide, R.anim.activity_new_exit_up_glide)
                        .replace(R.id.citySearchContainer, fragment)
                        .addToBackStack(null)
                        .commitAllowingStateLoss();
            }
        });
    }

    @SuppressWarnings("DanglingJavadoc")
    private void onSearchCitySelected(@NonNull LevelBean searchCity) {
        //region 考虑县级市选中逻辑，有县级市的话，将县级市信息取出构建 LevelBean 返回，简化了后续显示、保存逻辑
        /** {@link searchCity} 中可能携带县级市信息 */
        LevelBean searchCityLevel = searchCity; // 用户要选的可能是地级市，也可能是县级市
        searchCity.cityFlag = CitySortUtil.PARENT_GROUP_SEARCH;
        // 尝试获取县级市信息
        LevelBean subLevelBean = LList.getElement(searchCity.subLevelModeList, 0);
        // 从搜索传来的包含县级市结果 （地级市 + sub: 县级市）
        if (subLevelBean != null) { // 选中的是县级市或区县（916.610）
            subLevelBean.cityFlag = CitySortUtil.PARENT_GROUP_SEARCH;
            if (subLevelBean.isCountyLevelCity() || subLevelBean.isCountyOrDistrict()) {
                /* 关联上对应的地级市 */
                subLevelBean.parentName = searchCityLevel.name;
                subLevelBean.parentCode = searchCityLevel.code;

                searchCityLevel = subLevelBean; // 用户要选县级市
            }
        }
        //endregion

        if (!hasCity(searchCityLevel) && isFull()) { // 搜索点选的城市不在已选集合中，并且已选满，提示『最多支持选择三个城市』
            return;
        }

        if (pickAssociatedCity(searchCityLevel) != null) {
            return;
        }

        // 搜索的城市点击即选中
        searchCityLevel.setChecked(true);
        onCitySelected(searchCityLevel);
        try {
            getSupportFragmentManager().popBackStack();


        } catch (Exception ignored) {

        }
    }

    @Override
    public void onCityClick(@NonNull LevelBean item) {
        item.subLevelModeList.clear();
        onCitySelected(item);
        adapter.notifyDataSetChanged();
        refreshUiStatus();
    }

    @Override
    public boolean isFull() {
        boolean disableFullCheck = getRequestParams().isDisableFullCheck();
        if (disableFullCheck) {
            // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=159453855
            // 此字段仅可单选，当已有选中数据，点击其他城市时，自动切换已选值，如再次点击已选中数据，则取消选中
            List<LevelBean> selectedCities = getSelectedCities();
            LevelBean city = LList.getElement(selectedCities, 0);
            if (city != null) {
                city.setChecked(false);
                multiCityView.onCityChanged(city); // 移除选中的城市
                adapter.notifyDataSetChanged();
            }
            return false;
        }

        return multiCityView.isFull(true);
    }

    @Override
    public boolean hasCity(@NonNull LevelBean targetCity) {
        return multiCityView.hasCity(targetCity);
    }

    @Override
    @Nullable
    public LevelBean pickAssociatedCity(@NonNull LevelBean targetCity /* 用户要选择的城市 */) {
        return multiCityView.pickAssociatedCity(targetCity);
    }

    @Override
    public boolean isItemSelected(@NonNull LevelBean cityItem) {
        return multiCityView.isItemSelected(cityItem);
    }

    @Override
    public boolean isClearable() {
        return getRequestParams().isClearable();
    }

    /**
     * 绘制索引列表
     */
    @SuppressLint("ClickableViewAccessibility")
    public void drawIndexView() {
        int textHeight = ZPUIDisplayHelper.dp2px(this, 17f);
        int textPaddingHorizontal = ZPUIDisplayHelper.dp2px(this, 5f);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, textHeight);
        for (String s : INDEXER) {
            final MTextView tv = new MTextView(this);
            tv.setLayoutParams(params);
            tv.setText(s);
            tv.setGravity(Gravity.CENTER);
            tv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 11f);
            tv.setTextColor(ResourcesCompat.getColor(getResources(), R.color.app_green_dark, null));
            tv.setPadding(textPaddingHorizontal, 0, textPaddingHorizontal, 0);
            llIndex.addView(tv);
            llIndex.setOnTouchListener((v, event) -> {
                float y = event.getY();
                int index = (int) (y / textHeight);
                if (index > -1 && index < INDEXER.length) {// 防止越界
                    String key = INDEXER[index];
                    if (selector.containsKey(key)) {
                        int pos = selector.get(key);
                        if (listView.getHeaderViewsCount() > 0) {// 防止ListView有标题栏
                            listView.setSelectionFromTop(pos + listView.getHeaderViewsCount(), 0);
                        } else {
                            listView.setSelectionFromTop(pos, 0);// 滑动到第一项
                        }
                        tvOverlay.setVisibility(View.VISIBLE);
                        tvOverlay.setText(INDEXER[index]);
                    }
                }
                switch (event.getAction()) {
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        tvOverlay.setVisibility(View.GONE);
                        break;
                    default:
                        break;
                }
                return true;
            });
        }
    }

    private void refreshUiStatus() {
        final boolean enabled = LList.hasElement(getSelectedCities()) || isClearable();

        btnJump.setEnable(enabled);
        btnJump.setVisibility(getRequestParams().isEnableBtnJump() && enabled ? View.VISIBLE : View.GONE);

        titleView.getTvBtnAction().setEnabled(enabled);
        titleView.getTvBtnAction().setTextColor(ResourcesCompat.getColor(getResources(), enabled ? R.color.app_green_dark : R.color.text_c3, null));

        tvHeaderCounter.setText(
                HtmlCompat.fromHtml(
                        String.format(
                                Locale.getDefault(),
                                "<font color='#0D9EA3'>%d</font>/%d",
                                LList.getCount(getSelectedCities()),
                                getRequestParams().getMaxCityCount()
                        ),
                        HtmlCompat.FROM_HTML_MODE_LEGACY
                )
        );
    }

    private void onCitySelected(@NonNull LevelBean item) {


        if (isMultiSelectionEnabled()) {

            if (item.isChecked()) {
                AnalyticsFactory.create().action("select_city_info_report")
                        .param("p", getGeekId())
                        .param("p2", getMgeParamBySource(getSourceFrom()))
                        .param("p3", getCityMgeParam(item))
                        .param("p4", getCityGroupType(item))
                        .build();
            }
            if (isUseGray()) {
                addItemToBottom(item);
            }
            multiCityView.onCityChanged(item);
            refreshUiStatus();
        } else {

            if (isChangeExpectCity()){
                getExpectPositionChangeCityTip(item);
            }else {
                AnalyticsFactory.create().action("select_city_info_report")
                        .param("p", getGeekId())
                        .param("p2", getMgeParamBySource(getSourceFrom()))
                        .param("p3", getCityMgeParam(item))
                        .param("p4", getCityGroupType(item))
                        .build();
                Intent intent = getIntent();
                intent.putExtra(Constants.DATA_ENTITY, item);
                setResult(RESULT_OK, intent);
                finishCurrentPage();
            }

        }
    }

    private void addItemToBottom(@NonNull LevelBean item) {

        boolean forCheck = item.isChecked();
        //支持是为了执行
        if (forCheck) {
            boolean notSelect = LList.getFirstFilterElement(mSelectedItem, bean -> bean != null && bean.code == item.code) == null;
            if (notSelect) {
                mSelectedItem.add(item);
                selectedLabelAdapter.addItemToLast(item);
                layoutManager.scrollToPositionWithOffset(mSelectedItem.size() - 1, 0);
            }
        } else {
            // 删除选择
            for (int i = 0; i < mSelectedItem.size(); i++) {
                if (item.code == (mSelectedItem.get(i).code)) {
                    selectedLabelAdapter.removeItem(item, i);
                    mSelectedItem.remove(mSelectedItem.get(i));
                    break;
                }
            }
        }
        notifyAllState();

    }

    /**
     * 同时选择多个城市
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=123350487
     *
     * @param cities 多个城市集合
     */
    @SuppressWarnings("JavadocLinkAsPlainText")
    private void onCitiesSelected(@NonNull ArrayList<LevelBean> cities) {
        InterestLocation interestLocation = getRequestParams().getInterestLocation();
        if (interestLocation != null) { // 直接保存感兴趣的城市
            interestLocation.realSave(this, cities);
        } else {
            Intent intent = new Intent();
            intent.putExtra(Constants.DATA_ENTITYS, cities);
            setResult(RESULT_OK, intent);
            finishCurrentPage();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            int backStackEntryCount = getSupportFragmentManager().getBackStackEntryCount();
            if (backStackEntryCount > 0) {
                final Fragment fragment = getSupportFragmentManager().findFragmentById(R.id.citySearchContainer);
                if (fragment instanceof CitySearchFragment) {
                    CitySearchFragment citySearchFragment = (CitySearchFragment) fragment;
                    citySearchFragment.hideSelf();
                } else {
                    getSupportFragmentManager().popBackStack();
                }
                return false;
            }
            finishCurrentPage();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void finishCurrentPage() {
        if (isUpGlide()) {
            AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
        } else {
            finish();
        }
    }

    //region STARTER
    public static void startActivityForResult(@NonNull Context context, @NonNull CitySelectIntentParams requestParams) {
        Intent intent = new Intent(context, CitySelectActivity.class);
        intent.putExtra(KEY_REQUEST_PARAMS, requestParams);
        AppUtil.startActivityForResult(context, intent, requestParams.getRequestCode(), requestParams.isActivityAnimNone() ? ActivityAnimType.NONE : (requestParams.isUpGlide() ? ActivityAnimType.UP_GLIDE : ActivityAnimType.DEFAULT));
    }

    public static void startActivityForResultFromFragment(@NonNull Fragment startFragment, @NonNull CitySelectIntentParams requestParams) {
        final Context context = startFragment.getContext();
        Intent intent = new Intent(context, CitySelectActivity.class);
        intent.putExtra(KEY_REQUEST_PARAMS, CitySelectIntentParams.obj());
        AppUtil.startActivityForResultFromFragment(startFragment, context, intent, requestParams.getRequestCode(), requestParams.isActivityAnimNone() ? ActivityAnimType.NONE : (requestParams.isUpGlide() ? ActivityAnimType.UP_GLIDE : ActivityAnimType.DEFAULT));
    }
    //endregion

    public int getMgeParamBySource(int sourceFrom) {
        if (sourceFrom == FROM_F1_MIXED_EXPECT ||sourceFrom ==FROM_GEEK_F1_MIXED_FILTER) {
            return 0;
        } else if (sourceFrom == FROM_F1_SINGE_EXPECT || sourceFrom ==FROM_GEEK_F1_FILTER) {
            return 1;
        } else if (sourceFrom == FROM_SEARCH_PAGE) {
            return 2;
        } else if (sourceFrom == FROM_EXPECT_PAGE_CITY) {
            return 3;
        } else if (sourceFrom == FROM_INTEREST_LOCATION) {
            return 5;
        } else {
            return 4;
        }

    }

    public long getGeekId() {
        return UserManager.getUID();
    }

    public String getMgeParamCityList(List<LevelBean> cityList) {
        String cityParam = "";
        if (!LList.isEmpty(cityList)) {
            for (LevelBean city : cityList) {
                if (!LList.isEmpty(city.subLevelModeList) && LList.getElement(city.subLevelModeList, 0) != null) {
                    LevelBean subCity = LList.getElement(city.subLevelModeList, 0);
                    if (subCity != null) {
                        String cityString = StringUtil.connectTextWithChar("_", String.valueOf(city.code), String.valueOf(subCity.code));
                        cityParam = StringUtil.connectTextWithChar(",", cityParam, cityString);
                    } else {
                        cityParam = StringUtil.connectTextWithChar(",", cityParam, String.valueOf(city.code));
                    }

                } else {
                    cityParam = StringUtil.connectTextWithChar(",", cityParam, String.valueOf(city.code));
                }
            }
        }
        return cityParam;

    }

    public String getCityMgeParam(LevelBean city) {
        if (city.isCountyLevelCity() || city.isCountyOrDistrict()) {

            return StringUtil.connectTextWithChar("_", String.valueOf(city.parentCode), String.valueOf(city.code));
        } else {

            if (!LList.isEmpty(city.subLevelModeList)) { // 包含县级市 搜索返回
                LevelBean countryBean = LList.getElement(city.subLevelModeList, 0);
                if (countryBean != null && (countryBean.isCountyLevelCity() || countryBean.isCountyOrDistrict())) {
//                            bean = new LevelBean(countryBean.code, countryBean.name);
                    return StringUtil.connectTextWithChar("_", String.valueOf(city.code), String.valueOf(countryBean.code));
                }
            } else {
                return String.valueOf(city.code);
            }
        }
        return "";
    }

    public int getCityGroupType(LevelBean city) {
        if (city.cityFlag == CitySortUtil.PARENT_GROUP_LOCATION) {
            return 0;
        } else if (city.cityFlag == CitySortUtil.PARENT_GROUP_RECOMMEND) {
            return 1;
        } else if (city.cityFlag == CitySortUtil.PARENT_GROUP_HOT) {
            return 2;
        } else if (city.cityFlag == CitySortUtil.PARENT_GROUP_SEARCH) {
            return 4;
        } else {
            return 3;
        }
    }

    @Override
    public void add() {

    }

    @Override
    public void del(LevelBean bean, int position) {
        if (bean != null) {
            bean.setChecked(false);
            bean.subLevelModeList.clear();
            onCitySelected(bean);

            adapter.notifyDataSetChanged();

            refreshUiStatus();
        }
    }

    @SuppressLint("SetTextI18n")
    private void notifyAllState() {
//        if (LList.isEmpty(mSelectedItem)) {
        mBtnSubmit.setText("保存");
//        } else {
//            mBtnSubmit.setText("已选" + mSelectedItem.size() + "/" + getRequestParams().getMaxCityCount());
//        }
    }

    private String getAnalyticsSort() {
        String p = "";
        int source = getSourceFrom();
        if (source == FROM_SEARCH_PAGE || source == FROM_GEEK_SEARCH_CHANGE_EXPECT_CITY) {
            p="3";
        } else if (FROM_GEEK_F1_MIXED_FILTER==source||FROM_GEEK_F1_FILTER==source||FROM_GEEK_F1_CHANGE_EXPECT_CITY==source) {
            p=String.valueOf(getSortType());
        }

        return p;
    }

    @Override
    public void onClick(View v) {
        int id=v.getId();
        if (id== R.id.btn_submit) {
            onConfirm();
        }else if (id==R.id.subTitle){
            int source= getSourceFrom();
            if (mChangeExpectCity!=null ){

                AnalyticsFactory.create().action("geek-expect-set-click").param("p",getAnalyticsSort()).build();
                int   sourceFrom =0;
                if (FROM_GEEK_F1_MIXED_FILTER==source||FROM_GEEK_F1_FILTER==source){
                    sourceFrom=FROM_GEEK_F1_CHANGE_EXPECT_CITY;
                }else if (FROM_SEARCH_PAGE ==source){
                    sourceFrom=FROM_GEEK_SEARCH_CHANGE_EXPECT_CITY;
                }
                CitySelectActivity.startActivityForResult(
                        this,
                        CitySelectIntentParams.obj()
                                .setShowAll(false)
                                .setSourceFrom(sourceFrom)
                                .setSortType(getSortType())
                                .setSupportRecommend(true)
                                .setShowSearchBox(true)
                                .setRequestCode(REQ_CITY_SELECT_FOR_CHANGE_EXPECT_CITY)
                                .setUpGlide(true)
                );
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode ==RESULT_OK){
            if (requestCode ==REQ_CITY_SELECT_FOR_CHANGE_EXPECT_CITY){
                Intent intent = getIntent();
                intent.putExtra(RESULT_PARAM_TO_IS_FINISH, true);
                setResult(RESULT_OK, intent);
                finishCurrentPage();
            }
        }
    }


}