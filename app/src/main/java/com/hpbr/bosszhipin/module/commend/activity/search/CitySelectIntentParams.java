package com.hpbr.bosszhipin.module.commend.activity.search;

import android.view.View;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.module.commend.view.MultiCityView;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;

import java.util.ArrayList;

@SuppressWarnings({"UnusedReturnValue", "unused"})
public class CitySelectIntentParams extends BaseEntity {

    private static final long serialVersionUID = -620704229454501685L;

    private boolean isShowAll;
    private boolean showSearchBox;
    private boolean showClear;
    private boolean isUpGlide;
    private boolean isActivityAnimNone;

    private String userSelectedPositionCode;
    private String cityTitle;
    private int cityTitleVisibility = View.VISIBLE;
    private boolean useGray;
    private boolean firstExpectation;
    private boolean freshData;
    private String subTitle;

    private boolean filterSubCity; // 是否过滤掉县级市/区
    private boolean enableMultiSelection;
    private boolean enableMultiSelectionIndicator;

    private ArrayList<LevelBean> selectedCities;

    private boolean disableNestedScrolling;

    private int maxCityCount = MultiCityView.MAX_CITY_COUNT_THREE;
    private String overrunToast;

    private boolean clearable; // 可以选空（清除选择）

    private boolean supportRecommendAndHistory; // 1006 历史¥推荐

    private int sourceFrom; //来源

    private boolean enableBtnJump;

    private boolean disableFullCheck;

    private boolean enableAppTitle;

    @Nullable
    private InterestLocation interestLocation;

    private String expectId;


    private boolean isRegister; // 是否是注册流程

    private int requestCode = CitySelectActivity.REQ_CITY_SELECT;

    private int sortType;

    private CitySelectIntentParams() {

    }

    public static CitySelectIntentParams obj() {
        return new CitySelectIntentParams();
    }

    public ArrayList<LevelBean> getSelectedCities() {
        return selectedCities;
    }


    public int getSortType() {
        return sortType;
    }

    public CitySelectIntentParams setSortType(int sortType) {
        this.sortType = sortType;
        return this;
    }

    public CitySelectIntentParams setSelectedCities(ArrayList<LevelBean> selectedCities) {
        this.selectedCities = selectedCities;
        return this;
    }

    public boolean isUpGlide() {
        return isUpGlide;
    }

    public CitySelectIntentParams setUpGlide(boolean upGlide) {
        isUpGlide = upGlide;
        return this;
    }

    public boolean isActivityAnimNone() {
        return this.isActivityAnimNone;
    }

    public CitySelectIntentParams setActivityAnimNone(boolean isActivityAnimNone) {
        this.isActivityAnimNone = isActivityAnimNone;
        return this;
    }

    public boolean isShowAll() {
        return isShowAll;
    }

    public CitySelectIntentParams setShowAll(boolean showAll) {
        isShowAll = showAll;
        return this;
    }

    public CitySelectIntentParams setSupportRecommend(boolean supportRecommend) {
        supportRecommendAndHistory = supportRecommend;
        return this;
    }

    public int getSourceFrom() {
        return sourceFrom;
    }

    public CitySelectIntentParams setSourceFrom(int sourceFrom) {
        this.sourceFrom = sourceFrom;
        return this;
    }

    public boolean isSupportRecommendAndHistory() {
        return supportRecommendAndHistory;
    }

    public boolean isShowSearchBox() {
        return showSearchBox;
    }

    public CitySelectIntentParams setShowSearchBox(boolean showSearchBox) {
        this.showSearchBox = showSearchBox;
        return this;
    }

    public boolean isShowClear() {
        return showClear;
    }

    public CitySelectIntentParams setShowClear(boolean showClear) {
        this.showClear = showClear;
        return this;
    }

    public String getUserSelectedPositionCode() {
        return userSelectedPositionCode;
    }

    public CitySelectIntentParams setUserSelectedPositionCode(String userSelectedPositionCode) {
        this.userSelectedPositionCode = userSelectedPositionCode;
        return this;
    }

    public String getCityTitle() {
        return cityTitle;
    }

    public CitySelectIntentParams setUseGray(boolean useGray) {
        this.useGray = useGray;
        return this;
    }
    public boolean isUseGray(){
        return useGray;
    }
    public boolean isFirstExpectation(){
        return firstExpectation;
    }
    public CitySelectIntentParams setFirstExpectation(boolean firstExpectation) {
        this.firstExpectation = firstExpectation;
        return this;
    }

    public boolean isFreshData(){
        return freshData;
    }
    public CitySelectIntentParams setFreshData(boolean freshData) {
        this.freshData = freshData;
        return this;
    }

    public CitySelectIntentParams setCityTitle(String cityTitle) {
        this.cityTitle = cityTitle;
        return this;
    }

    public CitySelectIntentParams setCityTitleVisibility(int visibility) {
        this.cityTitleVisibility = visibility;
        return this;
    }

    public int getCityTitleVisibility() {
        return cityTitleVisibility;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public CitySelectIntentParams setSubTitle(String subTitle) {
        this.subTitle = subTitle;
        return this;
    }

    public boolean isFilterSubCity() {
        return filterSubCity;
    }

    public CitySelectIntentParams setFilterSubCity(boolean filterSubCity) {
        this.filterSubCity = filterSubCity;
        return this;
    }

    public boolean isEnableMultiSelection() {
        return enableMultiSelection;
    }

    public CitySelectIntentParams setEnableMultiSelection(boolean enableMultiSelection) {
        setEnableMultiSelection(enableMultiSelection, false);
        return this;
    }

    public CitySelectIntentParams setEnableMultiSelection(boolean enableMultiSelection, boolean enableMultiSelectionIndicator) {
        this.enableMultiSelection = enableMultiSelection;
        this.enableMultiSelectionIndicator = enableMultiSelectionIndicator;
        return this;
    }

    public boolean isDisableNestedScrolling() {
        return disableNestedScrolling;
    }

    public CitySelectIntentParams setDisableNestedScrolling(boolean disableNestedScrolling) {
        this.disableNestedScrolling = disableNestedScrolling;
        return this;
    }

    public int getMaxCityCount() {
        return maxCityCount;
    }

    @Nullable
    public String getOverrunToast() {
        return overrunToast;
    }

    public CitySelectIntentParams setMaxCityCount(int maxCityCount, @Nullable String overrunToast) {
        this.maxCityCount = maxCityCount;
        this.overrunToast = overrunToast;
        return this;
    }

    public CitySelectIntentParams setClearable(boolean clearable) {
        this.clearable = clearable;
        return this;
    }

    public boolean isClearable() {
        return clearable;
    }

    public boolean isEnableMultiSelectionIndicator() {
        return enableMultiSelectionIndicator;
    }

    public boolean isEnableBtnJump() {
        return enableBtnJump;
    }

    public CitySelectIntentParams setEnableBtnJump(boolean enableBtnJump) {
        this.enableBtnJump = enableBtnJump;
        return this;
    }

    public boolean isDisableFullCheck() {
        return disableFullCheck;
    }

    public CitySelectIntentParams setDisableFullCheck(boolean disableFullCheck) {
        this.disableFullCheck = disableFullCheck;
        return this;
    }

    public boolean isEnableAppTitle() {
        return enableAppTitle;
    }

    public CitySelectIntentParams setEnableAppTitle(boolean enableAppTitle) {
        this.enableAppTitle = enableAppTitle;
        return this;
    }

    @Nullable
    public InterestLocation getInterestLocation() {
        return interestLocation;
    }

    public CitySelectIntentParams setInterestLocation(@Nullable InterestLocation interestLocation) {
        this.interestLocation = interestLocation;
        return this;
    }

    public String getExpectId() {
        return expectId;
    }

    public CitySelectIntentParams setExpectId(@Nullable String expectId) {
        this.expectId = expectId;
        return this;
    }

    public boolean isRegister() {
        return isRegister;
    }

    public CitySelectIntentParams setRegister(boolean register) {
        isRegister = register;
        return this;
    }

    public CitySelectIntentParams setRequestCode(int requestCode) {
        this.requestCode = requestCode;
        return this;
    }

    public int getRequestCode() {
        return requestCode;
    }
}