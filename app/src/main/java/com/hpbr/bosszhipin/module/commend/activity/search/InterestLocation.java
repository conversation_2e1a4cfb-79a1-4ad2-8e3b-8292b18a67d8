package com.hpbr.bosszhipin.module.commend.activity.search;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.utils.StringUtil;

import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;

public class InterestLocation extends BaseEntity {

    private static final long serialVersionUID = 2885222331371164058L;

    private String encExpectId;
    private String locations;
    private String additionalSave;
    private String source;

    private InterestLocation() {

    }

    public static InterestLocation obj() {
        return new InterestLocation();
    }

    public String getEncExpectId() {
        return encExpectId;
    }

    public InterestLocation setEncExpectId(String encExpectId) {
        this.encExpectId = encExpectId;
        return this;
    }

    public String getLocations() {
        return locations;
    }

    public InterestLocation setLocations(String locations) {
        this.locations = locations;
        return this;
    }

    public String getAdditionalSave() {
        return additionalSave;
    }

    public InterestLocation setAdditionalSave(String additionalSave) {
        this.additionalSave = additionalSave;
        return this;
    }

    public String getSource() {
        return source;
    }

    public InterestLocation setSource(String source) {
        this.source = source;
        return this;
    }

    public void realSave(@NonNull BaseActivity activity, @NonNull ArrayList<LevelBean> cities) {
        @NonNull String locationCodeStr = buildLocationCodeStr(cities);

        AnalyticsFactory.create().action("geek-prefer-city-save")
                .param("p", locationCodeStr) // 选择城市code
                .build();

        AnalyticsFactory.create().action("interest_city_select_save")
                .param("p", UserManager.getUID()) // geek_id
                .param("p2", encExpectId) // expect_id（没有写0）
                .param("p3", 1) // 用户身份。1：职场人。2：学生
                .param("p4", 1) // 用户进入的入口。1：引导卡片，2：期望编辑页面
                .param("p5", locationCodeStr) // 用户保存时选择的感兴趣城市。city1,city2,,
                .build();

        SimpleApiRequest.POST(GeekUrlConfig.URL_GEEK_SAVE_EXPECT_CITY)
                .addParam("expectId", encExpectId)
                .addParam("locationCodeStr", locationCodeStr)
                .addParam("additionalSave", additionalSave) // 是否追加保存（默认0-否，1-是）
                .addParam("source", source) // 来源，1-JD页引导添加，2-F1引导添加
                .setRequestCallback(new SimpleApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        activity.showProgressDialog();
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        activity.dismissProgressDialog();
                        Intent intent = new Intent();
                        intent.putExtra(Constants.DATA_ENTITYS, cities);
                        activity.setResult(Activity.RESULT_OK,intent);
                        AppUtil.finishActivity(activity);
                    }
                }).execute();
    }

    @NonNull
    private String buildLocationCodeStr(@NonNull ArrayList<LevelBean> cities) {
        StringBuilder codeBuilder = new StringBuilder();
        int size = cities.size();
        for (int i = 0; i < size; i++) {
            LevelBean city = cities.get(i);

            codeBuilder.append(city.code);

            if (i < size - 1) {
                codeBuilder.append(StringUtil.SPLIT_CHAR_COMMA);
            }
        }
        return codeBuilder.toString();
    }

}
