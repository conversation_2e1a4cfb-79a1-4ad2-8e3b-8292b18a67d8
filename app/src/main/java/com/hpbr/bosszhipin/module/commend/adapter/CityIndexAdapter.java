package com.hpbr.bosszhipin.module.commend.adapter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.os.Build;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.views.MGridView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

/**
 * Created by zhouyou on 2015/10/31.
 */
public class CityIndexAdapter extends BaseAdapter {

    @NonNull
    private final Activity activity;
    @NonNull
    private final List<LevelBean> cityList = new ArrayList<>();
    private final boolean isShowAllCity;
    @NonNull
    private final LayoutInflater inflater;
    @Nullable
    private IOnCityClickListener listener;

    private boolean allowEmptyTitle = false;

    public void setAllowEmptyTitle(boolean allowEmptyTitle) {
        this.allowEmptyTitle = allowEmptyTitle;
    }

    public CityIndexAdapter(@NonNull Activity activity, boolean isShowAllCity) {
        this.activity = activity;
        inflater = LayoutInflater.from(activity);
        this.isShowAllCity = isShowAllCity;
    }

    public void setOnCityClickListener(@Nullable IOnCityClickListener listener) {
        this.listener = listener;
    }

    public void setData(List<LevelBean> list) {
        this.cityList.clear();
        for (LevelBean item : list) {
            if (item == null) continue;
            if (!allowEmptyTitle) {
                if (LText.empty(item.name)) continue;
            }
            if (LList.isEmpty(item.subLevelModeList)) continue;
            if (!isShowAllCity) {
                List<LevelBean> subList = item.subLevelModeList;
                Iterator<LevelBean> it = subList.iterator();
                while (it.hasNext()) {
                    LevelBean bean = it.next();
                    if (bean == null || LText.empty(bean.name) || bean.code == 0) {
                        it.remove();
                    }
                }
            }
            this.cityList.add(item);
        }
    }

    @Override
    public int getCount() {
        return LList.getCount(cityList);
    }

    @Override
    public LevelBean getItem(int position) {
        return LList.getElement(cityList, position);
    }

    @NonNull
    public List<LevelBean> getCityList() {
        return cityList;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @SuppressLint("InflateParams")
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        IndexViewHolder holder;
        if (convertView == null) {
            holder = new IndexViewHolder();
            convertView = inflater.inflate(R.layout.item_city_index, null);
            holder.tvIndex = convertView.findViewById(R.id.tv_index);
            holder.ivToggle = convertView.findViewById(R.id.iv_toggle);
            holder.gvContent = convertView.findViewById(R.id.gv_content);
            convertView.setTag(holder);
        } else {
            holder = (IndexViewHolder) convertView.getTag();
        }
        LevelBean item = getItem(position);
        setValue(holder, item);
        return convertView;
    }

    private void setValue(IndexViewHolder holder, LevelBean item) {
        if (item == null || holder == null) return;

        String title = item.name;
        if (allowEmptyTitle) {
            holder.tvIndex.setText(title, View.GONE);
        } else {
            holder.tvIndex.setText(title);
        }
        List<LevelBean> list = item.subLevelModeList;

        //region 展开收起逻辑
        boolean showToggle = false;
        int numColumns = 3;
        if (Constants.LOCATION_HISTORY.equals(title) || Constants.RECOMMEND_CITY.equals(title)) {
            if (Constants.LOCATION_HISTORY.equals(title)) {
                // 当前/历史访问：收缩展示 1 行，展开后最多展示 3 行
                if (LList.getCount(list) > numColumns) { // 超过一行，展示展开按钮
                    showToggle = true;
                }

                //region 初始化要显示的数据
                if (item.isChecked()) {
                    // 展开，展示完整数据
                    list = item.subLevelModeList;
                } else {
                    // 收起，展示一行数据
                    list = LList.getSubList(list, 0, numColumns);
                }
                //endregion
            }

            if (Constants.RECOMMEND_CITY.equals(title)) {
                numColumns = 3 * 2;

                // 您可能感兴趣的城市：收缩展示 2 行，展开后最多展示 3 行
                if (LList.getCount(list) > numColumns) { // 超过两行，展示展开按钮
                    showToggle = true;
                }

                //region 初始化要显示的数据
                if (item.isChecked()) {
                    // 展开，展示完整数据
                    list = item.subLevelModeList;
                } else {
                    // 收起，展示两行数据
                    list = LList.getSubList(list, 0, numColumns);
                }
                //endregion
            }
        }

        holder.ivToggle.setVisibility(showToggle ? View.VISIBLE : View.GONE);
        holder.ivToggle.setImageResource(item.isChecked() ? R.mipmap.ic_up_o : R.mipmap.ic_down_o);
        if (showToggle) {
            List<LevelBean> finalList = item.subLevelModeList;
            final int finalColumn = numColumns;
            holder.ivToggle.setOnClickListener(v -> {
                boolean checked = item.isChecked();
                if (!checked) {
                    // 已收起，展开
                    loadCityData(holder, finalList);
                } else {
                    // 已展开，收起
                    loadCityData(holder, LList.getSubList(finalList, 0, finalColumn));
                }

                item.setChecked(!checked);

                holder.ivToggle.setImageResource(item.isChecked() ? R.mipmap.ic_up_o : R.mipmap.ic_down_o);
            });
        } else {
            holder.ivToggle.setOnClickListener(null);
        }
        //endregion

        loadCityData(holder, list);
    }

    private void loadCityData(IndexViewHolder holder, List<LevelBean> list) {
        // 版本区分，为了解决7.0的刷新问题
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            CityAdapter adapter = new CityAdapter(activity, list);
            adapter.setOnCityClickListener(listener);
            holder.gvContent.setAdapter(adapter);
        } else {
            CityAdapter adapter = (CityAdapter) holder.gvContent.getAdapter();
            if (adapter == null) {
                adapter = new CityAdapter(activity, list);
                adapter.setOnCityClickListener(listener);
                holder.gvContent.setAdapter(adapter);
            } else {
                adapter.setData(list);
                adapter.notifyDataSetChanged();
            }
        }
    }

    public void syncCityCheckStatus(@NonNull ArrayList<LevelBean> selectedCities) {
        for (LevelBean selectedCity : selectedCities) {
            syncCityCheckStatus(selectedCity);
        }
    }

    public void syncCityCheckStatus(@NonNull LevelBean outerCity) {
        for (LevelBean firstLevel : cityList) {
            List<LevelBean> secondLevels = firstLevel.subLevelModeList;
            if (secondLevels == null) continue;

            for (LevelBean secondLevel : secondLevels) {
                if (outerCity != secondLevel && secondLevel.code == outerCity.code) { // 同步选中状态
                    secondLevel.setChecked(outerCity.isChecked());
                }
            }
        }
    }

    static class IndexViewHolder {
        MTextView tvIndex;
        ImageView ivToggle;
        MGridView gvContent;
    }

    private static class CityAdapter extends BaseAdapter {

        private final Activity activity;
        private List<LevelBean> cityList;
        @Nullable
        private IOnCityClickListener onCityClickListener;

        void setOnCityClickListener(@Nullable IOnCityClickListener onCityClickListener) {
            this.onCityClickListener = onCityClickListener;
        }

        CityAdapter(Activity activity, List<LevelBean> cityList) {
            this.activity = activity;
            this.cityList = cityList;
        }

        public void setData(List<LevelBean> cityList) {
            this.cityList = cityList;
        }

        @Override
        public int getCount() {
            return LList.getCount(cityList);
        }

        @Override
        public LevelBean getItem(int position) {
            return LList.getElement(cityList, position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @SuppressLint("InflateParams")
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            CityViewHolder holder;
            if (convertView == null) {
                holder = new CityViewHolder();
                convertView = LayoutInflater.from(activity).inflate(R.layout.item_city_new, null);
                holder.btnCity = convertView.findViewById(R.id.btn_city);
                convertView.setTag(holder);
            } else {
                holder = (CityViewHolder) convertView.getTag();
            }

            final LevelBean item = getItem(position);

            if (item != null && !TextUtils.isEmpty(item.name)) {
                holder.btnCity.setVisibility(View.VISIBLE);

                /*
                    https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=133870451

                    916.610【策略】 【C端】期望城市选择支持选择县 和 区的尝试 @闫泽华 @朱振楠@张祥东@范佳男@郭俊威@刘继帅@闫莹莹 @张一弛
                 */
                if (item.isCountyOrDistrict() && !LText.empty(item.parentName)) { // 区县显示成：区县（地级市）（916.610）
                    holder.btnCity.setText(String.format(Locale.getDefault(), "%s\n（%s）", item.name, item.parentName));
                } else {
                    holder.btnCity.setText(item.name);
                }

                item.setChecked(false);

                if (onCityClickListener != null && onCityClickListener.isMultiSelectionEnabled()) {
                    final boolean checked = onCityClickListener.isItemSelected(item);
                    item.setChecked(checked);

                    // 设置选中样式
                    holder.btnCity.setSelected(checked);
                } else {
                    holder.btnCity.setSelected(false);
                }

                holder.btnCity.setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        if (onCityClickListener != null) {
                            LocationService.LocationBean locationBean = LocationService.location;
                            if (locationBean != null && !TextUtils.isEmpty(locationBean.subLocalCityCode)) {
                                long subCode = LText.getLong(locationBean.subLocalCityCode);
                                if (item.code == subCode) {
                                    AnalyticsFactory.create().action("action-search-expect-cityclick").param("p", item.code).build();
                                }
                            }

                            if (onCityClickListener.isMultiSelectionEnabled()) { // 多选模式
                                /* 用户点击未选中的城市尝试去选中 */
                                boolean isItemNotChecked = !item.isChecked();
                                if (isItemNotChecked) {
                                    if (!onCityClickListener.isItemSelectable(item)) {
                                        return;
                                    }

                                    if (onCityClickListener.isFull()) {
                                        return;
                                    }

                                    if (onCityClickListener.pickAssociatedCity(item) != null) {
                                        return;
                                    }
                                }

                                // 反选选中状态
                                item.setChecked(isItemNotChecked);
                            }

                            onCityClickListener.onCityClick(item);
                        }
                    }
                });
            } else {
                holder.btnCity.setVisibility(View.GONE);
            }
            return convertView;
        }

        static class CityViewHolder {
            MTextView btnCity;
        }
    }

}
