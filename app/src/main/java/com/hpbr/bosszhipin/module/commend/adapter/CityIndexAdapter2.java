package com.hpbr.bosszhipin.module.commend.adapter;

import static com.hpbr.bosszhipin.utils.CitySortUtil.LOCATION_CITY;

import android.app.Activity;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import androidx.core.content.res.ResourcesCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener2;
import com.hpbr.bosszhipin.module.my.activity.boss.location.CitySelectFragment;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.BCitySortUtil;
import com.hpbr.bosszhipin.views.MGridView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Created by zhouyou on 2015/10/31.
 */
public class CityIndexAdapter2 extends BaseAdapter {

    private Activity activity;
    private List<LevelBean> cityList = new ArrayList<>();
    private IOnCityClickListener2 listener;
    private boolean isShowAllCity;

    private LayoutInflater inflater;

    public CityIndexAdapter2(Activity activity, List<LevelBean> cityList, boolean isShowAllCity) {
        this.activity = activity;
        inflater = LayoutInflater.from(activity);
        this.isShowAllCity = isShowAllCity;
        setData(cityList);
    }

    public void setOnCityClickListener(IOnCityClickListener2 listener) {
        this.listener = listener;
    }

    public void setData(List<LevelBean> list) {
        this.cityList.clear();
        for (LevelBean item : list) {
            if (item == null) continue;
            if (LText.empty(item.name)) continue;
            if (LList.isEmpty(item.subLevelModeList)) continue;
            if (isShowAllCity) {
                this.cityList.add(item);
            } else {
                List<LevelBean> subList = item.subLevelModeList;
                Iterator<LevelBean> it = subList.iterator();
                while (it.hasNext()) {
                    LevelBean bean = it.next();
                    if (bean == null || LText.empty(bean.name) || bean.code == 0) {
                        it.remove();
                    }
                }
                this.cityList.add(item);
            }
        }
    }

    public List<LevelBean> getData() {
        return this.cityList;
    }

    @Override
    public int getCount() {
        return LList.getCount(cityList);
    }

    @Override
    public LevelBean getItem(int position) {
        return LList.getElement(cityList, position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        IndexViewHolder holder;
        if (convertView == null) {
            holder = new IndexViewHolder();
            convertView = inflater.inflate(R.layout.item_city_index2, null);
            holder.tvIndex = convertView.findViewById(R.id.tv_index);
            holder.relocate = convertView.findViewById(R.id.relocate);
            holder.gvContent = convertView.findViewById(R.id.gv_content);
            convertView.setTag(holder);
        } else {
            holder = (IndexViewHolder) convertView.getTag();
        }
        LevelBean item = getItem(position);
        setValue(holder, item, position);
        return convertView;
    }

    private void setValue(IndexViewHolder holder, LevelBean item, int position) {
        if (item == null || holder == null) return;

        holder.tvIndex.setText(item.name);

        boolean isRelocateItem = TextUtils.equals(LOCATION_CITY, item.name) || TextUtils.equals(BCitySortUtil.LOCATION_CITY, item.name); // 是否是“定位城市”，是的话显示“重新定位”选项
        holder.relocate.setVisibility(isRelocateItem ? View.VISIBLE : View.GONE);
        holder.relocate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onRelocate();
            }
        });

        Drawable drawable = ResourcesCompat.getDrawable(holder.relocate.getResources(), R.drawable.icon_location, null);
        if (drawable != null) {
            drawable.setBounds(0, 0, ZPUIDisplayHelper.dp2px(activity, 12f), ZPUIDisplayHelper.dp2px(activity, 12f));
            holder.relocate.setCompoundDrawables(item.code == -1 ? null : drawable, null, null, null);
        }

        List<LevelBean> list = item.subLevelModeList;
        if (isRelocateItem){
            LevelBean element = LList.getElement(list, 0);
            if (element != null){
                element.firstChar = CitySelectFragment.TXT_LOCATION_CITY;
            }
        }
        // 版本区分，为了解决7.0的刷新问题
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            CityAdapter adapter = new CityAdapter(activity, list);
            adapter.setOnCityClickListener(listener);
            holder.gvContent.setAdapter(adapter);
        } else {
            CityAdapter adapter = (CityAdapter) holder.gvContent.getAdapter();
            if (adapter == null) {
                adapter = new CityAdapter(activity, list);
                adapter.setOnCityClickListener(listener);
                holder.gvContent.setAdapter(adapter);
            } else {
                adapter.setData(list);
                adapter.notifyDataSetChanged();
            }
        }
    }

    static class IndexViewHolder {
        MTextView tvIndex;
        MTextView relocate;
        MGridView gvContent;
    }

    private static class CityAdapter extends BaseAdapter {

        private Activity activity;
        private List<LevelBean> cityList = new ArrayList<>();
        private IOnCityClickListener onCityClickListener;

        public void setOnCityClickListener(IOnCityClickListener onCityClickListener) {
            this.onCityClickListener = onCityClickListener;
        }

        public CityAdapter(Activity activity, List<LevelBean> cityList) {
            this.activity = activity;
            this.cityList = cityList;
        }

        public void setData(List<LevelBean> cityList) {
            this.cityList = cityList;
        }

        @Override
        public int getCount() {
            return LList.getCount(cityList);
        }

        @Override
        public LevelBean getItem(int position) {
            return LList.getElement(cityList, position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            CityViewHolder holder;
            if (convertView == null) {
                holder = new CityViewHolder();
                convertView = LayoutInflater.from(activity).inflate(R.layout.item_city, null);
                holder.btnCity = convertView.findViewById(R.id.btn_city);
                convertView.setTag(holder);
            } else {
                holder = (CityViewHolder) convertView.getTag();
            }

            final LevelBean item = getItem(position);

            if (item != null && !TextUtils.isEmpty(item.name)) {
                holder.btnCity.setVisibility(View.VISIBLE);
                holder.btnCity.setText(item.name);
                holder.btnCity.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onCityClickListener != null) {
                            onCityClickListener.onCityClick(item);
                        }
                    }
                });
            } else {
                holder.btnCity.setVisibility(View.GONE);
            }
            return convertView;
        }

        static class CityViewHolder {
            ZPUIRoundButton btnCity;
        }
    }
}
