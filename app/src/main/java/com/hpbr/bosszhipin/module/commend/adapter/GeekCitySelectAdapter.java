package com.hpbr.bosszhipin.module.commend.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.commend.weight.GeekRGItemCityFilterLayout;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.CitySortUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.decoration.GridSpacingItemDecoration;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2023/11/22
 */
public class GeekCitySelectAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

    /*推荐城市*/
    private static final int ITEM_TYPE_REC_CITY = CitySortUtil.ITEM_TYPE_FILTER_RCD_CITY_LIST_1124_603;

    protected boolean isShowAll;

    public void setShowAll(boolean showAll) {
        isShowAll = showAll;
    }

    @Nullable
    private IOnCityClickListener onCityClickListener;

    public void setOnCityClickListener(@Nullable IOnCityClickListener onCityClickListener) {
        this.onCityClickListener = onCityClickListener;
    }

    public GeekCitySelectAdapter() {
        this(null);
    }

    public GeekCitySelectAdapter(@Nullable List<LevelBean> data) {
        super(R.layout.item_city_select_section, data);
    }

    @Override
    public void setNewData(@Nullable List<LevelBean> data) {
        if (data == null) return;

        Iterator<LevelBean> it = data.iterator();
        while (it.hasNext()) {
            LevelBean item = it.next();
            if (item == null || LList.isEmpty(item.subLevelModeList)) {
                it.remove();
            }
        }
        super.setNewData(data);
    }

    @Override
    protected int getDefItemViewType(int position) {
        LevelBean item = getItem(position);
        if (null != item && item.getItemType() == ITEM_TYPE_REC_CITY) {
            return ITEM_TYPE_REC_CITY;
        }
        return super.getDefItemViewType(position);
    }

    @Override
    protected BaseViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {

        int layoutResId = R.layout.item_city_select_section;

        if (viewType == ITEM_TYPE_REC_CITY) {
            layoutResId = R.layout.app_fragment_f1_filter_city_select_item_sub_rec_city;
        }

        return createBaseViewHolder(parent, layoutResId);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        int type = helper.getItemViewType();
        int position = helper.getAdapterPosition();
        if (type == ITEM_TYPE_REC_CITY) {
            // 职位信息
            covertGeekF1RecCityView(helper, item, position);
        } else {
            convertDefaultItem(helper, item, position);
        }
    }

    private void covertGeekF1RecCityView(@NonNull BaseViewHolder helper, LevelBean item, int position) {
        if (null != item) {
            GeekRGItemCityFilterLayout recCityLayout = helper.getView(R.id.item_rec_city_layout);

            recCityLayout.setTitle(item.name);
            recCityLayout.setFilterData(item.name, item.subLevelModeList, onCityClickListener);
        }
    }

    protected void convertDefaultItem(@NonNull BaseViewHolder helper, LevelBean item, int position) {
        if (item == null) return;

        MTextView tvIndex = helper.getView(R.id.tv_index);
        ImageView ivToggle = helper.getView(R.id.iv_toggle);
        RecyclerView rvContent = helper.getView(R.id.rv_content);

        if (rvContent.getItemDecorationCount() == 0) {
            GridSpacingItemDecoration decoration = new GridSpacingItemDecoration(3, ZPUIDisplayHelper.dp2px(mContext, 8), false);
            rvContent.addItemDecoration(decoration);
        }

        String title = item.name;
        tvIndex.setText(title, View.GONE);

        List<LevelBean> list = item.subLevelModeList;

        //region 展开收起逻辑
        boolean showToggle = false;
        int numColumns = 3;
        if (Constants.LOCATION_HISTORY.equals(title) || Constants.RECOMMEND_CITY.equals(title)) {
            if (Constants.LOCATION_HISTORY.equals(title)) {
                // 当前/历史访问：收缩展示 1 行，展开后最多展示 3 行
                if (LList.getCount(list) > numColumns) { // 超过一行，展示展开按钮
                    showToggle = true;
                }

                //region 初始化要显示的数据
                if (item.isChecked()) {
                    // 展开，展示完整数据
                    list = item.subLevelModeList;
                } else {
                    // 收起，展示一行数据
                    list = LList.getSubList(list, 0, numColumns);
                }
                //endregion
            }

            if (Constants.RECOMMEND_CITY.equals(title)) {
                numColumns = 3 * 2;

                // 您可能感兴趣的城市：收缩展示 2 行，展开后最多展示 3 行
                if (LList.getCount(list) > numColumns) { // 超过两行，展示展开按钮
                    showToggle = true;
                }

                //region 初始化要显示的数据
                if (item.isChecked()) {
                    // 展开，展示完整数据
                    list = item.subLevelModeList;
                } else {
                    // 收起，展示两行数据
                    list = LList.getSubList(list, 0, numColumns);
                }
                //endregion
            }
        }
        if (showToggle) {
            ivToggle.setVisibility(View.VISIBLE);
            ivToggle.setImageResource(item.isChecked() ? R.mipmap.ic_up_o : R.mipmap.ic_down_o);
            List<LevelBean> finalList = item.subLevelModeList;
            final int finalColumn = numColumns;
            ivToggle.setOnClickListener(v -> {
                boolean checked = item.isChecked();
                if (!checked) {
                    // 已收起，展开
                    loadCityData(rvContent, title, finalList);
                } else {
                    // 已展开，收起
                    loadCityData(rvContent, title, LList.getSubList(finalList, 0, finalColumn));
                }

                item.setChecked(!checked);

                ivToggle.setImageResource(item.isChecked() ? R.mipmap.ic_up_o : R.mipmap.ic_down_o);
            });
        } else {
            ivToggle.setVisibility(View.GONE);
        }

        loadCityData(rvContent, title, list);
    }

    private void loadCityData(RecyclerView rvContent,String title, List<LevelBean> list) {
        CityItemAdapter adapter = (CityItemAdapter) rvContent.getAdapter();
        if (adapter == null) {
            adapter = new CityItemAdapter(list);
            adapter.setTitle(title);
            adapter.setOnCityClickListener(onCityClickListener);
            rvContent.setAdapter(adapter);
        } else {
            adapter.setTitle(title);
            adapter.setOnCityClickListener(onCityClickListener);
            adapter.setNewData(list);
        }

        adapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                LevelBean item = (LevelBean) adapter.getItem(position);
                if (item == null) return;
                if (onCityClickListener != null) {
                    LocationService.LocationBean locationBean = LocationService.location;
                    if (locationBean != null && !TextUtils.isEmpty(locationBean.subLocalCityCode)) {
                        long subCode = LText.getLong(locationBean.subLocalCityCode);
                        if (item.code == subCode) {
                            AnalyticsFactory.create().action("action-search-expect-cityclick").param("p", item.code).build();
                        }
                    }

                    if (onCityClickListener.isMultiSelectionEnabled()) { // 多选模式
                        /* 用户点击未选中的城市尝试去选中 */
                        boolean isItemNotChecked = !item.isChecked();
                        if (isItemNotChecked) {
                            if (!onCityClickListener.isItemSelectable(item)) {
                                return;
                            }

                            if (onCityClickListener.isFull()) {
                                return;
                            }

                            if (onCityClickListener.pickAssociatedCity(item) != null) {
                                return;
                            }
                        }

                        // 反选选中状态
                        item.setChecked(isItemNotChecked);
                    }

                    onCityClickListener.onCityClick(title, item);
                }
            }
        });
    }

    public void syncCityCheckStatus(@NonNull ArrayList<LevelBean> selectedCities) {
        for (LevelBean selectedCity : selectedCities) {
            syncCityCheckStatus(selectedCity);
        }
    }

    public void syncCityCheckStatus(@NonNull LevelBean outerCity) {
        for (LevelBean firstLevel : mData) {
            List<LevelBean> secondLevels = firstLevel.subLevelModeList;
            if (secondLevels == null) continue;

            for (LevelBean secondLevel : secondLevels) {
                if (outerCity != secondLevel && secondLevel.code == outerCity.code) { // 同步选中状态
                    secondLevel.setChecked(outerCity.isChecked());
                }
            }
        }
    }

    static class CityItemAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

        protected String title;

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }

        @Nullable
        private IOnCityClickListener onCityClickListener;

        void setOnCityClickListener(@Nullable IOnCityClickListener onCityClickListener) {
            this.onCityClickListener = onCityClickListener;
        }

        public CityItemAdapter(@Nullable List<LevelBean> data) {
            super(R.layout.item_city_single_option, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
            if (item == null) return;
            ZPUIFrameLayout flParent = helper.getView(R.id.fl_parent);
            MTextView tvText = helper.getView(R.id.tv_text);

            String city = "";
            if (item.isCountyOrDistrict() && !LText.empty(item.parentName)) { // 区县显示成：区县（地级市）（916.610）
                city = String.format(Locale.getDefault(), "%s\n（%s）", item.name, item.parentName);
            } else {
                city = item.name;
            }
            tvText.setText(city);

            item.setChecked(false);
            if (onCityClickListener != null && onCityClickListener.isMultiSelectionEnabled()) {
                final boolean checked = onCityClickListener.isItemSelected(item);
                item.setChecked(checked);
                flParent.setSelected(checked);
                tvText.setSelected(checked);
            } else {
                flParent.setSelected(false);
                tvText.setSelected(false);
            }
        }
    }
}
