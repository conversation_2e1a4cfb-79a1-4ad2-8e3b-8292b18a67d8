package com.hpbr.bosszhipin.module.commend.dialog;

import android.app.Activity;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.IJDChatTip;
import com.hpbr.bosszhipin.common.dialog.ILoadCallBack;
import com.hpbr.bosszhipin.common.dialog.JDChatTip1Imp;
import com.hpbr.bosszhipin.common.dialog.JDChatTip4Imp;
import com.hpbr.bosszhipin.common.dialog.JDChatTips2Imp;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.chat.ProgressLoading;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.ConfigGreetingWordsRequest;
import net.bosszhipin.api.ConfigGreetingWordsResponse;
import net.bosszhipin.api.GetGreetingWordsRequest;
import net.bosszhipin.api.GetGreetingWordsResponse;
import net.bosszhipin.api.GetJDChatTipRequest;
import net.bosszhipin.api.GetJDChatTipsResponse;
import net.bosszhipin.api.GreetingUpdate2Response;
import net.bosszhipin.api.GreetingUpdateRequest2;
import net.bosszhipin.api.bean.GreetingUpdate2Bean;
import net.bosszhipin.api.bean.ServerJDChatTemplateBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class JDChatTipsDialog implements IJDChatTip.IOperate, ILoadCallBack {


    public static final String TAG_DIALOG_FIRSTSHOW = "jd_chattip_firstshow";

    public static final String KEY_ACTION = "key_action";

    private final long friendId;

    private final long jobId;

    private long expectId;

    private final int friendSource;

    private final Activity activity;

    private BottomView bottomView;

    private final LinearLayout rootLayout;

    private final ProgressLoading progressDialog;

    private onChatListener onChatListener;

    private final String securityId;


    public JDChatTipsDialog(Activity activity,
                            long friendId,
                            int friendSource,
                            long jobId,
                            String securityId, long expectId,
                            onChatListener onChatListener
                            ) {
        this.activity = activity;
        rootLayout = new LinearLayout(activity);
        progressDialog = new ProgressLoading(activity);
        this.onChatListener = onChatListener;
        this.friendId = friendId;
        this.friendSource = friendSource;
        this.jobId = jobId;
        this.expectId = expectId;
        this.securityId = securityId;
    }

    public void show() {
        request();
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, rootLayout);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(false);
    }

    public void onResume() {

        if (isShowing() && ijdChatTip != null) {
            ijdChatTip.onResume(needRequest -> {
                if (needRequest) {
                    request();
                }
            });
        }
    }


    private boolean isShowing() {
        return bottomView != null && bottomView.isShowing();
    }


    public void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            SpManager.get().user().edit().putInt(TAG_DIALOG_FIRSTSHOW, 0).apply();
        }
    }


    private IJDChatTip ijdChatTip;

    @Override
    public void onCompleteListener(@Nullable Map<String, List<ServerJDChatTemplateBean>> listMap) {

        if(ActivityUtils.isValid(activity)) {
            progressDialog.dismiss();
        }


        rootLayout.removeAllViews();


        if (listMap != null) {
            List<ServerJDChatTemplateBean> normalGreeting = listMap.get(ILoadCallBack.NORMAL_GREETING);
            List<ServerJDChatTemplateBean> introduceGreeting = listMap.get(ILoadCallBack.INTRODUCE_GREETING);

            //灰度新样式
            if (introduceGreeting != null && LList.getCount(introduceGreeting) > 0) {
                ijdChatTip = new JDChatTips2Imp(friendId, friendSource, jobId);
                View jdChatView = ijdChatTip.createJDChatView(activity, introduceGreeting, JDChatTipsDialog.this);
                rootLayout.addView(jdChatView);
                return;
            }


            //样式1
            if (normalGreeting != null && LList.getCount(normalGreeting) > 0) {
                ijdChatTip = new JDChatTip1Imp(friendId, jobId);
                View jdChatView = ijdChatTip.createJDChatView(activity, normalGreeting, JDChatTipsDialog.this);
                rootLayout.addView(jdChatView);
                return;
            }
        }


        ijdChatTip = new JDChatTip4Imp(friendId, jobId);
        View jdChatView = ijdChatTip.createJDChatView(activity, null, JDChatTipsDialog.this);
        rootLayout.addView(jdChatView);

    }



    @Override
    public void onCloseViewListener() {
        dismiss();

        if (onChatListener != null) {
            onChatListener.continueChat();
        }
    }

    @Override
    public void onSendListener(@Nullable ServerJDChatTemplateBean selectBean,
                               boolean isGreetingNeedCustomUpdate) {

        dismiss();

        if (selectBean != null) {
            // 0 普通招呼, 1 自定义招呼
            if (selectBean.greetingType == 1) {
                if (isGreetingNeedCustomUpdate) {
                    saveIntroduceGreeting(selectBean.demo, selectBean.templateId);
                } else {
                    setChatGreetingWords(String.valueOf(selectBean.templateId));
                }
            } else {
                setChatGreetingWords(String.valueOf(selectBean.templateId));
            }

            AnalyticsFactory.create()
                    .action("detial-geek-greeting-click")
                    .param("p", friendId)
                    .param("p2", jobId)
                    .param("p3", 1)
                    .param("p6", selectBean.greetingType)
                    .build();
        }


    }


    ///////////////////////////////////////////////////////////////////////////
    //     下面是串型调用接口 获得弹窗的接口数据
    ///////////////////////////////////////////////////////////////////////////

    private void request() {
        if(ActivityUtils.isValid(activity)) {
            progressDialog.show();
        }
        new GreetingWordsRequest(new JDChatRequest(null)).execute(securityId, this);
    }

    public interface IChainRequest {
        void execute(String securityId, @NonNull ILoadCallBack iLoadCallBack);
    }

    public abstract static class BaseChainRequest implements IChainRequest {

        protected @Nullable
        IChainRequest nextRequest;

        public BaseChainRequest(@Nullable IChainRequest nextRequest) {
            this.nextRequest = nextRequest;
        }
    }

    public static class GreetingWordsRequest extends BaseChainRequest {

        public GreetingWordsRequest(@Nullable IChainRequest nextRequest) {
            super(nextRequest);
        }

        @Override
        public void execute(String securityId, @NonNull ILoadCallBack iLoadCallBack) {

            GetGreetingWordsRequest request = new GetGreetingWordsRequest(new ApiRequestCallback<GetGreetingWordsResponse>() {

                @Override
                public void onStart() {
                    super.onStart();
                }

                @Override
                public void onSuccess(ApiData<GetGreetingWordsResponse> data) {
                    if (data != null && data.resp != null) {
                        boolean isOpen = data.resp.greeting != null && data.resp.greeting.status == 1; // 开关打开
                        if (isOpen) { // 打开以后再去请求JD下发的三条推荐招呼语
                            if (nextRequest != null) {
                                nextRequest.execute(securityId, iLoadCallBack);
                            }
                        } else {
                            iLoadCallBack.onCompleteListener(null);
                        }
                    } else {
                        iLoadCallBack.onCompleteListener(null);
                    }
                }

                @Override
                public void onComplete() {

                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                    iLoadCallBack.onCompleteListener(null);
                }
            });
            HttpExecutor.execute(request);

        }
    }

    public static class JDChatRequest extends BaseChainRequest {

        public JDChatRequest(@Nullable IChainRequest nextRequest) {
            super(nextRequest);
        }

        @Override
        public void execute(String securityId, @NonNull ILoadCallBack iLoadCallBack) {
            GetJDChatTipRequest request = new GetJDChatTipRequest(new ApiRequestCallback<GetJDChatTipsResponse>() {

                @Override
                public void onStart() {
                    super.onStart();
                }

                @Override
                public void onSuccess(ApiData<GetJDChatTipsResponse> data) {
                    if (data != null && data.resp != null) {
                        List<ServerJDChatTemplateBean> greetings = data.resp.greetings;
                        List<ServerJDChatTemplateBean> selfIntroGreetings = data.resp.selfIntroGreetings;

                        Map<String, List<ServerJDChatTemplateBean>> map = new HashMap<>();
                        map.put(ILoadCallBack.NORMAL_GREETING, greetings);
                        map.put(ILoadCallBack.INTRODUCE_GREETING, selfIntroGreetings);

                        iLoadCallBack.onCompleteListener(map);
                    } else {
                        iLoadCallBack.onCompleteListener(null);
                    }
                }

                @Override
                public void onComplete() {
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    iLoadCallBack.onCompleteListener(null);
                }
            });
            request.securityId = securityId;
            request.execute();
        }
    }


    //通知服务点击了具体的item
    private void setChatGreetingWords(String templateId) {

        if(ActivityUtils.isValid(activity)) {
            progressDialog.show();
        }


        ConfigGreetingWordsRequest request = new ConfigGreetingWordsRequest(new ApiRequestCallback<ConfigGreetingWordsResponse>() {

            @Override
            public void onSuccess(ApiData<ConfigGreetingWordsResponse> data) {
                if (data != null && data.resp != null) {
                    ToastUtils.showText("设置成功");
                }

                //开聊->需要设置完毕招呼语
                if (onChatListener != null) {
                    onChatListener.continueChat();
                }

            }

            @Override
            public void onComplete() {
                if(ActivityUtils.isValid(activity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.status = "1";
        request.templateId = templateId;
        HttpExecutor.execute(request);
    }


    //保存自我介绍
    private void saveIntroduceGreeting(String template, long templateId) {
        GreetingUpdateRequest2 greetingUpdateRequest = new GreetingUpdateRequest2(new ApiRequestCallback<GreetingUpdate2Response>() {
            @Override
            public void onStart() {
                super.onStart();
                if(ActivityUtils.isValid(activity)) {
                    progressDialog.show();
                }
            }

            @Override
            public void onSuccess(ApiData<GreetingUpdate2Response> data) {
                if (data != null && data.resp != null) {
                    //设置开聊招呼语
                    GreetingUpdate2Bean template1 = data.resp.template;
                    if (template1 != null) {
                        setChatGreetingWords(template1.templateId);
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        greetingUpdateRequest.template = template;
        greetingUpdateRequest.templateId = templateId;
        HttpExecutor.execute(greetingUpdateRequest);
    }

    public interface onChatListener {
        void continueChat();
    }
}
