package com.hpbr.bosszhipin.module.commend.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

import org.json.JSONObject;

/**
 * Created by <PERSON><PERSON>yo<PERSON> on 15/4/13.
 */
@Table("AdvantageContent")
public class AdvantageContentBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    public long advantageContentId;//"lureContentId": 67,
    public int advantageType;//"lureType": 2,
    public String advantageContent;//         "content": "梅西"

    public void parseJson(JSONObject jsonObject) {
        if (jsonObject != null) {
            advantageContentId = jsonObject.optLong("lureContentId");
            advantageType = jsonObject.optInt("lureType");
            advantageContent = jsonObject.optString("content");
        }
    }
}
