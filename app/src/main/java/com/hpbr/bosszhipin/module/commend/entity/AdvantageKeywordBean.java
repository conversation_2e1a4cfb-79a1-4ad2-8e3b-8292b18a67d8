package com.hpbr.bosszhipin.module.commend.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

import org.json.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15/4/13.
 */
@Table("AdvantageKeyword")
public class AdvantageKeywordBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    public long advantageKeywordId;//"keywordId": 182,
    public long advantageContentId;//     "lureContentId": 67,
    public String advantageKeywords;//       "keywords": "内马尔",
    public int sort;//       "sort": 1


    public void parseJson(JSONObject jsonObject) {
        advantageKeywordId = jsonObject.optLong("keywordId");
        advantageContentId = jsonObject.optLong("lureContentId");
        advantageKeywords = jsonObject.optString("keywords");
        sort = jsonObject.optInt("sort");
    }
}
