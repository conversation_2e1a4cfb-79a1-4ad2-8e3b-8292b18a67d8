package com.hpbr.bosszhipin.module.commend.entity;

import com.google.gson.annotations.SerializedName;
import com.hpbr.bosszhipin.base.BaseEntityAuto;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/11/4.
 */
public class AutoCompleteBean extends BaseEntityAuto {

    private static final long serialVersionUID = -1;
    /**
     * 搜索自动匹配的文字
     */
    @SerializedName("name")
    public String textTitle;
    /**
     * 公司的ID
     */
   public String compantId;

    /**
     * 临时名称,屏蔽公司超过四十个只显示6个省略号,提交时候上次全部名称
     */
    public  String tempTextTitle;
    /**
     * 高亮的下标
     */
    public List<AutoCompleteIndexBean> indexList = new ArrayList<>();

    /**
     * 品牌logo的url
     */
    public String logoUrl;

    /**
     * 主页的URL
     */
    public String homePageUrl;

    public void parseJson(JSONObject jsonObject) {
        if (jsonObject == null) return;
        textTitle = jsonObject.optString("name");
        compantId = jsonObject.optString("code");

        JSONArray ja = jsonObject.optJSONArray("highlightList");
        if (ja != null && ja.length() > 0) {
            for (int i = 0; i < ja.length(); i++) {
                JSONObject jo = ja.optJSONObject(i);
                if (jo == null) continue;
                AutoCompleteIndexBean bean = new AutoCompleteIndexBean();
                bean.parseJson(jo);
                indexList.add(bean);
            }
        }
    }

    public AutoCompleteBean() {
    }

    public AutoCompleteBean(String textTitle) {
        this.textTitle = textTitle;
    }
}
