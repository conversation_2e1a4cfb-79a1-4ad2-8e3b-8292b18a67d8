package com.hpbr.bosszhipin.module.commend.entity;

import com.google.gson.annotations.SerializedName;
import com.hpbr.bosszhipin.base.BaseEntityAuto;

import org.json.JSONObject;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2015/11/4.
 */
public class AutoCompleteIndexBean extends BaseEntityAuto {

    private static final long serialVersionUID = -1;

    /**
     * 起始下标
     */
    @SerializedName("startIndex")
    public int startIdx;

    /**
     * 结束下标
     */
    @SerializedName("endIndex")
    public int endIdx;

    public void parseJson(JSONObject jsonObject) {
        if (jsonObject == null) return;
        startIdx = jsonObject.optInt("startIndex");
        endIdx = jsonObject.optInt("endIndex");
    }
}
