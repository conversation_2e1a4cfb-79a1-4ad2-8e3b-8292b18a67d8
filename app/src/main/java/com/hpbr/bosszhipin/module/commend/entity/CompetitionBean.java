package com.hpbr.bosszhipin.module.commend.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

import net.bosszhipin.api.bean.ServerCompetitionBean;

import org.json.JSONObject;

/**
 * 作者：ZhouYou
 * 日期：2016/12/5.
 */
@Table("CompetitionBean")
public class CompetitionBean extends BaseEntity {
    private static final long serialVersionUID = -1;

    /**
     * 道具使用剩余次数
     */
    public int itemLeftCount;
    /**
     * 开聊的总数
     */
    public int chatCount;
    /**
     * 针对职位开聊的排名
     */
    public int chatRank;
    /**
     * 竞争力排名
     */
    public int competitiveRank;

    /**
     * 道具是否使用
     */
    public boolean isItemUsed;
    /**
     * 跳转链接
     */
    public String linkUrl;

    @Deprecated
    public void parseJson(JSONObject jo) {
        if (jo == null) return;
        itemLeftCount = jo.optInt("leftCount");
        chatCount = jo.optInt("chatCount");
        chatRank = jo.optInt("chatNum");
        competitiveRank = jo.optInt("rank");
        isItemUsed = jo.optBoolean("used");
        linkUrl = jo.optString("jumpUrl");
    }

    public void parseFromServer(ServerCompetitionBean competitionBean) {
        if (competitionBean == null) return;
        itemLeftCount = competitionBean.leftCount;
        chatCount = competitionBean.chatCount;
        chatRank = competitionBean.chatNum;
        competitiveRank = competitionBean.rank;
        isItemUsed = competitionBean.used;
        linkUrl = competitionBean.jumpUrl;
    }
}
