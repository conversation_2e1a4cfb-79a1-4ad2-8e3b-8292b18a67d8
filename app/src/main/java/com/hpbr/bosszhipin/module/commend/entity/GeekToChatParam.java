package com.hpbr.bosszhipin.module.commend.entity;

import com.hpbr.bosszhipin.module.commend.manager.GeekStartChatBlockDialogManager;

import net.bosszhipin.api.bean.ServerJobCardBean;

import androidx.annotation.NonNull;


/**
 * @ClassName: GeekJobListToChatParam
 * @Description:
 * 调用方式 1.能获取到 ServerJobCardBean jobCardBean 直接调用GeekJobListToChatUtils.clickToChat(GeekJobListToChatParam.obj(jobCardBean),activity);
 * 2. 获取不到 1.GeekJobListToChatUtils.clickToChat(GeekJobListToChatParam.obj().set相关参数,activity)
 * @Author: yanglinjie
 * @Date: 2024/5/15 13:53
 */
public  class GeekToChatParam {
    public String lid;
    public long expectId;
    public String securityId;
    public int friendSource;
    public long jobId;
    public String jobName;
    public String jobDegree;
    public long bossId;
    public String cityName;
    public String encryptJobId;

    public int entrance;
    public  boolean isJumpToChatPage = true;//是否跳转到开聊页，跳转需要singleChatParam参数，不跳转直接在当前页开聊
    public boolean isOneKeySign = false;//是否是一键报名 不需要跳转到聊天界面  默认不是
    public boolean isFriend = false; //默认不是好友

    public int source;
    public GeekStartChatBlockDialogManager.BlockSkip blockSkip = GeekStartChatBlockDialogManager.BlockSkip.RUN_CHAT_BLOCKED_TO_JD;//拦截器阻断，新用户，开聊框架，走哪些阻断，默认走阻断进入详情页面

    public static GeekToChatParam obj() {
        return new GeekToChatParam();
    }

    public static GeekToChatParam obj(@NonNull  ServerJobCardBean serverJobCardBean) {
        return new GeekToChatParam()
                .setJobDegree(serverJobCardBean.jobDegree)
                .setBossId(serverJobCardBean.bossId)
                .setCityName(serverJobCardBean.cityName)
                .setJobName(serverJobCardBean.jobName)
                .setJobId(serverJobCardBean.jobId)
                .setBossId(serverJobCardBean.bossId)
                .setSecurityId(serverJobCardBean.securityId)
                .setExpectId(serverJobCardBean.expectId)
                .setLid(serverJobCardBean.lid)
                .setEncryptJobId(serverJobCardBean.encryptJobId);
    }

    public GeekToChatParam setSource(int source) {
        this.source = source;
        return this;
    }

    public GeekToChatParam setEntrance(int entrance) {
        this.entrance = entrance;
        return this;
    }

    public GeekToChatParam setJumpToChatPage(boolean jumpToChatPage) {
        isJumpToChatPage = jumpToChatPage;
        return this;
    }

    public GeekToChatParam setOneKeySign(boolean oneKeySign) {
        isOneKeySign = oneKeySign;
        return this;
    }

    public GeekToChatParam setFriend(boolean friend) {
        isFriend = friend;
        return this;
    }

    public GeekToChatParam setBlockSkip(GeekStartChatBlockDialogManager.BlockSkip blockSkip) {
        this.blockSkip = blockSkip;
        return this;
    }



    public GeekToChatParam setSecurityId(String securityId) {
        this.securityId = securityId;
        return this;
    }

    public GeekToChatParam setFriendSource(int friendSource) {
        this.friendSource = friendSource;
        return this;
    }

    public GeekToChatParam setJobId(long jobId) {
        this.jobId = jobId;
        return this;
    }

    public GeekToChatParam setJobName(String jobName) {
        this.jobName = jobName;
        return this;
    }

    public GeekToChatParam setJobDegree(String jobDegree) {
        this.jobDegree = jobDegree;
        return this;
    }

    public GeekToChatParam setBossId(long bossId) {
        this.bossId = bossId;
        return this;
    }

    public GeekToChatParam setCityName(String cityName) {
        this.cityName = cityName;
        return this;
    }

    public GeekToChatParam setLid(String lid) {
        this.lid = lid;
        return this;
    }

    public GeekToChatParam setExpectId(long expectId) {
        this.expectId = expectId;
        return this;
    }

    public GeekToChatParam setEncryptJobId(String encryptJobId) {
        this.encryptJobId = encryptJobId;
        return this;
    }
}
