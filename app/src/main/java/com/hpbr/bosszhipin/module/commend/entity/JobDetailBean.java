package com.hpbr.bosszhipin.module.commend.entity;

import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.module.login.entity.ShareTextBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.entity.JobMatchBean;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.L;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.JobDetailResponse;
import net.bosszhipin.api.bean.ServerAddressVerificationGuideBean;
import net.bosszhipin.api.bean.ServerDisabledJobAcceptBean;
import net.bosszhipin.api.bean.ServerDisabledJobAcceptInfoBean;
import net.bosszhipin.api.bean.ServerDisabledJobAcceptListBean;
import net.bosszhipin.api.bean.ServerDisabledJobProjectBean;
import net.bosszhipin.api.bean.ServerHighRiskBarBean;
import net.bosszhipin.api.bean.ServerHumanTagSuggestBean;
import net.bosszhipin.api.bean.ServerJobDetailPassivityPhoneCallGuideBean;
import net.bosszhipin.api.bean.ServerJobExtraBarBean;
import net.bosszhipin.api.bean.ServerJobHeadInfoBean;
import net.bosszhipin.api.bean.ServerJobHunterBean;
import net.bosszhipin.api.bean.ServerJobRecruitTimeEndBarBean;
import net.bosszhipin.api.bean.ServerJobRefundTextBean;
import net.bosszhipin.api.bean.ServerJobTopTakeEffectBean;
import net.bosszhipin.api.bean.ServerJobTraitBean;
import net.bosszhipin.api.bean.ServerOverSeaDetailAddressItemBean;
import net.bosszhipin.api.bean.ServerOverSeaDetailTipBean;
import net.bosszhipin.api.bean.ServerShareTextBean;
import net.bosszhipin.api.bean.ServerSuggestInfoBean;
import net.bosszhipin.api.bean.ServerSystemCloseTipBean;
import net.bosszhipin.api.bean.ServerWorkEnvironmentInfoBean;
import net.bosszhipin.api.bean.ServerWuKongOuterGuideBean;
import net.bosszhipin.api.bean.job.ServerBossJobDetailBottomInfoBean;
import net.bosszhipin.api.bean.job.ServerBossJobDetailRecruitDataBean;
import net.bosszhipin.api.bean.job.ServerBossJobQABean;
import net.bosszhipin.boss.bean.ServerJobRejectTipBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 职位详情页的全量bean， 包含了其他的数据。比如：分享相关，一般用在职位详情页。
 * Created by zhouyou on 15/4/13.
 */
@Table("JobDetail")
public class JobDetailBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    /**
     * 工作的基本信息
     */
    public JobBean job;
    /**
     * 描述
     */
    @Deprecated
    public String userDescription;

    /**
     * 微信分享本职位url
     */
    public String wapShareUrl;

    /**
     * 分享内容
     */
    public ShareTextBean jobShareText;

    /**
     * 分享小程序中的图片信息
     */
    public String shareImgUrl;

    /**
     * 分享小程序，小程序页内的路径
     */
    public String miniPath;

    /**
     * tag 是否显示右上角的新 联系中
     */
    @Deprecated
    public int tag;

    /**
     * 是否感兴趣
     */
    public boolean isInterest;

    @Deprecated
    public AdvantageContentBean advantageContentBean;

    /**
     * boss职位数
     */
    @Deprecated
    public int positionCount;

    /**
     * 感兴趣的数目
     */
    @Deprecated
    public int interestCount;

    public int favourCount;

    /**
     * 查看的数目
     */
    public int viewCount;
    /**
     * 联系过的人数
     */
    public int contactCount;

    /**
     * keyword 列表
     */
    @Deprecated
    public List<AdvantageKeywordBean> keywords;

    /**
     * VIP标记（皇冠）URL
     */
    @Deprecated
    public String rewardCrown;

    /**
     * VIP标记（VIP）URL
     */
    @Deprecated
    public String rewardIcon;
    /**
     * VIP角标
     */
    @Deprecated
    public String rewardTag;

    /**
     * VIP标记的描述
     */
    @Deprecated
    public String rewardDescription;

    /**
     * 温馨提示
     */
    public String notice;

    // 该job是否与自己的期望职位匹配
    @Deprecated
    public JobMatchBean jobMatchBean;

    /**
     * 判断是否在详情页展示公司主页入口的标志
     */
    @Deprecated
    public boolean brandEntry;
    /**
     * 牛人期望id
     */
    public long expectId;
    /**
     * 红包金额
     */
    public int redEnvelope;
    /**
     * 红包提示
     */
    public String redEnvelopeCallText;
    /**
     * 牛人电话使用的关系id
     */
    public long geekCallItemUsedId;
    /**
     * 0 - 无一键投递 | 1 - 置顶卡类型 | 2 - 急聘置顶卡
     */
    public int rewardType;

    public boolean isTopCardType() {
        return rewardType > 0 && rewardType < 3;
    }

    /**
     * 简历是否已经发出
     */
    public boolean hasResumeSend;

    /**
     * 发布时间
     */
    @Deprecated
    public String pubTimeDesc;

    /**
     * 急聘图片
     */
    @Deprecated
    public String rewardTopIcon;
    /**
     * 竞争力分析数据
     */
    public CompetitionBean competition;

    @Deprecated
    public boolean isHotPosition;

    /**
     * 是否是付费职位
     */
    public boolean isPositionOnPay;

    @Deprecated
    public boolean isShowBrowseTimes;

//    public String privilegeUrl;

    /**
     * 3 - 火爆职位绿背景
     * 4 - 火爆职位红背景
     * 5 - vip绿背景
     * 6 - vip红背景
     */
    @Deprecated
    public int hotTemplate;

    public ServerJobHunterBean jobHunterBean;

    public ServerJobHeadInfoBean jobHeadInfoBean;

    @Deprecated
    public String hotTitle;

    public String staticMapUrl; // 地图图片
    public String baiduStaticMapUrl; // 地图图片

    public boolean blueCollar;//7.10职位是否为蓝领职位

    public ServerHumanTagSuggestBean humanTagSuggest;
    public ServerJobRejectTipBean modifyAuditSuggest;
    public ServerSystemCloseTipBean systemCloseTip;//902.100 职位关闭原因弹窗
    /*720 简问易答*/
    @Deprecated
    public ServerBossJobQABean jobQuestion;

    /*8.01 驳回退款职位职位详情提示*/
    public String rejectJobTip;
    public String projectJobTip;

    public ServerJobTraitBean jobTrait;

    public boolean phoneExchangeFree;
    public String openPhoneExchangeTipsText;
    /*810 工作环境*/
    public ServerWorkEnvironmentInfoBean workEnvironment;
    /*814 置顶职位生效时间*/
    public ServerJobTopTakeEffectBean itemBar;
    /*814 职位功能条*/
    public ServerJobExtraBarBean jobExtraBar;
    public ServerJobExtraBarBean jobTopBar;
    public ServerHighRiskBarBean highRiskBar;
    public ServerJobRecruitTimeEndBarBean recruitTimeEndBar;
    /*817 曝光城市*/
    public String exposureShowCitys;
    public List<ServerSuggestInfoBean> jobSuggest;
    public JobDetailResponse.WorkTasteBean brandWorkTaste = new JobDetailResponse.WorkTasteBean();

    public boolean anotherBzb; //是否是代付职位
    public String shareRecruitSwitchDesc;
    public int shareRecruitSwitch; //共享职位招聘数据开关 1打开 0关闭

    /*907.71【商业】用户退款体验优化 - 退款提示条*/
    public ServerJobRefundTextBean jobRefundText;
    public ServerDisabledJobProjectBean disabledJobProject;
    public ServerDisabledJobAcceptBean jobDisabledAccept;
    public ServerWuKongOuterGuideBean wuKongOuterGuide;
    public ServerAddressVerificationGuideBean addressGuide;
    public boolean emptyInviteData;// 1017.126 招聘数据是否为空

    public ServerJobDetailPassivityPhoneCallGuideBean passivityPhoneCallGuideUsedVO;

    public ServerOverSeaDetailTipBean overseasDetailTip;// 1126.62 驻外职位 详情顶部提示条
    public String jumpOverseasAddressUrl;// 1126.62 驻外职位 调整地址列表页
    public List<ServerOverSeaDetailAddressItemBean> overseasAddressList;// 1126.62 驻外职位 底部地址列表
    public ServerBossJobDetailBottomInfoBean bottomInfo;
    public int canReplicateJob;//1 // 表示可复制
    public String replicateJobText; //"复制并新建职位" // 复制文案
    public ServerBossJobDetailRecruitDataBean recruitData; //13.11版本客户端"职位招聘数据"模块渲染都切到该字段

    public void parseFromServer(JobDetailResponse jobDetailResponse) {
        if (jobDetailResponse == null) return;
        job = new JobBean();
        if (jobDetailResponse.job != null) {
            job.parseFromServer(jobDetailResponse.job);
        }
        geekCallItemUsedId = jobDetailResponse.usedId;
        redEnvelope = jobDetailResponse.redAmount;
        redEnvelopeCallText = jobDetailResponse.callGeekText;
        viewCount = jobDetailResponse.viewCount;
        favourCount = jobDetailResponse.favourCount;
        contactCount = jobDetailResponse.contactCount;
        wapShareUrl = jobDetailResponse.wapShareUrl;
        shareImgUrl = jobDetailResponse.shareImgUrl;
        miniPath = jobDetailResponse.miniPath;
        rewardType = jobDetailResponse.oneKeyType;
        hasResumeSend = jobDetailResponse.resumeSend;
        notice = jobDetailResponse.notice;
//        isPositionOnPay = (jobDetailResponse.hotPayStatus == 1);
//        job.isPositionOnPay = isPositionOnPay;
//        privilegeUrl = jobDetailResponse.privilegeUrl;
        if (jobDetailResponse.competitiveInfo != null) {
            competition = new CompetitionBean();
            competition.parseFromServer(jobDetailResponse.competitiveInfo);
        }
        if (!TextUtils.isEmpty(jobDetailResponse.jobShareText)) {
            try {
                ServerShareTextBean jobShare = GsonUtils.getGson().fromJson(jobDetailResponse.jobShareText, ServerShareTextBean.class);
                if (jobShare != null) {
                    jobShareText = new ShareTextBean();
                    jobShareText.parse(jobShare);
                }
            } catch (Exception e) {
                L.d("分享数据解析失败");
            }
        }
        isInterest = jobDetailResponse.interest;
        expectId = jobDetailResponse.geekExpect;
        jobHunterBean = jobDetailResponse.jobHeadhunter;
        jobHeadInfoBean = jobDetailResponse.headInfo;
        staticMapUrl = jobDetailResponse.staticMapUrl;
        baiduStaticMapUrl = jobDetailResponse.baiduStaticMapUrl;
        blueCollar = jobDetailResponse.blueCollar;

        humanTagSuggest = jobDetailResponse.humanTagSuggest;
        modifyAuditSuggest = jobDetailResponse.modifyAuditSuggest;
        systemCloseTip = jobDetailResponse.systemCloseTip;
        rejectJobTip = jobDetailResponse.rejectJobTip;
        projectJobTip = jobDetailResponse.projectJobTip;
        jobQuestion = jobDetailResponse.jobQuestion;
        jobTrait = jobDetailResponse.jobTrait;
        phoneExchangeFree = jobDetailResponse.phoneExchangeFree;
        openPhoneExchangeTipsText = jobDetailResponse.openPhoneExchangeTipsText;
        workEnvironment = jobDetailResponse.workEnvironment;
        itemBar = jobDetailResponse.itemBar;
        jobExtraBar = jobDetailResponse.jobExtraBar;
        jobTopBar = jobDetailResponse.jobTopBar;
        highRiskBar = jobDetailResponse.highRiskBar;
        recruitTimeEndBar = jobDetailResponse.recruitTimeEndBar;
        brandWorkTaste = jobDetailResponse.brandWorkTaste;
        exposureShowCitys = jobDetailResponse.exposureShowCitys;
        jobSuggest = jobDetailResponse.jobSuggest;
        anotherBzb = jobDetailResponse.anotherBzb;
        shareRecruitSwitchDesc = jobDetailResponse.shareRecruitSwitchDesc;
        shareRecruitSwitch = jobDetailResponse.shareRecruitSwitch;
        jobRefundText = jobDetailResponse.jobRefundText;
        disabledJobProject = jobDetailResponse.disabledJobProject;
        jobDisabledAccept = jobDetailResponse.jobDisabledAccept;
        wuKongOuterGuide = jobDetailResponse.wuKongOuterGuide;
        addressGuide = jobDetailResponse.addressVerificationGuide;
        emptyInviteData = jobDetailResponse.emptyInviteData;
        passivityPhoneCallGuideUsedVO = jobDetailResponse.passivityPhoneCallGuideUsedVO;
        overseasDetailTip = jobDetailResponse.overseasDetailTip;
        overseasAddressList = jobDetailResponse.overseasAddressList;
        jumpOverseasAddressUrl = jobDetailResponse.jumpOverseasAddressUrl;
        bottomInfo = jobDetailResponse.bottomInfo;
        canReplicateJob = jobDetailResponse.canReplicateJob;
        replicateJobText = jobDetailResponse.replicateJobText;
        recruitData = jobDetailResponse.recruitData;
//        disabledJobProject = mockProject();
//        jobDisabledAccept = mockAccept();
    }

    private ServerDisabledJobProjectBean mockProject() {
        ServerDisabledJobProjectBean bean = new ServerDisabledJobProjectBean();
        bean.title = "无障碍就业公益计划";
        bean.content = "改善残障人士求职环境，共同建设无障碍社会";
        bean.tip = "了解详情";
        bean.url = "https://baidu.com";
        return bean;
    }

    /**
     * 如果使用百度地图，不为空返回baiduStaticMapUrl，为空则使用staticMapUrl兜底
     */
    public String getStaticMapUrl() {
        if (!MapViewCompat.isForceAMap() && AndroidDataStarGray.getInstance().isUserBMapTypeV3()) {
            //baiduStaticMapUrl为空，使用staticMapUrl兜底
            return !TextUtils.isEmpty(baiduStaticMapUrl) ? baiduStaticMapUrl : staticMapUrl;
        } else {
            return staticMapUrl;
        }
    }

    private ServerDisabledJobAcceptBean mockAccept() {
        ServerDisabledJobAcceptBean bean = new ServerDisabledJobAcceptBean();
        ServerDisabledJobAcceptInfoBean infoBean = new ServerDisabledJobAcceptInfoBean();
        infoBean.jobDisabledAcceptInfoList = new ArrayList<>();
        ServerDisabledJobAcceptListBean listBean = new ServerDisabledJobAcceptListBean();
        listBean.disabledName = "视力残疾";
        listBean.disabledDesc = "该职位支持三级及四级障碍人群及以下";
        listBean.icon = "https://img.bosszhipin.com/beijin/icon/29271f01116743cfad109b9b8f4b162b09d72be1cb9f5d492f685227a2f8896e.png";
        infoBean.jobDisabledAcceptInfoList.add(listBean);
        infoBean.jobDisabledAcceptInfoList.add(listBean);
        infoBean.jobDisabledAcceptInfoList.add(listBean);
        infoBean.jobDisabledAcceptInfoList.add(listBean);
        bean.url = "https://baidu.com";
        bean.disabledAcceptInfo = infoBean;

        return bean;
    }
}

