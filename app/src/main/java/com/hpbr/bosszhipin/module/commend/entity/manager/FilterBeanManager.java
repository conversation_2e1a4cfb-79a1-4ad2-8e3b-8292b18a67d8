package com.hpbr.bosszhipin.module.commend.entity.manager;

import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.monch.lbase.util.LList;

import java.util.List;

/**
 * Created by zhouyou on 2015/9/15.
 */
public class FilterBeanManager {

    private static FilterBeanManager instance = new FilterBeanManager();

    public static FilterBeanManager getInstance() {
        return instance;
    }

    /**
     * 牛人筛选实体
     */
    private FilterBean filterGeekParam1, filterGeekParam2, filterGeekParam3, filterGeekParam4, filterGeekParam5, filterGeekParam6;

    /**
     * BOSS筛选实体
     */
    private FilterBean filterBossParam1, filterBossParam2, filterBossParam3, filterBossParam4,
            filterSchool, filterAge, filterChangeJobFrequency, exchangeResumeWithColleague, filterBigCompany, filterIntentionStrength, filterCallPhone,
            filterGender,filterRecentNotView;

    /******************
     * 获取牛人相关的筛选条件
     *******************/
    private FilterBean getGeekFilterSalary() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 0) return null;
        return list.get(0);
    }

    private FilterBean getGeekFilterExp() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 1) return null;
        return list.get(1);
    }

    private FilterBean getGeekFilterScale() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 2) return null;
        return list.get(2);
    }

    private FilterBean getGeekFilterDegree() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 3) return null;
        return list.get(3);
    }

    private FilterBean getGeekFilterStage() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 4) return null;
        return list.get(4);
    }

    private FilterBean getGeekFilterIndustry() {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getGeekFilterList();
        if (list == null || list.size() <= 5) return null;
        return list.get(5);
    }

    public FilterBean getFilterGeekParam1() {
        if (filterGeekParam1 == null) {
            filterGeekParam1 = getGeekFilterSalary();
        }
        return filterGeekParam1;
    }

    public FilterBean getFilterGeekParam2() {
        if (filterGeekParam2 == null) {
            filterGeekParam2 = getGeekFilterExp();
        }
        return filterGeekParam2;
    }

    public FilterBean getFilterGeekParam3() {
        if (filterGeekParam3 == null) {
            filterGeekParam3 = getGeekFilterScale();
        }
        return filterGeekParam3;
    }

    public FilterBean getFilterGeekParam4() {
        if (filterGeekParam4 == null) {
            filterGeekParam4 = getGeekFilterDegree();
        }
        return filterGeekParam4;
    }

    public FilterBean getFilterGeekParam5() {
        if (filterGeekParam5 == null) {
            filterGeekParam5 = getGeekFilterStage();
        }
        return filterGeekParam5;
    }

    public FilterBean getFilterGeekParam6() {
        if (filterGeekParam6 == null) {
            filterGeekParam6 = getGeekFilterIndustry();
        }
        return filterGeekParam6;
    }

    /******************
     * 获取BOSS相关的筛选条件
     *******************/

    private FilterBean getBossFilterList(int position) {
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getBossFilterList();
        return LList.getElement(list, position);
    }

    private FilterBean getBossFilterParam1() {
        return getBossFilterList(0);
    }

    private FilterBean getBossFilterParam2() {
        return getBossFilterList(1);
    }

    private FilterBean getBossFilterParam3() {
        return getBossFilterList(2);
    }

    private FilterBean getBossFilterParam4() {
        return getBossFilterList(3);
    }

    private FilterBean getBossFilterParam5() {
        return getBossFilterList(4);
    }

    private FilterBean getBossFilterParam6() {
        return getBossFilterList(5);
    }

    private FilterBean getBossFilterParam7() {
        return getBossFilterList(6);
    }

    private FilterBean getBossFilterParam8() {
        return getBossFilterList(7);
    }

    private FilterBean getBossFilterParam9() {
        return getBossFilterList(8);
    }

    private FilterBean getBossFilterParam10() {
        return getBossFilterList(9);
    }

    private FilterBean getBossFilterParam11() {
        return getBossFilterList(10);
    }

    private FilterBean getBossFilterParam12() {
        return getBossFilterList(11);
    }


    public FilterBean getFilterBossParam1() {
        if (filterBossParam1 == null) {
            filterBossParam1 = getBossFilterParam1();
        }
        return filterBossParam1;
    }

    public FilterBean getFilterBossParam2() {
        if (filterBossParam2 == null) {
            filterBossParam2 = getBossFilterParam2();
        }
        return filterBossParam2;
    }

    public FilterBean getFilterBossParam3() {
        if (filterBossParam3 == null) {
            filterBossParam3 = getBossFilterParam3();
        }
        return filterBossParam3;
    }

    public FilterBean getFilterBossParam4() {
        if (filterBossParam4 == null) {
            filterBossParam4 = getBossFilterParam4();
        }
        return filterBossParam4;
    }

    public FilterBean getFilterSchool() {
        if (filterSchool == null) {
            filterSchool = getBossFilterParam5();
        }
        return filterSchool;
    }

    public FilterBean getFilterChangeJobFrequency() {
        if (filterChangeJobFrequency == null) {
            filterChangeJobFrequency = getBossFilterParam6();
        }
        return filterChangeJobFrequency;
    }

    public FilterBean getExchangeResumeWithColleague() {
        if (exchangeResumeWithColleague == null) {
            exchangeResumeWithColleague = getBossFilterParam7();
        }
        return exchangeResumeWithColleague;
    }

    public FilterBean getFilterBigCompany() {
        if (filterBigCompany == null) {
            filterBigCompany = getBossFilterParam8();
        }
        return filterBigCompany;
    }

    public FilterBean getFilterIntentionStrength() {
        if (filterIntentionStrength == null) {
            filterIntentionStrength = getBossFilterParam9();
        }
        return filterIntentionStrength;
    }

    public FilterBean getFilterGender() {
        if (filterGender == null) {
            filterGender = getBossFilterParam10();
        }
        return filterGender;
    }

    public FilterBean getFilterRecentNotView() {
        if (filterRecentNotView == null) {
            filterRecentNotView = getBossFilterParam11();
        }
        return filterRecentNotView;
    }

    public FilterBean getFilterCallPhone() {
        if (filterCallPhone == null) {
            filterCallPhone = getBossFilterParam12();
        }
        return filterCallPhone;
    }



    public FilterBean getFilterAge() {
        if (filterAge == null) {
            filterAge = new FilterBean();
            filterAge.paramName = "age";
            filterAge.name = "年龄";
            filterAge.subFilterConfigModel.clear();
            for (int i = 16; i < 36; i++) {
                filterAge.subFilterConfigModel.add(new FilterBean(i, i + "岁"));
            }
            filterAge.subFilterConfigModel.add(new FilterBean(-1, "36及以上"));
        }
        return filterAge;
    }
}
