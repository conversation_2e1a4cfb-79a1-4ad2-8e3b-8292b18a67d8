package com.hpbr.bosszhipin.module.commend.interfaces;

import com.hpbr.bosszhipin.module.my.entity.LevelBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 作者：ZhouYou
 * 日期：2016/11/23.
 * 选择城市
 */
public interface IGeekToChatListener {

    /**
     * 开聊结果
     *
     * @param isChatSucceed true 开聊成功 false 没有开聊cheng'g
     */
    void onChatResult(boolean isChatSucceed);

    /**
     * 开聊阻断
     */
    void onBlocked();
}
