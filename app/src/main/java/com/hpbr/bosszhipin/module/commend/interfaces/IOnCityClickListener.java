package com.hpbr.bosszhipin.module.commend.interfaces;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 作者：ZhouYou
 * 日期：2016/11/23.
 * 选择城市
 */
public interface IOnCityClickListener {

    default void onCityClick(@NonNull LevelBean item){};
    default void onCityClick(String title, @NonNull LevelBean item){}

    default boolean isItemSelectable(@NonNull LevelBean item) {
        return true;
    }

    default boolean isMultiSelectionEnabled() {
        return false;
    }

    default boolean isFull() {
        return false;
    }

    default boolean hasCity(@NonNull LevelBean targetCity) {
        return false;
    }

    @Nullable
    default LevelBean pickAssociatedCity(@NonNull LevelBean targetCity) {
        return null;
    }

    default boolean isItemSelected(@NonNull LevelBean cityItem) {
        return false;
    }

    default boolean isClearable() { // 是否可以选空（未选择任何选项时，“确定”按钮可点击保存）
        return false;
    }

    @NonNull
    default List<LevelBean> getSelectedCities() {
        return new ArrayList<>();
    }

}
