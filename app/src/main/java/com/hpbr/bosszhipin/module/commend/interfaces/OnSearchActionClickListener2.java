package com.hpbr.bosszhipin.module.commend.interfaces;

import com.hpbr.bosszhipin.module.commend.SearchHistoryHelper2.Query;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2017/7/27.
 */
public interface OnSearchActionClickListener2 {

    /**
     * 设置文案
     *
     * @param text
     */
    void setEditText(String text);

    /**
     * 隐藏软键盘
     */
    void hideSoftKeyboard();

    /**
     * 保存搜索记录到本地
     *
     * @param query
     */
    void saveHistoryRecordInLocal(Query query);

    /**
     * 展示搜索结果
     *
     * @param searchType 搜索类型
     * @param query
     */
    void showSearchResult(int searchType, Query query);
}
