package com.hpbr.bosszhipin.module.commend.interfaces;

import com.hpbr.bosszhipin.module.commend.SearchHistoryHelper2.Query;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2017/10/31.
 */
public class OnSearchActionClickListenerAdapter implements OnSearchActionClickListener2 {
    @Override
    public void setEditText(String text) {

    }

    @Override
    public void hideSoftKeyboard() {

    }

    @Override
    public void saveHistoryRecordInLocal(Query query) {

    }

    @Override
    public void showSearchResult(int searchType, Query query) {

    }
}
