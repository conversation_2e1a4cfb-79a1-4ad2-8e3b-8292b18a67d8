package com.hpbr.bosszhipin.module.commend.interfaces;

/**
 * @ClassName ：SearchCardType
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/1/5  8:14 PM
 */
public @interface SearchCardType {
    /*「集团」卡片*/
    int SEARCH_CARD_TYPE_GROUP = 1;
    /*职位卡片*/
    int SEARCH_CARD_TYPE_POSITION = 2;
    /*「标准品牌」卡片*/
    int SEARCH_CARD_TYPE_STANDARD_BRAND = 4;
    /*暂未搜到更多职位，为你推荐以下职位提示的卡片*/
    int SEARCH_CARD_TYPE_RECOMMEND_HEADER = 5;
    /*"纠错"卡片*/
    int SEARCH_CARD_TYPE_CORRECT = 9;
    /*「公司列表」卡片*/
    int SEARCH_CARD_TYPE_COMPANY_LIST = 10;
    /*「查看附近职位」卡片*/
    int SEARCH_CARD_TYPE_CHECK_NEARBY_JOB = 11;
    /*「查看更多职位」卡片*/
    int SEARCH_CARD_TYPE_CHECK_MORE_JOB = 12;
    /*「订阅」卡片*/
    int SEARCH_CARD_TYPE_SUBSCRIBE = 13;
    /*「搜索反馈」卡片*/
    int SEARCH_CARD_TYPE_FEEDBACK = 14;
    /*「相关搜索」卡片*/
    int SEARCH_CARD_TYPE_RELATED_SEARCH_TIP = 15;
    /*「全国召回」卡片（919.36 搜公司列表增加全国召回）*/
    int SEARCH_CARD_TYPE_CITY_SUGGEST_TIP = 16;
    /*「安心保」卡片（1001）*/
    int SEARCH_CARD_TYPE_PEACE_OF_MIND_PROTECT = 17;
    /*「屏蔽」提示卡片*/
    int SEARCH_CARD_TYPE_SHIELD_TIP = 18;
    /*「无障碍求职」卡片*/
    int SEARCH_CARD_TYPE_ACCESSIBLE_APPLY_JOB = 21;
    /*「你可能喜欢」卡片*/
    int SEARCH_CARD_TYPE_JOB_MAYBE_LIKE = 22;
    /*「行业」卡片*/
    int SEARCH_CARD_TYPE_INDUSTRY = 23;
    /*「内容」卡片（包含「职业百科」 和  「文章内容」）*/
    int SEARCH_CARD_TYPE_CONTENT = 24;
    /*「异城职位提示」卡片*/
    int SEARCH_CARD_TYPE_DIFFERENT_CITY_TIP = 25;
    /*「猎头推荐提示」卡片*/
    int SEARCH_CARD_TYPE_HEAD_HUNTING_RECOMMEND_TIP = 26;
    /*「残疾人专区」入口卡片*/
    int SEARCH_CARD_TYPE_HANDICAPPED_ZONE_ENTER = 27;
    /*「驻外岗位专题」入口卡片*/
    int SEARCH_CARD_TYPE_OVERSEA_JOB = 28;
    /**
     * 1202.51 安心保 推荐相似职位
     */
    int SEARCH_CARD_TYPE_RECOMMEND_JOB = 29;

    /**
     * 1210.603 自然语言卡片 4个样式
     */
    int SEARCH_CARD_TYPE_NATURE_LANGUAGE_1 = 30;
    int SEARCH_CARD_TYPE_NATURE_LANGUAGE_2 = 31;
    int SEARCH_CARD_TYPE_NATURE_LANGUAGE_3 = 32;
    int SEARCH_CARD_TYPE_NATURE_LANGUAGE_4 = 33;

    /**
     *1217.621新样式带 立即开聊按钮
     */
    int SEARCH_CARD_TYPE_POSITION_WITH_CHAT = 34;

    int SEARCH_CARD_TYPE_AD_GUIDE_ADD_EXPECT = 35;

    int SEARCH_CARD_TYPE_RECOMMEND_JOB_1222801 = 36;


    int SEARCH_CARD_TYPE_SCHOOL_RECRUIT_SPEC = 37;
    
    /**
     * 寻址入口卡片
     */
    int SEARCH_CARD_TYPE_FIND_APP_FUNCTION = 39;
    
    /**
     * 服务返回相关类型
     */
    int  SERVER_SEARCH_CARD_TYPE_POSITION_WITH_CHAT=1; //1217.621新样式带 立即开聊按钮

    int SERVER_AD_GUIDE_ADD_EXPECT=19;
    
    /**
     * 服务端寻址入口卡片类型
     */
    int SERVER_FIND_APP_FUNCTION=22;
}
