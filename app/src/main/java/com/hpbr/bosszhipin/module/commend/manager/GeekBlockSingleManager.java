//package com.hpbr.bosszhipin.module.commend.manager;
//
//import android.app.Activity;
//
//import androidx.annotation.NonNull;
//
//import com.hpbr.bosszhipin.common.dialog.CertificationViolateWarningDialog;
//import com.twl.http.ApiData;
//import com.twl.http.HttpExecutor;
//import com.twl.http.error.ErrorReason;
//import com.twl.utils.ActivityUtils;
//
//import net.bosszhipin.api.GetCertViolateWarningRequest;
//import net.bosszhipin.api.GetCertViolateWarningResponse;
//import net.bosszhipin.api.bean.ServerCertViolateWarningDialogBean;
//import net.bosszhipin.base.ApiRequestCallback;
//
///**
// * <pre>
// *     author : Wp
// *     e-mail : <EMAIL>
// *     time   : 2024/6/5 18:05
// *     desc   : TODO-WP 完善好后，使用的时候报警处理下
// *     version:
// * </pre>
// */
//public class GeekBlockSingleManager {
//
//    /*违规弹框，在所有阻断之前，只要跳转到聊天页就需要这个阻断*/
//    private ServerCertViolateWarningDialogBean violateWarningDialog;
//    private Activity mActivity;
//    private GeekBlockSingleManager() {
//    }
//
//    private static class SingletonHelper {
//        private static final GeekBlockSingleManager INSTANCE = new GeekBlockSingleManager();
//    }
//
//    @NonNull
//    public static GeekBlockSingleManager get() {
//        return SingletonHelper.INSTANCE;
//    }
//
//    public GeekBlockSingleManager setActivity(Activity activity){
//        if(null!=mActivity && null!=activity)
//        //每个页面只请求一次
//        if(null!=mActivity && null!=activity && !mActivity.getClass().getSimpleName().equals(activity.getClass().getSimpleName())){
//            violateWarningDialog = null;
//        }
//        this.mActivity = activity;
//        return this;
//    }
//
//
//    /**
//     * 1012.702 违规警告弹框--违规弹框，在所有阻断之前，只要跳转到聊天页就需要这个阻断
//     * GeekBlockSingleManager.get().setActivity(mActivity).requestCertViolateWarningInfo(isBlock -> {
//     *                 if (!isBlock) {
//     *
//     *                 }
//     *             });
//     */
//    public void requestCertViolateWarningInfo(OnViolateWarningBlockCallback onViolateWarningBlockCallback) {
//        if(null!=violateWarningDialog){
//            showViolateWarningBlockDialog();
//            if(null!=onViolateWarningBlockCallback){
//                onViolateWarningBlockCallback.OnViolateWarningBlock(true);
//            }
//            return;
//        }
//        GetCertViolateWarningRequest violateWarningRequest = new GetCertViolateWarningRequest(new ApiRequestCallback<GetCertViolateWarningResponse>() {
//            @Override
//            public void onSuccess(ApiData<GetCertViolateWarningResponse> data) {
//                GetCertViolateWarningResponse resp = data.resp;
//                if (resp != null) {
//                    if (resp.show && resp.dialog != null) {
//                        violateWarningDialog = resp.dialog;
//                        showViolateWarningBlockDialog();
//                        if (null != onViolateWarningBlockCallback) {
//                            onViolateWarningBlockCallback.OnViolateWarningBlock(true);
//                        }
//                    } else {
//                        if(null!=onViolateWarningBlockCallback){
//                            onViolateWarningBlockCallback.OnViolateWarningBlock(false);
//                        }
//                    }
//                }else{
//                    if(null!=onViolateWarningBlockCallback){
//                        onViolateWarningBlockCallback.OnViolateWarningBlock(false);
//                    }
//                }
//            }
//
//            @Override
//            public void onComplete() {
//
//            }
//
//            @Override
//            public void onFailed(ErrorReason reason) {
//                if(null!=onViolateWarningBlockCallback){
//                    onViolateWarningBlockCallback.OnViolateWarningBlock(false);
//                }
//            }
//        });
//        //noinspection unchecked
//        HttpExecutor.execute(violateWarningRequest);
//    }
//    /**
//     * 1012.702【安全】违规警示弹窗 - 违规警告弹框阻断（非匿名牛人）
//     */
//    public boolean showViolateWarningBlockDialog() {
//        if(null==mActivity) return false;
//        boolean intercept = false;
//        if (null != violateWarningDialog && ActivityUtils.isValid(mActivity)) {
//            intercept = true;
//            CertificationViolateWarningDialog d = new CertificationViolateWarningDialog(mActivity, violateWarningDialog, () -> {
//                violateWarningDialog = null;
//            });
//            d.show();
//        }
//        return intercept;
//    }
//
//    public static interface OnViolateWarningBlockCallback{
//        void OnViolateWarningBlock(boolean isBlock);
//    }
//}
