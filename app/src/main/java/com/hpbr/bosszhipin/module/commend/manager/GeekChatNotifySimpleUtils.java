package com.hpbr.bosszhipin.module.commend.manager;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.dialog.GeekChatNotifyDialog;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.contacts.util.ReportUtil;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ChatNotRemindRequest;
import net.bosszhipin.api.GetChatRemindRequest;
import net.bosszhipin.api.GetChatRemindResponse;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.ServerChatRemindBean;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Author: ZhouYou
 * Date: 2017/11/28.
 * 只用于蓝领地图引导沟通
 */
public class GeekChatNotifySimpleUtils {

    private BaseActivity activity;
    private ParamBean paramBean;
    private String viewedPositionName;
    private boolean isDianZhangZpSource;
    private Callback callback;

//    private String securityId;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public GeekChatNotifySimpleUtils(BaseActivity activity, ParamBean paramBean, String viewedPositionName, boolean dianZhangZpSource) {
        this.activity = activity;
        this.paramBean = paramBean;
        this.viewedPositionName = viewedPositionName;
        this.isDianZhangZpSource = dianZhangZpSource;
//
//        if (!TextUtils.isEmpty(mSecurityId)) {
//            securityId = mSecurityId;
//        } else {
//            securityId = paramBean.securityId;
//        }
    }

    /**
     * 获取开聊提醒数据信息
     */
    public void getChatRemind() {
        if (paramBean == null) {
            T.ss("数据错误");
            return;
        }
        GetChatRemindRequest request = new GetChatRemindRequest(new ApiRequestCallback<GetChatRemindResponse>() {

            @Override
            public void onStart() {
                activity.showProgressDialog("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<GetChatRemindResponse> data) {
                GetChatRemindResponse resp = data.resp;
                if (resp != null) {
                    if (resp.dialog != null) {
                        show(resp.dialog);
                    } else {
                        startChat();
                    }
                }
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        if (paramBean != null) {
            request.bossId = paramBean.userId;
            request.expectId = paramBean.expectId;
            request.jobId = paramBean.jobId;
            request.lid = paramBean.lid;
            request.isTopJob = paramBean.isTopJob;
            request.securityId = paramBean.securityId;
        }
        HttpExecutor.execute(request);
    }

    /**
     * 展示开聊提醒的弹框
     *
     * @param data
     */
    private void show(@NonNull ServerChatRemindBean data) {
        GeekChatNotifyDialog d = new GeekChatNotifyDialog(activity, data);
        d.setDoNothingWhenCanceling(true);
        d.setOnChatRemindDialogClickListener(new GeekChatNotifyDialog.OnChatRemindDialogClickListener() {

            @Override
            public void onStartChat() {
                startChat();
            }

            @Override
            public void onOneKeySendResume() {
                // 一键投递不处理
            }

            @Override
            public void onSendPhoneCard() {

            }

            @Override
            public void onStartProtocolJump(@NonNull String url) {
                /* 追加action参数，用于直聘协议跳转页面的逻辑处理 */
                final Uri newUri = Uri.parse(url)
                        .buildUpon()
                        .appendQueryParameter(GeekConsts.Action.KEY_ACTION, String.valueOf(GeekConsts.Action.ACTION_START_CHAT))
                        .build();
                new ZPManager(activity, newUri.toString()).handler();
            }

            @Override
            public void onStopChatRemind(long remindType) {
                setNoMoreRemind(remindType);
            }

            @Override
            public void bgAction(int actionType) {
                clickActionBgAction(data, actionType);
            }
        });
        boolean isShow = d.show();
        if (isShow) {
           AnalyticsFactory.bgAction(data.ba);
        }
    }

    private void showDialogBgAction(@NonNull ServerChatRemindBean data) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_FRAMEWORK_DIALOG_POP_UP)
                .param("p", String.valueOf(paramBean.userId))
                .param("p2", String.valueOf(paramBean.jobId))
                .param("p3", String.valueOf(paramBean.expectId))
                .param("p4", paramBean.lid)
                .param("p7", String.valueOf(data.remindType))
                .param("p8", data.showNotRemind ? "1" : "0")
                .param("p9", String.valueOf(data.garbageType))
                .build();
    }

    private void clickActionBgAction(@NonNull ServerChatRemindBean data, int actionType) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_FRAMEWORK_DIALOG_CHAT)
                .param("p", String.valueOf(paramBean.userId))
                .param("p2", String.valueOf(paramBean.jobId))
                .param("p3", String.valueOf(paramBean.expectId))
                .param("p4", paramBean.lid)
                .param("p7", String.valueOf(data.remindType))
                .param("p8", String.valueOf(actionType))
                .param("p9", String.valueOf(data.garbageType))
                .build();
    }

    private void setNoMoreRemind(long remindType) {
        ChatNotRemindRequest request = new ChatNotRemindRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        request.remindType = remindType;
        HttpExecutor.execute(request);
    }

    private void startChat() {
        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setClearTop(true);
        singleChatParam.setFriendId(paramBean.userId);
        singleChatParam.setJobId(paramBean.jobId);
        singleChatParam.setSecurityId(paramBean.securityId);
        singleChatParam.setExpectId(paramBean.expectId);
        singleChatParam.setLid(paramBean.lid);
        singleChatParam.setFrom(paramBean.getFromString());
        singleChatParam.setFromDZUser(isDianZhangZpSource);
        singleChatParam.setSimilarPosition(paramBean.similarPosition);
        singleChatParam.setNotAnim(false);
        singleChatParam.setChangePositionDesc(viewedPositionName);
        singleChatParam.setEntrance(paramBean.from == ParamBean.FROM_COM_POST ? 1 : paramBean.entrance);
        SingleRouter.startChat(activity, singleChatParam);

        ReportUtil.reportNoJobId(paramBean.jobId, getClass().getSimpleName(), "startChat");

        if (callback != null) {
            callback.onStartChat();
        }
    }

    public interface Callback {
        void onStartChat();
    }

}
