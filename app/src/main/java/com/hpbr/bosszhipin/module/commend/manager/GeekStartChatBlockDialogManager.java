package com.hpbr.bosszhipin.module.commend.manager;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.chat.CreateFriendManager;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.dialog.CallVirtualPhoneDialog;
import com.hpbr.bosszhipin.common.dialog.GeekChatNotifyDialog;
import com.hpbr.bosszhipin.common.dialog.GeekJDOneSignDialog;
import com.hpbr.bosszhipin.common.dialog.JobChatRemindNewDialog;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.commend.dialog.JDChatTipsDialog;
import com.hpbr.bosszhipin.module.contacts.util.ReportUtil;
import com.hpbr.bosszhipin.module.contacts.util.SendMessageUtil;
import com.hpbr.bosszhipin.module.position.jdintercettor.ChatInterceptorManager;
import com.hpbr.bosszhipin.module.position.utils.JDShowDialogTimeManager;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.ToastNoPermissionAsDialogUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.ChatNotRemindRequest;
import net.bosszhipin.api.GetChatRemindRequest;
import net.bosszhipin.api.GetChatRemindResponse;
import net.bosszhipin.api.ResultVirtualPhoneResponse;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.ServerButtonBean;
import net.bosszhipin.api.bean.ServerChatRemindBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.Locale;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2023/6/7 14:07
 *     desc   :
 *     开聊阻断，执行顺序如下
 *     1.合规阻断
 *     2.新用户阻断 OR 开聊框架
 *
 *     使用方式：
 *             GeekStartChatBlockDialogManager.init().builder(this)
 *                  .addIsJumpToChatPage(false)
 *                  .addStartChatParam(GeekStartChatBlockDialogManager.StartChatParam.builder())
 *                  .setStartChatListener(new GeekStartChatBlockDialogManager.OnStartChatListener() {
 *                          @Override
 *                          public void showLoading(String loadingText) {
 *
 *                          }
 *                          @Override
 *                          public void dismissLoading() {
 *
 *                          }
 *                          @Override
 *                          public void onChatFinish(boolean isStartChat) {
 *
 *                          }
 *                          @Override
 *                          public void onOneKeySingFinish(boolean isFinish) {
 *
 *                          }
 *                   })
 *                  .start();
 * </pre>
 */
public class GeekStartChatBlockDialogManager {


    private Activity mActivity;
    private StartChatParam mStartChatParam;
    private ChatInterceptorManager mChatInterceptorManager;//开聊框架之前的阻断，和开聊框架串行
    private JDChatTipsDialog chatTipsDialog;
    private OnStartChatListener onStartChatListener;
    private GeekChatNotifyDialog.OnChatRemindDialogClickListener onChatRemindDialogClickListener;
    private int buttonAction = GeekConsts.Action.ACTION_START_CHAT;//立即沟通、一键投递、发电话卡片、发微信卡片 默认立即沟通
    private SingleRouter.SingleChatParam singleChatParam;//跳转开聊所需参数
    private boolean isJumpToChatPage;//是否跳转到开聊页，跳转需要singleChatParam参数，不跳转直接在当前页开聊

    private boolean isOneKeySign;//是否是一键报名 不需要跳转到聊天界面
    private boolean isCallTel;//是否是打电话
    private String sendChatText;//发送聊天消息，不进入聊天页时使用
    private int applyJobDirectly = 1;
    private BlockSkip blockSkip = BlockSkip.RUN_ALL;//拦截器阻断，新用户，开聊框架，走哪些阻断，默认走全部

    //---------------------------------------------必须参数start---------------------------------------------

    public static GeekStartChatBlockDialogManager init() {
        return new GeekStartChatBlockDialogManager();
    }

    public GeekStartChatBlockDialogManager builder(Activity activity) {
        mActivity = activity;
        mChatInterceptorManager = new ChatInterceptorManager(activity);
        return this;
    }

    /**
     * 设置请求需要的参数
     */
    public GeekStartChatBlockDialogManager addStartChatParam(StartChatParam startChatParam) {
        mStartChatParam = startChatParam;
        return this;
    }

    /**
     * loading
     */
    public GeekStartChatBlockDialogManager setStartChatListener(OnStartChatListener onStartChatListener) {
        this.onStartChatListener = onStartChatListener;
        return this;
    }

    /**
     * 是否跳转到开聊页，true跳转需要singleChatParam参数，不跳转直接在当前页开聊
     */
    public GeekStartChatBlockDialogManager addIsJumpToChatPage(boolean isJumpToChatPage) {
        this.isJumpToChatPage = isJumpToChatPage;
        return this;
    }

    public boolean isOneKeySign() {
        return isOneKeySign;
    }

    /**
     * 是否是一键开聊
     */
    public GeekStartChatBlockDialogManager setOneKeySign(boolean oneKeySign) {
        isOneKeySign = oneKeySign;
        return this;
    }


    public boolean isCallTel() {
        return isCallTel;
    }

    public GeekStartChatBlockDialogManager setCallTel(boolean callTel) {
        isCallTel = callTel;
        return this;
    }

    public GeekStartChatBlockDialogManager setSendChatText(String sendChatText) {
        this.sendChatText = sendChatText;
        return this;
    }

    public int getApplyJobDirectly() {
        return applyJobDirectly;
    }

    public GeekStartChatBlockDialogManager setApplyJobDirectly(int applyJobDirectly) {
        this.applyJobDirectly = applyJobDirectly;
        return this;
    }

    //---------------------------------------------必须参数end---------------------------------------------


    //---------------------------------------------选配参数start---------------------------------------------

    /**
     * 注意！注意！注意！注意！注意！注意！注意！注意！注意！注意！注意！
     * 扩展回调
     * 1.如果需要处理一键投递、发送微信、发送电话需要调用这个回调自己处理
     * 2.如果开聊或者埋点，需要回调自己处理
     */
    private GeekStartChatBlockDialogManager setChatRemindDialogClickListener(GeekChatNotifyDialog.OnChatRemindDialogClickListener onChatRemindDialogClickListener) {
        this.onChatRemindDialogClickListener = onChatRemindDialogClickListener;
        return this;
    }

    /**
     * 按钮类型
     * 立即沟通、一键投递、发电话卡片、发微信卡片 默认立即沟通
     */
    public GeekStartChatBlockDialogManager addButtonAction(int buttonAction) {
        this.buttonAction = buttonAction;
        return this;
    }

    /**
     * 注意！注意！注意！注意！注意！注意！注意！注意！注意！注意！注意！
     * 跳转开聊所需参数，如果要跳到开聊页面，此参数必须传
     * 必传参数：
     * singleChatParam.setSecurityId(securityId);
     * singleChatParam.setFriendId(friendId);
     * singleChatParam.setFromDZUser(蓝白);
     * 如果securityId中没有jobId和expectId，下面也必传
     * singleChatParam.setJobId(jobId);
     * singleChatParam.setExpectId(expectId);
     */
    public GeekStartChatBlockDialogManager addToChatPageParam(SingleRouter.SingleChatParam singleChatParam) {
        this.singleChatParam = singleChatParam;
        return this;
    }

    /**
     * 拦截器阻断，新用户，开聊框架，走哪些阻断，默认RUN_ALL
     */
    public GeekStartChatBlockDialogManager setBlockSkip(BlockSkip blockSkip) {
        this.blockSkip = blockSkip;
        return this;
    }
    //---------------------------------------------选配参数end---------------------------------------------

    public void start() {
        if (null == mStartChatParam || mActivity == null) return;
        checkOneKeySign(this::startCheckBlock);
    }


    /**
     * 检查是否是一键报名
     */
    private void checkOneKeySign(Runnable runnable) {
        //是否是一键报名
        if (isOneKeySign) {
            //已经是好友
            if (!mStartChatParam.isFriend) {
                if (JDShowDialogTimeManager.needOneKeySignDialogShow()) {
                    GeekJDOneSignDialog dialog = new GeekJDOneSignDialog(mActivity);
                    dialog.setListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            JDShowDialogTimeManager.onekeySignDialogShowed();
                            runnable.run();
                        }
                    });
                    if (ActivityUtils.isValid(mActivity)) {
                        dialog.show();
                    }
                } else {
                    runnable.run();
                }
            }
        } else {
            //正常开聊
            runnable.run();
        }

    }

    /**
     * 开始检查阻断
     */
    private void startCheckBlock() {
        if (mStartChatParam.isFriend) {
            if (isJumpToChatPage) {//如果需要跳转开聊，直接进去
                jumpStartChatPage();
            } else {
                //如果不跳转开聊，需要在外面单独处理后续逻辑
                onFinish(true);
            }
            return;
        }

        //根据blockSkip判定，到底走哪些阻断 「拦截器阻断，新用户阻断，开聊框架阻断」
        if (blockSkip == BlockSkip.RUN_CHAT_FRAME_ONLY) {//只走开聊框架
            requestStartChatBlockByNet();
            return;
        }

        if (blockSkip == BlockSkip.RUN_CHAT_BLOCKED_TO_JD) {//走安心保开聊阻断
            requestStartChatBlockByNet();
            return;
        }

        if (blockSkip == BlockSkip.RUN_CHAT_FRAME_AND_NEW_USER) {//走开聊框架和新用户阻断
            if (isNewUser() && isFirstChat()) {
                //新用户打招呼用于设置，里面接口调用逻辑有点乱，修改望君慎重
                chatTipsDialog = new JDChatTipsDialog(mActivity, mStartChatParam.bossId, mStartChatParam.bossSource, mStartChatParam.jobId,
                        mStartChatParam.securityId, mStartChatParam.expectId, new JDChatTipsDialog.onChatListener() {
                    @Override
                    public void continueChat() {
                        requestStartChatBlockByNet();
                    }
                });
                if (ActivityUtils.isValid(mActivity)) {
                    chatTipsDialog.show();
                }
            } else {
                // 原开聊逻辑起始方法
                requestStartChatBlockByNet();
            }
            return;
        }

        //BlockSkip.RUN_ALL 走下面的全部阻断
        //此处为开聊框架之前的阻断，目前里面有账号合规阻断，在开聊之前加的话，动这里
        mChatInterceptorManager.isFinish(new ChatInterceptorManager.OnInterceptorActionListener() {
            @Override
            public void onContinue() {
                if (isNewUser() && isFirstChat()) {
                    //新用户打招呼用于设置，里面接口调用逻辑有点乱，修改望君慎重
                    chatTipsDialog = new JDChatTipsDialog(mActivity, mStartChatParam.bossId, mStartChatParam.bossSource, mStartChatParam.jobId,
                            mStartChatParam.securityId, mStartChatParam.expectId, new JDChatTipsDialog.onChatListener() {
                        @Override
                        public void continueChat() {
                            requestStartChatBlockByNet();
                        }
                    });
                    if (ActivityUtils.isValid(mActivity)) {
                        chatTipsDialog.show();
                    }
                } else {
                    // 原开聊逻辑起始方法
                    requestStartChatBlockByNet();
                }
            }

            @Override
            public void onCancel() {
                onFinish(false);
            }
        });
    }

    //---------------------------------------------开聊框架阻断弹框start---------------------------------------------


    /**
     * 开聊框架接口
     */
    private void requestStartChatBlockByNet() {
        if (mStartChatParam == null) {
            T.ss("数据错误");
            return;
        }

        GetChatRemindRequest request = new GetChatRemindRequest(new ApiRequestCallback<GetChatRemindResponse>() {
            @Override
            public void onStart() {
                showLoading("正在处理中");
            }

            @Override
            public void onSuccess(ApiData<GetChatRemindResponse> data) {
                GetChatRemindResponse resp = data.resp;
                if (resp != null) {
                    if (resp.dialog != null) {

                        if (blockSkip == BlockSkip.RUN_CHAT_BLOCKED_TO_JD) { //如果是安心保，不弹框走进入详情
                            if (onStartChatListener != null) {
                                onStartChatListener.onNetBlocked();
                            }
                        } else { // 正常弹阻断弹框

                            if (resp.dialog.showType == 0) {
                                showOldDialog(resp.dialog);
                            } else {
                                show1001NewDialog(resp.dialog);
                            }
                        }

                    } else {
                        startChatJumpPageOrStay();
                    }
                }
            }

            @Override
            public void onComplete() {
                dismissLoading();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        if (mStartChatParam != null) {
            request.bossId = mStartChatParam.bossId;
            request.expectId = mStartChatParam.expectId;
            request.jobId = mStartChatParam.jobId;
            request.lid = mStartChatParam.lid;
            request.isTopJob = mStartChatParam.isTopJob;
            request.securityId = mStartChatParam.securityId;
            if (isOneKeySign) {
                request.applyJobDirectly = applyJobDirectly;
            }
            request.source = mStartChatParam.source;
        }
        HttpExecutor.execute(request);
    }

    /**
     * 基本所有开聊阻断都在这
     */
    private void showOldDialog(@NonNull ServerChatRemindBean data) {
        GeekChatNotifyDialog d = new GeekChatNotifyDialog(mActivity, buttonAction, data);
        d.setDoNothingWhenCanceling(true);
        d.setOnChatRemindDialogClickListener(new GeekChatNotifyDialog.OnChatRemindDialogClickListener() {

            @Override
            public void onStartChat() {
                startChatJumpPageOrStay();
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onStartChat();
                }
            }

            @Override
            public void onOneKeySendResume() {
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onOneKeySendResume();
                }
            }

            @Override
            public void onSendPhoneCard() {
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onSendPhoneCard();
                }
            }

            @Override
            public void onSendWechat() {
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onSendWechat();
                }
            }

            @Override
            public void onStartProtocolJump(@NonNull String url) {
                /* 追加action参数，用于直聘协议跳转页面的逻辑处理 */
                final Uri newUri = Uri.parse(url).buildUpon()
                        .appendQueryParameter(GeekConsts.Action.KEY_ACTION, String.valueOf(buttonAction))
                        .build();
                new ZPManager(mActivity, newUri.toString()).handler();

                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onStartProtocolJump(url);
                }
            }

            @Override
            public void onStopChatRemind(long remindType) {
                requestNoMoreRemindByNet(remindType);
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.onStopChatRemind(remindType);
                }
            }

            @Override
            public void bgAction(int actionType) {
                if (null != onChatRemindDialogClickListener) {
                    onChatRemindDialogClickListener.bgAction(actionType);
                }
            }

            @Override
            public void actionDispatcher(@NonNull ServerButtonBean bean) {
                //不是开聊或者继续
                if (bean.actionType != 7 && bean.actionType != 11) {
                    onFinish(false);
                }
            }
        });
        if (ActivityUtils.isValid(mActivity)) {
            boolean isShow = d.show();
            if (isShow) {
                AnalyticsFactory.bgAction(data.ba);
            }
        }
    }

    private void show1001NewDialog(ServerChatRemindBean data) {
        JobChatRemindNewDialog dialog = new JobChatRemindNewDialog(mActivity, data, mStartChatParam.lid, new Runnable() {
            @Override
            public void run() {
                startChatJumpPageOrStay();
            }
        }, new Runnable() {
            @Override
            public void run() {
                onFinish(false);
            }
        });
        AnalyticsFactory.bgAction(data.ba);
        if (ActivityUtils.isValid(mActivity)) {
            dialog.show();
        }
    }

    /**
     * 用户设置不再提示某种类型的开聊提醒
     */
    private void requestNoMoreRemindByNet(long remindType) {
        ChatNotRemindRequest request = new ChatNotRemindRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        request.remindType = remindType;
        HttpExecutor.execute(request);
    }
    //---------------------------------------------开聊框架阻断弹框end---------------------------------------------

    /**
     * 点击打招呼，开聊阻断完成后，执行加好友逻辑
     */
    private void requestAddFriend() {
        CreateFriendManager.FriendParams params = new CreateFriendManager.FriendParams();
        if (null != mStartChatParam) {
            params.setSecurityId(mStartChatParam.securityId);
            if (!LText.empty(mStartChatParam.lid)) {
                params.setLid(mStartChatParam.lid);
            }
            if (isOneKeySign) {
                //一键报名时添加上
                params.setApplyJobDirectly(applyJobDirectly);
            }
            params.setEntrance(mStartChatParam.entrance);
        }
        new CreateFriendManager(mActivity).createFriend(params, new CreateFriendManager.OnCreateFriendCallBack() {
            @Override
            public void onSuccessListener(ContactBean mContactBean) {
                if (TextUtils.isEmpty(sendChatText)) {
                    onFinish(true);
                } else {
                    SendMessageUtil sendMessageUtil = new SendMessageUtil();
                    sendMessageUtil.sendChatTextMessage(mContactBean, sendChatText, 0, null, "");
                    onFinish(true);
                }
            }

            @Override
            public void onFailedListener(ErrorReason reason) {


                if (blockSkip == BlockSkip.RUN_CHAT_BLOCKED_TO_JD) {
                    if (reason != null) {
                        ToastUtils.showText(reason.getErrReason());
                    }

                } else {
                    ToastNoPermissionAsDialogUtil.showAlertToast(mActivity, reason, true);
                }

            }
        });
    }

    private void requestVirtualPhone() {
        SimpleApiRequest.GET(ChatUrlConfig.URL_GEEK_VIDEO_JOB_VIRTUAL_PHONE)
                .setRequestCallback(new ApiRequestCallback<ResultVirtualPhoneResponse>() {
                    @Override
                    public void onSuccess(ApiData<ResultVirtualPhoneResponse> data) {
                        ResultVirtualPhoneResponse resp = data.resp;
                        String virtualPhone = resp.virtualPhone;
                        if (LText.empty(virtualPhone)) return;
                        String desc = String.format(Locale.getDefault(), "确认本机呼出号码为注册手机号%s\r\n您拨打的是老板的虚拟号，不可以直接加微信", ViewCommon.keepPhoneSecret(UserManager.getPhone()));
                        String buttonText = "确认拨打";
                        CallVirtualPhoneDialog callVirtualPhoneDialog = new CallVirtualPhoneDialog(mActivity, new Runnable() {
                            @Override
                            public void run() {
                                StringUtil.dial(mActivity, virtualPhone);
                                onFinish(true);
                            }
                        }, desc, buttonText);
                        if (ActivityUtils.isValid(mActivity)) {
                            callVirtualPhoneDialog.show();
                        }
                    }

                    @Override
                    public void onComplete() {

                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                })
                .addParam("securityId", mStartChatParam.securityId)
                .addParam("lid", mStartChatParam.lid)
                .addParam("videoId", mStartChatParam.videoId)
                .addParam("sessionId", mStartChatParam.sessionId)
                .execute();


    }


    /**
     * 跳转到开聊页或者不跳转开聊页直接开聊
     * 一键报名 只支持再当前页面添加好友
     */
    private void startChatJumpPageOrStay() {
        if (isJumpToChatPage && !isOneKeySign) {
            jumpStartChatPage();
        } else if (isCallTel) {
            requestVirtualPhone();
        } else {
            requestAddFriend();
        }
    }

    private void jumpStartChatPage() {
        if (null == singleChatParam) {
            ToastUtils.showText("跳转参数为空");
            return;
        }
        SingleRouter.startChat(mActivity, singleChatParam);
        ReportUtil.reportNoJobId(mStartChatParam.jobId, mActivity.getClass().getSimpleName(), "startChat");
        onFinish(true);
    }


    private boolean isNewUser() {
        int status = SpManager.get().user().getInt(JDChatTipsDialog.TAG_DIALOG_FIRSTSHOW, -1);
        if (status == -1) {
            status = AccountHelper.isRegister() ? 1 : 0;
            SpManager.get().user().edit().putInt(JDChatTipsDialog.TAG_DIALOG_FIRSTSHOW, status).apply();
        }
        return status == 1;
    }

    private boolean isFirstChat() {
        return !mStartChatParam.isFriend && (!isOneKeySign && !isCallTel);
    }

    private void showLoading(String loadingText) {
        if (null != onStartChatListener) {
            onStartChatListener.showLoading(loadingText);
        }
    }

    private void dismissLoading() {
        if (null != onStartChatListener) {
            onStartChatListener.dismissLoading();
        }
    }

    private void onFinish(boolean isFinish) {
        if (null != onStartChatListener) {
            if (isOneKeySign) {
                onStartChatListener.onOneKeySingFinish(isFinish);
            } else if (isCallTel) {
                onStartChatListener.onCallTelFinish(isFinish);
            } else {
                onStartChatListener.onChatFinish(isFinish);
            }
        }
    }

    public interface OnStartChatListener {
        default void showLoading(String loadingText) {

        }

        default void dismissLoading() {

        }

        //isStartChat
        // true 已经开聊
        //false 没有开聊
        void onChatFinish(boolean isStartChat);

        //一键报名结束
        default void onOneKeySingFinish(boolean isFinish) {

        }

        default void onCallTelFinish(boolean isFinish) {

        }

        default void onNetBlocked() {
        }
    }

    public enum BlockSkip {
        RUN_ALL,//默认走所有阻断，拦截器，新用户，开聊框架
        RUN_CHAT_FRAME_ONLY,//只走开聊框架，不走新用户和拦截器阻断
        RUN_CHAT_FRAME_AND_NEW_USER,//走开聊框架和新用户阻断，不走拦截器阻断

        RUN_CHAT_BLOCKED_TO_JD//走安心保开聊阻断，阻断后跳转详情页，不处罚弹框
    }

    public static class StartChatParam {
        public static final int SOURCE_STUDENT_PREFECTURE = 3;
        //必须参数
        public String securityId;
        public long bossId;
        public boolean isFriend;
        //有可能必须
        //如果securityId中没有expectId和jobId，expectId和jobId也是必须参数，有的话就不用传
        public long expectId;
        public long jobId;
        //非必须参数
        public String lid;
        public String videoId;
        public String sessionId;
        public boolean isTopJob;
        public int bossSource;
        public int entrance;

        public int source;

        public StartChatParam setSource(int source) {
            this.source = source;
            return this;
        }

        public StartChatParam setEntrance(int entrance) {
            this.entrance = entrance;
            return this;
        }

        public static StartChatParam builder() {
            return new StartChatParam();
        }

        public StartChatParam setBossId(long bossId) {
            this.bossId = bossId;
            return this;
        }

        public StartChatParam setExpectId(long expectId) {
            this.expectId = expectId;
            return this;
        }

        public StartChatParam setJobId(long jobId) {
            this.jobId = jobId;
            return this;
        }

        public StartChatParam setLid(String lid) {
            this.lid = lid;
            return this;
        }

        public StartChatParam setSecurityId(String securityId) {
            this.securityId = securityId;
            return this;
        }

        public StartChatParam setTopJob(boolean topJob) {
            isTopJob = topJob;
            return this;
        }

        public String getVideoId() {
            return videoId;
        }

        public StartChatParam setVideoId(String videoId) {
            this.videoId = videoId;
            return this;
        }

        public StartChatParam setBossSource(int bossSource) {
            this.bossSource = bossSource;
            return this;
        }

        public StartChatParam setFriend(boolean friend) {
            isFriend = friend;
            return this;
        }

        public String getSessionId() {
            return sessionId;
        }

        public StartChatParam setSessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }
    }
}
