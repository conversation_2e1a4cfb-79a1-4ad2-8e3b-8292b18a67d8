package com.hpbr.bosszhipin.module.commend.manager;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.dialog.JDAnxinCallDialogNew;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.commend.entity.GeekToChatParam;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module.commend.interfaces.IGeekToChatListener;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.recycleview.BaseMultipleItemRvAdapter;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GetAnxinCallStatusRequest;
import net.bosszhipin.api.GetAnxinbaoCallStatusResponse;
import net.bosszhipin.api.GetChatRemindRequest;
import net.bosszhipin.api.GetChatRemindResponse;
import net.bosszhipin.api.PostAnxinbaoCallResponse;
import net.bosszhipin.api.bean.PostAnxinbaoCallRequest;
import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.Collections;
import java.util.List;

import androidx.annotation.NonNull;

/**
 * @ClassName: JobListToChatUtils
 * @Description: geek 端列表开聊封装
 * @Author: yanglinjie
 * @Date: 2024/5/15 10:28
 */
public class GeekToChatUtils {

    public static final String TAG = "JobListToChatUtils";

    public static final int QUERY_CALL_STATUS_NEW_ONE_BTN_CHAT = 1;


    public static void handleTopicJobChat(ServerJobCardBean bean, Activity activity, BaseMultipleItemRvAdapter adapter) {
        if (bean == null || activity == null || adapter == null) {
            TLog.error(TAG, "bean or activity or adapter  is  null");
            return;
        }
        AnalyticsFactory.create().action("geek-pt-chat-click").build();

        GeekToChatUtils.clickToChat(GeekToChatParam.obj(bean).setFriend(bean.contact)
                .setEntrance(9).setJumpToChatPage(bean.contact), activity, new IGeekToChatListener() {
            @Override
            public void onChatResult(boolean isChatSucceed) {
                if (!bean.contact) { //非好友 才走逻辑
                    if (isChatSucceed) {
                        ToastUtils.showText("已向该Boss发送打招呼语");
                    }
                    if (isChatSucceed) {
                        if (!LList.isEmpty(adapter.getData())) {
                            int index = adapter.getData().indexOf(bean);
                            if (index > -1) {
                                bean.contact = true;
                                bean.communicateText = "继续沟通";
                                adapter.notifyItemChanged(index);
                                // 处理当前列表其他职位是否是改BOSS的
                                handleOtherJobWithSameContact();
                            }
                        }
                    }
                }
            }

            @Override
            public void onBlocked() {

            }

            @SuppressLint("NotifyDataSetChanged")
            private void handleOtherJobWithSameContact() {
                boolean hasMulti = false;
                List<Object> data = Collections.singletonList(adapter.getData());
                for (Object datum : data) {
                    if (datum instanceof ServerJobCardBean) {
                        ServerJobCardBean serverJobCardBean = (ServerJobCardBean) datum;
                        if (serverJobCardBean.bossId == bean.bossId) {
                            serverJobCardBean.contact = bean.contact;
                            serverJobCardBean.communicateText = bean.communicateText;
                            hasMulti = true;
                        }
                    }
                }
                if (hasMulti) adapter.notifyDataSetChanged();
            }
        });
    }

    /**
     * 来自详情页 安心保 海螺高选 一键报名 & 打电话 ，暂时先做复制操作，后续再梳理逻辑，重新封装
     *
     * @param type 0 打电话 1一键报名
     * @param bean 职位实体
     */

    public static void queryCallStatusNew(int type,int entrance, ServerJobCardBean bean, Activity activity, IGeekToChatListener geekToChatListener) {

        if (bean == null || activity == null) {
            TLog.error(TAG, "serverJobCardBean or activity is null ");
            return;
        }
        String sid = bean.securityId;
        GetAnxinCallStatusRequest request = new GetAnxinCallStatusRequest(new ApiRequestCallback<GetAnxinbaoCallStatusResponse>() {
            @Override
            public void onSuccess(ApiData<GetAnxinbaoCallStatusResponse> data) {
                if (ActivityUtils.isInvalid(activity)) {
                    return;
                }
                if (data != null && data.resp != null) {
                    GetAnxinbaoCallStatusResponse response = data.resp;
                    if (response.status != 0) {
                        int callStatus = response.status;

                        if (response.showNoMore == 1) {// 跳过弹框
                            if (type == 0) {
                                requestCall(sid, 0, entrance,0, activity, bean, geekToChatListener); // 直接拨打
                            } else if (type == 1) {
                                invokeAnxinApplyFramwork(() -> {
                                    requestCall(sid, 1, entrance,0, activity, bean, geekToChatListener); // 执行报名
                                }, bean, activity);
                            }
                        } else {
                            invokeAnxinApplyFramwork(() -> {
                                JDAnxinCallDialogNew dialog = new JDAnxinCallDialogNew(activity, data.resp, callStatus, sid, bean.lid, null, type, bean.jobId);
                                dialog.setApplyListner(settingValue -> requestCall(sid, 1, entrance,settingValue, activity, bean, geekToChatListener));
                                dialog.show();
                            }, bean, activity);
                        }
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });

        request.securityId = sid;
        request.type = type;
        HttpExecutor.execute(request);
    }

    private static void invokeAnxinApplyFramwork(@NonNull Runnable pendingAction, ServerJobCardBean bean, Activity activity) {

        if (bean.contact) {
            // 已经是好友
            pendingAction.run();
        } else {
            // 调起开聊框架
            requestAnxinApplyRemind(pendingAction, bean, activity);
        }
    }

    private static void requestAnxinApplyRemind(Runnable pendingAction, ServerJobCardBean bean, Activity activity) {

        final long userId = bean.bossId;
        final long expectId = bean.expectId;
        final long jobId = bean.jobId;
        String securityId = bean.securityId;
        String lid = TextUtils.isEmpty(bean.lid) ? "" : bean.lid;
        GetChatRemindRequest chatRemindRequest = new GetChatRemindRequest(new ApiRequestCallback<GetChatRemindResponse>() {
            @Override
            public void onSuccess(ApiData<GetChatRemindResponse> data) {
                if (ActivityUtils.isInvalid(activity)) {
                    return;
                }
                if (data != null && data.resp != null && data.resp.dialog != null) {
                    // 阻断直接进入职位详情页面
                    jumpToJDWithJobCardBean(bean, activity);
                } else {
                    if (pendingAction != null) {
                        pendingAction.run();
                    }
                }
            }


            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });

        chatRemindRequest.bossId = userId;
        chatRemindRequest.expectId = expectId;
        chatRemindRequest.jobId = jobId;
        chatRemindRequest.lid = lid;
        chatRemindRequest.securityId = TextUtils.isEmpty(securityId) ? "" : securityId;
        chatRemindRequest.applyJobDirectly = 1;
        HttpExecutor.execute(chatRemindRequest);
    }

    private static void requestCall(String sid, int type,int entrance, int settingValue, Activity activity, ServerJobCardBean bean, IGeekToChatListener geekToChatListener) {
        PostAnxinbaoCallRequest request = new PostAnxinbaoCallRequest(new ApiRequestCallback<PostAnxinbaoCallResponse>() {
            @Override
            public void onSuccess(ApiData<PostAnxinbaoCallResponse> data) {
                if (ActivityUtils.isInvalid(activity)) {
                    return;
                }
                if (data != null && data.resp != null) {
                    if (type == 0) { // 打电话
                        if (!TextUtils.isEmpty(data.resp.telephone)) {
                            new ZPManager(activity, data.resp.telephone).handler();
                        }
                    } else if (type == 1) { // 一键报名成功,跳转开聊
                        toAnxinApplyChatPage(activity, bean,entrance);
                        if (geekToChatListener != null) {
                            geekToChatListener.onChatResult(true);
                        }
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (null != reason && !TextUtils.isEmpty(reason.getErrReason())) {
                    ToastUtils.showText(reason.getErrReason());
                }
            }
        });

        request.securityId = sid;
        request.type = type;
        request.settingValue = settingValue;
        HttpExecutor.execute(request);
    }


    private static void toAnxinApplyChatPage(Activity activity, ServerJobCardBean bean,int entrance) {
        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setClearTop(true);
        singleChatParam.setFriendId(bean.bossId);
        singleChatParam.setJobId(bean.jobId);
        singleChatParam.setExpectId(bean.expectId);
        singleChatParam.setLid(bean.lid);
        singleChatParam.setFromDZUser(bean.bossSource == 1);
        singleChatParam.setSecurityId(bean.securityId);
        singleChatParam.setNotAnim(false);
        singleChatParam.setFirstChat(!bean.contact);
        singleChatParam.setChangePositionDesc(bean.jobName);
        singleChatParam.setEntrance(entrance);
        singleChatParam.setApplyJobDirectly(1);
        SingleRouter.startChat(activity, singleChatParam);
    }


    /**
     * 默认阻断进详情页面，不需要关心回调的
     */
    public static void clickToChat(GeekToChatParam param, Activity activity) {
        clickToChat(param, activity, null);
    }

    /**
     * 全部参数
     */
    public static void clickToChat(GeekToChatParam param, Activity activity, IGeekToChatListener listener) {
        if (param == null || activity == null) {
            ToastUtils.showText("参数不符合规范");
            TLog.error(TAG, "参数错误jobCardBean=%s; activity=%s ", param, activity);
            return;
        }

        GeekStartChatBlockDialogManager.init().builder(activity)
                .addIsJumpToChatPage(param.isJumpToChatPage)//false 不跳转到聊天页直接开聊
                .setOneKeySign(param.isOneKeySign)
                .setBlockSkip(param.blockSkip)
                .addStartChatParam(GeekStartChatBlockDialogManager.StartChatParam
                        .builder()
                        .setBossId(param.bossId)
                        .setFriend(param.isFriend)
                        .setSource(param.source)
                        .setSecurityId(param.securityId)
                        .setEntrance(param.entrance)
                        .setLid(param.lid)
                        .setJobId(param.jobId))
                .addToChatPageParam(getChatParam(param))
                .setStartChatListener(new GeekStartChatBlockDialogManager.OnStartChatListener() {
                    @Override
                    public void showLoading(String loadingText) {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).showProgressDialog(loadingText);
                        }
                    }

                    @Override
                    public void dismissLoading() {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).dismissProgressDialog();
                        }
                    }

                    @Override
                    public void onNetBlocked() {
                        jumpToJD(param, activity);
                        if (listener != null) {
                            listener.onBlocked();
                        }
                    }

                    @Override
                    public void onChatFinish(boolean isStartChat) {
                        if (listener != null) {
                            listener.onChatResult(isStartChat);
                        }
                    }

                    @Override
                    public void onOneKeySingFinish(boolean isFinish) {
                    }

                    @Override
                    public void onCallTelFinish(boolean isFinish) {
                    }
                })
                .start();
    }


    /**
     * 不处理阻断 调用方处理阻断
     *
     * @param param
     * @param activity
     * @param listener
     */
    public static void clickToChatNOHandleBlock(GeekToChatParam param, Activity activity, IGeekToChatListener listener) {
        if (param == null || activity == null) {
            ToastUtils.showText("参数不符合规范");
            TLog.error(TAG, "参数错误jobCardBean=%s; activity=%s ", param, activity);
            return;
        }

        GeekStartChatBlockDialogManager.init().builder(activity)
                .addIsJumpToChatPage(param.isJumpToChatPage)//false 不跳转到聊天页直接开聊
                .setOneKeySign(param.isOneKeySign)
                .setBlockSkip(param.blockSkip)
                .addStartChatParam(GeekStartChatBlockDialogManager.StartChatParam
                        .builder()
                        .setSource(param.source)
                        .setBossId(param.bossId)
                        .setFriend(param.isFriend)
                        .setSecurityId(param.securityId)
                        .setEntrance(param.entrance)
                        .setJobId(param.jobId))
                .addToChatPageParam(getChatParam(param))
                .setStartChatListener(new GeekStartChatBlockDialogManager.OnStartChatListener() {
                    @Override
                    public void showLoading(String loadingText) {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).showProgressDialog(loadingText);
                        }
                    }

                    @Override
                    public void dismissLoading() {
                        if (activity instanceof BaseActivity) {
                            ((BaseActivity) activity).dismissProgressDialog();
                        }
                    }

                    @Override
                    public void onNetBlocked() {
                        if (listener != null) {
                            listener.onBlocked();
                        }
                    }

                    @Override
                    public void onChatFinish(boolean isStartChat) {
                        if (listener != null) {
                            listener.onChatResult(isStartChat);
                        }
                    }

                    @Override
                    public void onOneKeySingFinish(boolean isFinish) {
                    }

                    @Override
                    public void onCallTelFinish(boolean isFinish) {
                    }
                })
                .start();
    }


    private static SingleRouter.SingleChatParam getChatParam(GeekToChatParam param) {
        SingleRouter.SingleChatParam singleChatParam = new SingleRouter.SingleChatParam();
        singleChatParam.setFriendId(param.bossId);
        singleChatParam.setJobId(param.jobId);
        singleChatParam.setSecurityId(param.securityId);
        singleChatParam.setChangePositionDesc(param.jobName);
        singleChatParam.setFromDZUser(param.friendSource == ContactBean.FROM_DIAN_ZHANG);
        singleChatParam.setLid(param.lid);
        singleChatParam.setEntrance(param.entrance);
        return singleChatParam;
    }

    private static void jumpToJD(GeekToChatParam bean, Activity activity) {
        ParamBean paramBean = getParamBean(bean, activity);
        GeekPageRouter.jumpToBossJobActivity(activity, paramBean, false);
    }

    private static void jumpToJDWithJobCardBean(ServerJobCardBean bean, Activity activity) {
        ParamBean paramBean = getParamBeanFromServerJobCardBean(bean, activity);
        GeekPageRouter.jumpToBossJobActivity(activity, paramBean, false);
    }

    private static ParamBean getParamBeanFromServerJobCardBean(ServerJobCardBean bean, Activity activity) {
        ParamBean paramBean = new ParamBean();
        paramBean.userId = bean.bossId;
        paramBean.jobId = bean.jobId;
        paramBean.securityId = bean.securityId;
        paramBean.expectId = bean.expectId;
        paramBean.lid = bean.lid;
        paramBean.jobName = bean.jobName;
        paramBean.degreeName = bean.jobDegree;
        paramBean.city = bean.cityName;
        if (TextUtils.isEmpty(bean.securityId)) {
            TLog.error("MissingSecurityId", "SecurityId missing from %s", activity.getClass().getSimpleName());
        }
        paramBean.localPageTag = activity.getClass().getSimpleName();
        return paramBean;
    }


    private static ParamBean getParamBean(GeekToChatParam bean, Activity activity) {
        ParamBean paramBean = new ParamBean();
        paramBean.userId = bean.bossId;
        paramBean.jobId = bean.jobId;
        paramBean.securityId = bean.securityId;
        paramBean.expectId = bean.expectId;
        paramBean.lid = bean.lid;
        paramBean.jobName = bean.jobName;
        paramBean.degreeName = bean.jobDegree;
        paramBean.city = bean.cityName;
        if (TextUtils.isEmpty(bean.securityId)) {
            TLog.error("MissingSecurityId", "SecurityId missing from %s", activity.getClass().getSimpleName());
        }
        paramBean.localPageTag = activity.getClass().getSimpleName();
        return paramBean;
    }
}
