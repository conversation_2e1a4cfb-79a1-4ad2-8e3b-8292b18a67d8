package com.hpbr.bosszhipin.module.commend.presenter;

import com.hpbr.bosszhipin.module.commend.entity.FilterBean;

import java.util.ArrayList;

/**
 * Author: <PERSON>You
 * Date: 2017/9/14.
 */
public interface ISearchAdvancedResultListener {

    void initSearchText(String query);

    void initCity(String city);

    void initSearchResultArrange(int selectArrange);

    void initPosition(String jobName);

    // 1313.604【B】高搜底纹词策略优化：新增底纹词初始化方法
    void initHintShow();

    void initCondition(ArrayList<FilterBean> filterBeans);

    void listRefreshing();
}
