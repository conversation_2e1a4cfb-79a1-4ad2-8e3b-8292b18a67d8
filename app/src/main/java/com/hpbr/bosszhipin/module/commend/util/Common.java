package com.hpbr.bosszhipin.module.commend.util;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.CommonConstants;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.search.FilterConstants;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhangxiangdong on 2019/6/3 14:53.
 */
public class Common {

    public static String calcAgeRangeText(int lowAge, int highAge) {
        final String ageRange;
        if (lowAge <= highAge && lowAge >= FilterConstants.AGE_DEFAULT_LOW && highAge >= FilterConstants.AGE_DEFAULT_LOW) {
            if (lowAge <= FilterConstants.AGE_DEFAULT_LOW && highAge >= FilterConstants.AGE_DEFAULT_HIGH) {// 全区间[]
                ageRange = "不限";
            } else if (lowAge >= FilterConstants.AGE_DEFAULT_HIGH && highAge >= FilterConstants.AGE_DEFAULT_HIGH) {// ]]
                ageRange = FilterConstants.AGE_DEFAULT_HIGH_SHOW_TXT + "岁+";
            } else if (lowAge > FilterConstants.AGE_DEFAULT_LOW && highAge >= FilterConstants.AGE_DEFAULT_HIGH) {//左开右闭(]
                ageRange = lowAge + "岁及以上";
            } else {
                if (lowAge == highAge) {
                    ageRange = lowAge + "岁";
                } else {
                    ageRange = lowAge + "岁-" + highAge + "岁";
                }
            }
        } else {
            ageRange = "不限";
        }
        return ageRange;
    }

    /**
     * 获取学历
     *
     * @param lowDegree
     * @param highDegree
     * @return
     */
    public static String getDegree(LevelBean lowDegree, LevelBean highDegree) {
        //region 学历
        String degree = "";
        if (lowDegree != null && highDegree != null) {
            long lowDegreeCode = lowDegree.code;
            long highDegreeCode = highDegree.code;
            if ((lowDegreeCode == 201 && highDegreeCode == 201) || (lowDegreeCode == 209 && highDegreeCode == 205)) {
                degree = "";
            } else {
                List<LevelBean> degreeList = VersionAndDatasCommon.getInstance().getDegreeList();
                if (!LList.isEmpty(degreeList)) {
                    String lowDegreeName = "";
                    String highDegreeName = "";
                    for (LevelBean item : degreeList) {
                        if (item == null) continue;
                        if (item.code == lowDegreeCode) {
                            lowDegreeName = item.name;
                        }
                        if (item.code == highDegreeCode) {
                            highDegreeName = item.name;
                        }
                    }

                    if (lowDegreeCode == highDegreeCode) {
                        degree = lowDegreeName;
                    } else {
                        degree = StringUtil.connectTextWithChar("-", lowDegreeName, highDegreeName);
                    }
                }
            }
        } else {
            degree = "";
        }
        return degree;
    }


    /**
     * 获取工作年限
     *
     * @param lowerYear
     * @param higherYear
     * @return
     */
    public static String getWorkYear(int lowerYear, int higherYear) {
        String workYear;
        if (lowerYear == -3 && higherYear == -3) { // 应届生
            workYear = "在校/应届生";
        } else if (lowerYear == -3 && higherYear == 11) { // ""
            workYear = "";
        } else if (lowerYear == -3 && (higherYear > 0 && higherYear < 11)) { // 应届生-3年，其中，“应届生”固定
            workYear = "在校/应届生-" + higherYear + "年";
        } else if (lowerYear == higherYear && higherYear == 11) {
            workYear = "10年+";
        } else if (lowerYear > 0 && lowerYear < higherYear && higherYear == 11) {
            workYear = lowerYear + "年-10年+";
        } else if ((lowerYear > 0 && higherYear < 11 && lowerYear == higherYear)) { // 3年
            workYear = lowerYear + "年";
        } else if ((lowerYear > 0 && higherYear < 11 && lowerYear != higherYear)) { // 3年-7年
            workYear = lowerYear + "年-" + higherYear + "年";
        } else {
            workYear = "";
        }
        return workYear;
    }


    public static boolean isSchoolDisabled(long startDegreeCode, long endDegreeCode) {
        return isLowerThanBachelorDegree(startDegreeCode) && isLowerThanBachelorDegree(endDegreeCode);
    }

    /**
     * 低于学士学位[本科]
     */
    public static boolean isLowerThanBachelorDegree(long degreeCode) {
        return degreeCode != 201 /* 不限 */ && degreeCode != 203 /* 本科 */
                && degreeCode != 204 /* 硕士 */ && degreeCode != 205 /* 博士 */;
    }

//    public static boolean isSalaryDisabled(int startWorkYear, int endWorkYear) {
//        return startWorkYear == WorkYearWheelView.INTERNAL_STUDENT
//                && endWorkYear == WorkYearWheelView.INTERNAL_STUDENT;
//    }

//    public static boolean isPositionStatusDisabled(int startWorkYear, int endWorkYear) {
//        return (startWorkYear == WorkYearWheelView.INTERNAL_STUDENT && endWorkYear == WorkYearWheelView.INTERNAL_STUDENT)
//                || (startWorkYear == WorkYearWheelView.INTERNAL_STUDENT && endWorkYear == WorkYearWheelView.FRESH_GRADUATE)
//                || (startWorkYear == WorkYearWheelView.FRESH_GRADUATE && endWorkYear == WorkYearWheelView.FRESH_GRADUATE);
//    }

    public static boolean isPositionStatusDisabled(int startWorkYear, int endWorkYear) {
        return (startWorkYear == CommonConstants.INTERNAL_GRADUATE_STUDENT && endWorkYear == CommonConstants.INTERNAL_GRADUATE_STUDENT);
    }

    /**
     * Title左侧提示文案
     *
     * @param emptyDesc     不限、（...）、（已选N项)
     * @param atLessCount   少于N项时，显示选中的名称
     * @param selectedItems 已选筛选项
     */
    public static <T> String getTitleTipContent(String emptyDesc, final int atLessCount, List<T> selectedItems) {

        String result = "";

        if (LList.isEmpty(selectedItems)) {
            result = "（" + emptyDesc + "）";
        } else if (selectedItems.size() < atLessCount || atLessCount == Integer.MAX_VALUE) {// 少于等于两项，显示选中的名称
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                T selected = selectedItems.get(i);
                boolean add = false;
                if (selected instanceof String) {
                    builder.append(selected);
                    add = true;
                } else if (selected instanceof FilterBean) {
                    builder.append(((FilterBean) selected).name);
                    add = true;
                }
                if (add && i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            result = "（" + builder.toString() + "）";
        } else {
            result = "（已选" + selectedItems.size() + "项）";
        }

        return result;
    }

    /**
     * 用特色字符链接字符串 比如(A.B.C.E.F.G)
     * 滤掉不限 或全部
     *
     * @param linkedChar
     * @param texts
     * @return
     */
    @NonNull
    public static String filterAndConnectTextWithChar(String linkedChar, String... texts) {
        StringBuilder sb = new StringBuilder();
        if (texts != null) {
            List<String> items = new ArrayList<>();
            //去掉空数据
            for (String text : texts) {
                if (LText.empty(text)
                        || LText.equal(text, FilterConstants.NO_LIMIT_TXT_NONE)
                        || LText.equal(text, FilterConstants.NO_LIMIT_TXT_ALL)) {
                    continue;
                }
                items.add(text);
            }
            final int size = items.size();
            if (size == 1) return items.get(0);
            //遍历数据源,字符之间用传入进来的linkedChar链接
            for (int i = 0; i < size; i++) {
                String value = items.get(i);
                if (i > 0) {
                    sb.append(linkedChar);
                }
                sb.append(value);
            }
        }
        return sb.toString();
    }
}
