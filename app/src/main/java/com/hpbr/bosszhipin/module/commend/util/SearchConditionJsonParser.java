package com.hpbr.bosszhipin.module.commend.util;

import android.text.TextUtils;

import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.module.commend.SearchHistoryHelper2;
import com.hpbr.bosszhipin.module.commend.activity.advanced.common.BossFilterConditionHelper;
import com.hpbr.bosszhipin.module.commend.entity.AdvancedSearchBean;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.commend.entity.manager.FilterBeanManager;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.search.FilterConstants;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/10/20
 */
public class SearchConditionJsonParser {

    public static SearchHistoryHelper2.Query getQuery(String conditions) {
        try {
            List<FilterBean> schoolLevelSource = BossFilterConditionHelper.getInstance().getSchoolLevelWithExt();
            List<FilterBean> genderSource = BossFilterConditionHelper.getInstance().getGender();
            List<FilterBean> geekRequirementSource = BossFilterConditionHelper.getInstance().getGeekJobRequirements();
            List<FilterBean> switchFreqSource = BossFilterConditionHelper.getInstance().getSwitchFreq();
            List<FilterBean> exchangeResumeSource = BossFilterConditionHelper.getInstance().getExchangeResume();
            List<FilterBean> viewResumeSource = BossFilterConditionHelper.getInstance().getViewResume();
            List<FilterBean> manageExperienceSource = BossFilterConditionHelper.getInstance().getManageExperience();
            List<FilterBean> withDesignImgsSource = BossFilterConditionHelper.getInstance().getWithDesignImgs();
            List<FilterBean> overSeaExpSource = BossFilterConditionHelper.getInstance().getOverSeaExpWithExtForCurrentJob();
            List<FilterBean> overSeaLangSource = BossFilterConditionHelper.getInstance().getOverSeaLangWithExtForCurrentJob();

            List<FilterBean> applyStatusSource = null;
            FilterBean applyStatusBean = FilterBeanManager.getInstance().getFilterBossParam4();
            if (applyStatusBean != null) {
                applyStatusSource = applyStatusBean.subFilterConfigModel;
            }

            JSONObject joConditions = new JSONObject(conditions);

            // 城市
            List<LevelBean> cityList = new ArrayList<>();
            JSONArray jaCites = joConditions.optJSONArray("cities");
            if (jaCites != null && jaCites.length() > 0) {
                int size = jaCites.length();
                for (int i = 0; i < size; i++) {
                    JSONObject joItem = jaCites.optJSONObject(i);
                    if (joItem != null) {
                        LevelBean item = new LevelBean();
                        item.code = joItem.optLong("code");
                        item.name = joItem.optString("name");
                        cityList.add(item);
                    }
                }
            }

            // 学历
            LevelBean lowDegree = null, highDegree = null;
            JSONObject joLowDegree = joConditions.optJSONObject("lowDegree");
            if (joLowDegree != null) {
                lowDegree = new LevelBean();
                lowDegree.code = joLowDegree.optLong("code");
                lowDegree.name = joLowDegree.optString("name");
            }
            JSONObject joHighDegree = joConditions.optJSONObject("highDegree");
            if (joHighDegree != null) {
                highDegree = new LevelBean();
                highDegree.code = joHighDegree.optLong("code");
                highDegree.name = joHighDegree.optString("name");
            }
            // 工作年限
            int lowWorkYear = joConditions.optInt("lowWorkYear");
            int highWorkYear = joConditions.optInt("highWorkYear");
            // 年龄
            String age = joConditions.optString("age");
            int lowAge = 0;
            int highAge = 0;
            if (!TextUtils.isEmpty(age)) {
                String[] ages = age.split(",");
                if (ages.length == 2) {
                    lowAge = LText.getInt(ages[0]);
                    highAge = LText.getInt(ages[1]);
                }
            }

            // 薪资
            int lowSalary = joConditions.optInt("lowSalary");
            int highSalary = joConditions.optInt("highSalary");
            // 院校要求
            List<FilterBean> schoolList = new ArrayList<>();
            String schoolLevel = joConditions.optString("schoolLevel");
            if (!TextUtils.isEmpty(schoolLevel) && !LList.isEmpty(schoolLevelSource)) {
                String[] schoolLevels = schoolLevel.split(",");
                if (schoolLevels.length > 0) {
                    for (String level : schoolLevels) {
                        if (TextUtils.isEmpty(level)) continue;
                        long code = LText.getLong(level);
                        for (FilterBean item : schoolLevelSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                schoolList.add(item);
                                break;
                            }
                        }
                    }
                }
            }
            //海外经历
            List<FilterBean> overSeaWorkExpList = new ArrayList<>();
            String overSeaWorkExps = joConditions.optString(BossFilterConditionHelper.KEY_OVER_SEA_WORK_EXP);
            if (!TextUtils.isEmpty(overSeaWorkExps) && !LList.isEmpty(overSeaExpSource)) {
                String[] splitArray = overSeaWorkExps.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : overSeaExpSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                overSeaWorkExpList.add(item);
                                break;
                            }
                        }
                    }
                }
            }

            //海外经历
            List<FilterBean> overSeaWorkLangList = new ArrayList<>();
            String overSeaWorkLangs = joConditions.optString(BossFilterConditionHelper.KEY_OVER_SEA_WORK_LANG);
            if (!TextUtils.isEmpty(overSeaWorkLangs) && !LList.isEmpty(overSeaLangSource)) {
                String[] splitArray = overSeaWorkLangs.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : overSeaLangSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                overSeaWorkLangList.add(item);
                                break;
                            }
                        }
                    }
                }
            }
            // 求职状态
            List<FilterBean> statesList = new ArrayList<>();
            String applyStatus = joConditions.optString("applyStatus");
            if (!TextUtils.isEmpty(applyStatus) && !LList.isEmpty(applyStatusSource)) {
                String[] states = applyStatus.split(",");
                if (states.length > 0) {
                    for (String state : states) {
                        if (TextUtils.isEmpty(state)) continue;
                        long code = LText.getLong(state);
                        for (FilterBean item : applyStatusSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                statesList.add(item);
                                break;
                            }
                        }
                    }
                }
            }
            // 跳槽频率
            List<FilterBean> freqList = new ArrayList<>();
            String switchFreq = joConditions.optString("switchFreq");
            if (!TextUtils.isEmpty(switchFreq) && !LList.isEmpty(switchFreqSource)) {
                String[] freqs = switchFreq.split(",");
                if (freqs.length > 0) {
                    for (String f : freqs) {
                        if (TextUtils.isEmpty(f)) continue;
                        long code = LText.getLong(f);
                        for (FilterBean item : switchFreqSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                freqList.add(item);
                                break;
                            }
                        }
                    }
                }
            }
            // 性别
            List<FilterBean> genderList = new ArrayList<>();
            String gender = joConditions.optString("gender");
            if (!TextUtils.isEmpty(gender) && !LList.isEmpty(genderSource)) {
                long code = LText.getLong(gender);
                for (FilterBean item : genderSource) {
                    if (item == null) continue;
                    if (item.code == code) {
                        genderList.add(item);
                        break;
                    }
                }
            }

            // 近14天没有看过
            List<FilterBean> viewResumeList = new ArrayList<>();
            String viewResume = joConditions.optString("viewResume");
            if (!TextUtils.isEmpty(viewResume) && !LList.isEmpty(viewResumeSource)) {
                String[] splitArray = viewResume.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : viewResumeSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                viewResumeList.add(item);
                                break;
                            }
                        }
                    }
                }
            }

            // 牛人职位要求
            List<FilterBean> exchangeResumeList = new ArrayList<>();
            String exchangeResume = joConditions.optString("exchangeResume");
            if (!TextUtils.isEmpty(exchangeResume) && !LList.isEmpty(exchangeResumeSource)) {
                String[] splitArray = exchangeResume.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : exchangeResumeSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                exchangeResumeList.add(item);
                                break;
                            }
                        }
                    }
                }
            }

            List<FilterBean> manageExperienceList = new ArrayList<>();
            String manageExperience = joConditions.optString("manageExperience");
            if (!TextUtils.isEmpty(manageExperience) && !LList.isEmpty(manageExperienceSource)) {
                String[] splitArray = manageExperience.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : manageExperienceSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                manageExperienceList.add(item);
                                break;
                            }
                        }
                    }
                }
            }
            //有无作品集
            List<FilterBean> withDesignImgsList = new ArrayList<>();
            String withDesignImgs = joConditions.optString("withDesignImgs");
            if (!TextUtils.isEmpty(withDesignImgs) && !LList.isEmpty(withDesignImgsSource)) {
                String[] splitArray = withDesignImgs.split(",");
                if (splitArray.length > 0) {
                    for (String splitStr : splitArray) {
                        if (TextUtils.isEmpty(splitStr)) continue;
                        long code = LText.getLong(splitStr);
                        for (FilterBean item : withDesignImgsSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                withDesignImgsList.add(item);
                                break;
                            }
                        }
                    }
                }
            }

            // 牛人职位要求
            List<FilterBean> requireList = new ArrayList<>();
            String geekJobRequirements = joConditions.optString("geekJobRequirements");
            if (!TextUtils.isEmpty(geekJobRequirements) && !LList.isEmpty(geekRequirementSource)) {
                String[] requirements = geekJobRequirements.split(",");
                if (requirements.length > 0) {

                    for (String require : requirements) {
                        if (TextUtils.isEmpty(require)) continue;
                        long code = LText.getLong(require);
                        for (FilterBean item : geekRequirementSource) {
                            if (item == null) continue;
                            if (item.code == code) {
                                requireList.add(item);
                                break;
                            }
                        }
                    }
                }
            }

            long jobId = joConditions.optLong("jobId");

            String queryString = "";
            List<AdvancedSearchBean.PQuery> queryList = getQueryList(conditions);
            if (LList.getElement(queryList, 0) != null) {
                queryString = LList.getElement(queryList, 0).query;
            }
            SearchHistoryHelper2.Query query = new SearchHistoryHelper2.Query(queryString);
            query.highAge = highAge;
            query.lowAge = lowAge;
            query.higherYear = highWorkYear;
            query.lowerYear = lowWorkYear;
            query.highSalary = highSalary;
            query.lowSalary = lowSalary;
            query.lowDegree = lowDegree;
            query.highDegree = highDegree;
            query.newGenderEx = genderList;
            query.switchFreqEx = freqList;
            query.geekJobRequirementEx = requireList;
            query.schoolRequires = schoolList;
            query.positionStatus = statesList;
            query.currJobId = jobId;
            query.exchangeResumeEx = exchangeResumeList;
            query.recentNotViewEx = viewResumeList;
            query.manageExperienceEx = manageExperienceList;
            query.withDesignImgsEx = withDesignImgsList;
            query.overSeaWorkExpEx = overSeaWorkExpList;
            query.overSeaWorkLangEx = overSeaWorkLangList;
            query.cityList = cityList;
            fixDataFromPc(query);//修正 从PC过来的数据
            return query;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static List<AdvancedSearchBean.PQuery> getQueryList(String conditions) {
        if (!TextUtils.isEmpty(conditions)) {
            try {
                JSONObject joConditions = new JSONObject(conditions);
                // 搜索词
                JSONArray jaQueryList = joConditions.optJSONArray("queryList");
                if (jaQueryList != null && jaQueryList.length() > 0) {
                    List<AdvancedSearchBean.PQuery> queryList = new ArrayList<>();
                    for (int i = 0; i < jaQueryList.length(); i++) {
                        JSONObject jo = jaQueryList.optJSONObject(i);
                        if (jo == null) continue;
                        String type = jo.optString("type");
                        String value = jo.optString("value");
                        AdvancedSearchBean.PQuery q = new AdvancedSearchBean.PQuery(type, value);
                        queryList.add(q);
                    }
                    return queryList;
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static AdvancedSearchBean getSearchAdvancedBean(String conditions) {
        SearchHistoryHelper2.Query query = getQuery(conditions);
        if (query != null) {
            AdvancedSearchBean advancedSearchBean = query.transformToSearchBean();
            advancedSearchBean.companyNames.clear();
            List<AdvancedSearchBean.PQuery> queryList = getQueryList(conditions);
            if (!LList.isEmpty(queryList)) {
                advancedSearchBean.companyNames.addAll(queryList);
            }
            return advancedSearchBean;
        }
        return null;
    }

    /**
     * 由于PC、APP两段取值范围不一致，沟通后暂定，对PC端数据进行兼容
     * 兼容规则如下：
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=95448981
     */
    private static void fixDataFromPc(SearchHistoryHelper2.Query query) {
        /***
         *
         * 兼容PC Degree
         *  如果「只有一个」Degree为201 不限时，需Degree进行校准
         *  若lowDegree为201，则校准为209，
         *  若highDegree为201，则校准为205
         */
        int lowDegree = query.lowDegree == null ? FilterConstants.DEGREE_LOW : (int) query.lowDegree.code;
        int highDegree = query.highDegree == null ? FilterConstants.DEGREE_HIGH : (int) query.highDegree.code;
        if ((lowDegree == FilterConstants.DEGREE_NONE && highDegree != FilterConstants.DEGREE_NONE)
                || (lowDegree != FilterConstants.DEGREE_NONE && highDegree == FilterConstants.DEGREE_NONE)) {
            List<LevelBean> degreeList = VersionAndDatasCommon.getInstance().getDegreeList();
            if (lowDegree == FilterConstants.DEGREE_NONE) {
                query.lowDegree = getTargetLevelBean(FilterConstants.DEGREE_LOW, degreeList);
            } else {
                query.highDegree = getTargetLevelBean(FilterConstants.DEGREE_HIGH, degreeList);
            }
        }
        /***
         * 兼容PC 年限
         *  如果「只有一个」年限为-1 不限时，需年限进行校准
         *  若lowerYear为-1，则校准为-3，
         *  若higherYear为-1，则校准为11
         */
        if ((query.lowerYear == FilterConstants.WORK_YEAR_NONE && query.higherYear != FilterConstants.WORK_YEAR_NONE)
                || (query.lowerYear != FilterConstants.WORK_YEAR_NONE && query.higherYear == FilterConstants.WORK_YEAR_NONE)) {

            if (query.lowerYear == FilterConstants.WORK_YEAR_NONE) {
                query.lowerYear = FilterConstants.WORK_YEAR_LOW;
            } else {
                query.higherYear = FilterConstants.WORK_YEAR_HIGH;
            }
        }

        /***
         * 兼容PC age
         *  如果「只有一个」age为-1 不限时，需age进行校准
         *  若lowerYear为-1，则校准为16，
         *  若higherYear为-1，则校准为10000
         */
        if ((query.lowAge == FilterConstants.AGE_SERVER_NONE && query.highAge != FilterConstants.AGE_SERVER_NONE)
                || (query.lowAge != FilterConstants.AGE_SERVER_NONE && query.highAge == FilterConstants.AGE_SERVER_NONE)) {

            if (query.lowAge == FilterConstants.AGE_SERVER_NONE) {
                query.lowAge = FilterConstants.AGE_DEFAULT_LOW;
            } else {
                query.highAge = FilterConstants.AGE_DEFAULT_HIGH_SERVER;
            }
        }

        /***
         * 兼容PC salary
         *  如果「只有一个」salary为-1 不限时，需salary进行校准
         *  若lowerYear为-1，则校准为1
         *  若higherYear为-1，则校准为200
         */
        if ((query.lowSalary == FilterConstants.SALARY_NONE && query.highSalary != FilterConstants.SALARY_NONE)
                || (query.lowSalary != FilterConstants.SALARY_NONE && query.highSalary == FilterConstants.SALARY_NONE)) {

            if (query.lowSalary == FilterConstants.SALARY_NONE) {
                query.lowSalary = FilterConstants.SALARY_LOW;
            } else {
                query.highSalary = FilterConstants.SALARY_HIGH;
            }
        }
    }

    private static LevelBean getTargetLevelBean(long targetCode, List<LevelBean> list) {
        if (LList.isEmpty(list)) return null;
        for (LevelBean bean : list) {
            if (bean.code == targetCode) {
                return bean;
            }
        }
        return null;
    }
}
