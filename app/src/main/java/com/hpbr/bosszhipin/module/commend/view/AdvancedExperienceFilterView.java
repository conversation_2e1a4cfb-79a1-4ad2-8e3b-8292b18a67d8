package com.hpbr.bosszhipin.module.commend.view;

import android.content.Context;
import android.graphics.Typeface;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.advancedsearch.export.AdsSearchFilterType;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.UserGrayFeatureManager;
import com.hpbr.bosszhipin.module.commend.activity.advanced.common.BossFilterConditionHelper;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.commend.entity.manager.FilterBeanManager;
import com.hpbr.bosszhipin.module.commend.presenter.SearchAdvancedResultPresenter;
import com.hpbr.bosszhipin.module.commend.util.Common;
import com.hpbr.bosszhipin.module.main.views.filter.BaseFilterRuleNewView;
import com.hpbr.bosszhipin.module.main.views.filter.FilterRuleAdapter;
import com.hpbr.bosszhipin.module.main.views.filter.MultiSelectFilterRuleNewAdapter;
import com.hpbr.bosszhipin.module.main.views.filter.SingleSelectFilterRuleNewAdapter;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.ExpandableKeywordsView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.RangeSeekBarView3;
import com.hpbr.bosszhipin.views.wheelview.AdvanceSearchAgeWheelView;
import com.hpbr.bosszhipin.views.wheelview.AdvanceSearchSalaryWheelView;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * 作者：ZhouYou
 * 日期：2017/2/28.
 */
public class AdvancedExperienceFilterView extends BaseFilterRuleNewView {

    private List<LevelBean> workYears;
    private List<LevelBean> workSalary;
    private List<LevelBean> workAge;
    private ArrayList<LevelBean> degreeList;

    private MTextView tvDegree;
    private RangeSeekBarView3 rangeDegree;
    private MTextView tvSchool;
    private MTextView tvDisableTips;
    private MTextView tvWorkYear;
    private MTextView tv_work_salary_disable_tips;
    private boolean isPartTimeJob;
    private MTextView tvWorkSalary;
    private RangeSeekBarView3 rangeWorkYear;
    private RangeSeekBarView3 rangeWorkSalary;
    private MTextView tvAge;
    private RangeSeekBarView3 rangeAge;

    private MTextView tv_position_status_disable_tips;
    private TextView filterName;
    private MultiSelectFilterRuleNewAdapter schoolAdapter;
    private MultiSelectFilterRuleNewAdapter statusAdapter;
    private MultiSelectFilterRuleNewAdapter jobRequirementAdapter;
    private SingleSelectFilterRuleNewAdapter switchFreqAdapter;
    private SingleSelectFilterRuleNewAdapter genderAdapter;
    private SingleSelectFilterRuleNewAdapter exchangeResumeAdapter;
    private SingleSelectFilterRuleNewAdapter recentNotViewAdapter;
    private SingleSelectFilterRuleNewAdapter manageExperienceAdapter;
    private SingleSelectFilterRuleNewAdapter withDesignImgsAdapter;
    private SingleSelectFilterRuleNewAdapter overSeaWorkExpAdapter;
    private MultiSelectFilterRuleNewAdapter overSeaWorkLangAdapter;

    public AdvancedExperienceFilterView(Context context) {
        this(context, null);
    }

    public AdvancedExperienceFilterView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AdvancedExperienceFilterView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    private MTextView filterJobRequirementName;

    /**
     * 专业
     */
    private void initRangeDegree() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.view_famous_degree_selection, mContainer, false);
        tvDegree = view.findViewById(R.id.tv_degree);
        rangeDegree = view.findViewById(R.id.range_degree);

        rangeDegree.setOnSeekbarChangeCallBack((start, end) -> {
            setConfirmText();
            setDegreeTitle(start, end);

            checkSchoolUsability(start, end);
        });

        degreeList = new ArrayList<>();
        List<LevelBean> degrees = VersionAndDatasCommon.getInstance().getDegreeList();
        if (LList.getCount(degrees) > 0) {
            for (LevelBean bean : degrees) {
                if (bean.code == 201 || "不限".equals(bean.name)) {
                    continue;
                }
                degreeList.add(bean);
            }
        }

        rangeDegree.setData(this.degreeList);

        mContainer.addView(view);
    }

    /**
     * 院校
     */
    private void initSchool() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.view_famous_school_selection, mContainer, false);
        ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        tvSchool = view.findViewById(R.id.tv_school_require);
        tvDisableTips = view.findViewById(R.id.tv_disable_tips);

        /* 院校要求 */
        FilterBean filterSchool = new FilterBean(AdsSearchFilterType.CODE_SCHOOL, mContext.getString(R.string.string_filter_condition_school), "school");
        filterSchool.subFilterConfigModel.addAll(BossFilterConditionHelper.getInstance().getSchoolLevelListWithExtForCurrentJob());

        schoolAdapter = new MultiSelectFilterRuleNewAdapter(mContext);
        schoolAdapter.setItems(filterSchool.subFilterConfigModel);
        // 1310.92378【高搜】高搜筛选项展示异常修复：设置院校筛选类型为默认展开
        keywordView.setDefExpanded(true);
        keywordView.setAdapter(schoolAdapter);

        schoolAdapter.setSelectStateListener(this);

        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(filterSchool.code, filterSchool.name, filterSchool.paramName);
        mAdapterFilterBeanMap.put(schoolAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, schoolAdapter);
    }

    private void checkSchoolUsability(@Nullable LevelBean startDegree, @Nullable LevelBean endDegree) {
        if (startDegree == null || endDegree == null) return;

        if (tvDisableTips == null || schoolAdapter == null) return;

        if (Common.isSchoolDisabled(startDegree.code, endDegree.code)) {
            tvDisableTips.setVisibility(VISIBLE);
            schoolAdapter.reset();
            schoolAdapter.setEnabled(false);
        } else {
            tvDisableTips.setVisibility(GONE);
            schoolAdapter.setEnabled(true);
        }
    }

    private void setDegreeTitle(LevelBean start, LevelBean end) {
        if (start == null || end == null || !rangeDegree.isSelected()) {
            tvDegree.setText(getTitle(mContext.getString(R.string.string_filter_condition_degree), "（不限）"));
        } else {

            String startName = start.name;
            String endName = end.name;

            // 如果传入的名称为空时，则进行匹配校验
            if ((TextUtils.isEmpty(startName) || TextUtils.isEmpty(endName)) && !LList.isEmpty(degreeList)) {
                for (LevelBean item : degreeList) {
                    if (item == null) continue;
                    if (item.code == start.code) {
                        startName = item.name;
                    }
                    if (item.code == end.code) {
                        endName = item.name;
                    }
                }
            }

            if (startName != null && startName.equals(endName)) {
                tvDegree.setText((getTitle(mContext.getString(R.string.string_filter_condition_degree), "（" + startName + "）")));
            } else {
                tvDegree.setText(getTitle(mContext.getString(R.string.string_filter_condition_degree), "（" + startName + "-" + endName + "）"));
            }
        }
    }

    private void setSchoolTitle(int selectCount) {
        if (selectCount == 0) { // 显示“不限”
            tvSchool.setText(getTitle(mContext.getString(R.string.string_filter_condition_school), "（不限）"));
        } else if (selectCount <= 2) { // 少于等于两项，显示选中的名称
            ArrayList<FilterBean> selectedItems = schoolAdapter.getSelectedItems();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                builder.append(selectedItems.get(i).name);
                if (i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            tvSchool.setText(getTitle(mContext.getString(R.string.string_filter_condition_school), "（" + builder.toString() + "）"));
        } else { // 超过两项，显示“已选X项”
            tvSchool.setText(getTitle(mContext.getString(R.string.string_filter_condition_school), "（已选" + selectCount + "项）"));
        }
    }

    private MTextView filterSwitchFreqName;

    /**
     * 重置
     */
    @Override
    protected void reset() {
        super.reset();
        rangeWorkYear.reset();
        rangeAge.reset();
        rangeDegree.reset();
        resetWorkSalary();
        setConfirmText();
        if (tvDisableTips != null) tvDisableTips.setVisibility(GONE);
        if (tv_position_status_disable_tips != null)
            tv_position_status_disable_tips.setVisibility(GONE);
        setYearTitle(rangeWorkYear.getStartSelection(), rangeWorkYear.getEndSelection());
        setSalaryTitle(rangeWorkSalary.getStartSelection(), rangeWorkSalary.getEndSelection());
        setAgeTitle(rangeAge.getStartSelection(), rangeAge.getEndSelection());
        setDegreeTitle(rangeDegree.getStartSelection(), rangeDegree.getEndSelection());
    }

    private void resetWorkSalary() {
        if (isPartTimeJob) {
            rangeWorkSalary.reset();
            rangeWorkSalary.setEnable(false);
            tv_work_salary_disable_tips.setVisibility(VISIBLE);
            tv_work_salary_disable_tips.setText("兼职职位不可用");
        } else {
            if (tv_work_salary_disable_tips != null)
                tv_work_salary_disable_tips.setVisibility(GONE);
            rangeWorkSalary.reset();
        }
    }

    /**
     * 确定
     */
    @Override
    protected void onConfirm() {
        if (mConfirmListener == null) return;
        mConfirmListener.onConfirm(calculateExperience());
    }

    private ArrayList<FilterBean> calculateExperience() {
        ArrayList<FilterBean> filterBeen = new ArrayList<>();
        // 工作年限
        if (rangeWorkYear.isSelected()) {
            FilterBean workYear = new FilterBean(AdsSearchFilterType.CODE_WORK_YEAR, mContext.getString(R.string.string_filter_condition_work_year), "workYear");
            LevelBean startYear = rangeWorkYear.getStartSelection();
            LevelBean endYear = rangeWorkYear.getEndSelection();
            if (startYear != null)
                workYear.subFilterConfigModel.add(new FilterBean(startYear.code, startYear.name));
            if (endYear != null)
                workYear.subFilterConfigModel.add(new FilterBean(endYear.code, endYear.name));
            filterBeen.add(workYear);
        }
        // 期望薪资
        if (rangeWorkSalary.isSelected()) {
            FilterBean workSalary = new FilterBean(AdsSearchFilterType.CODE_WORK_SALARY, mContext.getString(R.string.string_filter_condition_salary), "workSalary");
            LevelBean startSalary = rangeWorkSalary.getStartSelection();
            LevelBean endSalary = rangeWorkSalary.getEndSelection();
            if (startSalary != null)
                workSalary.subFilterConfigModel.add(new FilterBean(startSalary.code, startSalary.name));
            if (endSalary != null)
                workSalary.subFilterConfigModel.add(new FilterBean(endSalary.code, endSalary.name));
            filterBeen.add(workSalary);
        }
        // 年龄
        if (rangeAge.isSelected()) {
            FilterBean workAge = new FilterBean(AdsSearchFilterType.CODE_WORK_AGE, mContext.getString(R.string.string_filter_condition_age), "workAge");
            LevelBean startAge = rangeAge.getStartSelection();
            LevelBean endAge = rangeAge.getEndSelection();
            if (startAge != null)
                workAge.subFilterConfigModel.add(new FilterBean(startAge.code, startAge.name));
            if (endAge != null)
                workAge.subFilterConfigModel.add(new FilterBean(endAge.code, endAge.name));
            filterBeen.add(workAge);
        }
        // 学历
        if (rangeDegree.isSelected()) {
            FilterBean degree = new FilterBean(AdsSearchFilterType.CODE_DEGREE, mContext.getString(R.string.string_filter_condition_degree), "degree");
            LevelBean startDegree = rangeDegree.getStartSelection();
            LevelBean endDegree = rangeDegree.getEndSelection();
            if (startDegree != null)
                degree.subFilterConfigModel.add(new FilterBean(startDegree.code, startDegree.name));
            if (endDegree != null)
                degree.subFilterConfigModel.add(new FilterBean(endDegree.code, endDegree.name));
            filterBeen.add(degree);
        }

        filterBeen.addAll(calculateSelectedResult());
        return filterBeen;
    }


    /**
     * 初始化工作年限
     * 这里的数组需要跟 {@link SearchAdvancedResultPresenter workYearMap} 一一对应
     */
    private void initRangeWorkYear() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.view_advanced_filter_work_year, mContainer, false);

//        int[] workYearData = {-2/* 7.04新增在校生 */, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
        int[] workYearData = {-3/* 801新增在校生-应届生 */, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
        tvWorkYear = view.findViewById(R.id.tv_work_year);
        rangeWorkYear = view.findViewById(R.id.range_work_year);
        rangeWorkYear.setOnSeekbarChangeCallBack((start, end) -> {
            setYearTitle(start, end);
            setConfirmText();

//            checkWorkSalaryUsability(start, end);
            checkPositionStatusUsability(start, end);
        });
        workYears = new ArrayList<>();
        for (Integer i : workYearData) {
            LevelBean bean = new LevelBean();
            bean.code = i;
//            if (i == -2) {
//                bean.name = "在校生";
//            } else if (i == 0) {
//                bean.name = "应届生";
//            }
            if (i == -3 || i == -2 || i == 0) {
                bean.name = "在校/应届生";
            } else if (i == 11) {
                bean.name = "10年+";
            } else {
                bean.name = i + "年";
            }
            workYears.add(bean);
        }
        rangeWorkYear.setData(workYears);

        mContainer.addView(view);
    }

//    private void checkWorkSalaryUsability(@Nullable LevelBean startYear, @Nullable LevelBean endYear) {
//        if (startYear == null || endYear == null) return;
//
//        if (rangeWorkSalary == null) return;
//
//        // 当工作年限为“在校生-在校生”时，期望薪资默认不限，且置灰不可选
//        if (Common.isSalaryDisabled((int) startYear.code, (int) endYear.code)) {
//            tv_work_salary_disable_tips.setVisibility(VISIBLE);
//            rangeWorkSalary.reset();
//            rangeWorkSalary.setEnable(false);
//        } else {
//            tv_work_salary_disable_tips.setVisibility(GONE);
//            rangeWorkSalary.setEnable(true);
//            rangeWorkSalary.invalidate();
//        }
//    }

    private void checkWorkSalaryUsability() {
        if (rangeWorkSalary == null) return;
        // 当在线职位未兼职是
        boolean disable = isPartTimeJob;
        String tips = isPartTimeJob ? "兼职职位不可用" : "";
        if (disable) {
            rangeWorkSalary.reset();
            rangeWorkSalary.setEnable(false);
            tv_work_salary_disable_tips.setVisibility(VISIBLE);
            tv_work_salary_disable_tips.setText(tips);
        } else {
            rangeWorkSalary.setEnable(true);
            rangeWorkSalary.invalidate();
            tv_work_salary_disable_tips.setVisibility(GONE);
        }
    }

    /**
     * 初始化薪资
     */
    private void initRangeWorkSalary() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.view_advanced_filter_work_salary, mContainer, false);

        isPartTimeJob = BossFilterConditionHelper.getInstance().isPartTimeJob();
        tv_work_salary_disable_tips = view.findViewById(R.id.tv_work_salary_disable_tips);
        tvWorkSalary = view.findViewById(R.id.tv_work_salary);
        rangeWorkSalary = view.findViewById(R.id.range_work_salary);
        rangeWorkSalary.setOnSeekbarChangeCallBack((start, end) -> {
            setSalaryTitle(start, end);
            setConfirmText();
        });
        workSalary = AdvanceSearchSalaryWheelView.getAdvanceSearchSalaryDataForBoss();
        rangeWorkSalary.setData(workSalary);

        mContainer.addView(view);
    }

    private void initRangeAge() {
        View view = LayoutInflater.from(mContext).inflate(R.layout.view_advanced_filter_age, mContainer, false);

        tvAge = view.findViewById(R.id.tv_age);
        rangeAge = view.findViewById(R.id.range_age);

        rangeAge.setOnSeekbarChangeCallBack((start, end) -> {
            setAgeTitle(start, end);
            setConfirmText();
        });
        workAge = AdvanceSearchAgeWheelView.getAdvanceSearchAgeData();
        rangeAge.setData(workAge);

        mContainer.addView(view);
    }

    /**
     * 设置年限
     */
    private void setYearTitle(LevelBean start, LevelBean end) {
        if (start == null || end == null || !rangeWorkYear.isSelected()) {
            tvWorkYear.setText(getTitle(mContext.getString(R.string.string_filter_condition_work_year), "（不限）"));
        } else {
            String startName = start.name;
            String endName = end.name;
            if (startName != null && startName.equals(endName)) {
                tvWorkYear.setText(getTitle(mContext.getString(R.string.string_filter_condition_work_year), "（" + start.name + "）"));
            } else {
                tvWorkYear.setText(getTitle(mContext.getString(R.string.string_filter_condition_work_year), "（" + startName + "-" + end.name + "）"));
            }
        }
    }

    /**
     * 设置薪资
     */
    private void setSalaryTitle(LevelBean start, LevelBean end) {
        if (start == null || end == null || !rangeWorkSalary.isSelected()) {
            tvWorkSalary.setText(getTitle(mContext.getString(R.string.string_filter_condition_salary), "（不限）"));
        } else {
            long startCode = start.code;
            long endCode = end.code;
            if (startCode == endCode) {
                tvWorkSalary.setText(getTitle(mContext.getString(R.string.string_filter_condition_salary), "（" + start.name + ")"));
            } else {
                tvWorkSalary.setText(getTitle(mContext.getString(R.string.string_filter_condition_salary), "（" + start.name + "-" + end.name + "）"));
            }
        }
    }

    private void setAgeTitle(LevelBean start, LevelBean end) {
        if (start == null || end == null || !rangeAge.isSelected()) {
            tvAge.setText(getTitle(mContext.getString(R.string.string_filter_condition_age), "（不限）"));
        } else {
            String displayText = "（" + Common.calcAgeRangeText((int) start.code, (int) end.code) + "）";
            tvAge.setText(getTitle(mContext.getString(R.string.string_filter_condition_age), displayText));
        }
    }

    /**
     * 显示确认按钮文字
     */
    @Override
    public void setConfirmText() {
        int count = 0;
        if (rangeWorkYear.isSelected()) count++;
        if (rangeWorkSalary.isSelected()) count++;
        if (rangeAge.isSelected()) count++;
        if (rangeDegree.isSelected()) count++;

        if (mConfirmListener != null) {
            mConfirmListener.onSelectedConditionCount(super.getCount() + count);
        }
    }

    private void initStatus(Context context) {
        final FilterBean filterStatus = FilterBeanManager.getInstance().getFilterBossParam4();
        if (filterStatus == null) return;
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);

        tv_position_status_disable_tips = view.findViewById(R.id.tv_position_status_disable_tips);

        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        filterName = view.findViewById(R.id.tv_condition_title);
        filterName.setText(mContext.getString(R.string.string_filter_condition_status));
        statusAdapter = new MultiSelectFilterRuleNewAdapter(context);
        statusAdapter.setItems(filterStatus.subFilterConfigModel);
        keywordView.setAdapter(statusAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_STATUS, filterStatus.name, filterStatus.paramName);
        mAdapterFilterBeanMap.put(statusAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, statusAdapter);

        statusAdapter.setSelectStateListener(this);
    }

    private MTextView filterGenderName;


    private void initGender(Context context) {
        List<FilterBean> list = BossFilterConditionHelper.getInstance().getGender();
        if (LList.isEmpty(list)) return;

        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);

//        tv_position_status_disable_tips = view.findViewById(R.id.tv_position_status_disable_tips);

        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        filterGenderName = view.findViewById(R.id.tv_condition_title);
        filterGenderName.setText(mContext.getString(R.string.string_filter_condition_gender));
        genderAdapter = new SingleSelectFilterRuleNewAdapter(context);
        genderAdapter.setDefaultCode(-1);
        genderAdapter.setItems(list);
        keywordView.setAdapter(genderAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_GENDER, "性别", "gender");
        mAdapterFilterBeanMap.put(genderAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, genderAdapter);

        genderAdapter.setSelectStateListener(this);
    }


    @Override
    protected void inflateChildren(Context context) {

        initRangeDegree();
        initRangeWorkYear();
        initRangeWorkSalary();

        initOverSeaWorkExp(context);
        initOverSeaWorkLang(context);
        initStatus(context);
        initRangeAge();
        initSchool();
        initGender(context);

        initSwitchFreq(context);
        initJobRequirement(context);

        initExchangeResumeWithColleague(context);
        initRecentlyNotView(context);
        initManageExperience(context);
        initWithDesignImgs(context);

        initBottomRiskTip(context);


    }

    @Override
    public void setSelectedItems(ArrayList<FilterBean> selectedItems) {
//        super.setSelectedItems(selectedItems);
        final List<FilterBean> selectedSchools = new ArrayList<>();
        final List<FilterBean> selectedStatus = new ArrayList<>();

        final List<FilterBean> selectedGeekJobRequirement = new ArrayList<>();
        final List<FilterBean> selectedSwitchFreq = new ArrayList<>();
        final List<FilterBean> selectedGender = new ArrayList<>();
        final List<FilterBean> selectedExChangeResume = new ArrayList<>();
        final List<FilterBean> selectedRecentNotView = new ArrayList<>();
        final List<FilterBean> selectedManageExperience = new ArrayList<>();
        final List<FilterBean> selectedWithDesignImgs = new ArrayList<>();
        final List<FilterBean> selectedOverSeaWorkExp = new ArrayList<>();
        final List<FilterBean> selectedOverSeaWorkLang = new ArrayList<>();

        //region 初始化所有选项
        LevelBean startYear = LList.getElement(workYears, 0);
        LevelBean endYear = LList.getElement(workYears, workYears.size() - 1);

        LevelBean startSalary = LList.getElement(workSalary, 0);
        LevelBean endSalary = LList.getElement(workSalary, workSalary.size() - 1);

        LevelBean startAge = LList.getElement(workAge, 0);
        LevelBean endAge = LList.getElement(workAge, workAge.size() - 1);

        LevelBean lowDegree = LList.getElement(degreeList, 0);
        LevelBean highDegree = LList.getElement(degreeList, degreeList.size() - 1);
        //endregion

        // 获取已选的
        if (LList.getCount(selectedItems) > 0) {
            for (FilterBean bean : selectedItems) {
                long code = bean.code;
                List<FilterBean> subFilterConfigModel = bean.subFilterConfigModel;
                if (LList.getCount(subFilterConfigModel) != 2) continue;

                FilterBean s = bean.subFilterConfigModel.get(0);
                FilterBean e = bean.subFilterConfigModel.get(1);
                if (s == null || e == null) continue;

                LevelBean lS = new LevelBean(s.code, s.name);
                LevelBean eS = new LevelBean(e.code, e.name);
                if (code == AdsSearchFilterType.CODE_WORK_YEAR) {
                    startYear = lS;
                    endYear = eS;
                } else if (code == AdsSearchFilterType.CODE_WORK_SALARY) {
                    startSalary = lS;
                    endSalary = eS;
                } else if (code == AdsSearchFilterType.CODE_WORK_AGE) {
                    startAge = lS;
                    endAge = eS;
                } else if (code == AdsSearchFilterType.CODE_DEGREE) { // 学历
                    if (LList.getCount(bean.subFilterConfigModel) != 2) continue;
                    lowDegree = lS;
                    highDegree = eS;
                }
            }

            for (FilterBean bean : selectedItems) {
                long code = bean.code;
                if (code == AdsSearchFilterType.CODE_SCHOOL) { // 院校要求
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedSchools.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_STATUS) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedStatus.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_SWITCH_FREQ) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedSwitchFreq.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_GENDER) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedGender.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_JOB_REQUIREMENT) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedGeekJobRequirement.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_EXCHANGE_RESUME_WITH_COLLEAGUE) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedExChangeResume.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_RECENT_NOT_VIEW) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedRecentNotView.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_MANAGE_EXPERIENCE) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedManageExperience.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_WITH_DESIGN_IMGS) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedWithDesignImgs.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_OVER_SEA_WORK_EXP) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedOverSeaWorkExp.addAll(bean.subFilterConfigModel);
                    }
                } else if (code == AdsSearchFilterType.CODE_OVER_SEA_WORK_LANG) {
                    if (LList.getCount(bean.subFilterConfigModel) > 0) {
                        selectedOverSeaWorkLang.addAll(bean.subFilterConfigModel);
                    }
                }
            }
        }

        //region 初始化各个选项的可用性
//        checkSchoolUsability(lowDegree, highDegree);
//        checkWorkSalaryUsability(startYear, endYear);
//        checkPositionStatusUsability(startYear, endYear);
        //endregion
        rangeWorkYear.setSelections(startYear, endYear);
        rangeWorkSalary.setSelections(startSalary, endSalary);
        rangeAge.setSelections(startAge, endAge);
        rangeDegree.setSelections(lowDegree, highDegree);
        schoolAdapter.setSelectedItems(selectedSchools);
        statusAdapter.setSelectedItems(selectedStatus);

        setYearTitle(startYear, endYear);
        setSalaryTitle(startSalary, endSalary);
        setAgeTitle(startAge, endAge);
        setDegreeTitle(lowDegree, highDegree);
        setSchoolTitle(LList.getCount(schoolAdapter.getSelectedItems()));
        setStatusTitle(LList.getCount(statusAdapter.getSelectedItems()));
        if (jobRequirementAdapter != null) {
            jobRequirementAdapter.setSelectedItems(selectedGeekJobRequirement);
            setGeekJobRequirementTitle(LList.getCount(jobRequirementAdapter.getSelectedItems()));
        }
        if (switchFreqAdapter != null) {
            switchFreqAdapter.setSelectedItems(selectedSwitchFreq);
            setSwitchFreqTitle(LList.getCount(switchFreqAdapter.getSelectedItems()));
        }
        if (genderAdapter != null) {
            genderAdapter.setSelectedItems(selectedGender);
            setGenderTitle(LList.getCount(genderAdapter.getSelectedItems()));
        }
        if (exchangeResumeAdapter != null) {
            exchangeResumeAdapter.setSelectedItems(selectedExChangeResume);
            setExchangeResumeTitle(exchangeResumeAdapter.getSelectedItems());
        }
        if (recentNotViewAdapter != null) {
            recentNotViewAdapter.setSelectedItems(selectedRecentNotView);
            setRecentNotViewTitle(recentNotViewAdapter.getSelectedItems());
        }
        if (manageExperienceAdapter != null) {
            manageExperienceAdapter.setSelectedItems(selectedManageExperience);
            setManageExperienceTitle(manageExperienceAdapter.getSelectedItems());
        }
        if (withDesignImgsAdapter != null) {
            withDesignImgsAdapter.setSelectedItems(selectedWithDesignImgs);
            setWithDesignImgsTitle(withDesignImgsAdapter.getSelectedItems());
        }
        if (overSeaWorkExpAdapter != null) {
            overSeaWorkExpAdapter.setSelectedItems(selectedOverSeaWorkExp);
            setOverSeaWorkExpTitle(overSeaWorkExpAdapter.getSelectedItems());
        }
        if (overSeaWorkLangAdapter != null){
            overSeaWorkLangAdapter.setSelectedItems(selectedOverSeaWorkLang);
            setOverSeaWorkLangTitle(LList.getCount(overSeaWorkLangAdapter.getSelectedItems()));
        }

        checkSchoolUsability(lowDegree, highDegree);
        checkPositionStatusUsability(startYear, endYear);
        checkWorkSalaryUsability();
        setConfirmText();
    }

    private void initJobRequirement(Context context) {
        List<FilterBean> list = BossFilterConditionHelper.getInstance().getGeekJobRequirements();
        if (LList.isEmpty(list)) {
            return;
        }

        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);

        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        filterJobRequirementName = view.findViewById(R.id.tv_condition_title);
        filterJobRequirementName.setText(mContext.getString(R.string.string_filter_condition_switch_geek_job_requirement));
        jobRequirementAdapter = new MultiSelectFilterRuleNewAdapter(context);
        jobRequirementAdapter.setItems(list);
        keywordView.setAdapter(jobRequirementAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_JOB_REQUIREMENT, "牛人职位要求", "geekJobRequirements");
        mAdapterFilterBeanMap.put(jobRequirementAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, jobRequirementAdapter);

        jobRequirementAdapter.setSelectStateListener(this);
    }

    private void initSwitchFreq(Context context) {
        List<FilterBean> list = BossFilterConditionHelper.getInstance().getSwitchFreq();
        if (LList.isEmpty(list)) {
            return;
        }

        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);


        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        filterSwitchFreqName = view.findViewById(R.id.tv_condition_title);
        filterSwitchFreqName.setText(mContext.getString(R.string.string_filter_condition_switch_freq));
        switchFreqAdapter = new SingleSelectFilterRuleNewAdapter(context);
        switchFreqAdapter.setItems(list);
        keywordView.setAdapter(switchFreqAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_SWITCH_FREQ, "跳槽频率", "switchFreq");
        mAdapterFilterBeanMap.put(switchFreqAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, switchFreqAdapter);

        switchFreqAdapter.setSelectStateListener(this);
    }

    /**
     * ************** 是否和同事交换简历 **************************
     */
    private MTextView exchangeResumeNameTv;

    private void initExchangeResumeWithColleague(Context context) {
        List<FilterBean> exchangeResume = BossFilterConditionHelper.getInstance().getExchangeResume();
        if (LList.isEmpty(exchangeResume)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);
        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        exchangeResumeNameTv = view.findViewById(R.id.tv_condition_title);
        String exchangeResumeTitle = mContext.getString(R.string.string_filter_condition_exchange_resume);
        exchangeResumeNameTv.setText(exchangeResumeTitle);
        exchangeResumeAdapter = new SingleSelectFilterRuleNewAdapter(context);
        exchangeResumeAdapter.setItems(exchangeResume);
        keywordView.setAdapter(exchangeResumeAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_EXCHANGE_RESUME_WITH_COLLEAGUE, exchangeResumeTitle, "exchangeResumeWithColleague");
        mAdapterFilterBeanMap.put(exchangeResumeAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, exchangeResumeAdapter);

        exchangeResumeAdapter.setSelectStateListener(this);
    }


    private void setExchangeResumeTitle(ArrayList<FilterBean> selectedItems) {
        if (null != exchangeResumeNameTv) {
            exchangeResumeNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            String titleTipContent = Common.getTitleTipContent("不限", 3, selectedItems);
            exchangeResumeNameTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_exchange_resume), titleTipContent));
        }
    }

    /**
     * ************** 近期没看过 **************************
     */
    private MTextView recentNotViewNameTv;

    private void initRecentlyNotView(Context context) {
        List<FilterBean> viewResume = BossFilterConditionHelper.getInstance().getViewResume();
        if (LList.isEmpty(viewResume)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);
        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        recentNotViewNameTv = view.findViewById(R.id.tv_condition_title);
        String recentNotViewTitle = mContext.getString(R.string.string_filter_condition_recent_not_view);
        recentNotViewNameTv.setText(recentNotViewTitle);
        recentNotViewAdapter = new SingleSelectFilterRuleNewAdapter(context);
        recentNotViewAdapter.setItems(viewResume);
        keywordView.setAdapter(recentNotViewAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_RECENT_NOT_VIEW, recentNotViewTitle, "recentNotView");
        mAdapterFilterBeanMap.put(recentNotViewAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, recentNotViewAdapter);

        recentNotViewAdapter.setSelectStateListener(this);
    }

    private void setRecentNotViewTitle(ArrayList<FilterBean> selectedItems) {
        if (null != recentNotViewNameTv) {
            recentNotViewNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            String titleTipContent = Common.getTitleTipContent("不限", 3, selectedItems);
            recentNotViewNameTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_recent_not_view), titleTipContent));
        }
    }

    /**
     * ************** 有无管理经验**************************
     */
    private MTextView manageExperienceNameTv;

    private void initManageExperience(Context context) {
        List<FilterBean> manageExperience = BossFilterConditionHelper.getInstance().getManageExperience();
        if (LList.isEmpty(manageExperience)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);
        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        manageExperienceNameTv = view.findViewById(R.id.tv_condition_title);
        String manageExperienceTitle = mContext.getString(R.string.string_filter_condition_manage_experience);
        manageExperienceNameTv.setText(manageExperienceTitle);
        manageExperienceAdapter = new SingleSelectFilterRuleNewAdapter(context);
        manageExperienceAdapter.setItems(manageExperience);
        keywordView.setAdapter(manageExperienceAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_MANAGE_EXPERIENCE, manageExperienceTitle, "manageExperience");
        mAdapterFilterBeanMap.put(manageExperienceAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, manageExperienceAdapter);

        manageExperienceAdapter.setSelectStateListener(this);
        manageExperienceAdapter.setItemOnClickWatcher(new FilterRuleAdapter.ItemOnClickWatcher() {
            @Override
            public void watchOnClick(FilterBean filterBean) {
                AnalyticsFactory.create()
                        .action(AnalyticsAction.ACTION_SEARCH_FILTER_CLICK)
                        .param("p", 5)//筛选项：0-职位；1-城市
                        .param("p2", filterBean.code == 0 ? 2 : 1) //jobid/城市code
                        .build();
            }
        });
    }

    private void setManageExperienceTitle(ArrayList<FilterBean> selectedItems) {
        if (null != manageExperienceNameTv) {
            manageExperienceNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            String titleTipContent = Common.getTitleTipContent("不限", 3, selectedItems);
            manageExperienceNameTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_manage_experience), titleTipContent));
        }
    }

    /**
     * ************** 有无作品集**************************
     */
    private MTextView withDesignImgsNameTv;

    private void initWithDesignImgs(Context context) {
        List<FilterBean> withDesignImgs = BossFilterConditionHelper.getInstance().getWithDesignImgs();
        boolean isEnableWithDesignImgs = BossFilterConditionHelper.getInstance().isEnableWithDesignImgsForCurrentJob();
        if (!isEnableWithDesignImgs || LList.isEmpty(withDesignImgs)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);
        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        withDesignImgsNameTv = view.findViewById(R.id.tv_condition_title);
        String withDesignImgsTitle = mContext.getString(R.string.string_filter_condition_with_design_imgs);
        withDesignImgsNameTv.setText(withDesignImgsTitle);
        withDesignImgsAdapter = new SingleSelectFilterRuleNewAdapter(context);
        withDesignImgsAdapter.setItems(withDesignImgs);
        keywordView.setAdapter(withDesignImgsAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_WITH_DESIGN_IMGS, withDesignImgsTitle, "withDesignImgs");
        mAdapterFilterBeanMap.put(withDesignImgsAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, withDesignImgsAdapter);

        withDesignImgsAdapter.setSelectStateListener(this);
        withDesignImgsAdapter.setItemOnClickWatcher(new FilterRuleAdapter.ItemOnClickWatcher() {
            @Override
            public void watchOnClick(FilterBean filterBean) {
//                AnalyticsFactory.create()
//                        .action(AnalyticsAction.ACTION_SEARCH_FILTER_CLICK)
//                        .param("p", 5)//筛选项：0-职位；1-城市
//                        .param("p2", filterBean.code == 0 ? 2 : 1) //jobid/城市code
//                        .build();
            }
        });
    }

    private void setWithDesignImgsTitle(ArrayList<FilterBean> selectedItems) {
        if (null != withDesignImgsNameTv) {
            withDesignImgsNameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            String titleTipContent = Common.getTitleTipContent("不限", 3, selectedItems);
            withDesignImgsNameTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_with_design_imgs), titleTipContent));
        }
    }

    /**
     * ************** 海外经历**************************
     */
    private MTextView overSeaWorkExpTv;

    private void initOverSeaWorkExp(Context context) {
        List<FilterBean> overSeaExpList = BossFilterConditionHelper.getInstance().getOverSeaExpWithExtForCurrentJob();
        if (LList.isEmpty(overSeaExpList)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);
        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        overSeaWorkExpTv = view.findViewById(R.id.tv_condition_title);
        String title = mContext.getString(R.string.string_filter_condition_over_sea_work_exp);
        overSeaWorkExpTv.setText(title);
        overSeaWorkExpAdapter = new SingleSelectFilterRuleNewAdapter(context);
        overSeaWorkExpAdapter.setItems(overSeaExpList);
        keywordView.setAdapter(overSeaWorkExpAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_OVER_SEA_WORK_EXP, title, BossFilterConditionHelper.KEY_OVER_SEA_WORK_EXP);
        mAdapterFilterBeanMap.put(overSeaWorkExpAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, overSeaWorkExpAdapter);

        overSeaWorkExpAdapter.setSelectStateListener(this);
    }

    private void setOverSeaWorkExpTitle(ArrayList<FilterBean> selectedItems) {
        if (null != overSeaWorkExpTv) {
            overSeaWorkExpTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            String titleTipContent = Common.getTitleTipContent("不限", 3, selectedItems);
            overSeaWorkExpTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_over_sea_work_exp), titleTipContent));
        }
    }


    private TextView overSeaWorkLangTv;

    private void initOverSeaWorkLang(Context context) {
        List<FilterBean> overSeaLangList = BossFilterConditionHelper.getInstance().getOverSeaLangWithExtForCurrentJob();
        if (LList.isEmpty(overSeaLangList)) {
            return;
        }
        final View view = LayoutInflater.from(context).inflate(
                R.layout.item_filter_keywords_new2, mContainer, false);

        final ExpandableKeywordsView keywordView = view.findViewById(R.id.kv_keywords);
        overSeaWorkLangTv = view.findViewById(R.id.tv_condition_title);
        String title = mContext.getString(R.string.string_filter_condition_over_sea_work_lang);
        overSeaWorkLangTv.setText(title);
        overSeaWorkLangAdapter = new MultiSelectFilterRuleNewAdapter(context);
        overSeaWorkLangAdapter.setItems(overSeaLangList);
        keywordView.setAdapter(overSeaWorkLangAdapter);
        mContainer.addView(view);

        FilterBean cloneFilterBean = new FilterBean(AdsSearchFilterType.CODE_OVER_SEA_WORK_LANG, title, BossFilterConditionHelper.KEY_OVER_SEA_WORK_LANG);
        mAdapterFilterBeanMap.put(overSeaWorkLangAdapter, cloneFilterBean);
        mFilterBeanAdapterMap.put(cloneFilterBean, overSeaWorkLangAdapter);

        overSeaWorkLangAdapter.setSelectStateListener(this);
    }

    private void setOverSeaWorkLangTitle(int selectCount) {
        overSeaWorkLangTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        if (selectCount == 0) { // 显示“不限”
            overSeaWorkLangTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_over_sea_work_lang), "（不限）"));
        } else if (selectCount < 2) { // 少于两项，显示选中的名称
            ArrayList<FilterBean> selectedItems = overSeaWorkLangAdapter.getSelectedItems();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                builder.append(selectedItems.get(i).name);
                if (i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            overSeaWorkLangTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_over_sea_work_lang), "（" + builder.toString() + "）"));
        } else { // 超过两项，显示“已选X项”
            overSeaWorkLangTv.setText(getTitle(mContext.getString(R.string.string_filter_condition_over_sea_work_lang), "（已选" + selectCount + "项）"));
        }
    }

    private void checkPositionStatusUsability(@Nullable LevelBean startWorkYear, @Nullable LevelBean endWorkYear) { // 求职状态
        if (startWorkYear == null || endWorkYear == null) return;

        if (tv_position_status_disable_tips == null || statusAdapter == null) return;

        // 当工作年限为“在校生-在校生”、“在校生-应届生”、“应届生-应届生”时，求职状态默认不限，且置灰不可选，提示“非在校生/应届生时可用”（7.06）
        if (Common.isPositionStatusDisabled((int) startWorkYear.code, (int) endWorkYear.code)) {
            tv_position_status_disable_tips.setVisibility(VISIBLE);
            statusAdapter.reset();
            statusAdapter.setEnabled(false);//必须在reset后面，因为reset会启用开关
        } else {
            tv_position_status_disable_tips.setVisibility(GONE);
            statusAdapter.setEnabled(true);
        }
    }

    private void initBottomRiskTip(Context context) {
        String riskMsg = UserGrayFeatureManager.getInstance().getRiskMessage();
        if (TextUtils.isEmpty(riskMsg)) {
            return;
        }
        View view = LayoutInflater.from(context).inflate(R.layout.item_ads_search_filter_risk_tip, mContainer, false);
        MTextView tvBottomTip = view.findViewById(R.id.tv_bottom_tip);
        tvBottomTip.setText(riskMsg);

        mContainer.addView(view);
    }

    @Override
    protected ArrayList<FilterBean> getFilterBeen() {
        return null;
    }

    private SpannableStringBuilder getTitle(@NonNull String prefix, @NonNull String content) {
        String totalString = prefix;
        if (!TextUtils.isEmpty(content)) {
            totalString += content;
        }
        SpannableStringBuilder builder = new SpannableStringBuilder(totalString);
        builder.setSpan(new RelativeSizeSpan(0.72f), prefix.length(), totalString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.text_c5)), prefix.length(), totalString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        builder.setSpan(new StyleSpan(Typeface.BOLD), 0, prefix.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return builder;
    }

    private void setStatusTitle(int selectCount) {
        filterName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        if (selectCount == 0) { // 显示“不限”
            filterName.setText(getTitle(mContext.getString(R.string.string_filter_condition_status), "（不限）"));
        } else if (selectCount < 2) { // 少于两项，显示选中的名称
            ArrayList<FilterBean> selectedItems = statusAdapter.getSelectedItems();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                builder.append(selectedItems.get(i).name);
                if (i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            filterName.setText(getTitle(mContext.getString(R.string.string_filter_condition_status), "（" + builder.toString() + "）"));
        } else { // 超过两项，显示“已选X项”
            filterName.setText(getTitle(mContext.getString(R.string.string_filter_condition_status), "（已选" + selectCount + "项）"));
        }
    }

    private void setGeekJobRequirementTitle(int selectCount) {
        if (filterJobRequirementName == null) {
            return;
        }
        filterJobRequirementName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        if (selectCount == 0) { // 显示“不限”
            filterJobRequirementName.setText(getTitle(mContext.getString(R.string.string_filter_condition_switch_geek_job_requirement), "（不限）"));
        } else { // 超过两项，显示“已选X项”
            filterJobRequirementName.setText(getTitle(mContext.getString(R.string.string_filter_condition_switch_geek_job_requirement), "（已选" + selectCount + "项）"));
        }
    }

    private void setSwitchFreqTitle(int selectCount) {
        if (filterSwitchFreqName == null) {
            return;
        }
        filterSwitchFreqName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        if (selectCount == 0) { // 显示“不限”
            filterSwitchFreqName.setText(getTitle(mContext.getString(R.string.string_filter_condition_switch_freq), "（不限）"));
        } else { // 少于两项，显示选中的名称
            ArrayList<FilterBean> selectedItems = switchFreqAdapter.getSelectedItems();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                builder.append(selectedItems.get(i).name);
                if (i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            filterSwitchFreqName.setText(getTitle(mContext.getString(R.string.string_filter_condition_switch_freq), "（" + builder.toString() + "）"));
        }
    }

    private void setGenderTitle(int selectCount) {
        if (filterGenderName == null) {
            return;
        }
        filterGenderName.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        if (selectCount == 0) { // 显示“不限”
            filterGenderName.setText(getTitle(mContext.getString(R.string.string_filter_condition_gender), "（不限）"));
        } else { // 少于两项，显示选中的名称
            ArrayList<FilterBean> selectedItems = genderAdapter.getSelectedItems();
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < selectedItems.size(); i++) {
                builder.append(selectedItems.get(i).name);
                if (i < selectedItems.size() - 1) {
                    builder.append("，");
                }
            }
            filterGenderName.setText(getTitle(mContext.getString(R.string.string_filter_condition_gender), "（" + builder.toString() + "）"));
        }
    }

    @Override
    public void onItemSelectedStateChange(FilterRuleAdapter adapter, int selectCount) {
        super.onItemSelectedStateChange(adapter, selectCount);
        setSchoolTitle(LList.getCount(schoolAdapter.getSelectedItems()));
        setSalaryTitle(rangeWorkSalary.getStartSelection(), rangeWorkSalary.getEndSelection());
        setStatusTitle(LList.getCount(statusAdapter.getSelectedItems()));
        if (jobRequirementAdapter != null) {
            setGeekJobRequirementTitle(LList.getCount(jobRequirementAdapter.getSelectedItems()));
        }

        if (switchFreqAdapter != null) {
            setSwitchFreqTitle(LList.getCount(switchFreqAdapter.getSelectedItems()));
        }
        if (genderAdapter != null) {
            setGenderTitle(LList.getCount(genderAdapter.getSelectedItems()));
        }

        if (exchangeResumeAdapter != null) {
            setExchangeResumeTitle(exchangeResumeAdapter.getSelectedItems());
        }
        if (recentNotViewAdapter != null) {
            setRecentNotViewTitle(recentNotViewAdapter.getSelectedItems());
        }
        if (manageExperienceAdapter != null) {
            setManageExperienceTitle(manageExperienceAdapter.getSelectedItems());
        }
        if (withDesignImgsAdapter != null) {
            setWithDesignImgsTitle(withDesignImgsAdapter.getSelectedItems());
        }
        if (overSeaWorkExpAdapter != null) {
            setOverSeaWorkExpTitle(overSeaWorkExpAdapter.getSelectedItems());
        }
        if (overSeaWorkLangAdapter != null) {
            setOverSeaWorkLangTitle(LList.getCount(overSeaWorkLangAdapter.getSelectedItems()));
        }
    }

}
