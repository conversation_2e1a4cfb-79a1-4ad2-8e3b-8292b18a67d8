package com.hpbr.bosszhipin.module.commend.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class MultiCityView extends FrameLayout {

    public static final int MAX_CITY_COUNT_TWO = 2;
    public static final int MAX_CITY_COUNT_THREE = 3;
    public static final int MAX_CITY_COUNT_FIVE = 5;
    public static final int MAX_CITY_COUNT_NINE = 9;

    @NonNull
    private final List<LevelBean> selectedCities = new ArrayList<>();
    @Nullable
    private OnCityCallback callback;
    @NonNull
    private final InnerAdapter adapter;
    @NonNull
    private final RecyclerView cityRv;

    private boolean autoSetVisibility;

    private int maxCityCount = MAX_CITY_COUNT_THREE;
    @Nullable
    private String overrunToast;

    @NonNull
    public List<LevelBean> getSelectedCities() {
        return selectedCities;
    }

    public void setCallback(@Nullable OnCityCallback callback) {
        this.callback = callback;
    }

    public MultiCityView(@NonNull Context context) {
        this(context, null);
    }

    public MultiCityView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MultiCityView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        inflate(context, R.layout.view_multi_city, this);

        cityRv = findViewById(R.id.cityRv);
        adapter = new InnerAdapter();
        cityRv.setAdapter(adapter);
    }

    public void setSelectedCities(@Nullable List<LevelBean> levelBeans) {
        if (levelBeans == null || levelBeans.size() == 0) {
            selectedCities.clear();
        } else {
            selectedCities.addAll(levelBeans);
        }

        adapter.setNewData(selectedCities);

        refreshVisibility();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void onCityChanged(@NonNull LevelBean city) {
        // 判断是添加还是移除
        boolean checked = city.isChecked();
        if (checked) {
            if (indexOf(city) > -1) {
                // 已添加
                return;
            }

            // 1208.1001数据改为插入尾部并反显
            selectedCities.add(city);
            int lastIndex = LList.isNotEmpty(selectedCities) ? selectedCities.size() - 1 : 0;
            cityRv.scrollToPosition(lastIndex);
        } else {
            //region 移除该职位
            int removeIndex = indexOf(city);

            if (removeIndex >= 0 && removeIndex < selectedCities.size()) {
                selectedCities.remove(removeIndex);
            }
            //endregion
        }

        refreshVisibility();
        adapter.notifyDataSetChanged();
    }

    private int indexOf(@NonNull LevelBean secondItem) {
        int index = -1;
        int size = selectedCities.size();
        for (int i = 0; i < size; i++) {
            LevelBean s = selectedCities.get(i);
            if (s != null && s.code == secondItem.code) {
                index = i;
                break;
            }
        }
        return index;
    }

    private void refreshVisibility() {
        if (autoSetVisibility) {
            setVisibility(LList.hasElement(selectedCities) ? VISIBLE : GONE);
        }
    }

    public boolean isFull(boolean enableToast) {
        final int count = LList.getCount(getSelectedCities());
        if (count >= maxCityCount) {
            String toast;
            if (!LText.empty(overrunToast)) {
                toast = overrunToast;
            } else {
                toast = String.format(Locale.getDefault(), "最多可选 %d 个城市", maxCityCount);
            }

            if (enableToast) {
                ToastUtils.showText(toast);
            }
            return true;
        }

        return false;
    }

    public boolean hasCity(@NonNull LevelBean targetCity) {
        return indexOf(targetCity) > -1;
    }

    /**
     * 县级市和所属地级市的互斥逻辑保留：若先选县级市、再点击其所属地级市选项，toast“已选择xx（城市名称），不支持再选择xx（城市名称）”禁止选中；反之相同
     */
    @Nullable
    public LevelBean pickAssociatedCity(@NonNull LevelBean targetCity /* 用户要选择的城市 */) {
        /* 判断selectCity是否有关联的城市。地级市 <> 县级市 */
        LevelBean associatedCity = null;
        final List<LevelBean> selectedCities = getSelectedCities();
        outer:
        for (LevelBean selectedCity : selectedCities) {
            /*
             targetCity 是地级市，selectedCity 是县级市，则：
                    i. targetCity.code == selectedCity.parentCode；
                    ii. targetCity.subLevelModeList.get(i).code == selectedCity.code

             targetCity 是县级市，selectedCity 是地级市，则：
                    i. targetCity.parentCode == selectedCity.code
                    ii. targetCity.code == selectedCity.subLevelModeList.get(i).code
             */

            // Step 1.1
            if (targetCity.code == selectedCity.parentCode) {
                associatedCity = selectedCity;
                break;
            }

            // Step 1.2
            final List<LevelBean> targetCitySubCities = targetCity.subLevelModeList;
            if (targetCitySubCities != null) {
                for (LevelBean targetCitySubCity : targetCitySubCities) {
                    if (targetCitySubCity.code == selectedCity.code) {
                        associatedCity = selectedCity;
                        break outer;
                    }
                }
            }

            // Step 2.1
            if (targetCity.parentCode == selectedCity.code) {
                associatedCity = selectedCity;
                break;
            }

            // Step 2.2
            final List<LevelBean> selectedCitySubCities = selectedCity.subLevelModeList;
            if (selectedCitySubCities != null) {
                for (LevelBean selectedCitySubCity : selectedCitySubCities) {
                    if (targetCity.code == selectedCitySubCity.code) {
                        associatedCity = selectedCity;
                        break outer;
                    }
                }
            }

            // Step 3
            // 如，已选北京朝阳区，再选北京东城区，不可选，都属于北京
            if (targetCity.parentCode > 0 &&
                    targetCity.parentCode == selectedCity.parentCode) {
                associatedCity = selectedCity;
                break;
            }
        }

        if (associatedCity != null) {
            ToastUtils.showText("已选择" + associatedCity.name + "，不支持再选择" + targetCity.name);
            return associatedCity;
        }

        return null;
    }

    public boolean isItemSelected(@NonNull LevelBean cityItem) {
        boolean isSelected = false;

        final List<LevelBean> selectedCities = getSelectedCities();
        for (LevelBean item : selectedCities) {
            if (item == null) {
                continue;
            }
            if (item.code == cityItem.code) {
                isSelected = true;
                break;
            }
        }

        return isSelected;
    }

    public void setAutoSetVisibility(boolean autoSetVisibility) {
        this.autoSetVisibility = autoSetVisibility;
    }

    public void setMaxCityCount(int maxCityCount, String overrunToast) {
        this.maxCityCount = maxCityCount <= 0 ? MAX_CITY_COUNT_THREE /* 默认最多选择 3 个城市 */ : maxCityCount;
        this.overrunToast = overrunToast;
    }

    private class InnerAdapter extends BaseQuickAdapter<LevelBean, BaseViewHolder> {

        public InnerAdapter() {
            this(null);
        }

        public InnerAdapter(@Nullable List<LevelBean> data) {
            super(R.layout.item_common_selected_with_close, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
            helper.setText(R.id.tv_title, item.name);
            helper.getView(R.id.cl_container).setOnClickListener(new OnClickNoFastListener() {
                @SuppressLint("NotifyDataSetChanged")
                @Override
                public void onNoFastClick(View v) {
                    // 移除 item
                    item.setChecked(false);
                    selectedCities.remove(item);

                    if (callback != null) {
                        callback.onCityRemoved(item);
                    }
                    refreshVisibility();

                    notifyDataSetChanged();
                }
            });
        }
    }

    public interface OnCityCallback {
        void onCityRemoved(@NonNull LevelBean cityItem);
    }

}
