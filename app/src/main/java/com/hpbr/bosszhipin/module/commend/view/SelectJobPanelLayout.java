package com.hpbr.bosszhipin.module.commend.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.resume.GeekResumeChatHelper;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetMatchItemJobRequest;
import net.bosszhipin.api.GetMatchItemJobResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2019-10-24
 * <p>
 * 简历详情页、批量开聊、道具激活页中有使用
 */
public class SelectJobPanelLayout extends FrameLayout implements View.OnClickListener {

    protected Context context;

    public SelectJobPanelLayout(@NonNull Context context) {
        this(context, null);
    }

    public SelectJobPanelLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SelectJobPanelLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        popViewHeight = (int) ((App.get().getDisplayHeight() - ZPUIDisplayHelper.dp2px(context, 50)) * 0.8f);
        init();
    }

    private ConstraintLayout clParent;
    private ConstraintLayout clJob;
    private MTextView tvTitle;
    private MTextView descTv;
    private RecyclerView rvList;
    private OnlineJobSelectAdapter adapter;

    private int popViewHeight;

    private boolean isShowing;

    public boolean isShowing() {
        return isShowing;
    }

    private static final int HANDLE_ANIM_START = 1000;

    private long selectJobId;
    // 1218.253 【商业】匿名牛人详情页增加负反馈、举报入口：记录选中职位的加密ID
    private String selectEncryptJobId;

    public void setSelectJobId(long selectJobId) {
        this.selectJobId = selectJobId;
    }

    /**
     * 1218.253 【商业】匿名牛人详情页增加负反馈、举报入口：记录获取选中职位的加密ID
     * @param selectEncryptJobId 加密的职位ID
     */
    public void setSelectEncryptJobId(@Nullable String selectEncryptJobId) {
        this.selectEncryptJobId = selectEncryptJobId;
    }

    public void clearSelectJobId() {
        selectJobId = 0;
    }

    private OnJobSelectListener onJobSelectListener;

    public void setOnJobSelectListener(OnJobSelectListener onJobSelectListener) {
        this.onJobSelectListener = onJobSelectListener;
    }

    private OnJobDelaySelectListener onJobDelaySelectListener;

    public void setOnJobDelaySelectListener(OnJobDelaySelectListener onJobDelaySelectListener) {
        this.onJobDelaySelectListener = onJobDelaySelectListener;
    }

    /*延迟刷新item(如：点击item后，调接口，等待接口成功后再次更新Item)*/
    public void delayNotifyItem(long selectJobId) {
        if (null != adapter && selectJobId > 0) {
            setSelectJobId(selectJobId);
            adapter.delayNotifyItem(selectJobId);
        }
    }

    /*延迟刷新item(如：点击item后，调接口，等待接口成功后再次更新Item)*/
    public void delayNotifyItem(String selectEncryptJobId) {
        if (null != adapter && !LText.isEmptyOrNull(selectEncryptJobId)) {
            List<JobBean> jobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
            if (!LList.isEmpty(jobList)) {
                for (JobBean jobBean : jobList) {
                    if (null != jobBean && selectEncryptJobId.equals(jobBean.encryptJobId)) {
                        long selectJobId = jobBean.id;
                        setSelectJobId(selectJobId);
                        setSelectEncryptJobId(selectEncryptJobId);
                        adapter.delayNotifyItem(selectJobId);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 获取选中的职位id
     *
     * @return
     */
    public long getSelectJobId() {
        long jobId = 0;
        if (adapter != null) {
            jobId = adapter.getSelectJobId();
        }
        if (jobId <= 0) {
            jobId = selectJobId;
        }
        return jobId;
    }

    /**
     * 1218.253 【商业】匿名牛人详情页增加负反馈、举报入口：获取选中职位的加密ID
     *
     * @return 加密ID
     */
    @Nullable
    public String getSelectEncryptJobId() {
        List<JobBean> jobBeanList = adapter == null ? null : adapter.getData();
        if (jobBeanList == null || LList.isEmpty(jobBeanList)) {
            return selectEncryptJobId;
        }
        long onSelectedJobId = getSelectJobId();
        for (JobBean jobBean : jobBeanList) {
            if (jobBean != null && jobBean.id == onSelectedJobId) {
                return jobBean.encryptJobId;
            }
        }
        return selectEncryptJobId;
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_select_job_to_chat_layout, this);
        clParent = view.findViewById(R.id.cl_parent);
        clParent.setOnClickListener(this);
        clParent.getBackground().mutate().setAlpha(0);
        clJob = view.findViewById(R.id.cl_job);
        descTv = view.findViewById(R.id.tv_desc);
        tvTitle = view.findViewById(R.id.tv_job_select_title);

        rvList = view.findViewById(R.id.rv_list);
        adapter = new OnlineJobSelectAdapter(context);
        rvList.setAdapter(adapter);

        view.findViewById(R.id.iv_close).setOnClickListener(this);
    }

    /**
     * 901.560 同事推荐加入 表示提示选择职位的提示文案，如: “以下是与该牛人期望相关的职位“
     */
    public void setTitleDesc(String selectJobTips) {
        if (null != descTv) {
            descTv.setText(selectJobTips, View.GONE);
        }
    }

    /**
     * 1305.253【商业】搜畅道具激活场景优化：新增自定义底部弹窗标题信息
     *
     * @param titleValue 标题信息
     */
    public void setTvTitleValue(@NonNull String titleValue) {
        if (tvTitle != null) {
            tvTitle.setText(titleValue);
        }
    }

    //置顶
    public void scrollToTopPosition() {
        if (null != rvList && null != adapter) {
            rvList.scrollToPosition(0);
        }
    }

    //滚动到选择的位置
    public void scrollToSelectedPosition() {
        if (null != rvList && null != adapter) {
            int position = adapter.indSelectedPosition();
            position = position == -1 ? 0 : position;
            rvList.scrollToPosition(position);
        }
    }

    /**
     * 执行网络请求，获取在线开聊的职位id
     */

    OnClickDisMiss dismissListener = null;

    public void setDismissListener(OnClickDisMiss dismissListener) {
        this.dismissListener = dismissListener;
    }

    public List<OnClickDisMiss> disMissListenerList = new ArrayList<>();

    public void addDismissListener(OnClickDisMiss dismissListener) {
        if (null != dismissListener && !disMissListenerList.contains(dismissListener)) {
            disMissListenerList.add(dismissListener);
        }
    }

    public void removeDismissListener(OnClickDisMiss dismissListener) {
        if (null != dismissListener) {
            disMissListenerList.remove(dismissListener);
        }
    }

    public void removeAllDismissListener() {
        disMissListenerList.clear();
    }

    public void notifyDismissListener(boolean show) {
        for (OnClickDisMiss disMiss : disMissListenerList) {
            if (null != disMiss) {
                disMiss.showOrDismiss(show);
            }
        }
    }

    public void getMatchJob(BaseActivity activity, String securityId, OnActionWithOnlyOneJobOnlineListener listener) {
        List<JobBean> jobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
        int jobCount = LList.getCount(jobList);
        if (jobCount <= 0) {
            T.ss("暂时未开放职位");
        } else if (jobCount == 1) { // 只有一个职位
            JobBean job = jobList.get(0);
            if (job != null) {
                selectJobId = job.id;
            }
            if (listener != null) {
                listener.doActionWithOnlyOneJob(selectJobId);
            }
        } else {
            //本地记住
            long historySelectedJobId = GeekResumeChatHelper.getHistorySelectedJobId();
            if (historySelectedJobId > 0) {
                onGetMatchJobId(historySelectedJobId, jobList, null);
            } else {
                //服务器获取
                getMatchJobFromServer(activity, securityId, jobList, listener);
            }
        }
    }

    private void getMatchJobFromServer(BaseActivity activity, String securityId, List<JobBean> jobList, OnActionWithOnlyOneJobOnlineListener listener) {
        GetMatchItemJobRequest request = new GetMatchItemJobRequest(new ApiRequestCallback<GetMatchItemJobResponse>() {

            @Override
            public void onStart() {
                if (dismissListener != null) {
                    dismissListener.showOrDismiss(false);
                }
                activity.showProgressDialog("正在获取数据");
            }

            @Override
            public void handleInChildThread(ApiData<GetMatchItemJobResponse> data) {
                GetMatchItemJobResponse resp = data.resp;
                if (resp != null) {
                    long jobId = resp.jobId;
                    data.setExtendParam("matchJobId", jobId);
                }
            }

            @Override
            public void onSuccess(ApiData<GetMatchItemJobResponse> data) {
                if (data == null || data.resp == null) return;
                long selectJobId = data.resp.jobId;
                onGetMatchJobId(selectJobId, jobList, securityId);
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }

    public void getMatchJobLocal(long selectedJobId, OnActionWithOnlyOneJobOnlineListener listener) {
        getMatchJobLocal(selectedJobId, true, listener);
    }

    public void getMatchJobLocal(long selectedJobId, boolean useHistoryJobId, OnActionWithOnlyOneJobOnlineListener listener) {
        List<JobBean> jobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
        int jobCount = LList.getCount(jobList);
        if (jobCount <= 0) {
            T.ss("暂时未开放职位");
        } else if (jobCount == 1) { // 只有一个职位
            JobBean job = jobList.get(0);
            if (job != null) {
                setSelectJobId(job.id);
                setSelectEncryptJobId(job.encryptJobId);
            }
            if (listener != null) {
                listener.doActionWithOnlyOneJob(selectJobId);
            }
        } else {
            //本地记住
            long historySelectedJobId = GeekResumeChatHelper.getHistorySelectedJobId();
            if (useHistoryJobId && historySelectedJobId > 0) {
                onGetMatchJobId(historySelectedJobId, jobList, null);
            } else {
                /*兜底选中职位列表中的第一个职位*/
                JobBean firstJob = LList.getElement(jobList, 0);
                if (selectedJobId <= 0) {
                    selectedJobId = null != firstJob ? firstJob.id : 0;
                }
                onGetMatchJobId(selectedJobId, jobList, null);
            }
        }
    }

    public void getMatchJob2(BaseActivity activity, String securityId, OnActionWithOnlyOneJobOnlineListener listener) {
        GetMatchItemJobRequest request = new GetMatchItemJobRequest(new ApiRequestCallback<GetMatchItemJobResponse>() {

            @Override
            public void onStart() {
                if (dismissListener != null) {
                    dismissListener.showOrDismiss(false);
                }
                activity.showProgressDialog("正在获取数据");
            }

            @Override
            public void handleInChildThread(ApiData<GetMatchItemJobResponse> data) {
                GetMatchItemJobResponse resp = data.resp;
                if (resp != null) {
                    long jobId = resp.jobId;
                    data.setExtendParam("matchJobId", jobId);

                    List<JobBean> jobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
                    data.setExtendParam("joblist", jobList);
                }
            }

            @Override
            public void onSuccess(ApiData<GetMatchItemJobResponse> data) {
                List<JobBean> jobList = (List<JobBean>) data.getExtendParam("joblist");
                int jobCount = LList.getCount(jobList);
                if (jobCount <= 0) {
                    T.ss("暂时未开放职位");
                } else if (jobCount == 1) { // 只有一个职位
                    JobBean job = jobList.get(0);
                    if (job != null) {
                        selectJobId = job.id;
                    }
                    if (listener != null) {
                        listener.doActionWithOnlyOneJob(selectJobId);
                    }
                } else {
                    JobBean selectJob = null;
                    selectJobId = (long) data.getExtendParam("matchJobId");
                    Iterator<JobBean> it = jobList.iterator();
                    while (it.hasNext()) {
                        JobBean item = it.next();
                        if (item != null && item.id == selectJobId) {
                            selectJob = item;
                            it.remove();
                            break;
                        }
                    }
                    if (selectJob != null) {
                        LList.addElement(jobList, selectJob, 0);
                        String jobInfo = StringUtil.connectTextWithChar("·", selectJob.positionName, selectJob.salaryDesc);
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHANGE_CONNECT_JOB)
                                .param("p", securityId)
                                .param("p2", selectJobId)
                                .param("p3", jobInfo)
                                .param("p4", 0)
                                .build();
                    }
                    refreshAdapter(securityId, jobList);
                }
            }

            @Override
            public void onComplete() {
                activity.dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }

    private void onGetMatchJobId(long jobId, List<JobBean> jobList, String securityId) {
        JobBean selectJob = null;
        selectJobId = jobId;
        Iterator<JobBean> it = jobList.iterator();
        while (it.hasNext()) {
            JobBean item = it.next();
            if (item != null && item.id == selectJobId) {
                selectJob = item;
                it.remove();
                break;
            }
        }
        if (null != selectJob) {
            LList.addElement(jobList, selectJob, 0);
        }
        if (selectJob != null && !TextUtils.isEmpty(securityId)) {
            String jobInfo = StringUtil.connectTextWithChar("·", selectJob.positionName, selectJob.salaryDesc);
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHANGE_CONNECT_JOB)
                    .param("p", securityId)
                    .param("p2", selectJobId)
                    .param("p3", jobInfo)
                    .param("p4", 0)
                    .build();
        }
        refreshAdapter(securityId, jobList);
    }

    public void refreshAdapter(String securityId, List<JobBean> jobList) {
        refreshAdapter(securityId, jobList, new OnJobSelectListener() {
            @Override
            public void onJobSelect(@NonNull JobBean job) {
                selectJobId = job.id;
                selectEncryptJobId = job.encryptJobId;
                if (onJobSelectListener != null) {
                    onJobSelectListener.onJobSelect(job);
                }
            }
        }, onJobDelaySelectListener);
    }

    public void refreshAdapter(String securityId, List<JobBean> jobList, OnJobSelectListener selectListener, OnJobDelaySelectListener delaySelectListener) {
        if (adapter != null) {
            adapter.setOnJobSelectListener(selectListener);
            adapter.setDelaySelectListener(delaySelectListener);
            adapter.setSecurityId(securityId);
            adapter.setSelectJobId(selectJobId);
            adapter.setNewData(jobList);
        }
        // 展示动画
        show();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.iv_close) {
            if (dismissListener != null) {
                dismissListener.showOrDismiss(false);
            }
            dismiss();
        } else if (id == R.id.cl_parent) {
            if (dismissListener != null) {
                dismissListener.showOrDismiss(false);
            }
            dismiss();
        }
    }

    private void show() {
        isShowing = true;
        if (dismissListener != null) {
            dismissListener.showOrDismiss(isShowing);
        }
        notifyDismissListener(true);
        handler.sendEmptyMessageDelayed(HANDLE_ANIM_START, 16);
    }

    /**
     * 关闭弹窗
     */
    public void dismiss() {
        isShowing = false;
        notifyDismissListener(false);
        removeAllDismissListener();
        handler.sendEmptyMessageDelayed(HANDLE_ANIM_START, 16);
    }

    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == HANDLE_ANIM_START) {
                doAnim();
                return true;
            }
            return false;
        }
    });

    private AnimatorSet animatorSet;

    private void doAnim() {
        if (animatorSet != null && animatorSet.isStarted()) return;

        animatorSet = new AnimatorSet();
        animatorSet.setDuration(150);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (!isShowing) {
                    setVisibility(GONE);
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                if (isShowing) {
                    setVisibility(VISIBLE);
                }
            }
        });
        // 背景渐变
        ValueAnimator alphaAnim;
        // 列表平移
        ValueAnimator tranYAnim;
        if (isShowing) {
            alphaAnim = ValueAnimator.ofFloat(0f, 0.6f);
            tranYAnim = ValueAnimator.ofInt(popViewHeight, 0);
        } else {
            alphaAnim = ValueAnimator.ofFloat(0.6f, 0f);
            tranYAnim = ValueAnimator.ofInt(0, popViewHeight);
        }
        alphaAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float f = (float) animation.getAnimatedValue();
                int alpha = (int) (255 * f);
                clParent.getBackground().mutate().setAlpha(alpha);
            }
        });
        tranYAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int y = (int) animation.getAnimatedValue();
                clJob.setTranslationY(y);
            }
        });
        animatorSet.playTogether(alphaAnim, tranYAnim);
        animatorSet.start();
    }

    public static class OnlineJobSelectAdapter extends BaseQuickAdapter<JobBean, BaseViewHolder> {

        protected Context context;

        protected OnJobSelectListener listener;
        /*延迟选择监听*/
        protected OnJobDelaySelectListener delaySelectListener;

        private long selectJobId;
        private String securityId;

        public OnlineJobSelectAdapter(Context context) {
            super(R.layout.item_online_job_select_panel, null);
            this.context = context;
        }

        public void setOnJobSelectListener(OnJobSelectListener listener) {
            this.listener = listener;
        }

        public void setDelaySelectListener(OnJobDelaySelectListener delaySelectListener) {
            this.delaySelectListener = delaySelectListener;
        }

        public void setSelectJobId(long selectJobId) {
            this.selectJobId = selectJobId;
        }

        public long getSelectJobId() {
            return selectJobId;
        }

        public void setSecurityId(String securityId) {
            this.securityId = securityId;
        }

        /*延迟刷新item(如：点击item后，调接口，等待接口成功后再次更新Item)*/
        public void delayNotifyItem(long selectJobId) {
            setSelectJobId(selectJobId);
            notifyDataSetChanged();
        }

        public int indSelectedPosition() {
            int selPos = -1;
            for (int i = 0; i < getItemCount(); i++) {
                JobBean item = getItem(i);
                if (null != item && item.id == selectJobId) {
                    selPos = i;
                    break;
                }
            }
            return selPos;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, JobBean item) {
            if (null == item) return;

            MTextView tvJobInfo = helper.getView(R.id.tv_job_info);
            MTextView tvSalary = helper.getView(R.id.tv_salary_desc);
            AppCompatImageView ivAnonymous = helper.getView(R.id.iv_proxy_anonymous);
            MTextView tvCompanyInfo = helper.getView(R.id.tv_company_info);

            int position = helper.getAdapterPosition();

            JobBean job = getItem(position);
            if (job != null) {
                String jobInfo = StringUtil.connectTextWithChar("·", job.positionName, job.salaryDesc);

                tvJobInfo.setText(job.positionName);
                tvSalary.setText(job.salaryDesc);

                //-1 不允许 匿名，0:允许匿名 但是不匿名; 大于0 允许匿名，匿名中
                ivAnonymous.setVisibility(!TextUtils.isEmpty(job.anonymousIcon) ? VISIBLE : GONE);
                //1009.152【招聘者】外包职位的品牌名称展示位置梳理
                tvCompanyInfo.setText(job.getBrandNameIfProxyOrOutSourceJob(), View.GONE);

                if (selectJobId == job.id) {
                    if (null != tvJobInfo.getPaint()) {
                        tvJobInfo.getPaint().setFakeBoldText(true);
                    }
                    tvJobInfo.setTextColor(ContextCompat.getColor(context, R.color.app_green_dark));
                    tvSalary.setTextColor(ContextCompat.getColor(context, R.color.app_green_dark));
                } else {
                    if (null != tvJobInfo.getPaint()) {
                        tvJobInfo.getPaint().setFakeBoldText(false);
                    }
                    tvJobInfo.setTextColor(ContextCompat.getColor(context, R.color.text_c6));
                    tvSalary.setTextColor(ContextCompat.getColor(context, R.color.text_c6));
                }

                helper.itemView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        long selectJobId = job.id;

                        /*优先执行延迟监听，延迟结束后手动刷新Item*/
                        if (delaySelectListener != null) {
                            delaySelectListener.onJobDelaySelect(job);
                        } else {

                            setSelectJobId(selectJobId);
                            notifyDataSetChanged();

                            if (listener != null) {
                                listener.onJobSelect(job);
                            }
                        }

                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHANGE_CONNECT_JOB)
                                .param("p", LText.isEmptyOrNull(securityId) ? "" : securityId)
                                .param("p2", selectJobId)
                                .param("p3", jobInfo)
                                .param("p4", 1)
                                .build();
                    }
                });
            }
        }
    }

    public interface OnJobSelectListener {
        void onJobSelect(@NonNull JobBean job);
    }

    public interface OnJobDelaySelectListener {
        void onJobDelaySelect(@NonNull JobBean job);
    }

    public interface OnActionWithOnlyOneJobOnlineListener {
        void doActionWithOnlyOneJob(long jobId);
    }

    /**
     * 监听一下关闭的事件
     */
    public interface OnClickDisMiss {
        void showOrDismiss(boolean show);
    }

}
