package com.hpbr.bosszhipin.module.commend.weight;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.monch.lbase.util.LList;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

public class GeekRGItemCityFilterAdapter extends BaseQuickAdapter<LevelBean, BaseViewHolder> {

    protected String title;
    public void setTitle(String title) {
        this.title = title;
    }
    public String getTitle() {
        return title;
    }
    @Nullable
    private IOnCityClickListener onCityClickListener;

    void setOnCityClickListener(@Nullable IOnCityClickListener onCityClickListener) {
        this.onCityClickListener = onCityClickListener;
    }

    @Nullable
    public IOnCityClickListener getOnCityClickListener() {
        return onCityClickListener;
    }

    public GeekRGItemCityFilterAdapter() {
        super(R.layout.app_f1_filter_layout_item_city_filter_item, null);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        if (null == item) return;

        GeekRGSubItemCityFilterLayout filterLayout = helper.getView(R.id.sub_item_layout);

        onRefreshAdsFilterList(filterLayout, item.name, item.subLevelModeList, null);
    }


    public void onRefreshAdsFilterList(GeekRGSubItemCityFilterLayout filterLayout, String subTitle,
                                       List<LevelBean> filterList, List<LevelBean> selFilterList) {
        if (!LList.isEmpty(filterList)) {
            filterLayout.setTitle(subTitle);
            filterLayout.setFilterData(filterList, getTitle(), subTitle, getOnCityClickListener());
        }
        ZPUtils.setGone(filterLayout, filterList);
    }
}
