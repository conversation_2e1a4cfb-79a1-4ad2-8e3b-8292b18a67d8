package com.hpbr.bosszhipin.module.commend.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.List;

import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;

public class GeekRGItemCityFilterLayout extends ZPUIFrameLayout {

    public Context mContext;
    protected ZPUIConstraintLayout mClParent;
    protected MTextView mTvTitle;
    protected RecyclerView mRvList;

    protected GeekRGItemCityFilterAdapter filterAdapter;

    public GeekRGItemCityFilterAdapter getFilterAdapter() {
        return filterAdapter;
    }

    public GeekRGItemCityFilterLayout(@NonNull Context context) {
        this(context, null);
    }

    public GeekRGItemCityFilterLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GeekRGItemCityFilterLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mContext = context;

        View view = View.inflate(context, R.layout.app_f1_filter_layout_item_city_filter, this);

        mClParent = view.findViewById(R.id.cl_parent);
        mTvTitle = view.findViewById(R.id.tv_title);
        mRvList = view.findViewById(R.id.rv_list);

        filterAdapter = new GeekRGItemCityFilterAdapter();
        mRvList.setAdapter(filterAdapter);
    }

    public void setTitle(String title) {
        mTvTitle.setText(title);
    }

    /**
     * @param filterBeanList   筛选数据
     */
    public void setFilterData(String title, List<LevelBean> filterBeanList, @Nullable IOnCityClickListener onCityClickListener) {
        if (null != filterAdapter) {
            filterAdapter.setTitle(title);
            filterAdapter.setOnCityClickListener(onCityClickListener);
            filterAdapter.setNewData(filterBeanList);
        }
        ZPUtils.setGone(mRvList, filterBeanList);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void notifyFilterAdapter() {
        if (null != filterAdapter) {
            filterAdapter.notifyDataSetChanged();
        }
    }
}
