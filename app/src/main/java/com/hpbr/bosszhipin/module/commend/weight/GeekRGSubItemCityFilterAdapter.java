package com.hpbr.bosszhipin.module.commend.weight;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;

import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButtonDrawable;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class GeekRGSubItemCityFilterAdapter extends BaseQuickAdapter<LevelBean, BaseViewHolder> {

    private String title;

    private String subTitle;

    public void setTitle(String title, String subTitle) {
        this.title = title;
        this.subTitle = subTitle;
    }

    public String getTitle() {
        return title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    @Nullable
    private IOnCityClickListener onCityClickListener;

    void setOnCityClickListener(@Nullable IOnCityClickListener onCityClickListener) {
        this.onCityClickListener = onCityClickListener;
    }

    @Nullable
    public IOnCityClickListener getOnCityClickListener() {
        return onCityClickListener;
    }

    public GeekRGSubItemCityFilterAdapter() {
        super(R.layout.app_f1_filter_layout_sub_item_city_filter_item, null);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {

        if (null != item && !TextUtils.isEmpty(item.name)) {

            helper.itemView.setVisibility(View.VISIBLE);

            boolean selected = null != getOnCityClickListener()
                    && getOnCityClickListener().isItemSelected(item);
            boolean available = true;

            ZPUIRoundButton roundButton = helper.getView(R.id.btn_text);

            /*https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=133870451*/
            /*916.610【策略】 【C端】期望城市选择支持选择县 和 区的尝试 @闫泽华 @朱振楠@张祥东@范佳男@郭俊威@刘继帅@闫莹莹 @张一弛*/
            if (item.isCountyOrDistrict() && !LText.empty(item.parentName)) { // 区县显示成：区县（地级市）（916.610）
                roundButton.setText(String.format(Locale.getDefault(), "%s\n（%s）", item.name, item.parentName));
            } else {
                roundButton.setText(item.name);
            }

            setFilterItemGreenBg(roundButton, selected, !available, 1, 8);
            /*禁用状态*/
            roundButton.setEnabled(available);
            helper.itemView.setEnabled(available);

            item.setChecked(selected);

            helper.itemView.setOnClickListener(new OnClickNoFastListener() {
                @SuppressLint("NotifyDataSetChanged")
                @Override
                public void onNoFastClick(View v) {

                    if (onCityClickListener != null) {

//                        LocationService.LocationBean locationBean = LocationService.location;
//                        if (locationBean != null && !TextUtils.isEmpty(locationBean.subLocalCityCode)) {
//                            long subCode = LText.getLong(locationBean.subLocalCityCode);
//                            if (selectedBean.code == subCode) {
//                                AnalyticsFactory.create().action("action-search-expect-cityclick").param("p", item.code).build();
//                            }
//                        }

                        if (onCityClickListener.isMultiSelectionEnabled()) { // 多选模式
                            /* 用户点击未选中的城市尝试去选中 */
                            boolean isItemNotChecked = !item.isChecked();
                            if (isItemNotChecked) {
                                if (!onCityClickListener.isItemSelectable(item)) {
                                    return;
                                }

                                if (onCityClickListener.isFull()) {
                                    return;
                                }

                                if (onCityClickListener.pickAssociatedCity(item) != null) {
                                    return;
                                }
                            }

                            // 反选选中状态
                            item.setChecked(isItemNotChecked);
                        }

                        onCityClickListener.onCityClick(StringUtil.connectTextWithChar(
                                "_", getTitle(), getSubTitle()), item);

                        notifyDataSetChanged();
                    }

                }
            });

        } else {
            helper.itemView.setVisibility(View.GONE);
        }

    }

    /**
     * 绿色筛选项背景色
     */
    @SuppressWarnings("ConditionalExpressionWithIdenticalBranches")
    public static void setFilterItemGreenBg(@NonNull ZPUIRoundButton roundButton,
                                            boolean selected, boolean disable,
                                            int strokeWidthDp, int radiusDp) {

        Context context = roundButton.getContext();

        @ColorInt
        int backgroundColor = ContextCompat.getColor(context,
                selected ? R.color.color_1F15B3B3 :
                        disable ? R.color.color_FFF5F5F5_FF262629 :
                                R.color.color_FFF5F5F5_FF262629);
        ColorStateList backgroundStateList = ZPUIRoundButtonDrawable.getColorStateList(
                backgroundColor, backgroundColor, backgroundColor
        );

        @ColorInt
        int strokeColor = ContextCompat.getColor(context,
                selected ? R.color.app_green_dark :
                        disable ? R.color.color_FFF5F5F5_FF262629 :
                                R.color.color_FFF5F5F5_FF262629);
        int strokeWidth = ZPUIDisplayHelper.dp2px(context, strokeWidthDp);
        ColorStateList strokeStateList = ZPUIRoundButtonDrawable.getColorStateList(
                strokeColor, strokeColor, strokeColor
        );

        @ColorInt
        int textColor = ContextCompat.getColor(context,
                selected ? R.color.app_green_dark :
                        disable ? R.color.text_c3 : R.color.text_c6);
        ColorStateList textStateList = ZPUIRoundButtonDrawable.getColorStateList(
                textColor, textColor, textColor
        );

        ZPUIRoundButtonDrawable buttonDrawable =
                (ZPUIRoundButtonDrawable) roundButton.getBackground();
        /*背景色*/
        buttonDrawable.setColor(backgroundStateList);
        /*边框*/
        buttonDrawable.setStrokeData(strokeWidth, strokeStateList);
        /*圆角*/
        buttonDrawable.setCornerRadius(ZPUIDisplayHelper.dp2px(context, radiusDp));
        /*文本颜色*/
        roundButton.setTextColor(textStateList);
    }
}
