package com.hpbr.bosszhipin.module.commend.weight;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnCityClickListener;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.ZPUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.decoration.GridSpacingItemDecoration;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.shadow.layout.ZPUIFrameLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class GeekRGSubItemCityFilterLayout extends ZPUIFrameLayout {

    protected ZPUIConstraintLayout mClParent;
    protected MTextView mTvTitle;
    protected RecyclerView mRvList;

    protected GeekRGSubItemCityFilterAdapter filterAdapter;

    public GeekRGSubItemCityFilterLayout(@NonNull Context context) {
        this(context, null);
    }

    public GeekRGSubItemCityFilterLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GeekRGSubItemCityFilterLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        View view = View.inflate(context, R.layout.app_f1_filter_layout_sub_item_city_filter, this);
        mClParent = view.findViewById(R.id.cl_parent);
        mTvTitle = view.findViewById(R.id.tv_title);
        mRvList = view.findViewById(R.id.rv_list);

        GridSpacingItemDecoration decoration = new GridSpacingItemDecoration(3, ZPUIDisplayHelper.dp2px(context, 10), false);
        mRvList.addItemDecoration(decoration);

        filterAdapter = new GeekRGSubItemCityFilterAdapter();
        mRvList.setAdapter(filterAdapter);
    }

    public void setTitle(String title) {
        mTvTitle.setText(title);
    }

    /**
     * @param filterBeanList 筛选数据
     */
    public void setFilterData(List<LevelBean> filterBeanList,
                              String title,
                              String subTitle,
                              IOnCityClickListener onCityClickListener) {
        if (null != filterAdapter) {
            filterAdapter.setTitle(title, subTitle);
            filterAdapter.setOnCityClickListener(onCityClickListener);
            filterAdapter.setNewData(filterBeanList);
        }
        ZPUtils.setGone(mRvList, filterBeanList);
    }
}
