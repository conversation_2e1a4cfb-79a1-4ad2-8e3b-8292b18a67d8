package com.hpbr.bosszhipin.module.commend.weight;

import android.view.View;

import androidx.annotation.NonNull;

import com.chad.library.adapter.base.BaseQuickAdapter;

import java.util.Set;

public interface OnItemMultiSelectedListener<Child> {

    void onMultiSelected(@NonNull BaseQuickAdapter<?, ?> adapter, View view, int position, @NonNull Child selectedBean, @NonNull Set<Child> hasSelectedBean, boolean invertSelection);
}
