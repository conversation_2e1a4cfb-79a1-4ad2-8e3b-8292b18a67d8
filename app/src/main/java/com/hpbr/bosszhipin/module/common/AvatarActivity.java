package com.hpbr.bosszhipin.module.common;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewTreeObserver;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.fresco.lib.FrescoUtils;
import com.fresco.lib.zoomable.widget.DoubleTapGestureListener;
import com.fresco.lib.zoomable.widget.ZoomableDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.main.AxisBean;
import com.hpbr.bosszhipin.module.webview.DownLoadUtil;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.monch.lbase.widget.T;

/**
 * Created by monch on 15/4/17.
 * Last Edited by zhouyou
 * 预览图片
 */
public class AvatarActivity extends BaseActivity implements OnClickListener {

    private static final long ANIMATION_DURATION = 200;

    private View viewBackground;

    private ZoomableDraweeView imageView;
    // 头像控件的布局参数
    private FrameLayout.LayoutParams params = null;
    // 小头像初始显示时相对屏幕的坐标实体
    private AxisBean axis;
    // 屏幕宽度
    private int screenWidth = 0;
    // 屏幕高度
    private int screenHeight = 0;
    // 小图片的url
    private String avatarUrl;
    // 大图片的url
    private String largeUrl;
    // 是否是本地图片
    private boolean isLocalImage;

    private long userId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        String[] urls = intent.getStringArrayExtra(Constants.DATA_URL);
        if (urls == null || urls.length != 2) {
            T.ss("图片获取失败");
            AppUtil.finishActivity(AvatarActivity.this, ActivityAnimType.ALPHA);
            return;
        }
        userId = intent.getLongExtra(Constants.DATA_ID, 0);
        avatarUrl = urls[0];
        largeUrl = urls[1];
        if (TextUtils.isEmpty(avatarUrl) && TextUtils.isEmpty(largeUrl)) {
            AppUtil.finishActivity(this, ActivityAnimType.ALPHA);
            return;
        }
        isLocalImage = intent.getBooleanExtra(Constants.DATA_BOOLEAN, false);

        screenHeight = App.get().getDisplayHeight();
        screenWidth = App.get().getDisplayWidth();
        axis = (AxisBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
        setContentView(R.layout.activity_avatar);

        findViewById(R.id.btn_save).setOnClickListener(this);
        imageView = (ZoomableDraweeView) findViewById(R.id.iv_photo);
        DoubleTapGestureListener listener = new DoubleTapGestureListener(imageView);
        listener.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                startZoomInAnimation();
                startFadeOutAnimation();
            }
        });
        imageView.setTapListener(listener);
        imageView.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
            @Override
            public boolean onPreDraw() {
                imageView.getViewTreeObserver().removeOnPreDrawListener(this);
                resizeImage();
                return false;
            }
        });

        viewBackground = findViewById(R.id.view_background);
        viewBackground.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
            @Override
            public boolean onPreDraw() {
                viewBackground.getViewTreeObserver().removeOnPreDrawListener(this);
                startFadeInAnimation();
                return false;
            }
        });
    }

    private void resizeImage() {
        if (axis == null || axis.width < 0 || axis.height < 0) {
            params = new FrameLayout.LayoutParams(screenWidth, screenWidth);
            int sideMargin = (screenHeight - screenWidth) / 2;
            params.setMargins(0, sideMargin, 0, sideMargin);
            imageView.setLayoutParams(params);
            if (isLocalImage) {
                FrescoUtils.loadFromLocal(imageView, avatarUrl, largeUrl);
            } else {
                FrescoUtils.loadFromNetwork(imageView, avatarUrl, largeUrl);
            }
        } else {
            params = new FrameLayout.LayoutParams(axis.width, axis.height);
            params.setMargins(axis.x, axis.y, 0, 0);
            imageView.setLayoutParams(params);
            if (isLocalImage) {
                FrescoUtils.loadFromLocal(imageView, avatarUrl, largeUrl);
            } else {
                FrescoUtils.loadFromNetwork(imageView, avatarUrl, largeUrl);
            }
            startZoomOutAnimation();
        }
    }

    /**
     * 开启图片放大动画
     */
    private void startZoomOutAnimation() {
        imageView.clearAnimation();
        float zoomOutRatio = (float) screenWidth / axis.width;

        AnimationSet anim = new AnimationSet(true);
        ScaleAnimation sa = new ScaleAnimation(1f, zoomOutRatio, 1f, zoomOutRatio, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        sa.setDuration(ANIMATION_DURATION);

        // 横坐标移动的距离
        int distanceX = screenWidth / 2 - axis.width / 2 - axis.x;
        // 纵坐标移动的距离
        int distanceY = screenHeight / 2 - axis.height / 2 - axis.y;
        TranslateAnimation ta = new TranslateAnimation(1f, distanceX, 1f, distanceY);
        ta.setDuration(ANIMATION_DURATION);
        anim.addAnimation(sa);
        anim.addAnimation(ta);
        anim.setFillBefore(false);
        anim.setFillAfter(true);
        anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                viewBackground.setClickable(false);
                imageView.setClickable(false);
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                // 重新设置图片大小
                params = (FrameLayout.LayoutParams) imageView.getLayoutParams();
                params.width = FrameLayout.LayoutParams.MATCH_PARENT;
                params.height = FrameLayout.LayoutParams.MATCH_PARENT;
                params.gravity = Gravity.CENTER;
                params.setMargins(0, getStatusBarHeight() / 2, 0, 0);
                imageView.setLayoutParams(params);
                // 清空动画
                imageView.clearAnimation();
                viewBackground.clearAnimation();
                // 恢复点击效果
                viewBackground.setClickable(true);
                imageView.setClickable(true);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        imageView.startAnimation(anim);
    }

    private int getStatusBarHeight() {
        Rect frame = new Rect();
        getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        return frame.top;
    }

    /**
     * 开启图片缩小动画
     */
    private void startZoomInAnimation() {
        imageView.clearAnimation();
        if (axis == null) return;
        float zoomInRatio = (float) axis.width / screenWidth;
        AnimationSet anim = new AnimationSet(true);
        ScaleAnimation sa = new ScaleAnimation(1f, zoomInRatio, 1f, zoomInRatio, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        sa.setDuration(ANIMATION_DURATION);

        int distanceX = screenWidth / 2 - axis.width / 2 - axis.x;
        TranslateAnimation ta;
        int distanceY;
        if (axis.y < screenHeight / 2) {
            distanceY = screenHeight / 2 - axis.height / 2 - axis.y;
            ta = new TranslateAnimation(1f, -distanceX, 1f, -distanceY);
        } else if (axis.y == screenHeight / 2) {
            ta = new TranslateAnimation(1f, -distanceX, 1f, 1f);
        } else {
            distanceY = axis.y - screenHeight / 2 + axis.height / 2;
            ta = new TranslateAnimation(1f, -distanceX, 1f, distanceY);
        }
        ta.setDuration(ANIMATION_DURATION);

        anim.addAnimation(sa);
        anim.addAnimation(ta);
        anim.setFillBefore(false);
        anim.setFillAfter(true);
        anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
                viewBackground.setClickable(false);
                imageView.setClickable(false);
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                params = (FrameLayout.LayoutParams) imageView.getLayoutParams();
                params.width = axis.width;
                params.height = axis.height;
                params.gravity = Gravity.NO_GRAVITY;
                params.setMargins(axis.x, axis.y, 0, 0);
                imageView.setLayoutParams(params);
                imageView.clearAnimation();
                viewBackground.clearAnimation();
                handler.sendEmptyMessageDelayed(0, 50);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        imageView.startAnimation(anim);
    }

    /**
     * 开始背景淡入动画
     */
    private void startFadeInAnimation() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(viewBackground, "alpha", 0f, 0.8f);
        animator.setDuration(ANIMATION_DURATION);
        animator.start();
    }

    /**
     * 开始背景淡出动画
     */
    private void startFadeOutAnimation() {
        ObjectAnimator animator = ObjectAnimator.ofFloat(viewBackground, "alpha", 0.8f, 0f);
        animator.setDuration(ANIMATION_DURATION);
        animator.start();
    }

    /**
     * 返回键点击（只能点击一次）
     */
    private boolean hasOnBackKeyPressed = false;

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!hasOnBackKeyPressed) {
                hasOnBackKeyPressed = true;
                startZoomInAnimation();
                startFadeOutAnimation();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private Handler handler = AppThreadFactory.createMainHandler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            AppUtil.finishActivity(AvatarActivity.this, ActivityAnimType.NONE);
            return true;
        }
    });

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.btn_save) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_SAVE_AVATAR).param("p", String.valueOf(userId)).build();
            PermissionHelper.getStorageHelper(this)
                    .setPermissionCallback(new PermissionCallback() {
                        @Override
                        public void onResult(boolean yes, @NonNull PermissionData permission) {
                            if (yes) {
                                DownLoadUtil.downLoadImageToSdcard(largeUrl);
                            } else {
                                T.ss("没有存储权限");
                            }
                        }
                    }).requestPermission();
        } else {
        }
    }

    /**
     * 启动图片预览的Activity
     */
    public static class Builder {
        private Activity activity;
        private String[] urls;
        private AxisBean axis;
        private boolean isLocalImage; // 加载本地图片
        private long userId;

        public Builder(Activity activity) {
            this.activity = activity;
        }

        public Builder urls(String[] urls) {
            this.urls = urls;
            return this;
        }

        public Builder axis(AxisBean axis) {
            this.axis = axis;
            return this;
        }

        public Builder local(boolean localImage) {
            isLocalImage = localImage;
            return this;
        }

        public Builder userId(long userId) {
            this.userId = userId;
            return this;
        }

        public static Builder build(Activity activity) {
            return new Builder(activity);
        }

        public void launch() {
            Intent intent = new Intent(activity, AvatarActivity.class);
            intent.putExtra(Constants.DATA_ENTITY, axis);
            intent.putExtra(Constants.DATA_URL, urls);
            intent.putExtra(Constants.DATA_BOOLEAN, isLocalImage);
            intent.putExtra(Constants.DATA_ID, userId);
            AppUtil.startActivity(activity, intent, ActivityAnimType.NONE);
        }
    }
}
