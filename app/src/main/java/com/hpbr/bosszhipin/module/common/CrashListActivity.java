package com.hpbr.bosszhipin.module.common;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.widget.ArrayAdapter;
import android.widget.ListView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.exception.CrashBean;
import com.monch.lbase.util.LDate;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 15/11/10.
 */
public class CrashListActivity extends BaseActivity {

    private ListView listView;
    private List<String> data;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_crash_list);
        listView = (ListView) findViewById(R.id.listView);
        new Thread(runnable).start();
    }

    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
            List<CrashBean> list = App.get().db().queryAll(CrashBean.class);
            data = makeData(list);
            handler.sendEmptyMessage(0);
        }
    };

    private List<String> makeData(List<CrashBean> list) {
        List<String> data = new ArrayList<>();
        if (list == null || list.size() <= 0) return data;
        for (CrashBean bean : list) {
            StringBuilder sb = new StringBuilder();
            sb.append("时间：").append(LDate.getTime(bean.time, "yyyy-MM-dd HH:mm:ss:SSS")).append("\n");
            sb.append("APP版本：").append(bean.appVersionName).append("\n");
            sb.append("手机型号：").append(bean.systemDevice).append("\n");
            sb.append("系统版本：").append(bean.systemVersion).append("\n");
            sb.append("线程名：").append(bean.currentThreadName).append("\n");
            sb.append("异常内容：").append(bean.crashContent);
            data.add(sb.toString());
        }
        return data;
    }

    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            listView.setAdapter(
                    new ArrayAdapter<>(CrashListActivity.this,
                            R.layout.item_crash,
                            R.id.tv_content,
                            data));
            return true;
        }
    });

}
