package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=100752840
 */
public class CurrentExpectLayout extends FrameLayout {

    @NonNull
    private final List<JobIntentBean> sourceData = new ArrayList<>();
    @NonNull
    private final RecommendAdapter adapter;
    @Nullable
    private OnExpectItemClickListener onExpectItemClickListener;

    public void setOnExpectItemClickListener(@Nullable OnExpectItemClickListener onExpectItemClickListener) {
        this.onExpectItemClickListener = onExpectItemClickListener;
    }

    public CurrentExpectLayout(@NonNull Context context) {
        this(context, null);
    }

    public CurrentExpectLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CurrentExpectLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        View view = LayoutInflater.from(getContext()).inflate(R.layout.layout_current_expect, this);
        RecyclerView rvRecommend = view.findViewById(R.id.rv_recommend);
        adapter = new RecommendAdapter();
        adapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                JobIntentBean item = (JobIntentBean) adapter.getItem(position);
                if (item != null) {
                    if (onExpectItemClickListener != null) {
                        onExpectItemClickListener.onLevelItemClick(item);
                    }
                }
            }
        });
        rvRecommend.setAdapter(adapter);
    }

    public void loadData() {
        // 加载期望数据
        List<JobIntentBean> geekExpectList = GeekExpectManager.getGeekExpectList();
        if (LList.getCount(geekExpectList) > 1) { // 如用户期望大于一个，则展示当前求职期望模块
            sourceData.addAll(geekExpectList);
            refreshAdapter();
        } else {
            refreshVisibility(false);
        }
    }

    public void refreshVisibility(boolean visibility) {
        boolean hasData = !LList.isEmpty(sourceData);
        if (visibility && hasData) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    private void refreshAdapter() {
        if (adapter != null) {
            adapter.setNewData(sourceData);
        }
        if (LList.isEmpty(sourceData)) {
            setVisibility(View.GONE);
        } else {
            setVisibility(View.VISIBLE);
        }
    }

    private static class RecommendAdapter extends BaseRvAdapter<JobIntentBean, BaseViewHolder> {

        public RecommendAdapter() {
            this(null);
        }

        public RecommendAdapter(@Nullable List<JobIntentBean> data) {
            super(R.layout.item_expect, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, JobIntentBean item) {
            String positionClassName = item.positionClassName;
            helper.setText(R.id.btn_word, positionClassName);
        }
    }

    public interface OnExpectItemClickListener {
        void onLevelItemClick(@NonNull JobIntentBean jobIntentBean);
    }

}
