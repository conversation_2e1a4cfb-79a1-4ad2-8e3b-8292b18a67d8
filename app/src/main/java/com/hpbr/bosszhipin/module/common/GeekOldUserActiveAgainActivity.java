package com.hpbr.bosszhipin.module.common;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.navigation.NavGraph;
import androidx.navigation.NavInflater;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.common.bean.BelongIndustryBean;
import com.hpbr.bosszhipin.module.common.bean.PositionNameBean;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.router.PageRouter;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.module.guideprocess.manager.GuideProcessJumpManager;
import com.hpbr.bosszhipin.module.my.activity.geek.SingleIndustryChooserWithRecommendActivity;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekBackflowRouter;
import com.hpbr.bosszhipin.module_geek_export.OnlineResumeConstants;
import com.hpbr.bosszhipin.module_geek_export.bean.GeekWorkPositionResultParams;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.sankuai.waimai.router.annotation.RouterUri;

import net.bosszhipin.api.bean.ExtraMapBean;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ：GeekOldUserActiveAgainActivity
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  4:58 PM
 */
@RouterUri(path = PageConstant.PATH_GEEK_OLD_USER_ACTIVE_AGAIN)
public class GeekOldUserActiveAgainActivity extends BaseAwareActivity<GeekOldUserActiveAgainViewModel> {

    @Override
    public boolean baseMonitorBackPress() {
        return false;
    }

    @Override
    protected int contentLayout() {
        return R.layout.activity_geek_old_user_active_again;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        mViewModel.initNavController(this, R.id.nav_host_fragment_container);
        initParams();
        initView();
        initNav();
        initData();
    }

    private void initParams() {
        Intent intent = getIntent();
        if (intent == null) return;
        mViewModel.days = intent.getIntExtra(Constants.KEY_DAYS, 0);
        mViewModel.certName = intent.getBooleanExtra(Constants.KEY_CERT_NAME, false);
        mViewModel.extraMapBean = (ExtraMapBean) intent.getSerializableExtra(Constants.KEY_EXTRA_MAP);
        mViewModel.guideProcessType = intent.getIntExtra(Constants.KEY_GUIDE_PROGRESS_TYPE, GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_NONE);
        mViewModel.isAdvantageCallBack = intent.getBooleanExtra(Constants.KEY_IS_ADVANTAGE_CALL_BACK, false);
        mViewModel.status = intent.getIntExtra(Constants.KEY_STATUS, 0);
        mViewModel.isHaveFlowBackCompleteProcess = isHaveFlowBackCompleteProcess();
        mViewModel.flowBackPageCodeList = getFlowBackPageCodeList();

        AppAnalysisUtil.dotWelcomeGeek();/*埋点*/
    }

    private void initView() {
        ImmersiveUtils.immersiveStyleBar(this, true, NightUtil.isDarkMode(this));
        /*这段代码是为了解决，登录之后进入到这个页面，界面上面莫名其妙的蒙层*/
        View view_line_2 = findViewById(R.id.view_line_2);
        view_line_2.requestFocus();
        view_line_2.setFocusable(true);
        view_line_2.setFocusableInTouchMode(true);
    }

    private void initData() {
        if (!mViewModel.isAdvantageCallBack) {
            mViewModel.completeAction();
        }
    }

    private void initNav() {
        int startDestinationID = 0;
        if (mViewModel.guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_WORK_EXP) {
            startDestinationID = R.id.workplace_people_active_welcome_fragment;
        } else if (mViewModel.guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR) {
            startDestinationID = mViewModel.isAdvantageCallBack ? R.id.request_job_expect_fragment : R.id.workplace_people_active_welcome_fragment;
        } else if (mViewModel.guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_STUDENT) {
            startDestinationID = R.id.student_active_welcome_fragment;
        }

        if (startDestinationID <= 0 || mViewModel.navController == null) {
            AppUtil.finishActivity(this);
            return;
        }

        NavInflater navInflater = mViewModel.navController.getNavInflater();
        NavGraph navGraph = navInflater.inflate(R.navigation.nav_graph_geek_old_user_active_again);
        navGraph.setStartDestination(startDestinationID);
        mViewModel.navController.setGraph(navGraph);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            if (requestCode == PageConstant.REQ_COMPANY) {//公司名称
                String companyName = data.getStringExtra(Constants.DATA_STRING);
                mViewModel.updateCompanyNameLiveData.postValue(companyName);
            } else if (requestCode == PageConstant.REQ_WORK_CONTENT) {//工作内容
                String workContent = data.getStringExtra(Constants.DATA_STRING);
                mViewModel.updateWorkContentLiveData.postValue(workContent);
            } else if (requestCode == PageConstant.REQ_INDUSTRY) {  //公司行业
                List<LevelBean> industryList = (List<LevelBean>) data.getSerializableExtra(Constants.DATA_ENTITY);
                long reportIndustryId = data.getLongExtra(Constants.DATA_LONG, 0);
                int mRecommedSelectedCount = data.getIntExtra(SingleIndustryChooserWithRecommendActivity.RECOMMEND_SELECT_COUNT, 0);
                if (industryList == null || LList.getCount(industryList) != 1) return;
                LevelBean industryLevelBean = LList.getElement(industryList, 0);
                if (industryLevelBean == null) return;

                BelongIndustryBean belongIndustryBean = new BelongIndustryBean(industryLevelBean.name, String.valueOf(industryLevelBean.code), reportIndustryId, mRecommedSelectedCount);
                mViewModel.updateBelongIndustryLiveData.postValue(belongIndustryBean);
            } else if (requestCode == OnlineResumeConstants.REQ_POSITION_CLASS) {
                GeekWorkPositionResultParams params = (GeekWorkPositionResultParams) data.getSerializableExtra(OnlineResumeConstants.WORK_POSITION_RESULT_PARAMS);
                if (params == null) {
                    return;
                }
                long reportPositionId = params.getReportedPositionId();
                String nlpSugName = params.getNlpReportName();
                LevelBean secondItem = params.getSecondItem();
                LevelBean thirdItem = params.getThirdItem();
                int positionLv2 = secondItem != null ? (int) secondItem.code : 0;
                if (thirdItem != null) {
                    String positionClassName = thirdItem.name;
                    String positionName = positionClassName;
                    if (LText.notEmpty(nlpSugName)) {
                        positionName = nlpSugName;
                    }

                    PositionNameBean positionNameBean = new PositionNameBean(positionName, thirdItem.code, positionClassName, reportPositionId, positionLv2);
                    mViewModel.updatePositionNameLiveData.postValue(positionNameBean);

                    AnalyticsFactory.create()
                            .action(AnalyticsAction.ACTION_USERINFO_MICRORESUME_WORK_ADDJOBTITLE)
                            .param("p", params.getPositionType())
                            .param("p2", positionName)
                            .param("p3", thirdItem.code)
                            .build();
                }
            }
        }
        if (requestCode == PageConstant.REQ_ADVANTAGE_EDIT) {
            if (Activity.RESULT_OK == resultCode) {
                Bundle bundle = new Bundle();
                bundle.putInt(Constants.KEY_DAYS, mViewModel.days);
                bundle.putBoolean(Constants.KEY_CERT_NAME, mViewModel.certName);
                bundle.putSerializable(Constants.KEY_EXTRA_MAP, mViewModel.extraMapBean);
                bundle.putInt(Constants.KEY_STATUS, mViewModel.status);
                bundle.putInt(Constants.KEY_GUIDE_PROGRESS_TYPE, mViewModel.guideProcessType);
                bundle.putBoolean(Constants.KEY_IS_ADVANTAGE_CALL_BACK, true);
                PageRouter.openGeekOldUserActiveAgainPageForResult(this, bundle, PageConstant.REQ_JOB_EXPECT_FRAGMENT);
            }
        } else if (requestCode == PageConstant.REQ_JOB_EXPECT_FRAGMENT) {
            if (Activity.RESULT_OK == resultCode) {
                ArrayList<Integer> supplementList = mViewModel.extraMapBean != null ? mViewModel.extraMapBean.supplementList : null;
                AppUtil.finishActivity(this);
                if (LList.getCount(supplementList) > 0 && mViewModel.isHaveFlowBackCompleteProcess) {/*蓝领个人优势完善流程&&有「回流」完善引导*/
                    GeekBackflowRouter.builder(getThis(), mViewModel.certName, mViewModel.status, mViewModel.flowBackPageCodeList)
                            .build()
                            .jump();
                }
            }
        }
    }


    /**
     * 是否有「回流」完善引导
     *
     * @return
     */
    private boolean isHaveFlowBackCompleteProcess() {
        ArrayList<Integer> supplementList = mViewModel.extraMapBean != null ? mViewModel.extraMapBean.supplementList : null;
        return GuideProcessJumpManager.isHaveFlowBackCompleteProcess(supplementList);
    }

    /**
     * 获取「回流」完善引导的页面code
     *
     * @return
     */
    private ArrayList<Integer> getFlowBackPageCodeList() {
        ArrayList<Integer> supplementList = mViewModel.extraMapBean != null ? mViewModel.extraMapBean.supplementList : null;
        return GuideProcessJumpManager.getFlowBackPageCodeList(supplementList);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

}
