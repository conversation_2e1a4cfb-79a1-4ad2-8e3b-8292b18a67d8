package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LText;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class GeekReportPositionView extends ConstraintLayout {

    private ZPUIRoundButton btnGo;
    private TextView tvTitle;

    public GeekReportPositionView(Context context) {
        this(context, null);
    }

    public GeekReportPositionView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public GeekReportPositionView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public void init(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.geek_report_position_view, this);
        tvTitle = view.findViewById(R.id.tv_title);
        btnGo = view.findViewById(R.id.btn_go);
    }

    private OnClickListener mListener;

    public void setListener(OnClickListener listener) {
        this.mListener = listener;
    }

    public void show(String word) {
        if (LText.empty(word)) {
            setVisibility(View.GONE);
            return;
        }
        setVisibility(View.VISIBLE);
        btnGo.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View view) {
                if (mListener == null) return;
                mListener.onClick(view);
            }
        });
        String query = "【" + word + "】";
        String result = String.format(Utils.getApp().getString(R.string.geek_job_report_position_desc), query);
        tvTitle.setText(lighton(result, query));
    }

    public void dismiss() {
        setVisibility(View.GONE);
    }

    private SpannableString lighton(String content, String highlightword) {
        SpannableString ssb = new SpannableString(content);
        int begin = content.indexOf(highlightword);
        int end = begin + highlightword.length();
        ssb.setSpan(new ForegroundColorSpan(Color.parseColor("#FF0D9EA3")), begin, end, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        return ssb;
    }
}
