package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.utils.NightUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

public class GeekSimpleWelcomeActivity extends BaseActivity2 {
    private String jumpUrl;

    public static void startActivity(Context context, boolean certName, String jumpUrl) {
        Intent intent = new Intent(context, GeekSimpleWelcomeActivity.class);
        intent.putExtra(Constants.KEY_CERT_NAME, certName);
        intent.putExtra(Constants.KEY_JUMP_URL, jumpUrl);
        AppUtil.startActivity(context, intent);
    }

    private boolean certName;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        if (intent == null) return;
        certName = intent.getBooleanExtra(Constants.KEY_CERT_NAME, false);
        jumpUrl = intent.getStringExtra(Constants.KEY_JUMP_URL);

        setContentView(R.layout.activity_geek_simple_welcome);
        initViews();

        AnalyticsFactory.create()
                .action("lifecycle-complete-uncomp-landing")
                .build();
    }

    private void initViews() {
        ImmersiveUtils.immersiveStyleBar(this, true, NightUtil.isDarkMode(this));
        AppTitleView titleView = findViewById(R.id.title_view);
        ConstraintLayout cl_content = findViewById(R.id.cl_content);
        titleView.setDividerInvisible();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(this, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(this);
        titleView.setPadding(0, statusBarHeight, 0, 0);


        SimpleDraweeView ivAvatar = findViewById(R.id.iv_avatar);
        MTextView tvName = findViewById(R.id.tv_name);

        UserBean user = UserManager.getLoginUser();
        if (user != null) {
            ivAvatar.setImageURI(user.avatar);
            if (certName) {
                tvName.setText("Hi，" + user.name);
            } else {
                tvName.setText("Hi");
            }
        }
        findViewById(R.id.iv_next_step).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (!TextUtils.isEmpty(jumpUrl)) {
                    new ZPManager(GeekSimpleWelcomeActivity.this, jumpUrl).handler();
                }
                finish();

                AnalyticsFactory.create()
                        .action("lifecycle-complete-uncomp-enter")
                        .build();
            }
        });
        /*这段代码是为了解决，登录之后进入到这个页面，界面上面莫名其妙的蒙层*/
        View view_line_2 = findViewById(R.id.view_line_2);
        view_line_2.requestFocus();
        view_line_2.setFocusable(true);
        view_line_2.setFocusableInTouchMode(true);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
    }
}
