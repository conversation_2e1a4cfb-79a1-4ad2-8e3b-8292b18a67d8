package com.hpbr.bosszhipin.module.common;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.interfaces.IOnMatchWordClickListener;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.utils.TextSearchLimitHelper;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.SearchMatchListView;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.QueryMatchListRequest;
import net.bosszhipin.api.QueryMatchListResponse;
import net.bosszhipin.api.bean.ServerHlShotDescBean;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * 作者:郭峰
 * 日期:2016/2/3
 * 该页面服务于单行输入显示匹配列表
 * 该 页面必须传入的几个字段,搜索文字的长度,String数组0代表搜索匹配查询的URL,1代表标题,2代表历史填写记录
 */

public class InputMatchListActivity extends BaseActivity implements IOnMatchWordClickListener, TextSearchLimitHelper.TextChangeDelayCallBack {

    public static final String INPUT_TEXT = Constants.PREFIX + ".INPUT_TEXT";
    public static final String INPUT_CODE = Constants.PREFIX + ".INPUT_CODE";

    public static final String LAYOUT_TYPE = Constants.PREFIX + ".LAYOUT_TYPE";
    public static final String FROM = Constants.PREFIX + ".FROM";
    public static final String INPUT_MAX_LENGTH = Constants.PREFIX + ".INPUT_MAX_LENGTH";
    public static final String INPUT_MIN_LENGTH = Constants.PREFIX + ".INPUT_MIN_LENGTH";
    public static final String INPUT_ENABLE_EMPTY = Constants.PREFIX + ".INPUT_ENABLE_EMPTY";

    // 匹配列表
    private SearchMatchListView lvSearchList;
    // 搜索
    private MEditText etInput;
    // 显示搜索数量
    private MTextView tvInputCount;
    // 关于搜索结果为空的提示
    private MTextView tvTip;
    // 搜索的URL
    private String searchUrl;
    // 标题
    private String title;
    // 输入框默认显示的文字
    private String preSearchStr;
    // 输入框搜索数量限制
    private int inputMaxLength;
    private int inputMinLength;
    // 是否支持返回空
    private boolean isEnableEmpty;

    /**
     * 页面的业务类型
     * 1 - 学校
     * 2 - 专业
     */
    private int layoutType;
    public static final int TYPE_SCHOOL = 1;
    public static final int TYPE_MAJOR = 2;

    /**
     * 用于记录进入该页面的来源
     * 0 - 注册 | 1 - 注册后
     */
    private int from;

    // 是否已经编辑
    private boolean isEdit = false;

    private TextSearchLimitHelper searchLimitHelper;

    private InputUtils inputUtils;

    /**
     * 获取前一个页面传来的数据
     */
    private void getIntentData() {
        Intent intent = getIntent();
        //搜索的数量限制
        inputMaxLength = intent.getIntExtra(INPUT_MAX_LENGTH, 5000);
        inputMinLength = intent.getIntExtra(INPUT_MIN_LENGTH, 0);
        isEnableEmpty = intent.getBooleanExtra(INPUT_ENABLE_EMPTY, false);
        String strArray[] = intent.getStringArrayExtra(Constants.DATA_STRING);
        from = intent.getIntExtra(FROM, 0);
        layoutType = intent.getIntExtra(LAYOUT_TYPE, 0);
        if (strArray == null || strArray.length < 3) {
            T.ss("数据异常");
            AppUtil.finishActivity(this);
            return;
        }
        //匹配搜索的URL
        searchUrl = strArray[0];
        //标题
        title = strArray[1];
        //输入框默认显示的文字
        preSearchStr = strArray[2];

        inputUtils = new InputUtils(this, inputMinLength, inputMaxLength);
    }


    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_input_match_list);
        searchLimitHelper = new TextSearchLimitHelper(this);
        getIntentData();
        initView();
        AppUtil.showSoftInput(this, etInput);
    }

    private void initView() {
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setTitle(title);
        titleView.setBackClickListener(R.mipmap.ic_action_close_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                hideSoftKeyboard();
                AppUtil.finishActivity(InputMatchListActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
        titleView.setAction1ClickListener(R.mipmap.ic_action_commit_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onFinishActivity();
            }
        });

        lvSearchList = findViewById(R.id.lv_search);
        lvSearchList.setOnMatchWordClickListener(this);
        lvSearchList.setTitle(title);
        lvSearchList.refreshAdapter();
        etInput = findViewById(R.id.et_input);
        tvTip = findViewById(R.id.tv_tip);
        tvInputCount = findViewById(R.id.tv_input_count);
        inputUtils.checkInputCount(tvInputCount, preSearchStr);
        if (!LText.empty(preSearchStr)) {
            etInput.setText(preSearchStr);
            etInput.setSelection(preSearchStr.length());
        }
        //防止第一次进来显示匹配列表
        etInput.addTextChangedListener(watcher);
    }

    /**
     * 监控输入文字
     */
    TextWatcher watcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            isEdit = true;
        }

        @Override
        public void afterTextChanged(Editable s) {
            String str = s == null ? "" : s.toString().trim();
            inputUtils.checkInputCount(tvInputCount, str);
            searchLimitHelper.sendHandler(str);
        }
    };

    @Override
    public void onTextDelayChanged(String text) {
        if (TextUtils.isEmpty(text)) {
            lvSearchList.setVisibility(View.GONE);
            lvSearchList.setData(null);
            showTip(lvSearchList.refreshAdapter());
        } else {
            lvSearchList.setVisibility(View.VISIBLE);
            lvSearchList.setUserInput(text);

            if (!inputUtils.isInputLargerThanMaxLength(text) && !inputUtils.isInputLessThanMinLength(text)) {
                getMatchListData(text);
            }
        }
    }

    /**
     * 展示提示
     *
     * @param tip
     */
    private void showTip(String tip) {
        if (TextUtils.isEmpty(tip) || !isEdit) {
            tvTip.setVisibility(View.GONE);
            lvSearchList.setVisibility(View.VISIBLE);
        } else {
            tvTip.setVisibility(View.VISIBLE);
            tvTip.setText(tip);
            lvSearchList.setVisibility(View.GONE);
        }
    }

    @Override
    public void onMatchWordClick(ServerHlShotDescBean item, int position) {
        if (item != null && !TextUtils.isEmpty(item.name)) {
            hideSoftKeyboard();
            actionSaveSchool(1, item.name);
            Intent intent = getIntent();
            intent.putExtra(INPUT_TEXT, item.name);
            intent.putExtra(INPUT_CODE, item.schoolId);
            setResult(RESULT_OK, intent);
            AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
        }
    }

    /**
     * 获得搜索匹配列表数据
     *
     * @param strSearch 搜索关键字
     */
    private void getMatchListData(String strSearch) {
        QueryMatchListRequest request = new QueryMatchListRequest(searchUrl, new ApiRequestCallback<QueryMatchListResponse>() {
            @Override
            public void onSuccess(ApiData<QueryMatchListResponse> data) {
                QueryMatchListResponse resp = data.resp;
                if (resp != null) {
                    lvSearchList.setData(resp.itemList);
                    showTip(lvSearchList.refreshAdapter());
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                L.d(reason.getErrReason());
            }
        });
        request.query = strSearch;
        HttpExecutor.execute(request);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 点击【返回】按钮，判断键盘弹出状态
     * 若键盘未弹出，则直接finish();
     * 若键盘弹出，则先关闭键盘，然后finish();
     */
    private void onFinishActivity() {
        String inputStr = etInput.getText().toString().trim();
        if (inputUtils.isInputLessThanMinLength(inputStr)) {
            AnimUtil.errorInputAnim(etInput, getString(R.string.string_input_minimum_length_notify, inputMinLength));
            return;
        }
        if (inputUtils.isInputLargerThanMaxLength(inputStr)) {
            AnimUtil.errorInputAnim(etInput, "超过字数限制");
            return;
        }
        actionSaveSchool(0, inputStr);
        hideSoftKeyboard();
        Intent intent = getIntent();
        intent.putExtra(INPUT_TEXT, inputStr);
        setResult(RESULT_OK, intent);
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }


    /**
     * 点击其他区域隐藏软键盘
     */
    private void hideSoftKeyboard() {
        // 隐藏软键盘
        AppUtil.hideSoftInput(this, etInput);
        // EditText 失去焦点
        etInput.setCursorVisible(true);
    }


    private void actionSaveSchool(int type, String content) {
        if (layoutType == TYPE_SCHOOL) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_SAVE_EDU_SCHOOL)
                    .param("p", String.valueOf(type))
                    .param("p2", String.valueOf(from))
                    .param("p3", content)
                    .build();
        }
    }
}
