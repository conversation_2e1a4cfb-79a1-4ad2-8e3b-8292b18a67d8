package com.hpbr.bosszhipin.module.common;

import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.ColorFilter;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.core.widget.ImageViewCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.airbnb.lottie.LottieProperty;
import com.airbnb.lottie.SimpleColorFilter;
import com.airbnb.lottie.model.KeyPath;
import com.airbnb.lottie.value.LottieValueCallback;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.HostConfig;
import com.hpbr.bosszhipin.module.webview.DownLoadUtil;
import com.hpbr.bosszhipin.report.FeedbackReport;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.utils.screenshot.DrawViewToBitmap;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.hpbr.utils.platform.Utils;
import com.techwolf.lib.tlog.TLog;
import com.twl.net.NetTestService;
import com.twl.ui.ToastUtils;
import com.twl.ui.buttonlayout.ZPUIDynamicButtonPanelLayout;

import java.util.ArrayList;
import java.util.List;

public class NetTestActivity extends BaseActivity2 {
    private ZPUIDynamicButtonPanelLayout buttonPanelLayout;
    private TextView tv_result;
    private LottieAnimationView lottie_net_test;
    private ImageView img_circle, img_k;
    private Animation anim;
    private LottieValueCallback<ColorFilter> colorFilterLottieValueCallback;
    private String host;
    private int port = 80;
    private NetTestService netTestService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        host = getIntent().getStringExtra(Constants.DATA_STRING);
        port = getIntent().getIntExtra(Constants.DATA_INT, 80);
        if (TextUtils.isEmpty(host)) {
            host = HostConfig.CONFIG.getApiAddr();
        }
        setContentView(R.layout.activity_net_test_layout);
        AppTitleView appTitleView = findViewById(R.id.appTitleView);
        appTitleView.setBackClickListener();
        img_circle = findViewById(R.id.img_circle);
        img_k = findViewById(R.id.img_k);
        lottie_net_test = findViewById(R.id.lottie_net_test);
        buttonPanelLayout = findViewById(R.id.btn_panel_layout);
        tv_result = findViewById(R.id.tv_result);
        anim = AnimationUtils.loadAnimation(this, R.anim.ic_net_test_view);
        anim.setRepeatCount(Animation.INFINITE);
        anim.setDuration(3000);
        colorFilterLottieValueCallback = new LottieValueCallback<>(new SimpleColorFilter(0xFF15B3B3));
        lottie_net_test.addValueCallback(keyPath, LottieProperty.COLOR_FILTER, colorFilterLottieValueCallback);
        createCheckButtons();
        netTestService = new NetTestService(NetTestActivity.this, BuildInfoUtils.getVersionCode() + "", getString(R.string.app_name), BuildInfoUtils.getVersionName());

        //第一次主动弹窗
        if (SpManager.get().global().getBoolean("dialog_tip_newtest",true)) {
            showDialog();
            SpManager.get().global().edit().putBoolean("dialog_tip_newtest",false).apply();
        }
    }


    private String net_test_log;
    private volatile int status;
    private final KeyPath keyPath = new KeyPath("**");

    private void startNetTest() {
        status = 0;
        netTestService.setListen(new NetTestService.Listen() {
            @Override
            public void onNetTestUpdate(String log) {
                TLog.error("net-test", log);
            }

            @Override
            public void onNetTestStatus(int status) {
                NetTestActivity.this.status = status;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (status < 0) {
                            lottie_net_test.setRepeatCount(0);
                            ImageViewCompat.setImageTintList(img_k, ColorStateList.valueOf(0xFFFA6400));
                            colorFilterLottieValueCallback.setValue(new SimpleColorFilter(0xFFFA6400));
                            lottie_net_test.playAnimation();
                            img_circle.setImageResource(R.drawable.bg_net_test_error_circle);
                            tv_result.setText("网络检测失败");
                            tv_result.setVisibility(View.VISIBLE);
                            tv_result.setTextColor(ContextCompat.getColor(NetTestActivity.this, R.color.color_FFFA6400));
                            resetCheckButtons();
                        } else if (status > 4) {
                            createComplete();
                            tv_result.setText("网络检测完成");
                            tv_result.setVisibility(View.VISIBLE);
                            tv_result.setTextColor(ContextCompat.getColor(NetTestActivity.this, R.color.app_green));
                            lottie_net_test.setRepeatCount(1);
                        }
                    }
                });
            }

            @Override
            public void onNetTestFinished(String log) {
                anim.cancel();
            }
        });
        ImageViewCompat.setImageTintList(img_k, ColorStateList.valueOf(0xFF15B3B3));
        img_circle.setImageResource(R.drawable.bg_net_test_circle);
        netTestService.start(host, port);
        tv_result.setVisibility(View.GONE);
        lottie_net_test.setRepeatCount(LottieDrawable.INFINITE);
        colorFilterLottieValueCallback.setValue(new SimpleColorFilter(0xFF15B3B3));
        lottie_net_test.playAnimation();
        img_circle.startAnimation(anim);
    }


    /**
     * 开始检测
     */
    private void createCheckButtons() {
        List<ZPUIDynamicButtonPanelLayout.Button> buttonList = new ArrayList<>();
        ZPUIDynamicButtonPanelLayout.Button buttonCheck = ZPUIDynamicButtonPanelLayout.Button.create("开始检测");
        buttonCheck.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startNetTest();
                buttonCheck.buttonText = "检测中...";
                buttonCheck.isDisabled = true;
                buttonPanelLayout.build();
            }
        };
        buttonList.add(buttonCheck);
        buttonPanelLayout.refreshButtons(buttonList);
        buttonPanelLayout.build();
    }

    /**
     * 重新检测
     */
    private void resetCheckButtons() {
        List<ZPUIDynamicButtonPanelLayout.Button> buttonList = new ArrayList<>();
        ZPUIDynamicButtonPanelLayout.Button buttonCheck = ZPUIDynamicButtonPanelLayout.Button.create("重新检测");
        buttonCheck.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startNetTest();
                buttonCheck.buttonText = "检测中...";
                buttonCheck.isDisabled = true;
                buttonPanelLayout.build();
            }
        };
        buttonCheck.buttonWeight = 1;

        buttonList.add(buttonCheck);
        buttonPanelLayout.refreshButtons(buttonList);
        buttonPanelLayout.build();
    }

    /**
     * 反馈给BOSS直聘 ,获取检测报告
     */
    private void createComplete() {
        List<ZPUIDynamicButtonPanelLayout.Button> buttonList = new ArrayList<>();
        ZPUIDynamicButtonPanelLayout.Button buttonClear = ZPUIDynamicButtonPanelLayout.Button.create("反馈给BOSS直聘");
        buttonClear.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                new FeedbackReport().report();
                ToastUtils.showText("反馈成功");
                finish();
            }
        };
        buttonClear.buttonWeight = 1;

        ZPUIDynamicButtonPanelLayout.Button buttonOpen = ZPUIDynamicButtonPanelLayout.Button.create("获取检测报告");
        buttonOpen.buttonWeight = 1;
        buttonOpen.key = "geek_call";
        buttonOpen.listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final View view = LayoutInflater.from(NetTestActivity.this).inflate(R.layout.activity_net_test_result_layout, null);
                TextView textView = view.findViewById(R.id.tv_result);
                textView.setText(net_test_log);
                int w = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                int h = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                view.measure(w, h);
                view.layout(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());

                AppThreadFactory.POOL.execute(() -> {
                    DrawViewToBitmap drawViewToBitmap = new DrawViewToBitmap(view.findViewById(R.id.content));
                    Bitmap viewBitmap = drawViewToBitmap.getViewBitmap();
                    DownLoadUtil.saveBitmapToLocalImage("", viewBitmap, Utils.getApp(), true);
                    runOnUiThread(() -> finish());
                });
            }
        };

        buttonList.add(buttonClear);
//        buttonList.add(buttonOpen);
        buttonPanelLayout.refreshButtons(buttonList);
        buttonPanelLayout.build();
    }

    private void showDialog() {
        new DialogUtils
                .Builder(NetTestActivity.this)
                .setDesc("亲爱的用户，为了帮助您诊断网络异常，我们可能会收集您的设备信息、IP地址等网络信息，我们会按照相关规定保护好您的信息，请您放心使用。详见《BOSS直聘用户隐私政策》")
                .setDoubleButton()
                .setPositiveAction("知道了")
                .setNegativeAction("取消", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        finish();
                    }
                })
                .setCancelable(false)
                .build()
                .show();
    }

    public void onShow(View view) {
        showDialog();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (netTestService!=null) {
            netTestService.stop();
        }
    }
}
