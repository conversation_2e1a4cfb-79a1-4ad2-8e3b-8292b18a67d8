package com.hpbr.bosszhipin.module.common;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.fragment.OtherJobSelectWorkFragment;
import com.hpbr.bosszhipin.module.common.viewmodel.OtherJobGuideCompleteViewModel;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectPageRouter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.sankuai.waimai.router.annotation.RouterUri;

import net.bosszhipin.api.bean.ExtraMapBean;

import java.util.ArrayList;

/**
 * @ClassName ：OtherJobGuideCompleteActivity
 * @Description ：1106.7「其他」期望职位 引导完善
 * <AUTHOR> SheYi
 * @Date ：2023/3/29  4:31 PM
 */
@RouterUri(path = PageConstant.PATH_OTHER_JOB_GUIDE_COMPLETE)
public class OtherJobGuideCompleteActivity extends BaseAwareActivity<OtherJobGuideCompleteViewModel> {

    private int status;

    @Override
    protected int contentLayout() {
        return R.layout.activity_other_job_guide_complete;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        initParams();
        initView();
        initFragment();
        initData();
    }

    private void initView() {
        /*这段代码是为了解决，登录之后进入到这个页面，界面上面莫名其妙的蒙层*/
        View view_line_2 = findViewById(R.id.view_line_2);
        view_line_2.requestFocus();
        view_line_2.setFocusable(true);
        view_line_2.setFocusableInTouchMode(true);
    }

    private void initParams() {
        Intent intent = getIntent();
        if (intent == null) return;
        mViewModel.extraMapBean = (ExtraMapBean) intent.getSerializableExtra(Constants.KEY_EXTRA_MAP);
        status = intent.getIntExtra(Constants.KEY_STATUS, 0);
        if (mViewModel.extraMapBean == null) {
            AppUtil.finishActivity(this);
            return;
        }
        mViewModel.jobIntent.parseFromServer(mViewModel.extraMapBean.expectInfo);
    }

    private void initFragment() {
        Bundle bundle = new Bundle();
        OtherJobSelectWorkFragment otherJobSelectWorkFragment = OtherJobSelectWorkFragment.getInstance(bundle);
        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fl_container, otherJobSelectWorkFragment)
                .commitAllowingStateLoss();
    }

    private void initData() {
        mViewModel.completeAction(status);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null) {
            switch (requestCode) {
                case GeekExpectConstants.REQ_OTHER_JOB_SELECT:
                    long reportPositionId = data.getLongExtra(ThreeLevelPositionPickForMultiExpectActivity.REPORTED_POSITION_ID, 0);
                    mViewModel.jobIntent.nlpSuggestPosition = data.getStringExtra(ThreeLevelPositionPickForMultiExpectActivity.SELECTED_NLP_SUGGEST_POSITION);
                    LevelBean thirdItem = (LevelBean) data.getSerializableExtra(ThreeLevelPositionPickForMultiExpectActivity.SELECTED_THIRD_POSITION_ITEM);
                    ArrayList<LevelBean> multiPositions = (ArrayList<LevelBean>) data.getSerializableExtra(ThreeLevelPositionPickForMultiExpectActivity.SELECTED_MULTI_POSITION_ITEM);
                    mViewModel.resetPositions();

                    ArrayList<LevelBean> selectedLevelBeanList = new ArrayList<>();
                    if (LList.getCount(multiPositions) > 0) {// 多期望
                        mViewModel.resetMultiPositionClassValue(multiPositions);
                        LList.addAllElement(selectedLevelBeanList, multiPositions);
                    } else if (thirdItem != null) { //单期望
                        mViewModel.resetPositionClassValue(thirdItem);
                        LList.addElement(selectedLevelBeanList, thirdItem);
                    }

                    GeekExpectPageRouter.Create.jump(this,
                            GeekExpectPageRouter.Create.RequestParams.obj()
                                    .setRequestCode(GeekExpectConstants.REQ_OTHER_JOB_SELECT)
                                    .setMultiPositions(selectedLevelBeanList)
                                    .setPositionClassIndexString(mViewModel.jobIntent.positionClassIndexString)
                                    .setReportPositionId(reportPositionId)
                                    .setJobIntentBean(mViewModel.jobIntent));
                    AppUtil.finishActivity(this);

                    AppAnalysisUtil.dotOtherBackUserExpectGuide(5, StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, selectedLevelBeanList, (StringUtil.IValueFunction<LevelBean>) levelBean -> String.valueOf(levelBean.code)));/*埋点*/
                    break;
                default:
                    break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
    }
}
