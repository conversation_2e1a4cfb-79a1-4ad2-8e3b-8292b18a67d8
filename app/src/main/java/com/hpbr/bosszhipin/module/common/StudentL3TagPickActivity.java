package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.onlineresume.view.LevelBeanGroupItemView;
import com.hpbr.bosszhipin.module.onlineresume.view.TagSelectCallback;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: fanfan
 * 1115.607 学生L3 词条选择页
 */
public class StudentL3TagPickActivity extends BaseActivity2 {


    public static final String SELECT_L3_LIST = "com.hpbr.bosszhipin.SELECT_L3_LIST";
    public static final String SELECT_L2 = "com.hpbr.bosszhipin.SELECT_L2";
    public static final String EXPECT_ID_STU = "com.hpbr.bosszhipin.EXPECT_ID_STU";


    public static final int REQ_POSITION_CLASS_STU_L3 = 0x021;


    private LevelBean selectL2;
    @NonNull
    private ArrayList<LevelBean> originSelectL3List = new ArrayList<>();
    @NonNull
    private ArrayList<LevelBean> handledSelectL3List = new ArrayList<>();


    private MTextView mtvTitle;
    private MTextView mtvEmpty;
    private MTextView mtvDesc;
    private MTextView tvSelectCount;
    private LevelBeanGroupItemView sectionItemView;
    private ZPUIRoundButton btnConfirm;
    private int maxCount;
    private int selectCount;
    private String l2Name;
    private long expectId;

    /**
     * 职位选择回调
     *
     * @ firstLevel L1职类
     * @ secondLevel L2职类
     */

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_level_l3_pick_for_stu);
        initIntent();
        initViews();
        intData();
    }

    private void initIntent() {
        Intent intent = getIntent();

        Serializable selectlist = intent.getSerializableExtra(SELECT_L3_LIST);
        if (selectlist instanceof ArrayList) {
            originSelectL3List = (ArrayList<LevelBean>) selectlist;

        }

        Serializable itemL2 = intent.getSerializableExtra(SELECT_L2);

        if (itemL2 instanceof LevelBean) {
            selectL2 = (LevelBean) itemL2;
        }


        expectId = intent.getLongExtra(EXPECT_ID_STU, 0);


    }

    private void initViews() {

        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setBackClickListener();
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(this, R.color.app_green_dark));


        mtvTitle = findViewById(R.id.tv_page_title);
        mtvDesc = findViewById(R.id.tv_desc);
        tvSelectCount = findViewById(R.id.tv_select_count);
        btnConfirm = findViewById(R.id.btn_confirm);
        sectionItemView = findViewById(R.id.gv_content);
        mtvEmpty = findViewById(R.id.tv_empty);

        btnConfirm.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                saveL3Selected();
                mgeSaveAction(); // 埋点
            }
        });


    }


    private void intData() {

        if (selectL2 != null) {

            l2Name = selectL2.name;

            if (!LList.isEmpty(selectL2.subLevelModeList)) {

                if (!LList.isEmpty(originSelectL3List)) {
                    if (selectL2 != null && !LList.isEmpty(selectL2.subLevelModeList)) {
                        for (LevelBean originItem : selectL2.subLevelModeList) {
                            for (LevelBean selectItem : originSelectL3List) {
                                if (selectItem.code == originItem.code) {
                                    originItem.flag = 1;
                                    handledSelectL3List.add(originItem);
                                }
                            }
                        }
                    }
                } else {
                    for (LevelBean originItem : selectL2.subLevelModeList) {
                        originItem.flag = 1;
                        handledSelectL3List.add(originItem);
                    }
                }

                maxCount = LList.getCount(selectL2.subLevelModeList);
                selectCount = LList.getCount(handledSelectL3List);
            }
        }

        flushViews();
    }

    private void flushViews() {
        mtvDesc.setText(String.format(Locale.getDefault(), "%s现有%d个类别", l2Name, maxCount));
        btnConfirm.setEnabled(!LList.isEmpty(handledSelectL3List));
        sectionItemView.setTagSelectCallback(new TagSelectCallback() {
            @Override
            public boolean onAdd(@NonNull LevelBean bean) {
                boolean result = handledSelectL3List.add(bean);

                if (LList.getCount(handledSelectL3List) > 0) {
                    btnConfirm.setEnabled(true);
                }

                computeCount();
                return result;
            }

            @Override
            public boolean onRemove(@NonNull LevelBean bean) {

                boolean result = handledSelectL3List.remove(bean);
                if (result && LList.getCount(handledSelectL3List) == 0) {
                    btnConfirm.setEnabled(false);
                }

                computeCount();
                return result;
            }


        });

        if (selectL2 != null && !LList.isEmpty(selectL2.subLevelModeList)) {
            sectionItemView.setDataList(selectL2.subLevelModeList);
            mtvEmpty.setVisibility(View.GONE);
        } else {
            mtvEmpty.setVisibility(View.VISIBLE);
        }

        tvSelectCount.setText(getSelectItemCount());
    }

    private void computeCount() {
        selectCount = LList.getCount(handledSelectL3List);
        tvSelectCount.setText(getSelectItemCount());
    }

    private SpannableStringBuilder getSelectItemCount() {
        String selectCountString = selectCount + "/" + maxCount;
        String highlightString = String.valueOf(selectCount);
        SpannableStringBuilder builder = new SpannableStringBuilder(selectCountString);
        builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_BOSS7)), 0, highlightString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        return builder;
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

    }


    private void saveL3Selected() {

        if (LList.isEmpty(handledSelectL3List)) {
            ToastUtils.showText("请选择一个职位");
        } else {
            Intent intent = getIntent();
            intent.putExtra(SELECT_L3_LIST, handledSelectL3List);
            setResult(RESULT_OK, intent);
            AppUtil.finishActivity(this);

        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    /**
     *
     */
    public static void jumpForResult(@NonNull Context context, @NonNull ArrayList
            selectL3List, LevelBean l2Item, long expectId) {
        Intent intent = createIntent(context, selectL3List, l2Item, expectId);
        AppUtil.startActivityForResult(context, intent, REQ_POSITION_CLASS_STU_L3);
    }

    @NonNull
    public static Intent createIntent(@NonNull Context context, @NonNull ArrayList
            selectL3List, LevelBean l2Item, long expectId) {
        Intent intent = new Intent(context, StudentL3TagPickActivity.class);
        // 统一从对象中获取参数！
        //region 废弃，统一从 KEY_MULTI_EXPECT_PARAMS 获取 MultiExpectParams 对象
        intent.putExtra(SELECT_L3_LIST, selectL3List);
        intent.putExtra(SELECT_L2, l2Item);
        intent.putExtra(EXPECT_ID_STU, expectId);
        //endregion

        return intent;
    }


    public void mgeSaveAction() {
        AnalyticsFactory.create().action("stu-code-relate-select")
                .param("p", expectId)
                .param("p2", selectL2 == null ? 0 : selectL2.code)
                .param("p3", getCodeString(handledSelectL3List))
                .param("p4", getCodeString(selectL2 == null ? null : selectL2.subLevelModeList))
                .build();
    }


    public String getCodeString(List<LevelBean> beanList) {
        String result = "";

        if (!LList.isEmpty(beanList)) {
            for (LevelBean item : beanList) {
                result = StringUtil.connectTextWithChar(",", result, String.valueOf(item.code));
            }
        }

        return result;
    }
}