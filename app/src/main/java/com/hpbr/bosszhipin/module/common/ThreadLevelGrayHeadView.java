package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;

/**
 * Created by guofeng
 * on 2018/10/20.
 */

public class ThreadLevelGrayHeadView extends LinearLayout implements ObtainSelectCallback {

    private FlexboxLayout mTagContainers;

    private MTextView mCount;

    private MTextView mHint;

    public static final int MAX_COUNT = 3;

    private final ArrayList<ItemTag> CACHE = new ArrayList<>(MAX_COUNT);


    public ThreadLevelGrayHeadView(Context context) {
        super(context);
        init(context);
    }


    public ThreadLevelGrayHeadView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public ThreadLevelGrayHeadView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    /**
     * TAG间距
     */
    private int margin = Scale.dip2px(App.getAppContext(), 8);

    private void init(Context context) {
        View headView = LayoutInflater.from(context).inflate(R.layout.view_three_level_head, null);
        mTagContainers = headView.findViewById(R.id.labelsLayout);
        mCount = headView.findViewById(R.id.tv_count);
        mHint = headView.findViewById(R.id.tv_hint);
        addView(headView, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
    }

    public boolean containKey(String key) {
        for (ItemTag itemTag : CACHE) {
            if (itemTag.hashKey.equals(key)) {
                return true;
            }
        }
        return false;
    }

    public void deleteAndRefresh( LevelBean third, long reportId) {
        String key = createKey(third, reportId);
        for (ItemTag itemTag : CACHE) {
            if (itemTag.hashKey.equals(key)) {
                CACHE.remove(itemTag);
                break;
            }
        }

        refreshChild();

        refreshCount();
    }

    /**
     * 添加一条数据
     */
    public void addData(LevelBean first, LevelBean second, LevelBean third, long reportId) {
        if (CACHE.size() == MAX_COUNT) return;
        String key = createKey(third, reportId);
        if (containKey(key)) return;


        CACHE.add(0, new ItemTag(first, second, third, reportId));


        refreshChild();

        refreshCount();
    }

    private void refreshChild() {

        mTagContainers.removeAllViews();

        for (ItemTag tag : CACHE) {
            View child = LayoutInflater.from(getContext()).inflate(R.layout.view_level_tag, null);
            child.setTag(tag);

            MTextView tvText = child.findViewById(R.id.tv_text);
            tvText.setText(tag.getThird().name);

            LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            params.rightMargin = margin;
            params.bottomMargin = margin;
            mTagContainers.addView(child, params);
            child.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {

                    ItemTag itemTag = (ItemTag) child.getTag();
                    if (itemTag != null) {
                        deleteAndRefresh( itemTag.getThird(), itemTag.getReportId());
                    }

                    if (onDeleteCallback != null) {
                        onDeleteCallback.onDeleteListener();
                    }
                }
            });
        }
    }

    /**
     * 刷新Tag数量
     */
    private void refreshCount() {
        mCount.setText(CACHE.size() + "/" + MAX_COUNT);
        if (CACHE.isEmpty()) {
            mHint.setVisibility(VISIBLE);
        } else {
            mHint.setVisibility(GONE);
        }
    }

    public boolean isFull() {
        return CACHE.size() == MAX_COUNT;
    }

    @Override
    public boolean isContainThirdLevel(long code, String name) {
        for (ItemTag itemTag : CACHE) {
            LevelBean third = itemTag.getThird();
            if (third == null) continue;
            if (third.code == code && third.name.equals(name)) return true;
        }
        return false;
    }


    public ArrayList<ItemTag> getSelectIntent() {
        return CACHE;
    }

    public static class ItemTag extends BaseEntity {

        private LevelBean first;
        private LevelBean second;
        private LevelBean third;
        private long reportId;

        private String hashKey;

        public ItemTag(LevelBean first, LevelBean second, LevelBean third, long reportId) {
            this.first = first;
            this.second = second;
            this.third = third;
            this.reportId = reportId;
            hashKey = createKey(third, reportId);
        }

        public LevelBean getFirst() {
            return first;
        }

        public LevelBean getSecond() {
            return second;
        }

        public LevelBean getThird() {
            return third;
        }

        public long getReportId() {
            return reportId;
        }
    }

    public static ItemTag getDefaultItemTag(long code, String name) {
        LevelBean first = new LevelBean();
        LevelBean second = new LevelBean();
        LevelBean third = new LevelBean(code, name);
        return new ItemTag(first, second, third, 0);
    }

    public static String createKey(LevelBean third, long reportId) {
        return third.code + "_" + reportId;
    }

    private OnDeleteCallback onDeleteCallback;

    public void setOnDeleteCallback(OnDeleteCallback onDeleteCallback) {
        this.onDeleteCallback = onDeleteCallback;
    }

    public interface OnDeleteCallback {
        void onDeleteListener();
    }

}
