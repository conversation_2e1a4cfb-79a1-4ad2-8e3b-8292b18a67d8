package com.hpbr.bosszhipin.module.common;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;

import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.activity.ExpectPositionOtherActivity;
import com.hpbr.bosszhipin.module.my.activity.boss.optration.PositionUtil;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean.Type;

import com.hpbr.bosszhipin.module.register.geek.WorkExpCompletionActivity;
import com.hpbr.bosszhipin.module.register.geek.ab.CompletionUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.JobIntentSearchMatchView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.MatchCallBack;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.OnSuggestCallback;
import com.hpbr.bosszhipin.views.threelevel.JobIntentTreeView;
import com.hpbr.bosszhipin.views.threelevel.OnThreeLevelClickCallBack;
import com.hpbr.bosszhipin.views.tip.TipBar;
import com.hpbr.bosszhipin.views.tip.TipManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.CheckPositionFeatureRequest;
import net.bosszhipin.api.CheckPositionFeatureResponse;
import net.bosszhipin.api.CustomIndustryRequest;
import net.bosszhipin.api.CustomIndustryResponse;
import net.bosszhipin.api.GetAppPositionListRequest;
import net.bosszhipin.api.GetAppPositionListRsponse;
import net.bosszhipin.api.GetCityPositionRequest2;
import net.bosszhipin.api.GetCityPositionResponse2;
import net.bosszhipin.api.ReportCustomPositionRequest;
import net.bosszhipin.api.ReportCustomPositionResponse;
import net.bosszhipin.api.bean.ServerJobSuggestBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * 作者：YaLin
 * 日期：2016/10/13.
 */
public class ThreeLevelPositionPickActivity extends BaseActivity {

    //region KEYS
    public static final String CAN_USE_RE_VISIBLE = "com.hpbr.bosszhipin.CAN_USE_RE_VISIBLE";

    public static final String SELECTED_FIRST_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_FIRST_POSITION_ITEM";
    public static final String SELECTED_SECOND_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_SECOND_POSITION_ITEM";
    public static final String SELECTED_THIRD_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_THIRD_POSITION_ITEM";
    public static final String SELECTED_NLP_SUGGEST_POSITION = "com.hpbr.bosszhipin.SELECTED_NLP_SUGGEST_POSITION";
    public static final String REPORTED_POSITION_ID = "com.hpbr.bosszhipin.REPORTED_POSITION_ID";
    public static final String IS_NLP_RECOMMEND = Constants.PREFIX + ".IS_NLP_RECOMMEND";
    public static final String MATCH_INPUT = Constants.PREFIX + "MATCH_INPUT";
    public static final String OTHER_POSITION_KEYWORD = "其他";
    public static final String KEY_BOSS_EDIT_JOB_CLASS = Constants.PREFIX + ".KEY_BOSS_EDIT_JOB_CLASS";
    public static final String KEY_INPUT_STRING = Constants.PREFIX + ".KEY_INPUT_STRING";
    public static final String SELECTED_MULTI_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_MULTI_POSITION_ITEM";
    public static final String IS_REGISTER = "com.hpbr.bosszhipin.IS_REGISTER";
    public static final String KEY_USER_CITY = "com.hpbr.bosszhipin.KEY_USER_CITY";

    private static final String BUNDLE_USER_EXPECT_LIST = "BUNDLE_USER_EXPECT_LIST";
    private static final String BUNDLE_FROM_WORK_EXP_COMPLETION = "BUNDLE_FROM_WORK_EXP_COMPLETION";
    private static final String BUNDLE_FROM_EDIT_WORK_EXP = "BUNDLE_FROM_EDIT_WORK_EXP";
    private static final String BUNDLE_IS_INTER = "BUNDLE_IS_INTER";
    private static final String BUNDLE_JOB_TYPE = "BUNDLE_JOB_TYPE";
    private static final String BUNDLE_IS_SHOW_RESET = "BUNDLE_IS_SHOW_RESET";
    private static final String BUNDLE_RESET_TYPE = "BUNDLE_RESET_TYPE";
    private static final String KEY_SHOULD_ENTER_MULTI_CHOICE_MODE = "key_should_enter_multi_choice_mode";
    private static final String KEY_IN_PART_TIME_JOB = "KEY_IN_PART_TIME_JOB";
    private static final String KEY_IN_BLUE_JOB = "KEY_IN_BLUE_JOB";
    private static final String KEY_GET_SERVER_DATA = "KEY_GET_SERVER_DATA";
    private static final String KEY_FROM_TOURIST = "KEY_FROM_TOURIST";

    private static final String KEY_EXPECT_PARAMS = "KEY_EXPECT_PARAMS";
    //endregion

    private static final int REQUEST_OTHER_POSITION = 1;

    private static final int MAX_COUNT = 3;

    @NonNull
    private static final LevelBean OTHER = new LevelBean(200101, "其他");

    private ProgressBar pb_loading;
    private JobIntentSearchMatchView mSearchView;
    private TipBar tipBar;
    private AppTitleView titleView;
    private ListView customList;
    private View multi_divider;
    private ConstraintLayout multi_position_layout;
    private LinearLayout selected_position_layout;
    private MTextView confirm_btn;
    private ThreeLevelRecommendLayout mRecommendView;
    private JobIntentTreeView jobIntentTreeView;

    private int maxCount;
    public static boolean isClickSuggest;

    @NonNull
    private final List<LevelBean> mPositionList = new ArrayList<>();
    private static final ArrayList<LevelBean> selectedMultiPositions = new ArrayList<>();
    private final ArrayList<LevelBean> initialSeletedMultiPositions = new ArrayList<>();
    private boolean shouldEnterMultiChoiceMode = false;
    /**
     * 是否实习生
     * 这个逻辑需要优化，这个时候其实已经不再是实习生的意思了
     * 根据页面注册流程驱动 isInter 是否 true or false
     * isInter  true 显示实习生的二级
     * false 显示正常的三级职位
     */
    private boolean isInter;

    /**
     * 是否可以回显，目前用于学生端的选择期望
     */
    private boolean mCanReVisible;
    private LevelBean mFirstLevelBean, mSecondLevelBean;
    private int jobType;
    // 是否显示重置
    private boolean isShowReset;
    // 打点
    private boolean isFromBgAction;
    private LevelBean mTempFirstItem;
    private LevelBean mTempSecondItem;
    private int changeType;//默认0；1-应届、社招 切 实习生；2-实习生 切 应届、社招

    private GeekReportPositionView reportPositionView;
    private MTextView tvSelectCount;
    private AppBarLayout abl;
    private CoordinatorLayout parentCl;
    private LinearLayout topTitleLl;
    @SuppressLint("StaticFieldLeak")
    @NonNull
    private final AsyncTask<Object, Object, Object> mGetPositionTask = new AsyncTask<Object, Object, Object>() {

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            pb_loading.setVisibility(View.VISIBLE);
        }

        @Override
        protected Object doInBackground(Object... params) {
            mPositionList.clear();
            if (isInter()) {
                mPositionList.addAll(VersionAndDatasCommon.getInstance().getInternPositionList());
            } else {
                mPositionList.addAll(VersionAndDatasCommon.getInstance().getPositionList());
                //牛人 注册流程排序
                checkGeekRegisterGraySort();
            }
            return mPositionList;
        }

        @Override
        protected void onPostExecute(Object o) {
            pb_loading.setVisibility(View.GONE);
            if (o == null) {
                ToastUtils.showText(R.string.string_data_error);
                finish();
            } else {
                setData();
            }
        }
    };

    @NonNull
    private final OnThreeLevelClickCallBack threeLevelClickCallBack = new OnThreeLevelClickCallBack() {

        @Override
        public void onFirstLevelItemClick(AdapterView<?> parent, LevelBean firstLevel, int position) {

        }

        @Override
        public void onSecondLevelItemClick(AdapterView<?> parent, LevelBean firstLevel, int firstPosition, LevelBean secondLevel, int secondPosition) {
            if (firstLevel == null || secondLevel == null) {
                return;
            }

            if (isInter() || inPartTimeJob() || isBlueExprience()) {

                //region 兼职多期望
                if (inPartTimeJob() && shouldEnterMultiChoiceMode) { // 存量发兼职，可以一次选择多个职类（816.201）
                    if (maxCount <= 1) {
                        finishActivity(firstLevel, secondLevel, null, "", 0, false);
                        return;
                    }

                    if (isThisPositionChecked(secondLevel)) { // 点击已选中的，则移除该项目
                        onRemoveThisPosition(secondLevel);
                        return;
                    }

                    // 判断是否已选满
                    if (alreadyFull()) {
                        ToastUtils.showText("已达上限");
                        return;
                    }

                    checkThisPosition(secondLevel);

                    // 选中该三级类
                    onCheckThisPositionView(secondLevel);
                    // 进入多选模式
                    enterMultiChoiceMode();

                    return;
                }
                //endregion

                finishActivity(firstLevel, secondLevel, null, "", 0, false);
            }
        }

        @Override
        public void onThirdLevelItemClick(AdapterView<?> parent, LevelBean firstLevel, int firstPosition, LevelBean secondLevel, int secondPosition, LevelBean thirdLevel, int thirdPosition) {
            if (firstLevel == null || secondLevel == null || thirdLevel == null) {
                return;
            }

            if (isThisPositionChecked(thirdLevel)) { // 点击已选中的，则移除该项目
                onRemoveThisPosition(thirdLevel);
                return;
            }

            // 用户选中某个选项

            // 判断是否已选满
            if (alreadyFull()) {
                ToastUtils.showText("已达上限");
                return;
            }

            if (secondLevel.name.contains(OTHER_POSITION_KEYWORD) ||
                    thirdLevel.name.contains(OTHER_POSITION_KEYWORD)) { // 用户选择“其他”，执行上报操作
                reportOtherPosition(firstLevel, secondLevel, thirdLevel);
            } else { // 添加该选中项
                if (UserManager.isGeekRole()) { // 牛人端统一走接口判断，查询是否支持多期望
                    // 点击一个选项，加入到已选列表中，用于后续判断是否可以继续添加
                    checkThisPosition(thirdLevel);
                    CheckPositionFeatureRequest request = new CheckPositionFeatureRequest(new SimpleApiRequestCallback<CheckPositionFeatureResponse>() {
                        @Override
                        public void onStart() {
                            super.onStart();
                            showProgressDialog();
                        }

                        @Override
                        public void onSuccess(ApiData<CheckPositionFeatureResponse> data) {
                            CheckPositionFeatureResponse resp = data.resp;

                            // 判断是否可以继续选
                            boolean canAddMultipleExpect = resp.canAddMultipleExpect;
                            if (shouldEnterMultiChoiceMode && canAddMultipleExpect) {
                                // 选中该三级类
                                onCheckThisPositionView(thirdLevel);
                                // 进入多选模式
                                enterMultiChoiceMode();
                            } else {
                                // 直接直接返回选中的三级类集合
                                finishActivity(firstLevel, secondLevel, thirdLevel, "", 0, false);

                                if (isFromEditWorkExp()) {
                                    AnalyticsFactory.create()
                                            .action(AnalyticsAction.ACTION_USERINFO_MICRORESUME_WORK_ADDJOBTITLE)
                                            .param("p", 1) // 1.职类建议 2.NLP推荐 3.NLP预测 4.手动输入保存
                                            .param("p2", thirdLevel.name) // 职位名称文本
                                            .param("p3", thirdLevel.code) // 职位类型code
                                            // .param("p4", ) // 职位类型来源：1.推荐职位code 2.预测职位code 3.其他-其他（当actionp=4时）
                                            .build();
                                }
                            }
                        }

                        @Override
                        public void onComplete() {
                            super.onComplete();
                            dismissProgressDialog();
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            super.onFailed(reason);
                            uncheckThisPosition(thirdLevel);
                            ToastUtils.showText(reason.getErrReason());
                        }
                    });
                    request.positionCode = concateCheckedPosition();
                    request.type = 1 /* 720蓝领多期望 */;
                    request.execute();
                } else { // Boss端走以前的逻辑
                    finishActivity(firstLevel, secondLevel, thirdLevel, "", 0, false);
                }
            }
        }
    };

    //牛人 注册流程排序
    private void checkGeekRegisterGraySort() {
        //灰度 注册流程 灰度牛人（蓝白都有）职类排序优化：
        //职类排序调整：最近一份工作经历填写的一级类职位放在第一位；
        if (WorkExpCompletionActivity.isUidInGray
                && WorkExpCompletionActivity.positionLv1Code != 0
                && UserManager.isGeekRole()
                && !UserManager.isInfoCompleteGeek(UserManager.getLoginUser())) {

            int index = -1;
            //查找要置顶的数据
            for (int i = 0; i < mPositionList.size(); i++) {
                LevelBean item = LList.getElement(mPositionList, i);
                if (item == null) continue;
                if (item.code == WorkExpCompletionActivity.positionLv1Code) {
                    index = i;
                    break;
                }
            }
            //插入到第一个位置
            if (index != -1) {
                LevelBean item = mPositionList.remove(index);
                mPositionList.add(0, item);
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_three_level_position_pick);
        initIntent();
        initTitle(isShowReset);
        initViews();
        getData();
        reVisibleScroll();
    }

    /**
     * 当 选择了 期望后，进来需要回显。 如果这时候L1 在底部，  需要滚动一下 让它显示出来
     */
    private void reVisibleScroll() {
        if (getBehavior() != null && mSecondLevelBean != null && !TextUtils.isEmpty(mSecondLevelBean.name) && mCanReVisible) {
            CoordinatorLayout.Behavior behavior = getBehavior();
            abl.post(new Runnable() {
                @Override
                public void run() {
                    behavior.onNestedPreScroll(parentCl, abl, topTitleLl, 0, DisplayHelper.dp2px(ThreeLevelPositionPickActivity.this, 1000), new int[]{0, 0}, ViewCompat.TYPE_NON_TOUCH);
                }
            });

        }
    }

    public CoordinatorLayout.Behavior getBehavior() {
        CoordinatorLayout.LayoutParams layoutParams = (CoordinatorLayout.LayoutParams) abl.getLayoutParams();
        return layoutParams.getBehavior();
    }

    private void initTitle(boolean isShowReset) {
        titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(this, R.color.app_green_dark));
        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                onPressBack();
            }
        });
        if (isShowReset) {
            titleView.setActionButtonListener("不限职类", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    LevelBean level = new LevelBean(0, "不限");
                    finishActivity(level, level, level, "", 0, false);
                }
            });
        }

        if (isFromEditWorkExp()) { // 显示『保存』按钮
            titleView.setActionButtonListener("保存", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    // 判断用户输入的职位名称是否合法
                    if (!mSearchView.isValidInput()) {
                        return;
                    }

                    // 用户没选 sug，点击了「保存」，将用户输入的文字保存为「职位名称」，将NLP 预测的最相似的职类保存至「职位类型」（兜底的职位类型是其他-其他）
                    LevelBean thirdItem;
                    String nlpSugName;

                    String userInput = mSearchView.getInputString();

                    ServerJobSuggestBean bestMatchNlpSug = mSearchView.getBestMatchNlpSug();
                    if (bestMatchNlpSug == null || bestMatchNlpSug.config == null || bestMatchNlpSug.config.code == 0) {
                        thirdItem = OTHER;
                        nlpSugName = userInput;
                    } else {
                        thirdItem = bestMatchNlpSug.config;
                        try {
                            nlpSugName = bestMatchNlpSug.highlightItem.name;
                        } catch (Exception ignored) {
                            nlpSugName = userInput;
                        }
                    }

                    finishActivity(null, null, thirdItem, nlpSugName, 0, true);

                    if (isFromEditWorkExp()) {
                        String p4 = "";
                        if (thirdItem == OTHER) {
                            p4 = "3";
                        } else if (mSearchView.isNlpForecast()) {
                            p4 = "2";
                        } else if (mSearchView.isNLPRecommend()) {
                            p4 = "1";
                        }
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_USERINFO_MICRORESUME_WORK_ADDJOBTITLE)
                                .param("p", 4) // 1.职类建议 2.NLP推荐 3.NLP预测 4.手动输入保存
                                .param("p2", nlpSugName) // 职位名称文本
                                .param("p3", thirdItem.code) // 职位类型code
                                .param("p4", p4) // 职位类型来源：1.推荐职位code 2.预测职位code 3.其他-其他（当actionp=4时）
                                .build();
                    }
                }
            });
        }
    }

    private void onPressBack() {
        if (shouldEnterMultiChoiceMode && initialSeletedMultiPositions.size() > 0) {
            // 用户未选择新的，恢复之前选的多期望
            selectedMultiPositions.clear();
            for (LevelBean initialSeletedMultiPosition : initialSeletedMultiPositions) {
                initialSeletedMultiPosition.setChecked(true);
            }
            selectedMultiPositions.addAll(initialSeletedMultiPositions);
        }

        AppUtil.hideSoftInput(this);
        AppUtil.finishActivity(this);
    }

    //region Multi-Choice
    private boolean isThisPositionChecked(LevelBean thirdLevel) {
        return shouldEnterMultiChoiceMode && thirdLevel.isChecked();
    }

    private void uncheckThisPosition(LevelBean thirdLevel) {
        selectedMultiPositions.remove(thirdLevel);
        thirdLevel.setChecked(false);
    }

    private void checkThisPosition(LevelBean thirdLevel) {
        selectedMultiPositions.add(thirdLevel);
        thirdLevel.setChecked(true);

        if (inPartTimeJob()) {
            updateSelectCount();
        }
    }

    private void onCheckThisPositionView(@NonNull LevelBean thirdLevel) {
        jobIntentTreeView.notifyThreeLevelDataChanged();

        thirdLevel.setType(Type.TYPE_STANDARD);
        addSelectedPosition(thirdLevel);
    }

    private void onRemoveThisPosition(@NonNull LevelBean thirdLevel) {
        thirdLevel.setChecked(false);
        jobIntentTreeView.notifyThreeLevelDataChanged();

        selectedMultiPositions.remove(thirdLevel);

        Object tag = thirdLevel.getTag();
        if (tag instanceof ZPUIRoundButton) {
            // 移除该View
            selected_position_layout.removeView((View) tag);
        }

        updateConfirmButtonStatus();
        shouldLeaveMultiChoiceMode();

        if (inPartTimeJob()) {
            updateSelectCount();
        }
    }

    private void shouldLeaveMultiChoiceMode() {
        if (selectedMultiPositions.size() == 0) {
            leaveMultiChoiceMode();
        }
    }

    private void enterMultiChoiceMode() {
        multi_divider.setVisibility(View.VISIBLE);
        multi_position_layout.setVisibility(View.VISIBLE);

        updateConfirmButtonStatus();

        jobIntentTreeView.setInMultipleChoiceMode(true);
        jobIntentTreeView.notifyThreeLevelDataChanged();
    }

    private void updateConfirmButtonStatus() {
        // 更新确定按钮状态
        int size = selectedMultiPositions.size();
        confirm_btn.setText(String.format(Locale.getDefault(), "选好了%d/%d", size, maxCount));
//        confirm_btn.setEnabled(size == maxCount);
    }

    private void addSelectedPosition(@NonNull LevelBean levelBean) {
        ZPUIRoundButton selectedPositionView =
                (ZPUIRoundButton) LayoutInflater.from(this).inflate(R.layout.item_multi_position,
                        selected_position_layout, false);
        selectedPositionView.setText(levelBean.name);
        selectedPositionView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                // 点击移除该职位
                onRemoveThisPosition(levelBean);
            }
        });
        selected_position_layout.addView(selectedPositionView);

        levelBean.setTag(selectedPositionView);
    }

    private void leaveMultiChoiceMode() {
        multi_divider.setVisibility(View.GONE);
        multi_position_layout.setVisibility(View.GONE);
        selected_position_layout.removeAllViews();
        jobIntentTreeView.setInMultipleChoiceMode(false);
        jobIntentTreeView.notifyThreeLevelDataChanged();
    }
    //endregion

    @NonNull
    private String concateCheckedPosition() {
        List<String> selected = new ArrayList<>();
        for (LevelBean levelBean : selectedMultiPositions) {
            selected.add(String.valueOf(levelBean.code));
        }
        return StringUtil.connectTextWithChar(",", selected);
    }

    private void initIntent() {
        shouldEnterMultiChoiceMode = getIntent().getBooleanExtra(KEY_SHOULD_ENTER_MULTI_CHOICE_MODE, false);
        isShowReset = getIntent().getBooleanExtra(BUNDLE_IS_SHOW_RESET, false);
        isInter = getIntent().getBooleanExtra(BUNDLE_IS_INTER, false);

        mCanReVisible = getIntent().getBooleanExtra(CAN_USE_RE_VISIBLE, false);
        mFirstLevelBean = (LevelBean) getIntent().getSerializableExtra(SELECTED_FIRST_POSITION_ITEM);
        mSecondLevelBean = (LevelBean) getIntent().getSerializableExtra(SELECTED_SECOND_POSITION_ITEM);

        jobType = getIntent().getIntExtra(BUNDLE_JOB_TYPE, 0);
        isFromBgAction = getIntent().getBooleanExtra(KEY_BOSS_EDIT_JOB_CLASS, false);
        changeType = getIntent().getIntExtra(BUNDLE_RESET_TYPE, -1);

        boolean isRegister = getIntent().getBooleanExtra(IS_REGISTER, false);
        if (isRegister) {
            maxCount = MAX_COUNT;
        } else {
            maxCount = MAX_COUNT - GeekExpectManager.getGeekExpectCount();
            maxCount = maxCount <= 0 ? MAX_COUNT /* 兜底 */ : maxCount;
        }
    }

    private boolean inPartTimeJob() {
        // 809 兼职需求：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=70591507
        /*
        兼职【职位期望】
            1）点击后弹出列表，展示蓝领可兼职code
            2）隐藏职位的一级类目，仅展示二级类目和其下的三级类目
            3）隐藏职位搜索（已失效）
            4）1005.602【策略】 兼职牛人期望页面优化：增加搜索框；进入该页面，弹出键盘
         */
        return getIntent().getBooleanExtra(KEY_IN_PART_TIME_JOB, false);
    }

    private boolean isBlueExprience() {
        return getIntent().getBooleanExtra(KEY_IN_BLUE_JOB, false);
    }

    private boolean isInter() {
        return isInter;
    }

    private void setData() {
        setData(false);
    }

    private void setData(boolean ignoreInitSelectedPositions) {
        shouldReorder();
        if (mCanReVisible && mSecondLevelBean != null) {
            jobIntentTreeView.showView(mPositionList, true, mFirstLevelBean, mSecondLevelBean);
        } else {
            jobIntentTreeView.showView(mPositionList);
        }
        // 命中灰度的编辑工作页面跳过来，开启计数
        mSearchView.enableInputTextCount(isFromEditWorkExp());
        mSearchView.setIgnoreRoleType(isFromEditWorkExp());

        if ((CompletionUtils.isStandalone() && fromWorkExpCompletion()) || isFromEditWorkExp()) {
            jobIntentTreeView.setInChooseMode(false);
        }

        mSearchView.setMatchCallBack(new MatchCallBack() {
            @Override
            public void onSearchMatchResult(boolean isSearchEmpty) {
                if (isSearchEmpty) {
                    showMainList();
                } else {
                    showCustomList();
                }
            }

            @Override
            public void onItemClickListener(ServerJobSuggestBean itemCompleteBean, boolean isNLPRecommend, boolean isNlpForecast, int position) {
                LevelBean thirdBean = itemCompleteBean.config;
                if (thirdBean != null &&
                        itemCompleteBean.parentConfig != null &&
                        itemCompleteBean.gParentConfig != null) {
                    reportNlpResult(thirdBean, itemCompleteBean.suggestName);


//                    // 判断是否已选满
//                    if (alreadyFull()) {
//                        ToastUtils.showText("已达上限");
//                        return;
//                    }
//
//                    // 搜索推荐词（标准code），客户端添加标准词，并带着之前选择的code一起跳转上一页

                    thirdBean.setType(isNLPRecommend ? Type.TYPE_NLP : Type.TYPE_SUGGEST);
//                    selectedMultiPositions.add(thirdBean);

                    resetSelectedMultiPositionsStatus();
                    selectedMultiPositions.clear(); // 使用搜索选择期望，直接返回搜索结果中点选的，清除多期望
                    if (itemCompleteBean.blueCollar) {
                        thirdBean.mark = 21;//同步IOS逻辑，后续会去掉蓝领blueCollar字段逻辑，统一使用mark字段，来判断是否蓝领
                    }

                    finishActivity(
                            ResultParams.obj()
                                    .setFirstItem(itemCompleteBean.gParentConfig)
                                    .setSecondItem(itemCompleteBean.parentConfig)
                                    .setThirdItem(thirdBean)
                                    .setNlpReportName(itemCompleteBean.highlightItem != null ? itemCompleteBean.highlightItem.name : mSearchView.getInputString())
                                    .setNlpReportNameIndex(itemCompleteBean.index)
                                    .setNlpReportNameList(concateSuggestExpect(mSearchView.getSuggestListData()))
                                    .setReportedPositionId(0)
                                    .setNLPRecommend(isNLPRecommend)
                    );

                    if (isFromEditWorkExp()) {
                        AnalyticsFactory.create()
                                .action(AnalyticsAction.ACTION_USERINFO_MICRORESUME_WORK_ADDJOBTITLE)
                                .param("p", isNlpForecast ? 3 : 2) // 1.职类建议 2.NLP推荐 3.NLP预测 4.手动输入保存 5.上报职位
                                .param("p2", thirdBean.name) // 职位名称文本
                                .param("p3", thirdBean.code) // 职位类型code
                                // .param("p4", ) // 职位类型来源：1.推荐职位code 2.预测职位code 3.其他-其他（当actionp=4时）
                                .build();
                    }
                }
            }

            @NonNull
            private String concateSuggestExpect(@Nullable List<ServerJobSuggestBean> suggestListData) {
                List<String> suggestExpectList = new ArrayList<>(); // - 分隔
                if (suggestListData != null && suggestListData.size() > 0) {
                    for (ServerJobSuggestBean suggestListDatum : suggestListData) {
                        LevelBean levelBean;
                        if (UserManager.isStudentRecord()) {
                            levelBean = suggestListDatum.parentConfig;
                        } else {
                            levelBean = suggestListDatum.config;
                        }
                        if (levelBean == null) {
                            continue;
                        }

                        if (!LText.empty(levelBean.name) && levelBean.code > 0) {
                            suggestExpectList.add(String.format(Locale.getDefault(), "%s-%d", levelBean.name, levelBean.code));
                        }
                    }
                }

                String result = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_KEYWORD, suggestExpectList);
                return StringUtil.getUrlEncodeString(result);
            }

            @Override
            public void setNoteVisibility(boolean isShow) {
                tipBar.setVisibility(isShow ? View.VISIBLE : View.GONE);
                changeReportViewStatus(isShow, mSearchView.getInputString());
                if (isShow) {
                    showMainList();
                }
            }

            @Override
            public void onJobReport() {

            }
        });

        // 需要在三级列表创建后设置多选状态
        if (!ignoreInitSelectedPositions) {
            shouldInitSelectedPositions();
        }

        if (ignoreInitSelectedPositions) {
            if (inPartTimeJob()) { // 数据初始化，移除之前选择的数据
                Serializable extra = getIntent().getSerializableExtra(SELECTED_MULTI_POSITION_ITEM);
                if (extra instanceof ArrayList) {
                    //noinspection unchecked
                    ArrayList<LevelBean> list = (ArrayList<LevelBean>) extra;
                    if (LList.getCount(list) == 0 && shouldEnterMultiChoiceMode) { // 进入多选模式
                        resetSelectedMultiPositionsStatus();
                        selectedMultiPositions.clear();

                        updateSelectCount();
                    }
                }
            }
        }
    }

    private void shouldReorder() {
        // 804.22【大蓝用户产品】完善流程实验迭代
        // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=64815549
        /*
            1、在工作经历页面中，选择职位类型时，把求职意向中选中的一级code放在第一位。
            2、如果有多个期望的情况下，把多个期望命中的一级code置顶
         */
        //noinspection unchecked
        ArrayList<JobIntentBean> jobIntentBeans = (ArrayList<JobIntentBean>) getIntent().getSerializableExtra(BUNDLE_USER_EXPECT_LIST);
        if (jobIntentBeans != null && jobIntentBeans.size() > 0) {
            List<LevelBean> targetPositions = new ArrayList<>();
            // 重新排序一级职类列表数据
            for (LevelBean levelBean : mPositionList) {
                for (JobIntentBean jobIntentBean : jobIntentBeans) {
                    if (jobIntentBean.positionLv1 == levelBean.code) {
                        targetPositions.add(levelBean);
                    }
                }
            }

            // 重新排序
            for (int i = targetPositions.size() - 1; i >= 0; i--) {
                LevelBean targetPosition = targetPositions.get(i);
                mPositionList.remove(targetPosition);
                mPositionList.add(0, targetPosition);
            }
        }
    }

    private boolean alreadyFull() { // 多选期望已选满
        return shouldEnterMultiChoiceMode && maxCount == selectedMultiPositions.size();
    }

    private void shouldInitSelectedPositions() {
        Serializable extra = getIntent().getSerializableExtra(SELECTED_MULTI_POSITION_ITEM);
        if (extra instanceof ArrayList) {
            //noinspection unchecked
            ArrayList<LevelBean> list = (ArrayList<LevelBean>) extra;
            if (LList.getCount(list) > 1 && shouldEnterMultiChoiceMode) { // 进入多选模式
                for (LevelBean position : selectedMultiPositions) {
                    addSelectedPosition(position);
                }
                initialSeletedMultiPositions.addAll(selectedMultiPositions);
                enterMultiChoiceMode();
                return;
            }
        }
        resetSelectedMultiPositionsStatus();
        selectedMultiPositions.clear();
    }

    @SuppressLint("SetTextI18n")
    private void initViews() {
        parentCl = findViewById(R.id.parent_cl);
        topTitleLl = findViewById(R.id.top_title_ll);
        reportPositionView = findViewById(R.id.geekReportPositionView);
        multi_divider = findViewById(R.id.multi_divider);
        multi_position_layout = findViewById(R.id.multi_position_layout);
        selected_position_layout = findViewById(R.id.selected_position_layout);
        confirm_btn = findViewById(R.id.confirm_btn);

        confirm_btn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                // 提交选择的多职位
                finishActivity();
            }
        });
        tvSelectCount = findViewById(R.id.tv_select_count);

        pb_loading = findViewById(R.id.pb_loading);
        mSearchView = findViewById(R.id.rl_search);
        customList = findViewById(R.id.lv_search0);
        mSearchView.attachCustomList(customList);
        mSearchView.setInputString(getIntent().getStringExtra(KEY_INPUT_STRING));
        final ExpectParams expectParams = getExpectParams();
        mSearchView.set_920_688_style(expectParams != null && expectParams.enable_920_688_style);

        boolean noInput = (isBlueExprience() || isFromTourist());
        mSearchView.setVisibility(noInput ? View.GONE : View.VISIBLE);

        if (inPartTimeJob()) {
            mSearchView.postDelayed(() -> mSearchView.showKeyBoard(), 300);
        }

        jobIntentTreeView = findViewById(R.id.mLevelView);

        tipBar = findViewById(R.id.tip_bar);
        TipManager.Tip tip = new TipManager.Tip();
        tip.content = "暂无精准匹配职位，请在下方的类别中选择添加";
        tipBar.show(tip);
        tipBar.setVisibility(View.GONE);

        mRecommendView = findViewById(R.id.mRecommendView);
        mRecommendView.setFromWorkExpCompletion(fromWorkExpCompletion());
        mRecommendView.setChangeType(changeType);
        mRecommendView.setCallBack(item -> finishActivity(item, item, item, "", 0, false));
        mRecommendView.loadRecommend();
        findViewById(R.id.recommendLayout).setVisibility(inPartTimeJob() || isBlueExprience() ? View.GONE : View.VISIBLE);
        mSearchView.setOnSuggestCallback(new OnSuggestCallback() {
            @Override
            public void onSuggestListener(boolean isEmpty) {
                // 建议列表为空时,显示推荐列表
                mRecommendView.refreshVisibility(isEmpty);
            }

            @Override
            public boolean inPartTimeJob() {
                return ThreeLevelPositionPickActivity.this.inPartTimeJob();
            }
        });

        TextView mTitle = findViewById(R.id.mTextTitle);

        abl = findViewById(R.id.appBarLayout);
        abl.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            boolean collapsed = absOffset >= appBarLayout.getTotalScrollRange();
            //noinspection deprecation
            titleView.setDividerVisibility(collapsed ? View.VISIBLE : View.INVISIBLE);
            titleView.setTitle(collapsed ? mTitle.getText() : "");
        });

        jobIntentTreeView.setOnThreeLevelClickCallBack(threeLevelClickCallBack);

        MTextView mDesc = findViewById(R.id.mTextDesc);

        if (isInter() || UserManager.isStudentRecord()) {//实习生
            if (isFromTourist()) {
                mTitle.setText("选择期望职位");
            } else {
                if (UserManager.isStudentRecord()) {
                    mTitle.setText(isBlueExprience() ? "选择职位类型" : "选择期望职位");
                } else {
                    mTitle.setText(isBlueExprience() ? "选择职位类型" : "选择实习方向");
                }
            }
            mDesc.setText(isBlueExprience() ? "选择职位名称对应的类型" : "选择你要浏览的职位，我们会将你推荐给BOSS");
        } else {// 普通
            if (inPartTimeJob() || isBlueExprience()) {
                mTitle.setText("选择职位类型");
                mDesc.setText("选择职位名称对应的类型");
            } else {
                mTitle.setText("请先选择所属职位类型");
                mDesc.setText("选择职位信息对应的职位分类");

                if (isFromEditWorkExp()) {
                    mTitle.setText("选择职位名称");
                } else if (fromWorkExpCompletion()) {
                    mTitle.setText("想找哪些工作？");
                }
            }
        }

        //region 自定义标题和描述，不为 null，则表示调用方需要自定义标题/描述，可以传空串：""，表示清除标题/描述
        if (expectParams != null) {
            final String customTitle = expectParams.customTitle;
            if (customTitle != null) {
                mTitle.setText(customTitle);
            }

            final String customSubtitle = expectParams.customSubtitle;
            if (customSubtitle != null) {
                mDesc.setText(customSubtitle, View.GONE);
            }
        }
        //endregion

        if (inPartTimeJob() && shouldEnterMultiChoiceMode) {
            // 使用兼职特有的多选样式
            usePartTimeMultiChooseStyle();
            updateSelectCount();
        }
    }

    @Nullable
    private ExpectParams getExpectParams() {
        return (ExpectParams) getIntent().getSerializableExtra(KEY_EXPECT_PARAMS);
    }

    private boolean fromWorkExpCompletion() {
        return getIntent().getBooleanExtra(BUNDLE_FROM_WORK_EXP_COMPLETION, false);
    }

    private boolean isFromEditWorkExp() {
        return getIntent().getBooleanExtra(BUNDLE_FROM_EDIT_WORK_EXP, false);
    }

    private boolean isFromTourist() {
        return getIntent().getBooleanExtra(KEY_FROM_TOURIST, false);
    }

    private void showMainList() {
        jobIntentTreeView.setVisibility(View.VISIBLE);
        customList.setVisibility(View.GONE);
    }

    private void showCustomList() {
        jobIntentTreeView.setVisibility(View.GONE);
        customList.setVisibility(View.VISIBLE);
    }

    /**
     * 上报结果
     */
    private void reportNlpResult(@NonNull LevelBean config, String suggestName) {
        if (TextUtils.isEmpty(suggestName)) return;
        if (TextUtils.equals(config.name, suggestName)) return;
        CustomIndustryRequest request = new CustomIndustryRequest(new ApiRequestCallback<CustomIndustryResponse>() {
            @Override
            public void onSuccess(ApiData<CustomIndustryResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.name = suggestName;
        request.search = "0";
        //noinspection unchecked
        HttpExecutor.execute(request);
    }

    private void getData() {
        if (isInter()) {
            mGetPositionTask.execute();
        } else if (isBlueExprience()) {
            mPositionList.clear();
            List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
            mPositionList.addAll(basicData);
            PositionUtil.pickBlueJobs(true, mPositionList);
            jobIntentTreeView.setStudent(false);
            setData(); // 显示数据
        } else if (shouldGetServerData()) {
            GetCityPositionRequest2 request2 = new GetCityPositionRequest2(new ApiRequestCallback<GetCityPositionResponse2>() {
                @Override
                public void onStart() {
                    super.onStart();
                    pb_loading.setVisibility(View.VISIBLE);
                }

                @Override
                public void handleInChildThread(ApiData<GetCityPositionResponse2> data) {
                    super.handleInChildThread(data);
                    mPositionList.addAll(data.resp.position);

                    //region 兼职职位只需要二级、三级职类
                    PositionUtil.pickPartTimeJobs(inPartTimeJob(), mPositionList);
                    //endregion
                }

                @Override
                public void onSuccess(ApiData<GetCityPositionResponse2> data) {

                }

                @Override
                public void onComplete() {
                    pb_loading.setVisibility(View.GONE);

                    ArrayList<LevelBean> realSelectedList = new ArrayList<>();

                    Serializable extra = getIntent().getSerializableExtra(SELECTED_MULTI_POSITION_ITEM);
                    if (extra instanceof ArrayList) {
                        //noinspection unchecked
                        ArrayList<LevelBean> userSelectedList = (ArrayList<LevelBean>) extra;
                        if (LList.getCount(userSelectedList) > 0 && shouldEnterMultiChoiceMode) {

                            // 设置数据选中状态
                            if (inPartTimeJob()) {
                                for (LevelBean firstLevel : mPositionList) {
                                    List<LevelBean> subLevelModeList = firstLevel.subLevelModeList;
                                    if (subLevelModeList == null) continue;

                                    for (LevelBean secondLevel : subLevelModeList) {
                                        for (LevelBean levelBean : userSelectedList) {
                                            if (secondLevel.code == levelBean.code) {
                                                secondLevel.setChecked(true);
                                                realSelectedList.add(secondLevel);
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                for (LevelBean firstLevel : mPositionList) {
                                    List<LevelBean> secondLevelList = firstLevel.subLevelModeList;
                                    if (secondLevelList == null) continue;

                                    for (LevelBean secondLevel : secondLevelList) {
                                        List<LevelBean> thirdLevelList = secondLevel.subLevelModeList;
                                        if (thirdLevelList == null) continue;

                                        for (LevelBean thirdLevel : thirdLevelList) {
                                            for (LevelBean userSelected : userSelectedList) {
                                                if (thirdLevel.code == userSelected.code) {
                                                    thirdLevel.setChecked(true);
                                                    realSelectedList.add(thirdLevel);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            for (LevelBean userSelected : realSelectedList) {
                                addSelectedPosition(userSelected);
                            }

                        }
                    }

                    setData(true);

                    jobIntentTreeView.setInChooseMode(true);
                    if (realSelectedList.size() > 0) {
                        enterMultiChoiceMode();
                        updateSelectCount();
                        return;
                    }

                    resetSelectedMultiPositionsStatus();
                    selectedMultiPositions.clear();
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    //region 兜底数据（本地数据）
                    List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
                    mPositionList.addAll(basicData);

                    //region 兼职职位只需要二级、三级职类
                    PositionUtil.pickPartTimeJobs(inPartTimeJob(), mPositionList);
                    //endregion
                    //endregion
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            request2.cityCode = getCityIfAny();
            request2.execute();
        } else {
            GetAppPositionListRequest request = new GetAppPositionListRequest(new ApiRequestCallback<GetAppPositionListRsponse>() {
                @NonNull
                private final ArrayMap<Long, LevelBean> codeLevelMap = new ArrayMap<>();

                @Override
                public void onStart() {
                    super.onStart();
                    pb_loading.setVisibility(View.VISIBLE);
                }

                @Override
                public void handleInChildThread(ApiData<GetAppPositionListRsponse> data) {
                    super.handleInChildThread(data);
                    List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
                    codeLevelMap.clear();
                    for (LevelBean levelBean : basicData) {
                        codeLevelMap.put(levelBean.code, levelBean);
                    }

                    mPositionList.clear();
                    final List<LevelBean> orderedPositions = new ArrayList<>();
                    List<Long> positionCodes = data.resp.positionCodes;
                    if (LList.hasElement(positionCodes)) {
                        // 根据后台返回的一级类 code 顺序，排序本地基础数据
                        for (Long positionCode : positionCodes) {
                            LevelBean levelBean = codeLevelMap.get(positionCode);
                            codeLevelMap.remove(positionCode);
                            if (levelBean == null) continue;

                            orderedPositions.add(levelBean);
                        }

                        // 添加剩余的未排序的
                        if (codeLevelMap.size() > 0) {
                            Collection<LevelBean> remainingLevelBeans = codeLevelMap.values();
                            orderedPositions.addAll(remainingLevelBeans);
                        }

                        mPositionList.addAll(orderedPositions);
                    } else {
                        // 接口未返回排序规则，直接添加基础数据
                        mPositionList.addAll(basicData);
                    }

                    //region 兼职职位只需要二级、三级职类
                    PositionUtil.pickPartTimeJobs(inPartTimeJob(), mPositionList);
                    //endregion

                }

                @Override
                public void onSuccess(ApiData<GetAppPositionListRsponse> data) {

                }

                @Override
                public void onComplete() {
                    setData(); // 显示数据
                    pb_loading.setVisibility(View.GONE);
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=68420997
            request.city = getCityIfAny(); // 城市 code （807新增）
            request.execute();
        }
    }

    private void usePartTimeMultiChooseStyle() {
        if (maxCount <= 1) return;

        confirm_btn.setVisibility(View.GONE);
        tvSelectCount.setVisibility(View.VISIBLE);
        titleView.setBackClickListener(R.mipmap.ic_action_close_black, new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                onPressBack();
            }
        });
        titleView.setActionButtonListener("下一步", new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                /*
                在没有点选任何期望的情况下点击【下一步】，会展示toast：请选择一个期望职位
                 */
                if (selectedMultiPositions.isEmpty()) {
                    ToastUtils.showText("请选择一个期望职位");
                    return;
                }

                // 提交选择的多职位
                finishActivity();
            }
        });
    }

    /**
     * 更新选中个数
     */
    private void updateSelectCount() {
        int selectCount = selectedMultiPositions.size();
        String countString = String.valueOf(selectCount);
        String totalString = countString + "/" + maxCount;
        SpannableStringBuilder builder = new SpannableStringBuilder(totalString);
        builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.app_green_dark)),
                0, countString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        tvSelectCount.setText(builder);
        titleView.getTvBtnAction().setTextColor(Color.parseColor(selectCount > 0 ? "#0D9EA3" : "#B8B8B8"));
    }

    private boolean shouldGetServerData() {
        return getIntent().getBooleanExtra(KEY_GET_SERVER_DATA, false);
    }

    @Nullable
    private String getCityIfAny() {
        return getIntent().getStringExtra(KEY_USER_CITY);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isClickSuggest = false;
        mGetPositionTask.cancel(true);
    }

    private void finishActivity() {
        finishActivity(null, null, null, null, 0, false);
    }

    private void finishActivity(@NonNull ResultParams resultParams) {
        LevelBean thirdItem = resultParams.getThirdItem();
        LevelBean firstItem = resultParams.getFirstItem();
        LevelBean secondItem = resultParams.getSecondItem();
        String nlpReportName = resultParams.getNlpReportName();
        int nlpReportNameIndex = resultParams.getNlpReportNameIndex();
        String nlpReportNameList = resultParams.getNlpReportNameList();
        long reportedPositionId = resultParams.getReportedPositionId();
        boolean isNLPRecommend = resultParams.isNLPRecommend();

        Intent intent = new Intent();
        intent.putExtra(SELECTED_FIRST_POSITION_ITEM, firstItem);
        intent.putExtra(SELECTED_SECOND_POSITION_ITEM, secondItem);
        intent.putExtra(SELECTED_THIRD_POSITION_ITEM, thirdItem);

        // 多期望
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);

        intent.putExtra(REPORTED_POSITION_ID, reportedPositionId);
        intent.putExtra(IS_NLP_RECOMMEND, isNLPRecommend);
        intent.putExtra(SELECTED_NLP_SUGGEST_POSITION, nlpReportName);
        intent.putExtra(ExpectConstants.SELECTED_NLP_SUGGEST_POSITION_INDEX, nlpReportNameIndex);
        intent.putExtra(ExpectConstants.SELECTED_NLP_SUGGEST_POSITION_LIST, nlpReportNameList); // 推荐期望名称 - 期望code 的list（中间以英文横杠连接），#&#分隔分隔
        intent.putExtra(MATCH_INPUT, mSearchView.getInputString());
        setResult(RESULT_OK, intent);
        AppUtil.finishActivity(this);
    }

    private void finishActivity(LevelBean firstItem,
                                LevelBean secondItem,
                                LevelBean thirdItem,
                                String nlpReportName,
                                long reportedPositionId,
                                boolean isNLPRecommend) {
        finishActivity(
                ResultParams.obj()
                        .setFirstItem(firstItem)
                        .setSecondItem(secondItem)
                        .setThirdItem(thirdItem)
                        .setNlpReportName(nlpReportName)
                        .setReportedPositionId(reportedPositionId)
                        .setNLPRecommend(isNLPRecommend)
        );
    }

    private void resetSelectedMultiPositionsStatus() {
        for (LevelBean selectedMultiPosition : selectedMultiPositions) {
            selectedMultiPosition.setChecked(false);
        }
    }

    private void changeReportViewStatus(boolean toShow, String input) {
        if (!ActivityUtils.isValid(this) || jobIntentTreeView == null) return;

        if (!jobIntentTreeView.inThreeLevel) return;
        // if (isInter() || UserManager.isStudentRecord()) return; // 学生版二级类选择不加

        if (toShow) {
            if (LText.empty(input)) return;

            reportPositionView.setListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    directReportAsOther(input);
                }
            });
            reportPositionView.show(input);
            reportPositionView.setVisibility(View.VISIBLE);
        } else {
            reportPositionView.setVisibility(View.GONE);
        }
    }

    /**
     * 上报，并以其他职类 填充页面
     */
    private void directReportAsOther(String input) {
        ReportCustomPositionRequest request = new ReportCustomPositionRequest(new ApiRequestCallback<ReportCustomPositionResponse>() {

            @Override
            public void onStart() {
                showProgressDialog("正在上报中，请稍候");
            }

            @Override
            public void onSuccess(ApiData<ReportCustomPositionResponse> data) {
                ReportCustomPositionResponse resp = data.resp;
                if (resp != null) {
                    ToastUtils.showText("上报成功");
                    // 关闭上报卡片
                    reportPositionView.setVisibility(View.GONE);
                }
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.name = input;
        request.execute();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onPressBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void reportOtherPosition(LevelBean firstItem, LevelBean secondItem, LevelBean thirdItem) {
        mTempFirstItem = firstItem;
        mTempSecondItem = secondItem;
        Intent intent = new Intent(this, ExpectPositionOtherActivity.class);
        intent.putExtra(Constants.DATA_ENTITY, thirdItem);
        intent.putExtra(Constants.DATA_BOOLEAN, isFromEditWorkExp());
        AppUtil.startActivityForResult(this, intent, REQUEST_OTHER_POSITION, ActivityAnimType.UP_GLIDE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_OTHER_POSITION && resultCode == RESULT_OK) {
            long positionId = data.getLongExtra(REPORTED_POSITION_ID, 0);
            String positionName = data.getStringExtra(ExpectPositionOtherActivity.REPORT_POSITION_NAME);
            LevelBean thirdItem = (LevelBean) data.getSerializableExtra(SELECTED_THIRD_POSITION_ITEM);

            if (thirdItem != null) {
                thirdItem.setType(Type.TYPE_CUSTOM);
            }
            selectedMultiPositions.add(thirdItem);
            finishActivity(mTempFirstItem, mTempSecondItem, thirdItem, positionName, positionId, false);
        }
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    /**
     * 统一使用该接口跳转职类选择页面！跳转参数封装到{@link ExpectParams}对象中。
     */
    public static void jumpForResult(@NonNull Context context, @NonNull ExpectParams params, int requestCode) {
        Intent intent = getIntent(context, params);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    public static Intent getIntent(@NonNull Context context, @NonNull ExpectParams params) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);

        // 统一使用ExpectParams取值
        intent.putExtra(KEY_EXPECT_PARAMS, params);

        intent.putExtra(BUNDLE_IS_INTER, params.isIntern);
        intent.putExtra(IS_REGISTER, params.isRegister);
        intent.putExtra(BUNDLE_RESET_TYPE, params.resetType);
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, params.selectedMultiPositions);
        intent.putExtra(KEY_USER_CITY, String.valueOf(params.cityCode));
        intent.putExtra(KEY_GET_SERVER_DATA, params.getServerData);
        intent.putExtra(KEY_IN_BLUE_JOB, params.isBlueExprience);
        intent.putExtra(KEY_IN_PART_TIME_JOB, params.inPartTimeJob);
        intent.putExtra(KEY_SHOULD_ENTER_MULTI_CHOICE_MODE, params.inMultiChoiceMode);
        intent.putExtra(KEY_FROM_TOURIST, params.isFromTourist);

        intent.putExtra(SELECTED_FIRST_POSITION_ITEM, params.firstSelectLevel);
        intent.putExtra(SELECTED_SECOND_POSITION_ITEM, params.secondSelectLevel);
        intent.putExtra(CAN_USE_RE_VISIBLE, params.canReVisible);

        return intent;
    }

    public static class ExpectParams extends BaseEntity {

        private static final long serialVersionUID = 591825416639784934L;

        boolean isIntern;
        boolean isRegister;
        boolean inPartTimeJob;
        boolean isBlueExprience;
        long cityCode;
        boolean getServerData;
        boolean inMultiChoiceMode;
        boolean isFromTourist;
        ArrayList<LevelBean> selectedMultiPositions;
        private int resetType;
        private boolean enable_920_688_style;

        boolean canReVisible;
        LevelBean firstSelectLevel;
        LevelBean secondSelectLevel;

        String customTitle;
        String customSubtitle;

        private ExpectParams() {

        }

        public boolean isCanReVisible() {
            return canReVisible;
        }

        public ExpectParams setCanReVisible(boolean canReVisible) {
            this.canReVisible = canReVisible;
            return this;
        }

        public LevelBean getFirstSelectLevel() {
            return firstSelectLevel;
        }

        public ExpectParams setFirstSelectLevel(LevelBean firstSelectLevel) {
            this.firstSelectLevel = firstSelectLevel;
            return this;
        }

        public LevelBean getSecondSelectLevel() {
            return secondSelectLevel;
        }

        public ExpectParams setSecondSelectLevel(LevelBean secondSelectLevel) {
            this.secondSelectLevel = secondSelectLevel;
            return this;
        }

        public static ExpectParams obj() {
            return new ExpectParams();
        }

        public ExpectParams isIntern(boolean isIntern) {
            this.isIntern = isIntern;
            return this;
        }

        public ExpectParams isRegister(boolean isRegister) {
            this.isRegister = isRegister;
            return this;
        }

        public ExpectParams inPartTimeJob(boolean inPartTimeJob) {
            this.inPartTimeJob = inPartTimeJob;
            return this;
        }

        public ExpectParams cityCode(long cityCode) {
            this.cityCode = cityCode;
            return this;
        }

        public ExpectParams getServerData(boolean getServerData) {
            this.getServerData = getServerData;
            return this;
        }

        public ExpectParams selectedMultiPositions(ArrayList<LevelBean> selectedMultiPositions) {
            this.selectedMultiPositions = selectedMultiPositions;
            return this;
        }

        public ExpectParams inMultiChoiceMode(boolean inMultiChoiceMode) {
            this.inMultiChoiceMode = inMultiChoiceMode;
            return this;
        }

        public ExpectParams setFromTourist(boolean isFromTourist) {
            this.isFromTourist = isFromTourist;
            return this;
        }

        public ExpectParams setResetType(int resetType) {
            this.resetType = resetType;
            return this;
        }

        public ExpectParams setEnable_920_688_style(boolean enable_920_688_style) {
            this.enable_920_688_style = enable_920_688_style;
            return this;
        }

        public ExpectParams setCustomTitle(@Nullable String customTitle) {
            this.customTitle = customTitle;
            return this;
        }

        public ExpectParams setCustomSubtitle(@Nullable String customSubtitle) {
            this.customSubtitle = customSubtitle;
            return this;
        }
    }

    public static class ResultParams extends BaseEntity {

        private static final long serialVersionUID = -4383038596997733481L;

        private LevelBean firstItem;
        private LevelBean secondItem;
        private LevelBean thirdItem;
        private String nlpReportName;
        private int nlpReportNameIndex = -1;
        private String nlpReportNameList;
        private long reportedPositionId;
        private boolean isNLPRecommend;

        private ResultParams() {
        }

        public static ResultParams obj() {
            return new ResultParams();
        }

        public LevelBean getFirstItem() {
            return firstItem;
        }

        public ResultParams setFirstItem(LevelBean firstItem) {
            this.firstItem = firstItem;
            return this;
        }

        public LevelBean getSecondItem() {
            return secondItem;
        }

        public ResultParams setSecondItem(LevelBean secondItem) {
            this.secondItem = secondItem;
            return this;
        }

        public LevelBean getThirdItem() {
            return thirdItem;
        }

        public ResultParams setThirdItem(LevelBean thirdItem) {
            this.thirdItem = thirdItem;
            return this;
        }

        public String getNlpReportName() {
            return nlpReportName;
        }

        public ResultParams setNlpReportName(String nlpReportName) {
            this.nlpReportName = nlpReportName;
            return this;
        }

        public int getNlpReportNameIndex() {
            return nlpReportNameIndex;
        }

        public ResultParams setNlpReportNameIndex(int nlpReportNameIndex) {
            this.nlpReportNameIndex = nlpReportNameIndex;
            return this;
        }

        public String getNlpReportNameList() {
            return nlpReportNameList;
        }

        public ResultParams setNlpReportNameList(String nlpReportNameList) {
            this.nlpReportNameList = nlpReportNameList;
            return this;
        }

        public long getReportedPositionId() {
            return reportedPositionId;
        }

        public ResultParams setReportedPositionId(long reportedPositionId) {
            this.reportedPositionId = reportedPositionId;
            return this;
        }

        public boolean isNLPRecommend() {
            return isNLPRecommend;
        }

        public ResultParams setNLPRecommend(boolean isNLPRecommend) {
            this.isNLPRecommend = isNLPRecommend;
            return this;
        }
    }

    ///
    ///
    ///

    //region Deprecated
    @Deprecated
    public static void jumpForResult(@NonNull Context context,
                                     @NonNull ArrayList<JobIntentBean> userExpectList, /* 用于将列表中对应期望一级类前置 */
                                     int requestCode, boolean fromWorkExpCompletion) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);
        intent.putExtra(BUNDLE_USER_EXPECT_LIST, userExpectList);
        intent.putExtra(BUNDLE_FROM_WORK_EXP_COMPLETION, fromWorkExpCompletion);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @Deprecated
    public static Intent getDefaultIntent(@NonNull Context context) {
        return getIntent(context, UserManager.isStudentRecord(), false, false, -1);
    }

    @Deprecated
    public static void jumpForResult(Context context, boolean isInter, int requestCode) {
        jumpForResult(context, isInter, -1, requestCode);
    }

    @Deprecated
    public static void jumpForResultByFragment(Fragment startFragment, Context context, boolean isInter, int requestCode) {
        jumpForResultByFragment(startFragment, context, isInter, -1, requestCode);
    }

    @Deprecated
    public static void jumpForResult(Context context, boolean isInter, boolean fromEditWorkExp, int requestCode) {
        jumpForResult(context, isInter, -1, fromEditWorkExp, requestCode);
    }

    @SuppressWarnings("DeprecatedIsStillUsed")
    @Deprecated
    public static void jumpForResultInMultiChoiceMode(
            Context context,
            boolean isInter,
            boolean isRegister,
            int cityCode,
            @Nullable ArrayList<LevelBean> selectedMultiPositions,
            int requestCode
    ) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);

        intent.putExtra(BUNDLE_IS_INTER, isInter);
        intent.putExtra(IS_REGISTER, isRegister);
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
        intent.putExtra(KEY_USER_CITY, String.valueOf(cityCode));

        intent.putExtra(KEY_SHOULD_ENTER_MULTI_CHOICE_MODE, true);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @Deprecated
    public static void jumpForResultInMultiChoiceMode2(
            Context context,
            boolean inPartTimeJob,
            int cityCode,
            boolean getServerData,
            @Nullable ArrayList<LevelBean> selectedMultiPositions,
            int requestCode
    ) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);

        intent.putExtra(BUNDLE_IS_INTER, UserManager.isGeekIntern());
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
        // 是否是兼职
        intent.putExtra(KEY_IN_PART_TIME_JOB, inPartTimeJob);
        intent.putExtra(KEY_USER_CITY, String.valueOf(cityCode));
        intent.putExtra(KEY_GET_SERVER_DATA, getServerData);

        intent.putExtra(KEY_SHOULD_ENTER_MULTI_CHOICE_MODE, true);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @SuppressWarnings("DeprecatedIsStillUsed")
    @Deprecated
    public static void jumpForResultInMultiChoiceMode3(
            Context context,
            boolean isBlueExprience,
            @Nullable ArrayList<LevelBean> selectedMultiPositions,
            int requestCode
    ) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);

        intent.putExtra(BUNDLE_IS_INTER, UserManager.isGeekIntern());
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
        // 是否是兼职
        intent.putExtra(KEY_IN_BLUE_JOB, isBlueExprience);

        intent.putExtra(KEY_SHOULD_ENTER_MULTI_CHOICE_MODE, true);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @Deprecated
    public static void jumpForResult(Context context, boolean isInter, int changeType, int requestCode) {
        Intent intent = getIntent(context, isInter, false, false, changeType);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @Deprecated
    public static void jumpForResultByFragment(Fragment startFragment, Context context, boolean isInter, int changeType, int requestCode) {
        Intent intent = getIntent(context, isInter, false, false, changeType);
        startFragment.startActivityForResult(intent, requestCode);
    }

    @Deprecated
    public static void jumpForResult(Context context, boolean isInter, int changeType, boolean fromEditWorkExp, int requestCode) {
        Intent intent = getIntent(context, isInter, false, false, changeType, fromEditWorkExp);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    @Deprecated
    public static Intent getIntent(Context context, boolean isInter, boolean isEditJObClass, boolean isShowReset, int changeType) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);
        intent.putExtra(BUNDLE_IS_INTER, isInter);
        intent.putExtra(BUNDLE_RESET_TYPE, changeType);
        intent.putExtra(KEY_BOSS_EDIT_JOB_CLASS, isEditJObClass);
        intent.putExtra(BUNDLE_IS_SHOW_RESET, isShowReset);
        return intent;
    }

    @Deprecated
    public static Intent getIntent(Context context, boolean isInter, boolean isEditJObClass, boolean isShowReset, int changeType, boolean fromEditWorkExp) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);
        intent.putExtra(BUNDLE_IS_INTER, isInter);
        intent.putExtra(BUNDLE_RESET_TYPE, changeType);
        intent.putExtra(BUNDLE_FROM_EDIT_WORK_EXP, fromEditWorkExp);
        intent.putExtra(KEY_BOSS_EDIT_JOB_CLASS, isEditJObClass);
        intent.putExtra(BUNDLE_IS_SHOW_RESET, isShowReset);
        return intent;
    }

    @Deprecated
    public static Intent getIntent(Context context, int jobType) {
        Intent intent = new Intent(context, ThreeLevelPositionPickActivity.class);
        intent.putExtra(BUNDLE_JOB_TYPE, jobType);
        intent.putExtra(BUNDLE_IS_INTER, false);
        intent.putExtra(KEY_BOSS_EDIT_JOB_CLASS, true);
        intent.putExtra(BUNDLE_IS_SHOW_RESET, false);
        intent.putExtra(BUNDLE_RESET_TYPE, 0);
        return intent;
    }
    //endregion

    ///
    ///
    ///

}
