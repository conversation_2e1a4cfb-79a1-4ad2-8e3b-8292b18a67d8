package com.hpbr.bosszhipin.module.common;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ListView;
import android.widget.ProgressBar;

import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.common.adapter.ThreeLevelSelectionAdapter;
import com.hpbr.bosszhipin.module.my.activity.ExpectPositionOtherActivity;
import com.hpbr.bosszhipin.module.my.activity.boss.optration.PositionUtil;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.register.geek.WorkExpCompletionActivity;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.JobIntentSearchMatchView2;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.MatchCallBack;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.OnSuggestCallback;
import com.hpbr.bosszhipin.views.threelevel.OnPositionLevelSelectListener;
import com.hpbr.bosszhipin.views.threelevel.PositionLevelTreeView;
import com.hpbr.bosszhipin.views.tip.TipBar;
import com.hpbr.bosszhipin.views.tip.TipManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GetAppPositionListRequest;
import net.bosszhipin.api.GetAppPositionListRsponse;
import net.bosszhipin.api.GetGeekExpectPositionRequest;
import net.bosszhipin.api.GetGeekExpectPositionResponse;
import net.bosszhipin.api.bean.ServerJobNameSuggestBean;
import net.bosszhipin.api.bean.ServerJobSuggestBean;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CopyOnWriteArrayList;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Author: zhouyou
 * Date: 2020/5/18
 */
public class ThreeLevelPositionPickForMultiExpectActivity extends BaseActivity2 {

    //region 常量
    public static final String SELECTED_FIRST_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_FIRST_POSITION_ITEM";
    public static final String SELECTED_SECOND_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_SECOND_POSITION_ITEM";
    public static final String SELECTED_THIRD_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_THIRD_POSITION_ITEM";
    public static final String SELECTED_NLP_SUGGEST_POSITION = "com.hpbr.bosszhipin.SELECTED_NLP_SUGGEST_POSITION";
    public static final String REPORTED_POSITION_ID = "com.hpbr.bosszhipin.REPORTED_POSITION_ID";
    public static final String IS_NLP_RECOMMEND = Constants.PREFIX + ".IS_NLP_RECOMMEND";
    public static final String MATCH_INPUT = Constants.PREFIX + "MATCH_INPUT";
    public static final String KEY_INPUT_STRING = Constants.PREFIX + ".KEY_INPUT_STRING";
    public static final String SELECTED_MULTI_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_MULTI_POSITION_ITEM";
    public static final String IS_REGISTER = "com.hpbr.bosszhipin.IS_REGISTER";
    public static final String KEY_USER_CITY = "com.hpbr.bosszhipin.KEY_USER_CITY";
    public static final String KEY_CUSTOM_TITLE = "com.hpbr.bosszhipin.KEY_CUSTOM_TITLE";
    public static final String KEY_CUSTOM_SUB_TITLE = "com.hpbr.bosszhipin.KEY_CUSTOM_SUB_TITLE";
    public static final String IGNORE_BLUE_EXPECT_CHECK = "com.hpbr.bosszhipin.IGNORE_BLUE_EXPECT_CHECK";
    public static final String IN_SINGLE_CHOICE_MODE = "com.hpbr.bosszhipin.IN_SINGLE_CHOICE_MODE";
    public static final String FOR_ASK_COLLEAGUES_POST = "com.hpbr.bosszhipin.FOR_ASK_COLLEAGUES_POST";
    public static final String MAX_SELECT_ABLE_COUNT = "com.hpbr.bosszhipin.MAX_SELECT_ABLE_COUNT";
    public static final String KEY_JOB_INTENT_BEAN = "KEY_JOB_INTENT_BEAN";
    public static final String KEY_FROM_INTERVIEW_QUESTION = "KEY_FROM_INTERVIEW_QUESTION";

    // 统一从对象中获取参数
    public static final String KEY_MULTI_EXPECT_PARAMS = "KEY_MULTI_EXPECT_PARAMS";
    private static final String BUNDLE_RESET_TYPE = "BUNDLE_RESET_TYPE";
    private static final String KEY_GET_SERVER_DATA = "KEY_GET_SERVER_DATA";
    private static final String KEY_FROM_TOURIST = "KEY_FROM_TOURIST";
    private static final String KEY_HAS_JOBINTENT = "KEY_HAS_JOBINTENT";

    public static final String KEY_MULTI_RESULT_PARAMS = "KEY_MULTI_RESULT_PARAMS";

    public static final String OTHER_POSITION_KEYWORD = "其他";
    public static final String OTHER_POSITION_KEYWORD2 = "其它";
    //endregion

    @NonNull
    private static final LevelBean OTHER = new LevelBean(200101, "其他");

    private static final int REQUEST_OTHER_POSITION = 1;
    private static final int MAX_COUNT = 3;

    public static boolean isClickSuggest;

    private ProgressBar pb_loading;
    private JobIntentSearchMatchView2 mSearchView;
    private TipBar tipBar;
    private ListView customList;
    private PositionLevelTreeView positionLevelTreeView;
    private ConstraintLayout clSelectionPanel;
    private MTextView tvSelectCount;

    private ThreeLevelSelectionAdapter adapter;
    private RecyclerView rvSelections;

    /**
     * 是否实习生
     * 这个逻辑需要优化，这个时候其实已经不再是实习生的意思了
     * 根据页面注册流程驱动 isInter 是否 true or false
     * isInter  true 显示实习生的二级
     * false 显示正常的三级职位
     */
    private int changeType;
    private int maxCount;

    private LevelBean mTempFirstItem;
    private LevelBean mTempSecondItem;

    private boolean isInter;
    private boolean hasSetData;
    private boolean isFromTourist;
    private boolean hasJobIntent;
    private int jobTitleRecommendType; // 1219.611搜索无结果样式

    @NonNull
    private final List<LevelBean> mPositionList = new CopyOnWriteArrayList<>();
    @NonNull
    private final List<LevelBean> recommendList = new ArrayList<>();
    @NonNull
    private final ArrayList<LevelBean> selectedMultiPositions = new ArrayList<>();

    @NonNull
    private final Handler handler = AppThreadFactory.createMainHandler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == 100) {
                pb_loading.setVisibility(View.GONE);
                //noinspection unchecked
                List<LevelBean> list = (List<LevelBean>) msg.obj;
                if (LList.isEmpty(list)) {
                    ToastUtils.showText(R.string.string_data_error);
                    finish();
                } else {
                    mPositionList.addAll(list);
                    if (!isInter) {
                        //牛人 注册流程排序
                        checkGeekRegisterGraySort();
                    }
                    setData();
                }
                return true;
            }
            return false;
        }
    });

    /**
     * 三级类职位选择回调
     */
    @NonNull
    private final OnPositionLevelSelectListener onPositionLevelSelectListener = new OnPositionLevelSelectListener() {
        @Override
        public void onThirdLevelSelect(LevelBean firstLevel, LevelBean secondLevel, LevelBean thirdLevel) {
            if (thirdLevel.name != null
                    && (thirdLevel.name.contains(OTHER_POSITION_KEYWORD) || thirdLevel.name.contains(OTHER_POSITION_KEYWORD2))) { // 用户选择“其他/其它”，执行上报操作
                if (isFromTourist) {
                    ToastUtils.showText("暂不支持");
                } else {
                    if ((getMultiExpectParams() != null && getMultiExpectParams().directUseOther) ||
                            firstLevel.name.contains(OTHER_POSITION_KEYWORD) || firstLevel.name.contains(OTHER_POSITION_KEYWORD2)
                    ) {
                        // 1119.610 点选其他L1-其他L3客户端逻辑灰度，0-未命中进入其他职类的sug页面，1-命中直接选择该L3其他职类
                        if (!(thirdLevel.getTagObj() instanceof Integer)) {
                            thirdLevel.setTagObj(2); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他
                        }

                        onThirdLevelSelect0(firstLevel, secondLevel, thirdLevel);
                    } else {
                        reportOtherPosition(firstLevel, secondLevel, thirdLevel);
                    }
                }
            } else {
                if (!(thirdLevel.getTagObj() instanceof Integer)) {
                    thirdLevel.setTagObj(2); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他
                }

                onThirdLevelSelect0(firstLevel, secondLevel, thirdLevel);
            }
        }

        @Override
        public void onOtherLevelClick(@NonNull LevelBean firstLevel, @NonNull LevelBean secondLevel, @NonNull LevelBean thirdLevel) {
            if (!(thirdLevel.getTagObj() instanceof Integer)) {
                thirdLevel.setTagObj(3); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他
            }
            onThirdLevelSelect0(firstLevel, secondLevel, thirdLevel);

            showMainList();

            // 清空搜素框的词
            mSearchView.clearText();
        }
    };

    /**
     * 搜索匹配的回调
     */
    @NonNull
    private final MatchCallBack matchCallBack = new MatchCallBack() {
        @Override
        public void onSearchMatchResult(boolean isSearchEmpty) {
            if (isSearchEmpty) {
                showMainList();
            } else {
                showCustomList();
            }
        }

        @Override
        public void onItemClickListener(@NonNull ServerJobSuggestBean itemCompleteBean, boolean isNLPRecommend, boolean isNlpForecast, int position) {
            itemClickAction(itemCompleteBean, isNLPRecommend, isNlpForecast, position);
            if (!isNlpForecast) {
                reportExpectSugClick(itemCompleteBean, position);
            }
        }

        private void itemClickAction(@NonNull ServerJobSuggestBean itemCompleteBean, boolean isNLPRecommend, boolean isNlpForecast, int position) {
            boolean isNoMatchRecommend = jobTitleRecommendType == 1 && isNlpForecast; // 是否是1219.611新增的无期望推荐数据
            if (isInSingleChoiceMode()) {
                //region 单选，点击 sug item 直接返回
                LevelBean thirdBean = itemCompleteBean.config;
                if (thirdBean != null &&
                        itemCompleteBean.parentConfig != null &&
                        itemCompleteBean.gParentConfig != null) {
                    thirdBean.setType(isNLPRecommend ? LevelBean.Type.TYPE_NLP : LevelBean.Type.TYPE_SUGGEST);
                    thirdBean.setParentLevel(itemCompleteBean.parentConfig);
                    selectedMultiPositions.clear(); // 使用搜索选择期望，直接返回搜索结果中点选的，清除多期望

                    if (!(thirdBean.getTagObj() instanceof Integer)) {
                        thirdBean.setTagObj(isNlpForecast ? 4 : 3); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他（多个用逗号隔开,且与actionp一一对应），4、搜索无结果下的推荐列表
                    }
                    if (isNoMatchRecommend) {
                        thirdBean.queryName = mSearchView.getInputString();
                        AppAnalysisUtil.reportFuzzyMatchingClick(mSearchView.getInputString(), itemCompleteBean);
                    }

                    saveSinglePosition(MultiResultParams.obj()
                            .setFirstItem(itemCompleteBean.gParentConfig)
                            .setSecondItem(itemCompleteBean.parentConfig)
                            .setThirdItem(thirdBean)
                            .setNlpReportName(itemCompleteBean.highlightItem != null ? itemCompleteBean.highlightItem.name : "")
                            .setNlpReportNameIndex(itemCompleteBean.index)
                            .setNlpReportNameList(concateSuggestExpect(mSearchView.getSuggestListData()))
                            .setReportedPositionId(0)
                            .setNLPRecommend(isNLPRecommend)
                            .setSuggestName(isNoMatchRecommend ? null : itemCompleteBean.suggestName)
                    );

                    if (inPartTimeJob()) {
                        AnalyticsFactory.create().action("part-time-action-geek-sug-word-click")
                                .param("p2", thirdBean.code) // position_code (多个用逗号隔开)
                                .param("p5", mSearchView.getInputString()) // 期望职类填写文本信息（多个用逗号隔开)
                                .param("p11", itemCompleteBean.highlightItem != null ? itemCompleteBean.highlightItem.name
                                        : mSearchView.getInputString()) // 提示文本信息
                                .param("p15", thirdBean.name + "-" + (position - 1)) // 【想找什么工作】页面点击的期望列表的 期望名称 - 下角标（从0开始）（中间以英文横杠分隔）
                                .build();
                    }
                }
                //endregion
            } else {
                //region 进入多选
                // 在职位选择页，通过【搜索】、【其他职位】搜索并选择职位后，不再跳转回期望详情页，而是回到职位选择列表页，且回显在职类选择列表中展示已选状态
                LevelBean firstLevel = itemCompleteBean.gParentConfig;
                LevelBean secondLevel = itemCompleteBean.parentConfig;
                LevelBean thirdLevel = itemCompleteBean.config;

                if (thirdLevel != null) {
                    thirdLevel.setParentLevel(itemCompleteBean.parentConfig);
                    if (!(thirdLevel.getTagObj() instanceof Integer)) {
                        thirdLevel.setTagObj(isNlpForecast ? 4 : 3); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他（多个用逗号隔开,且与actionp一一对应），4、搜索无结果下的推荐列表
                    }
                    if (isNoMatchRecommend) {
                        thirdLevel.queryName = mSearchView.getInputString();
                        AppAnalysisUtil.reportFuzzyMatchingClick(mSearchView.getInputString(), itemCompleteBean);
                    }
                }
                onThirdLevelSelect0(firstLevel, secondLevel, thirdLevel);

                showMainList();
                //endregion

                // 清空搜素框的词
                mSearchView.clearText();
            }
        }

        @NonNull
        private String concateSuggestExpect(@Nullable List<ServerJobSuggestBean> suggestListData) {
            List<String> suggestExpectList = new ArrayList<>(); // - 分隔
            if (suggestListData != null && LList.isNotEmpty(suggestListData)) {
                for (ServerJobSuggestBean suggestListDatum : suggestListData) {
                    LevelBean levelBean;
                    if (UserManager.isStudentRecord()) {
                        levelBean = suggestListDatum.parentConfig;
                    } else {
                        levelBean = suggestListDatum.config;
                    }
                    if (levelBean == null) {
                        continue;
                    }

                    if (!LText.empty(levelBean.name) && levelBean.code > 0) {
                        suggestExpectList.add(String.format(Locale.getDefault(), "%s-%d", levelBean.name, levelBean.code));
                    }
                }
            }

            String result = StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_KEYWORD, suggestExpectList);
            return StringUtil.getUrlEncodeString(result);
        }

        @Override
        public void setNoteVisibility(boolean isShow) {
            setNoteVisibility(isShow, true);
        }

        @Override
        public void setNoteVisibility(boolean isShow, boolean showTipBar) {
            tipBar.setVisibility(isShow && showTipBar ? View.VISIBLE : View.GONE);
            if (isShow) {
                showMainList();
            }
        }

        @Override
        public void onJobReport() {

        }

        @NonNull
        @Override
        public OnPositionLevelSelectListener getOnPositionLevelSelectListener() {
            return onPositionLevelSelectListener;
        }

        @Override
        public void onSuggestResult(@Nullable String query, @Nullable List<ServerJobNameSuggestBean> resultList, boolean isNlpForecast) {
            // 上报期望的搜索sug列表
            reportExpectSugShow(query, !isNlpForecast ? resultList : null);
        }

        private void reportExpectSugShow(@Nullable String queryText, @Nullable List<ServerJobNameSuggestBean> list) {
            // 1305.623 仅期望添加和编辑两个页面来源上报该埋点
            if (!isFromCreateOrEditExpect()) {
                return;
            }
            String sugResult = null;
            if (list != null && LList.isNotEmpty(list)) {
                JSONArray resultArray = new JSONArray();
                for (int index = 0; index < list.size(); index++) {
                    ServerJobNameSuggestBean item = list.get(index);
                    if (item == null || item.pConfigLevel3 == null) continue;

                    JSONObject itemObj = new JSONObject();
                    try {
                        itemObj.put("query", item.suggestName);
                        itemObj.put("rank", index);
                        itemObj.put("l3Code", item.pConfigLevel3.code);
                        itemObj.put("l3Name", item.pConfigLevel3.name);
                        resultArray.put(itemObj);
                    } catch (JSONException e) {
                        TLog.error("reportExpectSugShow", e.getMessage());
                    }
                }
                sugResult = resultArray.toString();
            }
            AnalyticsFactory.create()
                    .action("action-expect-sug-expose")
                    .param("p", queryText)
                    .param("p2", sugResult)
                    .build();
        }

        private void reportExpectSugClick(@NonNull ServerJobSuggestBean suggestBean, int position) {
            // 1305.623 仅期望添加和编辑两个页面来源上报该埋点
            if (!isFromCreateOrEditExpect()) {
                return;
            }
            JSONObject clickObj = new JSONObject();
            try {
                clickObj.put("query", suggestBean.suggestName);
                clickObj.put("rank", position);
                clickObj.put("l3Code", suggestBean.config.code);
                clickObj.put("l3Name", suggestBean.config.name);
                AnalyticsFactory.create()
                        .action("action-expect-sug-click")
                        .param("p", suggestBean.query)
                        .param("p2", clickObj.toString())
                        .build();
            } catch (JSONException e) {
                TLog.error("reportExpectSugClick", e.getMessage());
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_three_level_position_pick_for_multi_expect);
        initIntent();
        initViews();
        getData();
    }

    private void initIntent() {
        Intent intent = getIntent();
        boolean isRegister = intent.getBooleanExtra(IS_REGISTER, false);
        int maxSelectAbleCount = intent.getIntExtra(MAX_SELECT_ABLE_COUNT, 0);
        if (maxSelectAbleCount > 0) {
            maxCount = maxSelectAbleCount;
        } else if (isRegister) {
            maxCount = MAX_COUNT;
        } else {
            maxCount = MAX_COUNT - GeekExpectManager.getGeekFullTimeExpectList().size();
            maxCount = maxCount <= 0 ? MAX_COUNT /* 兜底 */ : maxCount;
        }
        isInter = UserManager.isGeekIntern();
        changeType = intent.getIntExtra(BUNDLE_RESET_TYPE, -1);
        isFromTourist = intent.getBooleanExtra(KEY_FROM_TOURIST, false);
        hasJobIntent = intent.getBooleanExtra(KEY_HAS_JOBINTENT, false);

        Serializable extra = intent.getSerializableExtra(SELECTED_MULTI_POSITION_ITEM);
        if (extra instanceof ArrayList) {
            //noinspection unchecked
            ArrayList<LevelBean> list = (ArrayList<LevelBean>) extra;
            if (!LList.isEmpty(list)) {
                selectedMultiPositions.addAll(list);
            }
        }
    }

    private boolean isChecked(@NonNull LevelBean levelBean) {
        boolean isChecked = false;
        for (LevelBean selectedMultiPosition : selectedMultiPositions) {
            String name = selectedMultiPosition.name;
            long code = selectedMultiPosition.code;
            if (name == null || code <= 0) {
                continue;
            }

            if (code == levelBean.code && name.equals(levelBean.name)) {
                isChecked = true;
                break;
            }
        }
        return isChecked;
    }

    private void initViews() {
        final MultiExpectParams multiExpectParams = getMultiExpectParams();
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setBackClickListener();
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(this, R.color.app_green_dark));
        if (!isInSingleChoiceMode()) {
            titleView.setActionButtonListener(R.string.string_confirm, new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    saveMultiPositions();
                }
            });
        }
        tvSelectCount = findViewById(R.id.tv_select_count);

        tvSelectCount.setVisibility(isInSingleChoiceMode() ? View.GONE : View.VISIBLE);

        MTextView tvPageTitle = findViewById(R.id.tv_page_title);
        MTextView subTitleTv = findViewById(R.id.tv_page_sub_title);
        pb_loading = findViewById(R.id.pb_loading);
        mSearchView = findViewById(R.id.rl_search);
        customList = findViewById(R.id.lv_search0);
        mSearchView.setVisibility(isFromTourist ? View.GONE : View.VISIBLE);
        mSearchView.attachCustomList(customList);
        mSearchView.setInputString(getIntent().getStringExtra(KEY_INPUT_STRING));
        mSearchView.setStyleWith920(multiExpectParams != null && multiExpectParams.enable_920_688_style);
        mSearchView.setNeedDesc(multiExpectParams != null && multiExpectParams.isNeedDesc());
        mSearchView.setSearchHint(multiExpectParams == null ? "" : multiExpectParams.searchHint);

        if (!hasJobIntent) {
            mSearchView.requestFoucus();
        }
        positionLevelTreeView = findViewById(R.id.mLevelView);
        tipBar = findViewById(R.id.tip_bar);
        TipManager.Tip tip = new TipManager.Tip();
        tip.content = "暂无精准匹配职位，请在下方的类别中选择添加";
        tipBar.show(tip);
        tipBar.setVisibility(View.GONE);

        ThreeLevelRecommendForMultiExpectLayout mRecommendView = findViewById(R.id.mRecommendView);
        mRecommendView.setChangeType(changeType);
        mRecommendView.setOnRecommendCallback(new ThreeLevelRecommendForMultiExpectLayout.OnRecommendCallback() {
            @Override
            public void onLevelItemClick(LevelBean thirdLevel) {
                saveSinglePosition(thirdLevel, thirdLevel, thirdLevel, "", 0, false);
            }

            @Override
            public void onRecommendationLoaded(@NonNull List<LevelBean> sourceData) {
                if (LList.hasElement(sourceData)) {
                    recommendList.clear();
                    // 缓存数据
                    recommendList.addAll(sourceData);

                    if (hasSetData) {
                        // 添加推荐数据到首位
                        addRecommendList();
                    }
                }
            }
        });
        // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=155201252
        // 1005.603【策略】“为你推荐”职类展示优化
        // 全职用户，且至少有一个期望，请求“为你推荐”数据

        if (multiExpectParams != null && multiExpectParams.useNewCommend) {
            mRecommendView.loadNewRecommend(getCityIfAny(), multiExpectParams.inPartTimeJob ? 1 : 0);
        } else {
            mRecommendView.loadData();
        }

        mSearchView.setOnSuggestCallback(new OnSuggestCallback() {
            @Override
            public void onSuggestListener(boolean isEmpty) {
            }

            @Override
            public boolean inPartTimeJob() {
                return multiExpectParams != null && multiExpectParams.inPartTimeJob;
            }
        });

        AppBarLayout abl = findViewById(R.id.appBarLayout);
        abl.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            boolean collapsed = absOffset >= appBarLayout.getTotalScrollRange();
            //noinspection deprecation
            titleView.setDividerVisibility(collapsed ? View.VISIBLE : View.INVISIBLE);
            titleView.setTitle(collapsed ? tvPageTitle.getText() : "");
        });

        clSelectionPanel = findViewById(R.id.cl_selection_panel);
        rvSelections = findViewById(R.id.rv_selections);

        adapter = new ThreeLevelSelectionAdapter();
        adapter.setOnItemClickListener((adapter, view, position) -> {
            LevelBean bean = (LevelBean) adapter.getItem(position);
            if (bean != null) {
                removeItem(bean);
            }
        });
        rvSelections.setAdapter(adapter);

        // 默认标题
        tvPageTitle.setText("想找哪些工作？");

        String customTitle = getCustomTitle();

        if (!LText.empty(customTitle)) {
            tvPageTitle.setText(customTitle); // 支持调用方自定义标题
        }
        if (!LText.empty(getCustomSubTitle())) {
            subTitleTv.setVisibility(View.VISIBLE);
            subTitleTv.setText(getCustomSubTitle());
        } else {
            subTitleTv.setVisibility(View.GONE);
        }

        if (isFromInterviewQuestion()) {
            tvPageTitle.setText("面试职位是？");
        }

        if (multiExpectParams != null && multiExpectParams.isAutoPopSoftKeyboard()) {
            mSearchView.postDelayed(() -> mSearchView.showKeyBoard(), 500);
        }
    }

    @Nullable
    private MultiExpectParams getMultiExpectParams() {
        return (MultiExpectParams) getIntent().getSerializableExtra(KEY_MULTI_EXPECT_PARAMS);
    }

    //region 废弃，统一通过 getMultiExpectParams() 获取 MultiExpectParams 对象
    @Nullable
    private String getCustomTitle() {
        return getIntent().getStringExtra(KEY_CUSTOM_TITLE);
    }

    @Nullable
    private String getCustomSubTitle() {
        return getIntent().getStringExtra(KEY_CUSTOM_SUB_TITLE);
    }

    private boolean isInSingleChoiceMode() {
        return getIntent().getBooleanExtra(IN_SINGLE_CHOICE_MODE, false);
    }

    private boolean forAskColleaguesPost() {
        return getIntent().getBooleanExtra(FOR_ASK_COLLEAGUES_POST, false);
    }

    private boolean inPartTimeJob() {
        MultiExpectParams multiExpectParams = getMultiExpectParams();
        return multiExpectParams != null && multiExpectParams.inPartTimeJob;
    }

    private boolean isFromCreateOrEditExpect() {
        MultiExpectParams multiExpectParams = getMultiExpectParams();
        return multiExpectParams != null && (multiExpectParams.pageFrom == PageFrom.CREATE_EXPECT || multiExpectParams.pageFrom == PageFrom.EDIT_EXPECT);
    }

    private boolean isFromInterviewQuestion() {
        return getIntent().getBooleanExtra(KEY_FROM_INTERVIEW_QUESTION, false);
    }

    private boolean shouldGetServerData() {
        return getIntent().getBooleanExtra(KEY_GET_SERVER_DATA, false);
    }

    @Nullable
    private String getCityIfAny() {
        return getIntent().getStringExtra(KEY_USER_CITY);
    }
    //endregion

    private void getData() {
        pb_loading.setVisibility(View.VISIBLE);
        if (isFromTourist) {
            getLocalData();
        } else if (isInter) {
            getLocalData();
        } else if (shouldGetServerData()) {
            GetGeekExpectPositionRequest getGeekExpectPositionRequest = new GetGeekExpectPositionRequest(new ApiRequestCallback<GetGeekExpectPositionResponse>() {
                @Override
                public void onStart() {
                    super.onStart();
                    pb_loading.setVisibility(View.VISIBLE);
                }

                @Override
                public void handleInChildThread(ApiData<GetGeekExpectPositionResponse> data) {
                    super.handleInChildThread(data);
                    final List<LevelBean> position = data.resp.config;
                    if (LList.hasElement(position)) {
                        mPositionList.addAll(position);
                    } else {
                        saveClause();
                    }

                    if (getMultiExpectParams() != null && getMultiExpectParams().inPartTimeJob) { // 是否是兼职职位选择（需要挑选出兼职职位显示）
                        PositionUtil.pickPartTimeJobsThreeLevels(mPositionList);
                    }
                    if (data.resp != null) {
                        jobTitleRecommendType = data.resp.jobTitleRecommendType;
                        mSearchView.setJobTitleRecommendType(jobTitleRecommendType);
                    }
                }

                @Override
                public void onSuccess(ApiData<GetGeekExpectPositionResponse> data) {
                }

                @Override
                public void onComplete() {
                    pb_loading.setVisibility(View.GONE);
                    setData();
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    saveClause();
                }

                private void saveClause() {
                    //region 兜底数据（本地数据）
                    List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
                    mPositionList.addAll(basicData);
                    //endregion
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    ToastUtils.showText(reason.getErrReason());
                }
            });
            getGeekExpectPositionRequest.cityCode = getCityIfAny();
            getGeekExpectPositionRequest.execute();
        } else {
            GetAppPositionListRequest request = new GetAppPositionListRequest(new ApiRequestCallback<GetAppPositionListRsponse>() {
                @NonNull
                private final ArrayMap<Long, LevelBean> codeLevelMap = new ArrayMap<>();

                @Override
                public void handleInChildThread(ApiData<GetAppPositionListRsponse> data) {
                    super.handleInChildThread(data);
                    List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
                    codeLevelMap.clear();
                    for (LevelBean levelBean : basicData) {
                        codeLevelMap.put(levelBean.code, levelBean);
                    }

                    mPositionList.clear();
                    final List<LevelBean> orderedPositions = new ArrayList<>();
                    List<Long> positionCodes = data.resp.positionCodes;
                    if (LList.hasElement(positionCodes)) {
                        // 根据后台返回的一级类 code 顺序，排序本地基础数据
                        for (Long positionCode : positionCodes) {
                            LevelBean levelBean = codeLevelMap.get(positionCode);
                            codeLevelMap.remove(positionCode);
                            if (levelBean == null) continue;

                            orderedPositions.add(levelBean);
                        }

                        // 添加剩余的未排序的
                        if (!codeLevelMap.isEmpty()) {
                            Collection<LevelBean> remainingLevelBeans = codeLevelMap.values();
                            orderedPositions.addAll(remainingLevelBeans);
                        }

                        mPositionList.addAll(orderedPositions);
                    } else {
                        // 接口未返回排序规则，直接添加基础数据
                        mPositionList.addAll(basicData);
                    }
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    super.handleErrorInChildThread(reason);
                    // 接口未返回排序规则，直接添加基础数据
                    List<LevelBean> basicData = VersionAndDatasCommon.getInstance().getPositionList();
                    mPositionList.addAll(basicData);
                }

                @Override
                public void onSuccess(ApiData<GetAppPositionListRsponse> data) {

                }

                @Override
                public void onComplete() {
                    setData();
                    pb_loading.setVisibility(View.GONE);
                }

                @Override
                public void onFailed(ErrorReason reason) {

                }
            });
            // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=68420997
            request.city = getCityIfAny(); // 城市 code （807新增）
            request.execute();
        }
    }

    private void getLocalData() {
        AppThreadFactory.createThread(() -> {
            List<LevelBean> list;
            if (isInter) {
                list = VersionAndDatasCommon.getInstance().getInternPositionList();
            } else {
                list = VersionAndDatasCommon.getInstance().getPositionList();
            }
            // 过滤方案8中的【其他】入口
            if (isFromTourist) {
                Iterator<LevelBean> firstIt = list.iterator();
                while (firstIt.hasNext()) {
                    LevelBean item = firstIt.next();
                    if (item == null) continue;
                    if (!TextUtils.isEmpty(item.name) && item.name.contains(OTHER_POSITION_KEYWORD)) {
                        firstIt.remove();
                    }
                    if (!LList.isEmpty(item.subLevelModeList)) {
                        Iterator<LevelBean> secIt = item.subLevelModeList.iterator();
                        while (secIt.hasNext()) {
                            LevelBean secItem = secIt.next();
                            if (secItem == null) continue;
                            if (!TextUtils.isEmpty(secItem.name) && secItem.name.contains(OTHER_POSITION_KEYWORD)) {
                                secIt.remove();
                            }
                        }
                    }
                }
            }

            handler.obtainMessage(100, list).sendToTarget();
        }).start();
    }

    //牛人 注册流程排序
    private void checkGeekRegisterGraySort() {
        //灰度 注册流程 灰度牛人（蓝白都有）职类排序优化：
        //职类排序调整：最近一份工作经历填写的一级类职位放在第一位；
        if (WorkExpCompletionActivity.isUidInGray
                && WorkExpCompletionActivity.positionLv1Code != 0
                && UserManager.isGeekRole()
                && !UserManager.isInfoCompleteGeek(UserManager.getLoginUser())) {

            int index = -1;
            //查找要置顶的数据
            for (int i = 0; i < mPositionList.size(); i++) {
                LevelBean item = LList.getElement(mPositionList, i);
                if (item == null) continue;
                if (item.code == WorkExpCompletionActivity.positionLv1Code) {
                    index = i;
                    break;
                }
            }
            //插入到第一个位置
            if (index != -1) {
                LevelBean item = mPositionList.remove(index);
                mPositionList.add(0, item);
            }
        }
    }

    /**
     * 设置数据
     */
    private void setData() {
        //region 添加推荐数据到首位
        if (LList.hasElement(recommendList)) {
            addRecommendList();
        }
        //endregion

        positionLevelTreeView.setAdapter(mPositionList, selectedMultiPositions, onPositionLevelSelectListener);
        hasSetData = true;

        mSearchView.setMatchCallBack(matchCallBack);
        // 更新选中期望数目
        updateSelectCount();
        // 更新已选界面
        updateSelectionPanel(false);
    }

    private void addRecommendList() {
        boolean isNewStyle = false;
        if (LList.hasElement(recommendList)) {
            LevelBean firstItem = recommendList.get(0);
            if (LList.hasElement(firstItem.subLevelModeList)) {
                isNewStyle = true; // 二级结构，有标题
            }
        }

        // “为你推荐”模块
        final LevelBean recommend = new LevelBean(0, "为你推荐");
        if (isNewStyle) {
            // 二级结构
            recommend.subLevelModeList.addAll(recommendList);
        } else {
            // 一级结构
            // 无标题
            LevelBean subRecommend = new LevelBean(0, "");
            subRecommend.subLevelModeList.addAll(recommendList);
            recommend.subLevelModeList.add(subRecommend);
        }

        mPositionList.add(0, recommend);

        MultiExpectParams multiExpectParams = getMultiExpectParams();
        if (multiExpectParams != null) {
            int p2 = -1;
            if (multiExpectParams.isFromCreate()) {
                p2 = 3;
            } else if (multiExpectParams.isFromEdit()) {
                p2 = 2;
            }

            if (p2 > 0) {
                AnalyticsFactory.create().action(AnalyticsAction.ACTION_GEEK_EXPECT_SUG_EXP)
                        .param("p2", concatenateCodes(recommendList, isNewStyle)) // 推荐职类：position_code，用逗号隔开
                        .param("p7", p2) // 1、注册完善流程 2、修改期望职类信息 3、新增期望职类信息
                        .build();
            }
        }

        recommendList.clear();
    }

    @NonNull
    private String concatenateCodes(@NonNull List<LevelBean> recommendPositionList, boolean isNewStyle) {
        int size = recommendPositionList.size();

        JSONObject jo = new JSONObject();
        try {
            if (isNewStyle) {
                for (LevelBean item : recommendPositionList) {
                    if (item == null || LList.isEmpty(item.subLevelModeList)) continue;

                    StringBuilder subItemBuilder = new StringBuilder();
                    int subItemSize = item.subLevelModeList.size();
                    for (int j = 0; j < subItemSize; j++) {
                        LevelBean thirdLevel = item.subLevelModeList.get(j);
                        if (thirdLevel == null) continue;
                        subItemBuilder.append(thirdLevel.code);
                        if (!TextUtils.isEmpty(thirdLevel.value)) {
                            subItemBuilder.append(String.format("-%s", thirdLevel.value));
                        }
                        if (j < subItemSize - 1) {
                            subItemBuilder.append("+");
                        }
                    }
                    jo.put(item.name, subItemBuilder.toString());
                }
            } else {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < size; i++) {
                    LevelBean thirdLevel = recommendPositionList.get(i);
                    thirdLevel.setTagObj(1); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他
                    builder.append(thirdLevel.code);
                    if (i < size - 1) {
                        builder.append("+");
                    }
                }
                jo.put("为你推荐", builder.toString());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        TLog.print("ExpectSelect", "action = %s", jo.toString());
        return jo.toString();
    }

    private void onThirdLevelSelect0(LevelBean firstLevel, LevelBean secondLevel, LevelBean thirdLevel) {
        if (firstLevel == null || secondLevel == null || thirdLevel == null) {
            return;
        }

        // 点击已选中的，则移除该项目
        if (thirdLevel.isChecked() || isChecked(thirdLevel)) {
            removeItem(thirdLevel);

            AnalyticsFactory.create().action("geek-expect-click")
                    .param("p", firstLevel.code) // 选择点击的l1_code
                    .param("p2", secondLevel.code) // 选择点击的l2_code
                    .param("p3", thirdLevel.code) // 选择点击的l3_code
                    .param("p4", 2) // 1:选中 2:取消
                    .param("p5", 2) // 1:首善选择 2:非首善选择
                    .build();
            return;
        }

        // 判断是否已选满
        if (alreadyFull()) {
            ToastUtils.showText("已达上限");
            return;
        }

        // 1. 添加该选中项
        thirdLevel.setGrandParentLevel(firstLevel);
        thirdLevel.setParentLevel(secondLevel);
        addItem(thirdLevel);

        AnalyticsFactory.create().action("geek-expect-click")
                .param("p", firstLevel.code) // 选择点击的l1_code
                .param("p2", secondLevel.code) // 选择点击的l2_code
                .param("p3", thirdLevel.code) // 选择点击的l3_code
                .param("p4", 1) // 1:选中 2:取消
                .param("p5", 2) // 1:首善选择 2:非首善选择
                .build();

        if (forAskColleaguesPost()) {/* 邀请回答，直接返回选中项 */
            saveSinglePosition(firstLevel, secondLevel, thirdLevel, "", 0, false);
            return;
        }

        if (isInSingleChoiceMode()) {/* 单选模式下，直接返回选中项 */
            saveSinglePosition(firstLevel, secondLevel, thirdLevel, thirdLevel.reportPositionName, thirdLevel.reportPositionId, false);
        }
    }

    /**
     * 新增
     */
    private void addItem(@NonNull LevelBean thirdLevel) {
        selectedMultiPositions.add(thirdLevel);
        positionLevelTreeView.notifyDataSetChanged();
        updateSelectCount();
        updateSelectionPanel(true);
    }

    /**
     * 移除
     */
    private void removeItem(@NonNull LevelBean thirdLevel) {
        Iterator<LevelBean> it = selectedMultiPositions.iterator();
        while (it.hasNext()) {
            LevelBean item = it.next();
            if (item != null && item.code == thirdLevel.code) {
                it.remove();
                break;
            }
        }
        positionLevelTreeView.notifyDataSetChanged();
        updateSelectCount();
        updateSelectionPanel(false);
    }

    /**
     * 更新选中个数
     */
    private void updateSelectCount() {
        int selectCount = selectedMultiPositions.size();
        String countString = String.valueOf(selectCount);
        String totalString = countString + "/" + maxCount;
        SpannableStringBuilder builder = new SpannableStringBuilder(totalString);
        builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(ThreeLevelPositionPickForMultiExpectActivity.this, R.color.app_green_dark)),
                0, countString.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        tvSelectCount.setText(builder);
    }

    /**
     * 更新已选操作栏
     */
    private void updateSelectionPanel(boolean scrollToEnd) {
        int selectCount = selectedMultiPositions.size();
        if (selectCount == 0) {
            clSelectionPanel.setVisibility(View.GONE);
        } else {
            clSelectionPanel.setVisibility(isInSingleChoiceMode() ? View.GONE : View.VISIBLE);
            if (adapter != null) {
                adapter.setNewData(selectedMultiPositions);
                if (!isInSingleChoiceMode() && scrollToEnd) {
                    rvSelections.scrollToPosition(selectCount - 1);
                }
            }
        }
    }

    private boolean alreadyFull() { // 多选期望已选满
        return maxCount == selectedMultiPositions.size();
    }

    private void showMainList() {
        positionLevelTreeView.setVisibility(View.VISIBLE);
        customList.setVisibility(View.GONE);
    }

    private void showCustomList() {
        positionLevelTreeView.setVisibility(View.GONE);
        customList.setVisibility(View.VISIBLE);
    }

    @NonNull
    private String concatenateCheckedPosition() {
        List<String> selected = new ArrayList<>();
        for (LevelBean levelBean : selectedMultiPositions) {
            selected.add(String.valueOf(levelBean.code));
        }
        return StringUtil.connectTextWithChar(",", selected);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isClickSuggest = false;
    }

    /**
     * 多期望保存
     */
    private void saveMultiPositions() {
        if (selectedMultiPositions.isEmpty()) {
            ToastUtils.showText("请选择一个职类");
        } else {
            AppUtil.hideSoftInput(this);

            Intent intent = getIntent();
            intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
            setResult(RESULT_OK, intent);
            AppUtil.finishActivity(this);

            shouldReportExpectChoose(selectedMultiPositions.toArray(new LevelBean[0]));
        }
    }

    /**
     * 单期望保存
     */
    private void saveSinglePosition(LevelBean firstItem, LevelBean secondItem, LevelBean thirdItem, String nlpReportName, @SuppressWarnings("SameParameterValue") long reportedPositionId, boolean isNLPRecommend) {
        saveSinglePosition(MultiResultParams.obj()
                .setFirstItem(firstItem)
                .setSecondItem(secondItem)
                .setThirdItem(thirdItem)
                .setNlpReportName(nlpReportName)
                .setReportedPositionId(reportedPositionId)
                .setNLPRecommend(isNLPRecommend)
        );
    }

    private void saveSinglePosition(@NonNull MultiResultParams multiResultParams) {
        LevelBean firstItem = multiResultParams.getFirstItem();
        LevelBean secondItem = multiResultParams.getSecondItem();
        LevelBean thirdItem = multiResultParams.getThirdItem();
        int nlpReportNameIndex = multiResultParams.getNlpReportNameIndex();
        String nlpReportNameList = multiResultParams.getNlpReportNameList();
        long reportedPositionId = multiResultParams.getReportedPositionId();
        boolean isNLPRecommend = multiResultParams.isNLPRecommend();
        String nlpReportName = multiResultParams.getNlpReportName();

        if (firstItem == null || secondItem == null || thirdItem == null) {
            ToastUtils.showText("请选择一个职位");
        } else {
            AppUtil.hideSoftInput(this);

            selectedMultiPositions.clear();
            Intent intent = getIntent();
            // 搜索匹配
            intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
            intent.putExtra(SELECTED_FIRST_POSITION_ITEM, firstItem);
            intent.putExtra(SELECTED_SECOND_POSITION_ITEM, secondItem);
            intent.putExtra(SELECTED_THIRD_POSITION_ITEM, thirdItem);
            intent.putExtra(REPORTED_POSITION_ID, reportedPositionId);
            intent.putExtra(IS_NLP_RECOMMEND, isNLPRecommend);
            intent.putExtra(SELECTED_NLP_SUGGEST_POSITION, nlpReportName);
            intent.putExtra(ExpectConstants.SELECTED_NLP_SUGGEST_POSITION_INDEX, nlpReportNameIndex);
            intent.putExtra(ExpectConstants.SELECTED_NLP_SUGGEST_POSITION_LIST, nlpReportNameList); // 推荐期望名称 - 期望code 的list（中间以英文横杠连接），#&#分隔分隔
            intent.putExtra(MATCH_INPUT, mSearchView.getInputString());

            intent.putExtra(KEY_MULTI_RESULT_PARAMS, multiResultParams);

            setResult(RESULT_OK, intent);
            AppUtil.finishActivity(this);

            shouldReportExpectChoose(thirdItem);
        }
    }

    private void shouldReportExpectChoose(@NonNull LevelBean... thirdLevels) {
        MultiExpectParams multiExpectParams = getMultiExpectParams();
        if (multiExpectParams != null) {
            int p7 = 0;
            if (multiExpectParams.isFromCreate()) {
                p7 = 3;
            } else if (multiExpectParams.isFromEdit()) {
                p7 = 2;
            }
            if (p7 > 0) {
                reportGeekExpectSelect(String.valueOf(p7), new ArrayList<>(Arrays.asList(thirdLevels)));
            }
        }
    }

    public static void reportGeekExpectSelect(@NonNull String p7, @NonNull List<LevelBean> thirdLevels) {
        StringBuilder codeBuilder = new StringBuilder();
        StringBuilder valueBuilder = new StringBuilder();
        StringBuilder labelBuilder = new StringBuilder();
        StringBuilder l123NameBuilder = new StringBuilder();
        for (int i = 0; i < thirdLevels.size(); i++) {
            LevelBean thirdLevel = thirdLevels.get(i);
            codeBuilder.append(thirdLevel.code);
            if (!TextUtils.isEmpty(thirdLevel.value)) {
                codeBuilder.append(String.format("-%s", thirdLevel.value));
            }
            int label = 0;
            Object thirdLevelTag = thirdLevel.getTagObj();
            if (thirdLevelTag instanceof Integer) {
                label = ((Integer) thirdLevelTag);
            }
            labelBuilder.append(label);

            LevelBean firstLevel = thirdLevel.getGrandparentLevel();
            if (firstLevel != null) {
                l123NameBuilder.append(firstLevel.name).append("-");
            }

            LevelBean secondLevel = thirdLevel.getParentLevel();
            if (secondLevel != null) {
                l123NameBuilder.append(secondLevel.name).append("-");
            }

            l123NameBuilder.append(thirdLevel.name);

            if (i < thirdLevels.size() - 1) {
                codeBuilder.append(",");
                labelBuilder.append(",");
                if (valueBuilder.length() > 0) {
                    valueBuilder.append(",");
                }
                l123NameBuilder.append(";");
            }
        }

        AnalyticsFactory.create().action("geek-expect-select")
                .param("p", codeBuilder.toString()) // position_code(多个用逗号隔开)
                .param("p2", labelBuilder.toString()) // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他（多个用逗号隔开,且与actionp一一对应），4、搜索无结果下的推荐列表
                .param("p7", p7) // 1、注册完善流程 2、修改期望职类信息 3、 新增期望职类信息
                .param("p8", l123NameBuilder.toString()) // 当actionp2=1和2时，记录所选职类的一、二级标题，记录的格式：A标题-B标题-C标题，标题个数语actionp保持一致； 例：（互联网/AI-后端开发-JAVA；为你推荐-猜你想看-JAVA）
                .paramIfExist("p9", valueBuilder.toString(), valueBuilder.length() > 0).build();
    }

    private void reportOtherPosition(LevelBean firstItem, LevelBean secondItem, LevelBean thirdItem) {
        mTempFirstItem = firstItem;
        mTempSecondItem = secondItem;
        Intent intent = new Intent(this, ExpectPositionOtherActivity.class);
        intent.putExtra(Constants.DATA_ENTITY, thirdItem);
        intent.putExtra(Constants.DATA_BOOLEAN, false);
        AppUtil.startActivityForResult(this, intent, REQUEST_OTHER_POSITION, ActivityAnimType.UP_GLIDE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_OTHER_POSITION && resultCode == RESULT_OK) {
            long reportPositionId = data.getLongExtra(REPORTED_POSITION_ID, 0);
            String reportPositionName = data.getStringExtra(ExpectPositionOtherActivity.REPORT_POSITION_NAME);
            LevelBean thirdItem = (LevelBean) data.getSerializableExtra(SELECTED_THIRD_POSITION_ITEM);

            if (thirdItem != null) {
                thirdItem.setType(LevelBean.Type.TYPE_CUSTOM);
                thirdItem.reportPositionId = reportPositionId;
                thirdItem.reportPositionName = reportPositionName;

                if (!(thirdItem.getTagObj() instanceof Integer)) {
                    thirdItem.setTagObj(3); // 来源label：1：为你推荐/推荐职位 2：职类展示 3：搜索及其他
                }
            }

            onThirdLevelSelect0(mTempFirstItem, mTempSecondItem, thirdItem);
        }
    }

    /**
     * 统一使用该接口跳转职类选择页面！跳转参数封装到{@link MultiExpectParams}对象中。
     */
    public static void jumpForResult(@NonNull Context context, @NonNull MultiExpectParams params) {
        Intent intent = getIntent(context, params);
        AppUtil.startActivityForResult(context, intent, params.requestCode);
    }

    @NonNull
    public static Intent getIntent(@NonNull Context context, @NonNull MultiExpectParams params) {
        Intent intent = new Intent(context, ThreeLevelPositionPickForMultiExpectActivity.class);

        // 统一从对象中获取参数！
        intent.putExtra(KEY_MULTI_EXPECT_PARAMS, params);

        //region 废弃，统一从 KEY_MULTI_EXPECT_PARAMS 获取 MultiExpectParams 对象
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, params.selectedMultiPositions);
        intent.putExtra(IS_REGISTER, params.isRegister);
        intent.putExtra(KEY_USER_CITY, String.valueOf(params.cityCode));
        intent.putExtra(KEY_GET_SERVER_DATA, params.getServerData);
        intent.putExtra(IGNORE_BLUE_EXPECT_CHECK, params.ignoreBlueExpectCheck);
        intent.putExtra(IN_SINGLE_CHOICE_MODE, params.inSingleChoiceMode);
        intent.putExtra(FOR_ASK_COLLEAGUES_POST, params.forAskColleaguesPost);
        intent.putExtra(KEY_FROM_INTERVIEW_QUESTION, params.isInterviewQuestion);
        intent.putExtra(KEY_FROM_TOURIST, params.isFromTourist);
        intent.putExtra(KEY_HAS_JOBINTENT, params.hasChoseJobIntent);
        intent.putExtra(KEY_CUSTOM_TITLE, params.customTitle);
        intent.putExtra(KEY_CUSTOM_SUB_TITLE, params.subTitle);
        intent.putExtra(MAX_SELECT_ABLE_COUNT, params.maxSelectAbleCount);
        //endregion

        return intent;
    }

    @IntDef({PageFrom.CREATE_EXPECT, PageFrom.EDIT_EXPECT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PageFrom {
        int CREATE_EXPECT = 1;
        int EDIT_EXPECT = 2;
    }

    @SuppressWarnings("unused")
    public static class MultiExpectParams extends BaseEntity {

        private static final long serialVersionUID = -2021576221093654878L;

        int requestCode;

        boolean isRegister;
        @Nullable
        ArrayList<LevelBean> selectedMultiPositions;
        boolean ignoreBlueExpectCheck;
        long cityCode;
        boolean getServerData;
        boolean inSingleChoiceMode;
        boolean isFromTourist;
        boolean isFromCreate;
        boolean isFromEdit;

        boolean isInterviewQuestion;
        boolean forAskColleaguesPost;

        boolean hasChoseJobIntent;

        String customTitle;

        /**
         * 副标题。 个人主页 修改职位 会修改职位 并且显示副标题
         */
        String subTitle;
        String searchHint; //搜索提示

        boolean autoPopSoftKeyboard;

        boolean enable_920_688_style;
        /*最大可选择的数量*/
        public int maxSelectAbleCount;

        boolean inPartTimeJob;

        boolean useNewCommend;

        boolean needDesc;
        // 是否直接使用【其他xx职位】，默认是不使用，需要跳转到其他职位搜索页进行搜索和自定义
        boolean directUseOther;

        JobIntentBean expect;

        @PageFrom
        int pageFrom; // 页面来源（区分场景做差异化）

        public boolean isAutoPopSoftKeyboard() {
            return autoPopSoftKeyboard;
        }

        public MultiExpectParams setAutoPopSoftKeyboard(boolean autoPopSoftKeyboard) {
            this.autoPopSoftKeyboard = autoPopSoftKeyboard;
            return this;
        }

        public String getSubTitle() {
            return subTitle;
        }

        public MultiExpectParams setSubTitle(String subTitle) {
            this.subTitle = subTitle;
            return this;
        }

        private MultiExpectParams() {

        }

        public static MultiExpectParams obj() {
            return new MultiExpectParams();
        }

        public MultiExpectParams requestCode(int requestCode) {
            this.requestCode = requestCode;
            return this;
        }

        public MultiExpectParams hasChoseJobIntent(boolean hasChoseJobIntent) {
            this.hasChoseJobIntent = hasChoseJobIntent;
            return this;
        }

        public MultiExpectParams setIsInterviewQuestion(boolean isInterviewQuestion) {
            this.isInterviewQuestion = isInterviewQuestion;
            return this;
        }

        public MultiExpectParams isRegister(boolean isRegister) {
            this.isRegister = isRegister;
            return this;
        }

        public MultiExpectParams selectedMultiPositions(@Nullable
                                                        ArrayList<LevelBean> selectedMultiPositions) {
            this.selectedMultiPositions = selectedMultiPositions;
            return this;
        }

        public MultiExpectParams ignoreBlueExpectCheck(boolean ignoreBlueExpectCheck) {
            this.ignoreBlueExpectCheck = ignoreBlueExpectCheck;
            return this;
        }

        public MultiExpectParams cityCode(long cityCode) {
            this.cityCode = cityCode;
            return this;
        }

        public MultiExpectParams getServerData(boolean getServerData) {
            this.getServerData = getServerData;
            return this;
        }

        public MultiExpectParams inSingleChoiceMode(boolean inSingleChoiceMode) {
            this.inSingleChoiceMode = inSingleChoiceMode;
            return this;
        }

        public MultiExpectParams forAskColleaguesPost(boolean forAskColleaguesPost) {
            this.forAskColleaguesPost = forAskColleaguesPost;
            return this;
        }

        public String getSearchHint() {
            return searchHint;
        }

        public MultiExpectParams setSearchHint(String searchHint) {
            this.searchHint = searchHint;
            return this;
        }

        public MultiExpectParams setFromTourist(boolean isFromTourist) {
            this.isFromTourist = isFromTourist;
            return this;
        }

        public MultiExpectParams setCustomTitle(String customTitle) {
            this.customTitle = customTitle;
            return this;
        }

        public MultiExpectParams setEnable_920_688_style(boolean enable_920_688_style) {
            this.enable_920_688_style = enable_920_688_style;
            return this;
        }

        public MultiExpectParams setMaxSelectAbleCount(int maxSelectAbleCount) {
            this.maxSelectAbleCount = maxSelectAbleCount;
            return this;
        }

        public MultiExpectParams inPartTimeJob(boolean inPartTimeJob) {
            this.inPartTimeJob = inPartTimeJob;
            return this;
        }

        public MultiExpectParams useNewCommend(boolean useNewCommend) {
            this.useNewCommend = useNewCommend;
            return this;
        }

        public boolean isNeedDesc() {
            return needDesc;
        }

        public MultiExpectParams setNeedDesc(boolean needDesc) {
            this.needDesc = needDesc;
            return this;
        }

        public boolean isFromCreate() {
            return isFromCreate;
        }

        public MultiExpectParams setFromCreate(boolean fromCreate) {
            isFromCreate = fromCreate;
            return this;
        }

        public boolean isFromEdit() {
            return isFromEdit;
        }

        public MultiExpectParams setFromEdit(boolean fromEdit) {
            isFromEdit = fromEdit;
            return this;
        }

        public MultiExpectParams setDirectUseOther(boolean directUseOther) {
            this.directUseOther = directUseOther;
            return this;
        }

        public JobIntentBean getExpect() {
            return expect;
        }

        public MultiExpectParams setExpect(JobIntentBean expect) {
            this.expect = expect;
            return this;
        }

        public MultiExpectParams setPageFrom(@PageFrom int pageFrom) {
            this.pageFrom = pageFrom;
            return this;
        }

        public int getPageFrom() {
            return pageFrom;
        }
    }

    public static class MultiResultParams extends BaseEntity {

        private static final long serialVersionUID = -4383038596997733481L;

        private LevelBean firstItem;
        private LevelBean secondItem;
        private LevelBean thirdItem;
        private String nlpReportName;
        private int nlpReportNameIndex = -1;
        private String nlpReportNameList;
        private long reportedPositionId;
        private boolean isNLPRecommend;
        private String suggestName;

        private MultiResultParams() {
        }

        public static MultiResultParams obj() {
            return new MultiResultParams();
        }

        public LevelBean getFirstItem() {
            return firstItem;
        }

        public MultiResultParams setFirstItem(LevelBean firstItem) {
            this.firstItem = firstItem;
            return this;
        }

        public LevelBean getSecondItem() {
            return secondItem;
        }

        public MultiResultParams setSecondItem(LevelBean secondItem) {
            this.secondItem = secondItem;
            return this;
        }

        public LevelBean getThirdItem() {
            return thirdItem;
        }

        public MultiResultParams setThirdItem(LevelBean thirdItem) {
            this.thirdItem = thirdItem;
            return this;
        }

        public String getNlpReportName() {
            return nlpReportName;
        }

        public MultiResultParams setNlpReportName(String nlpReportName) {
            this.nlpReportName = nlpReportName;
            return this;
        }

        public int getNlpReportNameIndex() {
            return nlpReportNameIndex;
        }

        public MultiResultParams setNlpReportNameIndex(int nlpReportNameIndex) {
            this.nlpReportNameIndex = nlpReportNameIndex;
            return this;
        }

        public String getNlpReportNameList() {
            return nlpReportNameList;
        }

        public MultiResultParams setNlpReportNameList(String nlpReportNameList) {
            this.nlpReportNameList = nlpReportNameList;
            return this;
        }

        public long getReportedPositionId() {
            return reportedPositionId;
        }

        public MultiResultParams setReportedPositionId(long reportedPositionId) {
            this.reportedPositionId = reportedPositionId;
            return this;
        }

        public boolean isNLPRecommend() {
            return isNLPRecommend;
        }

        public MultiResultParams setNLPRecommend(boolean isNLPRecommend) {
            this.isNLPRecommend = isNLPRecommend;
            return this;
        }

        public MultiResultParams setSuggestName(String suggestName) {
            this.suggestName = suggestName;
            return this;
        }

        public String getSuggestName() {
            return suggestName;
        }
    }

}