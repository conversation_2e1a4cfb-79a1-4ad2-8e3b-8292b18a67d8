package com.hpbr.bosszhipin.module.common;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.register.geek.WorkExpCompletionActivity;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectService;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.monch.lbase.util.LList;
import com.sankuai.waimai.router.Router;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.BlueRecommandResponse;
import net.bosszhipin.api.GetGeekWorkExpRecommendPositionRequest;
import net.bosszhipin.api.GetGeekWorkExpRecommendPositionResponse;
import net.bosszhipin.api.GetRecommendPositionBatchRequest;
import net.bosszhipin.api.GetRecommendPositionBatchResponse;
import net.bosszhipin.api.RecommendCareerPositionResponse;
import net.bosszhipin.api.RegisterRecommendRequest;
import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/5/19
 */
public class ThreeLevelRecommendForMultiExpectLayout extends FrameLayout {
    private final Context context;
    private static final String TYPE_FIRST_COMPLETION = "0";
    private static final String TYPE_OTHER = "1";

    private LinearLayoutManager layoutManager;
    private RecyclerView rvRecommend;

    public ThreeLevelRecommendForMultiExpectLayout(@NonNull Context context) {
        this(context, null);
    }

    public ThreeLevelRecommendForMultiExpectLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ThreeLevelRecommendForMultiExpectLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        init();
    }

    //业务场景比较复杂，这个是 实习生切换社招场景需要请求 推荐列表
    private int changeType;

    private final List<LevelBean> sourceData = new ArrayList<>();
    private RecommendAdapter adapter;

    private ConstraintLayout clParent;

    @Nullable
    private OnRecommendCallback onRecommendCallback;

    public void setOnRecommendCallback(OnRecommendCallback onRecommendCallback) {
        this.onRecommendCallback = onRecommendCallback;
    }

    private void init() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_three_level_recommend_for_multi_expect, this);
        clParent = view.findViewById(R.id.cl_parent);
        rvRecommend = view.findViewById(R.id.rv_recommend);
        layoutManager = new LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false);
        rvRecommend.setLayoutManager(layoutManager);
        adapter = new RecommendAdapter();
        adapter.setOnItemClickListener((adapter, view1, position) -> {
            LevelBean item = (LevelBean) adapter.getItem(position);
            if (item != null && onRecommendCallback != null) {
                onRecommendCallback.onLevelItemClick(item);
            }
        });

        rvRecommend.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private boolean hasReport = false;

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING && !hasReport) {
                    AnalyticsFactory.create().action("interes-recommend-slide").build();
                    hasReport = true;
                }
            }
        });

        rvRecommend.setAdapter(adapter);
    }

    public void setChangeType(int changeType) {
        this.changeType = changeType;
    }

    public void loadNewRecommend(@Nullable String cityCodes, int positionType) {
        if (UserManager.isStudentRecord()) {
            // 仅职场人身份才有职类推荐（12.12版本增加身份限制 -- 张一弛）
            return;
        }
        // https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=155201252
        // 1005.603【策略】“为你推荐”职类展示优化
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPGEEK_APP_GEEK_EXPECTPOSITION_RECOMMENDCAREERPOSITION)
                .addParam("cityCode", cityCodes) // 用户选择的二级城市code，用于职类推荐
                .addParam("positionType", positionType) // 0-全职 1-兼职
                .setRequestCallback(new SimpleApiRequestCallback<RecommendCareerPositionResponse>() {
                    @Override
                    public void onSuccess(ApiData<RecommendCareerPositionResponse> data) {
                        super.onSuccess(data);
                        loadRecommend(data.resp);
                    }

                    private void loadRecommend(@NonNull RecommendCareerPositionResponse resp) {
                        if (ActivityUtils.isValid((Activity) getContext())) {
                            List<LevelBean> rpList = resp.recommendPositionList;
                            final List<LevelBean> rcList = new ArrayList<>();
                            if (!LList.isEmpty(rpList)) {
                                rcList.addAll(rpList);
                            }

                            sourceData.clear();
                            if (!LList.isEmpty(rcList)) {
                                sourceData.addAll(rcList);
                            }
                            refreshAdapter();

                            if (onRecommendCallback != null && LList.hasElement(sourceData)) {
                                onRecommendCallback.onRecommendationLoaded(sourceData);

                                refreshVisibility(false); // 不再显示推荐条，“为你推荐”数据显示在职类列表上
                            }
                        }
                    }
                })
                .execute();
    }

    public void loadData() {
        /*
         * 学生身份下的老完善流程、
         * 编辑流程，
         * 职场人学生身份互切流程，
         * 都不再调用推荐职类接口.
         */

        //职场人学生身份互切流程
        if (changeType > 0) {
            return;
        }

        //学生身份下的老完善流程、编辑流程
        if (UserManager.isStudentRecord()) {
            return;
        }

        //蓝领职位
        boolean isRegisterBlue = UserManager.isGeekRole()
                && UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                && WorkExpCompletionActivity.isBlueCollarPosition;

        if (isRegisterBlue) {
            getBlueSuggest();
        }
    }

    @NonNull
    private String getType() {
        //未完善信息或者没有求职意向
        if (!UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                || !UserManager.isMoreInfoCompleteGeek(UserManager.getLoginUser())) {
            return TYPE_FIRST_COMPLETION; // 用户完善信息
        }
        return TYPE_OTHER;
    }

    /**
     * 蓝领获得推荐关键字
     */
    private void getBlueSuggest() {
        RegisterRecommendRequest registerRecommendRequest = new RegisterRecommendRequest();
        registerRecommendRequest.type = getType();

        GetGeekWorkExpRecommendPositionRequest getGeekWorkExpRecommendPositionRequest = new GetGeekWorkExpRecommendPositionRequest();
        getGeekWorkExpRecommendPositionRequest.type = TYPE_FIRST_COMPLETION; // 默认值

        GetRecommendPositionBatchRequest batchRequest = new GetRecommendPositionBatchRequest(new ApiRequestCallback<GetRecommendPositionBatchResponse>() {
            @Override
            public void onSuccess(ApiData<GetRecommendPositionBatchResponse> data) {
                Context context = getContext();
                if (context instanceof BaseActivity) {
                    if (((BaseActivity) context).isDestroy) {
                        return;
                    }

                    GetRecommendPositionBatchResponse resp = data.resp;
                    GetGeekWorkExpRecommendPositionResponse getGeekWorkExpRecommendPositionResponse = resp.getGeekWorkExpRecommendPositionResponse;
                    BlueRecommandResponse blueRecommandResponse = resp.blueRecommandResponse;

                    final List<LevelBean> rcList = new ArrayList<>();
                    if (getGeekWorkExpRecommendPositionResponse != null
                            && !LList.isEmpty(getGeekWorkExpRecommendPositionResponse.recommendPositions)) {
                        List<LevelBean> recommendPositions = getGeekWorkExpRecommendPositionResponse.recommendPositions;
                        if (!LList.isEmpty(recommendPositions)) {
                            rcList.addAll(recommendPositions);
                        }
                    } else {
                        //蓝领职位
                        boolean isRegisterBlue = UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                                && WorkExpCompletionActivity.isBlueCollarPosition;
                        //求职意向管理界面进入
                        boolean isFromJobIntentManager = isFromJobIntentManager();

                        if (isRegisterBlue || isFromJobIntentManager) {
                            List<LevelBean> rpList = convertToLevelBean(blueRecommandResponse.recommendPositionList);
                            if (!LList.isEmpty(rpList)) {
                                rcList.addAll(rpList);
                            }
                        }
                    }

                    sourceData.clear();
                    if (!LList.isEmpty(rcList)) {
                        sourceData.addAll(rcList);
                    }
                    refreshAdapter();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        batchRequest.registerRecommendRequest = registerRecommendRequest;
        batchRequest.execute();
    }

    private boolean isFromJobIntentManager() {
        GeekExpectService expectService = Router.getService(GeekExpectService.class, GeekExpectConstants.KEY_GEEK_EXPECT_SERVICE);
        return expectService != null && expectService.isGeekJobIntentManageActivityAlive();
    }

    //转化为通用数据
    @NonNull
    private List<LevelBean> convertToLevelBean(List<CodeNameFlagBean> positionList) {
        final List<LevelBean> result = new ArrayList<>();
        if (positionList != null) {
            for (CodeNameFlagBean item : positionList) {
                if (item == null) continue;
                LevelBean levelBean = new LevelBean();
                levelBean.code = item.code;
                levelBean.name = item.name;
                result.add(levelBean);
            }
        }
        return result;
    }

    public void refreshVisibility(boolean visibility) {
        boolean hasData = !LList.isEmpty(sourceData);
        if (visibility && hasData) {
            clParent.setVisibility(VISIBLE);
        } else {
            clParent.setVisibility(GONE);
        }
    }

    private void refreshAdapter() {
        if (adapter != null) {
            adapter.setNewData(sourceData);
        }
        if (LList.isEmpty(sourceData)) {
            clParent.setVisibility(View.GONE);
        } else {
            clParent.setVisibility(View.VISIBLE);

            rvRecommend.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    if (clParent.getVisibility() == View.VISIBLE) {
                        JSONArray pArr = new JSONArray();
                        JSONArray p2Arr = new JSONArray();
                        final int position = layoutManager.findLastCompletelyVisibleItemPosition();
                        if (position >= 0 && position < sourceData.size()) {
                            for (int i = 0; i < sourceData.size(); i++) {
                                final long code = sourceData.get(i).code;
                                if (i <= position) {
                                    pArr.put(code);
                                } else {
                                    p2Arr.put(code);
                                }
                            }
                        }

                        if (pArr.length() > 0) {
                            AnalyticsFactory.create()
                                    .action("interes-recommend-expo")
                                    .param("p", pArr.toString()) // 已曝光的职类code（array形式，中间用逗号隔开）
                                    .param("p2", p2Arr.toString()) // 没曝光的职类code（array形式，中间用逗号隔开）
                                    .build();
                        }
                    }

                    rvRecommend.getViewTreeObserver().removeOnPreDrawListener(this);
                    return true;
                }
            });
        }
    }

    static class RecommendAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

        public RecommendAdapter() {
            this(null);
        }

        public RecommendAdapter(@Nullable List<LevelBean> data) {
            super(R.layout.item_three_level_recommend_word, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
            helper.setText(R.id.btn_word, item.name);
        }
    }

    public interface OnRecommendCallback {
        void onLevelItemClick(LevelBean thirdLevel);

        void onRecommendationLoaded(@NonNull List<LevelBean> sourceData);
    }
}
