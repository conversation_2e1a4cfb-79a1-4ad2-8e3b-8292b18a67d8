package com.hpbr.bosszhipin.module.common;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.google.android.flexbox.FlexLine;
import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.register.geek.WorkExpCompletionActivity;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectService;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.sankuai.waimai.router.Router;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.BlueRecommandResponse;
import net.bosszhipin.api.GetGeekWorkExpRecommendPositionRequest;
import net.bosszhipin.api.GetGeekWorkExpRecommendPositionResponse;
import net.bosszhipin.api.GetRecommendPositionBatchRequest;
import net.bosszhipin.api.GetRecommendPositionBatchResponse;
import net.bosszhipin.api.RegisterRecommendRequest;
import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2019-10-16
 */
public class ThreeLevelRecommendLayout extends LinearLayout {

    private static final String TYPE_FIRST_COMPLETION = "0";
    private static final String TYPE_OTHER = "1";

    private boolean isDzSug; // 是否是推荐的店长工作经历
    private OnRecommendItemCallBack callBack;
    private FlexboxLayout flexBoxLayout;
    //业务场景比较复杂，这个是 实习生切换社招场景需要请求 推荐列表
    private int changeType;
    @NonNull
    private final List<LevelBean> recommandList = new ArrayList<>();
    private int index = 0;
    private boolean fromWorkExpCompletion;
    @NonNull
    private final Runnable nextLine = new Runnable() {
        @Override
        public void run() {
            index++;
            List<FlexLine> flexLines = flexBoxLayout.getFlexLines();
            int count = LList.getCount(flexLines);
            if (count > 2) {
                //删除第三行
                flexBoxLayout.removeViewAt(flexBoxLayout.getChildCount() - 1);
                //删除第二行 最后一个 让 预留给更多
                flexBoxLayout.removeViewAt(flexBoxLayout.getChildCount() - 1);

                //添加更多按钮
                @SuppressLint("InflateParams") CheckBox moreView = (CheckBox) LayoutInflater.from(getContext()).inflate(R.layout.intern_suggest_view, null);
                moreView.setText("更多");
                moreView.setTextColor(ContextCompat.getColor(getContext(), R.color.app_green));
                LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
                params.rightMargin = Scale.dip2px(getContext(), 5);
                params.topMargin = Scale.dip2px(getContext(), 5);
                moreView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showAllCommend();
                    }
                });
                flexBoxLayout.addView(moreView, flexBoxLayout.getChildCount(), params);
                return;
            }
            addItemView();
            flexBoxLayout.post(nextLine);
        }
    };

    public ThreeLevelRecommendLayout(Context context) {
        super(context);
        initView(context);
    }

    public ThreeLevelRecommendLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    public ThreeLevelRecommendLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }

    private void initView(Context context) {
        @SuppressLint("InflateParams") View view = LayoutInflater.from(context).inflate(R.layout.view_three_level_recommand, null);
        flexBoxLayout = view.findViewById(R.id.flexBoxLayout);
        addView(view);
        setVisibility(GONE);
    }

    public void setChangeType(int changeType) {
        this.changeType = changeType;
    }

    //加载推荐列表
    public void loadRecommend() {
        //默认0；1-应届、社招 切 实习生；2-实习生 切 应届、社招

        /**
         * 学生身份下的老完善流程、
         * 编辑流程，
         * 职场人学生身份互切流程，
         * 都不再调用推荐职类接口.
         */

        //职场人学生身份互切流程
        if (changeType > 0) {
            return;
        }

        //学生身份下的老完善流程、编辑流程
        if (UserManager.isStudentRecord()) {
            return;
        }


        //职场人-蓝领职位
        boolean isRegisterBlue = UserManager.isGeekRole()
                && UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                && WorkExpCompletionActivity.isBlueCollarPosition;

        //求职意向管理界面进入
        boolean isFromJobIntentManager = isFromJobIntentManager();

        if (isRegisterBlue
                || isFromJobIntentManager
                || fromWorkExpCompletion /* 工作经历-选择职位 */) {
            getBlueSuggest();
        }
    }

    /**
     * 蓝领获得推荐关键字
     */
    private void getBlueSuggest() {
        RegisterRecommendRequest registerRecommendRequest = new RegisterRecommendRequest();
        registerRecommendRequest.type = getType();

        GetGeekWorkExpRecommendPositionRequest getGeekWorkExpRecommendPositionRequest = new GetGeekWorkExpRecommendPositionRequest();
        getGeekWorkExpRecommendPositionRequest.type = TYPE_FIRST_COMPLETION; // 默认值

        GetRecommendPositionBatchRequest batchRequest = new GetRecommendPositionBatchRequest(new ApiRequestCallback<GetRecommendPositionBatchResponse>() {
            @Override
            public void onSuccess(ApiData<GetRecommendPositionBatchResponse> data) {
                Context context = getContext();
                if (context instanceof BaseActivity) {
                    if (((BaseActivity) context).isDestroy) {
                        return;
                    }

                    GetRecommendPositionBatchResponse resp = data.resp;
                    GetGeekWorkExpRecommendPositionResponse getGeekWorkExpRecommendPositionResponse = resp.getGeekWorkExpRecommendPositionResponse;
                    BlueRecommandResponse blueRecommandResponse = resp.blueRecommandResponse;

                    isDzSug = false;
                    final List<LevelBean> rcList = new ArrayList<>();
                    if (getGeekWorkExpRecommendPositionResponse != null
                            && !LList.isEmpty(getGeekWorkExpRecommendPositionResponse.recommendPositions)) {
                        List<LevelBean> recommendPositions = getGeekWorkExpRecommendPositionResponse.recommendPositions;
                        if (!LList.isEmpty(recommendPositions)) {
                            rcList.addAll(recommendPositions);
                            isDzSug = true;
                        }
                    } else {
                        //蓝领职位
                        boolean isRegisterBlue = UserManager.isGeekRole()
                                && UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                                && WorkExpCompletionActivity.isBlueCollarPosition;
                        //求职意向管理界面进入
                        boolean isFromJobIntentManager = isFromJobIntentManager();

                        if (isRegisterBlue || isFromJobIntentManager) {
                            List<LevelBean> rpList = convertToLevelBean(blueRecommandResponse.recommendPositionList);
                            if (!LList.isEmpty(rpList)) {
                                rcList.addAll(rpList);
                            }
                        }
                    }

                    recommandList.clear();
                    if (!LList.isEmpty(rcList)) {
                        recommandList.addAll(rcList);
                    }

                    //显示推荐列表
                    showMaxTwoLineCommend();
                    //检测显示隐藏
                    checkVisibility();
                }
            }

            //转化为通用数据
            private List<LevelBean> convertToLevelBean(List<CodeNameFlagBean> positionList) {
                final List<LevelBean> result = new ArrayList<>();
                if (positionList != null) {
                    for (CodeNameFlagBean item : positionList) {
                        if (item == null) continue;
                        LevelBean levelBean = new LevelBean();
                        levelBean.code = item.code;
                        levelBean.name = item.name;
                        result.add(levelBean);
                    }
                }
                return result;
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        batchRequest.registerRecommendRequest = registerRecommendRequest;
        if (fromWorkExpCompletion) { // 只有首善『工作经历-选择职位』才发起店长推荐职位请求
            batchRequest.getGeekWorkExpRecommendPositionRequest = getGeekWorkExpRecommendPositionRequest;
        }
        batchRequest.execute();
    }

    private boolean isFromJobIntentManager() {
        GeekExpectService expectService = Router.getService(GeekExpectService.class, GeekExpectConstants.KEY_GEEK_EXPECT_SERVICE);
        return expectService != null && expectService.isGeekJobIntentManageActivityAlive();
    }

    @NonNull
    private String getType() {
        //未完善信息或者没有求职意向
        if (!UserManager.isBasicInfoCompleteGeek(UserManager.getLoginUser())
                || !UserManager.isMoreInfoCompleteGeek(UserManager.getLoginUser())) {
            return TYPE_FIRST_COMPLETION;
        }
        return TYPE_OTHER;
    }

    //显示推荐列表
    private void showMaxTwoLineCommend() {
        addItemView();
        flexBoxLayout.post(nextLine);
    }

    private void checkVisibility() {
        if (LList.getCount(recommandList) > 0) {
            setVisibility(View.VISIBLE);
        } else {
            setVisibility(View.GONE);
        }
    }

    public void refreshVisibility(boolean visibility) {
        boolean hasData = !LList.isEmpty(recommandList);
        if (visibility && hasData) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    //显示全部推荐标签
    private void showAllCommend() {
        flexBoxLayout.removeAllViews();
        for (LevelBean levelBean : recommandList) {
            @SuppressLint("InflateParams") CheckBox itemView = (CheckBox) LayoutInflater.from(getContext()).inflate(R.layout.intern_suggest_view, null);
            itemView.setText(levelBean.name);
            itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callBack != null) {
                        callBack.onRecommendItemClickListener(levelBean);
                    }
                    ThreeLevelPositionPickActivity.isClickSuggest = true;
                }
            });
            LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            params.rightMargin = Scale.dip2px(getContext(), 10);
            params.topMargin = Scale.dip2px(getContext(), 15);
            flexBoxLayout.addView(itemView, flexBoxLayout.getChildCount(), params);
        }
    }

    private void addItemView() {
        LevelBean item = LList.getElement(recommandList, index);
        if (item != null) {
            @SuppressLint("InflateParams") CheckBox itemView = (CheckBox) LayoutInflater.from(getContext()).inflate(R.layout.intern_suggest_view, null);
            itemView.setText(item.name);
            itemView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callBack != null) {
                        //非蓝领
                        if (!WorkExpCompletionActivity.isBlueCollarPosition && UserManager.isGeekRole()) {
                            AnalyticsFactory.create().action(AnalyticsAction.ACTION_LIFECYCLE_COMPLETE_EXPECT_CLICK_TAG)
                                    .param("p", item.code)
                                    .build();
                        }
                        callBack.onRecommendItemClickListener(item);

                        // 首次完善工作经历时选择职位类型-推荐店长工作经历点击（804）
                        if (isDzSug) {
                            AnalyticsFactory.create()
                                    .action(AnalyticsAction.ACTION_LIFECYCLE_COMPLETE_RECO_DZ_WORK_CLICK)
                                    .param("p", item.code)
                                    .build();
                        }
                    }
                    ThreeLevelPositionPickActivity.isClickSuggest = true;
                }
            });
            LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
            params.rightMargin = Scale.dip2px(getContext(), 5);
            params.topMargin = Scale.dip2px(getContext(), 5);
            flexBoxLayout.addView(itemView, flexBoxLayout.getChildCount(), params);
        }
    }

    public void setCallBack(OnRecommendItemCallBack callBack) {
        this.callBack = callBack;
    }

    public void setFromWorkExpCompletion(boolean fromWorkExpCompletion) {
        this.fromWorkExpCompletion = fromWorkExpCompletion;
    }

    //点击推荐列表回掉接口
    public interface OnRecommendItemCallBack {
        void onRecommendItemClickListener(LevelBean item);
    }

}
