package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.common.adapter.BaseSimpleMultipleItemRvAdapter;
import com.hpbr.bosszhipin.module.common.bean.BelongIndustryBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.common.provider.addworkexp.BelongIndustryProvider;
import com.hpbr.bosszhipin.module.common.provider.addworkexp.CompanyNameProvider;
import com.hpbr.bosszhipin.module.common.provider.addworkexp.OnTheJobTimeProvider;
import com.hpbr.bosszhipin.module.common.provider.addworkexp.PositionNameProvider;
import com.hpbr.bosszhipin.module.common.provider.addworkexp.WorkContentProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

/**
 * @ClassName ：AddNewWorkExpAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  2:22 PM
 */
public class AddNewWorkExpAdapter extends BaseSimpleMultipleItemRvAdapter<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private EventListener eventListener;

    public AddNewWorkExpAdapter(Context context, EventListener eventListener) {
        super(null);
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    protected void registerItemProvider() {
        /*公司名称*/
        registerProviders(new CompanyNameProvider(context, eventListener));
        /*在职时间*/
        registerProviders(new OnTheJobTimeProvider(context, eventListener));
        /*工作内容*/
        registerProviders(new WorkContentProvider(context, eventListener));
        /*所在行业*/
        registerProviders(new BelongIndustryProvider(context, eventListener));
        /*职位名称*/
        registerProviders(new PositionNameProvider(context, eventListener));
    }

    public interface EventListener {

        /**
         * 点击公司名称
         */
        void onClickCompanyName(String companyName);

        /**
         * 点击开始时间
         *
         * @param startDate
         */
        void onClickStartTime(int startDate);

        /**
         * 点击结束时间
         *
         * @param endDate
         */
        void onClickEndTime(int endDate);

        /**
         * 点击「工作内容」
         *
         * @param workContent
         */
        void onClickWorkContent(String workContent);

        /**
         * 点击「所在行业」
         *
         * @param belongIndustryBean
         */
        void onClickBelongIndustry(@NonNull BelongIndustryBean belongIndustryBean);

        /**
         * 点击「职位名称」
         */
        void onClickPositionName();
    }
}
