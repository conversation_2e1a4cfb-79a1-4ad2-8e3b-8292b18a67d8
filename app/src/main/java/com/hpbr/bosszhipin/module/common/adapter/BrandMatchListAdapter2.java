package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteIndexBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.bean.HighlightItem;
import net.bosszhipin.api.bean.ServerBrandSuggestBean;
import net.bosszhipin.api.bean.SuggestHighlightItemBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 作者:郭峰
 * 日期:2016/4/6
 * 品牌高量搜索适配器样式
 */
public class BrandMatchListAdapter2 extends LBaseAdapter<ServerBrandSuggestBean> {
    /**
     * 高量搜索颜色
     */
    private int highlightedColor;

    public BrandMatchListAdapter2(Context context) {
        super(context);
        highlightedColor = context.getResources().getColor(R.color.app_green_dark);
    }


    @Override
    public View getView(int position, View convertView, final ServerBrandSuggestBean item, final LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_brand_match_list2, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        if (item == null) {
            return convertView;
        }
        //品牌logo
        ViewCommon.setAvatar(holder.imgIcon, 0, item.logo);


        if (item.highlightItem == null) {
            return convertView;
        }
        SuggestHighlightItemBean highlightItem = item.highlightItem;
        String brandName = StringUtil.connectTextWithChar("丨", highlightItem.name, item.industryName);
        if (LText.empty(brandName)) {
            return convertView;
        }

        List<HighlightItem> highlightList = highlightItem.highlightList;

        if (LList.getCount(highlightList) <= 0) {
            holder.tvCompany.setText(brandName);
        } else {
            List<AutoCompleteIndexBean> indexList = new ArrayList<>(highlightList.size());
            for (HighlightItem it : highlightList) {
                AutoCompleteIndexBean autoCompleteIndexBean = new AutoCompleteIndexBean();
                autoCompleteIndexBean.startIdx = it.startIndex;
                autoCompleteIndexBean.endIdx = it.endIndex;
                indexList.add(autoCompleteIndexBean);
            }

            SpannableStringBuilder builder = ViewCommon.setHighlightedFormatter(brandName, indexList, highlightedColor);
            holder.tvCompany.setText(builder);
        }
        return convertView;
    }


    static class ViewHolder {
        MTextView tvCompany;
        SimpleDraweeView imgIcon;

        public ViewHolder(View view) {
            tvCompany = view.findViewById(R.id.tv_company);
            imgIcon = view.findViewById(R.id.iv_logo);
        }
    }

}
