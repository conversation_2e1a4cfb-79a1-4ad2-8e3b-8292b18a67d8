package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteBean;
import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteIndexBean;
import com.hpbr.bosszhipin.module.my.activity.boss.brand.bean.CompanyMatchBean;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;

import java.util.List;

/**
 * 作者:郭峰
 * 日期:2016/4/6
 * 公司高量搜索适配器样式
 */
public class CompanyMatchListAdapter extends LBaseAdapter<CompanyMatchBean> {
    /**
     * 高量搜索颜色
     */
    private int highlightedColor;
    /**
     * 是否显示高量,默认显示
     */
    private boolean isHighlightEnable = true;

    /**
     * 列表点击事件回调
     */
    private OnCompanyListListener onCompanyListListener;


    /**
     * 公司列表点击事件
     *
     * @param onCompanyListListener 回调接口
     */
    public void setOnItemClickListener(OnCompanyListListener onCompanyListListener) {
        this.onCompanyListListener = onCompanyListListener;
    }

    public CompanyMatchListAdapter(Context context) {
        super(context);
        highlightedColor = context.getResources().getColor(R.color.app_green);
    }

    @Override
    public View getView(int position, View convertView, final CompanyMatchBean item, LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_company_match_list, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        if (item != null) {
            //显示高量搜索
            if (isHighlightEnable) {
                setHighlightText(holder, item);
            } else {
                AutoCompleteBean autoCompleteBean = item.autoCompleteBean;
                if (autoCompleteBean != null) {
                    holder.tvFilter.setText(autoCompleteBean.textTitle);
                }
            }
            //列表点击事件
            holder.llContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onCompanyListListener.onClickCompanyList(item);
                }
            });
        }
        return convertView;
    }

    private void setHighlightText(ViewHolder holder, final CompanyMatchBean item) {
        if (item == null) return;
        AutoCompleteBean autoCompleteBean = item.autoCompleteBean;
        if (autoCompleteBean == null) return;

        final String text = autoCompleteBean.textTitle;
        if (TextUtils.isEmpty(text)) {
            holder.tvFilter.setText(text);
            return;
        }
        List<AutoCompleteIndexBean> indexList = autoCompleteBean.indexList;
        if (indexList == null || indexList.size() <= 0) {
            holder.tvFilter.setText(text);
            return;
        }
        SpannableStringBuilder builder = ViewCommon.setHighlightedFormatter(text, indexList, highlightedColor);
        holder.tvFilter.setText(builder);
    }

    static class ViewHolder {
        MTextView tvFilter;
        LinearLayout llContainer;

        public ViewHolder(View view) {
            tvFilter = (MTextView) view.findViewById(R.id.tv_filtered_name);
            llContainer = (LinearLayout) view.findViewById(R.id.ll_words);
        }
    }

    /**
     * 点击品牌列表回调
     */
    public interface OnCompanyListListener {
        void onClickCompanyList(CompanyMatchBean item);
    }
}
