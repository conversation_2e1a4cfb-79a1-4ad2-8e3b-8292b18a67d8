package com.hpbr.bosszhipin.module.common.adapter;

import android.text.Html;
import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;

import net.bosszhipin.api.bean.ExpectBean;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;


public class ExpectCityModifyGuideAdapter extends BaseRvAdapter<ExpectBean, ExpectCityModifyGuideAdapter.ViewHolder> {

    public ExpectCityModifyGuideAdapter(@Nullable List<ExpectBean> data) {
        super(R.layout.geek_item_expect_city_modify_guide, data);
    }

    @Override
    protected void convert(@NonNull ViewHolder helper, ExpectBean item) {

        helper.setData(item);
    }

    static class ViewHolder extends BaseViewHolder {

        private final ImageView ivSelected;


        private final MTextView tvTitle;
        private final ZPUIRoundButton btnWorkType;

        public ViewHolder(View view) {
            super(view);
            ivSelected = view.findViewById(R.id.ivSelected);
            tvTitle = view.findViewById(R.id.tvTitle);
            btnWorkType = view.findViewById(R.id.btn_work_type);

        }

        void setData(ExpectBean data) {
            String locationStr = String.format("[%s] ", data.locationName);
            String htmlText =String.format(" <b>%s</b>-%s",locationStr, data.positionName);
            tvTitle.setText(Html.fromHtml(htmlText));
            btnWorkType.setText(data.isPartTimeJob()?"兼职":"全职");
            ivSelected.setImageResource(data.isSelected ? R.mipmap.geek_icon_dialog_item_selected : R.mipmap.ic_round_o);
        }
    }
}
