package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.bean.RecentCompanyBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

/**
 * @ClassName ：RecentCompanyAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  10:53 AM
 */
public class RecentCompanyAdapter extends BaseRvAdapter<RecentCompanyBean, BaseViewHolder> {

    private Context context;
    private final int DEFAULT_SELECT_INDEX = -1;
    private int currentSelectIndex = DEFAULT_SELECT_INDEX;

    public RecentCompanyAdapter(Context context) {
        super(R.layout.layout_item_recent_company);
        this.context = context;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, RecentCompanyBean item) {
        if (item == null) return;
        ConstraintLayout cl_company = helper.getView(R.id.cl_company);
        TextView tv_company_name = helper.getView(R.id.tv_company_name);
        TextView tv_label = helper.getView(R.id.tv_label);

        tv_company_name.setText(item.companyName);
        tv_label.setText(item.cornerTag);
        tv_label.setVisibility(!TextUtils.isEmpty(item.cornerTag) ? View.VISIBLE : View.GONE);
        tv_label.setBackgroundResource(R.drawable.color_18a8a8_round_topleft_bottomright);
        tv_label.setTextColor(ContextCompat.getColor(context, R.color.color_FFFFFFFF));

        if (helper.getAdapterPosition() == currentSelectIndex) {
            cl_company.setBackgroundResource(R.drawable.bg_8_corner_1f15b3b3_solid_48c7c7);
            tv_company_name.setTextColor(ContextCompat.getColor(context, R.color.app_green_dark));
        } else {
            cl_company.setBackgroundResource(R.drawable.bg_8_corner_f5f5f5);
            tv_company_name.setTextColor(ContextCompat.getColor(context, R.color.text_c11));
        }
    }

    public void setCurrentSelectIndex(int selectIndex) {
        this.currentSelectIndex = selectIndex;
        notifyDataSetChanged();
    }
}
