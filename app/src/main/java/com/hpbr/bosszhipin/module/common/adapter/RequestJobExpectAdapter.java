package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;

import com.hpbr.bosszhipin.common.adapter.BaseSimpleMultipleItemRvAdapter;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.module.common.provider.requestjobexpect.ExpectItemProvider;
import com.hpbr.bosszhipin.module.common.provider.requestjobexpect.ExpectListTitleProvider;
import com.hpbr.bosszhipin.module.common.provider.requestjobexpect.RequestJobStautsProvider;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

/**
 * @ClassName ：RequestJobExpectAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  8:03 PM
 */
public class RequestJobExpectAdapter extends BaseSimpleMultipleItemRvAdapter<RequestJobExpectModel, BaseViewHolder> {

    private Context context;
    private EventListener eventListener;

    public RequestJobExpectAdapter(Context context, EventListener eventListener) {
        super(null);
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    protected void registerItemProvider() {
        /*「求职状态」*/
        registerProvider(new RequestJobStautsProvider(context, eventListener));
        /*「求职期望」 列表的title*/
        registerProvider(new ExpectListTitleProvider(context, eventListener));
        /*「期望」*/
        registerProvider(new ExpectItemProvider(context, eventListener));
    }

    public interface EventListener {
        /**
         * 点击「求职状态」
         *
         * @param currentWorkStatus
         */
        void onApplyStatusClick(int currentWorkStatus);

        /**
         * 点击期望
         *
         * @param jobIntent
         */
        void onEditJobIntentClick(JobIntentBean jobIntent);

        /**
         * 点击添加期望
         */
        void onClickAddJobIntent();
    }
}
