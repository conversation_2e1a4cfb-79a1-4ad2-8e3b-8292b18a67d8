package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.manager.PreDownloadImageManager;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.ImageUtil;

import net.bosszhipin.api.bean.NPSServerBean;

import java.io.File;


/**
 * 1119.62 满意度调查 评价
 *
 * <AUTHOR>
 * @since 2023-10-8
 */
public class SatisfactionNPSAdapter extends BaseRvAdapter<NPSServerBean.NPSQuestionBean, BaseViewHolder> {

    /*当前选中的选项索引，默认无选中*/
    private int currentSelectPositon = -1;

    public SatisfactionNPSAdapter(Context context) {
        super(R.layout.item_satisfaction_option);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, NPSServerBean.NPSQuestionBean item) {
        if (item == null) return;
        SimpleDraweeView sdv_emotion = helper.getView(R.id.sdv_emotion);
        TextView tv_text = helper.getView(R.id.tv_text);

        tv_text.setText(item.title);

        if (helper.getAdapterPosition() == currentSelectPositon) {
            File targetFile = PreDownloadImageManager.getFileByImageUrl(mContext, item.icon, PreDownloadImageManager.IMAGE_CACHE_DIR, PreDownloadImageManager.DownLoadFileType.IMAGE);
            if (targetFile != null) {
                ImageUtil.loadLocalFile(sdv_emotion, targetFile.getPath(), 1, true, null);
            } else {
                ImageUtil.loadNetAnimPic(sdv_emotion, item.icon, 1, true, null);
            }
        } else {
            sdv_emotion.setImageURI(item.ashIcon);
        }

        if (currentSelectPositon == helper.getAdapterPosition()) {
            tv_text.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF292929_FFD2D2D6));
        } else {
            tv_text.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF858585_FF818185));
        }
    }

    public void setCurrentSelectPositon(int currentSelectPositon) {
        if (this.currentSelectPositon == currentSelectPositon) return;
        this.currentSelectPositon = currentSelectPositon;
        notifyDataSetChanged();
    }

    public int getCurrentSelectPositon() {
        return currentSelectPositon;
    }

}
