package com.hpbr.bosszhipin.module.common.adapter;

import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.NPSServerBean;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 1119.62 满意度调查 标签
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
public class SatisfactionReasonAdapter extends BaseRvAdapter<NPSServerBean.NPSLabelBean, BaseViewHolder> {

    public SatisfactionReasonAdapter() {
        super(R.layout.item_nps_label);
    }

    private final Set<Long> mSelectedSet = new HashSet<>();

    @Override
    protected void convert(@NonNull BaseViewHolder helper, NPSServerBean.NPSLabelBean item) {
        if (item == null) return;
        TextView tv_label = helper.getView(R.id.tv_label);
        tv_label.setText(item.name);

        if (mSelectedSet.contains(item.labelId)) {
            tv_label.setTextColor(ContextCompat.getColor(mContext, R.color.app_green_dark));
            tv_label.setBackgroundResource(R.drawable.bg_4_corner_15b3b3_solid_1_stroke);
        } else {
            tv_label.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF292929_FFD2D2D6));
            tv_label.setBackgroundResource(R.drawable.bg_4_corner_e0e0e0_solid_1_storke);
        }
    }

    public void changeSelection(NPSServerBean.NPSLabelBean label) {
        long labelId = label.labelId;
        if (mSelectedSet.contains(labelId)) {
            mSelectedSet.remove(labelId);
        } else {
            mSelectedSet.add(labelId);
        }
    }

    public boolean isSelected(NPSServerBean.NPSLabelBean label) {
        return mSelectedSet.contains(label.labelId);
    }

    public Set<Long> getSelectedSet() {
        return mSelectedSet;
    }


    @NonNull
    public List<NPSServerBean.NPSLabelBean> getSelectedList() {
        List<NPSServerBean.NPSLabelBean> selectedList = new ArrayList<>();
        List<NPSServerBean.NPSLabelBean> mList = getData();
        if (LList.getCount(mList) == 0) return selectedList;
        for (NPSServerBean.NPSLabelBean reasonBean : mList) {
            if (reasonBean == null) continue;
            if (mSelectedSet.contains(reasonBean.labelId)) {
                selectedList.add(reasonBean);
            }
        }
        return selectedList;
    }

    public void clearSelectedSet() {
        mSelectedSet.clear();
    }
}
