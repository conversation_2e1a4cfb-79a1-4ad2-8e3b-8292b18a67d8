package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

/**
 * @ClassName ：SatisfactionScoreAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/7  3:28 PM
 */
public class SatisfactionScoreAdapter extends BaseRvAdapter<Integer, BaseViewHolder> {

    private Context context;
    private static final int DEFAULT_SELECT_INDEX = -1;
    private int currentSelectIndex = DEFAULT_SELECT_INDEX;

    public SatisfactionScoreAdapter(Context context) {
        super(R.layout.layout_item_satisfaction_score);
        this.context = context;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, Integer item) {
        if (item == null) return;
        ConstraintLayout cl_item = helper.getView(R.id.cl_item);
        TextView tv_score = helper.getView(R.id.tv_score);

        tv_score.setText(String.valueOf(item));
        if (currentSelectIndex == helper.getAdapterPosition()) {
            cl_item.setBackgroundResource(R.drawable.bg_8_corner_15b3b3_no_dark);
            tv_score.setTextColor(ContextCompat.getColor(context, R.color.color_FFFFFFFF));
        } else {
            cl_item.setBackgroundResource(R.drawable.bg_8_corner_f0f0f0);
            tv_score.setTextColor(ContextCompat.getColor(context, R.color.text_c5));
        }
    }

    public void setCurrentSelectIndex(int index) {
        this.currentSelectIndex = index;
        notifyDataSetChanged();
    }

    public int getCurrentSelectIndex() {
        return currentSelectIndex;
    }
}
