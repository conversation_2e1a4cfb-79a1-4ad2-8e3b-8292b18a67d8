package com.hpbr.bosszhipin.module.common.adapter;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * Author: zhouyou
 * Date: 2023/11/20
 */
public class StudentExpectMultiSelectionAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

    public StudentExpectMultiSelectionAdapter() {
        this(null);
    }

    public StudentExpectMultiSelectionAdapter(@Nullable List<LevelBean> data) {
        super(R.layout.item_student_expect_multi_select, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        if (item == null) return;
        int paddingLeft = ZPUIDisplayHelper.dp2px(mContext, 8);
        int paddingRight = (helper.getAdapterPosition() == getItemCount() - 1) ? ZPUIDisplayHelper.dp2px(mContext, 8) : 0;
        helper.itemView.setPadding(paddingLeft, 0, paddingRight, 0);
        ZPUIRoundButton btnWord = helper.getView(R.id.btn_word);
        btnWord.setText(item.name);
    }
}
