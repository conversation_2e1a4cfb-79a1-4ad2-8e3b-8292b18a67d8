package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.common.identity.StudentRecordSwitchManageActivity;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.SP;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import net.bosszhipin.api.ProfileettingRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_STUDENT_LIST_CHANGE;
import static com.hpbr.bosszhipin.module.common.identity.StudentRecordSwitchManageActivity.*;

/**
 * <AUTHOR>
 **/
public class StudentExpectTypeAdapter extends RecyclerView.Adapter {

    public static final String HAS_SHOWED_RECOMMEND_NEW_TAG = Constants.PREFIX + "_HAS_SHOWED_RECOMMEND_NEW_TAG";

    private final Context context;
    private int status = STATUS_TOGGLE;
    private List<CodeNameFlagBean> expectTypes = new ArrayList<>();

    public StudentExpectTypeAdapter(Context context, List<CodeNameFlagBean> expectTypes) {
        this.expectTypes = expectTypes;
        this.context = context;
    }

    public void setData(List<CodeNameFlagBean> expectTypes) {
        this.expectTypes.clear();
        this.expectTypes.addAll(expectTypes);
        notifyDataSetChanged();
    }

    public void setStatus(int status) {
        this.status = status;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View item = LayoutInflater.from(context).inflate(R.layout.item_student_record, parent, false);
        return new ExpectViewHolder(item);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {

        if (LList.isEmpty(expectTypes)) {
            return;
        }
        CodeNameFlagBean itemBean = expectTypes.get(position);
        if (itemBean == null) {
            return;
        }
        ExpectViewHolder viewHolder = (ExpectViewHolder) holder;
        viewHolder.mName.setText(itemBean.name);
        //操作状态，0-关闭开关，1-开启开关 2不可点击
        if (this.status == STATUS_TOGGLE) {
            viewHolder.mSwitch.setImageResource(itemBean.flag == OPEN_STATUS ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
            //不可点击
            boolean disable = itemBean.flag == FORBIDDEN_STATUS;
            if (disable) {
                viewHolder.itemView.findViewById(R.id.mTips).setVisibility(View.VISIBLE);
                viewHolder.itemView.setOnClickListener(null);
                viewHolder.mName.setTextColor(ContextCompat.getColor(context, R.color.text_c3));
                viewHolder.mAvatar.setImageResource(R.mipmap.ic_avatar_disable);
            } else {
                viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // F1协议自动打开开关，在StudentRecordManageActivity中的initRvExpect中performClick自动调用。
                        boolean closeToOpen = itemBean.flag == CLOSE_STATUS;
                        //点击开关
                        onSwitchClickListener(itemBean, viewHolder.mSwitch, viewHolder.mAvatar, viewHolder.mName, viewHolder.ivNewTips);

                        int workStatus = 1;
                        if (itemBean.code == INTERN) {
                            workStatus = 1;
                        } else if (itemBean.code == FULL_TIME) {
                            workStatus = 2;
                        } else if (itemBean.code == PART_TIME) {
                            workStatus = 3;
                        } else if (itemBean.code == RECOMMEND) {
                            workStatus = 4;
                            boolean hasShowRecommend = SP.get().getBoolean(HAS_SHOWED_RECOMMEND_NEW_TAG, false);
                            if (!hasShowRecommend)
                                SP.get().putBoolean(HAS_SHOWED_RECOMMEND_NEW_TAG, true);
                        }

                        AnalyticsFactory.create()
                                .action(ACTION_STUDENT_LIST_CHANGE)
                                .param("p", closeToOpen ? "0" : "1")//"0:由关闭到开启 1：由开启到关闭"
                                .param("p2", workStatus + "")//"1：实习 2：全职 3：兼职" 4: 期望
                                .buildSync();
                    }
                });
                if (itemBean.flag == CLOSE_STATUS) {
                    viewHolder.mName.setTextColor(ContextCompat.getColor(context, R.color.text_c3));
                    boolean hasShowRecommend = SP.get().getBoolean(HAS_SHOWED_RECOMMEND_NEW_TAG, false);
                    if (itemBean.code == RECOMMEND && !hasShowRecommend) {
                        viewHolder.itemView.findViewById(R.id.iv_new_tips).setVisibility(View.VISIBLE);
                        SP.get().putBoolean(HAS_SHOWED_RECOMMEND_NEW_TAG, true);
                    } else {
                        viewHolder.itemView.findViewById(R.id.iv_new_tips).setVisibility(View.GONE);
                    }
                } else {
                    viewHolder.mName.setTextColor(ContextCompat.getColor(context, R.color.text_c6));
                }
                //初始化头像
                refreshAvatar(itemBean, viewHolder.mAvatar);
            }
        } else if (status == STATUS_EDIT) {
            viewHolder.mSwitch.setImageResource(itemBean.flag == OPEN_STATUS ? R.mipmap.icon_geek_student_sort : R.mipmap.ic_online_switch_off);
            boolean disable = itemBean.flag == FORBIDDEN_STATUS;
            viewHolder.itemView.setOnClickListener(null);
            if (disable) {
                viewHolder.itemView.findViewById(R.id.mTips).setVisibility(View.VISIBLE);
                viewHolder.itemView.setOnClickListener(null);
                viewHolder.mName.setTextColor(ContextCompat.getColor(context, R.color.text_c3));
                viewHolder.mAvatar.setImageResource(R.mipmap.ic_avatar_disable);
            }
        }

        if (itemBean.code == RECOMMEND) {
            viewHolder.itemView.findViewById(R.id.mTips).setVisibility(View.VISIBLE);
            ((MTextView) (viewHolder.itemView.findViewById(R.id.mTips))).setText("为你推荐更多就业方向", View.GONE);
        }

    }

    @Override
    public int getItemCount() {
        return expectTypes.size();
    }

    static class ExpectViewHolder extends RecyclerView.ViewHolder {
        ImageView mAvatar;
        ImageView mSwitch;
        MTextView mName;
        ImageView ivNewTips;

        public ExpectViewHolder(@NonNull View itemView) {
            super(itemView);
            mAvatar = itemView.findViewById(R.id.mAvatar);
            mSwitch = itemView.findViewById(R.id.mSwitch);
            mName = itemView.findViewById(R.id.mName);
            ivNewTips = itemView.findViewById(R.id.iv_new_tips);

        }
    }

    //获得开关的数量
    private int getOpenCount() {
        int openCount = 0;
        List<CodeNameFlagBean> configList = expectTypes;
        if (configList != null) {
            for (CodeNameFlagBean codeNameFlagBean : configList) {
                if (codeNameFlagBean == null) continue;
                if (codeNameFlagBean.flag == OPEN_STATUS && codeNameFlagBean.code != RECOMMEND) {
                    openCount++;
                }
            }
        }
        return openCount;
    }


    //初始化头像
    private void refreshAvatar(CodeNameFlagBean itemBean, ImageView mAvatar) {
        if (itemBean.code == INTERN) {
            if (itemBean.flag == CLOSE_STATUS) {
                mAvatar.setImageResource(R.mipmap.ic_student_intern_disable);
            } else {
                mAvatar.setImageResource(R.mipmap.ic_student_intern);
            }
        } else if (itemBean.code == FULL_TIME) {
            if (itemBean.flag == CLOSE_STATUS) {
                mAvatar.setImageResource(R.mipmap.ic_student_full_disable);
            } else {
                mAvatar.setImageResource(R.mipmap.ic_full_time_avatar);
            }
        } else if (itemBean.code == PART_TIME) {
            if (itemBean.flag == CLOSE_STATUS) {
                mAvatar.setImageResource(R.mipmap.student_part_time_disable);
            } else {
                mAvatar.setImageResource(R.mipmap.student_part_time);
            }
        } else if (itemBean.code == RECOMMEND) {
            if (itemBean.flag == CLOSE_STATUS) {
                mAvatar.setImageResource(R.mipmap.student_type_recommend_close);
            } else {
                mAvatar.setImageResource(R.mipmap.student_type_recommend);
            }
        }
    }

    //获得开关状态
    private int getSwitchFlag(CodeNameFlagBean itemBean) {
        int flag = itemBean.flag;
        if (flag == CLOSE_STATUS) {
            return OPEN_STATUS;
        }
        if (flag == OPEN_STATUS) {
            return CLOSE_STATUS;
        }
        return flag;
    }

    //获得提交Code 牛人配置开关，1-F1全职tab，2-F1兼职tab，4-F1实习tab
    private int getSwitchCode(CodeNameFlagBean itemBean) {
        int code = (int) itemBean.code;
        //实习
        if (code == INTERN) {
            return 4;
        }
        //兼职
        if (code == PART_TIME) {
            return 2;
        }

        if (code == RECOMMEND) {
            return 8;
        }
        //全职
        return 1;
    }


    /**
     * 设置开关状态接口
     *
     * @param type
     * @param value
     */
    private void changeSwitch(int type, int value, StudentRecordSwitchManageActivity.OnSwitchChangeCallback callback) {

        ProfileettingRequest request = new ProfileettingRequest(new ApiRequestCallback<SuccessBooleanResponse>() {

            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                if (callback != null) {
                    callback.onSwitchChangeListener();
                }
            }

            @Override
            public void onComplete() {
//                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        request.settingType = type;
        request.settingValue = value;
        HttpExecutor.execute(request);
    }

    //点击开关
    private void onSwitchClickListener(CodeNameFlagBean itemBean, ImageView mSwitch, ImageView mAvatar, MTextView mName, ImageView mTipView) {
        final int flag = getSwitchFlag(itemBean);
        //当前是否关闭最后一个 状态
        if (flag == CLOSE_STATUS && getOpenCount() == 1 && itemBean.code != RECOMMEND) {
            T.ss("至少开启全职、实习、兼职列表中的1个");
            return;
        }
        //网络请求刷新
        changeSwitch(getSwitchCode(itemBean), flag, () -> {
            //数据刷新
            itemBean.flag = flag;
            //刷新开关状态
            mSwitch.setImageResource(itemBean.flag == OPEN_STATUS ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
            //通知F1
            Intent intent = new Intent(Constants.RECEIVER_STUDENT_EXPECT_TYPE_CHANGE_ACTION);
            ReceiverUtils.sendBroadcast(context, intent);

            //初始化头像
            refreshAvatar(itemBean, mAvatar);

            if (itemBean.flag == CLOSE_STATUS) {
                mName.setTextColor(ContextCompat.getColor(context, R.color.text_c3));
            } else {
                mName.setTextColor(ContextCompat.getColor(context, R.color.text_c6));
            }
            if (itemBean.code == RECOMMEND) {
                mTipView.setVisibility(View.GONE);
            }

        });
    }

}
