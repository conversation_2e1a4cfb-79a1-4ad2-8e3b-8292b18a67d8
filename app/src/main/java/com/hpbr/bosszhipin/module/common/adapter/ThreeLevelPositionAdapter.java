package com.hpbr.bosszhipin.module.common.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.threelevel.ThreeLevelAdapter;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/7/3.
 */
public class ThreeLevelPositionAdapter extends ThreeLevelAdapter {

    private boolean inMultipleChoiceMode;
    private boolean inChooseMode = true;
    private LayoutInflater mInflater;
    private List<LevelBean> mPositionList;

    public ThreeLevelPositionAdapter(Context context) {
        mInflater = LayoutInflater.from(context);
    }

    public void setData(List<LevelBean> positionList) {
        mPositionList = positionList;
        notifyDataSetChanged();
    }

    public void setInMultipleChoiceMode(boolean inMultipleChoiceMode) {
        this.inMultipleChoiceMode = inMultipleChoiceMode;
    }

    public void setInChooseMode(boolean inChooseMode) {
        this.inChooseMode = inChooseMode;
    }

    @Override
    public int getFirstLevelItemCount() {
        return mPositionList == null ? 0 : mPositionList.size();
    }

    @Override
    public int getSecondLevelItemCount(int firstLevelPosition) {
        LevelBean firstItem = getFirstLevelItem(firstLevelPosition);
        return firstItem.subLevelModeList == null ? 0 : firstItem.subLevelModeList.size();
    }

    @Override
    public int getThirdLevelItemCount(int firstLevelPosition, int secondLevelPosition) {
        LevelBean secondItem = getSecondLevelItem(firstLevelPosition, secondLevelPosition);
        return secondItem.subLevelModeList == null ? 0 : secondItem.subLevelModeList.size();
    }

    @Override
    public LevelBean getFirstLevelItem(int firstLevelPosition) {
        return mPositionList.get(firstLevelPosition);
    }

    @Override
    public LevelBean getSecondLevelItem(int firstLevelPosition, int secondLevelPosition) {
        return mPositionList.get(firstLevelPosition).subLevelModeList.get(secondLevelPosition);
    }

    @Override
    public LevelBean getThirdLevelItem(int firstLevelPosition, int secondLevelPosition, int thirdLevelPosition) {
        return mPositionList.get(firstLevelPosition).subLevelModeList.get(secondLevelPosition)
                .subLevelModeList.get(thirdLevelPosition);
    }

    @SuppressLint("InflateParams")
    @Override
    public View getFirstLevelView(int firstLevelPosition, View convertView, ViewGroup parent) {
        LevelBean item = getFirstLevelItem(firstLevelPosition);
        if (item == null) {
            return null;
        }
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.item_first_position, null, false);
        }
        ((TextView) convertView.findViewById(R.id.tv_text)).setText(item.name);

        //region 显示该一级类下已选中的三级类数量
        TextView checkCount = convertView.findViewById(R.id.checkCount);
        int firstLevelCheckCount = getFirstLevelCheckCount(item);
        if (inMultipleChoiceMode && firstLevelCheckCount > 0) {
            checkCount.setVisibility(View.VISIBLE);
            checkCount.setText(String.valueOf(firstLevelCheckCount));
        } else {
            checkCount.setVisibility(View.GONE);
        }
        //endregion

        return convertView;
    }

    private int getFirstLevelCheckCount(@NonNull LevelBean firstLevel) {
        int checkCount = 0;

        List<LevelBean> secondLevelList = firstLevel.subLevelModeList;
        if (LList.getCount(secondLevelList) > 0) {
            for (LevelBean secondLevel : secondLevelList) { // Second level
                List<LevelBean> thirdLevelList = secondLevel.subLevelModeList;
                if (LList.getCount(thirdLevelList) > 0) {
                    for (LevelBean thirdLevel : thirdLevelList) { // Third level
                        if (thirdLevel.isChecked()) {
                            checkCount++;
                        }
                    }
                }
            }
        }

        return checkCount;
    }

    @SuppressLint("InflateParams")
    @Override
    public View getSecondLevelView(int firstLevelPosition, int secondLevelPosition, View convertView, ViewGroup parent) {
        LevelBean item = getSecondLevelItem(firstLevelPosition, secondLevelPosition);
        if (item == null) {
            return null;
        }
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.item_second_second_position, null, false);
        }
        ((TextView) convertView.findViewById(R.id.tv_second)).setText(item.name);

        //region 二级类下三级类选中数量
        TextView checkCount = convertView.findViewById(R.id.checkCount);
        int secondLevelCheckCount = getSecondLevelCheckCount(item);
        if (inMultipleChoiceMode && secondLevelCheckCount > 0) {
            checkCount.setVisibility(View.VISIBLE);
            checkCount.setText(String.valueOf(secondLevelCheckCount));
        } else {
            checkCount.setVisibility(View.GONE);
        }
        //endregion

        return convertView;
    }

    private int getSecondLevelCheckCount(@NonNull LevelBean secondLevel) {
        int checkCount = 0;

        List<LevelBean> thirdLevelList = secondLevel.subLevelModeList;
        if (LList.getCount(thirdLevelList) > 0) {
            for (LevelBean thirdLevel : thirdLevelList) { // Third level
                if (thirdLevel.isChecked()) {
                    checkCount++;
                }
            }
        }

        return checkCount;
    }

    @SuppressLint("InflateParams")
    @Override
    public View getThirdLevelView(int firstLevelPosition, int secondLevelPosition, int thirdLevelPosition, View convertView, ViewGroup parent) {
        LevelBean item = getThirdLevelItem(firstLevelPosition, secondLevelPosition, thirdLevelPosition);
        if (item == null) {
            return null;
        }
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.item_second_third_position, null, false);
        }

        boolean contain = item.isChecked() && inChooseMode();
        if (contain) {
            TLog.print("txy", "=====================name:%s,code:%d", item.name, item.hashCode());
        }
        MTextView tvText = convertView.findViewById(R.id.tv_text);
        ImageView ivStatus = convertView.findViewById(R.id.iv_status);
        ivStatus.setVisibility(contain ? View.VISIBLE : View.GONE);
        tvText.setTextColor(contain ? ContextCompat.getColor(App.get().getContext(), R.color.app_green) : ContextCompat.getColor(App.get().getContext(), R.color.text_c6));
        tvText.setText(item.name);
        return convertView;
    }

    public boolean inChooseMode() {
        return inChooseMode;
    }

}
