package com.hpbr.bosszhipin.module.common.adapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;

import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/5/19
 */
public class ThreeLevelSelectionAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

    public ThreeLevelSelectionAdapter() {
        this(null);
    }

    public ThreeLevelSelectionAdapter(@Nullable List<LevelBean> data) {
        super(R.layout.item_common_selected_with_close, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        if (item == null) {
            return;
        }
        MTextView tvWord = helper.getView(R.id.tv_title);
        String positionName = item.name;
        String reportPositionName = item.reportPositionName;
        String thirdName = LText.notEmpty(reportPositionName) ? reportPositionName : positionName;
        if (LText.notEmpty(item.queryName)) {
            thirdName = thirdName + "（" + item.queryName + "）";
        }
        tvWord.setText(thirdName);
    }
}
