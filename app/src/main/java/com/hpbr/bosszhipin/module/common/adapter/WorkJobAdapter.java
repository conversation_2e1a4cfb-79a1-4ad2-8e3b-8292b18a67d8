package com.hpbr.bosszhipin.module.common.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

import net.bosszhipin.api.bean.RecPositionBean;

/**
 * @ClassName ：WorkJobAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/3/29  6:30 PM
 */
public class WorkJobAdapter extends BaseRvAdapter<RecPositionBean, BaseViewHolder> {

    private Context context;

    public WorkJobAdapter(Context context) {
        super(R.layout.item_work_job);
        this.context = context;
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, RecPositionBean item) {
        if (item == null) return;
        ConstraintLayout cl_item = helper.getView(R.id.cl_item);
        TextView tv_job_name = helper.getView(R.id.tv_job_name);
        ImageView iv_arrow = helper.getView(R.id.iv_arrow);

        tv_job_name.setText(item.name);
        if (item.isSelected && item.code != RecPositionBean.ALL_POSITION_CODE) {
            cl_item.setBackgroundResource(R.drawable.bg_round_4_color_1f15b3b3);
            tv_job_name.setTextColor(ContextCompat.getColor(context, R.color.color_BOSS7));
        } else {
            cl_item.setBackgroundResource(R.drawable.bg_4_corner_f5f5f5);
            tv_job_name.setTextColor(ContextCompat.getColor(context, R.color.text_c11));
        }

        iv_arrow.setVisibility(item.code == RecPositionBean.ALL_POSITION_CODE ? View.VISIBLE : View.GONE);

    }
}
