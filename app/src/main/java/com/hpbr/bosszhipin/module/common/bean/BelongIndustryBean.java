package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：BelongIndustryBean
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/13  8:45 PM
 */
public class BelongIndustryBean implements Serializable {
    private static final long serialVersionUID = -6179974545142445388L;

    /*是否显示拦截提示*/
    public boolean isShowInterceptTip;
    /*行业名称*/
    public String industryName;
    /*行业code*/
    public String industryCode;

    public long reportIndustryId;
    public int mRecommedSelectedCount;


    public BelongIndustryBean(String industryName, String industryCode, long reportIndustryId, int mRecommedSelectedCount) {
        this.industryName = industryName;
        this.industryCode = industryCode;
        this.reportIndustryId = reportIndustryId;
        this.mRecommedSelectedCount = mRecommedSelectedCount;
    }

    public BelongIndustryBean(boolean isShowInterceptTip, String industryName, String industryCode, long reportIndustryId, int mRecommedSelectedCount) {
        this.isShowInterceptTip = isShowInterceptTip;
        this.industryName = industryName;
        this.industryCode = industryCode;
        this.reportIndustryId = reportIndustryId;
        this.mRecommedSelectedCount = mRecommedSelectedCount;
    }
}
