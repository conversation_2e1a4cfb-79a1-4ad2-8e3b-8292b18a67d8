package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * 通用埋点使用
 */
public class CommonStatisticsParamBean implements Serializable {
    private static final long serialVersionUID = 3769590153426315140L;

    public String p1;
    public String p2;
    public String p3;
    public String p4;
    public String p5;
    public String p6;
    public String p7;
    public String p8;
    public String p9;

    public CommonStatisticsParamBean setP1(String p1) {
        this.p1 = p1;
        return this;
    }

    public CommonStatisticsParamBean setP2(String p2) {
        this.p2 = p2;
        return this;
    }

    public CommonStatisticsParamBean setP3(String p3) {
        this.p3 = p3;
        return this;
    }

    public CommonStatisticsParamBean setP4(String p4) {
        this.p4 = p4;
        return this;
    }

    public CommonStatisticsParamBean setP5(String p5) {
        this.p5 = p5;
        return this;
    }

    public CommonStatisticsParamBean setP6(String p6) {
        this.p6 = p6;
        return this;
    }

    public CommonStatisticsParamBean setP7(String p7) {
        this.p7 = p7;
        return this;
    }

    public CommonStatisticsParamBean setP8(String p8) {
        this.p8 = p8;
        return this;
    }

    public CommonStatisticsParamBean setP9(String p9) {
        this.p9 = p9;
        return this;
    }
}
