package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：NewWorkExpEntity
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/10  10:51 AM
 */
public class NewWorkExpEntity implements Serializable {
    private static final long serialVersionUID = 1536783617578734426L;
    /*公司名称*/
    public CompanyNameBean companyNameBean;
    /*在职时间*/
    public OnTheJobTimeBean onTheJobTimeBean;
    /*工作内容*/
    public WorkContentBean workContentBean;
    /*所在行业*/
    public BelongIndustryBean belongIndustryBean;
    /*职位名称*/
    public PositionNameBean positionNameBean;


    /**
     * 获取公司名称
     *
     * @return
     */
    public String getCompanyName() {
        return companyNameBean != null ? companyNameBean.companyName : "";
    }

    /**
     * 获取在职时间-起始时间
     *
     * @return
     */
    public int getStartDate() {
        return onTheJobTimeBean != null ? onTheJobTimeBean.startDate : 0;
    }

    /**
     * 获取在职时间-结束时间
     *
     * @return
     */
    public int getEndDate() {
        return onTheJobTimeBean != null ? onTheJobTimeBean.endDate : -1;
    }

    /**
     * 获取工作内容
     *
     * @return
     */
    public String getWorkContent() {
        return workContentBean != null ? workContentBean.workContent : "";
    }

    /**
     * 获取行业名称
     *
     * @return
     */
    public String getIndustryName() {
        return belongIndustryBean != null ? belongIndustryBean.industryName : "";
    }

    public long getReportIndustryId() {
        return belongIndustryBean != null ? belongIndustryBean.reportIndustryId : 0;
    }

    public String getIndustryCode() {
        return belongIndustryBean != null ? belongIndustryBean.industryCode : "";
    }


    /**
     * 获取职位名称
     *
     * @return
     */
    public String getPositionName() {
        return positionNameBean != null ? positionNameBean.positionName : "";
    }

    /**
     * 获取职类code
     *
     * @return
     */
    public long getPositionClassCode() {
        return positionNameBean != null ? positionNameBean.positionClassCode : 0;
    }

    /**
     * 获取职类名称
     *
     * @return
     */
    public String getPositionClassName() {
        return positionNameBean != null ? positionNameBean.positionClassName : "";
    }

    public long getReportPositionId() {
        return positionNameBean != null ? positionNameBean.reportPositionId : 0;
    }

    public int getPositionLv2(){
        return positionNameBean != null ? positionNameBean.positionLv2 : 0;
    }
}
