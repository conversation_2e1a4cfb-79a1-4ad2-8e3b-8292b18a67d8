package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：OnTheJobTimeBean
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  4:22 PM
 */
public class OnTheJobTimeBean implements Serializable {
    private static final long serialVersionUID = 1281938259287670064L;

    /**
     * 开始时间
     */
    public int startDate;
    /**
     * 结束时间,客户端用-1代表至今
     */
    public int endDate;
    /*是否显示拦截提示*/
    public String interceptTip;

    public OnTheJobTimeBean(String interceptTip, int startDate, int endDate) {
        this.interceptTip = interceptTip;
        this.startDate = startDate;
        this.endDate = endDate;
    }
}
