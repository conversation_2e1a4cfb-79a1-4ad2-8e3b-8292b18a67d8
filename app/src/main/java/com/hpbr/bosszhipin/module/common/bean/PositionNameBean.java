package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：PositionNameBean
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/13  8:46 PM
 */
public class PositionNameBean implements Serializable {
    private static final long serialVersionUID = 4331119263672734553L;

    /*是否显示拦截提示*/
    public boolean isShowInterceptTip;
    /*职位名称*/
    public String positionName;
    /*职类code*/
    public long positionClassCode;
    /*职类名称*/
    public String positionClassName;

    public long reportPositionId;
    public int positionLv2;

    public PositionNameBean(String positionName, long positionClassCode, String positionClassName, long reportPositionId, int positionLv2) {
        this.positionName = positionName;
        this.positionClassCode = positionClassCode;
        this.positionClassName = positionClassName;
        this.reportPositionId = reportPositionId;
        this.positionLv2 = positionLv2;
    }

    public PositionNameBean(boolean isShowInterceptTip, String positionName, long positionClassCode, String positionClassName, long reportPositionId, int positionLv2) {
        this.isShowInterceptTip = isShowInterceptTip;
        this.positionName = positionName;
        this.positionClassCode = positionClassCode;
        this.positionClassName = positionClassName;
        this.reportPositionId = reportPositionId;
        this.positionLv2 = positionLv2;
    }
}
