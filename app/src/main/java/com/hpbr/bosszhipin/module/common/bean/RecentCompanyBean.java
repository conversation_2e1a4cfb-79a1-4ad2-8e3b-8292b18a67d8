package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：RecentCompanyBean
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  10:54 AM
 */
public class RecentCompanyBean implements Serializable {
    private static final long serialVersionUID = -3988258302507882739L;

    /*公司名称*/
    public String companyName;
    /*标签*/
    public String cornerTag;

    /*是否是「以上都没有」*/
    public transient boolean isNoAll;

    public boolean skip = true;

    public RecentCompanyBean(String companyName, String cornerTag, boolean isNoAll) {
        this.companyName = companyName;
        this.cornerTag = cornerTag;
        this.isNoAll = isNoAll;
    }
}
