package com.hpbr.bosszhipin.module.common.bean;

import java.io.Serializable;

/**
 * @ClassName ：WorkContentBean
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  4:23 PM
 */
public class WorkContentBean implements Serializable {
    private static final long serialVersionUID = 3769590153426315140L;

    /*是否显示拦截提示*/
    public boolean isShowInterceptTip;
    /*工作内容*/
    public String workContent;


    public WorkContentBean(boolean isShowInterceptTip, String workContent) {
        this.isShowInterceptTip = isShowInterceptTip;
        this.workContent = workContent;
    }
}
