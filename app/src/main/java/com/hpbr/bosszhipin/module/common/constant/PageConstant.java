package com.hpbr.bosszhipin.module.common.constant;

import com.hpbr.bosszhipin.config.Constants;

/**
 * @ClassName ：PageConstant
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  7:43 PM
 */
public class PageConstant {

    /*==================页面requestCode=====================*/
    public static final int REQ_POSITION_CLASS = 100;
    public static final int REQ_COMPANY = 102;
    public static final int REQ_INDUSTRY = 103;
    public static final int REQ_WORK_CONTENT = 106;
    public static final int REQ_ADVANTAGE_EDIT = 107;
    public static final int REQ_JOB_EXPECT_FRAGMENT = 108;


    /*==================参数=====================*/
    public static final String KEY_RECENT_COMPANY_BEAN = Constants.PREFIX + ".RecentCompanyBean";
    public static final String KEY_EXP_ADD_SUG_LIST_RESPONSE = Constants.PREFIX + ".ExpAddSugListResponse";
    public static final String KEY_WORK_EXP_FORM_DATA = Constants.PREFIX + ".";

    /*==================页面Router=====================*/

    /*老用户再次活跃（新流程）*/
    public static final String PATH_GEEK_OLD_USER_ACTIVE_AGAIN = "/path_geek_old_user_active_again";
    /*新招呼牛人列表页*/
    public static final String PATH_NEW_GREET_GEEK_LIST = "/path_new_greet_geek_list";
    /*「其他」期望职位完善*/
    public static final String PATH_OTHER_JOB_GUIDE_COMPLETE = "/path_other_job_guide_complete";

}
