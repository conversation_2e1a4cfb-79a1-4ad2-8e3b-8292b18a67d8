package com.hpbr.bosszhipin.module.common.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate2Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class CommonF1DialogTemplate2 extends BaseBottomSheetFragment {
    private GeekF1CommonDialogResponse data;
    private Context context;

    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;

    private boolean isClickOtherClose = true;


    public static CommonF1DialogTemplate2 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate2 fragment = new CommonF1DialogTemplate2();
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
        setDraggable(false);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_common_template2, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvTitleCenter = view.findViewById(R.id.tvTitleCenter);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        MTextView tvContent = view.findViewById(R.id.tvContent);
        SimpleDraweeView sdvIcon = view.findViewById(R.id.sdvIcon);
        SimpleDraweeView sdvTopIcon = view.findViewById(R.id.sdvTopIcon);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType,data.bizId);
                }
                dismissAllowingStateLoss();
            }
        });

        if (data != null) {
            GeekF1CommonDialogTextBean title = data.title;
            if (title != null) {
                if (context != null && !LList.isEmpty(title.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, title.text, title.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvTitle.setText(builder);
                        tvTitleCenter.setText(builder);
                        if(isShowCenter()){
                            tvTitle.setVisibility(View.GONE);
                            tvTitleCenter.setVisibility(View.VISIBLE);
                        }else{
                            tvTitle.setVisibility(View.VISIBLE);
                            tvTitleCenter.setVisibility(View.GONE);
                        }
                        tvTitle.setLinksClickable(true);
                        tvTitleCenter.setLinksClickable(true);
                    } else {
                        tvTitle.setVisibility(View.GONE);
                        tvTitleCenter.setVisibility(View.GONE);
                    }
                } else {
                    if(isShowCenter()){
                        tvTitleCenter.setText(title.text, View.GONE);
                    }else{
                        tvTitle.setText(title.text, View.GONE);
                    }

                }
                tvTitle.setGravity(title.getGravity());
                tvTitle.setMaxLines(title.lineNumber);
                tvTitleCenter.setGravity(title.getGravity());
                tvTitleCenter.setMaxLines(title.lineNumber);
            } else {
                tvTitle.setVisibility(View.GONE);
                tvTitleCenter.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean subTitle = data.subTitle;
            if (subTitle != null) {
                if (context != null && !LList.isEmpty(subTitle.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, subTitle.text, subTitle.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvSubTitle.setVisibility(View.VISIBLE);
                        tvSubTitle.setText(builder);
                        tvSubTitle.setLinksClickable(true);
                    } else {
                        tvSubTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvSubTitle.setText(subTitle.text, View.GONE);
                }
                tvSubTitle.setGravity(subTitle.getGravity());
                tvSubTitle.setMaxLines(subTitle.lineNumber);
            } else {
                tvSubTitle.setVisibility(View.GONE);
            }
            GeekF1CommonDialogResponse.ImageBean imageBean = data.image;

            if (imageBean != null && !TextUtils.isEmpty(imageBean.url)) {
                sdvIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams params = sdvIcon.getLayoutParams();
                params.height = imageBean.height;
                params.width = imageBean.width;
                sdvIcon.setLayoutParams(params);
                sdvIcon.setImageURI(imageBean.url);
            } else {
                if (!TextUtils.isEmpty(data.imageUrl)) {
                    sdvIcon.setVisibility(View.VISIBLE);
                    sdvIcon.setImageURI(data.imageUrl);
                } else {
                    sdvIcon.setVisibility(View.GONE);
                }
            }
            GeekF1CommonDialogResponse.ImageBean topIcon = data.topIcon;
            if (topIcon != null && !TextUtils.isEmpty(topIcon.url)) {
                sdvTopIcon.setVisibility(View.VISIBLE);
                if (topIcon.height > 0 && topIcon.width > 0) {
                    ViewGroup.LayoutParams params = sdvTopIcon.getLayoutParams();
                    params.height = topIcon.height;
                    params.width = topIcon.width;
                    sdvTopIcon.setLayoutParams(params);
                }
                sdvTopIcon.setImageURI(topIcon.url);
            } else {
                sdvTopIcon.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean content = data.content;
            if (content != null) {
                if (!LList.isEmpty(content.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, content.text, content.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvContent.setVisibility(View.VISIBLE);
                        tvContent.setText(builder);
                        tvContent.setLinksClickable(true);
                    } else {
                        tvContent.setVisibility(View.GONE);
                    }
                } else {
                    tvContent.setText(content.text, View.GONE);
                }
                tvContent.setGravity(content.getGravity());
                tvContent.setMaxLines(content.lineNumber);
            } else {
                tvContent.setVisibility(View.GONE);
            }


            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                btnGo.setVisibility(View.VISIBLE);
                btnGo.setText(buttonGoto.text);
                btnGo.setOnClickListener(v -> {
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType,data.bizId);
                    new ZPManager(context, buttonGoto.url).handler();
                    isClickOtherClose = false;
                    dismissAllowingStateLoss();
                });
            } else {
                btnGo.setVisibility(View.GONE);
            }

            buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
            if (buttonCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
                btnCancel.setText(buttonCancel.text);
                btnCancel.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonCancel.actionType,data.bizId);
                    new ZPManager(context, buttonCancel.url).handler();
                    dismissAllowingStateLoss();
                });
            } else {
                btnCancel.setVisibility(View.GONE);
            }
            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id,data.bizId);
        }
    }

    private boolean isShowCenter(){
        GeekF1CommonDialogResponse.ImageBean topIcon = data.topIcon;
        return topIcon != null && !TextUtils.isEmpty(topIcon.url);
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        CommF1DialogTemplate2Task.getInstance().clearData();
        CommF1DialogTemplate2Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType,data.bizId);
        }
    }

    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF1DialogTemplate2Task.getInstance().dialogIsShowing = true;
    }
}
