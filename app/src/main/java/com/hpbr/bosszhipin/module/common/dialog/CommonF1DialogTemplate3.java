package com.hpbr.bosszhipin.module.common.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.bean.CommonStatisticsParamBean;
import com.hpbr.bosszhipin.module.common.dialog.adapter.CommF1DialogTemplate3Adapter;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate3Task;
import com.hpbr.bosszhipin.module.common.dialog.manager.GeekF1DialogRequestParams;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class CommonF1DialogTemplate3 extends BaseBottomSheetFragment {
    private static final String TAG = "CommonF1DialogTemplate4";
    private Context context;
    private CommF1DialogTemplate3Adapter adapter;
    private GeekF1CommonDialogResponse data;
    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;

    private boolean isClickOtherClose = true;

    public static CommonF1DialogTemplate3 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate3 fragment = new CommonF1DialogTemplate3();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_comon_template3, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (context == null) {
            return;
        }
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    handleClickStatistics(buttonClose);
                }
                dismissAllowingStateLoss();
            }
        });
        RecyclerView rvData = view.findViewById(R.id.rvData);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(context, 3);
        rvData.setLayoutManager(gridLayoutManager);
        adapter = new CommF1DialogTemplate3Adapter();
        adapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onNoFastItemClick(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                if (position >= 0 && position < adapter.getData().size()) {
                    GeekF1CommonDialogResponse.MultiContentItem multiContentItem = adapter.getData().get(position);
                    if (multiContentItem != null) {
                        multiContentItem.isSelected = !multiContentItem.isSelected;
                        adapter.notifyDataSetChanged();
                    }

                }

            }
        });
        rvData.setAdapter(adapter);
        if (data != null) {
            GeekF1CommonDialogTextBean title = data.title;
            if (title != null) {
                if (!LList.isEmpty(title.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, title.text, title.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvTitle.setVisibility(View.VISIBLE);
                        tvTitle.setText(builder);
                        tvTitle.setLinksClickable(true);
                        tvTitle.setMaxLines(title.lineNumber);
                    } else {
                        tvTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvTitle.setText(title.text, View.GONE);
                }
            } else {
                tvTitle.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean subTitle = data.subTitle;
            if (subTitle != null) {
                if (!LList.isEmpty(subTitle.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, subTitle.text, subTitle.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvSubTitle.setVisibility(View.VISIBLE);
                        tvSubTitle.setText(builder);
                        tvSubTitle.setLinksClickable(true);
                        tvSubTitle.setMaxLines(subTitle.lineNumber);
                    } else {
                        tvSubTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvSubTitle.setText(subTitle.text, View.GONE);
                }
            } else {
                tvSubTitle.setVisibility(View.GONE);
            }


            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                btnGo.setVisibility(View.VISIBLE);
                btnGo.setText(buttonGoto.text);
                btnGo.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    String json = getSelectJson();
                    try {
                        String expectInfo = URLEncoder.encode(json, "utf-8");
                        handleClickStatistics(buttonGoto);
                        buttonGoto.url = String.format("%s&selectInfo=%s", buttonGoto.url, expectInfo);
                        new ZPManager(context, buttonGoto.url).handler();
                    } catch (UnsupportedEncodingException e) {
                        TLog.info(TAG, e.toString());
                    }
                    dismissAllowingStateLoss();
                });
            } else {
                btnGo.setVisibility(View.GONE);
            }

            buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
            if (buttonCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
                btnCancel.setText(buttonCancel.text);
                btnCancel.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    handleClickStatistics(buttonCancel);
                    new ZPManager(context, buttonCancel.url).handler();
                    dismissAllowingStateLoss();
                });
            } else {
                btnCancel.setVisibility(View.GONE);
            }
            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);


            handleExposureStatistics();
            handleAdapter();
        }
    }

    private void handleExposureStatistics() {
        if (data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10004) {
            GeekF1DialogRequestParams params = data.params;
            CommonStatisticsParamBean statisticsParamBean = new CommonStatisticsParamBean();
            List<GeekF1CommonDialogResponse.MultiContentItem> multiContent = data.multiContent;
            statisticsParamBean.p3 = getCodeByComma(multiContent);
            if (params != null) {
                statisticsParamBean.p4 = params.isGeekPartime ? "2" : "1";
            }
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id, data.bizId,GsonUtils.toJson(statisticsParamBean));
        } else {
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id,data.bizId);

        }

    }

    private void handleClickStatistics(ServerCommonButtonBean buttonBean) {
        if (buttonBean == null) {
            return;
        }
        if (data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10004) {
            GeekF1DialogRequestParams params = data.params;
            CommonStatisticsParamBean statisticsParamBean = new CommonStatisticsParamBean();
            if (adapter != null && !LList.isEmpty(adapter.getData())) {
                statisticsParamBean.p4 = getCodeByComma(getSelectList(adapter.getData()));
            }
            if (params != null) {
                statisticsParamBean.p5 = params.isGeekPartime ? "2" : "1";
            }
            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonBean.actionType, data.bizId,GsonUtils.toJson(statisticsParamBean));
        } else {
            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonBean.actionType,data.bizId);
        }

    }


    private List<GeekF1CommonDialogResponse.MultiContentItem> getSelectList(List<GeekF1CommonDialogResponse.MultiContentItem> list) {
        List<GeekF1CommonDialogResponse.MultiContentItem> selectedList = new ArrayList<>();
        if (!LList.isEmpty(list)) {
            for (GeekF1CommonDialogResponse.MultiContentItem datum : list) {
                if (datum.isSelected) {
                    selectedList.add(datum);
                }
            }
        }
        return selectedList;
    }


    public String getCodeByComma(List<GeekF1CommonDialogResponse.MultiContentItem> list) {
        if (!LList.isEmpty(list)) {
            StringBuilder builderCode = new StringBuilder();
            for (GeekF1CommonDialogResponse.MultiContentItem itemBean : list) {
                if (builderCode.length() > 0) {
                    builderCode.append(",");
                }
                builderCode.append(itemBean.positionCode);
            }
            return builderCode.toString();
        }
        return "";
    }

    private void handleAdapter() {
        List<GeekF1CommonDialogResponse.MultiContentItem> multiContent = data.multiContent;
        if (!LList.isEmpty(multiContent)) {
            adapter.setNewData(multiContent);
        }
    }


    private String getSelectJson() {
        ArrayList<LevelBean> selectList = new ArrayList<>();
        if (adapter != null && !LList.isEmpty(adapter.getData())) {
            for (GeekF1CommonDialogResponse.MultiContentItem datum : adapter.getData()) {
                if (datum.isSelected) {
                    LevelBean levelBean = new LevelBean();
                    levelBean.code = datum.positionCode;
                    levelBean.name = datum.text;
                    selectList.add(levelBean);
                }
            }
        }
        return GsonUtils.getGson().toJson(selectList);
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        CommF1DialogTemplate3Task.getInstance().clearData();
        CommF1DialogTemplate3Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            handleClickStatistics(buttonClose);
        }
    }


    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF1DialogTemplate3Task.getInstance().dialogIsShowing = true;
    }

}
