package com.hpbr.bosszhipin.module.common.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate5Task;
import com.hpbr.bosszhipin.module.resume.views.FlowLayout;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.ServerAfterNameIconBean;
import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.floatlayout.ZPUIFloatLayout;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class CommonF1DialogTemplate5 extends BaseBottomSheetFragment {
    private static final String TAG = "CommonF1DialogTemplate5";
    private Context context;
    private GeekF1CommonDialogResponse data;
    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;

    private boolean isClickOtherClose = true;
    private MTextView tvPositionName;//职位名称
    private MTextView tvSalaryStatus;//薪资-停止招聘
    private MTextView tvCompanyName;//公司名称
    private MTextView tvStage;//融资状态
    private MTextView tvDistance;
    private MTextView tvEmployer;//雇主名称和职位
    private ImageView ivOnlinePoint;
    private ZPUIFloatLayout flAfterNameIcons;
    private FlowLayout flRequireInfo;//公司要求信息 【10年以上】【本科】
    private SimpleDraweeView ivAvatar;//雇主头像
    private MTextView tvScale; // 710 新增，公司规模：100-499人
    private ConstraintLayout clJob;

    public static CommonF1DialogTemplate5 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate5 fragment = new CommonF1DialogTemplate5();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_comon_template5, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (context == null) {
            return;
        }
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        tvPositionName = view.findViewById(R.id.tv_position_name);
        tvSalaryStatus = view.findViewById(R.id.tv_salary_statue);
        flAfterNameIcons = view.findViewById(R.id.fl_after_name_icons);
        tvCompanyName = view.findViewById(R.id.tv_company_name);
        tvStage = view.findViewById(R.id.tv_stage);
        tvScale = view.findViewById(R.id.tv_scale);
        ivAvatar = view.findViewById(R.id.iv_avatar);
        ivOnlinePoint = view.findViewById(R.id.iv_online_point);
        tvDistance = view.findViewById(R.id.tv_distance);
        tvEmployer = view.findViewById(R.id.tv_employer);
        flRequireInfo = view.findViewById(R.id.fl_require_info);
        clJob = view.findViewById(R.id.cl_job);

        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    handleClickStatistics(buttonClose);
                }
                dismissAllowingStateLoss();
            }
        });
        if (data != null) {
            GeekF1CommonDialogTextBean title = data.title;
            if (title != null) {
                if (!LList.isEmpty(title.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, title.text, title.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvTitle.setVisibility(View.VISIBLE);
                        tvTitle.setText(builder);
                        tvTitle.setLinksClickable(true);
                        tvTitle.setMaxLines(title.lineNumber);
                    } else {
                        tvTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvTitle.setText(title.text, View.GONE);
                }
            } else {
                tvTitle.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean subTitle = data.subTitle;
            if (subTitle != null) {
                if (!LList.isEmpty(subTitle.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, subTitle.text, subTitle.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvSubTitle.setVisibility(View.VISIBLE);
                        tvSubTitle.setText(builder);
                        tvSubTitle.setLinksClickable(true);
                        tvSubTitle.setMaxLines(subTitle.lineNumber);
                    } else {
                        tvSubTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvSubTitle.setText(subTitle.text, View.GONE);
                }
            } else {
                tvSubTitle.setVisibility(View.GONE);
            }


            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                btnGo.setVisibility(View.VISIBLE);
                btnGo.setText(buttonGoto.text);
                btnGo.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    new ZPManager(context, buttonGoto.url).handler();
                    handleClickStatistics(buttonGoto);
                    dismissAllowingStateLoss();
                });
            } else {
                btnGo.setVisibility(View.GONE);
            }

            buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
            if (buttonCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
                btnCancel.setText(buttonCancel.text);
                btnCancel.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    handleClickStatistics(buttonCancel);
                    new ZPManager(context, buttonCancel.url).handler();
                    dismissAllowingStateLoss();
                });
            } else {
                btnCancel.setVisibility(View.GONE);
            }
            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            handleExposureStatistics();
            GeekF1CommonDialogResponse.ExtendBean extend = data.extend;
            if (extend != null) {
                ServerJobCardBean job = extend.job;
                if (job != null) {
                    handleJobInfo(job);
                }

            }
        }
    }

    private void handleJobInfo(ServerJobCardBean bean) {
        tvPositionName.setText(bean.jobName);
        tvSalaryStatus.setText(bean.salaryDesc);
        flAfterNameIcons.setVisibility(View.GONE);
        flAfterNameIcons.removeAllViews();
        if (!LList.isEmpty(bean.afterNameIcons)) {
            flAfterNameIcons.setVisibility(View.VISIBLE);
            for (ServerAfterNameIconBean icon : bean.afterNameIcons) {
                if (icon == null || TextUtils.isEmpty(icon.url) || icon.height <= 0 || icon.width <= 0)
                    continue;
                int iconHeight = ZPUIDisplayHelper.dp2px(context, 16f);
                int iconWidth = iconHeight * icon.width / icon.height;

                SimpleDraweeView ivIcon = new SimpleDraweeView(context);
                ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(iconWidth, iconHeight);
                ivIcon.setLayoutParams(params);
                ivIcon.setImageURI(icon.url);
                flAfterNameIcons.addView(ivIcon);
            }
        }


        if (ivOnlinePoint != null) {
            if (!bean.online) {
                ivOnlinePoint.setVisibility(View.GONE);
            } else {
                ivOnlinePoint.setVisibility(View.VISIBLE);
            }
        }

        //开始绘制标签
        if (!LList.isEmpty(bean.jobLabels)) {
            for (String text : bean.jobLabels) {
                if (TextUtils.isEmpty(text)) continue;
                flRequireInfo.addView(getLabel(context, text));
            }
        }

        tvStage.setText(bean.brandStageName, View.GONE);
        tvScale.setText(bean.brandScaleName, View.GONE);
        tvCompanyName.setText(bean.brandName, View.GONE);
        //绘制标签结束
        ViewCommon.setAvatar(ivAvatar, 0, bean.bossAvatar);
        String employerInfo = StringUtil.connectTextWithChar(" · ", bean.bossName, bean.bossTitle);
        tvEmployer.setText(employerInfo);

        //距离
        String distance = bean.distance;
        //卡片样式为2时，展示对应的城市商圈信息
        distance = StringUtil.connectTextWithChar("  ", distance, bean.cityName, bean.areaDistrict, bean.businessDistrict);
        tvDistance.setText(distance, View.GONE);

        clJob.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                ToastUtils.showText("请先调整隐私设置");
                AnalyticsFactory.create().action("biz-item-AccurateSearch-F1hunterrecall-jobclick").build();

            }
        });

    }


    private FrameLayout getLabel(Context context, String text) {
        FrameLayout layout = new FrameLayout(context);
        layout.setPadding(0, 0, Scale.dip2px(context, 6), 0);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layout.setLayoutParams(params);

        int paddingHorizontal = ZPUIDisplayHelper.dp2px(context, 7);
        int paddingVertical = ZPUIDisplayHelper.dp2px(context, 3);
        MTextView textView = new MTextView(context);
        textView.setText(text);
        textView.setSingleLine();
        textView.setGravity(Gravity.CENTER);
        textView.setBackgroundResource(R.drawable.bg_storke_e0e0e0_4_corner);
        textView.setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        textView.setTextColor(ContextCompat.getColor(context, R.color.color_FF525252_FF9E9EA1));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        layout.addView(textView);

        return layout;
    }

    private void handleClickStatistics(ServerCommonButtonBean buttonBean) {
        if (buttonBean == null || data == null) {
            return;
        }
        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonBean.actionType,data.bizId);

    }


    private void handleExposureStatistics() {
        GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id,data.bizId);
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        CommF1DialogTemplate5Task.getInstance().clearData();
        CommF1DialogTemplate5Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            handleClickStatistics(buttonClose);
        }
    }


    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF1DialogTemplate5Task.getInstance().dialogIsShowing = true;
    }

}
