package com.hpbr.bosszhipin.module.common.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.bean.CommonStatisticsParamBean;
import com.hpbr.bosszhipin.module.common.dialog.adapter.CommF1DialogTemplate6Adapter;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate6Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.ui.wheel.WheelView;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class CommonF1DialogTemplate6 extends BaseBottomSheetFragment {
    private GeekF1CommonDialogResponse data;
    private Context context;

    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;
    private WheelView wheelMonth;
    private boolean isClickOtherClose = true;

    private int yearIndex;
    private int monthIndex;
    private List<GeekF1CommonDialogResponse.RollerConfigItem> rollerConfig;

    public static CommonF1DialogTemplate6 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate6 fragment = new CommonF1DialogTemplate6();
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
        setDraggable(false);
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_common_template6, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        SimpleDraweeView sdvIcon = view.findViewById(R.id.sdvIcon);
        SimpleDraweeView sdvTopIcon = view.findViewById(R.id.sdvTopIcon);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        WheelView wheelYear = view.findViewById(R.id.wheelYear);
        wheelMonth = view.findViewById(R.id.wheelMonth);

        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                }
                dismissAllowingStateLoss();
            }
        });

        if (data != null) {
            GeekF1CommonDialogTextBean title = data.title;
            if (title != null) {
                if (context != null && !LList.isEmpty(title.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, title.text, title.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvTitle.setVisibility(View.VISIBLE);
                        tvTitle.setText(builder);
                        tvTitle.setLinksClickable(true);
                    } else {
                        tvTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvTitle.setText(title.text, View.GONE);
                }
                tvTitle.setGravity(title.getGravity());
                tvTitle.setMaxLines(title.lineNumber);
            } else {
                tvTitle.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean subTitle = data.subTitle;
            if (subTitle != null) {
                if (context != null && !LList.isEmpty(subTitle.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, subTitle.text, subTitle.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvSubTitle.setVisibility(View.VISIBLE);
                        tvSubTitle.setText(builder);
                        tvSubTitle.setLinksClickable(true);
                    } else {
                        tvSubTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvSubTitle.setText(subTitle.text, View.GONE);
                }
                tvSubTitle.setGravity(subTitle.getGravity());
                tvSubTitle.setMaxLines(subTitle.lineNumber);
            } else {
                tvSubTitle.setVisibility(View.GONE);
            }
            GeekF1CommonDialogResponse.ImageBean imageBean = data.image;

            if (imageBean != null && !TextUtils.isEmpty(imageBean.url)) {
                sdvIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams params = sdvIcon.getLayoutParams();
                params.height = imageBean.height;
                params.width = imageBean.width;
                sdvIcon.setLayoutParams(params);
                sdvIcon.setImageURI(imageBean.url);
            } else {
                if (!TextUtils.isEmpty(data.imageUrl)) {
                    sdvIcon.setVisibility(View.VISIBLE);
                    sdvIcon.setImageURI(data.imageUrl);
                } else {
                    sdvIcon.setVisibility(View.GONE);
                }
            }
            GeekF1CommonDialogResponse.ImageBean topIcon = data.topIcon;
            if (topIcon != null && !TextUtils.isEmpty(topIcon.url)) {
                sdvTopIcon.setVisibility(View.VISIBLE);
                if (topIcon.height > 0 && topIcon.width > 0) {
                    ViewGroup.LayoutParams params = sdvTopIcon.getLayoutParams();
                    params.height = topIcon.height;
                    params.width = topIcon.width;
                    sdvTopIcon.setLayoutParams(params);
                }
                sdvTopIcon.setImageURI(topIcon.url);
            } else {
                sdvTopIcon.setVisibility(View.GONE);
            }

            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                btnGo.setVisibility(View.VISIBLE);
                btnGo.setText(buttonGoto.text);
                btnGo.setOnClickListener(v -> {
                    Map<String,String> map =new HashMap<>();
                    map.put("url",buttonGoto.url);
                    if (data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10015) {
                        StringBuilder stringBuilder = new StringBuilder();
                        if (!LList.isEmpty(rollerConfig) && yearIndex >= 0 && yearIndex < rollerConfig.size()) {
                            GeekF1CommonDialogResponse.RollerConfigItem item = rollerConfig.get(yearIndex);
                            stringBuilder.append(item.value);
                            if (!LList.isEmpty(item.rightConfig) && monthIndex >= 0 && monthIndex < item.rightConfig.size()) {
                                stringBuilder.append(item.rightConfig.get(monthIndex).value);
                            }
                            map.put("dateTime",stringBuilder.toString());
                            buttonGoto.url = String.format("%s&dateTime=%s", buttonGoto.url, stringBuilder);
                        }
                    }
                    Map<String,Map<String,String>> mapP4 =new HashMap<>();
                    mapP4.put("p4",map);
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, GsonUtils.toJson(mapP4),data.bizId);
                    new ZPManager(context, buttonGoto.url).handler();
                    isClickOtherClose = false;
                    dismissAllowingStateLoss();
                });
            } else {
                btnGo.setVisibility(View.GONE);
            }

            buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
            if (buttonCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
                btnCancel.setText(buttonCancel.text);
                btnCancel.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonCancel.actionType, data.bizId);
                    new ZPManager(context, buttonCancel.url).handler();
                    dismissAllowingStateLoss();
                });
            } else {
                btnCancel.setVisibility(View.GONE);
            }
            GeekF1CommonDialogResponse.ExtendBean extend = data.extend;

            if (extend != null) {
                rollerConfig = extend.rollerConfig;
                if (!LList.isEmpty(rollerConfig)) {
                    wheelYear.setViewAdapter(new CommF1DialogTemplate6Adapter(context, rollerConfig));
                    yearIndex = 0;
                    for (int i = 0; i < rollerConfig.size(); i++) {
                        if (rollerConfig.get(i).select) {
                            yearIndex = i;
                            break;
                        }
                    }
                    wheelYear.setCurrentItem(yearIndex);
                    wheelYear.addChangingListener((wheel, oldValue, newValue) -> {
                        yearIndex = newValue;
                        if (0 <= yearIndex && yearIndex < rollerConfig.size()) {
                            GeekF1CommonDialogResponse.RollerConfigItem item = rollerConfig.get(yearIndex);
                            refreshMonthAdapter(item.rightConfig);
                        }
                    });
                    GeekF1CommonDialogResponse.RollerConfigItem item = rollerConfig.get(yearIndex);
                    refreshMonthAdapter(item.rightConfig);
                    initWheelStyle(wheelYear, R.drawable.geek_f1_bg_wheel_val_start);
                    initWheelStyle(wheelMonth,R.drawable.geek_f1_bg_wheel_val_end);
                }
            }


            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id, data.bizId);
        }
    }


    protected void initWheelStyle(WheelView wheelView, @DrawableRes int selectedBackgroundRes) {

        wheelView.setVisibleItems(5);

        wheelView.setCenterOffsetRatio(1f);

        wheelView.setBackgroundCenterDrawable(ContextCompat.getDrawable(context, selectedBackgroundRes));

        wheelView.setWheelBackground(R.color.color_W_Card_02);
        wheelView.setWheelForeground(R.color.transparent);

        wheelView.setShadowColor(0xaaffffff, 0x88ffffff, 0x00ffffff);

        wheelView.setDrawShadows(true);

    }

    private void refreshMonthAdapter(List<GeekF1CommonDialogResponse.RollerConfigItem> rightConfig) {
        if (!LList.isEmpty(rightConfig)) {
            wheelMonth.setViewAdapter(new CommF1DialogTemplate6Adapter(context, rightConfig));
            monthIndex = 0;
            for (int i = 0; i < rightConfig.size(); i++) {
                if (rightConfig.get(i).select) {
                    monthIndex = i;
                }
            }
            wheelMonth.setCurrentItem(monthIndex);
        } else {
            wheelMonth.setViewAdapter(new CommF1DialogTemplate6Adapter(context, new ArrayList<>()));
        }
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        CommF1DialogTemplate6Task.getInstance().clearData();
        CommF1DialogTemplate6Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
        }
    }

    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF1DialogTemplate6Task.getInstance().dialogIsShowing = true;
    }
}
