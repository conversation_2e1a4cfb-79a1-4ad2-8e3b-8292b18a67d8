package com.hpbr.bosszhipin.module.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate7Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.monch.lbase.util.LList;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import androidx.core.content.ContextCompat;

public class CommonF1DialogTemplate7 {
    private GeekF1CommonDialogResponse data;

    public static CommonF1DialogTemplate7 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate7 template7 = new CommonF1DialogTemplate7();
        template7.data = data;
        return template7;
    }

    private boolean isClickOtherClose = true;
    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;

    private Activity activity = null;
    private DialogUtils dialogUtils;

    public void show(Context context) {

        if (context instanceof Activity) {
            activity = (Activity) context;
        }
        if (ActivityUtils.isInvalid(activity)) {
            return;
        }

        String title = "";
        GeekF1CommonDialogTextBean titleBean = data.title;
        if (titleBean != null) {
            title = titleBean.text;
        }

        CharSequence des = "";
        GeekF1CommonDialogTextBean contentBean = data.content;
        if (contentBean != null) {
            if (!LList.isEmpty(contentBean.highLight)) {
                des = ViewCommon.getExchangedText(contentBean.highLight, contentBean.text, activity, ContextCompat.getColor(context, R.color.app_green_dark));
            } else {
                des = contentBean.text;
            }
        }


        buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
        buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
        buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
        DialogUtils.Builder build = new DialogUtils.Builder(activity)
                .setTitle(title)
                .setTitleIsNullGone(true)
                .setOnDialogDismiss(dialogInterface -> {
                    CommF1DialogTemplate7Task.getInstance().dialogIsShowing = false;
                    CommF1DialogTemplate7Task.getInstance().clearData();
                    if (isClickOtherClose && data != null && buttonClose != null) {
                        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                    }
                })

                .setDesc(des);


        if (buttonClose != null) {
            build.setDismissListener(view -> {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                }
                dismiss();
            }).setCancelable(true);
        } else {
            build.setCancelable(false);
        }


        GeekF1CommonDialogResponse.ImageBean topIcon = data.topIcon;

        if (topIcon != null && !TextUtils.isEmpty(topIcon.url)) {
            float ratioHtW = 0.5f;
            if (topIcon.width > 0 && topIcon.height > 0) {
                ratioHtW = topIcon.height / topIcon.width;
            }

            DialogUtils.ImageParams imageParams = new DialogUtils.ImageParams(DialogUtils.ImageParams.TOP_BACKGROUND, Uri.parse(topIcon.url), ratioHtW);
            build.setTopBackground(imageParams);
        }
        if (buttonGoto != null && buttonCancel != null) {
            build.setDoubleButton();
        } else {
            build.setSingleButton();
        }


        if (buttonGoto != null && !TextUtils.isEmpty(buttonGoto.text)) {
            build.setPositiveAction(buttonGoto.text, v12 -> {
                isClickOtherClose = false;
                GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, data.bizId);
                new ZPManager(activity, buttonGoto.url).handler();
                dismiss();
            });
        }

        if (buttonCancel != null && !TextUtils.isEmpty(buttonCancel.text)) {
            build.setNegativeAction(buttonCancel.text, v12 -> {
                isClickOtherClose = false;
                GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonCancel.actionType, data.bizId);
                new ZPManager(activity, buttonCancel.url).handler();
                dismiss();
            });
        }

        dialogUtils = build.build();
        dialogUtils.show();
        GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id,data.bizId);
        CommF1DialogTemplate7Task.getInstance().dialogIsShowing = true;
    }

    private void dismiss() {
        if (dialogUtils != null && ActivityUtils.isValid(activity)) {
            dialogUtils.dismiss();
        }
    }
}
