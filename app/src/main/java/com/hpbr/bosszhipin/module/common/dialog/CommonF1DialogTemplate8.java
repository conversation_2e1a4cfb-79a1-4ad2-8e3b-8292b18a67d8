package com.hpbr.bosszhipin.module.common.dialog;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate8Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.permission.PermissionCallback;
import com.hpbr.bosszhipin.utils.permission.PermissionData;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;

import androidx.annotation.NonNull;

public class CommonF1DialogTemplate8 {
    private GeekF1CommonDialogResponse data;
    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;

    public static CommonF1DialogTemplate8 getInstance(GeekF1CommonDialogResponse data) {
        CommonF1DialogTemplate8 template8 = new CommonF1DialogTemplate8();
        template8.data = data;
        return template8;
    }
    public void show(BaseActivity activity) {

        if (ActivityUtils.isInvalid(activity)) {
            return;
        }

        if (!DataStarGray.getInstance().isShowF1NewGuideABTest()){
            return;
        }
        NotificationCheckUtils.dialogIsShowing = true;
        if (DataStarGray.getInstance().isShowF1NewGuideABTest1()&& data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10025){
            GeekNotifyDialogAB geekNotifyDialogAB=  GeekNotifyDialogAB.getInstance(data);
            geekNotifyDialogAB.show(activity.getSupportFragmentManager(), GeekNotifyDialogAB.class.getSimpleName());
        }else {
            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            NotificationCheckUtils.requestNotificationPermissionABTest2(activity, new PermissionCallback<PermissionData>() {
                @Override
                public void onResult(boolean yes, @NonNull PermissionData permission) {
                    {
                        CommF1DialogTemplate8Task.getInstance().clearData();
                        CommF1DialogTemplate8Task.getInstance().dialogIsShowing =false;
                        if (yes){
                            if (permission!=null){ //系统弹窗 点击了允许获得权限
                                if (buttonGoto!=null){
                                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, data.bizId);
                                }
                            }
                        }else {
                            ToastUtils.showText("请开启通知权限");
                            if (permission!=null){
                                if (permission.dialogClickType==0){//系统弹窗用户点击了拒绝
                                    if (buttonClose!=null){
                                        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                                    }
                                }else if (permission.dialogClickType==1){// app 弹窗 用户点击了取消
                                    if (buttonClose!=null){
                                        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                                    }
                                }else if (permission.dialogClickType==2){// app 弹窗用户点击了去设置
                                    if (buttonGoto!=null){
                                        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, data.bizId);
                                    }
                                }
                            }
                        }
                    }
                }

                @Override
                public void onRemindersClick(int clickType) {
                    if (clickType==PermissionCallback.REMINDERS_CLICK_OK){
                        if (buttonGoto!=null){
                            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, data.bizId);
                        }
                    }else {
                        if (buttonClose!=null){
                            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                        }
                    }
                }
            });
            CommF1DialogTemplate8Task.getInstance().dialogIsShowing =true;
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id, data.bizId);
        }
    }
}
