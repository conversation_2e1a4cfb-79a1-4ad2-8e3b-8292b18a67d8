package com.hpbr.bosszhipin.module.common.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.bean.CommonStatisticsParamBean;
import com.hpbr.bosszhipin.module.common.dialog.adapter.CommF1DialogTemplate4Adapter;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF2DialogTemplate4Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.GeekF2CommonDialogResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class CommonF2DialogTemplate4 extends BaseBottomSheetFragment {
    private static final String TAG = "CommonF2DialogTemplate4";
    private Context context;
    private CommF1DialogTemplate4Adapter adapter;
    private GeekF2CommonDialogResponse data;
    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private ServerCommonButtonBean buttonCancel;

    private boolean isClickOtherClose = true;

    public static CommonF2DialogTemplate4 getInstance(GeekF2CommonDialogResponse data) {
        CommonF2DialogTemplate4 fragment = new CommonF2DialogTemplate4();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
        setDraggable(false);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_comon_template4, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (context == null) {
            return;
        }
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        MTextView tvBottomTipToAdd = view.findViewById(R.id.tvBottomTipToAdd);
        View viewDivide = view.findViewById(R.id.viewDivide);
        MTextView tvBottomTip = view.findViewById(R.id.tvBottomTip);
        SimpleDraweeView sdvBottomTip = view.findViewById(R.id.sdvBottomTip);
        ivCancel.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                if (data != null && buttonClose != null) {
                    handleClickStatistics(buttonClose);
                }
                dismissAllowingStateLoss();
            }
        });
        RecyclerView rvData = view.findViewById(R.id.rvData);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context);
        rvData.setLayoutManager(linearLayoutManager);
        adapter = new CommF1DialogTemplate4Adapter();
        adapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @SuppressLint("NotifyDataSetChanged")
            @Override
            public void onNoFastItemClick(BaseQuickAdapter baseQuickAdapter, View view, int position) {
                if (data != null && data.multiContentSelect == GeekF1CommonDialogResponse.SINGLE_CONTENT_SELECT) {
                    if (position >= 0 && position < adapter.getData().size()) {
                        GeekF1CommonDialogResponse.MultiContentItem multiContentItem = adapter.getData().get(position);
                        if (multiContentItem != null && !multiContentItem.isSelected) {
                            List<GeekF1CommonDialogResponse.MultiContentItem> list = adapter.getData();
                            for (int i = 0; i < list.size(); i++) {
                                GeekF1CommonDialogResponse.MultiContentItem multiContentItemI = adapter.getData().get(i);
                                multiContentItemI.isSelected = i == position;
                            }
                            adapter.notifyDataSetChanged();
                        }
                    }

                } else {
                    if (position >= 0 && position < adapter.getData().size()) {
                        GeekF1CommonDialogResponse.MultiContentItem multiContentItem = adapter.getData().get(position);
                        if (multiContentItem != null) {
                            multiContentItem.isSelected = !multiContentItem.isSelected;
                            adapter.notifyDataSetChanged();
                        }
                    }
                }
            }
        });
        rvData.setAdapter(adapter);
        if (data != null) {
            GeekF1CommonDialogTextBean title = data.title;
            if (title != null) {
                if (!LList.isEmpty(title.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, title.text, title.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvTitle.setVisibility(View.VISIBLE);
                        tvTitle.setText(builder);
                        tvTitle.setLinksClickable(true);
                        tvTitle.setMaxLines(title.lineNumber);
                    } else {
                        tvTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvTitle.setText(title.text, View.GONE);
                }
            } else {
                tvTitle.setVisibility(View.GONE);
            }

            GeekF1CommonDialogTextBean subTitle = data.subTitle;
            if (subTitle != null) {
                if (!LList.isEmpty(subTitle.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClick(context, subTitle.text, subTitle.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvSubTitle.setVisibility(View.VISIBLE);
                        tvSubTitle.setText(builder);
                        tvSubTitle.setLinksClickable(true);
                        tvSubTitle.setMaxLines(subTitle.lineNumber);
                    } else {
                        tvSubTitle.setVisibility(View.GONE);
                    }
                } else {
                    tvSubTitle.setText(subTitle.text, View.GONE);
                }
            } else {
                tvSubTitle.setVisibility(View.GONE);
            }


            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                btnGo.setVisibility(View.VISIBLE);
                btnGo.setText(buttonGoto.text);
                btnGo.setOnClickListener(v -> {
                    String selectedUrl = getSelectedUrl();
                    if (data != null && data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10020 && data.multiContentSelect == GeekF1CommonDialogResponse.SINGLE_CONTENT_SELECT && !TextUtils.isEmpty(selectedUrl)) {
                        isClickOtherClose = false;
                        new ZPManager(context, selectedUrl).handler();
                        dismissAllowingStateLoss();
                    } else {
                        String json = getSelectJson();
                        if (!TextUtils.isEmpty(json)) {
                            try {
                                isClickOtherClose = false;
                                String expectInfo = URLEncoder.encode(json, "utf-8");
                                handleClickStatistics(buttonGoto);
                                buttonGoto.url = String.format("%s&expectInfo=%s&templateId=%s", buttonGoto.url, expectInfo, data.id);
                                new ZPManager(context, buttonGoto.url).handler();
                            } catch (UnsupportedEncodingException e) {
                                TLog.info(TAG, e.toString());
                            }
                            dismissAllowingStateLoss();
                        } else {
                            if (data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10005) {
                                ToastUtils.showText("请选择想添加的期望");
                            } else {
                                ToastUtils.showText("请选择至少一项");
                            }
                        }
                    }
                });
            } else {
                btnGo.setVisibility(View.GONE);
            }

            buttonCancel = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CANCEL);
            if (buttonCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
                btnCancel.setText(buttonCancel.text);
                btnCancel.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    handleClickStatistics(buttonCancel);
                    new ZPManager(context, buttonCancel.url).handler();
                    dismissAllowingStateLoss();
                });
            } else {
                btnCancel.setVisibility(View.GONE);
            }

            GeekF1CommonDialogResponse.TipContent tipContent = data.tipContent;
            if (tipContent != null && !TextUtils.isEmpty(tipContent.text)) {
                tvBottomTip.setVisibility(View.VISIBLE);
                tvBottomTip.setText(tipContent.text);
                tvBottomTip.setMaxLines(Math.max(tipContent.lineNumber, 1));
                if (!TextUtils.isEmpty(tipContent.icon)) {
                    sdvBottomTip.setImageURI(tipContent.icon);
                    sdvBottomTip.setVisibility(View.VISIBLE);
                } else {
                    sdvBottomTip.setVisibility(View.GONE);
                }
                ServerCommonButtonBean buttonBean = tipContent.button;
                if (buttonBean != null && !TextUtils.isEmpty(buttonBean.text)) {
                    tvBottomTipToAdd.setVisibility(View.VISIBLE);
                    tvBottomTipToAdd.setText(buttonBean.text);
                    tvBottomTipToAdd.setOnClickListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            isClickOtherClose = false;
                            new ZPManager(context, buttonBean.url).handler();
                            dismissAllowingStateLoss();
                        }
                    });
                }

            } else {
                tvBottomTipToAdd.setVisibility(View.GONE);
                viewDivide.setVisibility(View.GONE);
                tvBottomTip.setVisibility(View.GONE);
                sdvBottomTip.setVisibility(View.GONE);
            }
            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            handleAdapter();
            handleExposureStatistics();
        }
    }


    private void handleClickStatistics(ServerCommonButtonBean buttonBean) {
        if (buttonBean == null || adapter == null || data == null) {
            return;
        }
        CommonStatisticsParamBean statisticsParamBean = new CommonStatisticsParamBean();
        List<GeekF1CommonDialogResponse.MultiContentItem> selectedList = getSelectList(adapter.getData());
        if (!LList.isEmpty(selectedList)) {
            statisticsParamBean.p4 = GsonUtils.toJson(selectedList);
        }
        GeekF1Util.clickF2CommonDialogSubmit(data.templateType, data.id, buttonBean.actionType, data.bizId, GsonUtils.toJson(statisticsParamBean));
    }


    private List<GeekF1CommonDialogResponse.MultiContentItem> getSelectList(List<GeekF1CommonDialogResponse.MultiContentItem> list) {
        List<GeekF1CommonDialogResponse.MultiContentItem> selectedList = new ArrayList<>();
        if (!LList.isEmpty(list)) {
            for (GeekF1CommonDialogResponse.MultiContentItem datum : list) {
                if (datum.isSelected) {
                    selectedList.add(datum);
                }
            }
        }
        return selectedList;
    }

    private void handleExposureStatistics() {
        CommonStatisticsParamBean statisticsParamBean = new CommonStatisticsParamBean();
        List<GeekF1CommonDialogResponse.MultiContentItem> multiContentList = data.multiContent;
        if (!LList.isEmpty(multiContentList)) {
            statisticsParamBean.p3 = GsonUtils.toJson(multiContentList);
        }
        GeekF1Util.exposureF2CommonDialogSubmit(data.templateType, data.id, data.bizId, GsonUtils.toJson(statisticsParamBean));
    }

    private void handleAdapter() {
        List<GeekF1CommonDialogResponse.MultiContentItem> multiContent = data.multiContent;
        if (!LList.isEmpty(multiContent)) {
            if (multiContent.size() > 1) {
                for (int i = 0; i < multiContent.size(); i++) {
                    multiContent.get(i).isShowDivider = i != multiContent.size() - 1;
                }
            } else {
                multiContent.get(0).isShowDivider = false;
            }
            if (data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10006 || data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10007 || data.id == CommonGeekF1DialogConstant.TEMPLATE_ID_10020) {
                multiContent.get(0).isSelected = true;
            } else {
                for (GeekF1CommonDialogResponse.MultiContentItem serverF1guidexpectBean : multiContent) {
                    serverF1guidexpectBean.isSelected = true;
                }
            }
            adapter.setNewData(multiContent);

        }
    }


    private String getSelectedUrl() {
        String url = "";
        if (adapter != null && !LList.isEmpty(adapter.getData())) {
            for (GeekF1CommonDialogResponse.MultiContentItem datum : adapter.getData()) {
                if (datum.isSelected) {
                    url = datum.url;
                }
            }
        }
        return url;
    }


    private String getSelectJson() {
        List<GeekF1CommonDialogResponse.ExpectInfo> list = new ArrayList<>();
        if (adapter != null && !LList.isEmpty(adapter.getData())) {
            for (GeekF1CommonDialogResponse.MultiContentItem datum : adapter.getData()) {
                if (datum.isSelected) {
                    if (datum.expect != null) {
                        list.add(datum.expect);
                    }
                }
            }
        }
        String json = "";

        if (!LList.isEmpty(list)) {
            json = GsonUtils.getGson().toJson(list);
        }
        return json;
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        CommF2DialogTemplate4Task.getInstance().clearData();
        CommF2DialogTemplate4Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            handleClickStatistics(buttonClose);
        }
    }


    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF2DialogTemplate4Task.getInstance().dialogIsShowing = true;
    }

}
