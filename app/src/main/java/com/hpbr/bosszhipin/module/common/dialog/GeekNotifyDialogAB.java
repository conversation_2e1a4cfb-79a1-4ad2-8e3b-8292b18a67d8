package com.hpbr.bosszhipin.module.common.dialog;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.dialog.manager.CommF1DialogTemplate8Task;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.ServerMessagePushSettingOptionBean;
import net.bosszhipin.api.bean.ServerMessagePushSettingOptionGroupBean;
import net.bosszhipin.api.bean.ServerSettingsLayoutBean;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class GeekNotifyDialogAB extends BaseBottomSheetFragment {
    private GeekF1CommonDialogResponse data;
    private Context context;

    private ServerCommonButtonBean buttonGoto;
    private ServerCommonButtonBean buttonClose;
    private boolean isClickOtherClose = true;
    private ImageView mOtherCheckBox;
    private boolean isOtherSwitchChecked = true;

    public static GeekNotifyDialogAB getInstance(GeekF1CommonDialogResponse data) {
        GeekNotifyDialogAB fragment = new GeekNotifyDialogAB();
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
        setDraggable(false);
    }


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_geek_notify_ab, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View dialogView, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(dialogView, savedInstanceState);
        mOtherCheckBox = dialogView.findViewById(R.id.other_notify_checkbox);
        ZPUIRoundButton toOpenButton = dialogView.findViewById(R.id.btn_tp_open);
        ImageView closeView = dialogView.findViewById(R.id.iv_close);
        MTextView tvContent = dialogView.findViewById(R.id.tvContent);
        closeView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isClickOtherClose = false;
                dismissAllowingStateLoss();
                if (buttonClose != null) {
                    GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
                }
            }
        });
        SimpleDraweeView sdvIcon = dialogView.findViewById(R.id.sdvTopIcon);

        mOtherCheckBox.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                isOtherSwitchChecked = !isOtherSwitchChecked;
                mOtherCheckBox.setImageResource(isOtherSwitchChecked ? R.mipmap.ic_checkbox_checked : R.mipmap.ic_checkbox_default);
            }
        });

        if (data != null) {
            GeekF1CommonDialogResponse.ImageBean imageBean = data.image;
            if (imageBean != null && !TextUtils.isEmpty(imageBean.url)) {
                sdvIcon.setVisibility(View.VISIBLE);
                ViewGroup.LayoutParams params = sdvIcon.getLayoutParams();
                if (imageBean.width > 0 && imageBean.height > 0) {
                    float aspectRatio = (float) imageBean.width / imageBean.height;
                    sdvIcon.setAspectRatio(aspectRatio);
                }
                sdvIcon.setLayoutParams(params);
                sdvIcon.setImageURI(imageBean.url);
            } else {
//                sdvIcon.setImageResource(); 兜底 等待UI给图
            }


            GeekF1CommonDialogTextBean content = data.content;
            if (content != null) {
                if (!LList.isEmpty(content.highLight)) {
                    SpannableStringBuilder builder = ViewCommon.setTextHighLightWithClickByDefColor(context, content.text, content.highLight, ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder != null) {
                        tvContent.setVisibility(View.VISIBLE);
                        tvContent.setText(builder);
                        tvContent.setLinksClickable(true);
                    } else {
                        tvContent.setText("选择通知接收范围");
                    }
                } else {
                    tvContent.setText(content.text);
                }
                tvContent.setGravity(content.getGravity());
                tvContent.setMaxLines(content.lineNumber);
            } else {
                tvContent.setText("选择通知接收范围");
            }


            buttonGoto = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_GO_TO);
            if (buttonGoto != null) {
                toOpenButton.setVisibility(View.VISIBLE);
                toOpenButton.setText(buttonGoto.text);
                toOpenButton.setOnClickListener(v -> {
                    isClickOtherClose = false;
                    SimpleApiRequest.GET(ChatUrlConfig.URL_GEEK_BATCH_OPEN_PUSH).addParam("otherPush", isOtherSwitchChecked ? 1 : 0).execute();
                    refreshSettingSwitches();
                    if (context instanceof Activity && ActivityUtils.isValid(context)) {
                        Activity activity = (Activity) context;
                        if (DataStarGray.getInstance().f1PushGuideClickABTest()) {
                            NotificationCheckUtils.requestNotificationPermissionToSet(activity);
                        } else {
                            NotificationCheckUtils.requestNotificationPermission(activity, null);
                        }
                    }
                    if (buttonGoto != null) {
                        GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonGoto.actionType, data.bizId);
                    }
                    dismissAllowingStateLoss();
                });
            } else {
                toOpenButton.setVisibility(View.GONE);
            }

            buttonClose = GeekF1Util.getButtonViaType(data.buttonList, GeekF1Constant.BUTTON_ACTION_TYPE_CLOSE);
            GeekF1Util.exposureCommonDialogSubmit(data.templateType, data.id, data.bizId);
        }
    }


    private void refreshSettingSwitches() {
        ServerSettingsLayoutBean settingsLayoutBean = NotifyUtils.getSettingsLayoutBean();
        if (settingsLayoutBean == null || LList.isEmpty(settingsLayoutBean.optionGroups)) {
            return;
        }
        for (ServerMessagePushSettingOptionGroupBean optionGroup : settingsLayoutBean.optionGroups) {
            if (optionGroup != null && !LList.isEmpty(optionGroup.options)) {
                for (ServerMessagePushSettingOptionBean option : optionGroup.options) {
                    // 1105.603 聊天消息推送开关一定开启
                    if (option.notifyType == 132) {
                        option.settingType = 4;
                        continue;
                    }
                    // 1105.603 选中开启其他开关选项才会开启其他开关
                    if (isOtherSwitchChecked) {
                        option.settingType = 4;
                    }
                }
            }
        }

        NotifyUtils.saveSettingsLayoutBean(settingsLayoutBean);
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        NotificationCheckUtils.dialogIsShowing = false;
        CommF1DialogTemplate8Task.getInstance().clearData();
        CommF1DialogTemplate8Task.getInstance().dialogIsShowing = false;
        if (isClickOtherClose && data != null && buttonClose != null) {
            GeekF1Util.clickCommonDialogSubmit(data.templateType, data.id, buttonClose.actionType, data.bizId);
        }
    }

    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        CommF1DialogTemplate8Task.getInstance().dialogIsShowing = true;
    }
}
