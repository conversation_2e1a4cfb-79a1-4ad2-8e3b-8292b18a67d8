package com.hpbr.bosszhipin.module.common.dialog;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.adapter.utils.HolderUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ：RequestJobStatusDialog
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/3/3  2:41 PM
 */
public class RequestJobStatusDialog extends BaseBottomSheetFragment {

    @NonNull
    private final OnChooseWorkStatusListener listener;
    private TextView subTitleTextView;

    public RequestJobStatusDialog(@NonNull OnChooseWorkStatusListener listener) {
        setHeightWrapContent(true);
        this.listener = listener;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_request_job_status, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        view.findViewById(R.id.close).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissAllowingStateLoss();
            }
        });

        subTitleTextView = view.findViewById(R.id.tips2);
        subTitleTextView.setVisibility(View.VISIBLE);


        RecyclerView workStatusRv = view.findViewById(R.id.workStatusRv);
        workStatusRv.setAdapter(new WorkStatusAdapter(new OnChooseWorkStatusListener() {
            @Override
            public void onChooseWorkStatus(@NonNull LevelBean workStatus) {
                listener.onChooseWorkStatus(workStatus);
                dismissAllowingStateLoss();
            }

            @Override
            public int getInitialWorkStatus() {
                return listener.getInitialWorkStatus();
            }
        }));

        listener.onApplyStatusLoadCompleted(false);
    }

    private static class WorkStatusAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

        @NonNull
        private final OnChooseWorkStatusListener listener;
        @NonNull
        private final List<LevelBean> jobStatusList = new ArrayList<>();

        public WorkStatusAdapter(@NonNull OnChooseWorkStatusListener listener) {
            this.listener = listener;

            List<LevelBean> workStatusList;
            if (UserManager.isGeekStudent()) {
                workStatusList = VersionAndDatasCommon.getInstance().getStudentWorkStatusList();
            } else {
                workStatusList = VersionAndDatasCommon.getInstance().getJobStatusList();
            }

            if (workStatusList != null && workStatusList.size() > 0) {
                this.jobStatusList.addAll(workStatusList);
            }
        }

        @NonNull
        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new RecyclerView.ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_request_job_status, parent, false)) {

                {
                    itemView.setOnClickListener(new OnClickNoFastListener() {
                        @Override
                        public void onNoFastClick(View v) {
                            listener.onChooseWorkStatus(jobStatusList.get(getAdapterPosition()));
                        }
                    });
                }

                @Override
                public String toString() {
                    return super.toString();
                }
            };
        }

        @Override
        public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
            final int initialWorkStatus = listener.getInitialWorkStatus();
            final LevelBean workStatus = jobStatusList.get(position);

            TextView workStatusTv = HolderUtils.getViewById(holder, R.id.workStatus);
            ImageView selectIcon = HolderUtils.getViewById(holder, R.id.id_img_right);
            View parentView = HolderUtils.getViewById(holder, R.id.id_root);
            workStatusTv.setText(workStatus.name);

            if (initialWorkStatus == workStatus.code) {
                // 选中样式
                selectIcon.setVisibility(View.VISIBLE);
                workStatusTv.setTextColor(ContextCompat.getColor(workStatusTv.getContext(), R.color.app_green_dark));
            } else {
                // 未选中样式
                selectIcon.setVisibility(View.GONE);
                workStatusTv.setTextColor(ContextCompat.getColor(workStatusTv.getContext(), R.color.text_c11));
            }
        }

        @Override
        public int getItemCount() {
            return jobStatusList.size();
        }
    }

    public interface OnChooseWorkStatusListener {
        void onChooseWorkStatus(@NonNull LevelBean workStatus);

        int getInitialWorkStatus();

        default void onApplyStatusLoadCompleted(boolean hasApplyStatusTips) {
        }
    }
}
