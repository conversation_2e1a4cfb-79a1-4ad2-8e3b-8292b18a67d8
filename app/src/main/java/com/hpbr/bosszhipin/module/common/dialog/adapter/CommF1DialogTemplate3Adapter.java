package com.hpbr.bosszhipin.module.common.dialog.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;

import net.bosszhipin.api.GeekF1CommonDialogResponse;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;


public class CommF1DialogTemplate3Adapter extends BaseRvAdapter<GeekF1CommonDialogResponse.MultiContentItem, CommF1DialogTemplate3Adapter.ViewHolder> {

    public CommF1DialogTemplate3Adapter() {
        super(R.layout.geek_f1_dialog_item_common_template3_multi_select);
    }

    @Override
    protected void convert(@NonNull ViewHolder helper, GeekF1CommonDialogResponse.MultiContentItem item) {

        helper.setData(item, mContext);
    }

    static class ViewHolder extends BaseViewHolder {

        private final MTextView tvTitle;

        public ViewHolder(View view) {
            super(view);
            tvTitle = view.findViewById(R.id.tvTitle);
        }

        void setData(GeekF1CommonDialogResponse.MultiContentItem data, Context context) {
            if (context == null) {
                return;
            }
            tvTitle.setText(data.text);
            tvTitle.setMaxLines(data.lineNumber);
            tvTitle.setBackgroundResource(data.isSelected ? R.drawable.bg_1dp_4r_stroke_48c7c7_12_15b3b3 : R.drawable.bg_f5f5f5_4dp_corner);
            tvTitle.setTextColor(ContextCompat.getColor(context, data.isSelected ? R.color.color_BOSS7 : R.color.color_FF292929_FFD2D2D6));
        }
    }
}
