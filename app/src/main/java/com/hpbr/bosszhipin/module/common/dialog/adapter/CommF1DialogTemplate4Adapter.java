package com.hpbr.bosszhipin.module.common.dialog.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;

import net.bosszhipin.api.GeekF1CommonDialogResponse;

import androidx.annotation.NonNull;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;


public class CommF1DialogTemplate4Adapter extends BaseRvAdapter<GeekF1CommonDialogResponse.MultiContentItem, CommF1DialogTemplate4Adapter.ViewHolder> {

    public CommF1DialogTemplate4Adapter() {
        super(R.layout.geek_f1_dialog_item_common_template4_multi_select);
    }

    @Override
    protected void convert(@NonNull ViewHolder helper, GeekF1CommonDialogResponse.MultiContentItem item) {

        helper.setData(item, getData().size());
    }

    static class ViewHolder extends BaseViewHolder {
        private final View viewDivide;

        private final ImageView ivSelected;

        private final MTextView tvContent;

        private final MTextView tvTitle;
        private final ZPUIRoundButton btnWorkType;
        private final ZPUIRoundButton btnSubTagName;

        public ViewHolder(View view) {
            super(view);
            viewDivide = view.findViewById(R.id.viewDivide);
            ivSelected = view.findViewById(R.id.ivSelected);
            tvContent = view.findViewById(R.id.tvContent);
            tvTitle = view.findViewById(R.id.tvTitle);
            btnWorkType = view.findViewById(R.id.btn_work_type);
            btnSubTagName = view.findViewById(R.id.btnSubTagName);
        }

        void setData(GeekF1CommonDialogResponse.MultiContentItem data, int size) {
            tvTitle.setText(data.text, View.GONE);
            tvTitle.setMaxLines(Math.max(data.lineNumber,1));
            tvContent.setText(data.subText, View.GONE);
            ivSelected.setImageResource(data.isSelected ? R.mipmap.geek_f1_dialog_common_template_selected : R.mipmap.ic_round_o);
            viewDivide.setVisibility(data.isShowDivider ? View.VISIBLE : View.GONE);
            if (!TextUtils.isEmpty(data.tagName)) {
                btnWorkType.setText(data.tagName);
                btnWorkType.setVisibility(View.VISIBLE);
            } else {
                btnWorkType.setVisibility(View.GONE);
            }
            if (!TextUtils.isEmpty(data.subTagName)) {
                btnSubTagName.setText(data.subTagName);
                btnSubTagName.setVisibility(View.VISIBLE);
            } else {
                btnSubTagName.setVisibility(View.GONE);
            }
        }
    }
}
