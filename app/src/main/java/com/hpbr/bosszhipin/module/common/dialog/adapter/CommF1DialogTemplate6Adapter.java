package com.hpbr.bosszhipin.module.common.dialog.adapter;

import android.content.Context;

import com.hpbr.bosszhipin.app.R;
import com.monch.lbase.util.LList;
import com.twl.ui.wheel.adapter.AbstractWheelTextAdapter;

import net.bosszhipin.api.GeekF1CommonDialogResponse;

import java.util.List;



public   class CommF1DialogTemplate6Adapter extends AbstractWheelTextAdapter {

    private final List<GeekF1CommonDialogResponse.RollerConfigItem> data;

    public CommF1DialogTemplate6Adapter(Context context, List<GeekF1CommonDialogResponse.RollerConfigItem> data) {
        super(context, R.layout.geek_f1_dialog_item_common_template6_multi_select, NO_RESOURCE);
        this.data = data;
        setItemTextResource(R.id.tv_item_name);
    }

    @Override
    public int getItemsCount() {
        int count = LList.getCount(data);
        if (count == 0) {
            count = 1;
        }
        return count;
    }

    @Override
    protected CharSequence getItemText(int index) {
        final GeekF1CommonDialogResponse.RollerConfigItem element = LList.getElement(data, index);
        if (element != null) {
            return element.name;
        }
        return "";
    }
}


