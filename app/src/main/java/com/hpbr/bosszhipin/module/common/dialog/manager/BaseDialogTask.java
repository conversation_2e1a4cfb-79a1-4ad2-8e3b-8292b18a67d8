package com.hpbr.bosszhipin.module.common.dialog.manager;

import android.content.Context;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;
/**
 * @ClassName: BaseDialogTask
 * @Description:
 * @Author: yanglinjie
 * @Date: 2024/10/15 15:29
 */
public abstract class BaseDialogTask<T> {

    private static SpImpl sp;
    public boolean dialogIsShowing=false;

    static {
        Context context = App.get().getContext();
        sp = SpFactory.create(context, Constants.PREFIX + ".public_dialog" + UserManager.getUID());
    }

    protected SpImpl getSp() {
        return sp;
    }

    /**
     * 缓存的弹框数据数据
     */
    private T data;

    /**
     * 获取数据源
     *
     * @return
     */
    public T getData() {
        return data;
    }

    /**
     * 设置数据源
     *
     * @param data
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 清空数据源
     */
    public void clearData() {
        data = null;
    }

    /**
     * 是否需要弹窗
     *
     * @return true - 需要弹出
     */
    protected abstract boolean needShow();
}
