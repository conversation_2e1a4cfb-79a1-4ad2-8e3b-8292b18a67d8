package com.hpbr.bosszhipin.module.common.dialog.manager;

import net.bosszhipin.api.GeekF1CommonDialogResponse;


public class CommF1DialogTemplate2Task extends BaseDialogTask<GeekF1CommonDialogResponse> {

    private static final CommF1DialogTemplate2Task instance = new CommF1DialogTemplate2Task();

    public static CommF1DialogTemplate2Task getInstance() {
        return instance;
    }

    private  boolean isNeedShow =false;

    @Override
    public void setData(GeekF1CommonDialogResponse data) {
        super.setData(data);
        isNeedShow =true;
    }

    @Override
    public void clearData() {
        super.clearData();
        isNeedShow=false;
    }

    @Override
    protected boolean needShow() {
        return isNeedShow;
    }
}
