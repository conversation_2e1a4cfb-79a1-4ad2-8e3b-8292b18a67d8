package com.hpbr.bosszhipin.module.common.dialog.manager;

import net.bosszhipin.api.GeekF1CommonDialogResponse;


public class CommF1DialogTemplate7Task extends BaseDialogTask<GeekF1CommonDialogResponse> {

    private static final CommF1DialogTemplate7Task instance = new CommF1DialogTemplate7Task();

    public static CommF1DialogTemplate7Task getInstance() {
        return instance;
    }

    private  boolean isNeedShow =false;

    @Override
    public void setData(GeekF1CommonDialogResponse data) {
        super.setData(data);
        isNeedShow =true;
    }

    @Override
    public void clearData() {
        super.clearData();
        isNeedShow=false;
    }

    @Override
    protected boolean needShow() {
        return isNeedShow;
    }
}
