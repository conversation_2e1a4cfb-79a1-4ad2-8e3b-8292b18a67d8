package com.hpbr.bosszhipin.module.common.dialog.manager;

import net.bosszhipin.api.GeekF2CommonDialogResponse;


public class CommF2DialogTemplate4Task extends BaseDialogTask<GeekF2CommonDialogResponse> {

    private static final CommF2DialogTemplate4Task instance = new CommF2DialogTemplate4Task();

    public static CommF2DialogTemplate4Task getInstance() {
        return instance;
    }

    private  boolean isNeedShow =false;

    @Override
    public void setData(GeekF2CommonDialogResponse data) {
        super.setData(data);
        isNeedShow =true;
    }

    @Override
    public void clearData() {
        super.clearData();
        isNeedShow =false;
    }

    @Override
    protected boolean needShow() {
        return isNeedShow;
    }
} 