package com.hpbr.bosszhipin.module.common.dialog.manager;


import android.text.TextUtils;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.dialog.ChangeExpectCityDialog;
import com.hpbr.bosszhipin.module.common.dialog.ChangeExpectCityDialogListen;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate2;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate3;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate4;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate5;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate6;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate7;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate8;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.main.activity.MainUserAgreementActivity;
import com.hpbr.bosszhipin.module.main.views.BaseFloatView;
import com.hpbr.bosszhipin.net.request.AppTipGetPopupInfoRequest;
import com.hpbr.bosszhipin.net.response.AppTipGetPopupInfoResponse;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekF1CommonDialogRequest;
import net.bosszhipin.api.GeekF1CommonDialogResponse;
import net.bosszhipin.api.bean.ServerHighlightListBean;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.boss.bean.GeekF1CommonDialogTextBean;


import com.hpbr.bosszhipin.base.BaseActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: GeekF1DialogManager
 * @Description:
 * @Author: yanglinjie
 * @Date: 2024/10/15 15:29
 */
public class GeekF1DialogManager {
    public static final String TAG = "GeekF1DialogManager";
    private static volatile GeekF1DialogManager instance;

    private final List<BaseDialogTask<GeekF1CommonDialogResponse>> taskList = new ArrayList<>();
    private boolean isFirstRequest = true;

    private GeekF1DialogManager() {
    }

    public static GeekF1DialogManager getInstance() {
        if (instance == null) {
            synchronized (GeekF1DialogManager.class) {
                if (instance == null) {
                    instance = new GeekF1DialogManager();
                }
            }
        }
        return instance;
    }


    public void dismissFloatView(BaseFloatView<GeekF1CommonDialogResponse> floatViewTemplate1) {
        if (floatViewTemplate1 != null) {
            if (floatViewTemplate1.isShowing()) {
                floatViewTemplate1.dismiss();
            }
        }
    }

    public boolean show(BaseFloatView<GeekF1CommonDialogResponse> floatViewTemplate1, BaseActivity activity) {
        //不是Geek 并且 activity 无效
        if (ActivityUtils.isInvalid(activity) || !UserManager.isGeekRole()) {
            return false;
        }
        //如果不是F1 不弹
        int tabIndex = SpManager.get().user().getInt(Constants.SP_MAIN_TAB_INDEX_SELECTION, 0);
        if (tabIndex != 0) {
            return false;
        }

        // 如果有用户协议弹框，则不弹其他弹框
        if (ForegroundUtils.get().hasActivity(MainUserAgreementActivity.class)) {
            return false;
        }

        //有安全框架不弹
        if (!TextUtils.isEmpty(UserManager.getSecurityUrl())) {
            return false;
        }

        if (CommF1FloatTemplate1Task.getInstance().getData() != null && CommF1FloatTemplate1Task.getInstance().needShow()) {
            if (floatViewTemplate1 != null) {
                //dismiss 动画需要800毫秒
                floatViewTemplate1.postDelayed(() -> {
                    if (ActivityUtils.isValid(activity)) {
                        floatViewTemplate1.setData(CommF1FloatTemplate1Task.getInstance().getData());
                    }
                }, 900);

                return true;
            } else {
                return false;
            }
        } else if (CommF1DialogTemplate2Task.getInstance().getData() != null && CommF1DialogTemplate2Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate2Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate2 dialogTemplate2 = CommonF1DialogTemplate2.getInstance(CommF1DialogTemplate2Task.getInstance().getData());
                dialogTemplate2.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate2.class.getSimpleName());
            }
            return true;
        } else if (CommF1DialogTemplate3Task.getInstance().getData() != null && CommF1DialogTemplate3Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate3Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate3 dialogTemplate3 = CommonF1DialogTemplate3.getInstance(CommF1DialogTemplate3Task.getInstance().getData());
                dialogTemplate3.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate3.class.getSimpleName());
            }
            return true;
        } else if (CommF1DialogTemplate4Task.getInstance().getData() != null && CommF1DialogTemplate4Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate4Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate4 dialogTemplate4 = CommonF1DialogTemplate4.getInstance(CommF1DialogTemplate4Task.getInstance().getData());
                dialogTemplate4.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate4.class.getSimpleName());
            }
            return true;
        }else if (CommF1DialogTemplate5Task.getInstance().getData() != null && CommF1DialogTemplate5Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate5Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate5 dialogTemplate5 = CommonF1DialogTemplate5.getInstance(CommF1DialogTemplate5Task.getInstance().getData());
                dialogTemplate5.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate5.class.getSimpleName());
            }
            return true;
        }else if (CommF1DialogTemplate6Task.getInstance().getData() != null && CommF1DialogTemplate6Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate6Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate6 dialogTemplate6 = CommonF1DialogTemplate6.getInstance(CommF1DialogTemplate6Task.getInstance().getData());
                dialogTemplate6.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate6.class.getSimpleName());
            }
            return true;
        }else if (CommF1DialogTemplate7Task.getInstance().getData() != null && CommF1DialogTemplate7Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate7Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate7 dialogTemplate7 = CommonF1DialogTemplate7.getInstance(CommF1DialogTemplate7Task.getInstance().getData());
                dialogTemplate7.show(activity);
            }
            return true;
        }else if (CommF1DialogTemplate8Task.getInstance().getData() != null && CommF1DialogTemplate8Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF1DialogTemplate8Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF1DialogTemplate8 dialogTemplate8 = CommonF1DialogTemplate8.getInstance(CommF1DialogTemplate8Task.getInstance().getData());
                dialogTemplate8.show(activity);
            }
            return true;
        }
        return false;
    }



    private void handleData(GeekF1CommonDialogResponse data, GeekF1DialogRequestParams params) {
        if (data == null) {
            return;
        }
        data.params = params;
        BaseDialogTask<GeekF1CommonDialogResponse> task = null;
        if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_FLOAT_1) {
            task = CommF1FloatTemplate1Task.getInstance();
        } else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_2) {
            task = CommF1DialogTemplate2Task.getInstance();
        } else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_3) {
            task = CommF1DialogTemplate3Task.getInstance();
        } else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_4) {
            task = CommF1DialogTemplate4Task.getInstance();
        }else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_5) {
            task = CommF1DialogTemplate5Task.getInstance();
        }else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_6) {
            task = CommF1DialogTemplate6Task.getInstance();
        }else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_7) {
            task = CommF1DialogTemplate7Task.getInstance();
        }else if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_8) {
            task = CommF1DialogTemplate8Task.getInstance();
        }
        if (task != null) {
            if (!task.needShow()) {
                task.setData(data);
                taskList.add(task);
            }
        }
    }

    public void getF1Dialog(ApiRequestCallback<GeekF1CommonDialogResponse> callback, GeekF1DialogRequestParams params) {
        GeekF1CommonDialogRequest request = new GeekF1CommonDialogRequest(new ApiRequestCallback<GeekF1CommonDialogResponse>() {
            @Override
            public void onSuccess(ApiData<GeekF1CommonDialogResponse> data) {
                handleData(data.resp, params);
                if (callback != null) {
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                if (callback != null) {
                    callback.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onFailed(reason);
                }
            }
        });
        request.securityId = params.securityId;
        request.encryptExpectId = params.encryptExpectId;
        request.jobType = params.jobType;
        request.filterParams = params.filterParams;
        request.expectId = params.expectId;
        request.page = params.page;
        request.sortType = params.sortType;
        request.sysNotifySwitch=params.sysNotifySwitch;
        if (isFirstRequest) {
            request.tipType = GeekF1DialogRequestParams.TIP_TYPE_FIRST_ENTER_F1;
            isFirstRequest = false;
        } else {
            request.tipType = params.tipType;
        }
        HttpExecutor.execute(request);
    }


    public void getF1Dialog(GeekF1DialogRequestParams params) {
        getF1Dialog(null, params);
    }

    public void clearTask() {
        for (BaseDialogTask<GeekF1CommonDialogResponse> task : taskList) {
            task.clearData();
        }
        taskList.clear();
    }


    /**
     * 获取弹窗信息
     *
     * @param callback       回调
     * @param securityId 加密期望ID
     * @param cityCode       城市编码
     * @param source         来源
     */
    public  void getPopupInfo(ApiRequestCallback<AppTipGetPopupInfoResponse> callback,
                                    String securityId,
                                    String cityCode,
                                    int source) {
        AppTipGetPopupInfoRequest request = new AppTipGetPopupInfoRequest(new ApiRequestCallback<AppTipGetPopupInfoResponse>() {
            @Override
            public void onSuccess(ApiData<AppTipGetPopupInfoResponse> data) {
                if (callback != null && data.resp!=null) {
                    data.resp.cityCode =cityCode;
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                if (callback != null) {
                    callback.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onFailed(reason);
                }
            }
        });

        request.securityId = securityId;
        request.cityCode = cityCode;
        request.source = source;

        HttpExecutor.execute(request);
    }

    public  void showPopupInfo(AppTipGetPopupInfoResponse response, BaseActivity activity,ChangeExpectCityDialogListen listen) {
        if (response != null&& response.title!=null && ActivityUtils.isValid(activity)) {
            ChangeExpectCityDialog changeExpectCityDialog = ChangeExpectCityDialog.getInstance(response, listen);
            changeExpectCityDialog.show(activity.getSupportFragmentManager(), "ChangeExpectCityDialog");
        }
    }

}
