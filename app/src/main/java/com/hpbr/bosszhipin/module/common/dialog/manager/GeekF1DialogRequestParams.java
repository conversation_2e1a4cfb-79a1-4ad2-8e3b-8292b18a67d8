package com.hpbr.bosszhipin.module.common.dialog.manager;


/**
 * @ClassName: GeekF1DialogRequestParams
 * @Description:
 * @Author: yang<PERSON><PERSON><PERSON>
 * @Date: 2024/10/15 15:56
 */
public class GeekF1DialogRequestParams {
//   1-F1（冷启动）
//   2-行为触发（下拉刷新）
//   3-行为触发（开聊）
//   4-行为触发（上拉加载）
//   5-行为触发（更新地址）

    public static final int TIP_TYPE_FIRST_ENTER_F1 = 1;
    public static final int TIP_TYPE_BEHAVIOR_REFRESH = 2;
    public static final int TIP_TYPE_BEHAVIOR_CHAT = 3;
    public static final int TIP_TYPE_BEHAVIOR_LOAD_MORE = 4;
    public static final int TIP_TYPE_BEHAVIOR_UPDATE_ADDRESS = 5;
    public static final int SYS_NOTIFY_SWITCH_OPEN = 1;
    public static final int SYS_NOTIFY_SWITCH_CLOSE = 2;


    public long expectId;
    public String encryptExpectId;
    public String securityId; // 开聊后加密id
    public int page;
    public Long jobType;
    public int cityCode;
    public int subCityCode;
    public String filterParams;
    public int sortType;

    public int tipType; //1-F1（冷启动或者进入app） 2-行为触发（刷新、开聊）

    public boolean isGeekPartime; //兼职tab

    public  int sysNotifySwitch;

    public void clear() {

    }

    public void setSysNotifySwitch(int sysNotifySwitch) {
        this.sysNotifySwitch = sysNotifySwitch;
    }

    public GeekF1DialogRequestParams setGeekPartime(boolean geekPartime) {
        isGeekPartime = geekPartime;
        return this;
    }

    public GeekF1DialogRequestParams setTipType(int tipType) {
        this.tipType = tipType;
        return this;
    }

    public GeekF1DialogRequestParams setExpectId(long expectId) {
        this.expectId = expectId;
        return this;
    }

    public GeekF1DialogRequestParams setEncryptExpectId(String encryptExpectId) {
        this.encryptExpectId = encryptExpectId;
        return this;
    }

    public GeekF1DialogRequestParams setSecurityId(String securityId) {
        this.securityId = securityId;
        return this;
    }

    public GeekF1DialogRequestParams setPage(int page) {
        this.page = page;
        return this;
    }

    public GeekF1DialogRequestParams setJobType(Long jobType) {
        this.jobType = jobType;
        return this;
    }

    public GeekF1DialogRequestParams setCityCode(int cityCode) {
        this.cityCode = cityCode;
        return this;
    }

    public GeekF1DialogRequestParams setSubCityCode(int subCityCode) {
        this.subCityCode = subCityCode;
        return this;
    }

    public GeekF1DialogRequestParams setFilterParams(String filterParams) {
        this.filterParams = filterParams;
        return this;
    }

    public GeekF1DialogRequestParams setSortType(int sortType) {
        this.sortType = sortType;
        return this;
    }

    @Override
    public String toString() {
        return "GeekF1DialogRequestParams{" +
                "expectId=" + expectId +
                ", encryptExpectId='" + encryptExpectId + '\'' +
                ", securityId='" + securityId + '\'' +
                ", page=" + page +
                ", jobType=" + jobType +
                ", cityCode=" + cityCode +
                ", subCityCode=" + subCityCode +
                ", filterParams='" + filterParams + '\'' +
                ", sortType=" + sortType +
                ", tipType=" + tipType +
                '}';
    }
}
