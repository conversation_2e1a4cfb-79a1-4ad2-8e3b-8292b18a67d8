package com.hpbr.bosszhipin.module.common.dialog.manager;


import android.text.TextUtils;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.dialog.CommonF1DialogTemplate4;
import com.hpbr.bosszhipin.module.common.dialog.CommonF2DialogTemplate4;
import com.hpbr.bosszhipin.module.common.dialog.constant.CommonGeekF1DialogConstant;
import com.hpbr.bosszhipin.module.main.activity.MainUserAgreementActivity;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.GeekF2CommonDialogRequest;
import net.bosszhipin.api.GeekF2CommonDialogResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: GeekF2DialogManager
 * @Description:
 * @Author: yanglinjie
 * @Date: 2024/10/15 15:29
 */
public class GeekF2DialogManager {
    public static final String TAG = "GeekF2DialogManager";
    private static volatile GeekF2DialogManager instance;

    private final List<BaseDialogTask<GeekF2CommonDialogResponse>> taskList = new ArrayList<>();
    private boolean isFirstRequest = true;

    private GeekF2DialogManager() {
    }

    public static GeekF2DialogManager getInstance() {
        if (instance == null) {
            synchronized (GeekF2DialogManager.class) {
                if (instance == null) {
                    instance = new GeekF2DialogManager();
                }
            }
        }
        return instance;
    }

    public void getF2Dialog(ApiRequestCallback<GeekF2CommonDialogResponse> callback, GeekF2DialogRequestParams params) {
        int tabIndex = SpManager.get().user().getInt(Constants.SP_MAIN_TAB_INDEX_SELECTION, 0);
        if (tabIndex != 2) {
            return ;
        }
        GeekF2CommonDialogRequest request = new GeekF2CommonDialogRequest(new ApiRequestCallback<GeekF2CommonDialogResponse>() {
            @Override
            public void onSuccess(ApiData<GeekF2CommonDialogResponse> data) {
                handleData(data.resp, params);
                if (callback != null) {
                    callback.onSuccess(data);
                }
            }

            @Override
            public void onComplete() {
                if (callback != null) {
                    callback.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callback != null) {
                    callback.onFailed(reason);
                }
            }
        });
        request.page = params.page;
        if (isFirstRequest) {
            request.tipType = GeekF2DialogRequestParams.TIP_TYPE_FIRST_ENTER_F2;
            isFirstRequest = false;
        } else {
            request.tipType = params.tipType;
        }
        HttpExecutor.execute(request);
    }

    public void getF2Dialog(GeekF2DialogRequestParams params) {
        getF2Dialog(null, params);
    }

    private void handleData(GeekF2CommonDialogResponse data, GeekF2DialogRequestParams params) {
        if (data == null) {
            return;
        }
        data.params = params;
        BaseDialogTask<GeekF2CommonDialogResponse> task = null;
        if (data.templateType == CommonGeekF1DialogConstant.TEMPLATE_TYPE_DIALOG_4) {
            task = CommF2DialogTemplate4Task.getInstance();
        }
        if (task != null) {
            if (!task.needShow()) {
                task.setData(data);
                taskList.add(task);
            }
        }
    }

    public void clearTask() {
        for (BaseDialogTask<GeekF2CommonDialogResponse> task : taskList) {
            task.clearData();
        }
        taskList.clear();
    }


    public boolean show( BaseActivity activity) {
        //不是Geek 并且 activity 无效
        if (ActivityUtils.isInvalid(activity) || !UserManager.isGeekRole()) {
            return false;
        }
        //如果不是F2 不弹
        int tabIndex = SpManager.get().user().getInt(Constants.SP_MAIN_TAB_INDEX_SELECTION, 0);
        if (tabIndex != 2) {
            return false;
        }

        // 如果有用户协议弹框，则不弹其他弹框
        if (ForegroundUtils.get().hasActivity(MainUserAgreementActivity.class)) {
            return false;
        }

        //有安全框架不弹
        if (!TextUtils.isEmpty(UserManager.getSecurityUrl())) {
            return false;
        }

        if (CommF2DialogTemplate4Task.getInstance().getData() != null && CommF2DialogTemplate4Task.getInstance().needShow()) {
            boolean dialogIsShowing = CommF2DialogTemplate4Task.getInstance().dialogIsShowing;
            if (!dialogIsShowing) {
                CommonF2DialogTemplate4 dialogTemplate4 = CommonF2DialogTemplate4.getInstance(CommF2DialogTemplate4Task.getInstance().getData());
                dialogTemplate4.show(activity.getSupportFragmentManager(), CommonF1DialogTemplate4.class.getSimpleName());
            }
            return true;
        }
        return false;
    }

}
