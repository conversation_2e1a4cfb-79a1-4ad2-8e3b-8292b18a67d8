package com.hpbr.bosszhipin.module.common.dialog.manager;


/**
 * @ClassName: GeekF2DialogRequestParams
 * @Description:
 * @Author: yang<PERSON><PERSON>e
 * @Date: 2024/10/15 15:56
 */
public class GeekF2DialogRequestParams {
    //1-F2（冷启动或者进入app）
    //2-行为触发（刷新）
    //3-行为触发（开聊）
    public static final int TIP_TYPE_FIRST_ENTER_F2 = 1;
    public static final int TIP_TYPE_BEHAVIOR_REFRESH = 2;
    public static final int TIP_TYPE_BEHAVIOR_CHAT = 3;
    public static final int TIP_TYPE_BEHAVIOR_LOAD_MORE = 4;
    public static final int TIP_TYPE_BEHAVIOR_UPDATE_ADDRESS = 5;
    public static final int SYS_NOTIFY_SWITCH_OPEN = 1;
    public static final int SYS_NOTIFY_SWITCH_CLOSE = 2;

    public int page;

    public int tipType; //1-F2（冷启动或者进入app） 2-行为触发（刷新、开聊）

    public void clear() {

    }

    public GeekF2DialogRequestParams setPage(int page) {
        this.page = page;
        return this;
    }

    public GeekF2DialogRequestParams setTipType(int tipType) {
        this.tipType = tipType;
        return this;
    }
}