package com.hpbr.bosszhipin.module.common.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseNavigationFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.BelongIndustryBean;
import com.hpbr.bosszhipin.module.common.bean.CompanyNameBean;
import com.hpbr.bosszhipin.module.common.bean.NewWorkExpEntity;
import com.hpbr.bosszhipin.module.common.bean.OnTheJobTimeBean;
import com.hpbr.bosszhipin.module.common.bean.PositionNameBean;
import com.hpbr.bosszhipin.module.common.bean.RecentCompanyBean;
import com.hpbr.bosszhipin.module.common.bean.WorkContentBean;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.module.my.activity.geek.SingleIndustryChooserWithRecommendActivity;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.my.entity.WorkBean;
import com.hpbr.bosszhipin.module.onlineresume.activity.SubPageTransferActivity;
import com.hpbr.bosszhipin.module.onlineresume.activity.sub.CompanyFragment;
import com.hpbr.bosszhipin.module.onlineresume.activity.sub.WorkContentFragment2;
import com.hpbr.bosszhipin.module_geek_export.OnlineResumeRouter;
import com.hpbr.bosszhipin.module_geek_export.bean.GeekWorkPositionRequestParams;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.utils.functions.Consumer2;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.wheelview.projectexp.ProjectExpUtil;
import com.hpbr.bosszhipin.views.wheelview.workexp.WorkExpUtil;
import com.hpbr.bosszhipin.views.wheelview.workexp.WorkExpWheelView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：AddNewWorkExpFragment
 * @Description ：「添加新的工作经历」页面
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  9:12 PM
 */
public class AddNewWorkExpFragment extends BaseNavigationFragment<GeekOldUserActiveAgainViewModel> implements View.OnClickListener {

    private AppTitleView titleView;
    private TextView tv_tip;
    private TextView tv_tip_desc;
    private RecyclerView rv_list;
    private RecentCompanyBean recentCompanyBean;
    private AddNewWorkExpAdapter addNewWorkExpAdapter;
    private ZPUIRoundButton zpui_round_btn_next;

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(GeekOldUserActiveAgainViewModel.class);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_add_new_work_exp;
    }

    @Override
    protected void initViews(View view) {
        initParams();
        initView(view);
        initData();
        initEventListener();
        initLiveDataObserve();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (bundle == null) return;
        recentCompanyBean = (RecentCompanyBean) bundle.getSerializable(PageConstant.KEY_RECENT_COMPANY_BEAN);
    }

    private void initView(View view) {
        titleView = view.findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setActionButtonColorBlue();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(activity, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(activity);
        titleView.setPadding(0, statusBarHeight, 0, 0);
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
        titleView.getTvBtnAction().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);

        tv_tip = view.findViewById(R.id.tv_tip);
        tv_tip_desc = view.findViewById(R.id.tv_tip_desc);
        rv_list = view.findViewById(R.id.rv_list);
        zpui_round_btn_next = view.findViewById(R.id.zpui_round_btn_next);

        addNewWorkExpAdapter = new AddNewWorkExpAdapter(activity, eventListener);
        rv_list.setAdapter(addNewWorkExpAdapter);
    }

    @Override
    public void initData() {
        super.initData();
        resetData();
        AppAnalysisUtil.dotWelcomeGeekAddWordExpo(2);/*埋点*/
    }

    private void initEventListener() {
        if (mViewModel.canSkip()) {
            titleView.setActionButtonListener("跳过", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AppAnalysisUtil.dotWelcomeGeekAddWordDetailClick(0);/*埋点*/

                    mViewModel.expanddSugClose();
                    mViewModel.navigate(R.id.action_addNewWorkExpFragment_to_requestJobExpectFragment, null);
                }
            });
        }

        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                mViewModel.backUp(activity);
            }
        });
        zpui_round_btn_next.setOnClickListener(this);
    }

    private void initLiveDataObserve() {
        /*更新公司名称*/
        mViewModel.updateCompanyNameLiveData.observe(this, companyName -> {
            updateItemModel(AddNewWorkExpModel.TYPE_COMPANY_NAME, (targetIndex, model) -> {
                if (targetIndex < 0) return;
                if (model == null || !(model.getData() instanceof CompanyNameBean)) return;
                CompanyNameBean companyNameBean = (CompanyNameBean) model.getData();
                companyNameBean.companyName = companyName;
                addNewWorkExpAdapter.notifyItemChanged(targetIndex);
            });
        });
        /*更新工作内容*/
        mViewModel.updateWorkContentLiveData.observe(this, workContent -> {
            updateItemModel(AddNewWorkExpModel.TYPE_WORK_CONTENT, (targetIndex, model) -> {
                if (targetIndex < 0) return;
                if (model == null || !(model.getData() instanceof WorkContentBean)) return;
                WorkContentBean workContentBean = (WorkContentBean) model.getData();
                workContentBean.workContent = workContent;
                addNewWorkExpAdapter.notifyItemChanged(targetIndex);
            });
        });
        /*更新「所在行业」*/
        mViewModel.updateBelongIndustryLiveData.observe(this, selectIndustryBean -> updateItemModel(AddNewWorkExpModel.TYPE_BELONG_INDUSTRY, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof BelongIndustryBean)) return;
            BelongIndustryBean belongIndustryBean = (BelongIndustryBean) model.getData();
            belongIndustryBean.industryName = selectIndustryBean.industryName;
            belongIndustryBean.industryCode = selectIndustryBean.industryCode;
            belongIndustryBean.reportIndustryId = selectIndustryBean.reportIndustryId;
            belongIndustryBean.mRecommedSelectedCount = selectIndustryBean.mRecommedSelectedCount;
            addNewWorkExpAdapter.notifyItemChanged(targetIndex);
        }));

        /*更新「职位名称」*/
        mViewModel.updatePositionNameLiveData.observe(this, positionNameModel -> updateItemModel(AddNewWorkExpModel.TYPE_POSITION_NAME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof PositionNameBean)) return;
            PositionNameBean positionNameBean = (PositionNameBean) model.getData();
            positionNameBean.positionName = positionNameModel.positionName;
            positionNameBean.positionClassCode = positionNameModel.positionClassCode;
            positionNameBean.positionClassName = positionNameModel.positionClassName;
            positionNameBean.reportPositionId = positionNameModel.reportPositionId;
            addNewWorkExpAdapter.notifyItemChanged(targetIndex);
        }));
        /*重置「添加工作经历」的表单数据*/
        mViewModel.resetAddExpFormLiveData.observe(this, value -> {
            if (value == null) return;
            if (value) resetData();
        });
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.zpui_round_btn_next) {/*点击「下一步」按钮*/
            AppAnalysisUtil.dotWelcomeGeekAddWordDetailClick(1);/*埋点*/

            NewWorkExpEntity newWorkExpEntity = getFormData();

            /*校验「公司名称」*/
            if (TextUtils.isEmpty(newWorkExpEntity.getCompanyName())) {
                updateItemModel(AddNewWorkExpModel.TYPE_COMPANY_NAME, (targetIndex, model) -> {
                    if (targetIndex < 0) return;
                    if (model == null || !(model.getData() instanceof CompanyNameBean)) return;
                    CompanyNameBean companyNameBean = (CompanyNameBean) model.getData();
                    companyNameBean.isShowInterceptTip = true;
                    addNewWorkExpAdapter.notifyItemChanged(targetIndex);
                });
                return;
            }

            /*校验「所在行业」*/
            if (TextUtils.isEmpty(newWorkExpEntity.getIndustryName())) {
                updateItemModel(AddNewWorkExpModel.TYPE_BELONG_INDUSTRY, (targetIndex, model) -> {
                    if (targetIndex < 0) return;
                    if (model == null || !(model.getData() instanceof BelongIndustryBean)) return;
                    BelongIndustryBean belongIndustryBean = (BelongIndustryBean) model.getData();
                    belongIndustryBean.isShowInterceptTip = true;
                    addNewWorkExpAdapter.notifyItemChanged(targetIndex);
                });
                return;
            }

            /*校验「在职时间」*/
            if (newWorkExpEntity.onTheJobTimeBean != null) {
                String onTheTimeInterceptTip = getOnTheTimeInterceptTip(newWorkExpEntity.onTheJobTimeBean.startDate, newWorkExpEntity.onTheJobTimeBean.endDate);
                if (!TextUtils.isEmpty(onTheTimeInterceptTip)) {
                    updateItemModel(AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME, (targetIndex, model) -> {
                        if (targetIndex < 0) return;
                        if (model == null || !(model.getData() instanceof OnTheJobTimeBean)) return;
                        OnTheJobTimeBean onTheJobTimeBean = (OnTheJobTimeBean) model.getData();
                        onTheJobTimeBean.interceptTip = onTheTimeInterceptTip;
                        addNewWorkExpAdapter.notifyItemChanged(targetIndex);
                    });
                    return;
                }
            }

            /*校验「职位名称」*/
            if (TextUtils.isEmpty(newWorkExpEntity.getPositionClassName())) {
                updateItemModel(AddNewWorkExpModel.TYPE_POSITION_NAME, (targetIndex, model) -> {
                    if (targetIndex < 0) return;
                    if (model == null || !(model.getData() instanceof PositionNameBean)) return;
                    PositionNameBean positionNameBean = (PositionNameBean) model.getData();
                    positionNameBean.isShowInterceptTip = true;
                    addNewWorkExpAdapter.notifyItemChanged(targetIndex);
                });
                return;
            }

            /*校验「工作内容」*/
            if (TextUtils.isEmpty(newWorkExpEntity.getWorkContent())) {
                updateItemModel(AddNewWorkExpModel.TYPE_WORK_CONTENT, (targetIndex, model) -> {
                    if (targetIndex < 0) return;
                    if (model == null || !(model.getData() instanceof WorkContentBean)) return;
                    WorkContentBean workContentBean = (WorkContentBean) model.getData();
                    workContentBean.isShowInterceptTip = true;
                    addNewWorkExpAdapter.notifyItemChanged(targetIndex);
                });
                return;
            }


            Bundle bundle = new Bundle();
            bundle.putSerializable(PageConstant.KEY_WORK_EXP_FORM_DATA, newWorkExpEntity);
            mViewModel.navigate(R.id.action_addNewWorkExpFragment_to_requestJobExpectFragment, bundle);
        }
    }

    private final AddNewWorkExpAdapter.EventListener eventListener = new AddNewWorkExpAdapter.EventListener() {
        @Override
        public void onClickCompanyName(String companyName) {
            SubPageTransferActivity.jumpForResult(activity, CompanyFragment.class, CompanyFragment.getBundle(companyName), PageConstant.REQ_COMPANY);
        }

        @Override
        public void onClickStartTime(int startDate) {
            clickStartTime(startDate);
        }

        @Override
        public void onClickEndTime(int endDate) {
            clickEndTime(endDate);
        }

        @Override
        public void onClickWorkContent(String workContent) {/*跳转「工作内容」编辑页*/
            WorkBean workBean = new WorkBean();
            workBean.responsibility = workContent;
            SubPageTransferActivity.jumpForResult(activity, WorkContentFragment2.class, WorkContentFragment2.getBundle(WorkContentFragment2.MINUS, workBean), PageConstant.REQ_WORK_CONTENT);
        }

        @Override
        public void onClickBelongIndustry(@NonNull BelongIndustryBean belongIndustryBean) {
            clickIndustry(belongIndustryBean);
        }

        @Override
        public void onClickPositionName() {
            clickPositionName();
        }
    };

    /**
     * 点击"在职时间-开始时间"
     */
    private void clickStartTime(int startDate) {
        WorkExpWheelView startProjectView = new WorkExpWheelView(activity);
        startProjectView.setIsStartYear(true);
        startProjectView.setCallBack((leftBean, rightBean) -> updateItemModel(AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof OnTheJobTimeBean)) return;
            OnTheJobTimeBean onTheJobTimeBean = (OnTheJobTimeBean) model.getData();
            onTheJobTimeBean.startDate = LText.getInt(WorkExpUtil.wheelViewToDate8(leftBean, rightBean));
            addNewWorkExpAdapter.notifyItemChanged(targetIndex);
        }));
        startProjectView.show(startDate, "开始时间");
    }

    /**
     * 点击"在职时间-结束时间"
     */
    private void clickEndTime(int endDate) {
        WorkExpWheelView endProjectView = new WorkExpWheelView(activity);
        endProjectView.setIsStartYear(false);
        endProjectView.setCallBack((leftBean, rightBean) -> updateItemModel(AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof OnTheJobTimeBean)) return;
            OnTheJobTimeBean onTheJobTimeBean = (OnTheJobTimeBean) model.getData();
            onTheJobTimeBean.endDate = LText.getInt(WorkExpUtil.wheelViewToDate8(leftBean, rightBean));
            addNewWorkExpAdapter.notifyItemChanged(targetIndex);
        }));
        endProjectView.show(endDate, "结束时间");
    }

    /**
     * 点击"所在行业"
     */
    private void clickIndustry(@NonNull BelongIndustryBean belongIndustryBean) {
        Intent intent = new Intent(activity, SingleIndustryChooserWithRecommendActivity.class);
        intent.putExtra(Constants.DATA_BOOLEAN, true);

        LevelBean item = null;
        if (belongIndustryBean.industryName != null) {
            item = new LevelBean();
            item.code = LText.getLong(belongIndustryBean.industryCode);
            item.name = belongIndustryBean.industryName;
        }
        intent.putExtra(Constants.DATA_ENTITY, item);
        AppUtil.startActivityForResult(activity, intent, PageConstant.REQ_INDUSTRY);
    }

    /**
     * 点击"职位名称"
     */
    private void clickPositionName() {
        OnlineResumeRouter.jumpToWorkPositionActivity(activity, GeekWorkPositionRequestParams.obj().setShowSearchInputCount(true).setSupportSearchOther(true).setSource(GeekWorkPositionRequestParams.Source.OLD_USER_ACTIVE));
    }

    private void updateItemModel(int itemType, Consumer2<Integer, AddNewWorkExpModel> consumer) {
        int targetIndex = getItemModelIndexByType(itemType);
        if (targetIndex < 0) return;
        List<AddNewWorkExpModel> modelList = addNewWorkExpAdapter.getData();
        AddNewWorkExpModel itemModel = LList.getElement(modelList, targetIndex);
        if (consumer != null) {
            consumer.accept(targetIndex, itemModel);
        }
    }

    /**
     * 根据itemType获取Item在列表中的索引
     *
     * @param itemType
     * @return
     */
    private int getItemModelIndexByType(int itemType) {
        List<AddNewWorkExpModel> modelList = addNewWorkExpAdapter.getData();
        if (LList.getCount(modelList) == 0) return -1;
        for (AddNewWorkExpModel itemModel : modelList) {
            if (itemModel == null) continue;
            if (itemType == itemModel.getItemType()) {
                return modelList.indexOf(itemModel);
            }
        }
        return -1;
    }

    /**
     * 获取表单数据
     *
     * @return
     */
    @NonNull
    private NewWorkExpEntity getFormData() {
        NewWorkExpEntity newWorkExpEntity = new NewWorkExpEntity();

        /*公司名称*/
        updateItemModel(AddNewWorkExpModel.TYPE_COMPANY_NAME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof CompanyNameBean)) return;
            newWorkExpEntity.companyNameBean = (CompanyNameBean) model.getData();
        });

        /*所在行业*/
        updateItemModel(AddNewWorkExpModel.TYPE_BELONG_INDUSTRY, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof BelongIndustryBean)) return;
            newWorkExpEntity.belongIndustryBean = (BelongIndustryBean) model.getData();
        });

        /*在职时间*/
        updateItemModel(AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof OnTheJobTimeBean)) return;
            newWorkExpEntity.onTheJobTimeBean = (OnTheJobTimeBean) model.getData();
        });

        /*职位名称*/
        updateItemModel(AddNewWorkExpModel.TYPE_POSITION_NAME, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof PositionNameBean)) return;
            newWorkExpEntity.positionNameBean = (PositionNameBean) model.getData();
        });

        /*工作内容*/
        updateItemModel(AddNewWorkExpModel.TYPE_WORK_CONTENT, (targetIndex, model) -> {
            if (targetIndex < 0) return;
            if (model == null || !(model.getData() instanceof WorkContentBean)) return;
            newWorkExpEntity.workContentBean = (WorkContentBean) model.getData();
        });

        return newWorkExpEntity;
    }

    /**
     * 获取拦截提示文案
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @Nullable
    private String getOnTheTimeInterceptTip(int startDate, int endDate) {

        if (startDate <= 0) {
            return "请选择开始时间";
        }

        if (endDate == 0) { //-1 代表至今
            return "请选择结束时间";
        }

        boolean validateStart = ProjectExpUtil.isValidateTime(startDate + "");
        if (!validateStart) {
            return "起止时间不能大于当前时间";
        }

        boolean validateEnd = ProjectExpUtil.isValidateTime(endDate + "");
        if (!validateEnd) {
            return "起止时间不能大于当前时间";
        }

        if (startDate > 0 && endDate > 0) {
            if (!WorkExpUtil.isValidateTimeRange(startDate + "", endDate + "")) {
                return "起始时间不能大于结束时间";
            }
        }
        return null;
    }

    /**
     * 重置数据
     */
    private void resetData() {
        List<AddNewWorkExpModel> itemModelList = new ArrayList<>();
        /*「公司名称」*/
        itemModelList.add(new AddNewWorkExpModel<>(AddNewWorkExpModel.TYPE_COMPANY_NAME, new CompanyNameBean(false, recentCompanyBean != null ? recentCompanyBean.companyName : "")));
        /*「所在行业」*/
        itemModelList.add(new AddNewWorkExpModel<>(AddNewWorkExpModel.TYPE_BELONG_INDUSTRY, new BelongIndustryBean(false, "", "", 0, 0)));
        /*「在职时间」*/
        itemModelList.add(new AddNewWorkExpModel<>(AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME, new OnTheJobTimeBean(null, 0, -1)));
        /*「职位名称」*/
        itemModelList.add(new AddNewWorkExpModel<>(AddNewWorkExpModel.TYPE_POSITION_NAME, new PositionNameBean(false, "", 0, "", 0, 0)));
        /*「工作内容」*/
        itemModelList.add(new AddNewWorkExpModel<>(AddNewWorkExpModel.TYPE_WORK_CONTENT, new WorkContentBean(false, "")));

        addNewWorkExpAdapter.setNewData(itemModelList);
    }

    @Override
    protected void onBackPressed() {
        super.onBackPressed();
        mViewModel.backUp(activity);
    }
}
