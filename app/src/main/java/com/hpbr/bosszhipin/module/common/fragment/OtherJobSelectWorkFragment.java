package com.hpbr.bosszhipin.module.common.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.decoration.GridDecoration;
import com.hpbr.bosszhipin.module.common.ThreeLevelPositionPickForMultiExpectActivity;
import com.hpbr.bosszhipin.module.common.adapter.WorkJobAdapter;
import com.hpbr.bosszhipin.module.common.viewmodel.OtherJobGuideCompleteViewModel;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.onlineresume.view.WorkTypeView;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectPageRouter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.ItemHelper;
import com.hpbr.bosszhipin.utils.SpannableUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.api.bean.RecPositionBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：OtherJobSelectWorkFragment
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/3/29  5:41 PM
 */
public class OtherJobSelectWorkFragment extends BaseAwareFragment<OtherJobGuideCompleteViewModel> implements View.OnClickListener {

    private AppTitleView appTitleView;
    private TextView tv_num;
    private RecyclerView rv_list;
    private ZPUIRoundButton zpui_btn_submit;
    private WorkJobAdapter workJobAdapter;

    public static OtherJobSelectWorkFragment getInstance(Bundle bundle) {
        OtherJobSelectWorkFragment instance = new OtherJobSelectWorkFragment();
        instance.setArguments(bundle);
        return instance;
    }

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(OtherJobGuideCompleteViewModel.class);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_other_job_select_work;
    }

    @Override
    protected void initViews(View view) {
        initView(view);
        initEventListener();
        initLiveDataObserve();
        initData();
    }

    private void initView(View view) {
        appTitleView = view.findViewById(R.id.appTitleView);
        appTitleView.setDividerInvisible();
        appTitleView.showIvBack(false);
        tv_num = view.findViewById(R.id.tv_num);
        rv_list = view.findViewById(R.id.rv_list);
        zpui_btn_submit = view.findViewById(R.id.zpui_btn_submit);

        rv_list.addItemDecoration(new GridDecoration(activity, 12, 0, 12));
        workJobAdapter = new WorkJobAdapter(activity);
        rv_list.setAdapter(workJobAdapter);
    }

    private void initEventListener() {
        zpui_btn_submit.setOnClickListener(this);
        appTitleView.setActionButtonListener("跳过", new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppAnalysisUtil.dotOtherBackUserExpectGuide(3, "");/*埋点*/

                AppUtil.finishActivity(activity);
            }
        });
        appTitleView.getTvBtnAction().setTextColor(ContextCompat.getColor(activity, R.color.text_c2));
        workJobAdapter.setOnItemClickListener(itemClickListener);
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.zpui_btn_submit) {/*「选好了」*/
            List<RecPositionBean> recPositionBeanList = workJobAdapter.getData();
            List<RecPositionBean> selectRecPositionBeanList = ItemHelper.getItemListByCondition(recPositionBeanList, recPositionBean1 -> recPositionBean1.code != RecPositionBean.ALL_POSITION_CODE && recPositionBean1.isSelected);
            ArrayList<LevelBean> selectedLevelBeanList = mViewModel.convertToLevelBeanList(selectRecPositionBeanList);
            mViewModel.resetMultiPositionClassValue(selectedLevelBeanList);

            GeekExpectPageRouter.Create.jump(
                    activity,
                    GeekExpectPageRouter.Create.RequestParams.obj()
                            .setRequestCode(GeekExpectConstants.REQ_OTHER_JOB_SELECT)
                            .setMultiPositions(selectedLevelBeanList)
                            .setPositionClassIndexString(mViewModel.jobIntent.positionClassIndexString)
                            .setJobIntentBean(mViewModel.jobIntent));
            AppUtil.finishActivity(activity, ActivityAnimType.NONE);

            AppAnalysisUtil.dotOtherBackUserExpectGuide(2, StringUtil.connectTextWithChar(StringUtil.SPLIT_CHAR_COMMA, selectedLevelBeanList, (StringUtil.IValueFunction<LevelBean>) levelBean -> String.valueOf(levelBean.code)));/*埋点*/
        }
    }

    private void initLiveDataObserve() {

    }

    @Override
    public void initData() {
        super.initData();
        ExtraMapBean extraMapBean = mViewModel.extraMapBean;
        if (extraMapBean == null) return;

        renderNum(0, extraMapBean.addCount);
        List<RecPositionBean> recPositionBeanList = mViewModel.getRecPositionBeanList(extraMapBean);
        workJobAdapter.setNewData(recPositionBeanList);

        AppAnalysisUtil.dotOtherBackUserExpectGuide(0, "");/*埋点*/
    }

    private final BaseQuickAdapter.OnItemClickListener itemClickListener = new BaseQuickAdapter.OnItemClickListener() {
        @Override
        public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
            if (workJobAdapter == null || mViewModel.extraMapBean == null) return;
            if (LList.getCount(workJobAdapter.getData()) <= position) return;
            List<RecPositionBean> recPositionBeanList = workJobAdapter.getData();
            RecPositionBean recPositionBean = LList.getElement(recPositionBeanList, position);
            if (recPositionBean == null) return;
            List<RecPositionBean> selectRecPositionBeanList = ItemHelper.getItemListByCondition(recPositionBeanList, recPositionBean1 -> recPositionBean1.code != RecPositionBean.ALL_POSITION_CODE && recPositionBean1.isSelected);
            if (recPositionBean.code == RecPositionBean.ALL_POSITION_CODE) {
                ArrayList<LevelBean> selectedLevelBeanList = mViewModel.convertToLevelBeanList(selectRecPositionBeanList);
                jumpToThreeLevelPositionPage(selectedLevelBeanList);

                AppAnalysisUtil.dotOtherBackUserExpectGuide(4, "");/*埋点*/
            } else {
                handleListChange(position, selectRecPositionBeanList, recPositionBean);
            }
        }
    };

    /**
     * 跳转至三级职位选择页
     */
    private void jumpToThreeLevelPositionPage(ArrayList<LevelBean> levelBeanList) {
        boolean isPartTimeJob = mViewModel.extraMapBean != null && mViewModel.extraMapBean.expectInfo != null && mViewModel.extraMapBean.expectInfo.positionType == WorkTypeView.WorkType.TYPE_PARTTIME;
        ThreeLevelPositionPickForMultiExpectActivity.jumpForResult(activity,
                ThreeLevelPositionPickForMultiExpectActivity.MultiExpectParams.obj()
                        .cityCode(mViewModel.jobIntent != null ? mViewModel.jobIntent.locationIndex : 0L)
                        .inPartTimeJob(isPartTimeJob)
                        .hasChoseJobIntent(true)
                        .setMaxSelectAbleCount(mViewModel.extraMapBean != null ? mViewModel.extraMapBean.addCount : 0)
                        .selectedMultiPositions(levelBeanList)
                        .getServerData(true)
                        .useNewCommend(true)
                        .setEnable_920_688_style(true)
                        .setNeedDesc(true)
                        .setFromEdit(true)
                        .requestCode(GeekExpectConstants.REQ_OTHER_JOB_SELECT));
    }

    /**
     * 处理列表的变化
     *
     * @param position
     * @param selectRecPositionBeanList
     * @param recPositionBean
     */
    private void handleListChange(int position, List<RecPositionBean> selectRecPositionBeanList, RecPositionBean recPositionBean) {
        if (!recPositionBean.isSelected && LList.getCount(selectRecPositionBeanList) >= mViewModel.extraMapBean.addCount) {
            return;
        }
        /*刷新列表*/
        recPositionBean.isSelected = !recPositionBean.isSelected;
        workJobAdapter.notifyItemChanged(position);
        /*渲染选中的数量*/
        renderNum(LList.getCount(selectRecPositionBeanList) + (recPositionBean.isSelected ? 1 : -1), mViewModel.extraMapBean.addCount);

        if (recPositionBean.isSelected) {
            AppAnalysisUtil.dotOtherBackUserExpectGuide(1, String.valueOf(recPositionBean.code));/*埋点*/
        }
    }


    /**
     * 渲染选中的数量
     *
     * @param selectNum
     * @param totalNum
     */
    private void renderNum(int selectNum, int totalNum) {
        List<String> highLightTextList = new ArrayList<>();
        highLightTextList.add(String.valueOf(selectNum));
        highLightTextList.add("/" + totalNum);
        SpannableUtils.setTextStyleMultiColor(String.format(Locale.getDefault(), "%s/%s", selectNum, totalNum), tv_num, highLightTextList, ContextCompat.getColor(activity, R.color.color_BOSS7), ContextCompat.getColor(activity, R.color.text_c1));
        zpui_btn_submit.setEnabled(selectNum > 0);
        tv_num.setVisibility(View.VISIBLE);
    }

}
