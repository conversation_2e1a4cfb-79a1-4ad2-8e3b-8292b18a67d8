package com.hpbr.bosszhipin.module.common.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseNavigationFragment;
import com.hpbr.bosszhipin.common.decoration.HorizontalDecoration;
import com.hpbr.bosszhipin.module.common.adapter.RecentCompanyAdapter;
import com.hpbr.bosszhipin.module.common.bean.RecentCompanyBean;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.response.ExpAddSugListResponse;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ：RecentWorkExpListFragment
 * @Description ：最近工作经历列表
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  9:12 PM
 */
public class RecentWorkExpListFragment extends BaseNavigationFragment<GeekOldUserActiveAgainViewModel> {

    private AppTitleView titleView;
    private TextView tv_tip;
    private TextView tv_tip_desc;
    private RecyclerView rv_list;
    private RecentCompanyAdapter recentCompanyAdapter;
    private ExpAddSugListResponse expAddSugListResponse;

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(GeekOldUserActiveAgainViewModel.class);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_recent_work_exp_list;
    }

    @Override
    protected void initViews(View view) {
        initParams();
        initView(view);
        initEventListener();
        initData();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (bundle == null) return;
        expAddSugListResponse = (ExpAddSugListResponse) bundle.getSerializable(PageConstant.KEY_EXP_ADD_SUG_LIST_RESPONSE);
    }

    private void initView(View view) {
        titleView = view.findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setActionButtonColorBlue();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(activity, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(activity);
        titleView.setPadding(0, statusBarHeight, 0, 0);
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));

        tv_tip = view.findViewById(R.id.tv_tip);
        tv_tip_desc = view.findViewById(R.id.tv_tip_desc);
        rv_list = view.findViewById(R.id.rv_list);
        rv_list.addItemDecoration(new HorizontalDecoration(activity, R.drawable.decoration_width_10_transparent, true));
        recentCompanyAdapter = new RecentCompanyAdapter(activity);
        rv_list.setAdapter(recentCompanyAdapter);
    }

    private void initEventListener() {
        recentCompanyAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                List<RecentCompanyBean> recentCompanyBeanList = recentCompanyAdapter.getData();
                if (LList.getCount(recentCompanyBeanList) <= position) return;
                recentCompanyAdapter.setCurrentSelectIndex(position);

                RecentCompanyBean recentCompanyBean = LList.getElement(recentCompanyBeanList, position);
                if (recentCompanyBean == null) return;

                int eventType = recentCompanyBean.isNoAll ? 4 : 1;
                AppAnalysisUtil.dotWelcomeGeekAddWordClick(eventType);/*埋点*/

                Bundle bundle = new Bundle();
                bundle.putSerializable(PageConstant.KEY_RECENT_COMPANY_BEAN, recentCompanyBean);
                mViewModel.navigate(R.id.action_recentWorkExpListFragment_to_addNewWorkExpFragment, recentCompanyBean.isNoAll ? null : bundle);
            }
        });

        if (mViewModel.canSkip()) {
            titleView.setActionButtonListener("跳过", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AppAnalysisUtil.dotWelcomeGeekAddWordClick(3);/*埋点*/

                    mViewModel.navigate(R.id.action_recentWorkExpListFragment_to_addNewWorkExpFragment, null);
                }
            });
        }

        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                mViewModel.backUp(activity);
            }
        });
    }

    @Override
    public void initData() {
        super.initData();
        List<RecentCompanyBean> recentCompanyBeanList = new ArrayList<>();
        if (expAddSugListResponse != null && LList.getCount(expAddSugListResponse.dataList) > 0) {
            recentCompanyBeanList.addAll(expAddSugListResponse.dataList);
        }
        recentCompanyBeanList.add(new RecentCompanyBean("以上都没有", null, true));

        recentCompanyAdapter.setNewData(recentCompanyBeanList);

        AppAnalysisUtil.dotWelcomeGeekAddWordExpo(1);/*埋点*/
    }

    @Override
    protected void onBackPressed() {
        super.onBackPressed();
        mViewModel.backUp(activity);
    }

}
