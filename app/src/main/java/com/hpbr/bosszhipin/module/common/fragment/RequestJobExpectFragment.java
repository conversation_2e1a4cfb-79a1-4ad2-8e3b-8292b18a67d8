package com.hpbr.bosszhipin.module.common.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseNavigationFragment;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.common.adapter.RequestJobExpectAdapter;
import com.hpbr.bosszhipin.module.common.bean.NewWorkExpEntity;
import com.hpbr.bosszhipin.module.common.bean.RequestJobStatusBean;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekBackflowRouter;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectPageRouter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.utils.functions.Consumer2;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;

import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * @ClassName ：RequestJobExpectFragment
 * @Description ：「求职期望」页面
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  9:17 PM
 */
public class RequestJobExpectFragment extends BaseNavigationFragment<GeekOldUserActiveAgainViewModel> implements View.OnClickListener {

    private AppTitleView titleView;
    private TextView tv_tip;
    private TextView tv_tip_desc;
    private RecyclerView rv_list;
    private ZPUIRoundButton zpui_round_btn_begin_apply_job;
    private RequestJobExpectAdapter requestJobExpectAdapter;
    private ConstraintLayout cl_begin_apply_job;
    private List<LevelBean> workStatusList;
    private boolean isFirstOnResume = true;
    private Runnable getRequestJobExpectDataRunnable;
    private NewWorkExpEntity newWorkExpEntity;

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(GeekOldUserActiveAgainViewModel.class);
    }

    @Override
    protected void registerLoading() {
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_request_job_expect;
    }

    @Override
    protected void initViews(View view) {
        workStatusList = VersionAndDatasCommon.getInstance().getJobStatusList();
        initParams();
        initView(view);
        initLiveDataObserve();
        initEventListener();
        initData();
    }

    private void initParams() {
        Bundle bundle = getArguments();
        if (bundle == null) return;
        newWorkExpEntity = (NewWorkExpEntity) bundle.getSerializable(PageConstant.KEY_WORK_EXP_FORM_DATA);
    }

    private void initView(View view) {
        titleView = view.findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setActionButtonColorBlue();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(activity, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(activity);
        titleView.setPadding(0, statusBarHeight, 0, 0);

        tv_tip = view.findViewById(R.id.tv_tip);
        tv_tip_desc = view.findViewById(R.id.tv_tip_desc);
        rv_list = view.findViewById(R.id.rv_list);
        zpui_round_btn_begin_apply_job = view.findViewById(R.id.zpui_round_btn_begin_apply_job);
        cl_begin_apply_job = view.findViewById(R.id.cl_begin_apply_job);

        cl_begin_apply_job.setVisibility(View.VISIBLE);
        zpui_round_btn_begin_apply_job.setText(mViewModel.isHaveFlowBackCompleteProcess ? "下一步" : "确认并开始求职");
        rv_list.setPadding(ZPUIDisplayHelper.dp2px(activity, 20), 0, ZPUIDisplayHelper.dp2px(activity, 20), 0);

        requestJobExpectAdapter = new RequestJobExpectAdapter(activity, eventListener);
        rv_list.setAdapter(requestJobExpectAdapter);
    }

    private void initLiveDataObserve() {
        /*获取求职期望页面数据成功*/
        mViewModel.getRequestJobExpectData.observe(this, modelList -> {
            requestJobExpectAdapter.setNewData(modelList);
        });
        /*保存求职状态 成功*/
        mViewModel.saveWorkStatusLiveData.observe(this, requestJobStatusBean -> {
            if (requestJobStatusBean == null) return;
            updateItemModel(RequestJobExpectModel.TYPE_REQUEST_JOB_STATUS, (targetIndex, model) -> {
                if (targetIndex < 0) return;
                if (model == null || !(model.getData() instanceof RequestJobStatusBean)) return;
                RequestJobStatusBean statusBean = (RequestJobStatusBean) model.getData();
                statusBean.currentWorkStatus = requestJobStatusBean.currentWorkStatus;
                statusBean.applyStatusContent = requestJobStatusBean.applyStatusContent;

                requestJobExpectAdapter.notifyItemChanged(targetIndex);
            });
        });
        /*保存工作经历成功*/
        mViewModel.saveWorkExperienceLiveData.observe(this, value -> {
            if (value == null) return;
            handleJump();
        });
    }

    private void initEventListener() {
        zpui_round_btn_begin_apply_job.setOnClickListener(this);
        if (mViewModel.canSkip()) {
            titleView.setActionButtonListener("跳过", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    handleJump();
                    AppAnalysisUtil.dotWelcomeGeekApplyClick(0);//埋点
                }
            });
        }

        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                performBack();
            }
        });
    }

    private void performBack() {
        if (newWorkExpEntity == null) {/*从「添加新的工作经历」点击「跳过」按钮进入到当前页面的，再点击返回后，从当前页面返回到「添加新的工作经历」页面，需要清空表单*/
            mViewModel.resetAddExpFormLiveData.postValue(true);
        }
        mViewModel.backUp(activity);
    }

    private final RequestJobExpectAdapter.EventListener eventListener = new RequestJobExpectAdapter.EventListener() {
        @Override
        public void onApplyStatusClick(int currentWorkStatus) {
            mViewModel.showRequestJobStatusDialogDialog(activity, currentWorkStatus, selectWorkStatus -> mViewModel.saveWorkStatus(selectWorkStatus == null ? 0 : selectWorkStatus.intValue()));
        }

        @Override
        public void onEditJobIntentClick(JobIntentBean jobIntent) {
            GeekExpectPageRouter.Edit.jump(/*跳转 期望编辑页*/
                    activity,
                    GeekExpectPageRouter.Edit.RequestParams.obj()
                            .requestCode(GeekExpectConstants.REQ_JOB_INTENT_EDIT)
                            .jobIntentBean(jobIntent)
                            .markType(GeekExpectConstants.ORIGINAL_F1)
                            .sourceType(-1));
        }

        @Override
        public void onClickAddJobIntent() {/*跳转 期望添加页*/
            GeekExpectPageRouter.Create.jump(
                    activity,
                    GeekExpectPageRouter.Create.RequestParams.obj()
                            .setRequestCode(GeekExpectConstants.REQ_JOB_INTENT_ADD)
                            .setSource(-1));
        }
    };

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.zpui_round_btn_begin_apply_job) {
            AppAnalysisUtil.dotWelcomeGeekApplyClick(1);//埋点
            if (newWorkExpEntity != null) {
                mViewModel.saveWorkExperience(newWorkExpEntity);
            } else {
                handleJump();
            }

        }
    }

    /**
     * 处理跳转
     */
    private void handleJump() {
        if (mViewModel.isAdvantageCallBack) {
            activity.setResult(Activity.RESULT_OK);
            AppUtil.finishActivity(activity, ActivityAnimType.NONE);
        } else {
            AppUtil.finishActivity(activity, ActivityAnimType.NONE);
            if (mViewModel.isHaveFlowBackCompleteProcess) {
                GeekBackflowRouter.builder(activity, mViewModel.certName, mViewModel.status, mViewModel.flowBackPageCodeList)
                        .build()
                        .jump();
            }
        }

    }

    @Override
    public void initData() {
        super.initData();
        mViewModel.getRequestJobExpectData();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!isFirstOnResume) {
            Utils.runOnUiThreadDelayed(getRequestJobExpectDataRunnable = () -> mViewModel.getRequestJobExpectData(), 300);
        }
        isFirstOnResume = false;
    }

    /***
     * 根据ItemType查询某个Item在列表中的索引
     * @param itemType
     * @return
     */
    private int getItemModelIndexByType(int itemType) {
        if (requestJobExpectAdapter == null) return -1;
        List<RequestJobExpectModel> itemModelList = requestJobExpectAdapter.getData();
        if (LList.getCount(itemModelList) == 0) return -1;
        for (RequestJobExpectModel itemModel : itemModelList) {
            if (itemModel == null) continue;
            if (itemModel.getItemType() == itemType) {
                return itemModelList.indexOf(itemModel);
            }
        }
        return -1;
    }

    /***
     * 更新Item
     * @param itemType
     * @param consumer
     */
    private void updateItemModel(int itemType, Consumer2<Integer, RequestJobExpectModel> consumer) {
        int targetIndex = getItemModelIndexByType(itemType);
        if (targetIndex < 0) return;
        List<RequestJobExpectModel> modelList = requestJobExpectAdapter.getData();
        RequestJobExpectModel itemModel = LList.getElement(modelList, targetIndex);
        if (consumer != null) {
            consumer.accept(targetIndex, itemModel);
        }
    }

    @Override
    protected void onBackPressed() {
        super.onBackPressed();
        performBack();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (getRequestJobExpectDataRunnable != null) {
            Utils.removeHandlerCallbacks(getRequestJobExpectDataRunnable);
            getRequestJobExpectDataRunnable = null;
        }
    }
}
