package com.hpbr.bosszhipin.module.common.fragment;

import static com.hpbr.bosszhipin.config.Constants.FINISH_SWITCH_MIDDLE_PAGE;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseNavigationFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.identity.StudentRecordSwitchManageActivity;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module_geek_export.GeekBackflowRouter;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekMineModuleService;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.sankuai.waimai.router.Router;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：StudentActiveWelcomeFragment
 * @Description ：「学生」再次活跃欢迎页
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  5:11 PM
 */
public class StudentActiveWelcomeFragment extends BaseNavigationFragment<GeekOldUserActiveAgainViewModel> implements View.OnClickListener {

    private SimpleDraweeView ivAvatar;
    private MTextView tvName;
    private MTextView tvWelcomeTitle;
    private ZPUIRoundButton zpui_btn_is_student;
    private ZPUIRoundButton zpui_btn_is_workplace_people;
    private AppTitleView titleView;

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(GeekOldUserActiveAgainViewModel.class);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_student_active_welcome;
    }

    @Override
    protected void initViews(View view) {
        initView(view);
        initData();
        initEventListener();
    }

    private void initView(View view) {
        titleView = view.findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setActionButtonColorBlue();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(activity, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(activity);
        titleView.setPadding(0, statusBarHeight, 0, 0);
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
        /*这段代码是为了解决，登录之后进入到这个页面，界面上面莫名其妙的蒙层*/
        View view_line_2 = view.findViewById(R.id.view_line_2);
        view_line_2.requestFocus();
        view_line_2.setFocusable(true);
        view_line_2.setFocusableInTouchMode(true);

        ivAvatar = view.findViewById(R.id.iv_avatar);
        tvName = view.findViewById(R.id.tv_name);
        tvWelcomeTitle = view.findViewById(R.id.tv_welcome_title);
        zpui_btn_is_student = view.findViewById(R.id.zpui_btn_is_student);
        zpui_btn_is_workplace_people = view.findViewById(R.id.zpui_btn_is_workplace_people);
    }

    @Override
    public void initData() {
        super.initData();

        UserBean user = UserManager.getLoginUser();
        if (user != null) {
            ivAvatar.setImageURI(user.avatar);
            if (mViewModel.certName) {
                tvName.setText(getString(R.string.string_geek_new_active_greeting, user.name));
            } else {
                tvName.setText("Hello，");
            }
            tvWelcomeTitle.setText("请选择您当前的求职身份");
        }
    }

    private void initEventListener() {
        zpui_btn_is_student.setOnClickListener(this);
        zpui_btn_is_workplace_people.setOnClickListener(this);
        ReceiverUtils.register(activity, mReceiver, FINISH_SWITCH_MIDDLE_PAGE);
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.zpui_btn_is_student) {/*点击「目前是学生」*/
            AppAnalysisUtil.dotWelcomeGeekClick(3);/*埋点*/
            AppUtil.finishActivity(activity, ActivityAnimType.NONE);
            if (mViewModel.isHaveFlowBackCompleteProcess) {
                GeekBackflowRouter.builder(activity, mViewModel.certName, mViewModel.status, mViewModel.flowBackPageCodeList)
                        .build()
                        .jump();
            }
        } else if (viewId == R.id.zpui_btn_is_workplace_people) {/*点击「目前是职场人」*/
            AppAnalysisUtil.dotWelcomeGeekClick(4);/*埋点*/
            final GeekMineModuleService geekMineModuleService = Router.getService(GeekMineModuleService.class, GeekConsts.ServiceKey.KEY_GEEK_MODULE_SERVICE);
            if (geekMineModuleService == null) return;
            geekMineModuleService.studentChangeToWorker(activity, () -> {
                changeFinish();
                AppUtil.finishActivity(activity);
            });
        }
    }

    private void changeFinish() {
        //通知F1
        Intent f1Intent = new Intent(Constants.RECEIVER_STUDENT_ROLE_SWITCH);
        ReceiverUtils.sendBroadcast(activity, f1Intent);
        //学生党切换职场人 需要关闭 切换状态管理界面
        StudentRecordSwitchManageActivity.sendFinishBroadcast(activity);
        //刷新F3UI头像有学生党图标
        Intent f3Intent = new Intent(Constants.REFRESH_F3_LOCAL_DATA_ACTION);
        ReceiverUtils.sendBroadcastSystem(activity, f3Intent);
    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null && TextUtils.equals(intent.getAction(), Constants.FINISH_SWITCH_MIDDLE_PAGE)) {
                changeFinish();
                AppUtil.finishActivity(activity);
            }
        }
    };

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ReceiverUtils.unregister(activity, mReceiver);
    }
}
