package com.hpbr.bosszhipin.module.common.fragment;

import android.os.Bundle;
import android.view.View;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseNavigationFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;
import com.hpbr.bosszhipin.module.common.viewmodel.GeekOldUserActiveAgainViewModel;
import com.hpbr.bosszhipin.module.guideprocess.manager.GuideProcessJumpManager;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.onlineresume.activity.SubPageTransferActivity;
import com.hpbr.bosszhipin.module.onlineresume.activity.sub.AdvantageEditFragment;
import com.hpbr.bosszhipin.module.resume.bean.AdvantageEditParamsBean;
import com.hpbr.bosszhipin.module_geek_export.GeekBackflowRouter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：WorkplacePeopleActiveWelcomeFragment
 * @Description ：「职场人」再次活跃欢迎页
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  5:09 PM
 */
public class WorkplacePeopleActiveWelcomeFragment extends BaseNavigationFragment<GeekOldUserActiveAgainViewModel> implements View.OnClickListener {

    private SimpleDraweeView ivAvatar;
    private MTextView tvName;
    private MTextView tvWelcomeTitle;
    private ZPUIRoundButton zpui_btn_have_new_exp;
    private ZPUIRoundButton zpui_btn_no_exp;
    private AppTitleView titleView;

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(GeekOldUserActiveAgainViewModel.class);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.layout_fragment_workpalce_people_active_welcome;
    }

    @Override
    protected void initViews(View view) {
        initView(view);
        initData();
        initLiveDataObserve();
        initEventListener();
    }


    private void initView(View view) {
        titleView = view.findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setActionButtonColorBlue();
        titleView.getClTitle().setBackgroundColor(ContextCompat.getColor(activity, R.color.color_00000000));
        int statusBarHeight = ScreenUtil.getStatusBarHeight(activity);
        titleView.setPadding(0, statusBarHeight, 0, 0);
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(activity, R.color.app_green_dark));
        /*这段代码是为了解决，登录之后进入到这个页面，界面上面莫名其妙的蒙层*/
        View view_line_2 = view.findViewById(R.id.view_line_2);
        view_line_2.requestFocus();
        view_line_2.setFocusable(true);
        view_line_2.setFocusableInTouchMode(true);

        ivAvatar = view.findViewById(R.id.iv_avatar);
        tvName = view.findViewById(R.id.tv_name);
        tvWelcomeTitle = view.findViewById(R.id.tv_welcome_title);
        zpui_btn_have_new_exp = view.findViewById(R.id.zpui_btn_have_new_exp);
        zpui_btn_no_exp = view.findViewById(R.id.zpui_btn_no_exp);
    }

    @Override
    public void initData() {
        super.initData();

        UserBean user = UserManager.getLoginUser();
        if (user != null) {
            ivAvatar.setImageURI(user.avatar);
            if (mViewModel.certName) {
                tvName.setText(getString(R.string.string_geek_new_active_greeting, user.name));
            } else {
                tvName.setText("Hello，");
            }
            tvWelcomeTitle.setText(getString(R.string.string_geek_new_active_greeting_welcome_back_2, mViewModel.days));
        }
    }

    private void initLiveDataObserve() {
        /*获取添加工作经历建议数据*/
        mViewModel.getExpAddSugListLiveData.observe(this, response -> {
            if (response != null && LList.getCount(response.dataList) > 0) {/*约过面试*/
                Bundle bundle = new Bundle();
                bundle.putSerializable(PageConstant.KEY_EXP_ADD_SUG_LIST_RESPONSE, response);
                mViewModel.navigate(R.id.action_workplacePeopleActiveWelcome_to_recentWorkExpListFragment, bundle);
            } else {
                mViewModel.navigate(R.id.action_workplacePeopleActiveWelcome_to_addNewWorkExpFragment, null);
            }
        });
    }

    private void initEventListener() {
        zpui_btn_have_new_exp.setOnClickListener(this);
        zpui_btn_no_exp.setOnClickListener(this);
        if (mViewModel.canSkip()) {
            titleView.setActionButtonListener("跳过", new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    AppAnalysisUtil.dotWelcomeGeekClose();/*埋点*/

                    AppUtil.finishActivity(activity);
                    if (mViewModel.isHaveFlowBackCompleteProcess) {
                        GeekBackflowRouter.builder(activity, mViewModel.certName, mViewModel.status, mViewModel.flowBackPageCodeList)
                                .build()
                                .jump();
                    }
                }
            });
        }
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.zpui_btn_have_new_exp) {/*点击「有新工作经历」*/
            AppAnalysisUtil.dotWelcomeGeekClick(1);/*埋点*/
            if (mViewModel.guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR) {
                UserBean loginUser = UserManager.getLoginUser();
                String title = loginUser != null && loginUser.geekInfo != null ? loginUser.geekInfo.advantageTitle : "";
                Bundle b = AdvantageEditFragment.getBundle(
                        AdvantageEditParamsBean.obj()
                                .setFrom(AdvantageEditParamsBean.From.TYPE_CALL_BACK).setAdvantage(title), true);
                SubPageTransferActivity.jumpForResult(getActivity(), AdvantageEditFragment.class, b, PageConstant.REQ_ADVANTAGE_EDIT);
            } else {
                mViewModel.getExpAddSugList();
            }
        } else if (viewId == R.id.zpui_btn_no_exp) {/*点击「没有新工作经历」*/
            AppAnalysisUtil.dotWelcomeGeekClick(2);/*埋点*/
            mViewModel.navigate(R.id.action_workplacePeopleActiveWelcome_to_requestJobExpectFragment, null);
        }
    }
}
