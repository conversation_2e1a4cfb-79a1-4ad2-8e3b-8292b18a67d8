package com.hpbr.bosszhipin.module.common.identity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;

import androidx.fragment.app.Fragment;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.login.LoginRouter;

/**
 * Created by monch on 15/6/25.
 */
public class ChangeIdentityActivity extends BaseActivity {


    public static final String CHANGE_IDENTITY_COME_KEY = "com.hpbr.bosszhipin.CHANGE_IDENTITY_COME_KEY";
    public static final String CHANGE_IDENTITY_IS_REGISTER_KEY = "com.hpbr.bosszhipin.CHANGE_IDENTITY_IS_REGISTER_KEY";
    public static final String CHANGE_IDENTITY_SHOW_HUNTER = "com.hpbr.bosszhipin.CHANGE_IDENTITY_SHOW_HUNTER";//是否显示猎头入口
    public static final String CHANGE_IDENTITY_RE_FILL_BOSS_INFO = "com.hpbr.bosszhipin.CHANGE_IDENTITY_RE_FILL_BOSS_INFO";

    private static final String WITH_LOGOUT_FRAGMENT_TAG = "WITH_LOGOUT_FRAGMENT_TAG";
    private static final String WITH_OUT_LOGOUT_FRAGMENT_TAG = "WITH_OUT_LOGOUT_FRAGMENT_TAG";

    private final IdentityHelper identityHelper = new IdentityHelper(this);
    private boolean mComeType;
    private int expectedRole;

    public static void startActivity(Context context, boolean finishSelf, boolean comeType) {
        Intent intent = new Intent(context, ChangeIdentityActivity.class);
        intent.putExtra(ChangeIdentityActivity.CHANGE_IDENTITY_COME_KEY, comeType);
        AppUtil.startActivity(context, intent, finishSelf, ActivityAnimType.NONE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_change_identity);
        mComeType = getIntent().getBooleanExtra(CHANGE_IDENTITY_COME_KEY, false);
        expectedRole = getIntent().getIntExtra(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, -1);
        if (mComeType) {
            initFragment(IdentityWithOutLoginOutFragment.newInstance(expectedRole), WITH_OUT_LOGOUT_FRAGMENT_TAG);
        } else {
            initFragment(IdentityWithLoginOutFragment.newInstance(getIntent().getExtras()), WITH_LOGOUT_FRAGMENT_TAG);
        }
    }

    private void initFragment(Fragment fragment, String tag) {
        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.ll_container, fragment, tag)
                .commitAllowingStateLoss();
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Fragment fragment = getSupportFragmentManager().findFragmentByTag(WITH_OUT_LOGOUT_FRAGMENT_TAG);
            //当前屏蔽返回键
            if (fragment == null) {
                return false;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

}
