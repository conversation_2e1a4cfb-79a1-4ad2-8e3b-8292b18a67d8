package com.hpbr.bosszhipin.module.common.identity;

import android.content.Intent;
import android.text.TextUtils;

import com.bszp.kernel.DataKernel;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.helper.PreloadWebViewTool;
import com.hpbr.bosszhipin.common.pub.entity.LOGIN_STATUS;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.get.export.GetRouter;
import com.hpbr.bosszhipin.module.login.util.UserInfoUtil;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.receiver.LoginStatusManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.monch.lbase.util.L;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.api.SwitchIdentityRequest;
import net.bosszhipin.base.ApiRequestCallback;

import message.server.MSGManager;
import message.server.SyncMessageManager;

/**
 * Created by guofeng
 * on 2017/7/31.
 */

public class IdentityHelper implements UserInfoUtil.OnGetUserInfoCallback {

    private static final String TAG = "IdentityHelper";

    private BaseActivity baseActivity;
    private boolean isHunter;
    private boolean needRefillBossInfo;  // 是否需要充填Boss基本信息
    private boolean mComeType; // true 切换身份 false 身份选择

    public IdentityHelper(BaseActivity baseActivity) {
        this.baseActivity = baseActivity;
    }

    public void setComeType(boolean mComeType) {
        this.mComeType = mComeType;
    }

    public void changeIdentity(ROLE targetRole) {
        if (UserManager.getUserLoginStatus() != LOGIN_STATUS.LOGIN) {
            T.ss("请登录后再试");
            AppUtil.startActivity(baseActivity, new Intent(baseActivity, MainActivity.class), true, ActivityAnimType.NONE);
            return;
        }
        if (targetRole == UserManager.getUserRole() && !this.isHunter) { // 角色相同
            UserInfoUtil userInfoUtil = new UserInfoUtil();
            userInfoUtil.setOnGetUserInfoCallback(IdentityHelper.this);
            userInfoUtil.setCheckSecurityFramework(false);
            userInfoUtil.requestUserInfo(true);
        } else {// 角色不同
            switchRole(targetRole);
        }
    }

    public void setHunter(boolean isHunter) {
        this.isHunter = isHunter;
    }

    public void setNeedRefillBossInfo(boolean refillBossInfo) {
        needRefillBossInfo = refillBossInfo;
    }

    private void switchRole(final ROLE role) {
        MSGManager.get().disconnect();
        SwitchIdentityRequest request = new SwitchIdentityRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void handleInChildThread(ApiData<SuccessResponse> data) {
                if (DataKernel.isUserChatService()) { //切换身份后， 消息还在执行DB
                    SyncMessageManager.getInstance().clearMessageByRole(UserManager.isBossRole() ? ROLE.GEEK.get() : ROLE.BOSS.get());
                    SyncMessageManager.getInstance().submit(() -> TLog.info(TAG, "=========切换身份，消息队列 无法消息了========="));
                }
                DataKernel.getInstance().changeIdentity(role.get());
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                ContactManager.getInstance().obtainRefreshStrategy().initRefreshTime();
                NotifyUtils.resetNotifyId();
                ContactManager.getInstance().switchRole();// 切换身份成功后，需要初始化获取联系人数据
                //切换身份删除 之前身份的数据
                SyncMessageManager.getInstance().clearMessageByRole(UserManager.isBossRole() ? ROLE.GEEK.get() : ROLE.BOSS.get());
                //切换身份后，清除所有上传任务
                GetRouter.clearAllUploadVideoTask();
                TLog.info(TAG, "准备获取用户信息");
                UserInfoUtil userInfoUtil = new UserInfoUtil();
                userInfoUtil.setOnGetUserInfoCallback(IdentityHelper.this);
                userInfoUtil.requestUserInfo(true);
                TLog.info(TAG, "身份切换完成");
                PreloadWebViewTool.getInstance().resetReloadStatus();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                TLog.info(TAG, "身份切换失败");
                onSwitchRoleResult(false, reason.getErrReason());
                AppUtil.finishActivity(baseActivity, ActivityAnimType.NONE);
            }
        });
        request.identityType = role.get() + "";
        HttpExecutor.execute(request);
    }


    private void onSwitchRoleResult(boolean success, String errorReason) {
        L.d("jump", "======IdentityHelper========isHunter:" + isHunter + " success:" + success);
        if (success) {
            if (callback == null) {
                switchIdentitySuccess();
            } else {
                callback.onSuccess();
            }
        } else {
            if (TextUtils.isEmpty(errorReason)) {
                ToastUtils.showText("切换身份失败，请稍后再试");
            } else {
                ToastUtils.showText(errorReason);
            }
            if (callback != null) {
                callback.onFailed(new ErrorReason(-1, errorReason));
            }
        }
    }

    public void switchIdentitySuccess() {
        if (isHunter) {//猎头身份下切换Boss或者牛人身份成功
            App.get().finishAll();
            Intent intent = new Intent(baseActivity, MainActivity.class);
            intent.putExtra(Constants.DATA_BOOLEAN_JUNMP_COMPLETE_PAGE, true);
            AppUtil.startActivity(baseActivity, intent);
        } else {
            if (UserManager.isGeekRole()) {
                IdentitySwitchJumpUtils.dealJump(baseActivity, true);
            } else {
                IdentitySwitchJumpUtils.dealJump(baseActivity, needRefillBossInfo, true);
            }
        }
    }

    @Override
    public void onGetUserInfoCompleteCallback() {

    }

    @Override
    public void onGetUserInfoCallback(boolean success, String errorMsg) {
        TLog.info(TAG, "切换身份完成：%b--%s", success, errorMsg);
        if (UserManager.getUserLoginStatus() == LOGIN_STATUS.LOGIN) {
            LoginStatusManager.changed(baseActivity, false);
            onSwitchRoleResult(success, errorMsg);
        } else {
            AppUtil.startActivity(baseActivity, new Intent(baseActivity, MainActivity.class), true, ActivityAnimType.NONE);
        }
    }

    ChangeIdentityListener callback;

    public void setCallback(ChangeIdentityListener callback) {
        this.callback = callback;
    }

    public interface ChangeIdentityListener {
        void onSuccess();

        void onFailed(ErrorReason reason);
    }
}
