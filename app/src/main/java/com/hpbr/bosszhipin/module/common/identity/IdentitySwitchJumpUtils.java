package com.hpbr.bosszhipin.module.common.identity;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.RegistrationWizardGray;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.monch.lbase.util.SP;

public class IdentitySwitchJumpUtils {


    public static void dealJumpCanBack(Activity baseActivity, boolean finish) {
        dealJump(baseActivity, false, finish, true);
    }

    public static void dealJump(Activity baseActivity, boolean finish) {
        dealJump(baseActivity, false, finish, false);
    }

    public static void dealJump(Activity baseActivity, boolean needRefillBossInfo, boolean isFinish) {
        dealJump(baseActivity, needRefillBossInfo, isFinish, false);
    }

    /**
     * @param baseActivity
     * @param needRefillBossInfo
     * @param isFinish           是否  关闭当前页面  baseActivity
     * @param back               是否可能返回上一级页面
     */
    public static void dealJump(Activity baseActivity, boolean needRefillBossInfo, boolean isFinish, boolean back) {
        SP.get().putBoolean(ChangeIdentityActivity.CHANGE_IDENTITY_IS_REGISTER_KEY, false);
        if (UserManager.getUserRole() == ROLE.GEEK) {
            UserBean user = UserManager.getLoginUser();
            if (!UserManager.isInfoCompleteGeek(user)) {
                // 8.03 新版注册完善流程
//                if (NavigatorController.is803GeekCompletionWizard()) {
                GeekPageRouter.syncGeekData();
                GeekPageRouter.jumpToCompletionWizard(baseActivity, back);
//                }

//                else {
//                    if (!UserManager.isBasicInfoCompleteGeek(user)) {
//                        RegistrationWizardGray.startGeekRegistration(baseActivity, isFinish, back);
//                    } else if (!UserManager.isMoreInfoCompleteGeek(user)) {
//                        if (UserManager.isGeekRole()
//                                && user != null
//                                && user.geekInfo != null
//                                && (user.geekInfo.graduate == AT_SCHOOL_INTERN
//                                || user.geekInfo.graduate == FRESH_GRADUATE_FIND_JOB
//                                || user.geekInfo.graduate == NONE_WORK_EXPERIENCE)
//                        ) {
//                            RegistrationWizardGray.startGeekRegistration(baseActivity, isFinish, back);
//                        } else {
//                            RegistrationWizardGray.startGeekFirstExpectCompletion(baseActivity, isFinish, back);
//                        }
//                    }
//                }
            } else {
                AppUtil.startActivity(baseActivity, new Intent(baseActivity, MainActivity.class), isFinish, ActivityAnimType.NONE);
            }
        } else {
            UserBean user = UserManager.getLoginUser();
            if (!TextUtils.isEmpty(UserManager.getSecurityUrl())) {
                if (needRefillBossInfo && !UserManager.isBasicInfoCompleteBoss(user)) {
                    RegistrationWizardGray.startBossRegistration(baseActivity);
                } else {
                    SecurityFrameworkManager.getInstance().check();
                }
                if (isFinish) {
                    baseActivity.finish();
                }
            } else if (!UserManager.isBasicInfoCompleteBoss(user)) {
                //信息不完整跳转注册流程
                RegistrationWizardGray.startBossRegistration(baseActivity);
            } else {
                AppUtil.startActivity(baseActivity, new Intent(baseActivity, MainActivity.class), isFinish, ActivityAnimType.NONE);
            }
        }
    }
}
