package com.hpbr.bosszhipin.module.common.identity;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.LogoutRequest;
import net.bosszhipin.api.ResultStringResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Created by guofeng
 * on 2017/7/31.
 */
public class IdentityWithLoginOutFragment extends BaseFragment implements View.OnClickListener {

    private IdentityHelper identityHelper;
    //    private MTextView tvSwitchHunter;
    private int showHunter;

    public static IdentityWithLoginOutFragment newInstance(Bundle args) {
        IdentityWithLoginOutFragment fragment = new IdentityWithLoginOutFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof BaseActivity) {
            identityHelper = new IdentityHelper((BaseActivity) activity);
        }
    }


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_with_logion_out, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Bundle data = getArguments();
        boolean refillBossInfo = false;  // 充填Boss基本信息
        if (data != null) {
            showHunter = data.getInt(ChangeIdentityActivity.CHANGE_IDENTITY_SHOW_HUNTER);
            refillBossInfo = data.getBoolean(ChangeIdentityActivity.CHANGE_IDENTITY_RE_FILL_BOSS_INFO);
        }
        identityHelper.setHunter(showHunter == 1 ? true : false);
        identityHelper.setNeedRefillBossInfo(refillBossInfo);
        view.findViewById(R.id.tv_login_out).setOnClickListener(this);
        view.findViewById(R.id.btn_find_job).setOnClickListener(this);
        view.findViewById(R.id.btn_hire).setOnClickListener(this);

        Bundle arguments = getArguments();
        if (arguments != null) {
            int expectedRole = arguments.getInt(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, -1);
            autoChangeIdentity(expectedRole);
        }
    }

    //自动切换
    private void autoChangeIdentity(int expectedRole) {
        if (expectedRole != -1) { // 用户选择了期望身份，自动帮他切换
            if (expectedRole == ROLE.BOSS.get()) {
                identityHelper.changeIdentity(ROLE.BOSS);
            } else if (expectedRole == ROLE.GEEK.get()) {
                identityHelper.changeIdentity(ROLE.GEEK);
            }
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.tv_login_out) {
            DialogUtils d = new DialogUtils.Builder(activity)
                    .setDoubleButton()
                    .setTitle(R.string.warm_prompt)
                    .setDesc("确定要退出登录？")
                    .setNegativeAction(R.string.string_cancel)
                    .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            logout();
                        }
                    })
                    .build();
            d.show();

            AnalyticsFactory.create()
                    .action(AnalyticsAction.SIGN_SELECTION)
                    .param("p", 0) // 0:退出登录，1:我要找工作，2:我要招人
                    .build();
        } else if (i == R.id.btn_find_job) {
            identityHelper.changeIdentity(ROLE.GEEK);

            AnalyticsFactory.create()
                    .action(AnalyticsAction.SIGN_SELECTION)
                    .param("p", 1) // 0:退出登录，1:我要找工作，2:我要招人
                    .param("p15", 1) // p=1or2时，传场景：1:完善流程 2:完善后切换身份
                    .build();
        } else if (i == R.id.btn_hire) {
            identityHelper.changeIdentity(ROLE.BOSS);

            AnalyticsFactory.create()
                    .action(AnalyticsAction.SIGN_SELECTION)
                    .param("p", 2) // 0:退出登录，1:我要找工作，2:我要招人
                    .param("p15", 1) // p=1or2时，传场景：1:完善流程 2:完善后切换身份
                    .build();
        }

//        else if (i == R.id.tv_switch_hunter) {
//            ActionManager.startStopService(getActivity());
//            AnalyticsFactory.create().action(AnalyticsAction.ACTION_HUNTER_REG_BG)
//                    .param("p", "2")
//                    .build();
//            showProgressDialog("处理中...");
//            // TODO 切换猎头
//            Kernel.getInstance().prepare(new PrepareCallback() {
//
//                @Override
//                public void handleInChildThread(PrepareInfo prepareInfo) {
//                    Kernel.getInstance().login(new Account(UserManager.getUID() , AccountLifecycle.IDENTITY_HUNTER, UserManager.getToken(), UserManager.getWebToken(), UserManager.getSecretKey()));
//                    App.get().getMainHandler().post(new Runnable() {
//                        @Override
//                        public void run() {
//                            if(!TextUtils.isEmpty(prepareInfo.getToCertUrl())){
//                                Intent intent = new Intent(getActivity(), WebViewActivity.class);
//                                intent.putExtra(Constants.DATA_URL, prepareInfo.getToCertUrl());
//                                AppUtil.startActivity(getActivity(), intent);
//                            }
//                        }
//                    });
//                }
//
//                @Override
//                public void onSuccess() {
//                    dismissProgressDialog();
//                    L.d("switch","===========hunter success");
//                }
//
//                @Override
//                public void onFailed(String object) {
//                    dismissProgressDialog();
//                    L.d("switch","===========hunter fail 3");
//                    T.ss(object);
//                }
//            });
//        }
    }

    /**
     * 退出登录
     * 不论接口成功或者失败，都会退出
     */
    private void logout() {
        AnalyticsFactory.create().action("reg-log-off").build();
        LogoutRequest request = new LogoutRequest(new ApiRequestCallback<ResultStringResponse>() {

            @Override
            public void onStart() {
                showProgressDialog("正在退出...");
            }

            @Override
            public void onSuccess(ApiData<ResultStringResponse> data) {
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();

                UserManager.setAccountInvalid(activity, false);
                AppUtil.finishActivity(activity);
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        HttpExecutor.execute(request);
    }

}
