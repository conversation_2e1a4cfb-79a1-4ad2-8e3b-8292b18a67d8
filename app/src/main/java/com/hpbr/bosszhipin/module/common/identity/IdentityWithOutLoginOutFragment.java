package com.hpbr.bosszhipin.module.common.identity;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.airbnb.lottie.LottieAnimationView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.login.LoginRouter;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.error.ErrorReason;

import zpui.lib.ui.shadow.layout.ZPUILinearLayout;

/**
 * Created by guofeng
 * on 2017/7/31.
 */

public class IdentityWithOutLoginOutFragment extends BaseFragment implements View.OnClickListener {

    private IdentityHelper identityHelper;
    private ROLE role;
    private LottieAnimationView ivRoleHead;
    private TextView tv_identity;
    private View iv_loading;
    private ZPUILinearLayout btnIdentity;
    private TextView tv_role_desc;

    public static IdentityWithOutLoginOutFragment newInstance(int autoIdentityRole) {
        Bundle args = new Bundle();
        args.putInt(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, autoIdentityRole);
        IdentityWithOutLoginOutFragment fragment = new IdentityWithOutLoginOutFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof BaseActivity) {
            identityHelper = new IdentityHelper((BaseActivity) activity);
        }
        identityHelper.setComeType(true);
        identityHelper.setCallback(new IdentityHelper.ChangeIdentityListener() {
            @Override
            public void onSuccess() {
                TLog.debug("identity", "onSuccess====" + ivRoleHead + " isResumed() = " + isResumed() + " ivRoleHead.isShown()=" + ivRoleHead.isShown() + " Duration = " + ivRoleHead.getDuration());
                identityHelper.switchIdentitySuccess();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (ivRoleHead.getProgress() >= 0.45f) {
                    ivRoleHead.setProgress(0.45f);
                }
                initBtnIdentity();
                btnIdentity.setEnabled(true);
                //ignore
            }
        });
        role = UserManager.getUserRole();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_without_logion_out, container, false);
    }


    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        AppTitleView appTitleView = view.findViewById(R.id.appTitleView);
        appTitleView.changeStyle(AppTitleView.SKIN_CUSTOM, Color.TRANSPARENT, true);
        appTitleView.setDividerInvisible();
        appTitleView.setBackClickListener();
        ivRoleHead = view.findViewById(R.id.iv_role_head);
        tv_role_desc = view.findViewById(R.id.tv_role_desc);
        tv_identity = view.findViewById(R.id.tv_identity);
        iv_loading = view.findViewById(R.id.iv_loading);
        btnIdentity = view.findViewById(R.id.btn_identity);
        btnIdentity.setOnClickListener(this);
        btnIdentity.setEnabled(true);
        //切换身份 接口调用成功之后， 开始执行动画 ，动画结束之后， 执行跳转
        ivRoleHead.addAnimatorUpdateListener(animatorUpdateListener);
        initRoleBackground();
        initBtnIdentity();
        Bundle arguments = getArguments();
        if (arguments != null) {
            int expectedRole = arguments.getInt(LoginRouter.IntentKey.KEY_EXPECTED_ROLE, -1);
            autoChangeIdentity(expectedRole);
        }
    }

    //卡帧
    ValueAnimator.AnimatorUpdateListener animatorUpdateListener = new ValueAnimator.AnimatorUpdateListener() {
        @Override
        public void onAnimationUpdate(ValueAnimator animation) {
            if (animation.getAnimatedFraction() > 0.45f) {
                ivRoleHead.pauseAnimation();
                ivRoleHead.removeUpdateListener(this);
            }
        }
    };

    //自动切换
    private void autoChangeIdentity(int expectedRole) {
        if (expectedRole != -1) { // 用户选择了期望身份，自动帮他切换
            ivRoleHead.resumeAnimation();
            tv_identity.setText("身份切换中");
            iv_loading.setVisibility(View.VISIBLE);
            if (expectedRole == ROLE.BOSS.get()) {
                identityHelper.changeIdentity(ROLE.BOSS);
            } else if (expectedRole == ROLE.GEEK.get()) {
                identityHelper.changeIdentity(ROLE.GEEK);
            }
        }
    }

    private void initRoleBackground() {
        String lottie = "lottie/identity_b_to_c.json";
        if (role != ROLE.BOSS) {
            lottie = "lottie/identity_c_to_b.json";
        } else {
            lottie = "lottie/identity_b_to_c.json";
        }
        ViewCompat.animate(tv_role_desc).alpha(1).setDuration(400).start();
        ivRoleHead.setAnimation(lottie);
        ivRoleHead.playAnimation();
    }

    public void initBtnIdentity() {
        if (role != ROLE.BOSS) {
            tv_identity.setText("切换为“Boss”身份");
            tv_role_desc.setText("你当前的身份是“牛人”");
        } else {
            tv_identity.setText("切换为“牛人”身份");
            tv_role_desc.setText("你当前的身份是“Boss”");
        }
        iv_loading.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.btn_identity) {
            btnIdentity.setTag(null);
            btnIdentity.setEnabled(false);
            switchRole(role.get());
        }
    }

    private void switchRole(int role) {
        ivRoleHead.resumeAnimation();
        tv_identity.setText("身份切换中");
        iv_loading.setVisibility(View.VISIBLE);
        ivRoleHead.removeUpdateListener(animatorUpdateListener);
        boolean isBoss = role == ROLE.BOSS.get();
        if (isBoss) {
            tv_role_desc.setText("你当前的身份是“牛人”");
            identityHelper.changeIdentity(ROLE.GEEK);
        } else {
            tv_role_desc.setText("你当前的身份是“Boss”");
            identityHelper.changeIdentity(ROLE.BOSS);
        }

        AnalyticsFactory.create()
                .action(AnalyticsAction.SIGN_SELECTION)
                .param("p", isBoss ? 2 : 1) // 0:退出登录，1:我要找工作，2:我要招人
                .param("p15", 2) // p=1or2时，传场景：1:完善流程 2:完善后切换身份
                .build();
    }

//    @Override
//    public void onStop() {
//        super.onStop();
//        switchIdentitySuccess();
//    }
//
//    private void switchIdentitySuccess() {
//        if (btnIdentity.getTag() != null) {
//            identityHelper.switchIdentitySuccess();
//            btnIdentity.setTag(null);
//            btnIdentity.setEnabled(true);
//        }
//    }
}
