package com.hpbr.bosszhipin.module.common.identity;

import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.common.adapter.StudentExpectTypeAdapter;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.StudentExpectTypeSortedRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

/**
 * guofeng
 */
public class StudentRecordSwitchManageActivity extends BaseActivity {

    // http://************:8088/project/30/interface/api/2888


    //code -> 0-全职，1-兼职，2-实习
    //实习
    public static final int INTERN = 2;
    //全职
    public static final int FULL_TIME = 0;
    //兼职
    public static final int PART_TIME = 1;
    //推荐
    public static final int RECOMMEND = 3;

    //广播关闭界面
    public static final String ACTION_FINISH = "ACTION_FINISH";


    //flag -> 0-关闭，1-开启，2-禁用

    public static final int CLOSE_STATUS = 0;

    public static final int OPEN_STATUS = 1;

    public static final int FORBIDDEN_STATUS = 2;

    public static final int STATUS_EDIT = 1; //排序状态
    public static final int STATUS_TOGGLE = 2; // 编辑状态
    public static boolean isAlive;
    private RecyclerView rvExpects;
    private StudentExpectTypeAdapter adapter;
    private int protocalJobType = -1;
    private AppTitleView mTitleView;
    private List<CodeNameFlagBean> datalist;
    private ItemTouchHelper touchHelper;

    public static void jump(Activity activity) {
        Intent intent = new Intent(activity, StudentRecordSwitchManageActivity.class);
        AppUtil.startActivity(activity, intent);
    }

    public static void jump(Context activity, int jobType) {
        Intent intent = new Intent(activity, StudentRecordSwitchManageActivity.class);
        intent.putExtra(Constants.DATA_INT, jobType);
        AppUtil.startActivity(activity, intent);
    }


    //发送关闭当前页面的广播
    public static void sendFinishBroadcast(Context context) {
        Intent intent = new Intent(ACTION_FINISH);
        ReceiverUtils.sendBroadcast(context, intent);
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        protocalJobType = getIntent().getIntExtra(Constants.DATA_INT, -1);
        isAlive = true;
        setContentView(R.layout.activity_student_record_switch_manager);

        mTitleView = findViewById(R.id.mTitleView);
        mTitleView.setBackClickListener();
        mTitleView.setDividerInvisible();
        mTitleView.setActionButtonColorBlue();

        changeRightActionStatus(STATUS_TOGGLE);


        initRvExpects();

        findViewById(R.id.mChange).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GeekPageRouter.jumpStudentRecordChange(StudentRecordSwitchManageActivity.this, 2);
//                StudentRecordChangeActivity.jump(StudentRecordSwitchManageActivity.this);
                AnalyticsFactory.create().action("system-newguide-statuschange-manageclick").build();
            }
        });

        ReceiverUtils.register(this, receiver, ACTION_FINISH);
    }

    private void initRvExpects() {
        rvExpects = findViewById(R.id.rv_expects);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        rvExpects.setLayoutManager(linearLayoutManager);
        datalist = getConfigList();
        if (!LList.isEmpty(datalist)) {
            adapter = new StudentExpectTypeAdapter(this, datalist);
            rvExpects.setAdapter(adapter);
            if (protocalJobType != -1) {
                CodeNameFlagBean codeNameFlagBean = getPortocalOpenItem();
                if (codeNameFlagBean != null) {
                    int index = datalist.indexOf(codeNameFlagBean);
                    rvExpects.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            View targetView = linearLayoutManager.getChildAt(index);
                            if (targetView != null) {
                                targetView.performClick();
                            }
                            ToastUtils.showText(String.format(Locale.getDefault(), "已开启%s列表", getSwitchCodeName(protocalJobType)));
                        }
                    }, 500);

                }
            }
        }
    }

    private CodeNameFlagBean getPortocalOpenItem() {
        for (CodeNameFlagBean codeNameFlagBean : datalist) {
            if (codeNameFlagBean.code == protocalJobType) {
                return codeNameFlagBean;
            }
        }
        return null;
    }


    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            //关闭界面
            if (LText.equal(ACTION_FINISH, intent.getAction())) {
                finish();
            }
        }
    };


    private @Nullable
    List<CodeNameFlagBean> getConfigList() {
        return GeekExpectManager.getGeekExpectTypeList();
    }

    private @Nullable
    String getSortedString() {
        String sortedString = "";
        if (!LList.isEmpty(datalist)) {
            for (CodeNameFlagBean typeBean : datalist) {
                sortedString = StringUtil.connectTextWithChar(",", sortedString, String.valueOf(typeBean.settingValue));
            }
        }
        return sortedString;
    }

    //开关回掉
    public interface OnSwitchChangeCallback {
        void onSwitchChangeListener();
    }


    // 切换apptitle 右侧按钮
    private void changeRightActionStatus(int status) {

        switch (status) {
            case STATUS_TOGGLE: // 默认态 可开关不可编辑
                mTitleView.setActionButtonListener("编辑排序", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_STUDENT_LIST_SORT).build();

                        if (adapter != null) {
                            changeRightActionStatus(STATUS_EDIT);
                            adapter.setStatus(STATUS_EDIT);
                            attachSortMoveAction();
                        }

                    }
                });
                break;

            case STATUS_EDIT: // 编辑态 可编辑不可开关
                mTitleView.setActionButtonListener("完成", new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        postSortedList();
                        dettachMoveAction();
                    }
                });
                break;
            default:
                break;
        }
    }

    // 为recyclerView绑定拖拽排序
    private void attachSortMoveAction() {
        if (touchHelper == null) {
            touchHelper = new ItemTouchHelper(new ItemTouchHelper.Callback() {

                @Override
                public boolean isItemViewSwipeEnabled() {

                    return true;
                }

                @Override
                public boolean isLongPressDragEnabled() {
                    return true;
                }

                @Override
                public void onSelectedChanged(@Nullable RecyclerView.ViewHolder viewHolder, int actionState) {
                    super.onSelectedChanged(viewHolder, actionState);
//                    pickUpAnimation(viewHolder.itemView);

                }

                @Override
                public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                    int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT |
                            ItemTouchHelper.RIGHT;
                    // 滑动的标记，这里允许左右滑动
                    int swipeFlags = 0;
                    return makeMovementFlags(dragFlags, swipeFlags);
                }

                @Override
                public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
                    // 移动时更改列表中对应的位置并返回true
                    Collections.swap(datalist, viewHolder.getAdapterPosition(), target
                            .getAdapterPosition());
                    return true;
                }

                @Override
                public void onMoved(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, int fromPos, @NonNull RecyclerView.ViewHolder target, int toPos, int x, int y) {
                    super.onMoved(recyclerView, viewHolder, fromPos, target, toPos, x, y);
                    adapter.notifyItemMoved(viewHolder.getAdapterPosition(), target
                            .getAdapterPosition());
                }

                @Override
                public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
                }
            });
        }
        touchHelper.attachToRecyclerView(rvExpects);

    }

    private void dettachMoveAction() {
        touchHelper.attachToRecyclerView(null);
    }

    private void postSortedList() {
        StudentExpectTypeSortedRequest request = new StudentExpectTypeSortedRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                changeRightActionStatus(STATUS_TOGGLE);
                adapter.setStatus(STATUS_TOGGLE);
                List<CodeNameFlagBean> list = new ArrayList<>();
                List<CodeNameFlagBean> configList = getConfigList();
                if (!LList.isEmpty(configList)) {
                    Intent intent = new Intent(Constants.RECEIVER_STUDENT_EXPECT_TYPE_CHANGE_ACTION);
                    ReceiverUtils.sendBroadcast(StudentRecordSwitchManageActivity.this, intent);
                    list.addAll(configList);
                    AnalyticsFactory.create().action(AnalyticsAction.ACTION_STUDENT_LIST_SORT_COMPLETE).param("p", makeSortString(list)).build();
                    adapter.setData(list);
                    rvExpects.clearOnChildAttachStateChangeListeners();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });

        request.settingValues = getSortedString();
        HttpExecutor.execute(request);

    }

    private void pickUpAnimation(View view) {
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationZ", 1f, 10f);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.setDuration(300);
        animator.start();
    }

    private String makeSortString(List<CodeNameFlagBean> list) {
        String result = "";
        if (!LList.isEmpty(list)) {
            for (CodeNameFlagBean bean : list) {
                if (bean.flag == 1) {
                    result = StringUtil.connectTextWithChar(",", result, bean.name);
                }
            }
        }
        return result;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isAlive = false;
        ReceiverUtils.unregister(this, receiver);
    }


    private String getSwitchCodeName(int jobtype) {
        int code = jobtype;
        //实习
        if (code == INTERN) {
            return "实习";
        }
        //兼职
        if (code == PART_TIME) {
            return "兼职";
        }

        if (code == RECOMMEND) {
            return "推荐";
        }
        //全职
        return "全职";
    }

}
