package com.hpbr.bosszhipin.module.common.model;

import com.hpbr.bosszhipin.base.BaseMultiItemEntity;

/**
 * @ClassName ：AddNewWorkExpModel
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  2:26 PM
 */
public class AddNewWorkExpModel<T> extends BaseMultiItemEntity<T> {

    /*公司名称*/
    public static final int TYPE_COMPANY_NAME = 1;
    /*在职时间*/
    public static final int TYPE_ON_THE_JOB_TIME = 2;
    /*工作内容*/
    public static final int TYPE_WORK_CONTENT = 3;
    /*所在行业*/
    public static final int TYPE_BELONG_INDUSTRY = 4;
    /*职位名称*/
    public static final int TYPE_POSITION_NAME = 5;


    public AddNewWorkExpModel(int itemType, T data) {
        super(itemType, data);
    }
}
