package com.hpbr.bosszhipin.module.common.model;

import com.hpbr.bosszhipin.base.BaseMultiItemEntity;

/**
 * @ClassName ：RequestJobExpectModel
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  8:04 PM
 */
public class RequestJobExpectModel<T> extends BaseMultiItemEntity<T> {

    /*「求职状态」*/
    public static final int TYPE_REQUEST_JOB_STATUS = 1;
    /*「求职期望」 列表的title*/
    public static final int TYPE_EXPECT_LIST_TITLE = 2;
    /*「期望」*/
    public static final int TYPE_EXPECT_ITEM = 3;

    public RequestJobExpectModel(int itemType, T data) {
        super(itemType, data);
    }
}
