package com.hpbr.bosszhipin.module.common.mvp;

import android.app.Application;

import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekExpectConstants;
import com.hpbr.bosszhipin.module_geek_export.StudentExpectParams;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetGeekExpectPositionRequest;
import net.bosszhipin.api.GetGeekExpectPositionResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * Author: zhouyou
 * Date: 2023/11/18
 */
public class StudentExpectPickViewModel extends BaseViewModel {
    public static final int MAX_MULTI_SELECTION_COUNT = 10;

    public MutableLiveData<Integer> loadStateObserver = new MutableLiveData<>();
    public MutableLiveData<StudentExpectPickModel> sourceDataListObserver = new MutableLiveData<>();

    public StudentExpectPickViewModel(@NonNull Application application) {
        super(application);
    }

    public void getData(StudentExpectParams studentParam) {
        boolean isFromTourist = false;
        String cityCode = "0";
        LevelBean secondLevelItem = null;
        if (studentParam != null) {
            isFromTourist = studentParam.from == GeekExpectConstants.FROM_GUEST_TOURIST;
            cityCode = studentParam.cityCode;
            secondLevelItem = studentParam.secondItem;
        }
        LevelBean finalSecondLevelItem = secondLevelItem;
        if (isFromTourist) {
            loadStateObserver.setValue(IRefreshState.STATE_LOADING);
            AppThreadFactory.createThread(() -> {
                List<LevelBean> sourceData = VersionAndDatasCommon.getInstance().getInternPositionList();
                StudentExpectPickModel model = getStudentExpectPickModel(sourceData, finalSecondLevelItem);
                sourceDataListObserver.postValue(model);
                loadStateObserver.postValue(IRefreshState.STATE_COMPLETE);
            }).start();
        } else {
            GetGeekExpectPositionRequest getGeekExpectPositionRequest = new GetGeekExpectPositionRequest(new ApiRequestCallback<GetGeekExpectPositionResponse>() {
                @Override
                public void onStart() {
                    loadStateObserver.setValue(IRefreshState.STATE_LOADING);
                }

                @Override
                public void handleInChildThread(ApiData<GetGeekExpectPositionResponse> data) {
                    GetGeekExpectPositionResponse resp = data.resp;
                    if (resp != null) {
                        List<LevelBean> sourceData;
                        if (LList.hasElement(resp.config)) {
                            sourceData = resp.config;
                        } else {
                            sourceData = VersionAndDatasCommon.getInstance().getInternPositionList();
                        }
                        StudentExpectPickModel model = getStudentExpectPickModel(sourceData, finalSecondLevelItem);
                        sourceDataListObserver.postValue(model);
                    }
                }

                @Override
                public void onSuccess(ApiData<GetGeekExpectPositionResponse> data) {

                }

                @Override
                public void onComplete() {
                    loadStateObserver.setValue(IRefreshState.STATE_COMPLETE);
                }

                @Override
                public void handleErrorInChildThread(ErrorReason reason) {
                    List<LevelBean> sourceData = VersionAndDatasCommon.getInstance().getInternPositionList();
                    StudentExpectPickModel model = getStudentExpectPickModel(sourceData, finalSecondLevelItem);
                    sourceDataListObserver.postValue(model);
                }

                @Override
                public void onFailed(ErrorReason reason) {
                }
            });
            getGeekExpectPositionRequest.cityCode = cityCode; //getCityIfAny();
            getGeekExpectPositionRequest.execute();
        }
    }

    private StudentExpectPickModel getStudentExpectPickModel(List<LevelBean> source, LevelBean secondSelectLevel) {
        StudentExpectPickModel model = new StudentExpectPickModel();
        model.sourceList = source;

        // 匹配一级二级选择的逻辑
        LevelBean mMatchFirst = null;
        LevelBean mMatchSecond = null;
        if (!LList.isEmpty(source) && secondSelectLevel != null) {
            for (LevelBean firstItem : source) {
                if (firstItem == null || LList.isEmpty(firstItem.subLevelModeList)) {
                    continue;
                }

                // 单选
                for (LevelBean secondItem : firstItem.subLevelModeList) {
                    if (secondItem == null) continue;
                    if (secondItem.code == secondSelectLevel.code) {
                        mMatchFirst = firstItem;
                        mMatchSecond = secondItem;
                        break;
                    }
                }
            }
        }

        if (mMatchSecond != null) {
            model.firstSelectLevel = mMatchFirst;
            model.secondSelectLevel = mMatchSecond;
        }
        return model;
    }
}
