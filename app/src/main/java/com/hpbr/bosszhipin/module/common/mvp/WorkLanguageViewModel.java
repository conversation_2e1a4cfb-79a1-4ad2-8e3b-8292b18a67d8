package com.hpbr.bosszhipin.module.common.mvp;

import android.app.Application;
import android.text.TextUtils;

import com.basedata.core.BasicDataNetWrok;
import com.basedata.network.response.Get1121OverseasLanguageResponse;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.common.bean.LanguageItemBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.MutableLiveData;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class WorkLanguageViewModel extends BaseViewModel {

    public static final int MAX_SELECTIONS = 5;

    public MutableLiveData<LanguageModel> languageListObserver = new MutableLiveData<>();

    public MutableLiveData<Integer> loadStateObserver = new MutableLiveData<>();

    public WorkLanguageViewModel(@NonNull Application application) {
        super(application);
    }

    private final ArrayMap<Long, LanguageItemBean> distinctDataMap = new ArrayMap<>();

    public void loadData(String countryStr, String selectLanguageStr) {
        BasicDataNetWrok.requestOverseasLanguage(countryStr, 1, new BasicDataNetWrok.BaseDataReqeustCallback<Get1121OverseasLanguageResponse>() {
            @Override
            public void onStart() {
                loadStateObserver.setValue(IRefreshState.STATE_LOADING);
            }

            @Override
            public void onSuccess(ApiData<Get1121OverseasLanguageResponse> data) {
                loadStateObserver.setValue(IRefreshState.STATE_COMPLETE);
                Get1121OverseasLanguageResponse resp = data.resp;
                if (resp != null) {
                    LanguageModel languageModel = new LanguageModel();
                    languageModel.source = getLanguageList(resp);
                    languageModel.selectItems = getSelectedLanguageList(selectLanguageStr);
                    languageListObserver.setValue(languageModel);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onfailed(ErrorReason reason) {
                showError(reason);
                loadStateObserver.setValue(IRefreshState.STATE_ERROR);
            }
        });
    }


    private List<LanguageItemBean> getLanguageList(Get1121OverseasLanguageResponse response) {

        List<LevelBean> data = new ArrayList<>();

        if (!LList.isEmpty(response.languageConfig)) {

            LevelBean group0 = new LevelBean();
            group0.name = "根据你选择的国家/地区推荐";
            group0.subLevelModeList = new ArrayList<>();
            group0.subLevelModeList.addAll(response.languageConfig);
            data.add(group0);
        }

        if (!LList.isEmpty(response.allLanguageConfig)) {

            LevelBean group1 = new LevelBean();
            group1.name = "全部语言";
            group1.subLevelModeList = new ArrayList<>();
            group1.subLevelModeList.addAll(response.allLanguageConfig);
            data.add(group1);
        }

        return getLanguageList(data);

    }

    private List<LanguageItemBean> getLanguageList(List<LevelBean> configList) {
        List<LanguageItemBean> source = new ArrayList<>();

        if (!LList.isEmpty(configList)) {
            for (LevelBean item : configList) {
                if (item == null || LList.isEmpty(item.subLevelModeList)) continue;
                String groupName = item.name;
                int subItemSize = item.subLevelModeList.size();
                for (int i = 0; i < subItemSize; i++) {
                    LevelBean subItem = item.subLevelModeList.get(i);
                    if (subItem == null) continue;
                    LanguageItemBean languageItemBean = new LanguageItemBean(subItem.code, subItem.name, i == 0 ? groupName : "");
                    source.add(languageItemBean);

                    if (!distinctDataMap.containsKey(subItem.code)) {
                        distinctDataMap.put(subItem.code, languageItemBean);
                    }
                }
            }
        }
        return source;
    }

    private List<LanguageItemBean> getSelectedLanguageList(String selectLanguageStr) {
        List<LanguageItemBean> selectList = null;
        List<String> languageListString = StringUtil.splitKeywords(selectLanguageStr, ",");
        if (!LList.isEmpty(languageListString)) {
            selectList = new ArrayList<>();
            for (String item : languageListString) {
                if (TextUtils.isEmpty(item)) continue;
                long code = LText.getLong(item);
                if (distinctDataMap.containsKey(code)) {
                    LanguageItemBean language = distinctDataMap.get(code);
                    if (language != null) {
                        selectList.add(language);
                    }
                }
            }
        }
        return selectList;
    }

    private List<LevelBean> mockData2() {
        List<LevelBean> data = new ArrayList<>();
        LevelBean group0 = new LevelBean();
        group0.name = "根据你选择的国家/地区推荐";
        group0.subLevelModeList = new ArrayList<>();
        group0.subLevelModeList.add(new LevelBean(1, "简体中文"));
        group0.subLevelModeList.add(new LevelBean(2, "繁体中文"));
        group0.subLevelModeList.add(new LevelBean(3, "英语"));

        LevelBean group1 = new LevelBean();
        group1.name = "全部语言";
        group1.subLevelModeList = new ArrayList<>();
        group1.subLevelModeList.add(new LevelBean(1, "简体中文"));
        group1.subLevelModeList.add(new LevelBean(2, "繁体中文"));
        group1.subLevelModeList.add(new LevelBean(3, "英语"));
        group1.subLevelModeList.add(new LevelBean(4, "西班牙语"));
        group1.subLevelModeList.add(new LevelBean(5, "葡萄牙语"));
        group1.subLevelModeList.add(new LevelBean(6, "阿拉伯语"));
        group1.subLevelModeList.add(new LevelBean(7, "日语"));
        group1.subLevelModeList.add(new LevelBean(8, "韩语"));
        group1.subLevelModeList.add(new LevelBean(9, "法语"));
        group1.subLevelModeList.add(new LevelBean(10, "德语"));
        group1.subLevelModeList.add(new LevelBean(11, "俄语"));
        group1.subLevelModeList.add(new LevelBean(12, "瑞典语"));
        group1.subLevelModeList.add(new LevelBean(13, "芬兰语"));
        group1.subLevelModeList.add(new LevelBean(14, "挪威语"));
        group1.subLevelModeList.add(new LevelBean(15, "意大利语"));
        group1.subLevelModeList.add(new LevelBean(16, "丹麦语"));
        group1.subLevelModeList.add(new LevelBean(17, "土耳其语"));
        group1.subLevelModeList.add(new LevelBean(18, "泰国语"));
        group1.subLevelModeList.add(new LevelBean(19, "越南语"));
        group1.subLevelModeList.add(new LevelBean(20, "印度语"));
        group1.subLevelModeList.add(new LevelBean(21, "希伯来语"));

        data.add(group0);
        data.add(group1);

        return data;
    }


    public List<LanguageItemBean> getSearchResultDataList(String input) {
        List<LanguageItemBean> list = null;
        if (!TextUtils.isEmpty(input)) {
            list = new ArrayList<>();
            for (LanguageItemBean item : distinctDataMap.values()) {
                if (item == null || TextUtils.isEmpty(item.name)) continue;
                if (item.name.contains(input)) {
                    list.add(item);
                }
            }
        }
        return list;
    }
}
