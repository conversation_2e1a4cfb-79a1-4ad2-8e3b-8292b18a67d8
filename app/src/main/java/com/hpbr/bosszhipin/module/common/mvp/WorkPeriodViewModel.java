package com.hpbr.bosszhipin.module.common.mvp;

import android.app.Application;

import com.basedata.network.request.Get1121OverseasDurationRequest;
import com.basedata.network.response.Get1121OverseasDurationResponse;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class WorkPeriodViewModel extends BaseViewModel {

    public MutableLiveData<WorkPeriodModel> workPeriodListObserver = new MutableLiveData<>();

    public MutableLiveData<Integer> loadStateObserver = new MutableLiveData<>();

    public WorkPeriodViewModel(@NonNull Application application) {
        super(application);
    }

    public void loadData(long selectCode, int overseaType) {
        Get1121OverseasDurationRequest request = new Get1121OverseasDurationRequest(new ApiRequestCallback<Get1121OverseasDurationResponse>() {


            @Override
            public void onStart() {
                loadStateObserver.setValue(IRefreshState.STATE_LOADING);
            }

            @Override
            public void handleInChildThread(ApiData<Get1121OverseasDurationResponse> data) {
                Get1121OverseasDurationResponse resp = data.resp;
                if (resp != null) {
                    WorkPeriodModel model = new WorkPeriodModel();
                    LevelBean selectItem = null;
                    List<LevelBean> sourceData = resp.durationConfig;
                    if (!LList.isEmpty(sourceData)) {
                        for (LevelBean item : sourceData) {
                            if (item.code == selectCode) {
                                selectItem = item;
                                break;
                            }
                        }
                    }
                    model.source = sourceData;
                    model.selectItem = selectItem;
                    workPeriodListObserver.postValue(model);
                }
            }

            @Override
            public void onSuccess(ApiData<Get1121OverseasDurationResponse> data) {
                loadStateObserver.setValue(IRefreshState.STATE_COMPLETE);
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                showError(reason);
                loadStateObserver.setValue(IRefreshState.STATE_ERROR);
            }
        });
        request.type = getType(overseaType);
        HttpExecutor.execute(request);
    }

    private int getType(int overseaType) {
        int type = 0;
        if (UserManager.isBossRole()) {
            if (JobStatusChecker.isOverSeaChuChai(overseaType)) {
                type = 1;
            }
        } else {
            type = 2;
        }
        return type;
    }

    private List<LevelBean> mockData() {
        List<LevelBean> data = new ArrayList<>();
        data.add(new LevelBean(1, "1-3个月"));
        data.add(new LevelBean(2, "3-6个月"));
        data.add(new LevelBean(3, "6-12个月"));
        data.add(new LevelBean(4, "1年"));
        data.add(new LevelBean(5, "2年"));
        data.add(new LevelBean(6, "3年"));
        data.add(new LevelBean(7, "4年"));
        data.add(new LevelBean(8, "5年以上"));
        data.add(new LevelBean(9, "长期驻外"));
        return data;
    }
}
