package com.hpbr.bosszhipin.module.common.overseas;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.bean.LanguageItemBean;
import com.hpbr.bosszhipin.module.common.mvp.WorkLanguageViewModel;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class LanguageListAdapter extends BaseRvAdapter<LanguageItemBean, BaseViewHolder> {

    private final List<LanguageItemBean> selectItems = new ArrayList<>();

    public void setSelectItems(List<LanguageItemBean> mSelectItems) {
        if (LList.isEmpty(mSelectItems)) return;
        selectItems.addAll(mSelectItems);
    }

    public List<LanguageItemBean> getSelectItems() {
        return selectItems;
    }

    public LanguageListAdapter() {
        this(null);
    }

    public LanguageListAdapter(@Nullable List<LanguageItemBean> data) {
        super(R.layout.item_work_language, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LanguageItemBean item) {
        if (item == null) return;
        MTextView tvGroup = helper.getView(R.id.tv_group);
        if (TextUtils.isEmpty(item.groupName)) {
            tvGroup.setVisibility(View.GONE);
        } else {
            tvGroup.setVisibility(View.VISIBLE);
            tvGroup.setText(item.groupName);
        }
        MTextView tvLanguage = helper.getView(R.id.tv_language);
        tvLanguage.setText(item.name);

        ImageView ivCheck = helper.getView(R.id.iv_check);
        ivCheck.setVisibility(isItemSelected(item) >= 0 ? View.VISIBLE : View.GONE);
    }

    private int isItemSelected(@NonNull LanguageItemBean item) {
        int selectIndex = -1;
        if (!selectItems.isEmpty()) {
            int size = selectItems.size();
            for (int i = 0; i < size; i++) {
                LanguageItemBean selectItem = selectItems.get(i);
                if (selectItem.code == item.code) {
                    selectIndex = i;
                    break;
                }
            }
        }
        return selectIndex;
    }

    public void onItemSelectChanged(@NonNull LanguageItemBean item) {
        int selectIndex = isItemSelected(item);
        if (selectIndex >= 0) {
            selectItems.remove(selectIndex);
            notifyDataSetChanged();
        } else {
            if (selectItems.size() >= WorkLanguageViewModel.MAX_SELECTIONS) {
                ToastUtils.showText("最多可选" + WorkLanguageViewModel.MAX_SELECTIONS + "项");
                return;
            }
            selectItems.add(item);
            notifyDataSetChanged();
        }
    }
}
