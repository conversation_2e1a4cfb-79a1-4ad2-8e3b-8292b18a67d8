package com.hpbr.bosszhipin.module.common.overseas;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.common.bean.LanguageItemBean;
import com.hpbr.bosszhipin.module.common.mvp.LanguageModel;
import com.hpbr.bosszhipin.module.common.mvp.WorkLanguageViewModel;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module_boss_export.BossConst;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.error.ErrorReason;
import com.twl.ui.LeftFadingEdgeRecyclerView;
import com.twl.ui.ToastUtils;

import java.io.Serializable;
import java.util.List;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;
import zpui.lib.ui.statelayout.layout.ErrorSceneLayout;
import zpui.lib.ui.statelayout.layout.LoadingSceneLayout;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class LanguagePickerActivity extends BaseAwareActivity<WorkLanguageViewModel> {


    public static void jumpForResult(Context context, int requestCode, String countryString, String selectLanguageString) {
        jumpForResult(context, requestCode, countryString, selectLanguageString, 0);
    }

    public static void jumpForResult(Context context, int requestCode, String countryString, String selectLanguageString, int overseaType) {
        jumpForResult(context, requestCode, countryString, selectLanguageString, overseaType,"");
    }

    public static void jumpForResult(Context context, int requestCode, String countryString, String selectLanguageString, int overseaType,String customTitle) {
        Intent intent = new Intent(context, LanguagePickerActivity.class);
        intent.putExtra(Constants.DATA_STRING, countryString);
        intent.putExtra(Constants.DATA_STRING2, selectLanguageString);
        intent.putExtra(BossConst.OVERSEAS_TYPE, overseaType);
        intent.putExtra(BossConst.OVERSEAS_LANGUAGE_TITLE, customTitle);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    private MTextView tvSelectTitle;
    private LanguageSearchMatchView languageSearchMatchView;

    private ConstraintLayout clSelectionPanel;
    private ZPUIRoundButton btnSave;

    private LanguageListAdapter languageListAdapter;
    private LanguageSelectionAdapter languageSelectionAdapter;
    private LanguageSearchItemAdapter languageSearchItemAdapter;

    private ZPUIStateLayoutManager stateLayoutManager;

    private String countryString;
    private String selectLanguageString;
    private int overseaType;
    private String customTitle;
    @Override
    protected int contentLayout() {
        return R.layout.activity_work_language_picker;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        overseaType = getIntent().getIntExtra(BossConst.OVERSEAS_TYPE, 0);
        countryString = getIntent().getStringExtra(Constants.DATA_STRING);
        selectLanguageString = getIntent().getStringExtra(Constants.DATA_STRING2);
        customTitle=getIntent().getStringExtra(BossConst.OVERSEAS_LANGUAGE_TITLE);
        startObserver();
        initViews();
        mViewModel.loadData(countryString, selectLanguageString);
    }

    private void startObserver() {
        mViewModel.mLoading.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (s == null) {
                    dismissProgressDialog();
                } else {
                    showProgressDialog();
                }
            }
        });
        mViewModel.mError.observe(this, new Observer<ErrorReason>() {
            @Override
            public void onChanged(ErrorReason errorReason) {
                if (errorReason == null) {
                    return;
                }
                ToastUtils.showText(errorReason.getErrReason());
            }
        });
        mViewModel.loadStateObserver.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer state) {
                if (state == null) return;
                switch (state) {
                    case IRefreshState.STATE_LOADING:
                        stateLayoutManager.showLoadingScene();
                        break;
                    case IRefreshState.STATE_ERROR:
                        stateLayoutManager.showErrorScene();
                        break;
                    case IRefreshState.STATE_COMPLETE:
                        stateLayoutManager.dismiss();
                        break;
                    default:
                        break;
                }
            }
        });
        mViewModel.languageListObserver.observe(this, new Observer<LanguageModel>() {
            @Override
            public void onChanged(LanguageModel model) {
                if (model == null) return;

                if (languageListAdapter != null) {
                    languageListAdapter.setSelectItems(model.selectItems);
                    languageListAdapter.setNewData(model.source);
                }
                if (languageSelectionAdapter != null) {
                    languageSelectionAdapter.setNewData(model.selectItems);
                }
                refreshSelections();
                refreshButtonActions();
            }
        });
    }

    private void initViews() {
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(LanguagePickerActivity.this);
            }
        });

        MTextView tvPageTitle = findViewById(R.id.tv_page_title);
        if (!TextUtils.isEmpty(customTitle)){//自定义标题
            tvPageTitle.setText(customTitle);
        }else {
            if (UserManager.isBossRole()) {
                if (JobStatusChecker.isOverSeaChuChai(overseaType)) {
                    tvPageTitle.setText(R.string.string_business_trip_language_boss_title);
                } else {
                    tvPageTitle.setText(R.string.string_work_overseas_language_boss_title);
                }
            } else {
                tvPageTitle.setText(R.string.string_work_overseas_language_geek_title);
            }
        }


        AppBarLayout appBarLayout1 = findViewById(R.id.appBarLayout);
        appBarLayout1.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            boolean collapsed = absOffset >= appBarLayout.getTotalScrollRange();
            //noinspection deprecation
            titleView.setDividerVisibility(collapsed ? View.VISIBLE : View.INVISIBLE);
            titleView.setTitle(collapsed ? tvPageTitle.getText() : "");
        });

        clSelectionPanel = findViewById(R.id.cl_selection_panel);
        tvSelectTitle = findViewById(R.id.tv_select_title);

        RecyclerView rvList = findViewById(R.id.rv_list);
        languageListAdapter = new LanguageListAdapter();
        languageListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                LanguageItemBean item = (LanguageItemBean) adapter.getItem(position);
                if (item == null) return;
                dataChangedByActions(item);
            }
        });
        rvList.setAdapter(languageListAdapter);

        LeftFadingEdgeRecyclerView rvSelections = findViewById(R.id.rv_selections);
        languageSelectionAdapter = new LanguageSelectionAdapter();
        languageSelectionAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                LanguageItemBean item = (LanguageItemBean) adapter.getItem(position);
                if (item == null) return;
                languageListAdapter.onItemSelectChanged(item);
                refreshSelections();
                refreshButtonActions();
            }
        });
        rvSelections.setAdapter(languageSelectionAdapter);

        RecyclerView rvSearchResult = findViewById(R.id.rv_search_result);
        languageSearchItemAdapter = new LanguageSearchItemAdapter();
        languageSearchItemAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                LanguageItemBean item = (LanguageItemBean) adapter.getItem(position);
                if (item == null) return;
                dataChangedByActions(item);
                languageSearchMatchView.clearInput();
            }
        });
        rvSearchResult.setAdapter(languageSearchItemAdapter);


        languageSearchMatchView = findViewById(R.id.language_search_view);
        languageSearchMatchView.setOnTextInputChangeListener(new LanguageSearchMatchView.OnTextInputChangeListener() {
            @Override
            public void onTextChange(String input) {
                if (TextUtils.isEmpty(input)) {
                    rvSearchResult.setVisibility(View.GONE);
                } else {
                    rvSearchResult.setVisibility(View.VISIBLE);
                    refreshSearchResultAdapter(input);
                }
            }
        });


        stateLayoutManager = new ZPUIStateLayoutManager(this, rvList);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_PROGRESSBAR)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517));
        stateLayoutManager.getErrorLayout()
                .setType(ErrorSceneLayout.ERROR_SCENE_TYPE.ERROR_NETWORK)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517))
                .setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        mViewModel.loadData(countryString, selectLanguageString);
                    }
                });
        findViewById(R.id.btn_cancel).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(LanguagePickerActivity.this);
            }
        });

        btnSave = findViewById(R.id.btn_save);
        btnSave.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                List<LanguageItemBean> selectItems = languageListAdapter.getSelectItems();
                Intent intent = new Intent();
                intent.putExtra(Constants.DATA_ENTITY, (Serializable) selectItems);
                setResult(RESULT_OK, intent);
                AppUtil.finishActivity(LanguagePickerActivity.this);
            }
        });
    }

    /**
     * 通过操作改变数据列表选中变化
     *
     * @param item
     */
    private void dataChangedByActions(@NonNull LanguageItemBean item) {
        if (languageListAdapter != null) {
            languageListAdapter.onItemSelectChanged(item);
        }
        refreshSelections();
        refreshButtonActions();
    }

    private void refreshSelections() {
        if (languageSelectionAdapter != null) {
            List<LanguageItemBean> selectItems = languageListAdapter.getSelectItems();
            languageSelectionAdapter.setNewData(selectItems);
            tvSelectTitle.setText(Html.fromHtml(getString(R.string.string_has_selections, selectItems.size(), WorkLanguageViewModel.MAX_SELECTIONS)));
            clSelectionPanel.setVisibility(selectItems.isEmpty() ? View.GONE : View.VISIBLE);
        }
    }

    private void refreshButtonActions() {
        boolean isActionEnable = false;
        if (languageListAdapter != null) {
            List<LanguageItemBean> selectItems = languageListAdapter.getSelectItems();
            if (!selectItems.isEmpty()) {
                isActionEnable = true;
            }
        }
        btnSave.setEnabled(isActionEnable);
    }

    private void refreshSearchResultAdapter(String input) {
        List<LanguageItemBean> resultList = mViewModel.getSearchResultDataList(input);
        if (languageSearchItemAdapter != null) {
            languageSearchItemAdapter.setNewData(resultList);
        }
    }
}
