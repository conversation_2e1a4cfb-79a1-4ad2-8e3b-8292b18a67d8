package com.hpbr.bosszhipin.module.common.overseas;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.bean.LanguageItemBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: zhouyou
 * Date: 2023/10/12
 */
public class LanguageSearchItemAdapter extends BaseRvAdapter<LanguageItemBean, BaseViewHolder> {

    public LanguageSearchItemAdapter() {
        this(null);
    }

    public LanguageSearchItemAdapter(@Nullable List<LanguageItemBean> data) {
        super(R.layout.item_language_search_result, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LanguageItemBean item) {
        if (item == null) return;
        MTextView tvLanguage = helper.getView(R.id.tv_language);
        tvLanguage.setText(item.name);
    }
}
