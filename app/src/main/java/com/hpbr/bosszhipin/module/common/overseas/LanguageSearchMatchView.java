package com.hpbr.bosszhipin.module.common.overseas;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class LanguageSearchMatchView extends FrameLayout {

    private MEditText etSearch;
    private ImageView ivClear;
    private OnTextInputChangeListener onTextInputChangeListener;

    public void setOnTextInputChangeListener(OnTextInputChangeListener onTextInputChangeListener) {
        this.onTextInputChangeListener = onTextInputChangeListener;
    }

    public LanguageSearchMatchView(@NonNull Context context) {
        this(context, null);
    }

    public LanguageSearchMatchView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public LanguageSearchMatchView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.view_language_search, this);
        etSearch = view.findViewById(R.id.et_search);
        ivClear = view.findViewById(R.id.iv_clear);
        ivClear.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                etSearch.setText("");
            }
        });
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s == null) return;
                String text = s.toString().trim();
                ivClear.setVisibility(LText.empty(text) ? GONE : VISIBLE);
                if (onTextInputChangeListener != null) {
                    onTextInputChangeListener.onTextChange(text);
                }
            }
        });
    }

    public void clearInput() {
        etSearch.setText("");
        etSearch.clearFocus();
        AppUtil.hideSoftInput(getContext(), etSearch);
    }

    public interface OnTextInputChangeListener {

        void onTextChange(String input);
    }
}
