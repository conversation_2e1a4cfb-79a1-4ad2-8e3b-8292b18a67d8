package com.hpbr.bosszhipin.module.common.overseas;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.bean.LanguageItemBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Author: zhouyou
 * Date: 2023/10/12
 */
public class LanguageSelectionAdapter extends BaseRvAdapter<LanguageItemBean, BaseViewHolder> {

    public LanguageSelectionAdapter() {
        this(null);
    }
    public LanguageSelectionAdapter(@Nullable List<LanguageItemBean> data) {
        super(R.layout.item_language_selection, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LanguageItemBean item) {
        if (item == null) return;
        ZPUIRoundButton btnLanguage = helper.getView(R.id.btn_language);
        btnLanguage.setText(item.name);
    }
}
