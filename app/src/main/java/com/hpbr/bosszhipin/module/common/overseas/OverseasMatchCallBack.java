package com.hpbr.bosszhipin.module.common.overseas;

import com.hpbr.bosszhipin.module.my.entity.LevelBean;

/**
 * 搜索结果回调
 */
public interface OverseasMatchCallBack {
    /**
     * @param isSearchEmpty 是否搜索到内容
     */
    void onSearchMatchResult(boolean isSearchEmpty);

    /**
     * @param secondLevel 点击Item回调
     */
    void onItemClickListener(LevelBean secondLevel, int position);

    /**
     * 控制标题明文是否显示
     */
    void setNoteVisibility(boolean isShow);

    default void setNoteVisibility(boolean isShow, boolean showTipBar) {

    }

    /**
     * 职位上报
     */
    void onJobReport();

}
