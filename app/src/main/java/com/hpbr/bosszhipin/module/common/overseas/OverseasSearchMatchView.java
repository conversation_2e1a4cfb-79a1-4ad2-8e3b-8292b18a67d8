package com.hpbr.bosszhipin.module.common.overseas;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.utils.TextSearchLimitHelper;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnSuggestCallback;
import com.hpbr.bosszhipin.views.OnTextChangeListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.utils.DisplayHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OverseasSearchMatchView extends RelativeLayout implements AdapterView.OnItemClickListener, View.OnClickListener, TextSearchLimitHelper.TextChangeDelayCallBack {

    private MEditText etSearch;
    private ListView lvSearch;
    private OverseasSugAdapter adapter;
    private OverseasMatchCallBack matchCallBack;

    private TextSearchLimitHelper searchLimitHelper;

    /**
     * 是否需要展示nlp不匹配的view
     */
    private ImageView mClear;
    private MTextView inputCountTv;

    @NonNull
    private final InputUtils inputUtils12;

    private OnTextChangeListener onTextChangeListener;

    private final @NonNull List<LevelBean> overseasList = new ArrayList<>();
    private final Map<Long, LevelBean> overseasMap = new ArrayMap<>();

    public void setDataSource(@NonNull List<LevelBean> overseasList) {
        this.overseasList.clear();
        this.overseasList.addAll(overseasList);

        for (LevelBean firstLevel : overseasList) {
            if (firstLevel == null) continue;
            List<LevelBean> subLevelModeList = firstLevel.subLevelModeList;
            if (LList.hasElement(subLevelModeList)) {
                for (LevelBean secondLevel : subLevelModeList) {
                    if (secondLevel == null) continue;
                    secondLevel.setParentLevel(firstLevel);
                    overseasMap.put(secondLevel.code, secondLevel);
                }
            }
        }
    }

    public void setMatchCallBack(OverseasMatchCallBack matchCallBack) {
        this.matchCallBack = matchCallBack;
    }

    public OverseasSearchMatchView(Context context) {
        this(context, null);
    }

    public OverseasSearchMatchView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OverseasSearchMatchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
        inputUtils12 = new InputUtils(context, 12);
    }

    @SuppressLint("InflateParams")
    private void init(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.view_overseas_search, this, false);
        etSearch = view.findViewById(R.id.et_search);
        mClear = view.findViewById(R.id.iv_clear);
        inputCountTv = view.findViewById(R.id.input_count_tv);
        mClear.setOnClickListener(this);
        searchLimitHelper = new TextSearchLimitHelper(this);
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s == null) return;
                String text = s.toString().trim();
                searchLimitHelper.sendHandler(text);
                mClear.setVisibility(LText.empty(text) ? GONE : VISIBLE);

                inputUtils12.checkInputCount(inputCountTv, text);
                if (onTextChangeListener != null) onTextChangeListener.onChange(text);
            }
        });

        adapter = new OverseasSugAdapter(getContext());

        addView(view);
    }

    public void enableInputTextCount(boolean enableCount) {
        inputCountTv.setVisibility(enableCount ? VISIBLE : GONE);
        // 1104.290 根据是否展示字数, 确认文本最大长度, 和清除按钮位置
        int paddingStart = DisplayHelper.dp2px(getContext(), 15);
        int paddingEnd = DisplayHelper.dp2px(getContext(), enableCount ? 78 : 38);
        etSearch.setPadding(paddingStart, 0, paddingEnd, 0);
        int marginEnd = DisplayHelper.dp2px(getContext(), 12);
        mClear.setPadding(0, 0, enableCount ? marginEnd : 0, 0);
    }

    public void attachCustomList(@NonNull ListView listView) {
        this.lvSearch = listView;

        this.lvSearch.setOnItemClickListener(this);

        this.lvSearch.setAdapter(adapter);
    }

    public void showKeyBoard() {
        //显示键盘
        AppUtil.showSoftInput(getContext(), etSearch);
    }

    public void clear() {
        if (etSearch != null) {
            etSearch.setText("");
        }
    }

    @Override
    public void onTextDelayChanged(String text) {
        if (LText.empty(text)) {
            lvSearch.setVisibility(View.GONE);
            if (matchCallBack != null) {
                matchCallBack.onSearchMatchResult(true);
                matchCallBack.setNoteVisibility(false);
            }
            if (onSuggestCallback != null) {
                onSuggestCallback.onSuggestListener(true);
            }
        } else {
            lvSearch.setVisibility(View.VISIBLE);
            getMatchList(text);
        }
    }

    private OnSuggestCallback onSuggestCallback;

    public void setOnSuggestCallback(OnSuggestCallback onSuggestCallback) {
        this.onSuggestCallback = onSuggestCallback;
    }

    public void setOnTextChangeListener(OnTextChangeListener onTextChangeListener) {
        this.onTextChangeListener = onTextChangeListener;

    }

    /**
     * 若有nlp推荐词时，则加入底部
     *
     * @param suggestList 推荐词数据源
     */
    private void refreshAdapter(List<LevelBean> suggestList) {
        if (adapter != null) {
            adapter.setData(suggestList);
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        LevelBean secondLevel = (LevelBean) parent.getItemAtPosition(position);
        if (secondLevel != null && matchCallBack != null) {
            AppUtil.hideSoftInput(getContext(), etSearch);
            matchCallBack.onItemClickListener(secondLevel, position);
        }
    }

    public boolean isNlpForecast() {
        return false;
    }

    public boolean isNLPRecommend() {
        return false;
    }

    public void clearText() {
        etSearch.setText("");
        if (matchCallBack != null) {
            matchCallBack.onSearchMatchResult(true);
            matchCallBack.setNoteVisibility(false);
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_clear) {
            etSearch.setText("");
            if (matchCallBack != null) {
                matchCallBack.onSearchMatchResult(true);
                matchCallBack.setNoteVisibility(false);
            }

        }
    }

    private void getMatchList(String searchTxt) {
        // TODO: 2023/10/13 区分 B/C
        // 直接搜索本地数据（二级类名称）
        List<LevelBean> queryResult = new ArrayList<>();

        for (Long code : overseasMap.keySet()) {
            LevelBean item = overseasMap.get(code);
            if (item == null || TextUtils.isEmpty(item.name)) continue;
            if (item.name.contains(searchTxt)) {
                queryResult.add(item);
            }
        }
        refreshAdapter(queryResult);
    }

    public String getInputString() {
        return etSearch.getText().toString().trim();
    }

    public void setInputString(String inputString) {
        etSearch.setTextWithSelection(inputString);
    }

}
