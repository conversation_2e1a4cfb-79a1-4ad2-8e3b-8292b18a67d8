package com.hpbr.bosszhipin.module.common.overseas;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;

import java.util.List;

public class OverseasSelectionAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

    public OverseasSelectionAdapter() {
        this(null);
    }

    public OverseasSelectionAdapter(@Nullable List<LevelBean> data) {
        super(R.layout.item_common_selected_with_close, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        if (item == null) {
            return;
        }
        MTextView tvWord = helper.getView(R.id.tv_title);
        String positionName = item.name;
        String reportPositionName = item.reportPositionName;
        tvWord.setText(!LText.empty(reportPositionName) ? reportPositionName : positionName);
    }
}
