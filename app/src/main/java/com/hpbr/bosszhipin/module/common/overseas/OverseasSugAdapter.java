package com.hpbr.bosszhipin.module.common.overseas;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;

public class OverseasSugAdapter extends LBaseAdapter<LevelBean> {

    public OverseasSugAdapter(@NonNull Context context) {
        super(context);
    }

    @SuppressLint("InflateParams")
    @Override
    public View getView(int position, View convertView, LevelBean item, LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.view_overseas_sug_item, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (item != null) {
            holder.tvTitle.setText(item.name);
        }

        return convertView;
    }

    static class ViewHolder {
        MTextView tvTitle;

        public ViewHolder(View convertView) {
            tvTitle = convertView.findViewById(R.id.tv_title);
        }
    }

}
