package com.hpbr.bosszhipin.module.common.overseas;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.decoration.GridSpacingItemDecoration;
import com.hpbr.bosszhipin.views.threelevel.OnPositionLevelSelectListener;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

public class OverseasTreeView extends FrameLayout {

    private static final String TAG = "OverseasTreeView";

    private RecyclerView rvFirstLevel;
    private RecyclerView rvSecondLevel;
    private FirstLevelAdapter firstLevelAdapter;
    private SecondLevelAdapter secondLevelAdapter;
    private View vBottom;
    @NonNull
    private final List<LevelBean> sourceData = new ArrayList<>();
    private List<LevelBean> selectItems;
    private OnPositionLevelSelectListener listener;
    @NonNull
    private final RecyclerView.OnScrollListener onSecondLevelScrollListener = new RecyclerView.OnScrollListener() {
        private int preFp = -1;

        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            RecyclerView.LayoutManager lm = recyclerView.getLayoutManager();
            if (lm instanceof LinearLayoutManager) {
                LinearLayoutManager llm = (LinearLayoutManager) lm;
                int fvip = llm.findFirstVisibleItemPosition();
                LevelBean fvil = secondLevelAdapter.getItem(fvip);
                if (fvil != null) {
                    LevelBean selectItem = firstLevelAdapter.getSelectItem();
                    if (selectItem != fvil) {
                        firstLevelAdapter.setSelectItem(fvil);
                        List<LevelBean> firstLevelAdapterData = firstLevelAdapter.getData();
                        if (!LList.isEmpty(firstLevelAdapterData) && vBottom != null) {
                            int selectPosition = firstLevelAdapterData.indexOf(selectItem);
                            if (selectPosition == firstLevelAdapterData.size() - 2) {
                                vBottom.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.basic_bg_6_tr_corner_fff5f5f5));
                            } else {
                                vBottom.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.basic_bg_0_corner_fff5f5f5));
                            }
                        }

                        firstLevelAdapter.notifyDataSetChanged();

                        secondLevelAdapter.setFirstLevelItem(fvil);

                        int fp = firstLevelAdapterData.indexOf(fvil);
                        if (fp != preFp) {
                            L.debug(TAG, "preFp: %d, fp: %d", preFp, fp);
                            AnalyticsFactory.create().action("expect-position-code-slide").build();
                        }

                        rvFirstLevel.scrollToPosition(preFp = fp);
                    }
                }
            }
        }
    };

    public OverseasTreeView(@NonNull Context context) {
        this(context, null);
    }

    public OverseasTreeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OverseasTreeView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initViews();
    }

    private void initViews() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_overseas_tree, this);
        initFirstLevel(view);
        initSecondLevel(view);
    }

    private void initFirstLevel(View v) {
        vBottom = findViewById(R.id.view_bottom);
        rvFirstLevel = v.findViewById(R.id.rv_first_level);
        firstLevelAdapter = new FirstLevelAdapter();
        firstLevelAdapter.setOnItemClickListener((adapter, view, position) -> {
            LevelBean selectItem = (LevelBean) adapter.getItem(position);
            if (selectItem != null) {
                refreshAdapter2(selectItem);
                List<LevelBean> firstItems = firstLevelAdapter.getData();
                if (!LList.isEmpty(firstItems) ) {
                    View vBottom = findViewById(R.id.view_bottom);
                    if (position == firstItems.size() - 1) {
                        vBottom.setBackground(ContextCompat.getDrawable(vBottom.getContext(), R.drawable.basic_bg_6_tr_corner_fff5f5f5));
                    } else {
                        vBottom.setBackground(ContextCompat.getDrawable(vBottom.getContext(), R.drawable.basic_bg_0_corner_fff5f5f5));
                    }
                }
            }
        });
        rvFirstLevel.setAdapter(firstLevelAdapter);
    }

    private void initSecondLevel(View view) {
        rvSecondLevel = view.findViewById(R.id.rv_second_level);
        secondLevelAdapter = new SecondLevelAdapter();
        rvSecondLevel.setAdapter(secondLevelAdapter);
    }

    /**
     * 设置数据源并刷新
     */
    public void setAdapter(List<LevelBean> data, List<LevelBean> selectList, OnPositionLevelSelectListener listener) {
        this.listener = listener;
        sourceData.clear();
        if (!LList.isEmpty(data)) {
            sourceData.addAll(data);
        }
        selectItems = selectList;
        refreshAdapter();
    }

    private void refreshAdapter() {
        LevelBean selectFirstLevel = LList.getElement(sourceData, 0);
        if (firstLevelAdapter != null) {
            firstLevelAdapter.setSelectItem(selectFirstLevel);
            firstLevelAdapter.setNewData(sourceData);
        }

        if (selectFirstLevel != null) {
            if (secondLevelAdapter != null) {
                secondLevelAdapter.setFirstLevelItem(selectFirstLevel);
                secondLevelAdapter.setSelectList(selectItems);
                secondLevelAdapter.setOnPositionLevelSelectListener(listener);

                rvSecondLevel.addOnScrollListener(onSecondLevelScrollListener);
                secondLevelAdapter.setNewData(sourceData);
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private void refreshAdapter2(@NonNull LevelBean selectFirstLevel) {
        if (firstLevelAdapter != null) {
            firstLevelAdapter.setSelectItem(selectFirstLevel);
            firstLevelAdapter.notifyDataSetChanged();
        }

        if (secondLevelAdapter != null) {
            // 更新选择的一级类
            secondLevelAdapter.setFirstLevelItem(selectFirstLevel);
            List<LevelBean> data = secondLevelAdapter.getData();
            if (LList.hasElement(data)) {
                RecyclerView.LayoutManager lm = rvSecondLevel.getLayoutManager();
                if (lm instanceof LinearLayoutManager) {
                    rvSecondLevel.removeOnScrollListener(onSecondLevelScrollListener);
                    ((LinearLayoutManager) lm).scrollToPositionWithOffset(data.indexOf(selectFirstLevel), 0);
                    rvSecondLevel.post(() -> rvSecondLevel.addOnScrollListener(onSecondLevelScrollListener));
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    public void notifyDataSetChanged() {
        if (firstLevelAdapter != null) {
            firstLevelAdapter.notifyDataSetChanged();
        }
        if (secondLevelAdapter != null) {
            secondLevelAdapter.notifyDataSetChanged();
        }
    }

    static class FirstLevelAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

        @Nullable
        private LevelBean selectItem;

        FirstLevelAdapter() {
            this(null);
        }

        public void setSelectItem(@Nullable LevelBean selectItem) {
            this.selectItem = selectItem;
        }

        @Nullable
        public LevelBean getSelectItem() {
            return selectItem;
        }

        FirstLevelAdapter(@Nullable List<LevelBean> data) {
            super(R.layout.item_overseas_tree_first_level, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
            if (item == null) {
                return;
            }
            MTextView tvText = helper.getView(R.id.tv_text);
            shouldEnableDangerous(item, tvText);

            boolean itemSelect = isItemSelect(item);
            tvText.setTextColor(ContextCompat.getColor(mContext, itemSelect ? R.color.app_green_dark : R.color.text_c11));
            View vIndex = helper.getView(R.id.view_indicator);
            vIndex.setVisibility(itemSelect ? VISIBLE : GONE);
            List<LevelBean> firstItems = getData();
            if (itemSelect) {
                tvText.setTextColor(ContextCompat.getColor(helper.itemView.getContext(), R.color.color_BOSS7));
                helper.itemView.setBackgroundColor(ContextCompat.getColor(helper.itemView.getContext(), R.color.color_FFFFFFFF_FF151517));
            } else {
                if (!LList.isEmpty(firstItems) && selectItem != null) {
                    int curPosition = firstItems.indexOf(item);
                    int selectPosition = firstItems.indexOf(selectItem);
                    if (curPosition == 0) {
                        if (selectPosition == 1) {
                            helper.itemView.setBackground(ContextCompat.getDrawable(helper.itemView.getContext(), R.drawable.basic_bg_6_tr_br_corner_fff5f5f5));
                        } else {
                            helper.itemView.setBackground(ContextCompat.getDrawable(helper.itemView.getContext(), R.drawable.basic_bg_6_tr_corner_fff5f5f5));
                        }
                    } else {
                        if (curPosition == selectPosition - 1) {
                            helper.itemView.setBackground(ContextCompat.getDrawable(helper.itemView.getContext(), R.drawable.basic_bg_6_br_corner_fff5f5f5));
                        } else if (curPosition == selectPosition + 1) {
                            helper.itemView.setBackground(ContextCompat.getDrawable(helper.itemView.getContext(), R.drawable.basic_bg_6_tr_corner_fff5f5f5));
                        } else {
                            helper.itemView.setBackground(ContextCompat.getDrawable(helper.itemView.getContext(), R.drawable.basic_bg_0_corner_fff5f5f5));
                        }
                    }
                }else {
                    tvText.setTextColor(ContextCompat.getColor(helper.itemView.getContext(), R.color.color_FF292929_FFD2D2D6));
                    helper.itemView.setBackgroundColor(ContextCompat.getColor(helper.itemView.getContext(),  R.color.color_FFF5F5F5_FF262629 ));

                }
            }
        }

        private boolean isItemSelect(@NonNull LevelBean item) {
            return selectItem != null && selectItem.code == item.code;
        }
    }

    private static void shouldEnableDangerous(LevelBean item, MTextView tvText) {
        boolean enableDangerous = false;
        if (item.getTagObj() instanceof Integer) {
            enableDangerous = ((Integer) item.getTagObj()) == 1;
        }
        String name = item.name;
        SpannableStringBuilder builder = new SpannableStringBuilder(name);
        if (enableDangerous) {
            builder.append("*");
            int length = name.length();
            builder.setSpan(new ForegroundColorSpan(ContextCompat.getColor(tvText.getContext(), R.color.red)), length, length + 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            tvText.setText(builder);
        } else {
            tvText.setText(name);
        }
    }

    static class SecondLevelAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

        private LevelBean firstLevel;
        private OnPositionLevelSelectListener listener;
        private List<LevelBean> selectList;

        SecondLevelAdapter() {
            this(null);
        }

        SecondLevelAdapter(@Nullable List<LevelBean> data) {
            super(R.layout.item_overseas_tree_second_level, data);
        }

        void setOnPositionLevelSelectListener(OnPositionLevelSelectListener listener) {
            this.listener = listener;
        }

        void setFirstLevelItem(LevelBean firstLevelItem) {
            firstLevel = firstLevelItem;
        }

        void setSelectList(List<LevelBean> mSelectList) {
            selectList = mSelectList;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean secondItem) {
            if (secondItem == null || LList.isEmpty(secondItem.subLevelModeList)) {
                return;
            }
            MTextView tv_title = helper.getView(R.id.tv_title);
            tv_title.setText(secondItem.name, View.GONE);

            RecyclerView rvWord = helper.getView(R.id.rv_word);
            GridLayoutManager layoutManager = (GridLayoutManager) rvWord.getLayoutManager();
            if (layoutManager == null) {

                GridSpacingItemDecoration decoration = new GridSpacingItemDecoration(2, ZPUIDisplayHelper.dp2px(mContext, 8), false);
                rvWord.addItemDecoration(decoration);

                layoutManager = new GridLayoutManager(mContext, 2);
                rvWord.setLayoutManager(layoutManager);
            }

            ThirdLevelAdapter adapter = new ThirdLevelAdapter(secondItem.subLevelModeList, selectList);
            rvWord.setAdapter(adapter);
            adapter.setOnItemClickListener((a, view, position) -> {
                LevelBean thirdLevel = (LevelBean) a.getItem(position);
                if (thirdLevel != null) {

                    boolean isDangerCountry = false;
                    if (thirdLevel.getTagObj() instanceof Integer) {
                        isDangerCountry = ((Integer) thirdLevel.getTagObj()) == 1;
                    }
                    if (isDangerCountry) {
                        ToastUtils.showText("暂不支持选择该国家");
                        return;
                    }
                    if (listener != null) {
                        listener.onThirdLevelSelect(firstLevel, secondItem, thirdLevel);
                    }
                }
            });
        }
    }

    static class ThirdLevelAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {
        private final List<LevelBean> selectList;

        ThirdLevelAdapter(@Nullable List<LevelBean> data, List<LevelBean> selectList) {
            super(R.layout.item_overseas_tree_third_level, data);
            this.selectList = selectList;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, LevelBean thirdLevel) {
            if (thirdLevel == null) {
                return;
            }
            boolean isDangerCountry = false;
            if (thirdLevel.getTagObj() instanceof Integer) {
                isDangerCountry = ((Integer) thirdLevel.getTagObj()) == 1;
            }

            LinearLayout llWord = helper.getView(R.id.ll_word);
            MTextView tvWord = helper.getView(R.id.tv_word);
            MTextView tvPositionCount = helper.getView(R.id.tv_position_count);

            int backgroundColor;
            int textColor;

            int strokeWidth;
            int strokeColor;

            if (isDangerCountry) {
                thirdLevel.setChecked(false);
                textColor = ContextCompat.getColor(mContext, R.color.text_c5);
                backgroundColor = ContextCompat.getColor(mContext, R.color.color_FFF5F5F5_FF262629);
                strokeWidth = 0;
                strokeColor = 0;
            } else {
                if (!isItemSelected(thirdLevel)) {
                    thirdLevel.setChecked(false);
                    textColor = ContextCompat.getColor(mContext, R.color.text_c11);
                    backgroundColor = ContextCompat.getColor(mContext, R.color.color_FFF5F5F5_FF262629);
                    strokeWidth = 0;
                    strokeColor = 0;
                } else {
                    thirdLevel.setChecked(true);
                    textColor = ContextCompat.getColor(mContext, R.color.app_green_dark);
                    backgroundColor = ContextCompat.getColor(mContext, R.color.color_3315B3B3);
                    strokeWidth = ZPUIDisplayHelper.dp2px(mContext, 1);
                    strokeColor = ContextCompat.getColor(mContext, R.color.color_FF48C7C7);
                }
            }

            tvWord.setTextColor(textColor);
            tvPositionCount.setTextColor(textColor);
            tvWord.setText(thirdLevel.name);

            GradientDrawable drawable = new GradientDrawable();
            drawable.setCornerRadius(ZPUIDisplayHelper.dp2px(mContext, 6));
            drawable.setColor(backgroundColor);
            drawable.setStroke(strokeWidth, strokeColor);
            llWord.setBackground(drawable);
        }

        private boolean isItemSelected(@NonNull LevelBean thirdLevel) {
            boolean isSelected = false;
            if (!LList.isEmpty(selectList)) {
                for (LevelBean item : selectList) {
                    if (item == null) {
                        continue;
                    }
                    if (item.code == thirdLevel.code) {
                        isSelected = true;
                        break;
                    }
                }
            }
            return isSelected;
        }
    }

    public static void complementSelectionName(@NonNull List<LevelBean> positionList, @NonNull ArrayList<LevelBean> selectedMultiPositions) {
        if (!LList.isEmpty(selectedMultiPositions) && !LList.isEmpty(positionList)) {
            for (LevelBean select : selectedMultiPositions) {
                for (LevelBean source : positionList) {
                    if (select == null || source == null) {
                        continue;
                    }
                    List<LevelBean> subLevelModeList = source.subLevelModeList;
                    if (subLevelModeList != null && subLevelModeList.size() > 0) {
                        for (LevelBean subSource : subLevelModeList) {

                            if (select.code == subSource.code) {
                                select.name = subSource.name; // 补全 name
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

}
