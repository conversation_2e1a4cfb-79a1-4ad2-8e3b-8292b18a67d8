package com.hpbr.bosszhipin.module.common.overseas;

import android.view.View;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class OverseasWorkPeriodListAdapter extends BaseRvAdapter<LevelBean, BaseViewHolder> {

    private LevelBean selectItem;

    public void setSelectItem(LevelBean mSelectItem) {
        selectItem = mSelectItem;
    }

    public LevelBean getSelectItem() {
        return selectItem;
    }

    public OverseasWorkPeriodListAdapter() {
        this(null);
    }

    public OverseasWorkPeriodListAdapter(@Nullable List<LevelBean> data) {
        super(R.layout.item_work_period, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, LevelBean item) {
        if (item == null) return;
        MTextView tvLanguage = helper.getView(R.id.tv_work_period);
        tvLanguage.setText(item.name);

        ImageView ivCheck = helper.getView(R.id.iv_check);
        ivCheck.setVisibility(isItemSelected(item) ? View.VISIBLE : View.GONE);
    }

    private boolean isItemSelected(@NonNull LevelBean item) {
        return selectItem != null && selectItem.code == item.code;
    }
}
