package com.hpbr.bosszhipin.module.common.overseas;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.common.mvp.WorkPeriodModel;
import com.hpbr.bosszhipin.module.common.mvp.WorkPeriodViewModel;
import com.hpbr.bosszhipin.module.main.entity.JobStatusChecker;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_boss_export.BossConst;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;
import zpui.lib.ui.statelayout.layout.ErrorSceneLayout;
import zpui.lib.ui.statelayout.layout.LoadingSceneLayout;

/**
 * Author: zhouyou
 * Date: 2023/10/11
 */
public class OverseasWorkPeriodPickerActivity extends BaseAwareActivity<WorkPeriodViewModel> {

    public static void jumpForResult(Context context, int requestCode, LevelBean selectItem) {
        jumpForResult(context, requestCode, selectItem, 0);
    }

    public static void jumpForResult(Context context, int requestCode, LevelBean selectItem, int overseaType) {
        Intent intent = new Intent(context, OverseasWorkPeriodPickerActivity.class);
        intent.putExtra(Constants.DATA_ENTITY, selectItem);
        intent.putExtra(BossConst.OVERSEAS_TYPE, overseaType);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    private ZPUIRoundButton btnSave;
    private OverseasWorkPeriodListAdapter workPeriodListAdapter;

    private ZPUIStateLayoutManager stateLayoutManager;
    private long selectCode;

    private int overseaType;

    @Override
    protected int contentLayout() {
        return R.layout.activity_overseas_work_period_picker;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        LevelBean selectItem = (LevelBean) getIntent().getSerializableExtra(Constants.DATA_ENTITY);
        overseaType = getIntent().getIntExtra(BossConst.OVERSEAS_TYPE, 0);
        selectCode = selectItem != null ? selectItem.code : 0;

        startObserver();
        initViews();

        mViewModel.loadData(selectCode, overseaType);
    }

    private void startObserver() {
        mViewModel.mLoading.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                if (s == null) {
                    dismissProgressDialog();
                } else {
                    showProgressDialog();
                }
            }
        });
        mViewModel.mError.observe(this, new Observer<ErrorReason>() {
            @Override
            public void onChanged(ErrorReason errorReason) {
                if (errorReason == null) {
                    return;
                }
                ToastUtils.showText(errorReason.getErrReason());
            }
        });
        mViewModel.loadStateObserver.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer state) {
                if (state == null) return;
                switch (state) {
                    case IRefreshState.STATE_LOADING:
                        stateLayoutManager.showLoadingScene();
                        break;
                    case IRefreshState.STATE_ERROR:
                        stateLayoutManager.showErrorScene();
                        break;
                    case IRefreshState.STATE_COMPLETE:
                        stateLayoutManager.dismiss();
                        break;
                    default:
                        break;
                }
            }
        });
        mViewModel.workPeriodListObserver.observe(this, new Observer<WorkPeriodModel>() {
            @Override
            public void onChanged(WorkPeriodModel model) {
                if (model == null) {
                    return;
                }
                if (workPeriodListAdapter != null) {
                    workPeriodListAdapter.setSelectItem(model.selectItem);
                    workPeriodListAdapter.setNewData(model.source);
                }
                refreshButtonActions();
            }
        });
    }

    private void initViews() {
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(OverseasWorkPeriodPickerActivity.this);
            }
        });

        MTextView tvPageTitle = findViewById(R.id.tv_page_title);
        MTextView tvDesc = findViewById(R.id.tv_desc);

        String title;
        String desc;
        if (UserManager.isBossRole()) {
            if (JobStatusChecker.isOverSeaChuChai(overseaType)) {
                title = getString(R.string.string_business_trip_period_boss_title);
                desc = getString(R.string.string_business_trip_period_boss_desc);
            } else {
                title = getString(R.string.string_work_overseas_period_boss_title);
                desc = getString(R.string.string_work_overseas_period_boss_desc);
            }
        } else {
            title = getString(R.string.string_work_overseas_period_geek_title);
            desc = getString(R.string.string_work_overseas_period_geek_desc);
        }

        tvPageTitle.setText(title);
        tvDesc.setText(desc);

        AppBarLayout appBarLayout1 = findViewById(R.id.appBarLayout);
        appBarLayout1.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            boolean collapsed = absOffset >= appBarLayout.getTotalScrollRange();
            //noinspection deprecation
            titleView.setDividerVisibility(collapsed ? View.VISIBLE : View.INVISIBLE);
            titleView.setTitle(collapsed ? tvPageTitle.getText() : "");
        });

        RecyclerView rvList = findViewById(R.id.rv_list);
        workPeriodListAdapter = new OverseasWorkPeriodListAdapter();
        workPeriodListAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                LevelBean item = (LevelBean) adapter.getItem(position);
                if (item == null) return;
                workPeriodListAdapter.setSelectItem(item);
                workPeriodListAdapter.notifyDataSetChanged();

                refreshButtonActions();
            }
        });
        rvList.setAdapter(workPeriodListAdapter);

        stateLayoutManager = new ZPUIStateLayoutManager(this, rvList);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_PROGRESSBAR)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517));
        stateLayoutManager.getErrorLayout()
                .setType(ErrorSceneLayout.ERROR_SCENE_TYPE.ERROR_NETWORK)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517))
                .setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        mViewModel.loadData(selectCode, overseaType);
                    }
                });

        findViewById(R.id.btn_cancel).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(OverseasWorkPeriodPickerActivity.this);
            }
        });

        btnSave = findViewById(R.id.btn_save);
        btnSave.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                LevelBean selectItem = workPeriodListAdapter.getSelectItem();
                Intent intent = new Intent();
                intent.putExtra(Constants.DATA_ENTITY, selectItem);
                setResult(RESULT_OK, intent);
                AppUtil.finishActivity(OverseasWorkPeriodPickerActivity.this);
            }
        });
    }

    private void refreshButtonActions() {
        boolean isActionEnable = workPeriodListAdapter.getSelectItem() != null;
        btnSave.setEnabled(isActionEnable);
    }
}
