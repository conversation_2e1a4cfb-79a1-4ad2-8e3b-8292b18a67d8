package com.hpbr.bosszhipin.module.common.overseas.geek;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ListView;

import com.google.android.material.appbar.AppBarLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.hpbr.bosszhipin.module.common.overseas.OverseasMatchCallBack;
import com.hpbr.bosszhipin.module.common.overseas.OverseasSearchMatchView;
import com.hpbr.bosszhipin.module.common.overseas.OverseasSelectionAdapter;
import com.hpbr.bosszhipin.module.common.overseas.OverseasTreeView;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.threelevel.OnPositionLevelSelectListener;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.statelayout.ZPUIStateLayoutManager;
import zpui.lib.ui.statelayout.layout.ErrorSceneLayout;
import zpui.lib.ui.statelayout.layout.LoadingSceneLayout;

public class GeekOverseasPickerActivity extends BaseAwareActivity<GeekOverseasVm> {

    //region 常量
    public static final String SELECTED_MULTI_POSITION_ITEM = "com.hpbr.bosszhipin.SELECTED_MULTI_POSITION_ITEM";
    // 统一从对象中获取参数
    public static final String KEY_MULTI_EXPECT_PARAMS = "KEY_MULTI_EXPECT_PARAMS";
    public static final String CUSTOM_TITLE = "custom_title";

    //endregion

    private static final int MAX_COUNT = 5;

    private ZPUIStateLayoutManager stateLayoutManager;
    private OverseasSearchMatchView mSearchView;
    private ListView customList;
    private OverseasTreeView positionLevelTreeView;
    private ConstraintLayout clSelectionPanel;

    private OverseasSelectionAdapter adapter;

    private int maxCount;

    @NonNull
    private final List<LevelBean> mPositionList = new CopyOnWriteArrayList<>();
    @NonNull
    private final ArrayList<LevelBean> selectedMultiPositions = new ArrayList<>();

    private String customTitle;

    /**
     * 三级类职位选择回调
     */
    @NonNull
    private final OnPositionLevelSelectListener onPositionLevelSelectListener = new OnPositionLevelSelectListener() {
        @Override
        public void onThirdLevelSelect(LevelBean firstLevel, LevelBean secondLevel, LevelBean thirdLevel) {
            onSecondLevelSelect0(secondLevel, thirdLevel);
        }

        @Override
        public void onOtherLevelClick(@NonNull LevelBean firstLevel, @NonNull LevelBean secondLevel, @NonNull LevelBean thirdLevel) {
            onSecondLevelSelect0(firstLevel, secondLevel);
        }
    };

    /**
     * 搜索匹配的回调
     */
    @NonNull
    private final OverseasMatchCallBack matchCallBack = new OverseasMatchCallBack() {
        @Override
        public void onSearchMatchResult(boolean isSearchEmpty) {
            if (isSearchEmpty) {
                showMainList();
            } else {
                showCustomList();
            }
        }

        @Override
        public void onItemClickListener(LevelBean secondLevel, int position) {
            if (secondLevel != null) {
                onSecondLevelSelect0(secondLevel.getParentLevel(), secondLevel);
            }

            showMainList();

            // 清空搜素框的词
            mSearchView.clearText();
        }

        @Override
        public void setNoteVisibility(boolean isShow) {
            setNoteVisibility(isShow, true);
        }

        @Override
        public void setNoteVisibility(boolean isShow, boolean showTipBar) {
            if (isShow) {
                showMainList();
            }
        }

        @Override
        public void onJobReport() {

        }
    };
    private MTextView tvSelectCount;
    private RecyclerView rvSelections;
    private View saveBtn;

    @Override
    protected int contentLayout() {
        return R.layout.activity_overseas_picker;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        initIntent();
        initViews();
        getData();
    }

    private void initIntent() {
        maxCount = MAX_COUNT;
        customTitle =getIntent().getStringExtra(CUSTOM_TITLE);
        Serializable extra = getIntent().getSerializableExtra(SELECTED_MULTI_POSITION_ITEM);
        if (extra instanceof ArrayList) {
            //noinspection unchecked
            ArrayList<LevelBean> list = (ArrayList<LevelBean>) extra;
            if (!LList.isEmpty(list)) {
                selectedMultiPositions.addAll(list);
            }
        }
    }

    private boolean isChecked(@NonNull LevelBean levelBean) {
        boolean isChecked = false;
        for (LevelBean selectedMultiPosition : selectedMultiPositions) {
            String name = selectedMultiPosition.name;
            long code = selectedMultiPosition.code;
            if (name == null || code <= 0) {
                continue;
            }

            if (code == levelBean.code && name.equals(levelBean.name)) {
                isChecked = true;
                break;
            }
        }
        return isChecked;
    }

    private void initViews() {
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setDividerInvisible();
        titleView.setBackClickListener();
        titleView.getTvBtnAction().setTextColor(ContextCompat.getColor(this, R.color.app_green_dark));

        tvSelectCount = findViewById(R.id.tv_select_count);
        MTextView tvPageTitle = findViewById(R.id.tv_page_title);
        if (!TextUtils.isEmpty(customTitle)){
            tvPageTitle.setText(customTitle);

        }else {
            tvPageTitle.setText(R.string.string_work_overseas_country_geek_title);
        }
        mSearchView = findViewById(R.id.rl_search);
        customList = findViewById(R.id.lv_search0);
        mSearchView.attachCustomList(customList);

        positionLevelTreeView = findViewById(R.id.mLevelView);

        mSearchView.setOnSuggestCallback(isEmpty -> {
        });

        AppBarLayout abl = findViewById(R.id.appBarLayout);
        abl.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            int absOffset = Math.abs(verticalOffset);
            boolean collapsed = absOffset >= appBarLayout.getTotalScrollRange();
            //noinspection deprecation
            titleView.setDividerVisibility(collapsed ? View.VISIBLE : View.INVISIBLE);
            titleView.setTitle(collapsed ? tvPageTitle.getText() : "");
        });

        findViewById(R.id.btn_cancel).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                AppUtil.finishActivity(v.getContext());
            }
        });

        saveBtn = findViewById(R.id.btn_save);
        saveBtn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                saveMultiPositions();
            }
        });

        stateLayoutManager = new ZPUIStateLayoutManager(this, positionLevelTreeView);
        stateLayoutManager.getLoadingLayout()
                .setType(LoadingSceneLayout.LOADING_SCENE_TYPE.LOADING_PROGRESSBAR)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517));
        stateLayoutManager.getErrorLayout()
                .setType(ErrorSceneLayout.ERROR_SCENE_TYPE.ERROR_NETWORK)
                .setBackgroundColor(ContextCompat.getColor(this, R.color.color_FFFFFFFF_FF151517))
                .setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        mViewModel.getData();
                    }
                });

        clSelectionPanel = findViewById(R.id.cl_selection_panel);
        rvSelections = findViewById(R.id.rv_selections);

        adapter = new OverseasSelectionAdapter();
        adapter.setOnItemClickListener((adapter, view, position) -> {
            LevelBean bean = (LevelBean) adapter.getItem(position);
            if (bean != null) {
                removeItem(bean);
            }
        });
        rvSelections.setAdapter(adapter);
    }

    private void getData() {
        mViewModel.loadStateObserver.observe(this, state -> {
            if (state == null) return;
            switch (state) {
                case IRefreshState.STATE_LOADING:
                    stateLayoutManager.showLoadingScene();
                    break;
                case IRefreshState.STATE_ERROR:
                    stateLayoutManager.showErrorScene();
                    break;
                case IRefreshState.STATE_COMPLETE:
                    stateLayoutManager.dismiss();
                    break;
                default:
                    break;
            }
        });
        mViewModel.respMld.observe(this, countryConfigQueryResponse -> {
            if (countryConfigQueryResponse != null && countryConfigQueryResponse.configList != null) {
                mPositionList.clear();
                mPositionList.addAll(countryConfigQueryResponse.configList);
                setData();

                mSearchView.setDataSource(mPositionList);
            }
        });
        mViewModel.getData();
    }

    /**
     * 设置数据
     */
    private void setData() {
        positionLevelTreeView.setAdapter(mPositionList, selectedMultiPositions, onPositionLevelSelectListener);

        OverseasTreeView.complementSelectionName(mPositionList, selectedMultiPositions);

        mSearchView.setMatchCallBack(matchCallBack);
        // 需要在三级列表创建后设置多选状态
//        shouldInitSelectedPositions();
        // 更新选中期望数目
        updateSelectCount();
        // 更新已选界面
        updateSelectionPanel();
    }

    private void onSecondLevelSelect0(LevelBean firstLevel, LevelBean secondLevel) {
        // 点击已选中的，则移除该项目
        if (secondLevel.isChecked() || isChecked(secondLevel)) {
            removeItem(secondLevel);
            return;
        }

        // 判断是否已选满
        if (alreadyFull()) {
            ToastUtils.showText("最多可选" + MAX_COUNT + "项");
            return;
        }

        // 1. 添加该选中项
        secondLevel.setGrandParentLevel(firstLevel);
        addItem(secondLevel);
    }

    /**
     * 新增
     */
    private void addItem(@NonNull LevelBean thirdLevel) {
        selectedMultiPositions.add(thirdLevel);
        positionLevelTreeView.notifyDataSetChanged();
        updateSelectCount();
        updateSelectionPanel();
        rvSelections.post(() -> {
            int targetPosition = adapter.getItemCount() - 1;
            if (targetPosition > RecyclerView.NO_POSITION) {
                rvSelections.smoothScrollToPosition(targetPosition);
            }
        });
    }

    /**
     * 移除
     */
    private void removeItem(@NonNull LevelBean thirdLevel) {
        selectedMultiPositions.remove(thirdLevel);
        positionLevelTreeView.notifyDataSetChanged();
        updateSelectCount();
        updateSelectionPanel();
    }

    /**
     * 更新选中个数
     */
    private void updateSelectCount() {
        int selectCount = selectedMultiPositions.size();
        String countStr = String.valueOf(selectCount);
        String totalStr = countStr + "/" + maxCount;
        SpannableStringBuilder builder = new SpannableStringBuilder(totalStr);
        builder.setSpan(
                new ForegroundColorSpan(ContextCompat.getColor(GeekOverseasPickerActivity.this, R.color.app_green_dark)),
                0,
                countStr.length(),
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
        );
        tvSelectCount.setText(builder);
    }

    /**
     * 更新已选操作栏
     */
    private void updateSelectionPanel() {
        int selectCount = selectedMultiPositions.size();
        if (selectCount == 0) {
            clSelectionPanel.setVisibility(View.GONE);
        } else {
            clSelectionPanel.setVisibility(View.VISIBLE);
            if (adapter != null) {
                adapter.setNewData(selectedMultiPositions);
            }
        }
        saveBtn.setEnabled(selectCount > 0);
    }

    private boolean alreadyFull() { // 多选期望已选满
        return maxCount == selectedMultiPositions.size();
    }

    private void showMainList() {
        positionLevelTreeView.setVisibility(View.VISIBLE);
        customList.setVisibility(View.GONE);
    }

    private void showCustomList() {
        positionLevelTreeView.setVisibility(View.GONE);
        customList.setVisibility(View.VISIBLE);
    }

    /**
     * 多期望保存
     */
    private void saveMultiPositions() {
        if (selectedMultiPositions.isEmpty()) {
            ToastUtils.showText("请至少选择一个国家/地区");
        } else {
            AppUtil.hideSoftInput(this);

            Intent intent = getIntent();
            intent.putExtra(SELECTED_MULTI_POSITION_ITEM, selectedMultiPositions);
            setResult(RESULT_OK, intent);
            AppUtil.finishActivity(this);
        }
    }

    /**
     * 统一使用该接口跳转职类选择页面！跳转参数封装到{@link MultiExpectParams}对象中。
     */
    public static void jumpForResult(@NonNull Context context, @NonNull MultiExpectParams params) {
        Intent intent = getIntent(context, params);
        AppUtil.startActivityForResult(context, intent, params.requestCode);
    }

    @NonNull
    public static Intent getIntent(@NonNull Context context, @NonNull MultiExpectParams params) {
        Intent intent = new Intent(context, GeekOverseasPickerActivity.class);

        // 统一从对象中获取参数！
        intent.putExtra(KEY_MULTI_EXPECT_PARAMS, params);

        //region 废弃，统一从 KEY_MULTI_EXPECT_PARAMS 获取 MultiExpectParams 对象
        intent.putExtra(SELECTED_MULTI_POSITION_ITEM, params.selectedMultiPositions);
        intent.putExtra(CUSTOM_TITLE,params.customTitle);//暂时先这样，需要处理的参数还不是很多，统一对象那边也没有获取
        //endregion

        return intent;
    }

    @SuppressWarnings("unused")
    public static class MultiExpectParams extends BaseEntity {

        private static final long serialVersionUID = -2021576221093654878L;

        int requestCode;

        String customTitle;

        @Nullable
        ArrayList<LevelBean> selectedMultiPositions;

        private MultiExpectParams() {

        }

        public MultiExpectParams setCustomTitle(String customTitle) {
            this.customTitle = customTitle;
            return this;
        }

        public static MultiExpectParams obj() {
            return new MultiExpectParams();
        }

        public MultiExpectParams requestCode(int requestCode) {
            this.requestCode = requestCode;
            return this;
        }

        public MultiExpectParams selectedMultiPositions(@Nullable
                                                        ArrayList<LevelBean> selectedMultiPositions) {
            this.selectedMultiPositions = selectedMultiPositions;
            return this;
        }

    }

}