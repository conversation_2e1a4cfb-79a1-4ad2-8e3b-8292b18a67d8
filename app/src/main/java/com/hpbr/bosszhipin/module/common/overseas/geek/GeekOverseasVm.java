package com.hpbr.bosszhipin.module.common.overseas.geek;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.listener.IRefreshState;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.CountryConfigQueryRequest;
import net.bosszhipin.api.CountryConfigQueryResponse;
import net.bosszhipin.base.ApiRequestCallback;

public class GeekOverseasVm extends BaseViewModel {

    @NonNull
    public final MutableLiveData<Integer> loadStateObserver = new MutableLiveData<>();
    @NonNull
    public final MutableLiveData<CountryConfigQueryResponse> respMld = new MutableLiveData<>();

    public GeekOverseasVm(@NonNull Application application) {
        super(application);
    }

    public void getData() {
        CountryConfigQueryRequest request = new CountryConfigQueryRequest(new ApiRequestCallback<CountryConfigQueryResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                loadStateObserver.setValue(IRefreshState.STATE_LOADING);
            }

            @Override
            public void onSuccess(ApiData<CountryConfigQueryResponse> data) {
                loadStateObserver.setValue(IRefreshState.STATE_COMPLETE);
                respMld.setValue(data.resp);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                loadStateObserver.setValue(IRefreshState.STATE_ERROR);
                ToastUtils.showText(reason.getErrReason());
            }

            @Override
            public void onComplete() {
//                if (BuildInfoUtils.isDebug() && UserManager.getUID() == 6455996) {
//                    respMld.setValue(CountryConfigQueryResponse.mock());
//                }
            }
        });
        request.execute();
    }
}
