package com.hpbr.bosszhipin.module.common.popup;

import android.content.Context;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.twl.utils.sp.SpFactory;
import com.twl.utils.sp.SpImpl;

/**
 * Author: ZhouYou
 * Date: 2018/4/18.
 */
public abstract class BasePopupTask<T> {

    private static SpImpl sp;

    static {
        Context context = App.get().getContext();
        sp = SpFactory.create(context, Constants.PREFIX + ".public_dialog" + UserManager.getUID());
    }

    protected SpImpl getSp() {
        return sp;
    }

    /**
     * 缓存的弹框数据数据
     */
    private T data;

    /**
     * 获取数据源
     *
     * @return
     */
    public T getData() {
        return data;
    }

    /**
     * 设置数据源
     *
     * @param data
     */
    protected void setData(T data) {
        this.data = data;
    }

    /**
     * 清空数据源
     */
    protected void clearData() {
        data = null;
    }

    /**
     * 弹框成功的同时将弹框的时间戳写入偏好
     */
    protected abstract void saveDialogShowTime();

    /**
     * 获取上次弹框的时间戳
     */
    protected abstract long getLastTimeDialogShowTime();

    /**
     * 是否需要弹窗
     *
     * @return true - 需要弹出
     */
    protected abstract boolean needShow();
}
