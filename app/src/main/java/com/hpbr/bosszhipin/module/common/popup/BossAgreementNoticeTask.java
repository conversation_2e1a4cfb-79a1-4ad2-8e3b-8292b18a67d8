package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;

import net.bosszhipin.api.GetAgreementUpdateNoticeResponse;

/**
 * Author: zhouyou
 * Date: 2019/5/9
 */
public class BossAgreementNoticeTask extends BasePopupTask<GetAgreementUpdateNoticeResponse> {

    private static BossAgreementNoticeTask instance = new BossAgreementNoticeTask();

    public static BossAgreementNoticeTask getInstance() {
        return instance;
    }

    private static final String TIME_KEY = Constants.PREFIX + ".agreement_notice";

    @Override
    protected void saveDialogShowTime() {
        SpManager.get().user().edit().putBoolean(TIME_KEY, false).apply();
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return SpManager.get().user().getBoolean(TIME_KEY, true);
    }
}
