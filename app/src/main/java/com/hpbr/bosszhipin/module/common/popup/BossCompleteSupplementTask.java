package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;

import net.bosszhipin.api.GetBossCompleteSupplementPopWindowResponse;

/**
 * Author: zhouyou
 * Date: 2023/10/26
 */
public class BossCompleteSupplementTask extends BasePopupTask<GetBossCompleteSupplementPopWindowResponse> {

    private static BossCompleteSupplementTask instance = new BossCompleteSupplementTask();

    public static BossCompleteSupplementTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        SpManager.get().user().edit().putBoolean(Constants.SP_BOSS_SUPPLEMENT_COMPLETE_V2, false).apply();
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }


    @Override
    protected boolean needShow() {
        return SpManager.get().user().getBoolean(Constants.SP_BOSS_SUPPLEMENT_COMPLETE_V2, true);
    }
}
