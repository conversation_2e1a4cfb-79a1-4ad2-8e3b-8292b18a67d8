package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.GetBossLivingRoomInfoResponse;

/**
 * Author: zhouyou
 * Date: 2019/5/9
 */
public class BossLivingRoomInfoTask extends BasePopupTask<GetBossLivingRoomInfoResponse> {

    private static BossLivingRoomInfoTask instance = new BossLivingRoomInfoTask();

    public static BossLivingRoomInfoTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return true;
    }
}
