package com.hpbr.bosszhipin.module.common.popup;

import com.monch.lbase.util.LText;

import net.bosszhipin.api.GetPushEngineContentTipResponse;


public class BossPushEngineContentTipTask extends BasePopupTask<GetPushEngineContentTipResponse> {

    private static final BossPushEngineContentTipTask instance = new BossPushEngineContentTipTask();

    public static BossPushEngineContentTipTask getInstance() {
        return instance;
    }

    @Override
    public void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }


    @Override
    public void setData(GetPushEngineContentTipResponse data) {
        super.setData(data);
    }

    @Override
    protected boolean needShow() {
        GetPushEngineContentTipResponse data = getData();
        if (data!=null&&data.show && !LText.empty(data.img)) {
            return true;
        }
        return false;
    }
}
