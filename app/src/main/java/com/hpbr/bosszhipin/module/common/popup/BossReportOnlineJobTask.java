package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.GetBossReportDisposeJobListResponse;


public class BossReportOnlineJobTask extends BasePopupTask<GetBossReportDisposeJobListResponse> {

    private static BossReportOnlineJobTask instance = new BossReportOnlineJobTask();

    public static BossReportOnlineJobTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return false;
    }
}
