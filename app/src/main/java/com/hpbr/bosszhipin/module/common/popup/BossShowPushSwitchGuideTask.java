package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.SettingPushNoticeGetResponse;

public class BossShowPushSwitchGuideTask extends BasePopupTask<SettingPushNoticeGetResponse> {

    private static BossShowPushSwitchGuideTask instance = new BossShowPushSwitchGuideTask();

    public static BossShowPushSwitchGuideTask getInstance() {
        return instance;
    }


    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return getData() != null && getData().show;
    }


}
