package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.bean.ServerCertViolateRuleDialogBean;


public class CertViolateRuleBossTask extends BasePopupTask<ServerCertViolateRuleDialogBean> {

    private static CertViolateRuleBossTask instance = new CertViolateRuleBossTask();

    public static CertViolateRuleBossTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return false;
    }
}
