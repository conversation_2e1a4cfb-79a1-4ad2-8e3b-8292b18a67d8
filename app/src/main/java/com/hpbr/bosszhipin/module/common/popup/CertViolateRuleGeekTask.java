package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.bean.ServerCertViolateRuleDialogBean;


public class CertViolateRuleGeekTask extends BasePopupTask<ServerCertViolateRuleDialogBean> {

    private static CertViolateRuleGeekTask instance = new CertViolateRuleGeekTask();

    public static CertViolateRuleGeekTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return false;
    }
}
