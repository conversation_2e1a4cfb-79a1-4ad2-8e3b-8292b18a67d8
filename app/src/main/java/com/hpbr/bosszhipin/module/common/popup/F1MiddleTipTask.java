package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.F1MiddleTipQueryResponse;


public class F1MiddleTipTask extends BasePopupTask<F1MiddleTipQueryResponse> {

    private static F1MiddleTipTask instance = new F1MiddleTipTask();

    public static F1MiddleTipTask getInstance() {
        return instance;
    }

    private  boolean isNeedShow =true;
    @Override
    protected void saveDialogShowTime() {
        isNeedShow =false;
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return isNeedShow;
    }
}
