package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.F1MiddleTipQueryResponse;


public class F1PersonalityRecommendGuideTask extends BasePopupTask<F1MiddleTipQueryResponse> {

    private static F1PersonalityRecommendGuideTask instance = new F1PersonalityRecommendGuideTask();

    public static F1PersonalityRecommendGuideTask getInstance() {
        return instance;
    }

    private  boolean isNeedShow =true;
    @Override
    protected void saveDialogShowTime() {
        isNeedShow =false;
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return isNeedShow;
    }
}
