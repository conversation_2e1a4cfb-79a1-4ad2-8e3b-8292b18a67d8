package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;

import net.bosszhipin.api.GeekStudyOverseasAuthenticationResponse;

/**
 * Author: ZhouYou
 * Date: 2018/6/5.
 */
public class GeekOverseasAuthenticationPopupTask extends BasePopupTask<GeekStudyOverseasAuthenticationResponse> {

    private static GeekOverseasAuthenticationPopupTask instance = new GeekOverseasAuthenticationPopupTask();

    public static GeekOverseasAuthenticationPopupTask getInstance() {
        return instance;
    }

    private static final String GEEK_STUDY_OVERSEA_KEY = Constants.PREFIX + ".GEEK_STUDY_OVERSEA_KEY";

    @Override
    protected void saveDialogShowTime() {
        getSp().putBoolean(GEEK_STUDY_OVERSEA_KEY + "_" + UserManager.getUID(), false);
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return getSp().getBoolean(GEEK_STUDY_OVERSEA_KEY + "_" + UserManager.getUID(), true);
    }
}
