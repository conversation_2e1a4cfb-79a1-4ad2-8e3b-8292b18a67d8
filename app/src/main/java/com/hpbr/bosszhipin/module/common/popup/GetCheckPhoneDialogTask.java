package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.GetCheckPhoneStatusResponse;

/**
 * Author: fanfan
 */
public class GetCheckPhoneDialogTask extends BasePopupTask<GetCheckPhoneStatusResponse> {
    private static final GetCheckPhoneDialogTask instance = new GetCheckPhoneDialogTask();

    public static GetCheckPhoneDialogTask getInstance() {
        return instance;
    }

    public void setHasShow(boolean hasShow) {
        this.hasShow = hasShow;
    }

    private boolean hasShow = false;

    public boolean isHasShow() {
        return hasShow;
    }

    @Override
    protected void saveDialogShowTime() {
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return false;
    }
}
