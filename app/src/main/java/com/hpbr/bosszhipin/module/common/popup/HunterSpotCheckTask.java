package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;

import net.bosszhipin.api.GetHunterSpotCheckPopResponse;


public class HunterSpotCheckTask extends BasePopupTask<GetHunterSpotCheckPopResponse> {

    private static final String TIME_KEY = Constants.PREFIX + ".spot_check";
    private static HunterSpotCheckTask instance = new HunterSpotCheckTask();

    public static HunterSpotCheckTask getInstance() {
        return instance;
    }

    @Override
    protected void saveDialogShowTime() {
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return false;
    }
}
