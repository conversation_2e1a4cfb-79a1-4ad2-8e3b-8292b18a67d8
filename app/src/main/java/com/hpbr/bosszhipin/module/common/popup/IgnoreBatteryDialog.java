package com.hpbr.bosszhipin.module.common.popup;

import android.app.Activity;
import android.graphics.Typeface;
import android.os.Build;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.manager.NotificationCheckUtils;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.twl.utils.ActivityUtils;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class IgnoreBatteryDialog {

    public static final String TAG = "IgnoreBatteryDialog";
    public static final String TAG_COUNT = "IgnoreBatteryDialogCount";
    public static boolean isShowed;
    private BottomView bottomView;

    public static boolean isNeedShow() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return false;
        if (!NotificationCheckUtils.areNotificationsEnabled(Utils.getApp())) return false;
        if (!AndroidDataStarGray.getInstance().isIgnoreBattery()) return false;
        long showTime = SpManager.get().global().getLong(TAG, 0);
        if (showTime > 0 && System.currentTimeMillis() - showTime < 7 * 24 * 3600 * 1000) { //7天显示一次
            return false;
        }
        int anInt = SpManager.get().global().getInt(TAG_COUNT, 0); // 最多忽略3次
        if (anInt > 2) return false;
        return !MobileUtil.isIgnoringBatteryOptimizations();
    }


    public boolean showDialog(Activity activity) {
        if (!ActivityUtils.isValid(activity)) return false;
        if (!isNeedShow()) return false;
        if (bottomView != null && bottomView.isShowing()) {
            bottomView.dismissBottomView();
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return false;
        View view = LayoutInflater.from(activity).inflate(R.layout.dialog_layout_ignore_battery, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        TextView tv_title = view.findViewById(R.id.tv_title);
        ImageView iv_close = view.findViewById(R.id.iv_close);
        TextView tv_tips = view.findViewById(R.id.tv_tips);
        ZPUIRoundButton zpui_btn_submit = view.findViewById(R.id.zpui_btn_submit);

        String content = "当前无法及时收到Boss发送的消息，点击同意，使BOSS直聘将可以在后台运行，避免重要消息遗漏";
        String highStr = "避免重要消息遗漏";
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder(content);
        spannableStringBuilder.setSpan(new ForegroundColorSpan(tv_title.getCurrentTextColor()), content.indexOf(highStr), content.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        spannableStringBuilder.setSpan(new StyleSpan(Typeface.BOLD), content.indexOf(highStr), content.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        tv_tips.setText(spannableStringBuilder);
        /*点击关闭按钮*/
        iv_close.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View view) {
                dismissDialog(activity);
                int anInt = SpManager.get().global().getInt(TAG_COUNT, 0);
                SpManager.get().global().edit().putLong(TAG_COUNT, anInt + 1).apply();
                AnalyticsFactory.create().action("c-power-whitelist-permission-popup-click").param("p","1").debug().build();
            }
        });

        /*点击提交按钮*/
        zpui_btn_submit.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissDialog(activity);
                MobileUtil.requestIgnoreBatteryOptimizations(activity);
                AnalyticsFactory.create().action("c-power-whitelist-permission-popup-click").param("p","0").debug().build();
            }
        });

        bottomView.showBottomView(true);
        AnalyticsFactory.create().action("c-power-whitelist-permission-popup-exposure").debug().build();
        SpManager.get().global().edit().putLong(TAG, System.currentTimeMillis()).apply();
        return true;
    }


    public void dismissDialog(Activity activity) {
        if (bottomView != null && bottomView.isShowing() && ActivityUtils.isValid(activity)) {
            bottomView.dismissBottomView();
        }
        isShowed = false;
    }

}
