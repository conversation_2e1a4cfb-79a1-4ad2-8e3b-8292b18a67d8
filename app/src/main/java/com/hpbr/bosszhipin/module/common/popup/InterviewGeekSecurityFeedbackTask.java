package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;

import com.hpbr.bosszhipin.module.interview.api.InterviewGeekSecurityFeedbackResponse;

/**
 * 1011.47 牛人f1面试评价请求任务
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
public class InterviewGeekSecurityFeedbackTask extends BasePopupTask<InterviewGeekSecurityFeedbackResponse> {

    private static InterviewGeekSecurityFeedbackTask instance = new InterviewGeekSecurityFeedbackTask();

    public static InterviewGeekSecurityFeedbackTask getInstance() {
        return instance;
    }

    private static final String TIME_KEY = Constants.PREFIX + ".INTERVIEW_GEEK_SECURITY_FEEDBACK";

    @Override
    protected void saveDialogShowTime() {
        getSp().putBoolean(TIME_KEY + "_" + UserManager.getUID() + UserManager.getUserRole().get(), false);
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return getSp().getBoolean(TIME_KEY + "_" + UserManager.getUID() + UserManager.getUserRole().get(), true);
    }
}
