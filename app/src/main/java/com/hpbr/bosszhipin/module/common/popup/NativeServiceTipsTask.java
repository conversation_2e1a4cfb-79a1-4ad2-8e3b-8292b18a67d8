package com.hpbr.bosszhipin.module.common.popup;

import net.bosszhipin.api.NativeServicePopResponse;

/**
 * created by tong<PERSON><PERSON><PERSON>.
 * date: 2021-06-24
 * time: 18:12
 * description:
 */
public class NativeServiceTipsTask extends BasePopupTask<NativeServicePopResponse> {

    private static NativeServiceTipsTask sInstance = new NativeServiceTipsTask();

    public static NativeServiceTipsTask getInstance() {
        return sInstance;
    }


    @Override
    protected void saveDialogShowTime() {

    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return true;
    }
}
