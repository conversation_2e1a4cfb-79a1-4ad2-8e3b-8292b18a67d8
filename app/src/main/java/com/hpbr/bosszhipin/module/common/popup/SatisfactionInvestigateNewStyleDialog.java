package com.hpbr.bosszhipin.module.common.popup;

import android.app.Activity;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.decoration.GridDecoration;
import com.hpbr.bosszhipin.module.common.adapter.SatisfactionScoreAdapter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：SatisfactionInvestigateNewStyleDialog
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/7  2:13 PM
 */
public class SatisfactionInvestigateNewStyleDialog {

    /*文本框可输入最大字数*/
    private final int MAX_INPUT_LENGTH = 100;

    private BottomView bottomView;

    /**
     * 显示弹框
     */
    public boolean showDialog(Activity activity, long surveyId, int messageType, int userType, String title) {
        if (!ActivityUtils.isValid(activity)) return false;
        if (surveyId <= 0) return false;
        if (bottomView != null && bottomView.isShowing()) {
            bottomView.dismissBottomView();
        }

        View view = View.inflate(activity, R.layout.dialog_layout_satisfaction_investigate_new_style, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Auth_2, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        TextView tv_title = view.findViewById(R.id.tv_title);
        ImageView iv_close = view.findViewById(R.id.iv_close);
        TextView tv_desc = view.findViewById(R.id.tv_desc);
        RecyclerView rv_list = view.findViewById(R.id.rv_list);
        rv_list.addItemDecoration(new GridDecoration(activity, 4, 0, 0));
        ConstraintLayout cl_input = view.findViewById(R.id.cl_input);
        MEditText et_input = view.findViewById(R.id.et_input);
        TextView tv_input_count = view.findViewById(R.id.tv_input_count);
        ZPUIRoundButton zpui_btn_submit = view.findViewById(R.id.zpui_btn_submit);

        tv_title.setText("体验调查");
        tv_desc.setText(TextUtils.isEmpty(title) ? "基于你最近的使用，你有多大可能将BOSS直聘推荐给像你一样的人？" : title);
        InputUtils inputUtils = new InputUtils(activity, 0, MAX_INPUT_LENGTH);
        inputUtils.checkInputCount(tv_input_count, null, ContextCompat.getColor(activity, R.color.color_FFB8B8B8_FF6D6D70));
        et_input.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s == null) return;
                inputUtils.checkInputCount(tv_input_count, s.toString(), ContextCompat.getColor(activity, R.color.color_FFB8B8B8_FF6D6D70));
            }
        });

        List<Integer> scoreList = new ArrayList<>();
        for (int i = 0; i <= 10; i++) {
            scoreList.add(i);
        }

        SatisfactionScoreAdapter scoreAdapter = new SatisfactionScoreAdapter(activity);
        rv_list.setAdapter(scoreAdapter);
        scoreAdapter.setNewData(scoreList);
        zpui_btn_submit.setEnabled(false);

        scoreAdapter.setOnItemClickListener((adapter, view1, position) -> {
            if (ClickProtectedUtil.blockShortClickEvent()) return;
            if (scoreAdapter.getCurrentSelectIndex() == position) return;
            List<Integer> dataList = scoreAdapter.getData();
            if (LList.getCount(dataList) <= position) return;
            scoreAdapter.setCurrentSelectIndex(position);

            int currentSelectIndex = scoreAdapter.getCurrentSelectIndex();
            Integer score = LList.getElement(dataList, currentSelectIndex);
            zpui_btn_submit.setEnabled(currentSelectIndex >= 0);
            cl_input.setVisibility(currentSelectIndex >= 0 ? View.VISIBLE : View.GONE);
            if (currentSelectIndex >= 0 && score != null) {
                List<String> hintList = getHintBetween0And6(score);
                int hintCount = LList.getCount(hintList);
                if (hintCount == 0) {
                    et_input.setHint("");
                    return;
                }
                et_input.setHint(LList.getElement(hintList, new Random().nextInt(hintCount)));
            } else {
                et_input.setHint("");
            }

            AppAnalysisUtil.dotUserQuestClick(score == null ? 0 : score, messageType, userType);/*埋点*/
        });

        /*点击关闭按钮*/
        iv_close.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View view) {
                String inputContent = et_input.getText().toString();
                List<Integer> dataList = scoreAdapter.getData();
                Integer score = LList.getElement(dataList, scoreAdapter.getCurrentSelectIndex());
                AppAnalysisUtil.dotUserQuestSubmit(0, inputContent, score == null ? 0 : score, messageType, userType);/*埋点*/

                dismissDialog(activity);
            }
        });

        /*点击提交按钮*/
        zpui_btn_submit.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                List<Integer> dataList = scoreAdapter.getData();
                if (LList.getCount(dataList) == 0) return;
                Integer score = LList.getElement(dataList, scoreAdapter.getCurrentSelectIndex());
                if (score == null) return;
                String inputContent = et_input.getText().toString();
                boolean isInputBeyondMax = inputUtils.isInputLargerThanMaxLength(inputContent);
                if (isInputBeyondMax) {
                    ToastUtils.showText("最多可输入100个字");
                    return;
                }
                if (onEventListener != null) {
                    onEventListener.onClickSubmit(score, inputContent);
                }
                AppAnalysisUtil.dotUserQuestSubmit(1, inputContent, score, messageType, userType);/*埋点*/

                dismissDialog(activity);
            }
        });

        bottomView.showBottomView(true);

        AppAnalysisUtil.dotUserQuestAlert(messageType, userType, tv_desc.getText().toString());/*埋点*/

        return true;
    }

    private OnEventListener onEventListener;

    public interface OnEventListener {

        /**
         * 点击提交按钮
         *
         * @param score
         * @param content
         */
        void onClickSubmit(Integer score, String content);
    }

    public void setOnEventListener(OnEventListener onEventListener) {
        this.onEventListener = onEventListener;
    }

    public void dismissDialog(Activity activity) {
        if (bottomView != null && bottomView.isShowing() && ActivityUtils.isValid(activity)) {
            bottomView.dismissBottomView();
        }
    }


    /**
     * 获取提示文案
     *
     * @return
     */
    public ArrayList<String> getHintBetween0And6(int score) {
        String[] hintArray;
        if (score <= 6) {
            hintArray = new String[]{"我们对不良体验感到非常抱歉。我们非常想知道，您理想中的好产品是什么样子？",
                    "很抱歉没能让您满意，希望您能帮助我们改进。您介意告诉我们为什么给这样的分数吗？",
                    "很遗憾得到分数。您觉得我们哪里需要改进？",
                    "很抱歉没能让您打高分，请帮助我们改进。您觉得我们应该向谁学习，为什么？"};
        } else if (score <= 8) {
            hintArray = new String[]{"感谢您的答复和时间！我们想要做得更好，请问您觉得还有哪里地方可以改进的吗？",
                    "感谢您抽出宝贵的时间！我们对此非常感激！我们很好奇：有什么可以让您更满意的吗？",
                    "谢谢您的答复。有什么我们可以为您做的吗？"};
        } else {
            hintArray = new String[]{"我们很荣幸让您如此高兴！我们很好奇，您最喜欢我们产品的哪一点？",
                    "我们很高兴有您这样的客户！您能帮我们宣传一下并写一篇简短的评论吗？",
                    "感谢你的评分！有什么我们可以为您做的吗？（您对我们提供的任何其他产品有任何疑问吗？）"};
        }
        return new ArrayList<>(Arrays.asList(hintArray));
    }
}
