package com.hpbr.bosszhipin.module.common.popup;

import android.app.Activity;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintProperties;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.decoration.GridDecoration;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.adapter.SatisfactionNPSAdapter;
import com.hpbr.bosszhipin.module.common.adapter.SatisfactionReasonAdapter;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.NpsSubmitRequest;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.NPSServerBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.List;
import java.util.Set;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * 1119.62 NPS 5分制 满意度弹窗
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
public class SatisfactionInvestigateNpsDialog {

    /**
     * 来源: 点击表情气泡
     */
    public static final int FROM_FAB = 1;
    /**
     * 来源: push消息推送
     */
    public static final int FROM_PUSH_MSG = 2;

    private BottomView bottomView;

    private MEditText mEditText;

    private MTextView mTintText;

    SatisfactionNPSAdapter satisfactionAdapter;

    SatisfactionReasonAdapter reasonAdapter;

    private OnEventListener onEventListener;

    /**
     * 显示弹框
     */
    public boolean showDialog(Activity activity, NPSServerBean npsServerBean, int fromSource) {
        if (!ActivityUtils.isValid(activity))  {
            return false;
        }
        if (bottomView != null && bottomView.isShowing()) {
            bottomView.dismissBottomView();
        }

        View view = View.inflate(activity, R.layout.dialog_layout_satisfaction_investigate_nps, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Transparent, view);
        LinearLayout dialogLayout = view.findViewById(R.id.ll_dialog);
        MTextView titleView = view.findViewById(R.id.tv_title);
        ImageView closeView = view.findViewById(R.id.iv_close);
        titleView.setText(npsServerBean.title);
        //设置弹窗最大高度
        int maxHeight = ZPUIDisplayHelper.getScreenHeight(activity) - ZPUIDisplayHelper.dp2px(activity, 55);
        new ConstraintProperties(dialogLayout)
                .constrainMaxHeight(maxHeight)
                .apply();
        bottomView.setAnimation(R.style.BottomToTopAnim);
        RecyclerView optionsRv = view.findViewById(R.id.rv_satisfication_options);
        ScrollView scrollView = view.findViewById(R.id.scroll_layout);
        MTextView descView = view.findViewById(R.id.tv_desc);

        ConstraintLayout mEditLayout = view.findViewById(R.id.cl_feedback);
        /*文本框可输入最大字数*/
        final int MAX_INPUT_LENGTH = 50;
        InputUtils mInputUtils = new InputUtils(activity, MAX_INPUT_LENGTH);
        mEditText = view.findViewById(R.id.et_feedback);
        mTintText = view.findViewById(R.id.tv_start_hint);
        MTextView countView = view.findViewById(R.id.feedback_count_tv);
        ZPUIRoundButton commitBtn = view.findViewById(R.id.btn_commit_feedback);
        descView.setVisibility(View.GONE);
        closeView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissDialog(activity);
            }
        });

        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                mInputUtils.checkInputCount(countView, s.toString());
                refreshBottomButton(satisfactionAdapter, reasonAdapter, commitBtn);
            }
        });

        bottomView.setKeyBoardListenerWithY(show -> {
            if (show) {
                Utils.runOnUiThreadDelayed(() -> {
                    if (scrollView != null) {
                        scrollView.fullScroll(View.FOCUS_DOWN);
                    }
                    if (mEditText != null) {
                        mEditText.requestFocus();
                    }
                }, 120);
            }
        });

        RelativeLayout mTagsParentView = view.findViewById(R.id.rl_tags_parent);
        RecyclerView rv_tags = view.findViewById(R.id.rv_tags);

        /*「满意度」列表渲染*/
        optionsRv.setLayoutManager(new GridLayoutManager(activity, 5));
        satisfactionAdapter = new SatisfactionNPSAdapter(activity);
        optionsRv.setAdapter(satisfactionAdapter);
        satisfactionAdapter.setNewData(npsServerBean.question);

        /*「评价标签」列表渲染*/
        rv_tags.addItemDecoration(new GridDecoration(activity, 15, 0, 15));
        rv_tags.setLayoutManager(new GridLayoutManager(activity, 1));
        reasonAdapter = new SatisfactionReasonAdapter();
        rv_tags.setAdapter(reasonAdapter);
        reasonAdapter.setNewData(null);

        /*「满意度」Item点击*/
        satisfactionAdapter.setOnItemClickListener((adapter, view1, position) -> {
            handleSatisfactionClick(activity, npsServerBean, fromSource, descView, mEditLayout, commitBtn, mTagsParentView, position);
        });

        /*「评价标签」Item点击*/
        reasonAdapter.setOnItemClickListener((adapter, view1, position) -> {
            if (LList.getCount(reasonAdapter.getData()) <= position) {
                return;
            }
            NPSServerBean.NPSLabelBean reasonBean = LList.getElement(reasonAdapter.getData(), position);
            if (reasonBean == null) {
                return;
            }
            reasonAdapter.changeSelection(reasonBean);
            reasonAdapter.notifyItemChanged(position);

            if (reasonBean.backFill == 1) {
                if (reasonAdapter.isSelected(reasonBean)) {
                    mEditLayout.setVisibility(View.VISIBLE);
                } else {
                    mEditText.setText("");
                    mEditLayout.setVisibility(View.GONE);
                    AppUtil.hideSoftInput(activity, mEditText);
                }
            }

            refreshBottomButton(satisfactionAdapter, reasonAdapter, commitBtn);/*刷新底部按钮UI样式*/
        });


        commitBtn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                handleSubmit(mInputUtils, activity, npsServerBean, fromSource);
            }
        });

        bottomView.showBottomView(true);

        AppAnalysisUtil.doNpsDialogExpo(npsServerBean.pushType, fromSource);

        return true;
    }

    private void handleSatisfactionClick(Activity activity, NPSServerBean npsServerBean, int fromSource, MTextView descView, ConstraintLayout mEditLayout, ZPUIRoundButton commitBtn, RelativeLayout mTagsParentView, int position) {
        if (LList.getCount(satisfactionAdapter.getData()) <= position) {
            return;
        }
        NPSServerBean.NPSQuestionBean optionBean = LList.getElement(satisfactionAdapter.getData(), position);
        if (optionBean == null || position == satisfactionAdapter.getCurrentSelectPositon()) {
            return;
        }

        AppAnalysisUtil.doNpsClickSatisfaction(npsServerBean.pushType, fromSource, optionBean.title);
        AppUtil.hideSoftInput(activity, mEditText);
        if (position != satisfactionAdapter.getCurrentSelectPositon()) {
            mEditLayout.setVisibility(View.GONE);
        }
        mTagsParentView.setVisibility(View.VISIBLE);
        satisfactionAdapter.setCurrentSelectPositon(position);
        reasonAdapter.setNewData(optionBean.label);
        reasonAdapter.clearSelectedSet();
        if (!TextUtils.isEmpty(optionBean.desc)) {
            descView.setVisibility(View.VISIBLE);
            descView.setText(optionBean.desc);
        } else {
            descView.setVisibility(View.GONE);
            descView.setText("");

        }
        if (mTintText.getVisibility() == View.VISIBLE) {
            mTintText.setVisibility(View.GONE);
        }

        commitBtn.setVisibility(View.VISIBLE);
        /*刷新底部按钮UI样式*/
        refreshBottomButton(satisfactionAdapter, reasonAdapter, commitBtn);
    }

    private void handleSubmit(InputUtils mInputUtils, Activity activity, NPSServerBean npsServerBean, int fromSource) {
        List<NPSServerBean.NPSQuestionBean> dataList = satisfactionAdapter.getData();
        if (LList.getCount(dataList) == 0) {
            return;
        }
        NPSServerBean.NPSQuestionBean score = LList.getElement(dataList, satisfactionAdapter.getCurrentSelectPositon());
        if (score == null) {
            return;
        }
        String inputContent = mEditText.getText().toString();
        boolean isInputBeyondMax = mInputUtils.isInputLargerThanMaxLength(inputContent);
        if (isInputBeyondMax) {
            ToastUtils.showText("最多可输入50个字");
            return;
        }

        final NpsSubmitRequest request = new NpsSubmitRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {

                //隐藏键盘
                AppUtil.hideSoftInput(activity);
                //显示反馈界面
                if (onEventListener != null) {
                    onEventListener.onClickSubmit(score, inputContent);
                }

            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.labelId = getLabelId(reasonAdapter.getSelectedSet());
        NPSServerBean.NPSQuestionBean questionBean = LList.getElement(satisfactionAdapter.getData(), satisfactionAdapter.getCurrentSelectPositon());
        if (questionBean != null) {
            request.questionId = questionBean.questionId;

        }
        request.content = mEditText.getTextContent();
        request.userId = UserManager.getUID();
        HttpExecutor.execute(request);

        AppAnalysisUtil.doNpsSubmit(npsServerBean.pushType, fromSource, questionBean != null ? questionBean.title : "", getLabelName(reasonAdapter.getSelectedList()),mEditText.getTextContent());

        dismissDialog(activity);
    }

    private String getLabelId(Set<Long> selectedSet) {
        final StringBuilder stringBuilder = new StringBuilder();
        for (Long labelId : selectedSet) {
            stringBuilder.append(labelId);
            stringBuilder.append(",");
        }
        return stringBuilder.toString();
    }
    private String getLabelName(List<NPSServerBean.NPSLabelBean> labelList) {
        final StringBuilder stringBuilder = new StringBuilder();
        for (NPSServerBean.NPSLabelBean  label: labelList) {
            stringBuilder.append(label.name);
            stringBuilder.append(",");
        }
        if (stringBuilder.length() > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
        return stringBuilder.toString();
    }


    /**
     * 刷新底部按钮的样式
     */
    private void refreshBottomButton(@NonNull SatisfactionNPSAdapter satisfactionAdapter, @NonNull SatisfactionReasonAdapter reasonAdapter, @NonNull ZPUIRoundButton commitBtn) {
        if (satisfactionAdapter.getCurrentSelectPositon() >= 0 && checkLabelLegal(reasonAdapter)) {
            commitBtn.setEnabled(true);
        } else {
            commitBtn.setEnabled(false);
        }
    }

    private boolean checkLabelLegal(SatisfactionReasonAdapter reasonAdapter) {
        if (reasonAdapter == null) {
            return false;
        }

        boolean isLegal = true;
        for (Long labelId : reasonAdapter.getSelectedSet()) {
            for (NPSServerBean.NPSLabelBean labelBean : reasonAdapter.getData()) {
                if (labelBean.labelId == labelId) {
                    if (labelBean.backFill == 1) {
                        isLegal = isLegal && !TextUtils.isEmpty(mEditText.getTextContent());
                    } else {
                        break;
                    }
                }
            }
        }
        return isLegal;
    }

    public interface OnEventListener {

        /**
         * 点击提交按钮
         *
         * @param score
         * @param content
         */
        void onClickSubmit(NPSServerBean.NPSQuestionBean score, String content);
    }

    public void setOnEventListener(OnEventListener onEventListener) {
        this.onEventListener = onEventListener;
    }

    public void dismissDialog(Activity activity) {
        if (bottomView != null && bottomView.isShowing() && ActivityUtils.isValid(activity)) {
            if (mEditText != null) {
                AppUtil.hideSoftInput(activity, mEditText);
            }
            bottomView.dismissBottomView();
        }
    }


}
