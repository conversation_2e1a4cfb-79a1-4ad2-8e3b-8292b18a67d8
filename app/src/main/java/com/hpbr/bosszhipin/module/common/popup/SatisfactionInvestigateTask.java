package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.GetSatisfactionInvestigateResponse;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Author: ZhouYou
 * Date: 2018/10/16.
 */
public class SatisfactionInvestigateTask extends BasePopupTask<GetSatisfactionInvestigateResponse> {

    private static SatisfactionInvestigateTask instance = new SatisfactionInvestigateTask();

    public static SatisfactionInvestigateTask getInstance() {
        return instance;
    }

    private static final String TIME_KEY = Constants.PREFIX + ".SATISFACTION_INVESTIGATION_POP_UP_KEY";

    @Override
    protected void saveDialogShowTime() {
        getSp().putLong(TIME_KEY + "_" + UserManager.getUID() + UserManager.getUserRole().get(), System.currentTimeMillis());
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return getSp().getLong(TIME_KEY + "_" + UserManager.getUID() + UserManager.getUserRole().get());
    }

    /**
     * 判断弹框的逻辑
     *
     * @return true - 弹出弹窗
     */
    @Override
    protected boolean needShow() {
        long lastTime = getLastTimeDialogShowTime();
        if (lastTime == 0) return true;
        long currTime = System.currentTimeMillis();
        return checkDate(lastTime, currTime);
    }

    /**
     * 检查是否超过1天
     *
     * @param lastDate
     * @param currentDate 当前日期字符串
     * @return true - 超过3天
     */
    private boolean checkDate(long lastDate, long currentDate) {
        String lastDateString = timeStamp2Date(lastDate);
        String currentDateString = timeStamp2Date(currentDate);
        long last = LText.getLong(lastDateString);
        long now = LText.getLong(currentDateString);
        return now - last >= 3;
    }

    /**
     * 获取当前时间的年月日
     *
     * @param timestamp
     * @return
     */
    private String timeStamp2Date(long timestamp) {
        String format = "yyyyMMdd";
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date(timestamp));
    }
}
