package com.hpbr.bosszhipin.module.common.popup;

import android.app.Activity;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.utils.functions.Consumer;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.utils.platform.Utils;
import com.twl.utils.ActivityUtils;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * @ClassName ：SatisfactionSubmitSuccessDialog
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/7  5:27 PM
 */
public class SatisfactionSubmitSuccessDialog {

    private BottomView bottomView;
    private ScheduledFuture<?> scheduledTimeFuture;

    /**
     * 显示弹框
     */
    public boolean showDialog(Activity activity, @Nullable Runnable onDismissRunnable) {
        if (!ActivityUtils.isValid(activity)) return false;
        if (bottomView != null && bottomView.isShowing()) {
            bottomView.dismissBottomView();
        }

        View view = View.inflate(activity, R.layout.dialog_layout_satisfaction_submit_success, null);
        bottomView = new BottomView(activity, R.style.BottomViewTheme_Auth_2, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.setOnDismissListener(dialog -> {
            cancelTimer();
            if (onDismissRunnable != null) onDismissRunnable.run();
        });
        ImageView iv_close = view.findViewById(R.id.iv_close);
        ZPUIRoundButton zpui_btn_submit = view.findViewById(R.id.zpui_btn_submit);

        /*点击关闭按钮*/
        iv_close.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View view) {
                dismissDialog(activity);
            }
        });

        /*点击「知道了」按钮*/
        zpui_btn_submit.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                dismissDialog(activity);
            }
        });

        bottomView.showBottomView(true);

        /*开始计时*/
        startTimer(seconds -> {
            Utils.runOnUiThread(() -> {
                if (seconds <= 0) {
                    cancelTimer();
                    dismissDialog(activity);
                }
            });
        });

        return true;
    }

    public void dismissDialog(Activity activity) {
        if (bottomView != null && bottomView.isShowing() && ActivityUtils.isValid(activity)) {
            bottomView.dismissBottomView();
        }
    }

    /**
     * 开始计时
     */
    private void startTimer(@NonNull Consumer<Integer> consumer) {
        cancelTimer();
        scheduledTimeFuture = AppThreadFactory.POOL.scheduleAtFixedRate(new Runnable() {
            int seconds = 3;

            @Override
            public void run() {
                consumer.accept(seconds);
                seconds--;
            }
        }, 0, 1, TimeUnit.SECONDS);
    }

    /**
     * 取消计时
     */
    private void cancelTimer() {
        if (scheduledTimeFuture != null) {
            scheduledTimeFuture.cancel(true);
            scheduledTimeFuture = null;
        }
    }

}
