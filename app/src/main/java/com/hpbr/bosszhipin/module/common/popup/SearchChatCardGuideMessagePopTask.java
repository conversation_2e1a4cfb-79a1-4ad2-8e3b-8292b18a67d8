package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.bean.ServerSearchChatCardGuideMessageBean;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Author: zhouyou
 * Date: 2020-02-18
 */
public class SearchChatCardGuideMessagePopTask extends BasePopupTask<ServerSearchChatCardGuideMessageBean> {
    private static SearchChatCardGuideMessagePopTask instance = new SearchChatCardGuideMessagePopTask();

    public static SearchChatCardGuideMessagePopTask getInstance() {
        return instance;
    }

    private static final String TIME_KEY = "search_card_guide_message";

    /**
     * 最多展示3次
     */
    private static final int MAX_SHOW_TIMES = 3;

    @Override
    protected void saveDialogShowTime() {
        long times = getLastTimeDialogShowTime();
        times++;
        SpManager.get().user().edit().putLong(TIME_KEY, times).apply();
        clearData();
    }

    /**
     * 清空次数
     */
    public void clearShowTimesAfterClicking() {
        SpManager.get().user().edit().putLong(TIME_KEY, 0).apply();
    }

    /**
     * 获取展示过的次数
     *
     * @return
     */
    @Override
    protected long getLastTimeDialogShowTime() {
        return SpManager.get().user().getLong(TIME_KEY, 0);
    }

    @Override
    protected boolean needShow() {
        long showTimes = getLastTimeDialogShowTime();
        return showTimes < MAX_SHOW_TIMES;
    }
}
