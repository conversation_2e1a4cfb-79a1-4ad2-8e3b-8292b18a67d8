package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;

import net.bosszhipin.api.UserActiveQueryResponse;

/**
 * Author: zhouyou
 * Date: 2019-12-13
 */
public class UserActivePopupTask extends BasePopupTask<UserActiveQueryResponse> {

    private static UserActivePopupTask instance = new UserActivePopupTask();

    public static UserActivePopupTask getInstance() {
        return instance;
    }

        private static final String GEEK_ACTIVE_RECALL = Constants.PREFIX + ".GEEK_ACTIVE_RECALL";

//    private String getKey() {
//        SpManager.get().user().getBoolean(GEEK_ACTIVE_RECALL, false);
//        return GEEK_ACTIVE_RECALL;
//    }

    @Override
    protected void saveDialogShowTime() {
        SpManager.get().user().edit().putBoolean(GEEK_ACTIVE_RECALL, false).commit();
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return SpManager.get().user().getBoolean(GEEK_ACTIVE_RECALL, true);
    }
}
