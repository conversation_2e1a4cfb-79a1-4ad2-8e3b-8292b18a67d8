package com.hpbr.bosszhipin.module.common.popup;

import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;

import net.bosszhipin.api.UserActiveQueryResponse;

/**
 * Author: zhouyou
 * Date: 2019-12-13
 */
public class UserWorkStatusTask extends BasePopupTask<UserActiveQueryResponse> {

    private static UserWorkStatusTask instance = new UserWorkStatusTask();

    public static UserWorkStatusTask getInstance() {
        return instance;
    }

    private static final String GEEK_WORK_STATUS = Constants.PREFIX + ".GEEK_WORK_STATUS";

    public boolean isHasShow() {
        return hasShow;
    }

    public void setHasShow(boolean hasShow) {
        this.hasShow = hasShow;
    }

    private boolean hasShow = false;

    @Override
    protected void saveDialogShowTime() {
        SpManager.get().user().edit().putBoolean(GEEK_WORK_STATUS, false).commit();
        clearData();
    }

    @Override
    protected long getLastTimeDialogShowTime() {
        return 0;
    }

    @Override
    protected boolean needShow() {
        return SpManager.get().user().getBoolean(GEEK_WORK_STATUS, true);
    }
}
