package com.hpbr.bosszhipin.module.common.popup.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module.common.adapter.ExpectCityModifyGuideAdapter;
import com.hpbr.bosszhipin.module.main.helper.AddNewJobIntentHelper;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1BeanCovertUtil;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.GetGeekDirectionGuideResponse;
import net.bosszhipin.api.bean.ExpectBean;
import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.api.bean.ServerButtonBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;
import com.hpbr.bosszhipin.utils.GeekF1DialogUtil;

/**
 * 期望城市修改引导
 */
public class ExpectCityModifyGuideDialog extends BaseBottomSheetFragment implements View.OnClickListener {
    public static final  int FROM_F1_GUIDE_SWITCH_CITY=1;
    public static final  int FROM_SWITCH_CITY=2;
    public static final  int FROM_F1_NEARBY_GUIDE_SWITCH_CITY=3;
    private ExpectCityModifyGuideAdapter adapter;
    private GetGeekDirectionGuideResponse data;

    private int from=FROM_F1_GUIDE_SWITCH_CITY;

    private  int type;
    private long  subLocation;
    private long  location;

    private String toAddExpectProtocol;
    private  Context context;

    private  DialogListener dialogListener;

    public void setFrom(int from) {
        this.from = from;
    }

    public void setDialogListener(DialogListener dialogListener) {
        this.dialogListener = dialogListener;
    }

    public static ExpectCityModifyGuideDialog getInstance(GetGeekDirectionGuideResponse  data) {
        ExpectCityModifyGuideDialog fragment = new ExpectCityModifyGuideDialog();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    protected boolean enableTouchOutSide() {
        return false;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context =context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_expect_city_modify, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        btnGo.setOnClickListener(this);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        btnCancel.setOnClickListener(this);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvDesc = view.findViewById(R.id.tv_desc);
        MTextView tvToAddExpectDes = view.findViewById(R.id.tvToAddExpectDes);
        tvToAddExpectDes.setOnClickListener(this);
        MTextView tvToAddExpect = view.findViewById(R.id.tvToAddExpect);
        tvToAddExpect.setOnClickListener(this);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(this);
        RecyclerView rvData = view.findViewById(R.id.rvData);

        if (data != null ) {
            type =data.type;
            ExtraMapBean extraMapBean =data.extraMap;
            if (extraMapBean!=null){
                subLocation =extraMapBean.subLocation;
                location=extraMapBean.location;
                if (context!=null&&!LList.isEmpty(data.titleHighlight)){
                    SpannableStringBuilder builder = ViewCommon.setTextHighLight(data.title, GeekF1BeanCovertUtil.covertHightList(data.titleHighlight), ContextCompat.getColor(context, R.color.app_green_dark));
                    if (builder!=null){
                        tvTitle.setText(builder);
                    }
                }else {
                    tvTitle.setText(data.title);
                }
                if (!TextUtils.isEmpty(data.content)) {
                    tvDesc.setText(data.content);
                }

                if (!LList.isEmpty(extraMapBean.expects)&& context!=null) {
                    extraMapBean.expects.get(0).isSelected =true;
                    adapter = new ExpectCityModifyGuideAdapter(extraMapBean.expects);
                    adapter.setOnItemClickListener((baseQuickAdapter, view1, position) -> {
                        adapter.getData().get(position).isSelected = !adapter.getData().get(position).isSelected;
                        adapter.notifyDataSetChanged();
                    });
                    LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context);
                    rvData.setLayoutManager(linearLayoutManager);
                    rvData.setAdapter(adapter);

                    AnalyticsFactory
                            .create()
                            .param("p",location)
                            .paramIfExist("p2", String.valueOf(subLocation),subLocation>0)
                            .param("p3",GsonUtils.getGson().toJson(extraMapBean.expects))
                            .param("p5",isEnableToNewIntent()?"1":"0")
                            .param("p6",from)
                            .param("p7",data==null?"":data.analyticsSortP)
                            .action(AnalyticsAction.ACTION_GEEK_RCMD_CITYPOPUP_SHOW)
                            .build();
                }
                if (isEnableToNewIntent()){
                    ServerButtonBean serverButtonBean =  data.buttonList.get(0);
                    tvToAddExpectDes.setText(serverButtonBean.data.addExpectText);
                    tvToAddExpect.setText(serverButtonBean.text);
                    tvToAddExpectDes.setVisibility(View.VISIBLE);
                    tvToAddExpect.setVisibility(View.VISIBLE);
                    toAddExpectProtocol =serverButtonBean.url;
                }else {
                    tvToAddExpectDes.setVisibility(View.GONE);
                    tvToAddExpect.setVisibility(View.GONE);
                }
                GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_EXPOSE);
            }

        }
    }


    private boolean isEnableToNewIntent(){
        boolean isEnableToNewIntent =false;
        if (data!=null){
            List<ServerButtonBean> buttonList =  data.buttonList;
            if (!LList.isEmpty(buttonList)){
                ServerButtonBean serverButtonBean =  buttonList.get(0);
                isEnableToNewIntent =serverButtonBean!=null && serverButtonBean.data!=null && serverButtonBean.data.addExpectText!=null;
            }
        }

        return isEnableToNewIntent;
    }
    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (data==null){
            return;
        }
        if (i == R.id.btn_cancel) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_CLOSE);
            doAnalytics("2",null);
            if (!data.clickCloseNoF1Guide){
                LiveBus.with(ChannelConstants.GEEK_F1_EXPECT_CITY_MODIFY_GUIDE).postValue(true);
            }
            dismissAllowingStateLoss();
        } else if (i == R.id.iv_close) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_CLOSE);

            doAnalytics("3",null);
            if (!data.clickCloseNoF1Guide){
                LiveBus.with(ChannelConstants.GEEK_F1_EXPECT_CITY_MODIFY_GUIDE).postValue(true);
            }
            dismissAllowingStateLoss();
        } else if (i == R.id.btn_go) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_OK);
            submitData();
        }else if (i==R.id.tvToAddExpect ||i ==R.id.tvToAddExpectDes){
            doAnalytics("4",null);
            if (context!=null){
                new ZPManager(context, toAddExpectProtocol).handler();
            }
            dismissAllowingStateLoss();
        }

    }

    private void  doAnalytics(String actionP,String actionP2){
        String analyticsSortP="";
        if (data!=null){
            analyticsSortP =data.analyticsSortP;
        }

        AnalyticsFactory
                .create()
                .param("p",actionP)
                .paramIfExist("p2",actionP2,!TextUtils.isEmpty(actionP2))
                .param("p3",location)
                .paramIfExist("p4", String.valueOf(subLocation),subLocation>0)
                .param("p5",from)
                .param("p6",analyticsSortP)
                .action(AnalyticsAction.ACTION_GEEK_RCMD_CITYPOPUP_CLICK)
                .build();
    }

    private void  submitData(){
        List<ExpectBean.ExpectInfo> list  =new ArrayList<>();
        List<ExpectBean> actionP2List  =new ArrayList<>();
        if (adapter != null) {
            for (ExpectBean datum : adapter.getData()) {
                if (datum.isSelected){
                    actionP2List.add(datum);
                    list.add(datum.expectInfo);
                }
            }
        }

        doAnalytics("1",GsonUtils.getGson().toJson(actionP2List));


        if (list.size()==0){
            ToastUtils.showText("请选择至少一项");
            return;
        }
        new AddNewJobIntentHelper().addJobIntentFromJson(context, GsonUtils.getGson().toJson(list), new AddNewJobIntentHelper.EventListener() {
            @Override
            public void onStart() {
            }

            @Override
            public void onComplete() {
                LiveBus.with(ChannelConstants.GEEK_F1_EXPECT_CITY_MODIFY_GUIDE).postValue(true);
                ToastUtils.showText("期望城市更新成功");
                dismissAllowingStateLoss();

                if (dialogListener!=null){
                    dialogListener.addJobIntentComplete();
                }
            }
        });
    }




    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        GeekF1DialogUtil.isShowGuideChangeExpectCity =true;

    }

    public  interface  DialogListener{
     void addJobIntentComplete();
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        GeekF1DialogUtil.isShowGuideChangeExpectCity =false;
    }
}
