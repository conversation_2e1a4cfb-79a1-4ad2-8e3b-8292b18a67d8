package com.hpbr.bosszhipin.module.common.popup.dialog;

import android.content.Context;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.setting_export.SettingConstants;
import com.hpbr.bosszhipin.setting_export.SettingRouter;
import com.hpbr.bosszhipin.utils.GeekF1Util;

import net.bosszhipin.api.F1MiddleTipQueryResponse;
import net.bosszhipin.api.bean.geek.ServerCommonButtonBean;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentManager;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * 期望城市修改引导
 */
public class F1PersonalityRecommendGuideDialog extends BaseBottomSheetFragment implements View.OnClickListener {
    private F1MiddleTipQueryResponse data;
    private TextView tvTitle;
    private TextView tvDesc;
    private ZPUIRoundButton btnGo;
    private ZPUIRoundButton btnCancel;

    private ServerCommonButtonBean cancelButtonBean;
    private ServerCommonButtonBean goButtonBean;

    public static F1PersonalityRecommendGuideDialog getInstance(F1MiddleTipQueryResponse data) {
        F1PersonalityRecommendGuideDialog fragment = new F1PersonalityRecommendGuideDialog();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    protected boolean enableTouchOutSide() {
        return false;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_f1_personality_recommend, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        btnGo = view.findViewById(R.id.btn_go);
        btnGo.setOnClickListener(this);
        btnCancel = view.findViewById(R.id.btn_cancel);
        btnCancel.setOnClickListener(this);
        tvTitle = view.findViewById(R.id.tv_title);
        tvDesc = view.findViewById(R.id.tv_desc);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(this);
        updateUi();
    }

    public void updateUi() {
        if (data != null) {
            tvTitle.setText(data.title);
            tvDesc.setText(data.content);
            List<ServerCommonButtonBean> buttonList = data.buttonList;
            if (buttonList != null && buttonList.size() >= 2) {
                cancelButtonBean = buttonList.get(0);
                goButtonBean = buttonList.get(1);
                if (cancelButtonBean != null && !TextUtils.isEmpty(cancelButtonBean.text)) { //服务没有返回 就用客户端xml 写死兜底文案
                    btnCancel.setText(cancelButtonBean.text);
                }
                if (goButtonBean != null && !TextUtils.isEmpty(goButtonBean.text)) { //服务没有返回 就用客户端xml 写死兜底文案
                    btnGo.setText(goButtonBean.text);
                }
            }
            GeekF1Util.doF1MiddleTipCloseERequest(data.type, 0);
            AnalyticsFactory.bgAction(data.exposureBa);
        }

    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.btn_cancel || i == R.id.iv_close) {
            GeekF1Util.doF1MiddleTipCloseERequest(data.type, 1);
            if (cancelButtonBean != null) {
                AnalyticsFactory.bgAction(cancelButtonBean.ba);
            }
        } else if (i == R.id.btn_go) {
            GeekF1Util.doF1MiddleTipCloseERequest(data.type, 2);
            if (goButtonBean != null) {
                AnalyticsFactory.bgAction(goButtonBean.ba);
            }
            SettingRouter.updateNotifySettingSwitch(true, SettingConstants.NOTIFY_TYPE_POSITION_RECOMMENDATION);
        }
        dismissAllowingStateLoss();

    }
    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
    }
}
