package com.hpbr.bosszhipin.module.common.popup.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.boss.fragment.BaseBottomSheetFragment;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.hpbr.bosszhipin.utils.GeekF1BeanCovertUtil;
import com.hpbr.bosszhipin.utils.GeekF1DialogUtil;
import com.hpbr.bosszhipin.utils.GeekF1Util;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.GetGeekDirectionGuideResponse;
import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.api.bean.ServerButtonBean;


import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

public class GeekPersonalAdvantageGuideDialog extends BaseBottomSheetFragment implements View.OnClickListener {
    private GetGeekDirectionGuideResponse data;
    private int type;
    private Context context;

    private String toLookUrl;

    private boolean isClickToGo;

    public static GeekPersonalAdvantageGuideDialog getInstance(GetGeekDirectionGuideResponse data) {
        GeekPersonalAdvantageGuideDialog fragment = new GeekPersonalAdvantageGuideDialog();
        fragment.setHeightWrapContent(false);
        fragment.data = data;
        fragment.setHeightWrapContent(true);
        return fragment;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        this.context = context;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.geek_dialog_personal_advantage, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        ZPUIRoundButton btnGo = view.findViewById(R.id.btn_go);
        btnGo.setOnClickListener(this);
        ZPUIRoundButton btnCancel = view.findViewById(R.id.btn_cancel);
        btnCancel.setOnClickListener(this);
        MTextView tvTitle = view.findViewById(R.id.tv_title);
        MTextView tvSubTitle = view.findViewById(R.id.tvSubTitle);
        MTextView tvContent = view.findViewById(R.id.tvContent);
        ImageView ivCancel = view.findViewById(R.id.iv_close);
        ivCancel.setOnClickListener(this);
        AnalyticsFactory.create().action("advan-auto-generate-f1-popup-show").build();
        if (data != null) {
            type = data.type;
            if (context != null && !LList.isEmpty(data.titleHighlight)) {
                SpannableStringBuilder builder = ViewCommon.setTextHighLight(data.title, GeekF1BeanCovertUtil.covertHightList(data.titleHighlight), ContextCompat.getColor(context, R.color.app_green_dark));
                if (builder != null) {
                    tvTitle.setText(builder);
                }
            } else {
                tvTitle.setText(data.title);
            }
            if (!TextUtils.isEmpty(data.subTitle)) {
                tvSubTitle.setText(data.subTitle);
            }

            if (!TextUtils.isEmpty(data.content)) {
                tvContent.setText(data.content);
            }

            if (data.buttonList != null && data.buttonList.size() >= 2) {
                ServerButtonBean cancel = data.buttonList.get(0);
                if (cancel != null) {
                    btnCancel.setText(cancel.text);
                }
                ServerButtonBean toLook = data.buttonList.get(1);
                if (toLook != null) {
                    toLookUrl = toLook.url;
                    btnGo.setText(toLook.text);
                }

            }

            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_EXPOSE);
        }
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.btn_cancel) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_CLOSE);
        } else if (i == R.id.iv_close) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_CLOSE);
        } else if (i == R.id.btn_go) {
            GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_OK);
            new ZPManager(context, toLookUrl).handler();
            AnalyticsFactory.create().action("advan-auto-generate-f1-popup-click").param("p",1).build();
            isClickToGo =true;
        }
        dismissAllowingStateLoss();
    }


    @Override
    public void show(@NonNull FragmentManager manager, String tag) {
        super.show(manager, tag);
        GeekF1DialogUtil.isShowPersonalAdvantageGuide = true;
    }


    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (!isClickToGo){
            AnalyticsFactory.create().action("advan-auto-generate-f1-popup-click").param("p",2).build();
        }
        GeekF1Util.submitF1TipCloseFrequency(type, GeekF1Constant.F1_F1_TIP_CLOSE_CLOSE);
        GeekF1DialogUtil.isShowPersonalAdvantageGuide = false;
    }
}
