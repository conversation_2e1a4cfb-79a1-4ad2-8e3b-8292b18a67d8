package com.hpbr.bosszhipin.module.common.provider.addworkexp;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.BelongIndustryBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.onlineresume.view.ItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

/**
 * @ClassName ：BelongIndustryProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/13  8:20 PM
 */
public class BelongIndustryProvider extends BaseItemProvider<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private AddNewWorkExpAdapter.EventListener eventListener;

    public BelongIndustryProvider(Context context, AddNewWorkExpAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return AddNewWorkExpModel.TYPE_BELONG_INDUSTRY;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_belong_industry;
    }

    @Override
    public void convert(BaseViewHolder helper, AddNewWorkExpModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof BelongIndustryBean)) return;
        BelongIndustryBean belongIndustryBean = (BelongIndustryBean) itemModel.getData();

        ItemView item_industry = helper.getView(R.id.item_industry);
        item_industry.setContent(belongIndustryBean.industryName, false);

        if (TextUtils.isEmpty(belongIndustryBean.industryName) && belongIndustryBean.isShowInterceptTip) {
            AnimUtil.errorInputAnim(item_industry, "请选择公司行业");
            belongIndustryBean.isShowInterceptTip = false;
        }

        item_industry.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickBelongIndustry(belongIndustryBean);
                }
            }
        });
    }
}
