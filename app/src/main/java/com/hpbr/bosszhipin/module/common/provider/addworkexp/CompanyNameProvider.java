package com.hpbr.bosszhipin.module.common.provider.addworkexp;

import android.content.Context;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.CompanyNameBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.onlineresume.view.ItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;

/**
 * @ClassName ：CompanyNameProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  2:24 PM
 */
public class CompanyNameProvider extends BaseItemProvider<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private AddNewWorkExpAdapter.EventListener eventListener;

    public CompanyNameProvider(Context context, AddNewWorkExpAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return AddNewWorkExpModel.TYPE_COMPANY_NAME;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_company_name;
    }

    @Override
    public void convert(BaseViewHolder helper, AddNewWorkExpModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof CompanyNameBean)) return;
        CompanyNameBean companyNameBean = (CompanyNameBean) itemModel.getData();

        ItemView item_company = helper.getView(R.id.item_company);
        item_company.setContent(companyNameBean.companyName, false);

        if (LText.empty(companyNameBean.companyName) && companyNameBean.isShowInterceptTip) {
            AnimUtil.errorInputAnim(item_company, "请填写公司名称");
            companyNameBean.isShowInterceptTip = false;
        }

        item_company.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickCompanyName(companyNameBean.companyName);
                }
            }
        });
    }
}
