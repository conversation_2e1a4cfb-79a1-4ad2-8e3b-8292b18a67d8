package com.hpbr.bosszhipin.module.common.provider.addworkexp;

import android.content.Context;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.OnTheJobTimeBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.onlineresume.view.TimeItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.wheelview.workexp.WorkExpUtil;

/**
 * @ClassName ：OnTheJobTimeProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  2:32 PM
 */
public class OnTheJobTimeProvider extends BaseItemProvider<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private AddNewWorkExpAdapter.EventListener eventListener;

    public OnTheJobTimeProvider(Context context, AddNewWorkExpAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return AddNewWorkExpModel.TYPE_ON_THE_JOB_TIME;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_on_the_job;
    }

    @Override
    public void convert(BaseViewHolder helper, AddNewWorkExpModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof OnTheJobTimeBean)) return;
        OnTheJobTimeBean onTheJobTimeBean = (OnTheJobTimeBean) itemModel.getData();

        TimeItemView item_work_time = helper.getView(R.id.item_work_time);
        item_work_time.setStartContent(WorkExpUtil.date8ToText(onTheJobTimeBean.startDate));
        item_work_time.setEndContent(WorkExpUtil.date8ToText(onTheJobTimeBean.endDate));
        item_work_time.getTvTitleView().setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14f);
        item_work_time.getTvTitleView().setTextColor(ContextCompat.getColor(context, R.color.text_c5));

        if (!TextUtils.isEmpty(onTheJobTimeBean.interceptTip)) {
            AnimUtil.errorInputAnim(item_work_time, onTheJobTimeBean.interceptTip);
            onTheJobTimeBean.interceptTip = null;
        }

        item_work_time.getStartContentView().setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickStartTime(onTheJobTimeBean.startDate);
                }
            }
        });
        item_work_time.getEndContentView().setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickEndTime(onTheJobTimeBean.endDate);
                }
            }
        });


    }
}
