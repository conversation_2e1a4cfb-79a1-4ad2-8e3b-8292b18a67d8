package com.hpbr.bosszhipin.module.common.provider.addworkexp;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.PositionNameBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.onlineresume.view.ItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

/**
 * @ClassName ：PositionNameProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/13  8:21 PM
 */
public class PositionNameProvider extends BaseItemProvider<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private AddNewWorkExpAdapter.EventListener eventListener;

    public PositionNameProvider(Context context, AddNewWorkExpAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return AddNewWorkExpModel.TYPE_POSITION_NAME;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_position_name;
    }

    @Override
    public void convert(BaseViewHolder helper, AddNewWorkExpModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof PositionNameBean)) return;
        PositionNameBean positionNameBean = (PositionNameBean) itemModel.getData();

        ItemView item_position_name = helper.getView(R.id.item_position_name);
        item_position_name.setContent(positionNameBean.positionName, false);

        if (TextUtils.isEmpty(positionNameBean.positionClassName) && positionNameBean.isShowInterceptTip) {
            AnimUtil.errorInputAnim(item_position_name, "请选择职位名称");
            positionNameBean.isShowInterceptTip = false;
        }

        item_position_name.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickPositionName();
                }
            }
        });

    }
}
