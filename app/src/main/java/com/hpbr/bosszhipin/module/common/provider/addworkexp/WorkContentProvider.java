package com.hpbr.bosszhipin.module.common.provider.addworkexp;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.AddNewWorkExpAdapter;
import com.hpbr.bosszhipin.module.common.bean.WorkContentBean;
import com.hpbr.bosszhipin.module.common.model.AddNewWorkExpModel;
import com.hpbr.bosszhipin.module.onlineresume.view.ItemView;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.utils.AnimUtil;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

/**
 * @ClassName ：WorkContentProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  2:32 PM
 */
public class WorkContentProvider extends BaseItemProvider<AddNewWorkExpModel, BaseViewHolder> {

    private Context context;
    private AddNewWorkExpAdapter.EventListener eventListener;

    public WorkContentProvider(Context context, AddNewWorkExpAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return AddNewWorkExpModel.TYPE_WORK_CONTENT;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_work_content;
    }

    @Override
    public void convert(BaseViewHolder helper, AddNewWorkExpModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof WorkContentBean)) return;
        WorkContentBean workContentBean = (WorkContentBean) itemModel.getData();

        ItemView item_work_content = helper.getView(R.id.item_work_content);
        item_work_content.setContent(workContentBean.workContent,false);
        item_work_content.setContentMaxLines(3);

        if (TextUtils.isEmpty(workContentBean.workContent) && workContentBean.isShowInterceptTip) {
            AnimUtil.errorInputAnim(item_work_content, "请填写工作内容");
            workContentBean.isShowInterceptTip = false;
        }

        item_work_content.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickWorkContent(workContentBean.workContent);
                }
            }
        });
    }
}
