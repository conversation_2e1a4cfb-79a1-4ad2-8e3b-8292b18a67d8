package com.hpbr.bosszhipin.module.common.provider.requestjobexpect;

import android.content.Context;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.RequestJobExpectAdapter;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.monch.lbase.util.LText;

/**
 * @ClassName ：ExpectItemProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  8:11 PM
 */
public class ExpectItemProvider extends BaseItemProvider<RequestJobExpectModel, BaseViewHolder> {

    private Context context;
    private RequestJobExpectAdapter.EventListener eventListener;

    public ExpectItemProvider(Context context, RequestJobExpectAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return RequestJobExpectModel.TYPE_EXPECT_ITEM;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_expect_item;
    }

    @Override
    public void convert(BaseViewHolder helper, RequestJobExpectModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof JobIntentBean)) return;
        JobIntentBean jobIntentBean = (JobIntentBean) itemModel.getData();

        if (jobIntentBean == null) return;
        LinearLayout ll_item = helper.getView(R.id.ll_item);
        String title = getLocationPosition(jobIntentBean);
        helper.setText(R.id.tv_job_intent_title, title);
        helper.setText(R.id.tv_job_intent_desc, jobIntentBean.salaryDesc);

        ll_item.setOnClickListener(v -> {
            if (eventListener != null) {
                eventListener.onEditJobIntentClick(jobIntentBean);
            }
        });

        helper.setGone(R.id.divider, false);
    }

    private String getLocationPosition(JobIntentBean item) {
        StringBuilder sb = new StringBuilder();
        if (!LText.empty(item.locationName)) {
            sb.append("[").append(item.locationName).append("] ");
        }
        if (!LText.empty(item.positionClassName)) {
            sb.append(item.positionClassName);
        }
        return sb.toString();
    }
}
