package com.hpbr.bosszhipin.module.common.provider.requestjobexpect;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.RequestJobExpectAdapter;
import com.hpbr.bosszhipin.module.common.bean.ExpectListTitleBean;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

/**
 * @ClassName ：ExpectListTitleProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  8:10 PM
 */
public class ExpectListTitleProvider extends BaseItemProvider<RequestJobExpectModel, BaseViewHolder> {

    private Context context;
    private RequestJobExpectAdapter.EventListener eventListener;

    public ExpectListTitleProvider(Context context, RequestJobExpectAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return RequestJobExpectModel.TYPE_EXPECT_LIST_TITLE;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_expect_list_title;
    }

    @Override
    public void convert(BaseViewHolder helper, RequestJobExpectModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof ExpectListTitleBean)) return;
        ExpectListTitleBean expectListTitleBean = (ExpectListTitleBean) itemModel.getData();

        helper.setText(R.id.tv_sub_title, expectListTitleBean.title);
        ImageView iv_add = helper.getView(R.id.iv_add);
        iv_add.setVisibility(expectListTitleBean.jobIntentCount >= 3 ? View.GONE : View.VISIBLE);

        iv_add.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onClickAddJobIntent();
                }
            }
        });
    }
}
