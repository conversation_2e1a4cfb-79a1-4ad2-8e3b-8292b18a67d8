package com.hpbr.bosszhipin.module.common.provider.requestjobexpect;

import android.content.Context;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.common.adapter.RequestJobExpectAdapter;
import com.hpbr.bosszhipin.module.common.bean.RequestJobStatusBean;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.recycleview.BaseItemProvider;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

/**
 * @ClassName ：RequestJobStautsProvider
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/9  8:08 PM
 */
public class RequestJobStautsProvider extends BaseItemProvider<RequestJobExpectModel, BaseViewHolder> {

    private Context context;
    private RequestJobExpectAdapter.EventListener eventListener;

    public RequestJobStautsProvider(Context context, RequestJobExpectAdapter.EventListener eventListener) {
        this.context = context;
        this.eventListener = eventListener;
    }

    @Override
    public int viewType() {
        return RequestJobExpectModel.TYPE_REQUEST_JOB_STATUS;
    }

    @Override
    public int layout() {
        return R.layout.layout_item_request_job_status;
    }

    @Override
    public void convert(BaseViewHolder helper, RequestJobExpectModel itemModel, int position) {
        if (itemModel == null) return;
        if (!(itemModel.getData() instanceof RequestJobStatusBean)) return;
        RequestJobStatusBean requestJobStatusBean = (RequestJobStatusBean) itemModel.getData();

        ConstraintLayout cl_expect_status = helper.getView(R.id.cl_expect_status);
        MTextView tv_apply_status = helper.getView(R.id.tv_apply_status);
        tv_apply_status.setText(requestJobStatusBean.applyStatusContent);
        cl_expect_status.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (eventListener != null) {
                    eventListener.onApplyStatusClick(requestJobStatusBean.currentWorkStatus);
                }
            }
        });
    }
}
