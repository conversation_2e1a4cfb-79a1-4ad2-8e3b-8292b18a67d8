package com.hpbr.bosszhipin.module.common.request;

import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteBean;
import com.hpbr.bosszhipin.module.commend.entity.AutoCompleteIndexBean;
import com.hpbr.bosszhipin.module.my.activity.boss.brand.bean.CompanyMatchBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import net.bosszhipin.api.*;
import net.bosszhipin.api.bean.*;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * 作者:郭峰
 * 日期:2016/4/5
 * 匹配列表网络请求【品牌列表】和【公司列表】
 */
public class InputMatchRequest {

    public void getSuggestComListV2(String strSearch, final OnCompanySuccessListener onSuccessListener) {
        GetSuggestComListRequest request = new GetSuggestComListRequest(new ApiRequestCallback<GetSuggestComListResponse>() {

            @Override
            public void handleInChildThread(ApiData<GetSuggestComListResponse> data) {
                GetSuggestComListResponse resp = data.resp;
                if (resp != null) {
                    List<ServerCompanySuggestBean> companyList = resp.companyList;
                    List<CompanyMatchBean> matchList = new ArrayList<>();

                    if (!LList.isEmpty(companyList)) {
                        for (ServerCompanySuggestBean suggestBean : companyList) {
                            CompanyMatchBean matchBean = new CompanyMatchBean();
                            matchBean.companyId = suggestBean.comId;
                            matchBean.companyName = suggestBean.name;

                            SuggestHighlightItemBean highlightItem = suggestBean.highlightItem;
                            if (highlightItem != null) {
                                AutoCompleteBean autoCompleteBean = new AutoCompleteBean();
                                autoCompleteBean.textTitle = highlightItem.name;
                                autoCompleteBean.compantId = highlightItem.code;

                                List<HighlightItem> highlightList = highlightItem.highlightList;
                                if (highlightList != null) {
                                    List<AutoCompleteIndexBean> indexList = new ArrayList<>();

                                    for (HighlightItem item : highlightList) {
                                        AutoCompleteIndexBean indexBean = new AutoCompleteIndexBean();
                                        indexBean.startIdx = item.startIndex;
                                        indexBean.endIdx = item.endIndex;
                                        indexList.add(indexBean);
                                    }

                                    autoCompleteBean.indexList = indexList;
                                }

                                matchBean.autoCompleteBean = autoCompleteBean;
                            }
                            matchList.add(matchBean);
                        }
                    }

                    data.setExtendParam("matchList", matchList);

                }
            }

            @Override
            public void onSuccess(ApiData<GetSuggestComListResponse> data) {
                List<CompanyMatchBean> matchList = (List<CompanyMatchBean>) data.getExtendParam("matchList");
                if (onSuccessListener != null) {
                    onSuccessListener.onSuccess(matchList);
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.name = strSearch;
        HttpExecutor.execute(request);
    }

    /**
     * 获得公司匹配列表
     *
     * @param strSearch 搜索公司名称
     */
    public void getCompanyMatchList(String strSearch, final OnCompanySuccessListener onSuccessListener) {
        GetCompanySuggestRequest request = new GetCompanySuggestRequest(new ApiRequestCallback<GetCompanySuggestResponse>() {
            @Override
            public void onSuccess(ApiData<GetCompanySuggestResponse> data) {
                GetCompanySuggestResponse resp = data.resp;

                if (resp != null) {
                    List<SuggestCompanyBean> companyList = resp.itemList;
                    List<CompanyMatchBean> matchList = new ArrayList<>();

                    if (companyList != null && companyList.size() > 0) {
                        for (SuggestCompanyBean suggestBean : companyList) {
                            CompanyMatchBean matchBean = new CompanyMatchBean();
                            matchBean.companyId = LText.getLong(suggestBean.code);
                            matchBean.companyName = suggestBean.name;
                            matchBean.brandName = suggestBean.brandName;

                            AutoCompleteBean autoCompleteBean = new AutoCompleteBean();
                            autoCompleteBean.textTitle = suggestBean.name;
                            autoCompleteBean.compantId = suggestBean.code;

                            List<HighlightItem> highlightList = suggestBean.highlightList;
                            if (highlightList != null) {
                                List<AutoCompleteIndexBean> indexList = new ArrayList<>();

                                for (HighlightItem item : highlightList) {
                                    AutoCompleteIndexBean indexBean = new AutoCompleteIndexBean();
                                    indexBean.startIdx = item.startIndex;
                                    indexBean.endIdx = item.endIndex;
                                    indexList.add(indexBean);
                                }

                                autoCompleteBean.indexList = indexList;
                            }

                            matchBean.autoCompleteBean = autoCompleteBean;

                            matchList.add(matchBean);
                        }

                    }
                    onSuccessListener.onSuccess(matchList);
                }

            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
            }
        });
        request.query = strSearch;
        request.execute();
    }

    /**
     * 品牌搜索匹配列表
     *
     * @param industry  公司行业Code
     * @param strSearch 品牌名称
     */
    public void getBrandMatchList(String industry, String strSearch, final OnBrandSuccessListener onBrandSuccessListener) {
        GetBrandSuggestRequest request = new GetBrandSuggestRequest(new ApiRequestCallback<GetBrandSuggestResponse>() {
            @Override
            public void onSuccess(ApiData<GetBrandSuggestResponse> data) {
                GetBrandSuggestResponse resp = data.resp;

                if (resp != null) {
                    List<ServerBrandSuggestBean> brandList = resp.brandList;
                    onBrandSuccessListener.onSuccess(brandList);
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                onBrandSuccessListener.onFailed(reason);
            }
        });
        request.industry = industry;
        request.name = strSearch;
        HttpExecutor.execute(request);
    }

    public interface OnBrandSuccessListener {
        void onSuccess(List<ServerBrandSuggestBean> brandList);

        void onFailed(ErrorReason reason);
    }

    public interface OnCompanySuccessListener {
        void onSuccess(List<CompanyMatchBean> companyList);
    }
}


