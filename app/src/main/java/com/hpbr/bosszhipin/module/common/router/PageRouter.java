package com.hpbr.bosszhipin.module.common.router;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.common.constant.PageConstant;

/**
 * @ClassName ：PageRouter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/2/8  7:47 PM
 */
public class PageRouter {

    /**
     * 打开「老用户再次活跃」页面
     *
     * @param context
     */
    public static void openGeekOldUserActiveAgainPage(Context context, Bundle data) {
        AppUtil.startUri(context, PageConstant.PATH_GEEK_OLD_USER_ACTIVE_AGAIN, data);
    }

    /**
     * 打开「老用户再次活跃」页面
     *
     * @param context
     */
    public static void openGeekOldUserActiveAgainPageForResult(Context context, Bundle data, int requestCode) {
        AppUtil.startUriForResult(context, PageConstant.PATH_GEEK_OLD_USER_ACTIVE_AGAIN, data, requestCode, ActivityAnimType.DEFAULT);
    }



    /**
     * 打开「其他」期望职位完善页
     */
    public static void openOtherJobGuideComplete(@NonNull Context context, Bundle data) {
        AppUtil.startUri(context, PageConstant.PATH_OTHER_JOB_GUIDE_COMPLETE, data);
    }

}
