package com.hpbr.bosszhipin.module.common.viewmodel;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.MutableLiveData;
import androidx.navigation.NavController;
import androidx.navigation.fragment.NavHostFragment;

import com.bszp.kernel.utils.SingleLiveEvent;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.manager.GeekExpectManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.common.bean.BelongIndustryBean;
import com.hpbr.bosszhipin.module.common.bean.ExpectListTitleBean;
import com.hpbr.bosszhipin.module.common.bean.NewWorkExpEntity;
import com.hpbr.bosszhipin.module.common.bean.PositionNameBean;
import com.hpbr.bosszhipin.module.common.bean.RequestJobStatusBean;
import com.hpbr.bosszhipin.module.common.dialog.RequestJobStatusDialog;
import com.hpbr.bosszhipin.module.common.model.RequestJobExpectModel;
import com.hpbr.bosszhipin.module.common.response.ExpAddSugListResponse;
import com.hpbr.bosszhipin.module.guideprocess.manager.GuideProcessJumpManager;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.login.util.UserInfoUtil;
import com.hpbr.bosszhipin.module.my.activity.geek.manager.ChangeJobIntentTypeManager;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module.my.entity.WorkBean;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.utils.NavControllerUtils;
import com.hpbr.bosszhipin.utils.functions.Consumer;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GeekUpdateBaseInfoRequest;
import net.bosszhipin.api.UserActiveCloseRequest;
import net.bosszhipin.api.UserActiveCloseResponse;
import net.bosszhipin.api.UserUpdateBaseInfoResponse;
import net.bosszhipin.api.WorkExpSaveRequest;
import net.bosszhipin.api.WorkExpSaveResponse;
import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> SheYi
 * @date ：2023/2/8  4:59 PM
 */
public class GeekOldUserActiveAgainViewModel extends BaseViewModel {

    /*获取添加工作经历建议数据*/
    public final MutableLiveData<ExpAddSugListResponse> getExpAddSugListLiveData = new SingleLiveEvent<>();
    /*获取求职期望页面数据成功*/
    public final MutableLiveData<List<RequestJobExpectModel>> getRequestJobExpectData = new SingleLiveEvent<>();
    /*保存求职状态 成功*/
    public final MutableLiveData<RequestJobStatusBean> saveWorkStatusLiveData = new SingleLiveEvent<>();
    /*更新「公司名称」*/
    public final MutableLiveData<String> updateCompanyNameLiveData = new SingleLiveEvent<>();
    /*更新「工作内容」*/
    public final MutableLiveData<String> updateWorkContentLiveData = new SingleLiveEvent<>();
    /*更新「所在行业」*/
    public final MutableLiveData<BelongIndustryBean> updateBelongIndustryLiveData = new SingleLiveEvent<>();
    /*更新「职位名称」*/
    public final MutableLiveData<PositionNameBean> updatePositionNameLiveData = new SingleLiveEvent<>();
    /*重置「添加工作经历」的表单数据*/
    public final MutableLiveData<Boolean> resetAddExpFormLiveData = new SingleLiveEvent<>();
    /*保存工作经历成功*/
    public final MutableLiveData<Boolean> saveWorkExperienceLiveData = new SingleLiveEvent<>();

    public int days;
    public boolean certName;
    public int status;
    public ExtraMapBean extraMapBean;
    /*是否有「回流」完善引导*/
    public boolean isHaveFlowBackCompleteProcess;
    /*「回流」完善引导的页面code*/
    public ArrayList<Integer> flowBackPageCodeList;

    /*是通过 个人优势完成后的回调开启的下一步的页面*/
    public boolean isAdvantageCallBack;
    @Nullable
    public NavController navController;
    /*流程的类型*/
    public @GuideProcessJumpManager.GuideProcessType int guideProcessType;

    public GeekOldUserActiveAgainViewModel(@NonNull Application application) {
        super(application);
    }

    public void initNavController(@NonNull FragmentActivity activity, @IdRes int id) {
        NavHostFragment navHostFragment = (NavHostFragment) activity.getSupportFragmentManager().findFragmentById(id);
        navController = navHostFragment != null ? navHostFragment.getNavController() : null;
    }


    /**
     * 获取添加工作经历建议数据
     */
    public void getExpAddSugList() {
        SimpleApiRequest.GET(GeekUrlConfig.URL_USER_ACTIVE_EXP_ADD_SUG_QUERY)
                .setRequestCallback(new SimpleCommonApiRequestCallback<ExpAddSugListResponse>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<ExpAddSugListResponse> data) {
                        super.onSuccess(data);
                        if (data == null || data.resp == null) return;
                        getExpAddSugListLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                })
                .execute();
    }

    /*获取求职期望页面数据*/
    public void getRequestJobExpectData() {
        showProgress();
        UserInfoUtil userInfoUtil = new UserInfoUtil();
        userInfoUtil.setOnGetUserInfoCallback(new UserInfoUtil.OnGetUserInfoCallback() {
            @Override
            public void onGetUserInfoCompleteCallback() {
                hideProgress();
            }

            @Override
            public void onGetUserInfoCallback(boolean success, String errorMsg) {
                AppThreadFactory.POOL.execute(() -> {
                    List<RequestJobExpectModel> modelList = new ArrayList<>();
                    UserBean user = UserManager.getLoginUser();
                    if (user != null && user.geekInfo != null) {
                        /*「求职状态」*/
                        int currentWorkStatus = user.geekInfo.currentWorkStatus;
                        int graduate = user.geekInfo.graduate;
                        String applyStatusContent = ChangeJobIntentTypeManager.getJobIntentStatus(currentWorkStatus, graduate);
                        modelList.add(new RequestJobExpectModel<>(RequestJobExpectModel.TYPE_REQUEST_JOB_STATUS, new RequestJobStatusBean(currentWorkStatus, applyStatusContent)));
                    }

                    List<JobIntentBean> jobIntentList = GeekExpectManager.getGeekExpectList(user);
                    modelList.add(new RequestJobExpectModel<>(RequestJobExpectModel.TYPE_EXPECT_LIST_TITLE, new ExpectListTitleBean("求职期望", LList.getCount(jobIntentList))));

                    if (LList.getCount(jobIntentList) > 0) {
                        for (JobIntentBean jobIntentBean : jobIntentList) {
                            if (jobIntentBean == null) continue;

                            modelList.add(new RequestJobExpectModel<>(RequestJobExpectModel.TYPE_EXPECT_ITEM, jobIntentBean));
                        }
                    }

                    getRequestJobExpectData.postValue(modelList);
                });
            }
        });
        userInfoUtil.requestUserInfo();
    }

    /**
     * 保存求职状态
     */
    public void saveWorkStatus(int workStatus) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("applyStatus", String.valueOf(workStatus));
        GeekUpdateBaseInfoRequest request = new GeekUpdateBaseInfoRequest(new ApiRequestCallback<UserUpdateBaseInfoResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void handleInChildThread(ApiData<UserUpdateBaseInfoResponse> data) {
                UserUpdateBaseInfoResponse resp = data.resp;
                if (resp != null) {
                    UserBean user = UserManager.getLoginUser();
                    if (user != null && user.geekInfo != null) {
                        user.geekInfo.currentWorkStatus = workStatus;
                        long id = UserManager.save(user);
                        data.setExtendParam("userId", id);
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<UserUpdateBaseInfoResponse> data) {
                long id = (long) data.getExtendParam("userId");
                if (id >= 0) {
                    ToastUtils.showText("保存成功");

                    AppThreadFactory.POOL.execute(() -> {
                        UserBean user = UserManager.getLoginUser();
                        if (user != null && user.geekInfo != null) {
                            /*「求职状态」*/
                            int applyStatus = user.geekInfo.currentWorkStatus;
                            int graduate = user.geekInfo.graduate;
                            String applyStatusContent = ChangeJobIntentTypeManager.getJobIntentStatus(applyStatus, graduate);

                            saveWorkStatusLiveData.postValue(new RequestJobStatusBean(applyStatus, applyStatusContent));
                        }
                    });
                }
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.extra_map = paramMap;
        HttpExecutor.execute(request);
    }

    /**
     * 保存工作经历
     */
    public void saveWorkExperience(@NonNull NewWorkExpEntity workExpEntity) {

        if (workExpEntity.getStartDate() <= 0) {
            ToastUtils.showText("请选择开始时间");
            return;
        }

        WorkBean workBean = new WorkBean();
        workBean.company = workExpEntity.getCompanyName();
        workBean.startDate = workExpEntity.getStartDate();
        workBean.endDate = workExpEntity.getEndDate();
        workBean.responsibility = workExpEntity.getWorkContent();
        workBean.industryName = workExpEntity.getIndustryName();
        workBean.industryCode = workExpEntity.getIndustryCode();
        workBean.positionName = workExpEntity.getPositionName();
        workBean.positionClassIndex = (int) workExpEntity.getPositionClassCode();
        workBean.positionClassName = workExpEntity.getPositionClassName();
        workBean.positionLv2 = workExpEntity.getPositionLv2();
        workBean.isPublic = 0;
        workBean.reportPositionId = workExpEntity.getReportPositionId();
        workBean.reportIndustryId = workExpEntity.getReportIndustryId();

        Map<String, String> paramsMap = getSaveWorkParams(workBean);
        WorkExpSaveRequest request = new WorkExpSaveRequest(new ApiRequestCallback<WorkExpSaveResponse>() {

            @Override
            public void onStart() {
                showProgress();
            }

            @Override
            public void handleInChildThread(ApiData<WorkExpSaveResponse> data) {
                WorkExpSaveResponse resp = data.resp;
                if (resp != null) {
                    workBean.updateId = resp.workId;
                    getUserBean().geekInfo.workList.add(workBean);
                    UserManager.save(getUserBean());
                }
            }

            @Override
            public void onSuccess(ApiData<WorkExpSaveResponse> data) {
                saveWorkExperienceLiveData.postValue(true);
            }

            @Override
            public void onComplete() {
                hideProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.extra_map = paramsMap;
        HttpExecutor.execute(request);
    }

    @NonNull
    private Map<String, String> getSaveWorkParams(@NonNull WorkBean workBean) {
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("position", String.valueOf(workBean.positionClassIndex));//所属职类
        paramsMap.put("positionName", workBean.positionName);//职位名称
        paramsMap.put("company", workBean.company); // 公司
        paramsMap.put("industryCode", workBean.industryCode);
        paramsMap.put("responsibility", workBean.responsibility);//工作内容
        paramsMap.put("startDate", String.valueOf(workBean.startDate));//在职时间-起始时间
        int endDate = workBean.endDate;
        paramsMap.put("endDate", endDate <= 0 ? "" : String.valueOf(endDate));//在职时间-结束时间
        paramsMap.put("customPositionId", String.valueOf(workBean.reportPositionId));
        paramsMap.put("customIndustryId", String.valueOf(workBean.reportIndustryId));
        return paramsMap;
    }

    /**
     * 【用户召回】跳过添加工作经历引导
     */
    public void expanddSugClose() {
        SimpleApiRequest.POST(GeekUrlConfig.URL_USER_ACTIVE_EXP_ADD_SUG_CLOSE)
                .setRequestCallback(new SimpleCommonApiRequestCallback<HttpResponse>() {
                    @Override
                    public void onFailed(ErrorReason reason) {
                    }
                }).execute();
    }


    /**
     * 上报给server，告诉server已经进入过这个流程
     */
    public void completeAction() {
        UserActiveCloseRequest request = new UserActiveCloseRequest(new ApiRequestCallback<UserActiveCloseResponse>() {
            @Override
            public void onSuccess(ApiData<UserActiveCloseResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.status = status;
        request.bizType = getBizType();
        HttpExecutor.execute(request);
    }


    /**
     * 显示选择「求职状态」的弹框
     */
    public void showRequestJobStatusDialogDialog(Activity activity, int currentWorkStatus, Consumer<Long> consumer) {
        final RequestJobStatusDialog requestJobStatusDialog = new RequestJobStatusDialog(new RequestJobStatusDialog.OnChooseWorkStatusListener() {
            @Override
            public void onChooseWorkStatus(@NonNull LevelBean workStatus) {
                if (workStatus.code != currentWorkStatus) {
                    if (consumer != null) consumer.accept(workStatus.code);
                }
            }

            @Override
            public int getInitialWorkStatus() {
                return currentWorkStatus;
            }
        });
        if (activity instanceof FragmentActivity) {
            requestJobStatusDialog.show(((FragmentActivity) activity).getSupportFragmentManager(), "tag_work_status");
        }
    }

    @NonNull
    private UserBean getUserBean() {
        UserBean userBean = UserManager.getLoginUser();
        if (userBean == null) {
            userBean = new UserBean();
        }
        if (userBean.geekInfo == null) {
            userBean.geekInfo = new GeekInfoBean();
        }
        return userBean;
    }


    /**
     * 获取得到bizType
     */
    public int getBizType() {
        if (guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_STUDENT) {
            return GuideProcessJumpManager.PageType.STUDENT;
        } else if (guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR) {
            return GuideProcessJumpManager.PageType.BLUE_COLLAR;
        } else if (guideProcessType == GuideProcessJumpManager.GuideProcessType.GUIDE_PROCESS_TYPE_WORK_EXP) {
            return GuideProcessJumpManager.PageType.WORK_EXP;
        }
        return 0;
    }

    /**
     * 是否可跳过
     */
    public boolean canSkip() {
        return extraMapBean != null && extraMapBean.sp1WorkSkipExpGroup == 1;
    }

    /**
     * 跳转
     */
    public void navigate(@IdRes int resId, @Nullable Bundle args) {
        NavControllerUtils.safeNavigate(navController, resId, args);
    }

    /**
     * 返回
     */
    public void backUp(@NonNull Activity activity) {
        if (navController == null) return;
        if (!navController.navigateUp()) {
            AppUtil.finishActivity(activity);
        }
    }
}
