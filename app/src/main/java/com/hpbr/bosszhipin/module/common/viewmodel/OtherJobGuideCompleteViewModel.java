package com.hpbr.bosszhipin.module.common.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.LevelBeanStringUtils;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.UserActiveCloseRequest;
import net.bosszhipin.api.UserActiveCloseResponse;
import net.bosszhipin.api.bean.ExtraMapBean;
import net.bosszhipin.api.bean.RecPositionBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ：OtherJobGuideCompleteViewModel
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/3/29  4:43 PM
 */
public class OtherJobGuideCompleteViewModel extends BaseViewModel {

    public ExtraMapBean extraMapBean;
    public final JobIntentBean jobIntent = new JobIntentBean();

    public OtherJobGuideCompleteViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 上报给server，告诉server已经进入过这个流程
     */
    public void completeAction(int status) {
        UserActiveCloseRequest request = new UserActiveCloseRequest(new ApiRequestCallback<UserActiveCloseResponse>() {
            @Override
            public void onSuccess(ApiData<UserActiveCloseResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        request.status = status;
        HttpExecutor.execute(request);
    }

    /**
     * 获取职类列表数据
     *
     * @param extraMapBean
     * @return
     */
    @NonNull
    public List<RecPositionBean> getRecPositionBeanList(ExtraMapBean extraMapBean) {
        List<RecPositionBean> recPositionBeanList = new ArrayList<>();
        if (LList.getCount(extraMapBean.recPositions) > 0) {
            recPositionBeanList.addAll(extraMapBean.recPositions);
            recPositionBeanList.add(new RecPositionBean(RecPositionBean.ALL_POSITION_CODE, RecPositionBean.ALL_POSITION_NAME, false));
        }
        return recPositionBeanList;
    }

    @NonNull
    public ArrayList<LevelBean> convertToLevelBeanList(List<RecPositionBean> recPositionBeanList) {
        ArrayList<LevelBean> levelBeanList = new ArrayList<>();
        for (RecPositionBean bean : recPositionBeanList) {
            if (bean == null) continue;
            if (bean.code == RecPositionBean.ALL_POSITION_CODE) continue;
            LevelBean levelBean = new LevelBean();
            levelBean.code = bean.code;
            levelBean.name = bean.name;
            levelBeanList.add(levelBean);
        }
        return levelBeanList;
    }

    public void resetPositions() {
        jobIntent.positionClassName = null;
        jobIntent.positionClassIndex = 0;
        jobIntent.positionClassIndexString = null;
    }

    /**
     * 刷新职位类型的数据
     */
    public void resetPositionClassValue(LevelBean thirdItem) {
        String positionClassName = thirdItem.name;
        String reportPositionName = thirdItem.reportPositionName;
        int positionClassCode = (int) thirdItem.code;
        jobIntent.positionClassName = positionClassName;
        jobIntent.positionClassIndex = positionClassCode;
        jobIntent.positionClassIndexString = String.valueOf(thirdItem.code);
    }

    /**
     * 刷新多期望职位类型的数据
     */
    public void resetMultiPositionClassValue(@NonNull ArrayList<LevelBean> multiPositions) {
        jobIntent.positionClassName = LevelBeanStringUtils.concateCheckedPositionNames(multiPositions, LevelBeanStringUtils.COMMON_SEPERATOR_COMMA);
        jobIntent.positionClassIndexString = LevelBeanStringUtils.concateCheckedPositionCodes(multiPositions);
    }


}
