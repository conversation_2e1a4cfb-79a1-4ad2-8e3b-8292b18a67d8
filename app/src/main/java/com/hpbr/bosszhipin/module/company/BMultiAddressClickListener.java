package com.hpbr.bosszhipin.module.company;

import com.hpbr.bosszhipin.map.Marker;

import net.bosszhipin.api.BMultiAddressBean;

import java.util.List;


public interface BMultiAddressClickListener {
        /**
         * 点击聚合点的回调处理函数
         *
         * @param marker
         *            点击的聚合点
         * @param clusterItems
         *            聚合点所包含的元素
         */
        public void onClick(Marker marker, List<BMultiAddressBean> clusterItems);
}
