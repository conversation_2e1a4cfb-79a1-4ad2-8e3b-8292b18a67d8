package com.hpbr.bosszhipin.module.company;

import net.bosszhipin.api.BMultiAddressBean;

import java.util.List;

/**
 * created by yhy
 * date 2021/10/25
 * desc:
 */
public class MultiAddressDataManager {
    private static final MultiAddressDataManager mSingleInstance = new MultiAddressDataManager();

    public static MultiAddressDataManager getInstance() {
        return mSingleInstance;
    }

    private MultiAddressDataManager() {
    }
    private List<BMultiAddressBean> mData;

    public List<BMultiAddressBean> getData() {
        return mData;
    }

    public MultiAddressDataManager setData(List<BMultiAddressBean> data) {
        mData = data;
        return this;
    }
    public void clearData(){
        if(mData!=null){
            mData.clear();
        }
    }
}
