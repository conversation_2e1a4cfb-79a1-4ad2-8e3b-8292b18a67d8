package com.hpbr.bosszhipin.module.company.activity;


import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.lifecycle.Observer;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.IMapDelegate;
import com.hpbr.bosszhipin.map.MapViewCompat;
import com.hpbr.bosszhipin.map.Marker;
import com.hpbr.bosszhipin.map.location.LatLonPoint;
import com.hpbr.bosszhipin.map.model.CameraPosition;
import com.hpbr.bosszhipin.module.company.BMultiAddressClickListener;
import com.hpbr.bosszhipin.module.company.MultiAddressDataManager;
import com.hpbr.bosszhipin.module.company.entity.CompanyAddressBean;
import com.hpbr.bosszhipin.module.company.fragment.MultiAddressAllFragment;
import com.hpbr.bosszhipin.module.company.other.BMultiAddressOverlay;
import com.hpbr.bosszhipin.module.map.bean.GeekRouteParam;
import com.hpbr.bosszhipin.module.mapcompat.GeekRouteHelper;
import com.hpbr.bosszhipin.utils.PoiLocationTranslator;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.behavior.MultiAddressSheetBehavior;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.BMultiAddressBean;
import net.bosszhipin.api.MultiAddressAreaResponse;
import net.bosszhipin.api.MultiAddressBatchResponse;
import net.bosszhipin.api.bean.ServerGeekHomeAddressInfoBean;
import net.bosszhipin.api.bean.ServerTranslatedPoiAddressBean;
import net.bosszhipin.api.bean.job.ServerJobBaseInfoBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * created by yhy
 * date 2021/9/9
 * desc:
 */
public class MultiAddressActivity extends BaseAwareActivity<MultiAddressModel> implements
        BMultiAddressClickListener, IMapDelegate.OnCameraChangeListener, IMapDelegate.OnMapLoadedCallback {
    public static final String KEY_CITY_CODE = "KEY_CITY_CODE";
    public static final String KEY_HOME_ADDRESS = "KEY_HOME_ADDRESS";
    public static final String KEY_MAP_STYLE = "KEY_MAP_STYLE";
    public static final String KEY_SHOW_TRAFFIC = "KEY_SHOW_TRAFFIC";
    public static final String KEY_JOB = "KEY_JOB";
    public static final String KEY_REQUEST_CODE = "KEY_REQUEST_CODE";
    private final int ZOOM_XINGZHENG_DEFAULT = 10;

    private final int ZOOM_XINGZHENG_BOLDER = 13;

    private final int ZOOM_POI_DEFAULT = 15;
    private AppTitleView mTitleView;
    private final List<BMultiAddressBean> mData = new ArrayList<>();
    private final List<BMultiAddressBean> mAreaData = new ArrayList<>();
    private MapViewCompat mMapView;
    private TextView mTitleTv;
    private TextView mDescTv;
    private TextView mAllTv;
    private TextView mSendBtn;
    private LinearLayout mSendLl;

    private LinearLayout mCommunicateLl;
    private View mRouteBtn;
    private View mCommunicateBtn;

    private ImageView mCloseIv;
    /**
     * B身份进入 没有titleview  只有返回
     */
    private ImageView mBackIv;
    private IMapDelegate mapDelegate;
    private BMultiAddressOverlay mClusterOverlay;
    private LinearLayout mBottomDialogLl;
    private FrameLayout mBottomPanelFl;
    private MultiAddressAllFragment mMultiAddressAllFragment;
    private MultiAddressSheetBehavior<LinearLayout> mBottomSheetBehavior;
    private CoordinatorLayout mCoordinatorLayout;

    public static void startForB(Activity activity, String jobId, String cityCode, int requestCode) {
        Intent starter = new Intent(activity, MultiAddressActivity.class);
        starter.putExtra(Constants.DATA_STRING, jobId);
        starter.putExtra(KEY_CITY_CODE, cityCode);
        starter.putExtra(KEY_REQUEST_CODE, requestCode);
        activity.startActivityForResult(starter, requestCode);
    }

    public static void startForC(Activity activity, String securityId, ServerJobBaseInfoBean jobDetailBean, ServerGeekHomeAddressInfoBean geekHomeAddressInfo, int mapStyle, boolean showTraffic) {
        Intent starter = new Intent(activity, MultiAddressActivity.class);
        starter.putExtra(Constants.DATA_STRING, securityId);
        starter.putExtra(KEY_HOME_ADDRESS, geekHomeAddressInfo);
        starter.putExtra(KEY_JOB, jobDetailBean);
        starter.putExtra(KEY_MAP_STYLE, mapStyle);
        starter.putExtra(KEY_SHOW_TRAFFIC, showTraffic);
        activity.startActivity(starter);
    }

    @Override
    protected void onBeforeCreate(Bundle savedInstanceState) {
        super.onBeforeCreate(savedInstanceState);
        MapViewCompat.initializer(AndroidDataStarGray.getInstance().isUserAMapTypeV3() ? MapViewCompat.A_MAP_TYPE : MapViewCompat.B_MAP_TYPE);
    }

    @Override
    protected int contentLayout() {
        return R.layout.act_b_multi_address;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        useLightStatusBar();
        initView(savedInstanceState);

        if (UserManager.isBossRole()) {
            mSendLl.setVisibility(View.VISIBLE);
            mCommunicateLl.setVisibility(View.GONE);
            mTitleView.setVisibility(View.GONE);
            mBackIv.setVisibility(View.VISIBLE);
            mBackIv.setImageResource(R.mipmap.ic_action_back_white);
            makeStatusBarTransparent();
        } else {
            mSendLl.setVisibility(View.GONE);
            mCommunicateLl.setVisibility(View.VISIBLE);
            if (getJobBean() != null) {
                String positionName = getJobBean().positionName;
                String salary = getJobBean().salaryDesc;
                mTitleView.getTitleTextView().setEllipsize(TextUtils.TruncateAt.MIDDLE);
                mTitleView.setTitle(String.format("%1$s (%2$s)", positionName, salary));
            }

            mTitleView.setVisibility(View.VISIBLE);
            mBackIv.setVisibility(View.GONE);

            AnalyticsFactory.create().action("more-address-page-show").param("p2", getJobBean() == null ? 0 : getJobBean().jobId).build();
        }

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams) mBottomDialogLl.getLayoutParams();
        params.height = DisplayHelper.getScreenHeight(this) - DisplayHelper.dp2px(this, 30);
        mBottomDialogLl.setLayoutParams(params);

        int logoBottomMargin = ZPUIDisplayHelper.dp2px(this, 24);
        mMapView.showMapContentApprovalNumber(logoBottomMargin);
        mapDelegate.setLogoBottomMargin(logoBottomMargin);
        mapDelegate.setOnMapLoadedListener(this);
        mapDelegate.getUiSettings().setRotateGesturesEnabled(false);
        mapDelegate.getUiSettings().setTiltGesturesEnabled(false);


        initData();
        initBehavior();

        showProgressDialog();
        if (UserManager.isBossRole()) {
            mViewModel.requestAddress(getIntent().getStringExtra(KEY_CITY_CODE), getIntent().getStringExtra(Constants.DATA_STRING), "");
        } else {
            if (getJobBean() != null) {
                mViewModel.requestAddress(String.valueOf(getJobBean().location),
                        "", getIntent().getStringExtra(Constants.DATA_STRING)
                );
            }

        }
        dealViewModel();
    }

    private void dealViewModel() {
        mViewModel.mErrorData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String s) {
                ToastUtils.showText(s);
            }
        });
        mViewModel.mShowLoading.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (!aBoolean) {
                    dismissProgressDialog();
                }
            }
        });

        mViewModel.mResultData.observe(this, new Observer<MultiAddressBatchResponse>() {
            @Override
            public void onChanged(MultiAddressBatchResponse multiAddressBatchResponse) {

                HashMap<String, Integer> areaAddressCountMap = new HashMap<>();

                if (UserManager.isGeekRole()) {
                    if (multiAddressBatchResponse.cAddressResponse == null
                            || LList.getCount(multiAddressBatchResponse.cAddressResponse.jobAddressList) == 0) {
                        return;
                    }  //  把C 端 的模型 转换为B 端的
                    List<CompanyAddressBean> jobAddressList = multiAddressBatchResponse.cAddressResponse.jobAddressList;
                    for (CompanyAddressBean bean : jobAddressList) {
                        if (bean == null) continue;
                        BMultiAddressBean temp = new BMultiAddressBean();
                        temp.latitude = bean.latitude;
                        temp.longitude = bean.longitude;
                        temp.desc = bean.locationDesc;
                        temp.encRelationId = bean.jobAddressId;
                        temp.addressText = bean.addressTitle;
                        temp.areaCode = bean.areaCode;
                        mData.add(temp);
                        if (areaAddressCountMap.containsKey(temp.areaCode)) {
                            int count = areaAddressCountMap.get(temp.areaCode);
                            count += 1;
                            areaAddressCountMap.put(temp.areaCode, count);
                        } else {
                            areaAddressCountMap.put(temp.areaCode, 1);
                        }
                    }
                } else {
                    if (multiAddressBatchResponse.bAddressResponse == null
                            || LList.getCount(multiAddressBatchResponse.bAddressResponse.list) == 0) {
                        return;
                    }
                    for (BMultiAddressBean bean : multiAddressBatchResponse.bAddressResponse.list) {
                        if (areaAddressCountMap.containsKey(bean.areaCode)) {
                            int count = areaAddressCountMap.get(bean.areaCode);
                            count += 1;
                            areaAddressCountMap.put(bean.areaCode, count);
                        } else {
                            areaAddressCountMap.put(bean.areaCode, 1);
                        }
                    }
                    mData.addAll(multiAddressBatchResponse.bAddressResponse.list);
                    reCombineData(mData);
                }
                // areaAddressCountMap 里装的是  11000，4 这样的数据， 代表 朝阳区 4个地址。
                if (multiAddressBatchResponse.areaResponse != null) {
                    List<MultiAddressAreaResponse.MultiAddressAreaBean> gpsList = multiAddressBatchResponse.areaResponse.gpsList;

                    for (MultiAddressAreaResponse.MultiAddressAreaBean multiAddressAreaBean : gpsList) {
                        if (multiAddressAreaBean == null) continue;
                        BMultiAddressBean temp = new BMultiAddressBean();
                        temp.latitude = multiAddressAreaBean.midlat;
                        temp.longitude = multiAddressAreaBean.midlong;
                        temp.addressText = multiAddressAreaBean.typeName;
                        temp.areaCode = multiAddressAreaBean.typeCode;
                        temp.level = BMultiAddressBean.LEVEL_XINGZHENG;

                        if (areaAddressCountMap.containsKey(temp.areaCode)) {
                            temp.addressCount = areaAddressCountMap.get(temp.areaCode);
                            mAreaData.add(temp);
                        }
                    }
                }


                // 临 灰度 ，改动  没有行政区 就不做聚合，因为有的县级市没有区
                if (LList.getCount(mAreaData) > 0 && LList.getCount(mData) > 0) {
                    BMultiAddressBean area1 = mAreaData.get(0);

                    // 转换区域地址
                    if (area1 != null) {
                        mUserMoveCamera = false;
                        double lat = area1.latitude;
                        double lon = area1.longitude;
                        mapDelegate.moveCamera(lat, lon, ZOOM_XINGZHENG_DEFAULT);
                    }
                    mAllTv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mClusterOverlay.setItems(mAreaData);

                        }
                    }, 300);

                } else if (LList.getCount(mData) > 0) {
                    BMultiAddressBean poi = mData.get(0);
                    if (poi != null) {
                        mUserMoveCamera = false;
                        mapDelegate.moveCamera(poi.latitude, poi.longitude, ZOOM_POI_DEFAULT);
                    }
                    mAllTv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mClusterOverlay.setItems(mData);

                        }
                    }, 300);
                } else {
                    return;
                }


                mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_HALF_EXPANDED);
                //
                if (LList.getCount(mData) > 0) {
                    MultiAddressDataManager.getInstance().setData(mData);
                    getSupportFragmentManager().beginTransaction().replace(R.id.container_fl, mMultiAddressAllFragment, "").commitAllowingStateLoss();
                }
            }
        });
    }

    private void initData() {

        mTitleView.setBackClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                finish();
            }
        });
        mBackIv.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                finish();
            }
        });
        mClusterOverlay = new BMultiAddressOverlay(mapDelegate, mData,
                this);
        mClusterOverlay.setOnClusterClickListener(this);

        mCloseIv.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (mBottomSheetBehavior.getState() != MultiAddressSheetBehavior.STATE_HIDDEN) {
                    mOriginBehaviorState = mBottomSheetBehavior.getState();
                }
//                mAddressRv.setVisibility(View.VISIBLE);
//                mMultiAddressAllFragment.switchToIndex(0);
                mAllTv.setVisibility(View.VISIBLE);
                mBottomPanelFl.setVisibility(View.GONE);
                itemSelectReset(mData);
                itemSelectReset(mAreaData);
                mClusterOverlay.setItems(mData);
                if (mOriginBehaviorState == MultiAddressSheetBehavior.STATE_EXPANDED || mOriginBehaviorState == MultiAddressSheetBehavior.STATE_HALF_EXPANDED || mOriginBehaviorState == MultiAddressSheetBehavior.STATE_COLLAPSED) {
                    mBottomSheetBehavior.setState(mOriginBehaviorState);
                }
                mCloseIv.post(new Runnable() {
                    @Override
                    public void run() {
                        mBottomSheetBehavior.setHideable(false);
                    }
                });

            }
        });


        mMultiAddressAllFragment = MultiAddressAllFragment.newInstance();
        mMultiAddressAllFragment.setOnMultiAddressActionListener(new OnMultiAddressActionListener() {
            @Override
            public void onListClickListener(BMultiAddressBean bean) {
                if (mBottomSheetBehavior.getState() != MultiAddressSheetBehavior.STATE_HIDDEN) {
                    mOriginBehaviorState = mBottomSheetBehavior.getState();
                }
                mSelectBean = bean;
                mBottomSheetBehavior.setHideable(true);
                mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_HIDDEN);
                itemSelect(mData, bean);
                selectArea(mAreaData, bean.areaCode);
                mUserMoveCamera = false;
                mapDelegate.moveCamera(bean.latitude, bean.longitude, ZOOM_POI_DEFAULT);
                mCloseIv.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mClusterOverlay.setItems(mData);
                    }
                }, 300);

                showDetailAddress(bean);

                if(bean != null && getJobBean() != null) {
                    AnalyticsFactory.create().action("addresspage-address-click")
                            .param("p2", getJobBean().jobId)
                            .param("p3",bean.encRelationId)
                            .build();
                }

            }

            @Override
            public void onSearchClickListener(BMultiAddressBean bean) {
                if (mMultiAddressAllFragment != null) {
                    mMultiAddressAllFragment.hideSoftBoard();
                    mCloseIv.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            mBottomSheetBehavior.setHideable(true);
                            mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_HIDDEN);
//                            mMultiAddressAllFragment.switchToIndex(0);
                        }
                    }, 100);

                }
                if (mBottomSheetBehavior.getState() != MultiAddressSheetBehavior.STATE_HIDDEN) {
                    mOriginBehaviorState = mBottomSheetBehavior.getState();
                }
                mSelectBean = bean;
                itemSelect(mData, bean);
                selectArea(mAreaData, bean.areaCode);
                mUserMoveCamera = false;

                mapDelegate.moveCamera(bean.latitude, bean.longitude, ZOOM_POI_DEFAULT);
                mCloseIv.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mClusterOverlay.setItems(mData);
                    }
                }, 300);

                showDetailAddress(bean);
            }

            @Override
            public void onSearchCancelListener() {
                mMultiAddressAllFragment.switchToIndex(0);
            }
        });
    }

    private void initView(Bundle savedInstanceState) {
        mCoordinatorLayout = findViewById(R.id.coordinator_layout);
        mBackIv = findViewById(R.id.back_iv);
        mTitleView = findViewById(R.id.title_view);
        mTitleView.setTitle("");
        mTitleView.setDividerInvisible();
        mBottomPanelFl = findViewById(R.id.bottom_panel_fl);
        mCloseIv = findViewById(R.id.close_iv);
        mMapView = findViewById(R.id.map_view);
        mTitleTv = findViewById(R.id.title_tv);
        mDescTv = findViewById(R.id.desc_tv);

        mSendBtn = findViewById(R.id.bottom_btn);
        mSendLl = findViewById(R.id.bottom_send_ll);
        mCommunicateLl = findViewById(R.id.bottom_communicate_ll);

        mRouteBtn = findViewById(R.id.route_btn);
        mCommunicateBtn = findViewById(R.id.communicate_btn);


        mAllTv = findViewById(R.id.all_tv);
        mBottomDialogLl = findViewById(R.id.container_ll);
        mBottomSheetBehavior = MultiAddressSheetBehavior.from(mBottomDialogLl);
        mMapView.onCreate(savedInstanceState);
        mapDelegate = mMapView.getMap();
        mapDelegate.setOnCameraChangeListener(this);
    }

    public void setBehaviorState(int state) {
        mBottomSheetBehavior.setState(state);
    }

    private void initBehavior() {
        mBottomSheetBehavior.setBottomSheetCallback(new MultiAddressSheetBehavior.BottomSheetCallback() {

            //BottomSheet状态改变时的回调
            @Override
            public void onStateChanged(@NonNull View bottomSheet, int newState) {
                if (newState == MultiAddressSheetBehavior.STATE_HALF_EXPANDED || newState == MultiAddressSheetBehavior.STATE_COLLAPSED) {
                    if (mMultiAddressAllFragment != null && mMultiAddressAllFragment.getCurrentItem() == 1) {
                        if (newState == MultiAddressSheetBehavior.STATE_HALF_EXPANDED) {
                            mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_COLLAPSED);
                        }
                        mCloseIv.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                mMultiAddressAllFragment.switchToIndex(0);
                            }
                        }, 300);
                    }
                }
            }

            //BottomSheet滑动时的回调
            @Override
            public void onSlide(@NonNull View bottomSheet, float slideOffset) {


            }
        });
    }

    private boolean mUserMoveCamera = false;


    @Override
    public void onCameraChange(CameraPosition cameraPosition) {

    }

    @Override
    public void onCameraChangeFinish(CameraPosition cameraPosition) {
        float zoom = cameraPosition.zoom;
        if (mUserMoveCamera) { //用户自己手动缩放的话  才重新绘制数据
            if (zoom <= ZOOM_XINGZHENG_BOLDER && LList.getCount(mAreaData) > 0) {
                mClusterOverlay.setItems(mAreaData);
            } else {
                mClusterOverlay.setItems(mData);
            }
        }
        mUserMoveCamera = true;
    }

    @Override
    public void onMapLoaded() {
    }

    /**
     * 记录弹窗原有状态， 反选后恢复
     */
    private int mOriginBehaviorState = MultiAddressSheetBehavior.STATE_HALF_EXPANDED;
    private BMultiAddressBean mSelectBean;

    @Override
    public void onClick(Marker marker, List<BMultiAddressBean> bMultiAddressBeans) {
        if (!LList.isEmpty(bMultiAddressBeans)) {
            BMultiAddressBean bean = bMultiAddressBeans.get(0);
            if (bean.level == BMultiAddressBean.LEVEL_XINGZHENG) {
                mClusterOverlay.setItems(mData);
                if (mSelectBean != null) {
                    mUserMoveCamera = false;
                    mapDelegate.moveCamera(mSelectBean.latitude, mSelectBean.longitude, ZOOM_POI_DEFAULT);
                } else {
                    LatLonPoint latLng = findFirstAddressByAreaCode(bean.areaCode);
                    if (latLng != null) {
                        mapDelegate.moveCamera(latLng.latitude, latLng.longitude, ZOOM_POI_DEFAULT);
                    }

                }
            } else {
                if (!bean.select) {
                    mSelectBean = bean;
                    if (mBottomSheetBehavior.getState() != MultiAddressSheetBehavior.STATE_HIDDEN) {
                        mOriginBehaviorState = mBottomSheetBehavior.getState();
                    }
                    itemSelect(mData, bean);
                    selectArea(mAreaData, bean.areaCode);
                    mUserMoveCamera = false;
                    mBottomSheetBehavior.setHideable(true);
                    mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_HIDDEN);
                    showDetailAddress(bean);
                    mapDelegate.moveCamera(bean.latitude, bean.longitude, 0);
                } else { //取消选中
                    mSelectBean = null;
                    itemSelectReset(mData);
                    itemSelectReset(mAreaData);
                    mBottomPanelFl.setVisibility(View.GONE);
                    mCloseIv.post(new Runnable() {
                        @Override
                        public void run() {
                            mBottomSheetBehavior.setHideable(false);
                        }
                    });
                    if (mOriginBehaviorState == MultiAddressSheetBehavior.STATE_EXPANDED || mOriginBehaviorState == MultiAddressSheetBehavior.STATE_HALF_EXPANDED || mOriginBehaviorState == MultiAddressSheetBehavior.STATE_COLLAPSED) {
                        mBottomSheetBehavior.setState(mOriginBehaviorState);
                    } //原来是什么状态 此处反选后恢复什么状态

                }
                mClusterOverlay.setItems(mData);
            }

        }
    }

    private void showDetailAddress(BMultiAddressBean bean) {
        mBottomPanelFl.setVisibility(View.VISIBLE);
//        mAddressRv.setVisibility(View.GONE);
        mAllTv.setVisibility(View.GONE);
        mTitleTv.setText(bean.addressText);
        mDescTv.setText(bean.desc);
        // B 端显示的按钮
        mSendBtn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                ServerTranslatedPoiAddressBean addressBean = new ServerTranslatedPoiAddressBean();
                addressBean.poiLatitude = bean.latitude;
                addressBean.poiLongitude = bean.longitude;
                StringBuilder stringBuilder = new StringBuilder();
                if (!LText.empty(bean.poiTitle)) {
                    stringBuilder.append(bean.poiTitle);
                }
                if (!LText.empty(bean.roomInfo)) {
                    stringBuilder.append(bean.roomInfo);
                }
                //多职位case ,用于上传locationDesc
                addressBean.poiTitle = stringBuilder.toString();
                addressBean.poiCity = bean.city;
                addressBean.poiCityCode = LText.getInt(bean.cityCode);

                addressBean.geoId = bean.geoId;
                addressBean.poiArea = bean.area;
                addressBean.poiProvince = bean.province;
                addressBean.roomInfo = bean.roomInfo;
                exportExtraAddressResult(addressBean, bean);

                Intent intent = new Intent();
                intent.putExtra(PoiLocationTranslator.POI_TRANSLATE_ADDRESS, addressBean);
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });
        //C 端
        mCommunicateBtn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                Intent intent = new Intent(Constants.RECEIVER_JOB_DETAIL_MULTI_ADDRESS_TOCHAT);
                intent.putExtra(Constants.DATA_JOB_ADDRESS_ID, bean.encRelationId);
                ReceiverUtils.sendBroadcast(MultiAddressActivity.this, intent);
                finish();
            }
        });
        mRouteBtn.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (getJobBean() != null) {
                    GeekRouteHelper.gotoThisActivity(MultiAddressActivity.this, new GeekRouteParam()
                            .setTargetAddress(bean.addressText)
                            .setCity(getJobBean().locationName)
                            .setLatitude(bean.latitude)
                            .setLongitude(bean.longitude)
                            .setHomeAddress(getHomeAddressBean())
                            .setJobId(getJobBean().jobId)
                            .setCityCode(getJobBean().location)
                            .setSource(GeekRouteHelper.SOURCE_FROM_MAP)
                            .setGuideSource(GeekRouteHelper.SOURCE_GUIDE_JD)
                            .setSecurityId(getIntent().getStringExtra(Constants.DATA_STRING))
                    );

                }

            }
        });

    }

    private void itemSelect(List<BMultiAddressBean> list, BMultiAddressBean region) {
        for (BMultiAddressBean item : list) {
            item.select = region.addressText.equals(item.addressText) && region.latitude == item.latitude;
        }
    }

    /**
     * 选择行政区
     *
     * @param list
     */
    private void selectArea(List<BMultiAddressBean> list, String areaCode) {
        for (BMultiAddressBean item : list) {
            item.select = areaCode.equals(item.areaCode);
        }
    }


    private void itemSelectReset(List<BMultiAddressBean> list) {
        for (BMultiAddressBean item : list) {
            if (item == null) continue;
            item.select = false;
        }
    }

    //    private int mResumeState;
    @Override
    public void onResume() {
        super.onResume();
        mMapView.onResume();

        if (mBottomSheetBehavior != null) {  // 据说弹窗弹起，锁屏后 一段时间，底部会出现空白。  没有复现，兜底策略吧
            mCloseIv.post(new Runnable() {
                @Override
                public void run() {
//                    if(mResumeState!=0){
//                        mBottomSheetBehavior.setState(mResumeState);
//                    }
                    int state = mBottomSheetBehavior.getState();
                    if (state == MultiAddressSheetBehavior.STATE_EXPANDED || state == MultiAddressSheetBehavior.STATE_HALF_EXPANDED || state == MultiAddressSheetBehavior.STATE_COLLAPSED) {
                        mBottomSheetBehavior.setState(state);
                    }
                }
            });
        }
    }
/*

    @Override
    protected void onStop() {
        super.onStop();
        mResumeState = mBottomSheetBehavior.getState();
        mBottomSheetBehavior.setState(STATE_COLLAPSED);
    }
*/

    @Override
    public void onPause() {
        super.onPause();
        mMapView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
        if (mClusterOverlay != null) {
            mClusterOverlay.onDestroy();
        }
        MultiAddressDataManager.getInstance().clearData();
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        mMapView.onSaveInstanceState(outState);
    }

    private LatLonPoint findDefaultPoi(List<BMultiAddressBean> list) {
        if (LList.isEmpty(list)) return null;
        for (BMultiAddressBean bean : list) {
            if (bean.cover == 1 && bean.latitude != 0 && bean.longitude != 0) {
                return new LatLonPoint(bean.latitude, bean.longitude);
            }
        }
        return null;
    }

    private LatLonPoint findFirstAddressByAreaCode(String areaCode) {
        for (BMultiAddressBean bean : mData) {
            if (bean.areaCode.equals(areaCode)) {
                return new LatLonPoint(bean.latitude, bean.longitude);
            }
        }
        return null;
    }

    private void reCombineData(List<BMultiAddressBean> list) {
        if (LList.isEmpty(list)) return;
        for (BMultiAddressBean bean : list) {
            bean.desc = StringUtil.connectTextWithChar(" · ", bean.city, bean.area, bean.businessName);
        }
    }

    public interface OnMultiAddressActionListener {
        void onListClickListener(BMultiAddressBean bean);

        void onSearchClickListener(BMultiAddressBean bean);

        void onSearchCancelListener();
    }

    public String dealStringLength(String str, int length) {
        if (TextUtils.isEmpty(str) || str.length() <= length) return str;

        return str.substring(0, length) + "...";
    }

    public ServerJobBaseInfoBean getJobBean() {
        if (getIntent() != null) {
            return (ServerJobBaseInfoBean) getIntent().getSerializableExtra(KEY_JOB);
        }

        return null;
    }

    public ServerGeekHomeAddressInfoBean getHomeAddressBean() {
        if (getIntent() != null) {
            return (ServerGeekHomeAddressInfoBean) getIntent().getSerializableExtra(KEY_HOME_ADDRESS);
        }

        return null;
    }

    public int getRequestCode() {
        if (getIntent() != null) {
            return getIntent().getIntExtra(KEY_REQUEST_CODE, 0);
        }

        return 0;
    }

    /**
     * 针对某些业务场景需要额外对外暴露的地址数据
     * @return
     */
    private void exportExtraAddressResult(ServerTranslatedPoiAddressBean resultAddressBean, BMultiAddressBean bean) {
        // 数据为null，不处理，直接返回原值
        if (resultAddressBean == null || bean == null) {
            return;
        }

        // ChatBaseActivity.REQ_SELECT_LOCATION = 201
        if (getRequestCode() == 201) {
            resultAddressBean.poiProvince = bean.province;
            resultAddressBean.poiCity = bean.city;
            resultAddressBean.poiArea = bean.area;
            resultAddressBean.poiStreet = bean.area;
            resultAddressBean.snippet = bean.poiTitle;
            resultAddressBean.roomInfo = bean.roomInfo;
        }
    }

    private int getMapStyle() {
        if (getIntent() != null) {
            return getIntent().getIntExtra(KEY_MAP_STYLE, 0);
        }

        return 0;
    }

    private boolean showTrafficTab() {
        if (getIntent() != null) {
            return getIntent().getBooleanExtra(KEY_SHOW_TRAFFIC, false);
        }
        return false;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mBottomSheetBehavior != null && (mBottomSheetBehavior.getState() == MultiAddressSheetBehavior.STATE_HALF_EXPANDED
                    || mBottomSheetBehavior.getState() == MultiAddressSheetBehavior.STATE_EXPANDED)) {
                mBottomSheetBehavior.setState(MultiAddressSheetBehavior.STATE_COLLAPSED);
            } else {
                AppUtil.finishActivity(this);
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}