package com.hpbr.bosszhipin.module.company.activity;

import android.app.Application;

import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.MultiAddressAreaRequest;
import net.bosszhipin.api.MultiAddressBRequest;
import net.bosszhipin.api.MultiAddressBatchRequest;
import net.bosszhipin.api.MultiAddressBatchResponse;
import net.bosszhipin.api.MultiAddressCRequest;
import net.bosszhipin.base.ApiRequestCallback;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;


public class MultiAddressModel extends BaseViewModel {

    public MutableLiveData<Boolean> mShowLoading = new MutableLiveData<>();

    public MutableLiveData<MultiAddressBatchResponse> mResultData = new MutableLiveData<>();

    public MutableLiveData<String> mErrorData = new MutableLiveData<>();

    public MultiAddressModel(@NonNull Application application) {
        super(application);
    }

    public void requestAddress(String cityCode,String jobId,String securityId) {
        MultiAddressBatchRequest batchRequest = new MultiAddressBatchRequest(new ApiRequestCallback<MultiAddressBatchResponse>() {
            @Override
            public void onSuccess(ApiData<MultiAddressBatchResponse> data) {
                mResultData.setValue(data.resp);
            }

            @Override
            public void onComplete() {
                mShowLoading.setValue(false);
            }

            @Override
            public void onFailed(ErrorReason reason) {
                mErrorData.setValue(reason.getErrReason());
            }
        });
        MultiAddressAreaRequest areaRequest = new MultiAddressAreaRequest();
        areaRequest.cityCode = cityCode;
        batchRequest.mMultiAddressAreaRequest = areaRequest;

        if (UserManager.isBossRole()) {
            MultiAddressBRequest bRequest = new MultiAddressBRequest();
            bRequest.jobId = jobId;
            batchRequest.mAddressBRequest = bRequest;
        } else {
            MultiAddressCRequest cRequest = new MultiAddressCRequest();
            cRequest.securityId = securityId;
            batchRequest.mAddressCRequest = cRequest;
        }
        batchRequest.execute();
    }

}
