package com.hpbr.bosszhipin.module.company.activity;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static com.facebook.drawee.drawable.ScalingUtils.ScaleType.CENTER_CROP;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SnapHelper;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.company.export.CompanyConsts;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.company.entity.StoreCompanyBusinessInfo;
import com.hpbr.bosszhipin.module.imageviewer.ExtraParams;
import com.hpbr.bosszhipin.module.imageviewer.Image;
import com.hpbr.bosszhipin.module.imageviewer.IntentBuilder;
import com.hpbr.bosszhipin.module.map.activity.WorkLocationReviewActivity;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.snap.StartSnapHelper;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.CollapseTextView2;
import com.twl.ui.flexbox.StringTagAdapter;
import com.twl.ui.flexbox.widget.TagFlowLayout;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.StoreCompanyNewInfoQueryRequest;
import net.bosszhipin.api.GeekBlueBrandInfoRequest;
import net.bosszhipin.api.GeekBlueBrandInfoResponse;
import net.bosszhipin.api.StoreBrandInfoBatchRequest;
import net.bosszhipin.api.StoreBrandInfoBatchResponse;
import net.bosszhipin.api.bean.ServerBlueBrandAddressInfo;
import net.bosszhipin.api.bean.ServerBlueBrandInfo;
import net.bosszhipin.api.bean.ServerBlueBrandPictrueBean;
import net.bosszhipin.api.bean.ServerBlueBrandWelfareInfo;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * 作者：fanfan
 */

public class StoreHomepageDetailActivity extends BaseActivity {


    private String securityId;
    private ConstraintLayout clTitle;
    private ConstraintLayout clStroeBaseInfo;
    private ConstraintLayout clStoreAddressInfo;
    private ConstraintLayout clStroeIntroduceInfo;
    private ConstraintLayout clStoreEnvironmentinfo;

    private TextView miniTitleText;
    private ImageView ivback;
    private MTextView tvBossName;
    private MTextView tvStroeInfo;
    private SimpleDraweeView ivAvatar;
    private MTextView tvAddressinfo;
    private TagFlowLayout tagBounus;
    private CollapseTextView2 storeDescription;
    private RecyclerView rcEnvironment;
    private ConstraintLayout companyDetailTitle;
    private ConstraintLayout title;
    private ImageView ivBack;
    private ConstraintLayout clStoreBaseInfo;
    private MTextView tvStoreInfo;
    private MTextView tvAddressTitle;
    private MTextView tvAddressInfo;
    private ImageView ivArrow;
    private ConstraintLayout clStoreIntroduce;
    private MTextView tvIntroduceTitle;
    private TagFlowLayout flowBonusList;
    private CollapseTextView2 tvDescription;
    private ConstraintLayout clEnvironment;
    private MTextView tvEnvironmentTitle;
    private RecyclerView rvCompanyPictures;
    private MTextView tvTitle;
    private MTextView tvSeeAll;
    private MTextView tvCompanyNameTitle;
    private MTextView tvCompanyName;
    private LinearLayout llLegal;
    private MTextView tvLegalTitle;
    private MTextView tvLegal;
    private LinearLayout llCapital;
    private MTextView tvCapitalTitle;
    private MTextView tvCapital;
    private LinearLayout llRegTime;
    private MTextView tvRegTimeTitle;
    private MTextView tvRegTime;
    private LinearLayout llDataSource;
    private MTextView tvDataSourceTitle;
    private SimpleDraweeView tvDataSourceIv;
    private RecyclerView supplementInfoRv;
    private LinearLayout llCompanyBusiness;

    public static void start(Context context, String securityId) {
        Intent intent = new Intent(context, StoreHomepageDetailActivity.class);
        intent.putExtra(Constants.DATA_STRING, securityId);
        AppUtil.startActivity(context, intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        parseData();
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        setContentView(R.layout.activity_store_detail);
        initViews();
        requestDetailData();
    }

    private void parseData() {

        Intent intent = getIntent();
        securityId = intent.getStringExtra(Constants.DATA_STRING);
    }

    private void initViews() {
        initTitle();
        initStoreBaseInfoLayout();
        initStoreAddressInfoLayout();
        initStoreIntroduceLayout();
        initStoreEnvironmentLayout();
        initBusinessView();

    }

    private void initTitle() {
        clTitle = findViewById(R.id.title);
        miniTitleText = findViewById(R.id.miniTitleText);
        ivback = findViewById(R.id.iv_back);
        ivback.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

    }

    private void initStoreEnvironmentLayout() {
        clStoreEnvironmentinfo = findViewById(R.id.cl_environment);
        rcEnvironment = findViewById(R.id.rv_company_pictures);
        rcEnvironment.setNestedScrollingEnabled(false);

        SnapHelper snapHelper = new StartSnapHelper();
        snapHelper.attachToRecyclerView(rcEnvironment);

        rcEnvironment.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.right = Scale.dip2px(getBaseContext(), 10);
            }
        });

    }

    private void initStoreIntroduceLayout() {
        clStroeIntroduceInfo = findViewById(R.id.cl_store_introduce);
        tagBounus = findViewById(R.id.flow_bonus_list);
        storeDescription = findViewById(R.id.tv_description);
        storeDescription.initWidth(ZPUIDisplayHelper.getScreenWidth(getBaseContext()) - Scale.dip2px(getBaseContext(), 60));
        storeDescription.setExpandText("阅读全文");
        storeDescription.setMaxLines(3);
        storeDescription.setShowCollapseFeature(true, 3);
    }

    private void initStoreAddressInfoLayout() {
        clStoreAddressInfo = findViewById(R.id.cl_store_addressInfo);
        tvAddressinfo = findViewById(R.id.tv_address_info);
    }

    private void initStoreBaseInfoLayout() {
        clStroeBaseInfo = findViewById(R.id.cl_store_baseInfo);
        tvStroeInfo = findViewById(R.id.tv_store_info);
        tvBossName = findViewById(R.id.tv_boss_name);
//        ivAvatar = findViewById(R.id.iv_ivatar);
    }


    private void requestDetailData() {
        StoreBrandInfoBatchRequest batchRequest = new StoreBrandInfoBatchRequest(new ApiRequestCallback<StoreBrandInfoBatchResponse>() {
            @Override
            public void onSuccess(ApiData<StoreBrandInfoBatchResponse> data) {
                if (data != null && data.resp != null) {
                    if(null!=data.resp.geekBlueBrandInfoResponse){
                        flushData(data.resp.geekBlueBrandInfoResponse);
                    }
                    if (null != data.resp.storeCompanyNewInfoQueryResponse && null != data.resp.storeCompanyNewInfoQueryResponse.businessInfo) {
                        llCompanyBusiness.setVisibility(View.VISIBLE);
                        handleBusinessData(data.resp.storeCompanyNewInfoQueryResponse.businessInfo);
                    } else {
                        llCompanyBusiness.setVisibility(View.GONE);
                    }
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        GeekBlueBrandInfoRequest geekBlueBrandInfoRequest = new GeekBlueBrandInfoRequest();
        geekBlueBrandInfoRequest.securityId = securityId;
        batchRequest.geekBlueBrandInfoRequest = geekBlueBrandInfoRequest;

        StoreCompanyNewInfoQueryRequest companyNewInfoQueryRequest = new StoreCompanyNewInfoQueryRequest();
        companyNewInfoQueryRequest.securityId = securityId;
        batchRequest.companyNewInfoQueryRequest = companyNewInfoQueryRequest;
        batchRequest.execute();
    }

    private void handleBusinessData(StoreCompanyBusinessInfo companyInfo){
        tvCompanyName.setText(companyInfo.name);
        tvLegal.setText(companyInfo.legalPerson,LText.empty(companyInfo.legalPerson) ? View.GONE : View.VISIBLE);
        tvCapital.setText(companyInfo.regCapital,LText.empty(companyInfo.regCapital) ? View.GONE : View.VISIBLE);
        tvRegTime.setText(companyInfo.startDate,LText.empty(companyInfo.startDate) ? View.GONE : View.VISIBLE);
        llDataSource.setVisibility(LText.empty(companyInfo.kzLogo) ? View.GONE : View.VISIBLE);
        tvDataSourceIv.setImageURI(companyInfo.kzLogo);
        tvSeeAll.setVisibility(View.GONE);
        if (!LText.empty(companyInfo.buttonText) && !LText.empty(companyInfo.webUrl)) {
//            tvSeeAll.setVisibility(View.VISIBLE);
//            tvSeeAll.setOnClickListener(new OnClickNoFastListener() {
//                @Override
//                public void onNoFastClick(View v) {
//                    Intent intent = new Intent(StoreHomepageDetailActivity.this, WebViewActivity.class);
//                    intent.putExtra(Constants.DATA_URL, companyInfo.webUrl);
//                    intent.putExtra(Constants.DATA_BACK_BY_LEVEL, true);
//                    AppUtil.startActivity(StoreHomepageDetailActivity.this, intent);
//                }
//            });
        }
        if (LList.getCount(companyInfo.moduleList) > 0) {
            supplementInfoRv.setVisibility(View.VISIBLE);
            supplementInfoRv.setLayoutManager(new LinearLayoutManager(StoreHomepageDetailActivity.this, LinearLayoutManager.HORIZONTAL, false));
            SupplementAdapter adapter = new SupplementAdapter(companyInfo.moduleList);
            supplementInfoRv.setAdapter(adapter);
            StringBuilder tabs = new StringBuilder();
            for (StoreCompanyBusinessInfo.ModuleListBean bean : companyInfo.moduleList) {
                if (tabs.length() > 0) {
                    tabs.append(",");
                }
                tabs.append(bean.name);
            }
        } else {
            supplementInfoRv.setVisibility(View.GONE);
        }
    }

    private void flushData(GeekBlueBrandInfoResponse data) {

        if (data.brand != null) {
            ServerBlueBrandInfo brandBaseInfo = data.brand;
            // Header
            if (brandBaseInfo != null) {
                tvBossName.setText(brandBaseInfo.name);
                tvStroeInfo.setText(brandBaseInfo.scaleName + " · " + brandBaseInfo.industryName);
                storeDescription.setCloseText(brandBaseInfo.introduce);
//                ivAvatar.setImageURI(brandBaseInfo.logo);
            }
            // Address
            List<ServerBlueBrandAddressInfo> brandAddressInfos = data.brandAddressList;
            if (!LList.isEmpty(brandAddressInfos)) {
                ServerBlueBrandAddressInfo displayAddress = LList.getElement(brandAddressInfos, 0);
                if (displayAddress != null) {
                    tvAddressinfo.setText(displayAddress.address);
                    tvAddressinfo.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            WorkLocationReviewActivity.review(getBaseContext(), displayAddress.address, "", displayAddress.latitude, displayAddress.longitude);
//                            getListener().onSeeAddressClick();
                        }
                    });
                }
            }
            // introduceInfo
            List<ServerBlueBrandWelfareInfo> bounus = data.brandWelfareList;
            List<String> bonusNames = new ArrayList<>();
            if (!LList.isEmpty(bounus)) {
                for (ServerBlueBrandWelfareInfo info : bounus) {
                    bonusNames.add(info.title);
                }
            }
            if (!LList.isEmpty(bonusNames)) {
                tagBounus.setAdapter(new StringTagAdapter(StoreHomepageDetailActivity.this, bonusNames));
            }

            // environment
            List<ServerBlueBrandPictrueBean> pictures = data.brandPictureList;
            if (!LList.isEmpty(pictures)) {
                PictureAdapter adapter = new PictureAdapter(pictures);
                rcEnvironment.setAdapter(adapter);
                adapter.notifyDataSetChanged();
            } else {
                clStoreEnvironmentinfo.setVisibility(View.GONE);
            }
        }
    }

    public void onCompanyPictureClick(ArrayList<ServerBlueBrandPictrueBean> imageList, int position, View sharedView) {
        final ArrayList<Image> images = new ArrayList<>();
        for (ServerBlueBrandPictrueBean picture : imageList) {
            images.add(new Image(picture.url));
        }
        // 预览公司图集大图
        Intent intent = IntentBuilder.with(this)
                .images(images)
                .params(new ExtraParams(position, null))
                .enableIndicator(true)
                .build();
        startActivityForResult(intent, CompanyConsts.REQUEST_CODE_PREVIEW_PICTURE);
        this.overridePendingTransition(R.anim.fade_in_fast, R.anim.fade_out_fast);

    }

    private void initBusinessView() {
        tvTitle = (MTextView) findViewById(R.id.tv_title);
        tvSeeAll = (MTextView) findViewById(R.id.tv_see_all);
        tvCompanyNameTitle = (MTextView) findViewById(R.id.tv_company_name_title);
        tvCompanyName = (MTextView) findViewById(R.id.tv_company_name);
        llLegal = (LinearLayout) findViewById(R.id.ll_legal);
        tvLegalTitle = (MTextView) findViewById(R.id.tv_legal_title);
        tvLegal = (MTextView) findViewById(R.id.tv_legal);
        llCapital = (LinearLayout) findViewById(R.id.ll_capital);
        tvCapitalTitle = (MTextView) findViewById(R.id.tv_capital_title);
        tvCapital = (MTextView) findViewById(R.id.tv_capital);
        llRegTime = (LinearLayout) findViewById(R.id.ll_reg_time);
        tvRegTimeTitle = (MTextView) findViewById(R.id.tv_reg_time_title);
        tvRegTime = (MTextView) findViewById(R.id.tv_reg_time);
        llDataSource = (LinearLayout) findViewById(R.id.ll_data_source);
        tvDataSourceTitle = (MTextView) findViewById(R.id.tv_data_source_title);
        tvDataSourceIv = (SimpleDraweeView) findViewById(R.id.tv_data_source_iv);
        supplementInfoRv = (RecyclerView) findViewById(R.id.supplement_info_rv);
        llCompanyBusiness = (LinearLayout) findViewById(R.id.ll_company_business);
    }

    public class PictureAdapter extends RecyclerView.Adapter<PictureAdapter.VH> {

        private ArrayList<ServerBlueBrandPictrueBean> picList = new ArrayList();

        public PictureAdapter(List<ServerBlueBrandPictrueBean> brandPicList) {
            picList.clear();
            picList.addAll(brandPicList);
        }

        @NonNull
        @Override
        public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            SimpleDraweeView pictureCover = new SimpleDraweeView(getBaseContext());
            pictureCover.setId(R.id.cover);

            FrameLayout container = new FrameLayout(getBaseContext());
            FrameLayout.LayoutParams coverLps;
            RecyclerView.LayoutParams containerLps;

            if (getItemCount() == 1) { // 只有一个媒体，图片使用大图
                coverLps = new FrameLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
                int displayWidth = App.get().getDisplayWidth();
                final float pictureViewHeight = displayWidth / 1.8f;
                coverLps.height = (int) pictureViewHeight;
                containerLps = new RecyclerView.LayoutParams(MATCH_PARENT, WRAP_CONTENT);

            } else { // 超过一个媒体，图片使用小图
                int _width = Scale.dip2px(getBaseContext(), 200);
                int _height = Scale.dip2px(getBaseContext(), 124);
                coverLps = new FrameLayout.LayoutParams(_width, _height);
                containerLps = new RecyclerView.LayoutParams(_width, _height);
            }
            pictureCover.setLayoutParams(coverLps);
            container.setLayoutParams(containerLps);
            RoundingParams roundingParams = RoundingParams.fromCornersRadius(Scale.dip2px(getBaseContext(), 12));
            pictureCover.setHierarchy(new GenericDraweeHierarchyBuilder(getBaseContext().getResources())
                    .setRoundingParams(roundingParams)
                    .setFadeDuration(250)
                    .setActualImageScaleType(CENTER_CROP)
                    .setPlaceholderImage(R.color.app_gray)
                    .build());

            container.addView(pictureCover);
            return new VH(container);
        }


        @Override
        public void onBindViewHolder(@NonNull VH holder, int position) {
            holder.bindMedia(picList.get(position).url);
        }

        @Override
        public int getItemCount() {
            return picList.size();
        }

        class VH extends RecyclerView.ViewHolder implements View.OnClickListener {

            SimpleDraweeView mPictureView;

            @Override
            public void onClick(View v) {
                onCompanyPictureClick(picList, getAdapterPosition(), itemView);
            }

            public VH(View itemView) {
                super(itemView);
                mPictureView = itemView.findViewById(R.id.cover);
                mPictureView.setOnClickListener(this);
            }

            void bindMedia(String url) {
                mPictureView.setImageURI(url);
            }
        }

    }

    static class SupplementAdapter extends BaseQuickAdapter<StoreCompanyBusinessInfo.ModuleListBean, BaseViewHolder> {

        public SupplementAdapter(@Nullable List<StoreCompanyBusinessInfo.ModuleListBean> data) {
            super(R.layout.store_item_brand_supplement_info, data);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, StoreCompanyBusinessInfo.ModuleListBean item) {
            RecyclerView.LayoutParams layoutParams = (RecyclerView.LayoutParams) helper.itemView.getLayoutParams();
            if (null == layoutParams) {
                layoutParams = new RecyclerView.LayoutParams(WRAP_CONTENT, WRAP_CONTENT);
            }

            layoutParams.width = DisplayHelper.getScreenWidth(mContext)/5;
            helper.itemView.setLayoutParams(layoutParams);

            SimpleDraweeView simpleDraweeView = helper.getView(R.id.supplement_iv);
            simpleDraweeView.setImageURI(item.logo);
            helper.setText(R.id.experience_tv, item.name);
            helper.itemView.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    new ZPManager(mContext, item.url).handler();
                }
            });
        }
    }
}
