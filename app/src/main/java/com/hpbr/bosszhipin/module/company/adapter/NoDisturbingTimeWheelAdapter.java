package com.hpbr.bosszhipin.module.company.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.app.R;
import com.twl.ui.wheel.adapter.AbstractWheelTextAdapter;

import java.util.List;

public class NoDisturbingTimeWheelAdapter extends AbstractWheelTextAdapter {

    private List<Integer> dateList;

    public NoDisturbingTimeWheelAdapter(Context context, @NonNull List<Integer> dateList) {
        super(context, R.layout.item_single_column, NO_RESOURCE);

        this.dateList = dateList;

        setItemTextResource(R.id.tv_item_name);
    }

    @Override
    public View getItem(int index, View cachedView, ViewGroup parent) {
        return super.getItem(index, cachedView, parent);
    }

    @Override
    public int getItemsCount() {
        return dateList.size();
    }

    @Override
    protected CharSequence getItemText(int index) {
        Integer i = dateList.get(index);
        return getDisplayText(i);
    }

    public static String getDisplayText(Integer i) {
        String displayText = "";
        if (i >= 10 && i < 24) {
            displayText = i + ":00";
        } else if (i >= 0 && i < 10) {
            displayText = "0" + i + ":00";
        }
        return displayText;
    }

}
