package com.hpbr.bosszhipin.module.company.circle.helper;

import android.content.Intent;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.utils.ReceiverUtils;

/**
 * created by ton<PERSON><PERSON><PERSON><PERSON>.
 * date: 2019-11-06
 * time: 10:14
 * description:圈子广播发送帮助类
 */
public class CIrcleBroadcastHelper {
    /**
     * 删除回答/问答/帖子 通知
     */
    public static final String ACTION_REFRESH_DELETE = Constants.PREFIX + "ACTION_REFRESH_DELETE";
}
