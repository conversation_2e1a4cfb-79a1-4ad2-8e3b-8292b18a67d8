package com.hpbr.bosszhipin.module.company.circle.manager;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.get.export.GetRouter;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.Scale;
import com.twl.ui.popup.TriangleDrawable;
import com.twl.ui.popup.XGravity;
import com.twl.ui.popup.YGravity;
import com.twl.ui.popup.ZPUIPopup;
import com.twl.utils.ActivityUtils;

import java.util.ArrayList;
import java.util.List;

import static com.hpbr.bosszhipin.data.manager.ContactManager.FOLLOW_FRIENDID;
import static com.hpbr.bosszhipin.data.manager.ContactManager.INTERACT_FRIENDID;
import static com.hpbr.bosszhipin.data.manager.ContactManager.NOTIFY_FRIENDID;

public class GetHomeRedDotManager {

    private static final String KEY_LAST_NON_READ_MESSAGE_TIME = "KEY_LAST_NON_READ_MESSAGE_TIME";

    private static final GetHomeRedDotManager instance = new GetHomeRedDotManager();

    public static GetHomeRedDotManager getInstance() {
        return instance;
    }

    private int redDotType; // 红点类型 0 无红点 1 红点 2 气泡
    @Nullable
    private String bubbleText; // 气泡文案
    private String formId;  // 干预卡片的id 仅用于埋点
    private String guideId;  // 卡片的策略id 仅用于埋点



    private GetHomeRedDotManager() {
    }

    public void reloadStatus(int redDotType, @Nullable String bubbleText) {
        this.redDotType = redDotType;
        this.bubbleText = bubbleText;
    }

    public void clearDot() {
        redDotType = 0;
    }

    public void clearBubble() {
        bubbleText = null;
    }

    public void clearDotOrBubble() {
        // 红点显示优先级高于其他；点击后红点消失，否则上线期间一直显示
        clearDot();
//        clearBubble();
    }

    public int getRedDotType() {
        return redDotType;
    }

    public boolean isRedDotExist() {
        return redDotType == 1;
    }

    public boolean isShowBubble() {
        return redDotType == 2;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getGuideId() {
        return guideId;
    }

    public void setGuideId(String guideId) {
        this.guideId = guideId;
    }

    @Nullable
    public String getBubbleText() {
        return bubbleText;
    }

    public void getDiscoverDot() {
        GetRouter.getGuideRecCard();
    }

    public void showGetGuideTips(@NonNull View anchor, OnClickNoFastListener listener) {
        Activity activity = (Activity) anchor.getContext();

        ZPUIPopup popup = ZPUIPopup.create(activity).setContentView(R.layout.view_guide_rec_card_tips).setOnViewListener(new ZPUIPopup.OnViewListener() {
            @Override
            public void initViews(View view, ZPUIPopup popup) {
                View vArrow = view.findViewById(R.id.v_arrow);
                MTextView bubbleTextView = view.findViewById(R.id.bubbleText);
                bubbleTextView.setText(bubbleText);
                bubbleTextView.setOnClickListener(listener);
                view.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                    @SuppressLint("RtlHardcoded")
                    @Override
                    public boolean onPreDraw() {
                        int[] anchorLocation = new int[2];
                        anchor.getLocationOnScreen(anchorLocation);
                        int anchorX = anchorLocation[0];

                        int[] viewLocation = new int[2];
                        view.getLocationOnScreen(viewLocation);
                        int viewX = viewLocation[0];

                        int arrowWidth = Scale.dip2px(activity, 10);
                        int arrowHeight = Scale.dip2px(activity, 5);
                        int leftMargin = anchorX - viewX + (anchor.getWidth() - arrowWidth) / 2;
                        LinearLayout.LayoutParams arrowParams = new LinearLayout.LayoutParams(arrowWidth, arrowHeight);
                        arrowParams.leftMargin = leftMargin;
                        vArrow.setLayoutParams(arrowParams);
                        vArrow.setBackground(new TriangleDrawable(TriangleDrawable.BOTTOM, 0xE015B3B3));

                        view.getViewTreeObserver().removeOnPreDrawListener(this);
                        return true;
                    }
                });
            }
        }).apply();

        if (popup.isShowing()) {
            popup.dismiss();
        } else {
            if (ActivityUtils.isValid(activity)) {
                popup.showAtAnchorView(
                        anchor,
                        YGravity.ABOVE,
                        XGravity.ALIGN_LEFT,
                        0,
                        -Scale.dip2px(activity, 3),
                        false
                );

                // 显示气泡提示，仅提示一次，6s 后消失
                anchor.postDelayed(popup::dismiss, 6000);
            }
        }
    }

    /**
     * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=71935988
     */
    public static boolean hasNewIncomingNoneReadMessages(@NonNull ROLE role) {
        List<Long> getContacts = new ArrayList<>();
        getContacts.add(NOTIFY_FRIENDID);
        getContacts.add(INTERACT_FRIENDID);
        getContacts.add(FOLLOW_FRIENDID);

        long lastUpdateTime = SpManager.get().user().getLong(KEY_LAST_NON_READ_MESSAGE_TIME, 0);

        long nonReadMsgUpdateTime = 0;
        for (Long contactId : getContacts) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(contactId, role.get(), 0);
            int contactNoneReadCount = ContactManager.getInstance().getContactNoneReadCount(contactId);
            if (contactBean != null && contactNoneReadCount > 0) {
                long ut = contactBean.updateTime;
                if (ut > nonReadMsgUpdateTime) { // 记下最新的未读消息的时间
                    nonReadMsgUpdateTime = ut;
                }
            }
        }

        // 记下最新的未读消息时间，用于后续判断是否有收到新的未读消息
        SpManager.get().user().edit().putLong(KEY_LAST_NON_READ_MESSAGE_TIME, nonReadMsgUpdateTime).apply();

        // 有新的未读消息
        return nonReadMsgUpdateTime > lastUpdateTime;
    }

}
