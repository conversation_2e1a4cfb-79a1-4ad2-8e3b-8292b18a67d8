package com.hpbr.bosszhipin.module.company.circle.ui.main;

import android.content.Context;
import android.util.AttributeSet;

import androidx.coordinatorlayout.widget.CoordinatorLayout;

/**
 * Create by Chong
 * 2019-12-18
 */
public class FloatingView extends CoordinatorLayout {
    public FloatingView(Context context) {
        super(context);
    }

    public FloatingView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public FloatingView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

//    @Override
//    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
//        Log.d("-----111--->", widthMeasureSpec + "  " + widthMeasureSpec);
//        Log.d("-----111--->", MeasureSpec.getSize(widthMeasureSpec) + "  " + MeasureSpec.getSize(heightMeasureSpec));
//
////        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
//        Log.d("-----111222--->", MeasureSpec.getSize(getMeasuredWidthAndState()) + "  " + MeasureSpec.getSize(getMeasuredHeightAndState()));
//        Log.d("-----111222--->", getMeasuredWidth() + "  " + getMeasuredHeight());
//
//        int w = resolveAdjustedSize(getMeasuredWidth());
//        int h = resolveAdjustedSize(getMeasuredHeight());
//        w = Scale.dip2px(getContext(), 82) + w;
//
//        setMeasuredDimension(widthMeasureSpec, heightMeasureSpec);
//    }
//
//    private static int resolveAdjustedSize(int measureSpec) {
//        int specMode = MeasureSpec.getMode(measureSpec);
//        int specSize = MeasureSpec.getSize(measureSpec);
//        int result;
//        switch (specMode) {
//            case -2147483648:
//                result = specSize;
//                break;
//            case 0:
//                result = 0;
//                break;
//            case 1073741824:
//                result = specSize;
//                break;
//            default:
//                throw new IllegalArgumentException();
//        }
//
//        return result;
//    }

    /**
     * 最后一步强行纠正View的尺寸
     * 设计图131*48
     * <p>
     * 左右各自增加41dp
     */
//    @Override
//    public void layout(int l, int t, int r, int b) {
//        super.layout(l - Scale.dip2px(getContext(), 41), t, r + Scale.dip2px(getContext(), 41), b);
//    }
}
