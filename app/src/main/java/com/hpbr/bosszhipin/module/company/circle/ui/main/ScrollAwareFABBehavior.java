package com.hpbr.bosszhipin.module.company.circle.ui.main;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.Interpolator;

import androidx.annotation.NonNull;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.view.ViewCompat;
import androidx.core.view.ViewPropertyAnimatorListener;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;

/**
 * Create by Chong
 * 2019-12-18
 */
public class ScrollAwareFABBehavior extends CoordinatorLayout.Behavior<View> {

    private static final Interpolator INTERPOLATOR = new FastOutSlowInInterpolator();
    private boolean mIsAnimatingOut = false;

    public ScrollAwareFABBehavior(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onStartNestedScroll(@NonNull CoordinatorLayout coordinatorLayout, @NonNull View child, @NonNull View directTargetChild, @NonNull View target, int axes, int type) {
        return axes == ViewCompat.SCROLL_AXIS_VERTICAL
                || super.onStartNestedScroll(coordinatorLayout, child, directTargetChild, target, axes, type);
    }

    @Override
    public void onNestedScroll(@NonNull CoordinatorLayout coordinatorLayout, @NonNull View child,
                               @NonNull View target, int dxConsumed, int dyConsumed, int dxUnconsumed, int dyUnconsumed, int type) {
        super.onNestedScroll(coordinatorLayout, child, target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type);
        if (dyConsumed > 0 && !this.mIsAnimatingOut && child.getVisibility() == View.VISIBLE) {
            // User scrolled down and the FAB is currently visible -> hide the FAB
            animateOut(child, target);
        } else if (dyConsumed < 0 && child.getVisibility() == View.INVISIBLE) {
            // User scrolled up and the FAB is currently not visible -> show the FAB
            animateIn(child, target);
        } else {
            // 修复RecyclerView监听页面滚动状态延迟问题
            ViewCompat.stopNestedScroll(target, ViewCompat.TYPE_NON_TOUCH);
        }
    }

    private void animateOut(View button, final View target) {
        ViewCompat.animate(button).scaleX(0.0F).scaleY(0.0F).alpha(0.0F).setInterpolator(INTERPOLATOR).withLayer()
                .setListener(new ViewPropertyAnimatorListener() {
                    public void onAnimationStart(View view) {
                        ScrollAwareFABBehavior.this.mIsAnimatingOut = true;
                    }

                    public void onAnimationCancel(View view) {
                        ScrollAwareFABBehavior.this.mIsAnimatingOut = false;
                    }

                    public void onAnimationEnd(View view) {
                        /*
                         * 注意 这里不能用GONE  ,否则会跳出循环，不会再调用 onNestedScroll
                         * CoordinatorLayout#onNestedScroll  =->
                         * if (view.getVisibility() == GONE) {
                         *                 // If the child is GONE, skip...
                         *                 continue; }
                         */
                        ScrollAwareFABBehavior.this.mIsAnimatingOut = false;
                        view.setVisibility(View.INVISIBLE);

                        // 修复RecyclerView监听页面滚动状态延迟问题
                        ViewCompat.stopNestedScroll(target, ViewCompat.TYPE_NON_TOUCH);
                    }
                }).start();
    }

    private void animateIn(View button, final View target) {
        ViewCompat.animate(button).scaleX(1.0F).scaleY(1.0F).alpha(1.0F)
                .setInterpolator(INTERPOLATOR).withLayer().setListener(new ViewPropertyAnimatorListener() {
            @Override
            public void onAnimationStart(View view) {
                view.setVisibility(View.VISIBLE);
            }

            @Override
            public void onAnimationEnd(View view) {
                // 修复RecyclerView监听页面滚动状态延迟问题
                ViewCompat.stopNestedScroll(target, ViewCompat.TYPE_NON_TOUCH);
            }

            @Override
            public void onAnimationCancel(View view) {

            }
        }).start();

    }
}
