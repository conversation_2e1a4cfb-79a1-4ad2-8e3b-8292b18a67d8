package com.hpbr.bosszhipin.module.company.entity;

import com.google.gson.annotations.SerializedName;
import com.hpbr.bosszhipin.base.BaseEntity;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/3/5.
 */
public class CodeNamePair extends BaseEntity {

    private static final long serialVersionUID = 1071740352055598543L;

    @SerializedName("code")
    public long code;

    @SerializedName("name")
    public String name;

    @SerializedName("firstChar")
    public String firstChar;

    @SerializedName("subLevelModelList")
    public List<CodeNamePair> subLevelModelList;

    public transient String groupName; // 该选项所在分组的名称

    public CodeNamePair() {
    }

    public CodeNamePair(long code, String name, String firstChar) {
        this.code = code;
        this.name = name;
        this.firstChar = firstChar;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CodeNamePair)) return false;

        CodeNamePair that = (CodeNamePair) o;

        if (code != that.code) return false;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        int result = (int) (code ^ (code >>> 32));
        result = 31 * result + (name != null ? name.hashCode() : 0);
        return result;
    }
}
