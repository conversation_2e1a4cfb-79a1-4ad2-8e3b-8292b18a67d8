package com.hpbr.bosszhipin.module.company.entity;


import com.amap.api.maps.model.Marker;

import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.HighlightItem;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * created by yhy
 * date 2021/9/9
 * desc:
 */
public class CompanyAddressBean extends BaseServerBean {

    private static final long serialVersionUID = 6317542857355797027L;

    @Override
    public String toString() {
        return "CompanyAddressBean{" +
                "mLevel=" + mLevel +
                ", isSelect=" +select  +
                ", title='" + getAddressTitle() + '\'' +
                '}';
    }
    public List<HighlightItem> highlightItems;
    public List<CompanyAddressBean> mClusterItems;
    public transient Marker mMarker;
    public int mLevel;
    public int cover;
    public boolean select;
    public String addressTitle;
    public String securityId;
    public String jobAddressId;
    public String locationDesc;
    public String areaCode;
    public  double latitude;
    public  double longitude;
    public CompanyAddressBean() {
        mClusterItems = new ArrayList<CompanyAddressBean>();
        mClusterItems.add(this);
    }

    public void addClusterItem(CompanyAddressBean clusterItem) {
        mClusterItems.add(clusterItem);
    }

    public int getClusterCount() {
        return mClusterItems.size();
    }


    public String getAddressTitle() {

        return addressTitle;
    }

    Marker getMarker() {
        return mMarker;
    }

    List<CompanyAddressBean> getClusterItems() {
        return mClusterItems;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CompanyAddressBean)) return false;
        CompanyAddressBean cluster = (CompanyAddressBean) o;
        return mLevel == cluster.mLevel &&
                select == cluster.select &&
                latitude == cluster.latitude &&
                longitude == cluster.longitude &&
                getAddressTitle().equals(cluster.getAddressTitle());
    }

    @Override
    public int hashCode() {
        return Objects.hash( mLevel, select, getAddressTitle());
    }
}