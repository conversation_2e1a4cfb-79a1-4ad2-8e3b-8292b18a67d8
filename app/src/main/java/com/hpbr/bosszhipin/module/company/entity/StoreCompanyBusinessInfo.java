package com.hpbr.bosszhipin.module.company.entity;

import net.bosszhipin.api.GetBrandInfoResponse;
import net.bosszhipin.api.bean.BaseServerBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *     author : Wp
 *     e-mail : <EMAIL>
 *     time   : 2023/11/7 16:26
 *     desc   :
 *     version:
 * </pre>
 */
public class StoreCompanyBusinessInfo extends BaseServerBean {
    private static final long serialVersionUID = 9211894140813202752L;

    public transient String logo;
    public transient String website;

    public long id;
    public String name;
    public String regCode;
    public int status;
    public String statusDesc;
    public String startDate;
    public int certification;
    public String creditCode;
    public String orgCode;
    public String enterpriseType;
    public String companyType;
    public String businessScope;
    public String operatingPeriod;
    public String url;
    public String srcId;
    public int srcFrom;
    public Object srcFromDesc;
    public String email;
    public int certificationFrom;
    public String address;
    public double longitude;
    public double latitude;
    public String regCapital;
    public String phoneNumber;
    public String legalPerson;
    public String regAuthority;
    public String checkDate;
    public String province;
    public String parentIndustry;
    public String industry;
    public String srcUrl;
    public String kzLogo;
    public int completeType;
    public long addTime;
    public String buttonText; // 按钮文案
    public List<GetBrandInfoResponse.HighlightItemBean> highlightList; // 按钮文案高亮区间
    public String webUrl; // 看准h5地址url

    public String insuranceNumber;//参保人数
    public GetBrandInfoResponse.RecruitInfoBean recruitInfo = new GetBrandInfoResponse.RecruitInfoBean();

    public String encryptComId;

    public List<ModuleListBean> moduleList = new ArrayList<>();

    public static class ModuleListBean implements Serializable {
        private static final long serialVersionUID = -8170012463843958097L;
        /**
         * name : 股东信息
         * logo :
         * url :
         * count : 5
         */

        public String name;
        public String logo;
        public String url;
        public int count;
    }
}
