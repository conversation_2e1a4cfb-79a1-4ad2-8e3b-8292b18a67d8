package com.hpbr.bosszhipin.module.company.fragment;


import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.module.company.MultiAddressDataManager;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.views.NoScrollViewPager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import static com.hpbr.bosszhipin.views.behavior.MultiAddressSheetBehavior.STATE_EXPANDED;

/**
 * created by yhy
 * date 2021/10/25
 * desc: MultiAddressAllFragment  ,下面有列表 和搜索 fragment
 */
public class MultiAddressAllFragment extends BaseFragment {
    private final List<BaseFragment> mFragments = new ArrayList<>();
    private NoScrollViewPager mViewPager;
    private Bundle bundle;
    private TextView mAllTv;
    private MultiAddressListFragment mListFragment;
    private MultiAddressSearchFragment mSearchFragment;

    public static MultiAddressAllFragment newInstance() {
        MultiAddressAllFragment fragment = new MultiAddressAllFragment();

        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fg_multi_address_all, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }

    private void initView(View view) {
//        FrameLayout mPositionHandle = view.findViewById(R.id.positionHandle);
//        mPositionHandle.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (null != getCompanyFragment()) {
//                    // 用户点击同一个Tab，弹起卡片
//                    getCompanyFragment().onExpandCollapsedBottomSheet();
//                }
//            }
//        });

        mAllTv = view.findViewById(R.id.all_tv);
        mAllTv.setText(String.format("全部地址(%1d)",  MultiAddressDataManager.getInstance().getData().size()));
        mViewPager = view.findViewById(R.id.viewpager);
        mViewPager.setScroll(false);
        bundle = getArguments();

        initFragment ();
        initPagerAdapter();

    }

    private void initFragment() {
        mFragments.clear();
        mListFragment = MultiAddressListFragment.newInstance();
        mListFragment.setOnMultiAddressActionListener(mOnMultiAddressActionListener);
        mSearchFragment = MultiAddressSearchFragment.newInstance();
        mSearchFragment.setOnMultiAddressActionListener(mOnMultiAddressActionListener);
        mFragments.add(mListFragment);
        mFragments.add(mSearchFragment);
    }
    private MultiAddressActivity.OnMultiAddressActionListener mOnMultiAddressActionListener;

    public void setOnMultiAddressActionListener(MultiAddressActivity.OnMultiAddressActionListener onMultiAddressActionListener) {
        mOnMultiAddressActionListener = onMultiAddressActionListener;
    }

    public void switchToIndex(int position) {
        if (position == 0) {
            if (mSearchFragment != null) {
                mSearchFragment.clearData();
                mSearchFragment.hideSoftKeyBoard();
            }
        }
        if(position ==1){
           if(getActivity() instanceof MultiAddressActivity){
               ((MultiAddressActivity)getActivity()).setBehaviorState(STATE_EXPANDED);
           }
            if (mSearchFragment != null) {
                mSearchFragment.showSoftKeyBoard();
            }
        }
        mViewPager.setCurrentItem(position, false);
    }

    public int getCurrentItem(){
        if(mViewPager!=null){
           return mViewPager.getCurrentItem();
        }
        return -1;
    }
    public void hideSoftBoard() {
        if (mSearchFragment != null) {
            mSearchFragment.hideSoftKeyBoard();
        }
    }

    private void initPagerAdapter() {
        TabAdapter tabAdapter = new TabAdapter(getChildFragmentManager(), mFragments);
        mViewPager.setAdapter(tabAdapter);
        mViewPager.setPageMargin(Scale.dip2px(App.getApplication(), 4));
    }


    private static class TabAdapter extends FragmentPagerAdapter implements ViewPager.OnPageChangeListener {

        //        private List<String> title;
        private List<BaseFragment> data;

        TabAdapter(FragmentManager fm, List<BaseFragment> data) {
            super(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT);
//            this.title = title;
            this.data = data;
        }


        @Override
        public Fragment getItem(int position) {
            return LList.getElement(data, position);
        }

        @Override
        public int getCount() {
            return data.size();
        }

        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) { // 用户手动切换，上报
            if (position == 0) { // 职位
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (!LList.isEmpty(mFragments)) {
            for (Fragment fragment : mFragments) {
                if (null != fragment) {
                    fragment.onActivityResult(requestCode, resultCode, data);
                }
            }
        }
    }
}