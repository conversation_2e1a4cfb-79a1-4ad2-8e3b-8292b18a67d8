package com.hpbr.bosszhipin.module.company.fragment;


import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.module.company.MultiAddressDataManager;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.module.onlineresume.activity.sub.CompanyFragment;
import com.hpbr.bosszhipin.views.NoScrollViewPager;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.BMultiAddressBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * created by yhy
 * date 2021/10/25
 */
public class MultiAddressListFragment extends BaseFragment {
    private final List<BaseFragment> mFragments = new ArrayList<>();
    private NoScrollViewPager positionAndTrendContainer;
    private Bundle bundle;
    private AddressAdapter mAddressAdapter;
    private RecyclerView mAddressRv;
    ;
    private LinearLayout mSearchLl;
    private List<BMultiAddressBean> mData = new ArrayList<>();

    public static MultiAddressListFragment newInstance() {
        MultiAddressListFragment fragment = new MultiAddressListFragment();
        return fragment;
    }

    public CompanyFragment getCompanyFragment() {
        return (getParentFragment() instanceof CompanyFragment) ? (CompanyFragment) getParentFragment() : null;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fg_multi_address_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initView(view);
    }

    private void initView(View view) {
        mAddressRv = view.findViewById(R.id.address_rv);
        mSearchLl = view.findViewById(R.id.search_ll);
        mData = MultiAddressDataManager.getInstance().getData();
        mAddressRv.setLayoutManager(new LinearLayoutManager(getContext()));
        mAddressAdapter = new AddressAdapter();
        mAddressRv.setAdapter(mAddressAdapter);
        mAddressAdapter.setNewData(mData);

        mAddressAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                if(position < LList.getCount(mData)){
                    BMultiAddressBean bean = mData.get(position);
                    if (mOnMultiAddressActionListener != null) {
                        mOnMultiAddressActionListener.onListClickListener(bean);
                    }
                }
            }
        });
        mSearchLl.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (getParentMultiFragment() != null) {
                    getParentMultiFragment().switchToIndex(1);
                }
            }
        });
        view.findViewById(R.id.title_tv).setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (getParentMultiFragment() != null) {
                    getParentMultiFragment().switchToIndex(1);
                }
            }
        });
    }

    private MultiAddressAllFragment getParentMultiFragment() {
        return (MultiAddressAllFragment) getParentFragment();
    }

    private MultiAddressActivity.OnMultiAddressActionListener mOnMultiAddressActionListener;

    public void setOnMultiAddressActionListener(MultiAddressActivity.OnMultiAddressActionListener onMultiAddressActionListener) {
        mOnMultiAddressActionListener = onMultiAddressActionListener;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (!LList.isEmpty(mFragments)) {
            for (Fragment fragment : mFragments) {
                if (null != fragment) {
                    fragment.onActivityResult(requestCode, resultCode, data);
                }
            }
        }
    }

    private static class AddressAdapter extends BaseQuickAdapter<BMultiAddressBean, BaseViewHolder> {

        public AddressAdapter() {
            super(R.layout.item_com_address);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, BMultiAddressBean item) {
            helper.setText(R.id.tv_address, item.desc)
                    .setText(R.id.tv_title, item.addressText);
        }
    }
}