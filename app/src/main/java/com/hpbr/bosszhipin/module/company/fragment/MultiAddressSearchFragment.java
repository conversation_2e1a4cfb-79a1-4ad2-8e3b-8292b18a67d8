package com.hpbr.bosszhipin.module.company.fragment;


import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.company.MultiAddressDataManager;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.module.onlineresume.activity.sub.CompanyFragment;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnAdapterItemClickNoFastListener;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.BMultiAddressBean;
import net.bosszhipin.api.bean.HighlightItem;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * created by yhy
 * date 2021/10/25
 */
public class MultiAddressSearchFragment extends BaseFragment {
    private final List<BaseFragment> mFragments = new ArrayList<>();
    private MEditText mEtInput;
    private ImageView mIvClear;
    private MTextView mCancelTv;
    private List<BMultiAddressBean> mData = new ArrayList<>();
    private RecyclerView mRvAddress;
    private LinearLayout mEmptyView;
    private AddressAdapter mAddressAdapter;
    private ArrayList<BMultiAddressBean> resultList = new ArrayList<>();

    public static MultiAddressSearchFragment newInstance() {
        MultiAddressSearchFragment fragment = new MultiAddressSearchFragment();
        return fragment;
    }

    public CompanyFragment getCompanyFragment() {
        return (getParentFragment() instanceof CompanyFragment) ? (CompanyFragment) getParentFragment() : null;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fg_multi_address_search, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mData = MultiAddressDataManager.getInstance().getData();
        initView(view);
    }

    private void initView(View view) {

        mEtInput = (MEditText) view.findViewById(R.id.et_input);
        mIvClear = (ImageView) view.findViewById(R.id.iv_clear);
        mCancelTv = (MTextView) view.findViewById(R.id.cancel_tv);

        mRvAddress = (RecyclerView) view.findViewById(R.id.rv_address);
        mEmptyView = (LinearLayout) view.findViewById(R.id.empty_View);
        mRvAddress.setLayoutManager(new LinearLayoutManager(getContext()));
        mAddressAdapter = new AddressAdapter();

        mRvAddress.setAdapter(mAddressAdapter);
        mEtInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mIvClear.setVisibility(TextUtils.isEmpty(s.toString()) ? View.GONE : View.VISIBLE);
                if (!TextUtils.isEmpty(s.toString().trim())) {
                    searchAddress(s.toString().trim());
                } else {
                    mRvAddress.setVisibility(View.GONE);
                    mEmptyView.setVisibility(View.GONE);
                }
            }
        });

        mAddressAdapter.setOnItemClickListener(new OnAdapterItemClickNoFastListener() {
            @Override
            public void onNoFastItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (mOnMultiAddressActionListener != null) {
                    mOnMultiAddressActionListener.onSearchClickListener(resultList.get(position));
                }

            }
        });
        mCancelTv.setVisibility(View.VISIBLE);
        mIvClear.setOnClickListener(mOnClickNoFastListener);
        mCancelTv.setOnClickListener(mOnClickNoFastListener);
        mEtInput.setOnClickListener(mOnClickNoFastListener);

    }

    public void clearData() {
        if (mEtInput != null) {
            mEtInput.setText("");
        }
        if (resultList != null && mAddressAdapter != null) {
            resultList.clear();
            mAddressAdapter.notifyDataSetChanged();
        }
    }

    private void searchAddress(String keyWord) {
        resultList.clear();
        for (BMultiAddressBean bean : mData) {
            if (bean == null || TextUtils.isEmpty(bean.addressText)) continue;
            if (bean.addressText.contains(keyWord)) {
                ArrayList<HighlightItem> highlightItems = new ArrayList<>();
                HighlightItem highlightItem = new HighlightItem();
                highlightItem.startIndex = bean.addressText.indexOf(keyWord);
                highlightItem.endIndex = highlightItem.startIndex + keyWord.length();
                if (highlightItem.startIndex >= 0 && highlightItem.endIndex <= bean.addressText.length()) {
                    highlightItems.add(highlightItem);
                    bean.highlightItems = highlightItems;
                    resultList.add(bean);
                }

            }
        }
        if (mAddressAdapter != null) {
            if (!LList.isEmpty(resultList)) {
                mAddressAdapter.setNewData(resultList);
                mRvAddress.setVisibility(View.VISIBLE);
                mEmptyView.setVisibility(View.GONE);
            } else {
                mRvAddress.setVisibility(View.GONE);
                mEmptyView.setVisibility(View.VISIBLE);
            }
        }
    }

    public void hideSoftKeyBoard() {
        if (mEtInput != null) {
            AppUtil.hideSoftInput(getContext(), mEtInput);
        }
    }

    public void showSoftKeyBoard() {
        if (mEtInput != null) {
            mEtInput.post(new Runnable() {
                @Override
                public void run() {
                    AppUtil.showSoftInput(getContext(), mEtInput);
                }
            });

        }
    }

    private MultiAddressActivity.OnMultiAddressActionListener mOnMultiAddressActionListener;

    public void setOnMultiAddressActionListener(MultiAddressActivity.OnMultiAddressActionListener onMultiAddressActionListener) {
        mOnMultiAddressActionListener = onMultiAddressActionListener;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (!LList.isEmpty(mFragments)) {
            for (Fragment fragment : mFragments) {
                if (null != fragment) {
                    fragment.onActivityResult(requestCode, resultCode, data);
                }
            }
        }
    }
    private  OnClickNoFastListener mOnClickNoFastListener = new OnClickNoFastListener() {
        @Override
        public void onNoFastClick(View v) {
            if (v.equals(mIvClear)) {
                mEtInput.setText("");
            } else if (v.equals(mCancelTv)) {
                hideSoftKeyBoard();
                if (mOnMultiAddressActionListener != null) {
                    mOnMultiAddressActionListener.onSearchCancelListener();
                }
            } else if (v.equals(mEtInput)) {
                AppUtil.showSoftInput(getContext(), mEtInput);
            }
        }
    };

    private static class AddressAdapter extends BaseQuickAdapter<BMultiAddressBean, BaseViewHolder> {

        public AddressAdapter() {
            super(R.layout.item_com_address);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, BMultiAddressBean item) {
            TextView title = helper.getView(R.id.tv_title);
            title.setText(StringUtil.highLightString(item.highlightItems, item.addressText));
            helper.setText(R.id.tv_address, item.desc);
        }
    }
}