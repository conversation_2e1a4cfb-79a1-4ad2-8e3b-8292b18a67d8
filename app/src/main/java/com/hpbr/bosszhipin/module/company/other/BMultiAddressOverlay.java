package com.hpbr.bosszhipin.module.company.other;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.LruCache;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.map.IMapDelegate;
import com.hpbr.bosszhipin.map.Marker;
import com.hpbr.bosszhipin.map.MarkerOptions;
import com.hpbr.bosszhipin.map.anim.AlphaAnimation;
import com.hpbr.bosszhipin.map.anim.AnimationListener;
import com.hpbr.bosszhipin.map.location.LatLngBound;
import com.hpbr.bosszhipin.map.location.LatLonPoint;
import com.hpbr.bosszhipin.map.utils.MapUtil;
import com.hpbr.bosszhipin.module.company.BMultiAddressClickListener;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.BMultiAddressBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 整体设计采用了两个线程,一个线程用于计算组织聚合数据,一个线程负责处理Marker相关操作
 */
public class BMultiAddressOverlay implements
//        AMap.OnCameraChangeListener,
        IMapDelegate.OnMarkerClickListener {
    private static final String TAG = "BMultiAddressOverlay";
    private IMapDelegate mapDelegate;
    private Context mContext;
    private List<BMultiAddressBean> mClusterItems;  //所有数据
    private List<BMultiAddressBean> mClusters;//// 可视的 渲染的
    private int mClusterSize = 100;   //聚合范围的大小
    private BMultiAddressClickListener mClusterClickListener;
    private List<Marker> mAddMarkers = new ArrayList<Marker>();
    private double mClusterDistance;  //聚合距离
    private LruCache<Integer, Bitmap> mLruCache;
    //    private LruCache<String, BitmapDescriptor> mLruCacheName;
    private HandlerThread mSignClusterThread = new HandlerThread("calculateCluster");
    private MarkerHandler mMarkerhandler; //更新marker
    private SignClusterHandler mSignClusterHandler;  //计算marker
    private float mPXInMeters;
    private boolean mIsCanceled = false;
    private Map<Integer, Drawable> mBackDrawAbles = new HashMap<Integer, Drawable>();

    /**
     * 构造函数
     *
     * @param amap
     * @param context
     */
    public BMultiAddressOverlay(IMapDelegate amap, Context context) {
        this(amap, null, context);
    }

    /**
     * 构造函数,批量添加聚合元素时,调用此构造函数
     *
     * @param amap
     * @param clusterItems 聚合元素
     * @param context
     */
    public BMultiAddressOverlay(IMapDelegate amap, List<BMultiAddressBean> clusterItems,
                                Context context) {
        //默认最多会缓存80张图片作为聚合显示元素图片,根据自己显示需求和app使用内存情况,可以修改数量
        mLruCache = new LruCache<Integer, Bitmap>(80) {
            @Override
            protected void entryRemoved(boolean evicted, Integer key, Bitmap oldValue,
                                        Bitmap newValue) {
                oldValue.recycle();
            }
        };

        /*mLruCacheName = new LruCache<String, BitmapDescriptor>(80) {
            protected void entryRemoved(boolean evicted, Integer key, BitmapDescriptor oldValue, BitmapDescriptor newValue) {
                oldValue.getBitmap().recycle();
            }
        };*/
        if (clusterItems != null) {
            mClusterItems = clusterItems;
        } else {
            mClusterItems = new ArrayList<BMultiAddressBean>();
        }
        mContext = context;
        mClusters = new ArrayList<BMultiAddressBean>();
        this.mapDelegate = amap;
        mPXInMeters = mapDelegate.getScalePerPixel(); // fixme 百度无
        mClusterDistance = mPXInMeters * mClusterSize;
//        amap.setOnCameraChangeListener(this);
        amap.setOnMarkerClickListener(this);
        initThreadHandler();//初始化线程
    }

    public void setItems(List<BMultiAddressBean> items) {
        // 平移后 ，显示当前屏幕的poi
        mPXInMeters = mapDelegate.getScalePerPixel();
        mClusterDistance = mPXInMeters * mClusterSize;
        mClusterItems = items;
        assignClusters();
    }

    /**
     * 设置聚合点的点击事件
     *
     * @param clusterClickListener
     */
    public void setOnClusterClickListener(BMultiAddressClickListener clusterClickListener) {
        mClusterClickListener = clusterClickListener;
    }

    /**
     * 添加一个聚合点
     *
     * @param item
     */
    public void addClusterItem(BMultiAddressBean item) {
        Message message = Message.obtain();
        message.what = 1;
        message.obj = item;
        mSignClusterHandler.sendMessage(message);
    }

    /**
     * 销毁资源
     */
    public void onDestroy() {
        mIsCanceled = true;
        mSignClusterHandler.removeCallbacksAndMessages(null);
        mMarkerhandler.removeCallbacksAndMessages(null);
        mSignClusterThread.quit();
        for (int i = 0; i < mAddMarkers.size(); i++) {
            mAddMarkers.get(i).clear();
        }
        mAddMarkers.clear();
        mLruCache.evictAll();
    }

    //初始化Handler
    private void initThreadHandler() {
        mSignClusterThread.start();
        mMarkerhandler = new MarkerHandler(Looper.getMainLooper());
        mSignClusterHandler = new SignClusterHandler(mSignClusterThread.getLooper());
    }

   /* @Override
    public void onCameraChange(CameraPosition arg0) {

    }

    @Override
    public void onCameraChangeFinish(CameraPosition arg0) {
        //完成缩放的时候
        mPXInMeters = mAMap.getScalePerPixel();
        mClusterDistance = mPXInMeters * mClusterSize;
        //重新对点进行聚合
        assignClusters();
    }*/

    //点击事件
    @Override
    public boolean onMarkerClick(Marker marker) {
        if (mClusterClickListener == null) {
            return true;
        }
        BMultiAddressBean cluster = (BMultiAddressBean) marker.getObject();
        if (cluster != null) {
            mClusterClickListener.onClick(marker, cluster.mClusterItems);
            return true;
        }
        return false;
    }

    /**
     * 将聚合元素添加至地图上
     */
    private void addClusterToMap(List<BMultiAddressBean> clusters) {
        ArrayList<BMultiAddressBean> containsClusters = new ArrayList<>();

        ArrayList<Marker> removeMarkers = new ArrayList<>();

        if (LList.getCount(mAddMarkers) > 0) {
            Marker marker1 = mAddMarkers.get(0);
            BMultiAddressBean c = (BMultiAddressBean) marker1.getObject();
            BMultiAddressBean cNew = null;
            if (LList.getCount(clusters) > 0) {
                cNew = clusters.get(0);
            }
            TLog.info("clusoverlay", "all 新增 %1d   %2s", clusters.size(), clusters);
            if (cNew != null && c != null && c.level == cNew.level) { //和上次绘制的是同一级别
                for (Marker marker : mAddMarkers) {
                    BMultiAddressBean cluster = (BMultiAddressBean) marker.getObject();
                    if (cluster != null) {
                        if (!clusters.contains(cluster)) { //这是 现有地图上的点，在即将绘制中，不需要展现的，移除
                            removeMarkers.add(marker);
                        } else {
                            containsClusters.add(cluster); // 这是 现有地图上的点，在即将绘制中，仍需要展现的，不移除 也不重新绘制。
                        }
                    }
                }
            } else {
                removeMarkers.addAll(mAddMarkers);
            }
        }
        mAddMarkers.removeAll(removeMarkers);
        TLog.info("clusoverlay", "不需要移除 %1d   %2s", containsClusters.size(), containsClusters.toString());
//        //已经添加过的聚合元素

        //做一个隐藏的动画，百度地图不支持动画
        if (AndroidDataStarGray.getInstance().isUserAMapTypeV3()) {
            com.hpbr.bosszhipin.map.anim.AlphaAnimation alphaAnimation = new com.hpbr.bosszhipin.map.anim.AlphaAnimation(1f, 0f);
            AnimationListener animationListener = new AnimationListener();
            animationListener.setOnAnimationEnd(() -> {
                clearMarkers(removeMarkers);
            });
            alphaAnimation.setListener(animationListener);
            for (Marker marker : removeMarkers) {
                marker.setAnimationWithListener(alphaAnimation);
                marker.startAnimation();
            }
        } else {
            clearMarkers(removeMarkers);
        }

        if (LList.getCount(containsClusters) > 0) {
            clusters.removeAll(containsClusters);
        }

        TLog.info("clusoverlay", "仍需绘制 %1d   %2s", clusters.size(), clusters.toString());
        //然后再把所有的聚合元素重新添加
        for (int i = 0; i < clusters.size(); i++) {
            addSingleClusterToMap(clusters.get(i));
        }

    }

    private com.hpbr.bosszhipin.map.anim.AlphaAnimation mADDAnimation = new AlphaAnimation(0f, 1f);

    /**
     * 将单个聚合元素添加至地图显示
     *
     * @param cluster
     */
    private void addSingleClusterToMap(BMultiAddressBean cluster) {
        LatLonPoint latlng = new LatLonPoint(cluster.latitude, cluster.longitude);
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.anchor(0.5f, 0.5f).icon(getBitmapDes(cluster)).position(latlng.latitude, latlng.longitude);

//        markerOptions.anchor(0.5f, 0.5f).icon(BitmapDescriptorFactory.fromResource(com.hpbr.bosszhipin.app.R.mipmap.ic_geek_map_translate)).position(latlng);
//        markerOptions.anchor(0.5f, 0.5f).icon(getBitmapDes(cluster)).position(latlng).visible(true);

        Marker marker = mapDelegate.addMarker(markerOptions);
        marker.setAnimationWithListener(mADDAnimation);
        marker.setObject(cluster);

        marker.startAnimation();
        cluster.mMarker = marker;
        mAddMarkers.add(marker);
    }


    /**
     * 这个是处理多个坐标点。将附近相邻的点 聚合成一个，需求用不到 就注释吧  一个点  用一个
     */
    private void calculateClusters() {
        mIsCanceled = false;
        mClusters.clear();
        //判断现在地图上的区域是不是应该包含这个点，如果包含，就把点加到聚合数据里边，然后去通知mMarkerhandler更新一下。
        LatLngBound visibleBounds = mapDelegate.getVisibleRegion();  //由可视区域的四个顶点形成的经纬度范围
        for (int i = 0; i < mClusterItems.size(); i++) {
            if (mIsCanceled) {
                return;
            }
            LatLonPoint latlng = new LatLonPoint(mClusterItems.get(i).latitude, mClusterItems.get(i).longitude);
            if (visibleBounds.contains(latlng)) {
//                //判断坐标是否可以依附某个聚合点   不可以返回null
//                BMultiAddressBean cluster = getCluster(latlng, mClusters);
//                if (cluster != null) {
//                    cluster.addClusterItem(mClusterItems.get(i));
//                } else {
//                    //没有可以依附的聚合点的时候，就创建一个新的聚合点，加到聚合点数组里边
//                    cluster = new BMultiAddressBean(latlng);
//                BMultiAddressBean cluster = new BMultiAddressBean(latlng);
//                cluster.addClusterItem(cluster);
                BMultiAddressBean bean = mClusterItems.get(i);
                BMultiAddressBean temp = new BMultiAddressBean();
                temp.latitude = bean.latitude;
                temp.longitude = bean.longitude;
                temp.desc = bean.desc;
                temp.encRelationId = bean.encRelationId;
                temp.addressText = bean.addressText;
                temp.select = bean.select;
                temp.level = bean.level;
                temp.areaCode = bean.areaCode;
                temp.addressCount = bean.addressCount;
                temp.poiTitle = bean.poiTitle;
                temp.cityCode = bean.cityCode;
                temp.province = bean.province;
                temp.city = bean.city;
                temp.area = bean.area;
                temp.geoId = bean.geoId;
                temp.roomInfo = bean.roomInfo;

                mClusters.add(temp);  //
                //然后把这个坐标加到聚合点里边
//                    cluster.addClusterItem(mClusterItems.get(i));
//            }
            }
        }
        TLog.info("clusoverlay", "calculateClusters %1d  ", mClusterItems.size());
        //复制一份数据，规避同步
        //创建一个新的聚合点数组
        //把现有的聚合点加进去
        List<BMultiAddressBean> clusters = new ArrayList<BMultiAddressBean>();
        clusters.addAll(mClusters);
        TLog.info("clusoverlay", "calculate 添加 %1d  ", clusters.size());
        //给后给控制marher的headler发送消息。把所有的聚合点信息发过去
        Message message = Message.obtain();
        message.what = 0;
        message.obj = clusters;
        if (mIsCanceled) {
            return;
        }
        mMarkerhandler.sendMessage(message);
    }

    /**
     * 对点进行聚合
     */
    private void assignClusters() {
        mIsCanceled = true;
        mSignClusterHandler.removeMessages(0);//先把队列里边的消息移除
        mSignClusterHandler.sendEmptyMessage(0);//然后再发消息
    }

    /**
     * 在已有的聚合基础上，对添加的单个元素进行聚合
     *
     * @param clusterItem
     */
    private void calculateSingleCluster(BMultiAddressBean clusterItem) {
        LatLngBound visibleBounds = mapDelegate.getVisibleRegion();
        LatLonPoint latlng = new LatLonPoint(clusterItem.latitude, clusterItem.longitude);
        if (!visibleBounds.contains(latlng)) {
            return;
        }
        BMultiAddressBean cluster = getCluster(latlng, mClusters);
        if (cluster != null) {
            cluster.addClusterItem(clusterItem);
            Message message = Message.obtain();
            message.what = 2;
            message.obj = cluster;
            mMarkerhandler.removeMessages(2);
            mMarkerhandler.sendMessageDelayed(message, 5);

        } else {
            cluster = new BMultiAddressBean();
            mClusters.add(cluster);
            cluster.addClusterItem(clusterItem);
            Message message = Message.obtain();
            message.what = 1;
            message.obj = cluster;
            mMarkerhandler.sendMessage(message);

        }
    }

    /**
     * 根据一个点获取是否可以依附的聚合点，没有则返回null
     *
     * @param latLng
     * @return
     */
    private BMultiAddressBean getCluster(LatLonPoint latLng, List<BMultiAddressBean> clusters) {
        for (BMultiAddressBean cluster : clusters) {
            LatLonPoint clusterCenterPoint = new LatLonPoint(cluster.latitude, cluster.longitude);
            double distance = MapUtil.calculateLineDistance(latLng, clusterCenterPoint);
            if (distance < mClusterDistance) {
                return cluster;
            }
        }
        return null;
    }


    /**
     * 获取每个聚合点的绘制样式
     */
    private Bitmap getBitmapDes(BMultiAddressBean mCluster) {
        Bitmap bitmap = null;
        try {
            if (mCluster.getClusterCount() > 1) {//当数量》1设置个数
                bitmap = mLruCache.get(mCluster.getClusterCount());
                if (bitmap == null) {
                    TextView textView = new TextView(mContext);
                    String tile = String.valueOf(mCluster.getClusterCount());
//                textView.setText("附近有\n"+tile+"个"+"\n坐标");
                    textView.setText(tile);
                    textView.setGravity(Gravity.CENTER);
                    textView.setTextColor(Color.WHITE);
                    textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 15);
                    if (getDrawAble(mCluster) != null) {
                        textView.setBackgroundDrawable(getDrawAble(mCluster));
                    } else {
                        textView.setBackgroundResource(R.drawable.bg_3_3a8d88);
                    }
                    bitmap = mapDelegate.getIconBitmap(textView);
                    mLruCache.put(mCluster.getClusterCount(), bitmap);
                }
            } else {// 目前需求 都是走到这里
//            bitmapDescriptor = mLruCacheName.get(regionItem.getTitle());
//            if (bitmapDescriptor == null) {

                if (mCluster.level == BMultiAddressBean.LEVEL_ADDRESS) { // 是地址级别的
                    TextView textView = new TextView(mContext);
                    Drawable drawable = getDrawAble(mCluster);
                    if (drawable != null) {
                        textView.setBackground(drawable);
                    } else {
                        textView.setBackgroundResource(R.mipmap.ic_item_com_address);
                    }
                    bitmap = mapDelegate.getIconBitmap(textView);
                } else {// 行政区 气泡
                    View view = LayoutInflater.from(mContext).inflate(R.layout.view_multi_address_bubble, null);
                    TextView titleTv = view.findViewById(R.id.title_tv);
                    titleTv.setTextColor(mCluster.select ? mContext.getResources().getColor(R.color.color_FFFFFFFF) : mContext.getResources().getColor(R.color.color_FF141414));
                    TextView descTv = view.findViewById(R.id.desc_tv);
                    descTv.setTextColor(mCluster.select ? mContext.getResources().getColor(R.color.color_FFFFFFFF) : mContext.getResources().getColor(R.color.color_FF141414));
                    titleTv.setText(dealStringLength(mCluster.addressText, 3));
                    descTv.setText(String.format("%1$d个", mCluster.addressCount));
                    if (getDrawAble(mCluster) != null) {
                        view.setBackground(getDrawAble(mCluster));
                    } else {
                        view.setBackgroundResource(R.drawable.bg_3_3a8d88);
                    }
                    bitmap = mapDelegate.getIconBitmap(view);
                }
            }
        } catch (Exception e) {
            TLog.error(TAG, "getBitmapDes error msg = %s", e);
        }

        return bitmap;
    }

    /**
     * 更新已加入地图聚合点的样式
     */
    private void updateCluster(BMultiAddressBean cluster) {
        try {
            Marker marker = cluster.mMarker;
            marker.setIcon(getBitmapDes(cluster));
        } catch (Exception e) {
            TLog.error("updateCluster", "error msg = %s", e);
        }

    }


    public Drawable getDrawAble(BMultiAddressBean cluster) {
        int radius = DisplayHelper.dp2px(mContext, 80);
        if (cluster.level == BMultiAddressBean.LEVEL_ADDRESS) { //POI
            Drawable bitmapDrawable;
            if (cluster.select) {
                bitmapDrawable = mBackDrawAbles.get(4);
                if (bitmapDrawable == null) {
                    bitmapDrawable = mContext.getResources().getDrawable(
                            R.mipmap.ic_item_com_address_select);

                    mBackDrawAbles.put(4, bitmapDrawable);
                }

            } else {
                bitmapDrawable = mBackDrawAbles.get(3);
                if (bitmapDrawable == null) {
                    bitmapDrawable = mContext.getResources().getDrawable(
                            R.mipmap.ic_item_com_address);

                    mBackDrawAbles.put(3, bitmapDrawable);
                }

            }
            return bitmapDrawable;

        } else {
            //level == 1 行政区
            Drawable bitmapDrawable = null;
            if (cluster.select) {
                bitmapDrawable = mBackDrawAbles.get(2);
                if (bitmapDrawable == null) {
                    bitmapDrawable = new BitmapDrawable(null, drawCircle(radius,
                            mContext.getResources().getColor(R.color.color_FF15B3B3)));
                    mBackDrawAbles.put(2, bitmapDrawable);
                }
            } else {
                bitmapDrawable = mBackDrawAbles.get(1);
                if (bitmapDrawable == null) {
                    bitmapDrawable = new BitmapDrawable(null, drawCircle(radius,
                            mContext.getResources().getColor(R.color.color_FFFFFFFF)));
                    mBackDrawAbles.put(1, bitmapDrawable);
                }
            }
            return bitmapDrawable;

        }

    }

    private Bitmap drawCircle(int radius, int color) {

        Bitmap bitmap = Bitmap.createBitmap(radius * 2, radius * 2,
                Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        RectF rectF = new RectF(0, 0, radius * 2, radius * 2);
        Paint paint = new Paint();
        paint.setColor(color);
        canvas.drawCircle(radius, radius, radius, paint);
        return bitmap;
    }

    private void clearMarkers(List<Marker> removeMarkers) {
        if (LList.isEmpty(removeMarkers)) {
            return;
        }

        for (Marker marker : removeMarkers) {
            marker.clear();
        }
        removeMarkers.clear();
    }

//-----------------------辅助内部类用---------------------------------------------

    /**
     * marker渐变动画，动画结束后将Marker删除
     */
//    static class MyAnimationListener implements Animation.AnimationListener {
//        private List<Marker> mRemoveMarkers;
//
//        MyAnimationListener(List<Marker> removeMarkers) {
//            mRemoveMarkers = removeMarkers;
//        }
//
//        @Override
//        public void onAnimationStart() {
//
//        }
//
//        @Override
//        public void onAnimationEnd() {
//            for (Marker marker : mRemoveMarkers) {
//                marker.remove();
//            }
//            mRemoveMarkers.clear();
//        }
//    }

    /**
     * 处理market添加，更新等操作
     */
    class MarkerHandler extends Handler {

        MarkerHandler(Looper looper) {
            super(looper);
        }

        public void handleMessage(Message message) {
            switch (message.what) {
                case 0://接收到在当前区域内应该显示的所有的聚合点，把聚合点加到地图上
                    List<BMultiAddressBean> clusters = (List<BMultiAddressBean>) message.obj;
                    addClusterToMap(clusters);
                    break;
                case 1://接收单个聚合点
                    BMultiAddressBean cluster = (BMultiAddressBean) message.obj;
                    addSingleClusterToMap(cluster);
                    break;
                case 2:
                    BMultiAddressBean updateCluster = (BMultiAddressBean) message.obj;
                    updateCluster(updateCluster);
                    break;
            }
        }
    }

    /**
     * 处理聚合点算法线程
     */
    class SignClusterHandler extends Handler {

        SignClusterHandler(Looper looper) {
            super(looper);
        }

        public void handleMessage(Message message) {
            switch (message.what) {
                case 0:
                    calculateClusters();
                    break;
                case 1:
                    BMultiAddressBean item = (BMultiAddressBean) message.obj;
                    mClusterItems.add(item);
                    calculateSingleCluster(item);
                    break;
            }
        }
    }

    public String dealStringLength(String str, int length) {
        if (TextUtils.isEmpty(str) || str.length() <= length) return str;

        return str.substring(0, length) + "...";
    }
}