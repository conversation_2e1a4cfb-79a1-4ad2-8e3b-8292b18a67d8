package com.hpbr.bosszhipin.module.company.presenter;

import android.text.TextUtils;

import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ComCollectionSuccessResponse;
import net.bosszhipin.api.LikeOrDislikeCompanyRequest;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * 抽取关注/取消关注公司
 *
 * Created by quzhiyong on 2019/10/16
 */
public class CompanyConcernPresenter {

    /**
     * 添加关注
     */
    public static final int ADD_CONCERN = 0;
    /**
     * 取消关注
     */
    public static final int CANCEL_CONCERN = 1;

    private IOnConcernListener listener;
    private String sid;

    public void setSecurityId(String securityId) {
        this.sid = securityId;
    }

    public CompanyConcernPresenter(IOnConcernListener listener) {
        this.listener = listener;
    }

    /**
     * 添加取消关注接口
     *
     * @param type    {@link #ADD_CONCERN 添加关注},{@link #CANCEL_CONCERN 取消关注}
     * @param brandId 品牌id
     * @param source 关注来源
     */
    public void addOrCancelConcern(final int type, final long brandId, final String lid, final int source) {
        LikeOrDislikeCompanyRequest request = new LikeOrDislikeCompanyRequest(new ApiRequestCallback<ComCollectionSuccessResponse>() {

            @Override
            public void onStart() {
                if (listener != null) {
                    listener.onStart();
                }
            }

            @Override
            public void onSuccess(ApiData<ComCollectionSuccessResponse> data) {
                if (listener != null) {
                    listener.onSuccess(data.resp);
                }
            }

            @Override
            public void onComplete() {
                if (listener != null) {
                    listener.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (listener != null) {
                    listener.onFailed(reason.getErrReason());
                }
            }

        });
        request.brandId = brandId;
        request.flag = type;
        request.lid = TextUtils.isEmpty(lid) ? "" : lid;
        request.source = source;
        request.securityId = sid;

        HttpExecutor.execute(request);
    }

    public interface IOnConcernListener {

        void onStart();

        void onSuccess(ComCollectionSuccessResponse data);

        void onComplete();

        void onFailed(String reason);

    }
}
