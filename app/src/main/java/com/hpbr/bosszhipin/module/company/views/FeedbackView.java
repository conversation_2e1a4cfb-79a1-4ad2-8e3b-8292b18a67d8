package com.hpbr.bosszhipin.module.company.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import com.hpbr.bosszhipin.app.R;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by zhangxiangdong on 2018/3/1.
 */
public class FeedbackView extends FrameLayout implements View.OnClickListener {

    private OnFeedbackClickListener mListener;

    public void setOnFeekbackClickListener(OnFeedbackClickListener listener) {
        mListener = listener;
    }

    public interface OnFeedbackClickListener {
        void onFeedbackClick(boolean isGood);
    }

    public FeedbackView(@NonNull Context context) {
        this(context, null);
    }

    public FeedbackView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FeedbackView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(context).inflate(R.layout.view_feedback_common, this);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        findViewById(R.id.iv_bad).setOnClickListener(this);
        findViewById(R.id.iv_good).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_bad) {
            onFeedbackClick(false);

        } else if (i == R.id.iv_good) {
            onFeedbackClick(true);

        }
    }

    private void onFeedbackClick(boolean isGood) {
        if (mListener != null) mListener.onFeedbackClick(isGood);
    }


}
