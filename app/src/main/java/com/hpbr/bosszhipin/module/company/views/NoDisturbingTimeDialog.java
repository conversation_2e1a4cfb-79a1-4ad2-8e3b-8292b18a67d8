package com.hpbr.bosszhipin.module.company.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.adapter.NoDisturbingTimeWheelAdapter;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;
import com.twl.ui.wheel.OnWheelChangedListener;
import com.twl.ui.wheel.WheelView;

import java.util.ArrayList;
import java.util.List;

/**
 * 公司福利 - 工作时间Dialog
 * <p>
 * Created by quzhiyong on 2018/11/14
 */
public class NoDisturbingTimeDialog {

    public static final int DEFAULT_START_HOUR = 23;
    public static final int DEFAULT_END_HOUR = 7;
    private OnWheelSelectedListener onWheelSelectedListener;

    private Context context;
    private WheelView startWheelView, endWheelView;

    private List<Integer> allStartTimeList;
    private List<Integer> allEndTimeList;

    private int startSelectedIndex = 0; // 起始时间滚轮的选中项位置，默认选中时间
    private int endSelectedIndex = 0;
    private BottomView bottomView;

    public NoDisturbingTimeDialog(Context context) {
        this.context = context;
        allStartTimeList = getAllStartTime();
        allEndTimeList = getAllEndTime();
    }

    private List<Integer> getAllStartTime() { // [00:00, 23:00]
        List<Integer> allTimeList = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            allTimeList.add(i);
        }
        return allTimeList;
    }

    private List<Integer> getAllEndTime() { // [01:00, 12:00]
        List<Integer> allTimeList = new ArrayList<>();
        for (int i = 1; i < 13; i++) {
            allTimeList.add(i);
        }
        return allTimeList;
    }

    /**
     * 根据开始时间计算结束时间集合(开始时间之后的12h可选，开始时间集合[00:00, 23:00])
     * 开始时间00：00，结束时间[01:00, 12:00]
     * ...
     * 开始时间11：00，结束时间[12:00, 23:00]
     * 开始时间12：00，结束时间[13:00, 00:00]
     * 开始时间13：00，结束时间[14:00, 1:00]
     * ...
     * 开始时间23：00，结束时间[00:00, 11:00]
     *
     * @param startTime
     * @return
     */
    private List<Integer> getAllEndTimeByStartTime(int startTime) {
        List<Integer> allTimeList = new ArrayList<>();
        int start = (startTime + 1) % 24;
        int end = (startTime + 12) % 24;
        if (start >= 0 && start < 12) {
            for (int i = start; i <= end; i++) {
                allTimeList.add(i);
            }
        } else {
            for (int i = start; i < 24; i++) {
                allTimeList.add(i);
            }
            for (int i = 0; i <= end; i++) {
                allTimeList.add(i);
            }
        }
        return allTimeList;
    }

    /**
     * 刷新endWheelView适配器
     *
     * @param endSelectedIndex end WheelView的选中位置
     */
    private void refreshEndWheelAdapter(int endSelectedIndex) {
        this.endSelectedIndex = endSelectedIndex;
        endWheelView.setViewAdapter(new NoDisturbingTimeWheelAdapter(context, allEndTimeList));
        endWheelView.setCurrentItem(endSelectedIndex);
    }

    public void setSelectedIndex(int startHour, int endHour) {
        this.allEndTimeList = getAllEndTimeByStartTime(startHour);
        this.startSelectedIndex = allStartTimeList.indexOf(startHour);
        this.endSelectedIndex = allEndTimeList.indexOf(endHour);
    }

    public void setOnWheelSelectedListener(OnWheelSelectedListener onWheelSelectedListener) {
        this.onWheelSelectedListener = onWheelSelectedListener;
    }

    public void show(String title) {
        @SuppressLint("InflateParams") View view = LayoutInflater.from(context).inflate(R.layout.view_no_disturbing_time_double_wheel, null);
        view.findViewById(R.id.iv_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onWheelSelectedListener != null) {
                    startSelectedIndex = startWheelView.getCurrentItem();
                    endSelectedIndex = endWheelView.getCurrentItem();

                    onWheelSelectedListener.onTimeSelectedDone(allStartTimeList.get(startSelectedIndex),
                            allEndTimeList.get(endSelectedIndex));

                    dismiss();
                }
            }
        });

        if (!LText.empty(title)) {
            MTextView tvTitle = view.findViewById(R.id.tv_title);
            tvTitle.setText(title);
        }

        startWheelView = view.findViewById(R.id.wv_left_wheel);
        endWheelView = view.findViewById(R.id.wv_right_wheel);
        initStyle(startWheelView);
        initStyle(endWheelView);

        startWheelView.setViewAdapter(new NoDisturbingTimeWheelAdapter(context, allStartTimeList));
        endWheelView.setViewAdapter(new NoDisturbingTimeWheelAdapter(context, allEndTimeList));

        startWheelView.setCurrentItem(startSelectedIndex);
        endWheelView.setCurrentItem(endSelectedIndex);
        startWheelView.addChangingListener(new OnWheelChangedListener() {
            @Override
            public void onChanged(WheelView wheel, int oldValue, int newValue) {
                allEndTimeList = getAllEndTimeByStartTime(newValue);
                refreshEndWheelAdapter(0);
            }
        });

        bottomView = new BottomView(context, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
    }

    /**
     * 初始化滚轮样式
     */
    private void initStyle(WheelView wheelView) {
        wheelView.setVisibleItems(5); // Number of items
        wheelView.setWheelBackground(R.drawable.bg_wheel_holo);
        wheelView.setWheelForeground(R.drawable.bg_wheel_val_holo);
        wheelView.setShadowColor(0x70ffffff, 0x77ffffff, 0x70ffffff);
        wheelView.setDrawShadows(true);
    }

    public static boolean isValidTimeRange(int startHour, int endHour) {
        return (startHour >= 0 && startHour < 24) && (endHour >= 0 && endHour < 24);
    }

    /**
     * 确定按钮回调
     */
    public interface OnWheelSelectedListener {
        void onTimeSelectedDone(int startTime, int endTime);
    }

}
