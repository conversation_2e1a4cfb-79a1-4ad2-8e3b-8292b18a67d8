package com.hpbr.bosszhipin.module.company.views.filter.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.hpbr.bosszhipin.module.company.views.filter.inter.OnFilterComItemClickListener;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;

public class CodeNameAdapter extends RecyclerView.Adapter<CodeNameAdapter.VH> {

    private final Context context;
    private Condition condition;
    private OnFilterComItemClickListener listener;

    public CodeNameAdapter(Context context, Condition condition) {
        this.context = context;
        this.condition = condition;
    }

    public void setNewCondition(Condition condition) {
        this.condition = condition;
        notifyDataSetChanged();
    }

    public void setSelectedAndNotify(int position) {
        if (null != condition) {
            condition.setSelectedIndex(position);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public VH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new VH(LayoutInflater.from(context).inflate(R.layout.item_condition, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VH holder, int position) {
        if (null == condition || null == (LList.getElement(
                condition.getCodeNamePairs(), position))) {
            return;
        }

        CodeNamePair codeNamePair = LList.getElement(condition.getCodeNamePairs(), position);

        holder.conditionText.setText(codeNamePair.name);
        /* 已选中的样式加粗 */
        if (position == condition.getSelectedIndex()) {
            holder.conditionText.setTypeface(null, Typeface.BOLD);
            holder.conditionText.setTextColor(ContextCompat.getColor(context, R.color.app_green_dark));
        } else {
            holder.conditionText.setTypeface(null, Typeface.NORMAL);
            holder.conditionText.setTextColor(ContextCompat.getColor(context, R.color.color_FF141414_FFE6E6EB));
        }

        holder.itemView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (listener != null) {
                    listener.onItemClick(CodeNameAdapter.this, condition, codeNamePair, position);
                }
            }
        });
    }

    public void setOnItemClickListener(OnFilterComItemClickListener listener) {
        this.listener = listener;
    }

    @Override
    public int getItemCount() {
        return null != condition ? LList.getCount(condition.getCodeNamePairs()) : 0;
    }

    @Nullable
    public CodeNamePair getItem(int position) {
        return null != condition ? LList.getElement(condition.getCodeNamePairs(), position) : null;
    }

    public static class VH extends RecyclerView.ViewHolder {

        MTextView conditionText;

        VH(View itemView) {
            super(itemView);
            conditionText = (MTextView) itemView;
        }
    }
}
