package com.hpbr.bosszhipin.module.company.views.filter.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Typeface;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StyleRes;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.hpbr.bosszhipin.module.company.views.filter.inter.OnAdapterItemClickListener;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.List;

public class ShortcutAdapter extends RecyclerView.Adapter<ShortcutAdapter.H> {

    private final Context context;
    private final Condition shortcuts;

    private final int textSize;
    private final int textSizeActived;
    @ColorInt
    private final int textColor;
    @ColorInt
    private final int textColorActived;
    @DrawableRes
    private final int background;
    @DrawableRes
    private final int backgroundActived;
    private final int paddingLeft;
    private final int paddingRight;
    private final int textStyleActived;
    private final boolean indicatorEnabled;

    private OnAdapterItemClickListener listener;

    public boolean isIndicatorEnabled() {
        return indicatorEnabled;
    }

    public void setOnItemClickListener(OnAdapterItemClickListener listener) {
        this.listener = listener;
    }

    @SuppressWarnings("WeakerAccess")
    public ShortcutAdapter(Context context, @NonNull Condition shortcuts, @StyleRes int shortcutItemStyle) {
        this.context = context;
        this.shortcuts = shortcuts;

        ContextThemeWrapper wrapper = new ContextThemeWrapper(context, shortcutItemStyle);
        Resources.Theme theme = wrapper.getTheme();

        TypedArray ta = theme.obtainStyledAttributes(shortcutItemStyle, R.styleable.CompanyTopic);
        textSizeActived = ta.getDimensionPixelSize(R.styleable.CompanyTopic_textSizeActived, context.getResources().getDimensionPixelSize(R.dimen.text_d4));
        textColorActived = ta.getColor(R.styleable.CompanyTopic_textColorActived, ContextCompat.getColor(context, R.color.color_FFFFFFFF_FF151517));
        backgroundActived = ta.getResourceId(R.styleable.CompanyTopic_backgroundActived, R.drawable.bg_round_corner_solid_green);
        textStyleActived = ta.getInt(R.styleable.CompanyTopic_textStyleActived, 1);
        indicatorEnabled = ta.getBoolean(R.styleable.CompanyTopic_indicatorEnabled, false);

        textSize = ta.getDimensionPixelSize(R.styleable.CompanyTopic_android_textSize, context.getResources().getDimensionPixelSize(R.dimen.text_d4));
        textColor = ta.getColor(R.styleable.CompanyTopic_android_textColor, ContextCompat.getColor(context, R.color.text_c6));
        background = ta.getResourceId(R.styleable.CompanyTopic_android_background, R.drawable.bg_round_corner_stroke_gray);
        paddingLeft = ta.getDimensionPixelOffset(R.styleable.CompanyTopic_android_paddingLeft, Scale.dip2px(context, 18));
        paddingRight = ta.getDimensionPixelOffset(R.styleable.CompanyTopic_android_paddingRight, Scale.dip2px(context, 18));
        ta.recycle();
    }

    @NonNull
    @Override
    public H onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new H(LayoutInflater.from(context).inflate(R.layout.item_shortcut, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull H holder, int position) {
        List<CodeNamePair> codeNamePairs = shortcuts.getCodeNamePairs();
        CodeNamePair codeNamePair = LList.getElement(codeNamePairs, position);
        if (codeNamePair == null) return;

        MTextView sciv = holder.shortcutItem;
        sciv.setText(codeNamePair.name);

        if (position == shortcuts.getSelectedIndex()) { // Actived
            sciv.setBackgroundResource(backgroundActived);
            sciv.setTextColor(textColorActived);
            sciv.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizeActived);
            sciv.setTypeface(null, textStyleActived == 0 ? Typeface.BOLD : Typeface.NORMAL);
        } else {
            sciv.setBackgroundResource(background);
            sciv.setTextColor(textColor);
            sciv.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
            sciv.setTypeface(null, Typeface.NORMAL);
        }
        sciv.setPadding(paddingLeft, sciv.getPaddingTop(), paddingRight, sciv.getPaddingBottom());
    }

    @Nullable
    public CodeNamePair getItem(int position) {
        return LList.getElement(shortcuts.getCodeNamePairs(), position);
    }

    @Override
    public int getItemCount() {
        return LList.getCount(shortcuts.getCodeNamePairs());
    }

    class H extends RecyclerView.ViewHolder {

        @SuppressWarnings("WeakerAccess")
        public final MTextView shortcutItem;

        public H(View itemView) {
            super(itemView);
            shortcutItem = itemView.findViewById(R.id.shortcutItem);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 用户点选快捷方式
                    if (listener != null) {
                        listener.onItemClick(getAdapterPosition());
                    }
                }
            });
        }

    }

}
