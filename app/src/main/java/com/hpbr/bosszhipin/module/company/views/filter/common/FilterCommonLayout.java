package com.hpbr.bosszhipin.module.company.views.filter.common;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.adapter.CodeNameAdapter;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.hpbr.bosszhipin.module.company.views.filter.inter.OnFilterComItemClickListener;
import com.hpbr.bosszhipin.module.login.views.CountrySectionItemDecorator;
import com.hpbr.bosszhipin.module.login.views.QuickFitIndexView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.Scale;

/**
 * 条件筛选公共能控件
 */
public class FilterCommonLayout extends FrameLayout {

    protected Context mContext;

    protected RecyclerView conditionRecyclerView;

    protected QuickFitIndexView quickIndexView;

    protected MTextView overlayView;

    protected CodeNameAdapter codeNameAdapter;

    protected CountrySectionItemDecorator itemDecorator;

    protected OnFilterComItemClickListener itemClickListener;

    public void setItemClickListener(OnFilterComItemClickListener itemClickListener) {
        this.itemClickListener = itemClickListener;
    }

    public OnFilterComItemClickListener getItemClickListener() {
        return itemClickListener;
    }

    public FilterCommonLayout(@NonNull Context context) {
        this(context, null);
    }

    public FilterCommonLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterCommonLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        this.mContext = context;

        View.inflate(context, R.layout.layout_hot_hire_condition_filter_common, this);

        initRv(context);

        initQuickIndexView();

        initItemDecorator(context);
    }

    private void initRv(Context context) {
        conditionRecyclerView = findViewById(R.id.rv_condition);
        conditionRecyclerView.setNestedScrollingEnabled(false);

        codeNameAdapter = new CodeNameAdapter(context, null);
        codeNameAdapter.setOnItemClickListener(new OnFilterComItemClickListener() {
            @Override
            public void onItemClick(@NonNull CodeNameAdapter adapter, Condition condition, @NonNull CodeNamePair item, int position) {
                if (null != getItemClickListener() && null != item) {
                    getItemClickListener().onItemClick(adapter, condition, item, position);
                }
            }
        });
        conditionRecyclerView.setAdapter(codeNameAdapter);
    }

    public void scrollTOSelectPos(Condition condition) {
        // 用户已经选择过了，滑动到已选择位置
        if (null != condition && condition.getSelectedIndex() != Condition.NOT_SELECTED) {
            conditionRecyclerView.post(new Runnable() {
                @Override
                public void run() {
                    LinearLayoutManager layoutManager = (LinearLayoutManager) conditionRecyclerView.getLayoutManager();
                    if (null != layoutManager) {
                        layoutManager.scrollToPositionWithOffset(condition.getSelectedIndex(),
                                getContext().getResources().getDimensionPixelSize(R.dimen.dimen_quick_index_section_height));
                    }
                }
            });
        }
    }

    private void initQuickIndexView() {
        quickIndexView = findViewById(R.id.quickIndexView);
        quickIndexView.setIndexTextColor(ContextCompat.getColor(getContext(), R.color.text_c6));
        quickIndexView.setIndexTextSize(Scale.dip2px(getContext(), 10));
        overlayView = findViewById(R.id.overlayView);
    }

    private void initItemDecorator(Context context) {
        itemDecorator = new CountrySectionItemDecorator(context);
        itemDecorator.setSectionColor(ContextCompat.getColor(context, R.color.color_FFF5F5F5_FF262629));
        itemDecorator.setTextColor(ContextCompat.getColor(context, R.color.text_c6));
        itemDecorator.setSectionHeight(Scale.dip2px(context, 22));
        itemDecorator.setDividerHeight(0);
    }

    /*
     * --------------------------------------城市快速索引--------------------------------------------
     */

    public void setNewCondition(Condition condition) {
        if (null != codeNameAdapter) {
            shouldEnableQuickIndex(condition, codeNameAdapter);
            codeNameAdapter.setNewCondition(condition);
        }
    }

    private void shouldEnableQuickIndex(@NonNull Condition condition,
                                        @NonNull final CodeNameAdapter adapter) {
        // 如果有的话先移除快速定位装饰器
        conditionRecyclerView.removeItemDecoration(itemDecorator);
        if (condition.isQuickIndexEnabled()) {
            itemDecorator.setListener(position -> {
                final CodeNamePair namePair = adapter.getItem(position);
                if (namePair == null) return "";
                // 当分组名没有时，使用firstChar，否则使用空字符串作为列表展示的分组名
                final String groupName = namePair.groupName;
                final String firstChar = namePair.firstChar;
                return groupName == null ? (firstChar == null ? "" : firstChar) : groupName;
            });
            quickIndexView.setIndexer(condition.getIndices());
            quickIndexView.setOnIndexChangeListener(new QuickFitIndexView.OnIndexChangeListener() {
                @Override
                public void onIndexChange(int index, String text) {
                    /* 显示滑动提示 */
                    overlayView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 30);
                    overlayView.setVisibility(View.VISIBLE);
                    overlayView.setText(text);

                    /* 滑动列表 */
                    final int position = getPosByText(text);
                    if (position >= 0) {
                        ((LinearLayoutManager) conditionRecyclerView.getLayoutManager())
                                .scrollToPositionWithOffset(position, 0);
                    }
                }

                private int getPosByText(String text) {
                    if (TextUtils.isEmpty(text)) {
                        return -1;
                    }
                    int size = adapter.getItemCount();
                    for (int i = 0; i < size; i++) {
                        CodeNamePair pair = adapter.getItem(i);
                        if (pair == null) continue;
                        if (TextUtils.equals(text, pair.firstChar)) {
                            return i;
                        }
                    }
                    return -1;
                }

                @Override
                public void onMotionEventEnd() {
                    overlayView.setVisibility(View.GONE);
                }
            });

            // 开启快速定位
            conditionRecyclerView.addItemDecoration(itemDecorator);
            // 快速定位模式下不需要FadingEdge
            conditionRecyclerView.setVerticalFadingEdgeEnabled(false);
            quickIndexView.setVisibility(VISIBLE);
        } else {
            conditionRecyclerView.setVerticalFadingEdgeEnabled(true);
            overlayView.setVisibility(GONE);
            quickIndexView.setVisibility(GONE);
        }
    }

}
