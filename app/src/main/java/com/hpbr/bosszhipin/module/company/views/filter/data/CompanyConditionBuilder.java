package com.hpbr.bosszhipin.module.company.views.filter.data;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.utils.CitySortUtil;
import com.monch.lbase.util.LList;
import net.bosszhipin.api.BrandJobListV2Response;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.hpbr.bosszhipin.module.company.views.filter.data.CompanyConsts.*;

/**
 * Created by zhangxiangdong on 2018/9/25 17:15.
 */
public class CompanyConditionBuilder {

    private final List<Condition> conditions;

    public CompanyConditionBuilder(@NonNull List<Condition> conditions) {
        this.conditions = conditions;
    }

    @Nullable
    public Condition buildRecommend(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> positionList = response.position1List;
        if (LList.getCount(positionList) > 0) {
            Condition condition = Condition.newBuilder()
                    .title(TITLE_HOT_JOB)
                    .type(Condition.TYPE_HOT_JOB)
                    .codeNamePairs(positionList)
                    .filterType(response.enablePosition2Code() ?
                            ConditionFilterType.FILTER_TWO_LEVEL
                            : ConditionFilterType.FILTER_COMMON)
                    .build();
            conditions.add(condition);
            return condition;
        }
        return null;
    }

    @Nullable
    public Condition getRecommend(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> positionList = response.position1List;
        if (LList.getCount(positionList) > 0) {
            Condition condition = Condition.newBuilder()
                    .title(TITLE_HOT_JOB)
                    .type(Condition.TYPE_HOT_JOB)
                    .codeNamePairs(positionList)
                    .filterType(response.enablePosition2Code() ?
                            ConditionFilterType.FILTER_TWO_LEVEL
                            : ConditionFilterType.FILTER_COMMON)
                    .build();
            return condition;
        }
        return null;
    }

    public Condition buildRecommend(@NonNull BrandJobListV2Response response, String title) {
        List<CodeNamePair> positionList = response.position1List;
        if (LList.getCount(positionList) > 0) {
            Condition condition = Condition.newBuilder()
                    .title(title)
                    .type(Condition.TYPE_HOT_JOB)
                    .codeNamePairs(positionList)
                    .filterType(response.enablePosition2Code() ?
                            ConditionFilterType.FILTER_TWO_LEVEL
                            : ConditionFilterType.FILTER_COMMON)
                    .build();
            conditions.add(condition);
            return condition;
        }
        return null;
    }

    public void buildCity(@NonNull BrandJobListV2Response response) {
        BrandJobListV2Response.CityLists cityLists = response.cityLists;
        if (cityLists == null) return;

        /* 城市列表：包括“推荐城市”（如果有的话）、“热门城市”（如果有的话）、“所有城市”（该公司所有职位所在的城市） */

        final List<CodeNamePair> cityList = new ArrayList<>();
        // 推荐城市
        List<CodeNamePair> recommendList = cityLists.recommendList;
        final LevelBean locationBean = CitySortUtil.getLocationBean();
        final CodeNamePair locationPair = new CodeNamePair(locationBean.code, locationBean.name, null);
        locationPair.groupName = GROUP_NAME_RECOMMEND_CITY;
        if (LList.getCount(recommendList) > 0) {
            boolean contains = false;
            for (CodeNamePair pair : recommendList) { // 添加分组属性
                pair.firstChar = null; // “推荐城市”不需要索引定位
                pair.groupName = GROUP_NAME_RECOMMEND_CITY;

                if (isValidCity(locationBean) && locationBean.code == pair.code) {
                    contains = true;
                }
            }
            // 定位城市合法，并且推荐城市中不包含定位城市，添加定位城市到首位
            if (isValidCity(locationBean) && !contains) {
                recommendList.add(0, locationPair);
            }
            cityList.addAll(recommendList);
        } else {
            // 推荐城市为空，并且需要显示索引时（所有城市数量大于5个），直接添加一个定位城市作为推荐城市
            if (isValidCity(locationBean)
                    && LList.getCount(cityLists.commonCity) > THRESHOLD_MAX_NUM_OF_CITIES) {
                cityList.add(locationPair);
            }
        }

        // 热门城市
        List<CodeNamePair> hotCity = cityLists.hotCity;
        if (LList.getCount(hotCity) > 0) {
            for (CodeNamePair pair : hotCity) {
                pair.firstChar = INDEX_HOT_CITY; // “热门城市”需要索引定位
                pair.groupName = GROUP_NAME_HOT_CITY; // 添加分组属性
            }
            cityList.addAll(hotCity);
        }

        // 所有城市
        List<CodeNamePair> commonCity = cityLists.commonCity;
        if (LList.getCount(commonCity) > 0) {
            /* 将“不限/全部”放在整个cityList的第一个位置 */
            CodeNamePair unlimitedOrAll = null;
            final String firstName = commonCity.get(0).name;
            if (UNLIMITED.equals(firstName) || ALL.equals(firstName)) {
                // 移除第一个“不限/全部”
                unlimitedOrAll = commonCity.remove(0);
            }
            cityList.addAll(commonCity);
            if (unlimitedOrAll != null) {
                cityList.add(0, unlimitedOrAll);
            }
        }

        if (LList.getCount(cityList) > 0) {
            Condition.Builder builder = Condition.newBuilder()
                    .title(TITLE_CITY)
                    .type(Condition.TYPE_CITY)
                    .codeNamePairs(cityList);
            // 超过5个城市才开启快速定位功能
            final List<String> indexList = new ArrayList<>();
            if (LList.getCount(commonCity) >= THRESHOLD_MAX_NUM_OF_CITIES) {
                gatherOrderedIndices(commonCity, indexList);
                builder.indices(indexList).quickIndexEnabled(true);
            }
            conditions.add(builder.build());
        }
    }

    private boolean isValidCity(LevelBean bean) {
        return bean.code > 0 && !TextUtils.isEmpty(bean.name);
    }

    private void gatherOrderedIndices(List<CodeNamePair> commonCity, List<String> indexList) {
        Set<String> indexSet = new HashSet<>();
        for (CodeNamePair pair : commonCity) {
            // 忽视不合法的城市对应的索引
            if (pair == null || TextUtils.isEmpty(pair.firstChar)) continue;
            indexSet.add(pair.firstChar);
        }
        indexList.addAll(indexSet);
        Collections.sort(indexList);
        indexList.add(0, INDEX_HOT_CITY);
    }

    public void buildExp(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> experienceList = response.experienceList;
        if (LList.getCount(experienceList) > 0) {
            conditions.add(Condition.newBuilder()
                    .title(TITLE_EXP)
                    .type(Condition.TYPE_EXP)
                    .codeNamePairs(experienceList)
                    .build());
        }
    }

    public void buildSalary(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> salaryList = response.salaryList;
        if (LList.getCount(salaryList) > 0) {
            conditions.add(Condition.newBuilder()
                    .title(TITLE_SALARY)
                    .type(Condition.TYPE_SALARY)
                    .codeNamePairs(salaryList)
                    .build());
        }
    }

    public void buildType(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> typeList = response.jobTypeList;
        if (LList.getCount(typeList) > 0) {
            conditions.add(Condition.newBuilder()
                    .title(TITLE_TYPE)
                    .type(Condition.TYPE_TYPE)
                    .codeNamePairs(typeList)
                    .build());
        }
    }

    public void buildDegree(@NonNull BrandJobListV2Response response) {
        List<CodeNamePair> typeList = response.degreeList;
        if (LList.getCount(typeList) > 0) {
            conditions.add(Condition.newBuilder()
                    .title(TITLE_DEGREE)
                    .type(Condition.TYPE_DEGREE)
                    .codeNamePairs(typeList)
                    .build());
        }
    }

}
