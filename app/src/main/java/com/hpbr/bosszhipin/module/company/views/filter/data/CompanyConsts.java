package com.hpbr.bosszhipin.module.company.views.filter.data;

import androidx.annotation.Nullable;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;

/**
 * Created by zhangxiangdong on 2018/7/5 15:14.
 */
public class CompanyConsts {

    private CompanyConsts() {
    }

    public static final String TITLE_HOT_JOB = "推荐职位";
    public static final String TITLE_CITY = "城市";
    public static final String TITLE_EXP = "经验";
    public static final String TITLE_SALARY = "薪资";
    public static final String TITLE_TYPE = "类型";
    public static final String TITLE_DEGREE = "学历";

    public static final String GROUP_NAME_RECOMMEND_CITY = "推荐城市";
    public static final String GROUP_NAME_HOT_CITY = "热门城市";

    public static final String INDEX_HOT_CITY = "热";
    public static final String UNLIMITED = "不限";
    public static final String ALL = "全部";
    public static final String ALL_POSITION = "全部职位";
    public static final int THRESHOLD_MAX_NUM_OF_CITIES = 5;

    public static final int ALL_UNLIMITED_CODE = 0;

    /**
     * 全部、不限
     */
    public static boolean isAllOrUnlimited(@Nullable String s) {
        return UNLIMITED.equals(s) || ALL.equals(s);
    }

    public static boolean isAllUnlimitedCode(String name, long code) {
        return code <= 0 || isAllOrUnlimited(name);
    }

    public static boolean isDefaultItem(CodeNamePair selectedPair) {
        return null != selectedPair && isAllUnlimitedCode(selectedPair.name, selectedPair.code);
    }

}
