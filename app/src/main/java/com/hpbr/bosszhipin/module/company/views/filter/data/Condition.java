package com.hpbr.bosszhipin.module.company.views.filter.data;

import androidx.annotation.IntDef;

import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;

import java.util.List;

public class Condition {
    @IntDef({TYPE_HOT_JOB, TYPE_CITY, TYPE_EXP, TYPE_SALARY, TYPE_TYPE, TYPE_DEGREE})
    public @interface ConditionType {
    }

    public static final int TYPE_HOT_JOB = 0;
    public static final int TYPE_CITY = 1;
    public static final int TYPE_EXP = 2;
    public static final int TYPE_SALARY = 3;
    public static final int TYPE_TYPE = 4;
    public static final int TYPE_DEGREE = 5;
    public static final int NOT_SELECTED = -1;

    // 标记当前类型，如，推荐职位0、城市1等
    protected int type;

    // 当前类型名称，如，推荐职位、城市等
    protected String title;

    protected List<CodeNamePair> codeNamePairs;

    protected List<String> indices;

    protected int filterType;

    // 是否开启快速定位，默认false
    protected boolean isQuickIndexEnabled;

    protected transient int selectedIndex = NOT_SELECTED;

    protected transient int selectedSecondIndex = NOT_SELECTED;

    private Condition(Builder builder) {
        type = builder.type;
        title = builder.title;
        codeNamePairs = builder.codeNamePairs;
        indices = builder.indices;
        isQuickIndexEnabled = builder.isQuickIndexEnabled;
        filterType = builder.filterType;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public boolean isQuickIndexEnabled() {
        return isQuickIndexEnabled;
    }

    public int getType() {
        return type;
    }

    public String getTitle() {
        return title;
    }

    public int getSelectedIndex() {
        return selectedIndex;
    }

    public int getSelectedSecondIndex() {
        return selectedSecondIndex;
    }

    public List<CodeNamePair> getCodeNamePairs() {
        return codeNamePairs;
    }

    public List<String> getIndices() {
        return indices;
    }

    public void setSelectedIndex(int selectedIndex) {
        this.selectedIndex = selectedIndex;
    }

    public void setSelectedSecondIndex(int selectedSecondIndex) {
        this.selectedSecondIndex = selectedSecondIndex;
    }

    public int getFilterType() {
        return filterType;
    }

    public static final class Builder {

        private int type;

        private String title;

        private List<CodeNamePair> codeNamePairs;

        private List<String> indices;

        private boolean isQuickIndexEnabled;

        private int filterType = ConditionFilterType.FILTER_COMMON;

        public Builder title(String title) {
            this.title = title;
            return this;
        }

        public Builder type(@ConditionType int type) {
            this.type = type;
            return this;
        }

        public Builder codeNamePairs(List<CodeNamePair> codeNamePairs) {
            this.codeNamePairs = codeNamePairs;
            return this;
        }

        public Builder indices(List<String> indices) {
            this.indices = indices;
            return this;
        }

        @SuppressWarnings("UnusedReturnValue")
        public Builder quickIndexEnabled(boolean isQuickIndexEnabled) {
            this.isQuickIndexEnabled = isQuickIndexEnabled;
            return this;
        }

        public Builder filterType(int filterType) {
            this.filterType = filterType;
            return this;
        }

        public Condition build() {
            return new Condition(this);
        }
    }

}
