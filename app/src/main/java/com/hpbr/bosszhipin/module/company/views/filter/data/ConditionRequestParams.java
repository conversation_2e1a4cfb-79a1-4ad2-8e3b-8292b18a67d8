package com.hpbr.bosszhipin.module.company.views.filter.data;

@SuppressWarnings("WeakerAccess")
public class ConditionRequestParams {
    public long position1Code;
    public long position2Code;
    public long cityCode;
    public long experienceCode;
    public long salaryCode;
    public long jobType;
    public int filterPosition; // 用户点击快捷条传1，点击下拉筛选条传2
    public long degreeCode;

    public ConditionRequestParams() {
    }

    public void set(ConditionRequestParams requestParams) {
        this.position1Code = requestParams.position1Code;
        this.position2Code = requestParams.position2Code;
        this.cityCode = requestParams.cityCode;
        this.experienceCode = requestParams.experienceCode;
        this.salaryCode = requestParams.salaryCode;
        this.jobType = requestParams.jobType;
        this.filterPosition = requestParams.filterPosition;
        this.degreeCode = requestParams.degreeCode;
    }

    public void reSet() {
        this.position1Code = 0;
        this.position2Code = 0;
        this.cityCode = 0;
        this.experienceCode = 0;
        this.salaryCode = 0;
        this.jobType = 0;
        this.filterPosition = 0;
        this.degreeCode = 0;
    }

}
