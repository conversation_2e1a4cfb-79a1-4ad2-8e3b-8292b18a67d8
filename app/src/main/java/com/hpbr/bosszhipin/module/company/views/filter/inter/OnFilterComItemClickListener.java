package com.hpbr.bosszhipin.module.company.views.filter.inter;

import androidx.annotation.NonNull;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.adapter.CodeNameAdapter;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;

public interface OnFilterComItemClickListener {

    void onItemClick(@NonNull CodeNameAdapter adapter, Condition condition,
                     @NonNull CodeNamePair item, int position);
}
