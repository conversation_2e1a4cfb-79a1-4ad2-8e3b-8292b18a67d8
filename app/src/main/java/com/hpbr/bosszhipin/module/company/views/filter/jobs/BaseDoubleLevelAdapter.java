package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseDoubleLevelAdapter<Data> {

    public static final int NOT_SELECTED = -1;

    public static final String UNLIMITED = "不限";

    public static final String ALL = "全部";

    protected Condition condition;

    /*第一列数据*/
    protected List<CodeNamePair> firstLevels;

    public Condition getCondition() {
        return condition;
    }

    public List<CodeNamePair> getFirstLevels() {
        return firstLevels;
    }

    public void setCondition(Condition condition) {
        this.condition = condition;
    }

    public void setFirstLevels(List<CodeNamePair> firstLevels) {
        this.firstLevels = firstLevels;
    }

    public boolean isDataValid() {
        return null != condition && !LList.isEmpty(firstLevels);
    }

    public BaseDoubleLevelAdapter(Condition condition, List<CodeNamePair> firstLevels) {
        this.condition = condition;
        this.firstLevels = LList.isEmpty(firstLevels) ? new ArrayList<>() : firstLevels;
    }

    //左侧选中位置
    protected int mFirstLevelSelectedPosition = NOT_SELECTED;

    public void setFirstLevelSelectedPosition(int mFirstLevelSelectedPosition) {
        this.mFirstLevelSelectedPosition = mFirstLevelSelectedPosition;
    }

    public int getFirstLevelSelectedPosition() {
        return mFirstLevelSelectedPosition;
    }

    //右侧第一个选中位置
    protected int mSecondLevelSelectedPosition = NOT_SELECTED;

    public int getSecondLevelSelectedPosition() {
        return mSecondLevelSelectedPosition;
    }

    public void setSecondLevelSelectedPosition(int mSecondLevelFirstSelectedPosition) {
        this.mSecondLevelSelectedPosition = mSecondLevelFirstSelectedPosition;
    }

    /**
     * 左侧item数量
     *
     * @return item数量
     */
    public abstract int getFirstLevelItemCount();

    public abstract Data getFirstLevelDefItem();

    public abstract List<Data> getFirstLevelSelectedItems(boolean hasDefItem);

//    public abstract List<LevelBean> findFirstLevelSelectedItem();

    /**
     * 获取左侧item
     *
     * @param firstLevelPosition 左侧位置
     * @return item
     */
    public abstract Data getFirstLevelItem(int firstLevelPosition);

    public abstract int findFirstLevelItemPos(Data firstLevelItem);

    /**
     * 根据左侧位置，获取右侧item数量，+1表示将左侧的item放在右侧第一个位置
     *
     * @param firstLevelPosition 左侧位置
     * @return 右侧数量
     */
    public abstract int getSecondLevelItemCount(int firstLevelPosition);

    public abstract Data getSecondLevelDefItem(int firstLevelPosition);

    public abstract List<Data> getSecondLevelSelectedItems(boolean hasDefItem);

    /**
     * 获取右侧item
     *
     * @param firstLevelPosition  左侧的位置
     * @param secondLevelPosition 右侧的位置
     * @return 右侧的item
     */
    public abstract Data getSecondLevelItem(int firstLevelPosition, int secondLevelPosition);

}
