package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.data.CompanyConsts;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * 层级说明：Topic层、第一次、第二层
 */
public class FilterDoubleLevelAdapter extends BaseDoubleLevelAdapter<CodeNamePair> {

    public FilterDoubleLevelAdapter(Condition condition, List<CodeNamePair> firstLevels) {
        super(condition, firstLevels);
    }

    @Override
    public int getFirstLevelItemCount() {
        return LList.getCount(getFirstLevels());
    }

    @Override
    public CodeNamePair getFirstLevelDefItem() {
        CodeNamePair element = LList.getElement(getFirstLevels(), 0);
        return CompanyConsts.isDefaultItem(element) ? element : null;
    }

    @Override
    public List<CodeNamePair> getFirstLevelSelectedItems(boolean hasDefItem) {
        //默认
        CodeNamePair selectedItem = getFirstLevelItem(getFirstLevelSelectedPosition());

        List<CodeNamePair> selectedItems = new ArrayList<>();

        if (null != selectedItem) {
            selectedItems.add(selectedItem);
        } else if (hasDefItem) {
            selectedItem = getFirstLevelDefItem();
            if (null != selectedItem) {
                selectedItems.add(selectedItem);
            }
        }

        return selectedItems;
    }

    @Override
    public CodeNamePair getFirstLevelItem(int firstLevelPosition) {
        return LList.getElement(getFirstLevels(), firstLevelPosition);
    }

    @Override
    public int findFirstLevelItemPos(CodeNamePair firstLevelItem) {
        return 0;
    }

    @Override
    public int getSecondLevelItemCount(int firstLevelPosition) {
        CodeNamePair firstLevelItem = getFirstLevelItem(firstLevelPosition);
        return null != firstLevelItem ? LList.getCount(firstLevelItem.subLevelModelList) : 0;
    }

    @Override
    public CodeNamePair getSecondLevelDefItem(int firstLevelPosition) {
        CodeNamePair firstLevelItem = getFirstLevelItem(firstLevelPosition);
        CodeNamePair secondLevelItem = null != firstLevelItem ? LList.getElement(firstLevelItem.subLevelModelList, 0) : null;
        return  CompanyConsts.isDefaultItem(secondLevelItem) ? secondLevelItem : null;
    }

    @Override
    public List<CodeNamePair> getSecondLevelSelectedItems(boolean hasDefItem) {
        CodeNamePair selectedItem = getSecondLevelItem(
                getFirstLevelSelectedPosition(), getSecondLevelSelectedPosition());

        List<CodeNamePair> selectedItems = new ArrayList<>();

        if (null != selectedItem) {
            selectedItems.add(selectedItem);
        } else if (hasDefItem) {
            selectedItem = getSecondLevelDefItem(getFirstLevelSelectedPosition());
            if (null != selectedItem) {
                selectedItems.add(selectedItem);
            }
        }

        return selectedItems;
    }

    @Override
    public CodeNamePair getSecondLevelItem(int firstLevelPosition, int secondLevelPosition) {
        CodeNamePair firstLevelItem = getFirstLevelItem(firstLevelPosition);
        return null != firstLevelItem ? LList.getElement(
                firstLevelItem.subLevelModelList, secondLevelPosition) : null;
    }

}
