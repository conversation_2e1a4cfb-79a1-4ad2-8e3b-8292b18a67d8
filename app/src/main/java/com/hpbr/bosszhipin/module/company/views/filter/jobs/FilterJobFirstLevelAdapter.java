package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseViewHolder;
import com.filter.common.adapter.IFilterSelectAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;

import java.util.List;

/**
 * 职位筛选第一层级适配器
 */
public class FilterJobFirstLevelAdapter extends IFilterSelectAdapter<CodeNamePair> {

    protected FilterDoubleLevelAdapter mFilterJobAdapter;

    public void notifyDataJobAdapter(FilterDoubleLevelAdapter mFilterJobAdapter) {
        this.mFilterJobAdapter = mFilterJobAdapter;
        notifyDataSetChanged();
    }

    public FilterJobFirstLevelAdapter() {
        super(R.layout.layout_hot_hire_condition_filter_job_first_level_item);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, CodeNamePair item) {

        if (null == item) return;

        TextView keyword = helper.getView(R.id.keywords_view_text);

        keyword.setText(item.name);

        //默认选中
        keyword.setActivated(selectedItems.contains(item));
        helper.itemView.setBackgroundColor(ContextCompat.getColor(helper.itemView.getContext(), selectedItems.contains(item) ? R.color.color_FFF5F5F6_FF131314 : R.color.color_FFFFFFFF_FF2E2E31));
    }

    @Override
    public int getItemCount() {
        return null == mFilterJobAdapter ? 0 :
                mFilterJobAdapter.getFirstLevelItemCount();
    }

    @Nullable
    @Override
    public CodeNamePair getItem(int position) {
        return null == mFilterJobAdapter ? null :
                mFilterJobAdapter.getFirstLevelItem(position);
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @NonNull
    @Override
    public List<CodeNamePair> getData() {
        return super.getData();
    }
}
