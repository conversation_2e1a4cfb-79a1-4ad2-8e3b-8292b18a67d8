package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import androidx.annotation.Nullable;
import com.chad.library.adapter.base.BaseViewHolder;
import com.filter.common.adapter.MultiSelectAdapter;

import java.util.List;

/**
 * 多选适配器
 * 例：作为RecyclerView Item使用、Item可多选、单选
 */
public abstract class FilterJobMultiSelectAdapter<T, VH extends BaseViewHolder> extends MultiSelectAdapter<T, VH> {

    public FilterJobMultiSelectAdapter(int layoutResId) {
        super(layoutResId);
    }

    public FilterJobMultiSelectAdapter(int layoutResId, @Nullable List<T> data) {
        super(layoutResId, data);
    }
}
