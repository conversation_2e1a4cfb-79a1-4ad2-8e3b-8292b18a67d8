package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseViewHolder;
import com.filter.common.adapter.IFilterSelectAdapter;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;

import java.util.List;

/**
 * 职位筛选第二层级适配器
 */
public class FilterJobSecondLevelAdapter extends IFilterSelectAdapter<CodeNamePair> {

    protected FilterDoubleLevelAdapter mFilterJobAdapter;

    public void notifyDataJobAdapter(FilterDoubleLevelAdapter mFilterJobAdapter) {
        this.mFilterJobAdapter = mFilterJobAdapter;
        notifyDataSetChanged();
    }

    public FilterJobSecondLevelAdapter() {
        super(R.layout.layout_hot_hire_condition_filter_job_second_level_item);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, CodeNamePair item) {

        if (null == item) return;

        int adapterPosition = helper.getAdapterPosition();

        TextView keyword = helper.getView(R.id.keywords_view_text);

        keyword.setText(item.name);

        //默认选中
        keyword.setActivated(selectedItems.contains(item));
    }

    protected void activeView(TextView view, boolean active) {
        view.setActivated(active);
        if (active) {
            view.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.mipmap.ic_filter_location_select, 0);
        } else {
            view.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
    }

    @Override
    public int getItemCount() {
        return null != mFilterJobAdapter ? mFilterJobAdapter.getSecondLevelItemCount(
                mFilterJobAdapter.getFirstLevelSelectedPosition()) : 0;
    }

    @Nullable
    @Override
    public CodeNamePair getItem(int position) {
        return null != mFilterJobAdapter ? mFilterJobAdapter.getSecondLevelItem(
                mFilterJobAdapter.getFirstLevelSelectedPosition(), position) : null;
    }

    @Override
    public int getItemViewType(int position) {
        return 0;
    }

    @NonNull
    @Override
    public List<CodeNamePair> getData() {
        return super.getData();
    }
}
