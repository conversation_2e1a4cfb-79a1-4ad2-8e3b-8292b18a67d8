package com.hpbr.bosszhipin.module.company.views.filter.jobs;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.filter.common.data.listener.OnSubItemMultiSelectedListener;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.company.entity.CodeNamePair;
import com.hpbr.bosszhipin.module.company.views.filter.data.CompanyConsts;
import com.hpbr.bosszhipin.module.company.views.filter.data.Condition;
import com.hpbr.bosszhipin.module.company.views.filter.inter.OnFilterTwoLevelSelectListener;
import com.monch.lbase.util.LList;

import java.util.List;
import java.util.Set;

public class FilterJobTwoLevelLayout extends FrameLayout {

    protected Context mContext;

    private FilterDoubleLevelAdapter mFilterDoubleLevelAdapter;

    private RecyclerView mFirstRecyclerView;
    private FilterJobFirstLevelAdapter mFirstFilterAdapter;

    private RecyclerView mSecondRecyclerView;
    private FilterJobSecondLevelAdapter mSecondFilterAdapter;

    protected OnFilterTwoLevelSelectListener selectListener;

    public void setSelectListener(OnFilterTwoLevelSelectListener selectListener) {
        this.selectListener = selectListener;
    }

    public OnFilterTwoLevelSelectListener getSelectListener() {
        return selectListener;
    }

    public FilterJobTwoLevelLayout(@NonNull Context context) {
        this(context, null);
    }

    public FilterJobTwoLevelLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterJobTwoLevelLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mContext = context;

        View.inflate(context, R.layout.layout_hot_hire_condition_filter_job_layout, this);

        init();
    }

    private void init() {
        mFirstRecyclerView = findViewById(com.filter.R.id.first_rv_list);
        mSecondRecyclerView = findViewById(com.filter.R.id.second_rv_list);

        /*筛选数据处理适配器*/
        mFilterDoubleLevelAdapter = new FilterDoubleLevelAdapter(null, null);

        initFirstRecyclerView();
        initSecondRecyclerView();
    }

    private void initFirstRecyclerView() {
        mFirstRecyclerView.setNestedScrollingEnabled(false);
        mFirstRecyclerView.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.VERTICAL, false));
        mFirstFilterAdapter = new FilterJobFirstLevelAdapter();
        //单选、多选（最多可选择几个Item）
        mFirstFilterAdapter.setMaxSelectedCount(1);
        mFirstRecyclerView.setAdapter(mFirstFilterAdapter);
        mFirstFilterAdapter.setSelectedListener(new OnSubItemMultiSelectedListener<CodeNamePair>() {

            /*选中一级类选项时，一级类选项高亮，不回显（仍展示“推荐职位”），不默认选中二级类选项*/
            @Override
            public void onMultiSelected(@NonNull BaseQuickAdapter adapter, View view, int position, @NonNull CodeNamePair selectedBean, @NonNull Set<CodeNamePair> hasSelectedBean, boolean invertSelection) {
                //更新第一列选择的位置
                updateFirstLevelSelectedPosition(position);

                /*检查历史选择位置*/
                int lastSecondSelectedPos = BaseDoubleLevelAdapter.NOT_SELECTED;
                if (null != mFilterDoubleLevelAdapter && null != mFilterDoubleLevelAdapter.getCondition()
                        && mFilterDoubleLevelAdapter.getCondition().getSelectedIndex() == position) {
                    lastSecondSelectedPos = mFilterDoubleLevelAdapter.getCondition().getSelectedSecondIndex();
                }
                /*重置第二列已选位置*/
                updateSecondLevelSelectedPosition(lastSecondSelectedPos);
                //更新SecondLevel数据
                initSecondSelectedData();
            }
        });
    }

    private void initSecondRecyclerView() {
        mSecondRecyclerView.setNestedScrollingEnabled(false);
        mSecondRecyclerView.setLayoutManager(new LinearLayoutManager(mContext, RecyclerView.VERTICAL, false));
        mSecondFilterAdapter = new FilterJobSecondLevelAdapter();
        //单选、多选（最多可选择几个Item）
        mSecondFilterAdapter.setMaxSelectedCount(1);
        mSecondRecyclerView.setAdapter(mSecondFilterAdapter);
        mSecondFilterAdapter.setSelectedListener(new OnSubItemMultiSelectedListener<CodeNamePair>() {
            @Override
            public void onMultiSelected(@NonNull BaseQuickAdapter adapter, View view, int position, @NonNull CodeNamePair selectedBean, @NonNull Set<CodeNamePair> hasSelectedBean, boolean invertSelection) {
                /*更新第二列已选位置*/
                updateSecondLevelSelectedPosition(position);
                /*处理筛选结果*/
                handleFilterResult(position, selectedBean);
            }
        });
    }

    /**
     * 更新选中位置
     */
    private void updateFirstLevelSelectedPosition(int firstSelectedPosition) {
        mFilterDoubleLevelAdapter.setFirstLevelSelectedPosition(firstSelectedPosition);
    }

    private void updateSecondLevelSelectedPosition(int secondSelectedPosition) {
        mFilterDoubleLevelAdapter.setSecondLevelSelectedPosition(secondSelectedPosition);
    }

    /**
     * 更新筛选数据
     */
    public void updateSelectedData(Condition condition) {

        mFilterDoubleLevelAdapter.setCondition(condition);
        mFilterDoubleLevelAdapter.setFirstLevels(
                null != condition ? condition.getCodeNamePairs() : null);

        mFilterDoubleLevelAdapter.setFirstLevelSelectedPosition(null != condition ?
                condition.getSelectedIndex() : Condition.NOT_SELECTED);
        mFilterDoubleLevelAdapter.setSecondLevelSelectedPosition(null != condition ?
                condition.getSelectedSecondIndex() : Condition.NOT_SELECTED);

        initFirstSelectedData();
        initSecondSelectedData();
    }

    //第一层
    private void initFirstSelectedData() {
        if (null != mFilterDoubleLevelAdapter) {
            //第一层已选中Item
            List<CodeNamePair> firstLevelSelectedItems = mFilterDoubleLevelAdapter
                    .getFirstLevelSelectedItems(true);
            mFirstFilterAdapter.setSelectedItems(firstLevelSelectedItems);
            mFirstFilterAdapter.setDefaultItem(mFilterDoubleLevelAdapter.getFirstLevelDefItem());

            //默认Item数据（不限、全部、code <0）
            //单选、多选（最多可选择几个Item）
            mFirstFilterAdapter.setMaxSelectedCount(1);
            //限制单选反选、防止重复点击（并且不设置默认Item）
            mFirstFilterAdapter.setLimitSingleInvertSelection(true);
            mFirstFilterAdapter.setLimitSingleInvertSelectionNotify(true);
            //刷新数据
            mFirstFilterAdapter.notifyDataJobAdapter(mFilterDoubleLevelAdapter);

            //滚动到默认位置
            CodeNamePair firstSel = LList.getElement(firstLevelSelectedItems, 0);
            if (null != firstSel && firstSel.equals(mFilterDoubleLevelAdapter.getFirstLevelDefItem())) {
                mFirstRecyclerView.scrollToPosition(0);
            }
        }
    }


    //已选中Item数据
    private void initSecondSelectedData() {
        if (null != mFilterDoubleLevelAdapter) {
            //已选中Item数据
            List<CodeNamePair> secondLevelSelectedItems = mFilterDoubleLevelAdapter
                    .getSecondLevelSelectedItems(false);
            mSecondFilterAdapter.setSelectedItems(secondLevelSelectedItems);
            //默认Item数据（不限、全部、code <0）
            mSecondFilterAdapter.setDefaultItem(mFilterDoubleLevelAdapter.getSecondLevelDefItem(
                    mFilterDoubleLevelAdapter.getFirstLevelSelectedPosition()));

            mSecondFilterAdapter.notifyDataJobAdapter(mFilterDoubleLevelAdapter);

            //滚动到默认位置
            CodeNamePair secondSel = LList.getElement(secondLevelSelectedItems, 0);
            if (null != secondSel && secondSel.equals(mSecondFilterAdapter.getDefaultItem())) {
                mSecondRecyclerView.scrollToPosition(0);
            }
        }
    }

    /**
     * 最终获取的筛选结果
     */
    public void handleFilterResult(int secondSelectedPosition, @NonNull CodeNamePair secondSelectedItem) {
        /*第一列已选位置*/
        int firstLevelSelectedPosition = mFilterDoubleLevelAdapter.getFirstLevelSelectedPosition();
        CodeNamePair firstLevelItem = mFilterDoubleLevelAdapter.getFirstLevelItem(firstLevelSelectedPosition);
        long firstLevelCode = null != firstLevelItem ? firstLevelItem.code : 0;

        /*第二列 全选\不限*/
        boolean secondAllOrUnlimited = CompanyConsts.isDefaultItem(secondSelectedItem);

        /*更新职位筛选Tab名称*/
        String jobTabText = secondSelectedItem.name;
        if (secondAllOrUnlimited && null != firstLevelItem) {
            jobTabText = firstLevelItem.name;
            if (CompanyConsts.isDefaultItem(firstLevelItem)) {
                jobTabText = CompanyConsts.TITLE_HOT_JOB;
            }
        }

//        /*最终接口请求code*/
//        long secondLevelCode = secondSelectedItem.code;
//        if (secondAllOrUnlimited && null != firstLevelItem) {
//            resultCode = firstLevelItem.code;
//        }

        long secondLevelCode = secondSelectedItem.code;

        if (null != getSelectListener()) {
            getSelectListener().onFilterSelected(firstLevelSelectedPosition,
                    firstLevelCode, secondSelectedPosition, jobTabText, secondLevelCode);
        }
    }
}
