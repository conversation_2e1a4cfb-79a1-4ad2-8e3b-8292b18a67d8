package com.hpbr.bosszhipin.module.contacts;

import androidx.annotation.MainThread;

import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.BlackListRemovingRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * create by guofeng
 * date on 2022/4/24
 */

public class BlackRemoveRequestHelper {

    private BlackRemoveRequestHelper() {
    }

    public interface OnBlackRemoveCallBack {

        default void onStart() {
        }

        void onSuccess(long friendId, int friendSource);

        default void onComplete() {
        }
    }


    public interface OnGeekBlackRemoveCallBack {

        default void onStart() {
        }

        void onSuccess();

        default void onComplete() {
        }
    }

    @MainThread
    public static void bossBlackRemoveListener(long friendId, int friendSource, OnBlackRemoveCallBack onBlackRemoveCallBack) {
        BlackListRemovingRequest request = new BlackListRemovingRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onStart();
                }
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onSuccess(friendId, friendSource);
                }
            }

            @Override
            public void onComplete() {
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.friendSource = friendSource;
        request.blackUserId = friendId;
        request.identity = UserManager.getUserRole().get();
        HttpExecutor.execute(request);
    }



    @MainThread
    public static void geekBlackRemoveListener(String securityId, OnGeekBlackRemoveCallBack onBlackRemoveCallBack) {
        BlackListRemovingRequest request = new BlackListRemovingRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onStart();
                }
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onSuccess();
                }
            }

            @Override
            public void onComplete() {
                if (onBlackRemoveCallBack != null) {
                    onBlackRemoveCallBack.onComplete();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }
} 