package com.hpbr.bosszhipin.module.contacts;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 可扩展的复合过滤框架
 *
 * 核心特点：
 * 1. 支持多个过滤器组合，一次遍历完成
 * 2. 支持外部扩展除筛选以外的其他逻辑
 * 3. 支持链式调用，灵活组合
 */
public class ContactFilterFramework {

    /**
     * 单个过滤条件接口
     */
    public interface ContactFilter {
        /**
         * 测试联系人是否符合条件
         * @param contact 联系人
         * @return true-符合条件，false-不符合
         */
        boolean test(ContactBean contact);
    }

    /**
     * 数据处理器接口 - 用于扩展除筛选以外的逻辑
     */
    public interface ContactProcessor {
        /**
         * 处理联系人数据
         * @param contact 联系人
         */
        void process(ContactBean contact);
    }

    /**
     * 结果转换器接口
     */
    public interface ContactTransformer {
        /**
         * 转换结果列表
         * @param contacts 过滤后的联系人列表
         * @return 转换后的联系人列表
         */
        List<ContactBean> transform(List<ContactBean> contacts);
    }

    /**
     * 过滤构建器 - 支持链式调用
     */
    public static class FilterBuilder {
        private final List<ContactFilter> predicates = new ArrayList<ContactFilter>();
        private final List<ContactProcessor> processors = new ArrayList<ContactProcessor>();
        private ContactTransformer transformer = null;
        private int initialCapacity = -1;

        /**
         * 添加过滤条件
         */
        public FilterBuilder addFilter(ContactFilter predicate) {
            if (predicate != null) {
                predicates.add(predicate);
            }
            return this;
        }

        /**
         * 添加多个过滤条件
         */
        public FilterBuilder addFilters(ContactFilter[] predicates) {
            if (predicates != null) {
                for (ContactFilter predicate : predicates) {
                    addFilter(predicate);
                }
            }
            return this;
        }

        /**
         * 添加数据处理器（用于统计、计数、缓存等）
         */
        public FilterBuilder addProcessor(ContactProcessor processor) {
            if (processor != null) {
                processors.add(processor);
            }
            return this;
        }

        /**
         * 设置结果转换器（用于排序、分组等）
         */
        public FilterBuilder setTransformer(ContactTransformer transformer) {
            this.transformer = transformer;
            return this;
        }

        /**
         * 设置初始容量
         */
        public FilterBuilder setInitialCapacity(int capacity) {
            this.initialCapacity = capacity > 0 ? capacity : 100;
            return this;
        }

        /**
         * 构建并执行过滤
         */
        public List<ContactBean> apply(List<ContactBean> source) {
            return ContactFilterFramework.filter(source, this);
        }

        // Package-private getters
        List<ContactFilter> getPredicates() {
            return predicates;
        }

        List<ContactProcessor> getProcessors() {
            return processors;
        }

        ContactTransformer getTransformer() {
            return transformer;
        }

        int getInitialCapacity() {
            return initialCapacity;
        }
    }

    /**
     * 核心过滤方法 - 一次遍历完成所有操作
     */
    public static List<ContactBean> filter(@NonNull List<ContactBean> source, @NonNull FilterBuilder builder) {
        if (source == null || source.isEmpty()) {
            return new ArrayList<ContactBean>();
        }

        // 预分配容量
        int capacity = source.size();
        if (builder.getInitialCapacity() > 0) {
            capacity = Math.min(builder.getInitialCapacity(), source.size());
        }
        List<ContactBean> result = new ArrayList<ContactBean>(capacity);

        // 获取过滤条件和处理器
        List<ContactFilter> predicates = builder.getPredicates();
        List<ContactProcessor> processors = builder.getProcessors();

        // 单线程处理 - 一次遍历完成所有操作
        for (ContactBean contact : source) {
            if (contact == null) {
                continue;
            }

            // 应用所有过滤条件（按添加顺序）
            boolean passAllFilters = true;
            for (ContactFilter predicate : predicates) {
                if (!predicate.test(contact)) {
                    passAllFilters = false;
                    break; // 提前终止，不符合条件
                }
            }

            if (passAllFilters) {
                // 应用所有处理器（统计、缓存等）
                for (ContactProcessor processor : processors) {
                    processor.process(contact);
                }
                result.add(contact);
            }
        }

        // 应用结果转换器
        if (builder.getTransformer() != null) {
            return builder.getTransformer().transform(result);
        }

        return result;
    }

    /**
     * 便捷方法 - 创建过滤构建器
     */
    public static FilterBuilder newBuilder() {
        return new FilterBuilder();
    }

    /**
     * 便捷方法 - 简单过滤
     */
    public static List<ContactBean> simpleFilter(List<ContactBean> source, ContactFilter[] predicates) {
        return newBuilder()
                .addFilters(predicates)
                .apply(source);
    }

    /**
     * 便捷方法 - 单个过滤器
     */
    public static List<ContactBean> simpleFilter(List<ContactBean> source, ContactFilter predicate) {
        return newBuilder()
                .addFilter(predicate)
                .apply(source);
    }
} 