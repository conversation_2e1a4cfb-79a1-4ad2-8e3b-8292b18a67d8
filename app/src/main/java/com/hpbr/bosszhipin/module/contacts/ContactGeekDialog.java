package com.hpbr.bosszhipin.module.contacts;

import android.content.Context;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.entity.ParamBean;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import net.bosszhipin.api.ChatAgainGeekListBean;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_DOUBLE_CHAT_HI;

/**
 * create by guofeng
 * date on 2019-10-29
 */
public class ContactGeekDialog {

    private final Context context;

    private final List<String> selectIds = new ArrayList<>();

    public ContactGeekDialog(Context context) {
        this.context = context;
    }

    /*来源说明 0：默认来源 1：F4招聘数据 1002.182中入口进入(使用新接口)*/
    protected String sourceType;

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    private List<ChatAgainGeekListBean> memberList;

    public void setMemberList(List<ChatAgainGeekListBean> memberList) {
        this.memberList = memberList;
    }

    public void initSelectId(List<String> memberList) {
        selectIds.clear();
        if (memberList != null) {
            selectIds.addAll(memberList);
        }
    }

    private OnChangeCallBack onChangeCallBack;

    public void setOnChangeCallBack(OnChangeCallBack onChangeCallBack) {
        this.onChangeCallBack = onChangeCallBack;
    }

    private BottomView bottomView;

    private MTextView mTitleText;

    private void refreshTitle() {
        int selectCount = getGreetingAdapter().getSelectCount();
        int maxCont = LList.getCount(memberList);
        String previoutText = "主动沟通未回复的牛人(";
        String value = previoutText + selectCount + "/" + maxCont + ")";
        SpannableString spannableString = new SpannableString(value);
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(App.get(), R.color.app_green)), previoutText.length(), (previoutText.length() + String.valueOf(selectCount).length()), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        mTitleText.setText(spannableString);
    }

    private GreetingAdapter greetingAdapter;

    private GreetingAdapter getGreetingAdapter() {
        if (greetingAdapter == null) {
            greetingAdapter = new GreetingAdapter();
        }
        return greetingAdapter;
    }


    public void show() {
        View view = LayoutInflater.from(context).inflate(R.layout.view_contact_geek_dialog, null);

        mTitleText = view.findViewById(R.id.mTitleText);

        RecyclerView mRecycleView = view.findViewById(R.id.mRecycleView);
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(context, RecyclerView.VERTICAL, false);
        mRecycleView.setLayoutManager(linearLayoutManager);

        mRecycleView.setAdapter(getGreetingAdapter());
        getGreetingAdapter().setData(memberList);

        refreshTitle();

        bottomView = new BottomView(context, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
        view.findViewById(R.id.mClose).setOnClickListener(v -> {
            bottomView.dismissBottomView();
        });

        view.findViewById(R.id.mSend).setOnClickListener(v -> {

            if (onChangeCallBack != null) {
                onChangeCallBack.onSelectChangListener(selectIds);
            }

            StringBuilder StringBuilder = new StringBuilder();

            for (String selectId : selectIds) {
                StringBuilder.append(selectId);
                StringBuilder.append(",");
            }

            AnalyticsFactory.create()
                    .action(ACTION_CHAT_DOUBLE_CHAT_HI)
                    .param("p", StringBuilder.toString())
                    .param("p2", LList.getCount(selectIds))
                    .param("p3", getSourceTypeP())
                    .debug()
                    .build();


            if (bottomView != null) {
                bottomView.dismissBottomView();
            }


        });
    }


    private class GreetingAdapter extends RecyclerView.Adapter<GreetingAdapter.GreetViewHolder> {

        private final List<ChatAgainGeekListBean> data = new ArrayList<>();


        private int getSelectCount() {
            return LList.getCount(selectIds);
        }


        private void setData(List<ChatAgainGeekListBean> source) {
            data.clear();
            if (source != null) {
                data.addAll(source);
            }
            notifyDataSetChanged();
        }

        @NonNull
        @Override
        public GreetingAdapter.GreetViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new GreetingAdapter.GreetViewHolder(LayoutInflater.from(context).inflate(R.layout.item_greeting_dialog, null));
        }

        @Override
        public void onBindViewHolder(@NonNull GreetViewHolder holder, int position) {
            ChatAgainGeekListBean element = LList.getElement(data, position);
            if (element == null) return;
            holder.mAvatar.setImageURI(StringUtil.getNetworkUri(element.headUrl));
            holder.mChecked.setImageResource(selectIds.contains(element.encryptGeekId) ? R.mipmap.ic_greet_checked : R.mipmap.ic_greet_unchecked);
            holder.mChecked.setOnClickListener(v -> {

                if (selectIds.contains(element.encryptGeekId)) {
                    selectIds.remove(element.encryptGeekId);
                } else {
                    selectIds.add(element.encryptGeekId);
                }

                notifyItemChanged(position);

                refreshTitle();


            });

            holder.itemView.setOnClickListener(v -> {
                ParamBean paramBean = new ParamBean();
                //paramBean.userId = LText.getLong(element.geekId);
                //paramBean.jobId = LText.getLong(element.jobId);
                paramBean.securityId = element.securityId;
                BossPageRouter.jumpResumeActivity(context, paramBean);
            });

            holder.mName.setText(element.geekName);

            holder.mPosition.setText("求职: " + element.chatJobName);

            holder.mPosition.setVisibility(LText.empty(element.chatJobName) ? View.GONE : View.VISIBLE);

            holder.mInfo.setText(StringUtil.connectTextWithChar(" · ", element.workYear, element.edu, element.expectSalary));

            if (!LText.empty(element.organizationName) || !LText.empty(element.workPositionName)) {
                holder.mCompanyName.setText(StringUtil.connectTextWithChar(" · ", element.organizationName, element.workPositionName), View.GONE);
            } else if (!LText.empty(element.school) || !LText.empty(element.major)) {
                holder.mCompanyName.setText(StringUtil.connectTextWithChar(" · ", element.school, element.major), View.GONE);
            }
        }

        @Override
        public int getItemCount() {
            return LList.getCount(data);
        }

        private class GreetViewHolder extends RecyclerView.ViewHolder {

            private final SimpleDraweeView mAvatar;

            private final ImageView mChecked;

            private final MTextView mName;

            private final MTextView mInfo;

            private final MTextView mCompanyName;

            private final MTextView mPosition;

            public GreetViewHolder(@NonNull View itemView) {
                super(itemView);
                mAvatar = itemView.findViewById(R.id.mAvatar);
                mChecked = itemView.findViewById(R.id.mChecked);
                mName = itemView.findViewById(R.id.mName);
                mInfo = itemView.findViewById(R.id.mInfo);
                mCompanyName = itemView.findViewById(R.id.mCompanyName);
                mPosition = itemView.findViewById(R.id.mPosition);
            }
        }
    }

    public interface OnChangeCallBack {

        void onSelectChangListener(List<String> selectIds);

    }

    //p2 来源：1.F2列表 2.招聘数据中心
    public String getSourceTypeP() {
        String p = "1";
        if (Objects.equals("1", sourceType)) {
            p = "2";
        }
        return p;
    }

}
