package com.hpbr.bosszhipin.module.contacts;

import net.bosszhipin.api.ChatAgainGeekListBean;
import net.bosszhipin.api.bean.ServerGeekCardBean;
import net.bosszhipin.api.bean.VIPBuyDialogBean;

import java.util.List;

/**
 * Created by guofeng
 * on 2020-06-29.
 */

public interface OnGreetingResultCallBack {

    void onGreetingResultListener(VIPBuyDialogBean vipDialog, List<ServerGeekCardBean> geekList);


    void onAlreadySelectListener(List<ChatAgainGeekListBean> chaseQueryBeans,List<String> selectEncryIds);
}
