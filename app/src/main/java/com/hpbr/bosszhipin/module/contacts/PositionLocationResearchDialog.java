package com.hpbr.bosszhipin.module.contacts;

import android.app.Activity;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.AdditionalEvaluateIntermediaryRequest;
import net.bosszhipin.api.AdditionalEvaluateResponse;
import net.bosszhipin.api.bean.AdditionAnswerBean;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2021/4/27
 */

public class PositionLocationResearchDialog {

    public static class ResearchBean extends BaseServerBean {

        private static final long serialVersionUID = 1L;

        private String title;

        private List<ResearchItemBean> items;

        private boolean supportMulSelect;//是否支持多选择

        private ParamsBean paramsBean;

        public static class ResearchItemBean extends BaseServerBean {

            private static final long serialVersionUID = 1L;

            private String itemText;//标题

            private boolean isSelect;//是否选中

            private boolean needShowInputText;//是否显示输入框

            private String defaultHintText;//输入框默认文案

            private String inputText;//输入框的数据

            private String answerId;//提交数据的id

            private boolean isMustInput;//输入框是必须填写内容的

        }

        private static class ParamsBean extends BaseServerBean {

            private static final long serialVersionUID = 1L;

            private String evaluationId;

            private String securityId;

            private String answer;

            private String messageId;

            private int source;

        }

    }

    public static ResearchBean createResearchBean(String title,
                                                  boolean supportMulSelect,
                                                  List<AdditionAnswerBean> answerList,
                                                  String evaluationId,
                                                  String securityId,
                                                  String answer,
                                                  String messageId,
                                                  int source) {
        final ResearchBean researchBean = new ResearchBean();
        researchBean.title = title;
        researchBean.supportMulSelect = supportMulSelect;
        if (LList.getCount(answerList) > 0) {
            researchBean.items = new ArrayList<>();
            for (AdditionAnswerBean additionAnswerBean : answerList) {

                if (additionAnswerBean == null) continue;
                ResearchBean.ResearchItemBean researchItemBean = new ResearchBean.ResearchItemBean();
                researchItemBean.isSelect = false;
                researchItemBean.inputText = "";
                /**
                 * 0：有问题，没有输入框
                 * 1、有问题，有输入框
                 * 2、没问题，只有输入框
                 */
                researchItemBean.needShowInputText = additionAnswerBean.answerType == 1
                        || additionAnswerBean.answerType == 2;
                researchItemBean.answerId = additionAnswerBean.answerId;
                researchItemBean.defaultHintText = additionAnswerBean.answerText;
                researchItemBean.itemText = additionAnswerBean.answerName;
                researchItemBean.isMustInput = additionAnswerBean.forceInputText == 1;
                researchBean.items.add(researchItemBean);

            }
        }

        researchBean.paramsBean = new ResearchBean.ParamsBean();
        researchBean.paramsBean.answer = answer;
        researchBean.paramsBean.evaluationId = evaluationId;
        researchBean.paramsBean.securityId = securityId;
        researchBean.paramsBean.messageId = messageId;
        researchBean.paramsBean.source = source;

        return researchBean;
    }



    private final InputUtils inputUtils = new InputUtils(App.getAppContext(), 60);

    private ResearchBean researchBean;

    public void setResearchBean(ResearchBean researchBean) {
        this.researchBean = researchBean;
    }

    private BottomView bottomView;

    public void show() {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        View view = LayoutInflater.from(topActivity).inflate(R.layout.position_location_research_dialog, null);

        View mOutArea = view.findViewById(R.id.mOutArea);
        mOutArea.setOnTouchListener(this::onTouch);
        MTextView mSubmit = view.findViewById(R.id.mSubmit);
        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        ImageView mCloseView = view.findViewById(R.id.mCloseView);
        ScrollView mScrollView = view.findViewById(R.id.mScrollView);
        ConstraintLayout mConstraintLayout = view.findViewById(R.id.mConstraintLayout);
        LinearLayout mItemContainer = view.findViewById(R.id.mItemContainer);
        mCloseView.setOnClickListener(v -> dismiss());
        mSubmit.setOnClickListener(v -> submitListener());
        mTitleView.setText(researchBean.title);
        //刷新容器的itemView
        refreshContainerView(topActivity, mItemContainer, mScrollView, mConstraintLayout, mSubmit);
        bottomView = new BottomView(topActivity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }


    //检测提交按钮状态
    private void checkSubmitStatus(MTextView mSubmit) {
        //用户没有选择选项，或有必填文本未填写时，按钮置灰。
        List<ResearchBean.ResearchItemBean> selectItemBean = getSelectItemBean();

        boolean hasEmptyInput = false;
        for (ResearchBean.ResearchItemBean researchItemBean : selectItemBean) {
            if (researchItemBean.isMustInput && LText.empty(researchItemBean.inputText)) {
                hasEmptyInput = true;
                break;
            }
        }

        if (LList.isEmpty(selectItemBean) || hasEmptyInput) {
            mSubmit.setEnabled(false);
            mSubmit.setBackgroundColor(Color.parseColor("#6c15B3B3"));
        } else {
            mSubmit.setEnabled(true);
            mSubmit.setBackgroundResource(R.drawable.bg_selector_green_button);
        }

    }


    //点击区域外
    private boolean onTouch(View v, MotionEvent event) {
        dismiss();
        return true;
    }


    //刷新容器的itemView
    private void refreshContainerView(Activity topActivity,
                                      LinearLayout mItemContainer,
                                      ScrollView mScrollView,
                                      ConstraintLayout mConstraintLayout, MTextView mSubmit) {

        mItemContainer.removeAllViews();

        List<ResearchBean.ResearchItemBean> items = researchBean.items;
        if (items != null) {
            for (ResearchBean.ResearchItemBean item : items) {
                if (item == null) continue;
                String itemText = item.itemText;
                boolean isSelect = item.isSelect;
                String defaultHintText = item.defaultHintText;
                String inputText = item.inputText;
                boolean needShowInputText = item.needShowInputText;
                //添加ITEM_VIEW
                View itemView = LayoutInflater.from(topActivity).inflate(R.layout.item_position_location_research, null);
                ImageView mCheckView = itemView.findViewById(R.id.mCheckView);
                MTextView mTextContent = itemView.findViewById(R.id.mTextContent);

                mItemContainer.addView(itemView);
                mTextContent.setText(itemText);
                if (isSelect) {
                    mCheckView.setImageResource(R.mipmap.ic_pay_checked);
                    if (needShowInputText) {

                        View inputView = LayoutInflater.from(topActivity).inflate(R.layout.item_position_location_input, null);
                        EditText mEditTextView = inputView.findViewById(R.id.mEditTextView);
                        TextView mCountView = inputView.findViewById(R.id.mCountView);
                        mEditTextView.setText(inputText);
                        mEditTextView.setHint(defaultHintText);
                        mEditTextView.addTextChangedListener(new TextWatcher() {
                            @Override
                            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                            }

                            @Override
                            public void onTextChanged(CharSequence s, int start, int before, int count) {
                                item.inputText = s.toString();
                                inputUtils.checkInputCount(mCountView, item.inputText);
                                checkSubmitStatus(mSubmit);
                            }

                            @Override
                            public void afterTextChanged(Editable s) {

                            }
                        });
                        inputUtils.checkInputCount(mCountView, item.inputText);
                        mItemContainer.addView(inputView);
                    }
                } else {
                    mCheckView.setImageResource(R.mipmap.ic_pay_unchecked);
                }

                itemView.setOnClickListener(v -> {

                    //选择了，不能取消
                    if (item.isSelect) return;

                    if (researchBean.supportMulSelect) { //支持多选
                        item.isSelect = true;
                    } else {//不支持多选
                        clearPreSelect(items);
                        item.isSelect = true;
                    }

                    refreshContainerView(topActivity, mItemContainer, mScrollView, mConstraintLayout, mSubmit);
                });

            }
        }
        ////检测对话框最大高度
        checkMaxHeight(mScrollView, mItemContainer);
        //检测提交按钮状态
        checkSubmitStatus(mSubmit);
    }

    //检测对话框最大高度
    private void checkMaxHeight(ScrollView mScrollView, LinearLayout mItemContainer) {
        int measureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        mItemContainer.measure(measureSpec, measureSpec);
        ViewGroup.LayoutParams layoutParams = mScrollView.getLayoutParams();
        int measuredHeight = mItemContainer.getMeasuredHeight();
        int maxHeight = App.get().getDisplayHeight() / 2;
        if (measuredHeight > maxHeight) {
            layoutParams.height = maxHeight;
        } else {
            layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        }
        mScrollView.setLayoutParams(layoutParams);
    }

    //全部取消
    private void clearPreSelect(List<ResearchBean.ResearchItemBean> items) {
        if (items != null) {
            for (ResearchBean.ResearchItemBean item : items) {
                item.isSelect = false;
            }
        }
    }

    //post data to server
    private void submitListener() {

        ResearchBean.ParamsBean paramsBean = researchBean.paramsBean;
        if (paramsBean == null) return;

        if (checkError()) return;

        AdditionalEvaluateIntermediaryRequest additionalEvaluateIntermediaryRequest = new AdditionalEvaluateIntermediaryRequest(new ApiRequestCallback<AdditionalEvaluateResponse>() {
            @Override
            public void onSuccess(ApiData<AdditionalEvaluateResponse> data) {
                ToastUtils.showText("提交成功");
            }

            @Override
            public void onComplete() {
                dismiss();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        additionalEvaluateIntermediaryRequest.evaluationId = paramsBean.evaluationId;
        additionalEvaluateIntermediaryRequest.securityId = paramsBean.securityId;
        additionalEvaluateIntermediaryRequest.answer = paramsBean.answer;
        additionalEvaluateIntermediaryRequest.messageId = paramsBean.messageId;
        additionalEvaluateIntermediaryRequest.source = paramsBean.source;
        additionalEvaluateIntermediaryRequest.additionalAnswer = getAdditionAnswer();
        additionalEvaluateIntermediaryRequest.execute();

    }

    private boolean checkError() {
        List<ResearchBean.ResearchItemBean> selectItemBean = getSelectItemBean();
        for (ResearchBean.ResearchItemBean researchItemBean : selectItemBean) {
            String inputText = researchItemBean.inputText;
            if (inputUtils.isInputLargerThanMaxLength(inputText)) {
                ToastUtils.showText("超过字数限制");
                return true;
            }
        }
        return false;
    }


    private String getAdditionAnswer() {
        final JSONArray jsonArray = new JSONArray();
        List<ResearchBean.ResearchItemBean> selectItemBean = getSelectItemBean();
        for (ResearchBean.ResearchItemBean researchItemBean : selectItemBean) {

            try {
                final JSONObject jsonObject = new JSONObject();
                jsonObject.put("answerId", researchItemBean.answerId);
                jsonObject.put("answerText", researchItemBean.inputText);
                jsonArray.put(jsonObject);
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return jsonArray.toString();
    }

    //find the select bean object
    private List<ResearchBean.ResearchItemBean> getSelectItemBean() {
        final List<ResearchBean.ResearchItemBean> result = new ArrayList<>();
        if (researchBean.items != null) {
            for (ResearchBean.ResearchItemBean item : researchBean.items) {
                if (item == null) continue;
                if (item.isSelect) {
                    result.add(item);
                }
            }
        }
        return result;
    }

    //关闭弹窗
    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }

}