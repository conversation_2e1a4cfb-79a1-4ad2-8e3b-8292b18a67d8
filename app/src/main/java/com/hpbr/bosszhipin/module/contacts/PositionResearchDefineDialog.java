package com.hpbr.bosszhipin.module.contacts;

import android.app.Activity;
import android.graphics.Color;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MEditText;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.AdditionalEvaluateIntermediaryRequest;
import net.bosszhipin.api.AdditionalEvaluateResponse;
import net.bosszhipin.api.bean.AdditionAnswerBean;
import net.bosszhipin.base.ApiRequestCallback;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * create by guofeng
 * date on 2021/4/28
 */

public class PositionResearchDefineDialog {

    private BottomView bottomView;

    private final InputUtils inputUtils = new InputUtils(App.getAppContext(), 60);

    public void show() {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        View view = LayoutInflater.from(topActivity).inflate(R.layout.position_research_define_dialog, null);

        ImageView mCloseView = view.findViewById(R.id.mCloseView);
        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        MTextView mCountView = view.findViewById(R.id.mCountView);
        MEditText mEditText = view.findViewById(R.id.mEditText);
        MTextView mSubmit = view.findViewById(R.id.mSubmit);
        mCloseView.setOnClickListener(v -> dismiss());
        mSubmit.setOnClickListener(v -> onSubmitListener(mEditText.getTextContent()));
        mTitleView.setText(questionName);
        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                //检测提交按钮是否可以点击
                checkSubmitStatus(s.toString(), mSubmit, mCountView);
            }
        });

        inputUtils.checkInputCount(mCountView, "");
        checkSubmitStatus("", mSubmit, mCountView);

        bottomView = new BottomView(topActivity, R.style.BottomViewTheme_Transparent, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }


    /**
     * 检测提交按钮是否可以点击
     *
     * @param inputText
     * @param mSubmit
     * @param mCountView
     */
    private void checkSubmitStatus(String inputText, MTextView mSubmit, MTextView mCountView) {
        //是否必须输入文本
        boolean forceInput = additionAnswerBean.forceInputText == 1;
        if (forceInput && LText.empty(inputText)) {
            mSubmit.setEnabled(false);
            mSubmit.setBackgroundColor(Color.parseColor("#6c15B3B3"));
        } else {
            mSubmit.setEnabled(true);
            mSubmit.setBackgroundResource(R.drawable.bg_selector_green_button);
        }
        inputUtils.checkInputCount(mCountView, inputText);
    }



    private AdditionAnswerBean additionAnswerBean;

    private String evaluationId;

    private String securityId;

    private String answer;

    private String messageId;

    private int source;

    private String questionName;

    public void setAdditionAnswerBean(AdditionAnswerBean additionAnswerBean) {
        this.additionAnswerBean = additionAnswerBean;
    }

    public void setAdditionalQuestion(String questionName) {
        this.questionName = questionName;
    }

    public void setEvaluationId(String evaluationId) {
        this.evaluationId = evaluationId;
    }

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public void setSource(int source) {
        this.source = source;
    }

    //提交数据到服务器
    private void onSubmitListener(String inputText) {

        AdditionalEvaluateIntermediaryRequest additionalEvaluateIntermediaryRequest = new AdditionalEvaluateIntermediaryRequest(new ApiRequestCallback<AdditionalEvaluateResponse>() {
            @Override
            public void onSuccess(ApiData<AdditionalEvaluateResponse> data) {
                AppUtil.hideSoftInput(ForegroundUtils.get().getTopActivity());
            }

            @Override
            public void onComplete() {
                dismiss();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        additionalEvaluateIntermediaryRequest.evaluationId = evaluationId;
        additionalEvaluateIntermediaryRequest.securityId = securityId;
        additionalEvaluateIntermediaryRequest.answer = answer;
        additionalEvaluateIntermediaryRequest.messageId = messageId;
        additionalEvaluateIntermediaryRequest.source = source;
        additionalEvaluateIntermediaryRequest.additionalAnswer = getAdditionAnswer(inputText);
        additionalEvaluateIntermediaryRequest.execute();

    }

    //获得提交数据
    private String getAdditionAnswer(String answerText) {
        final JSONArray jsonArray = new JSONArray();
        final JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("answerId", additionAnswerBean.answerId);
            jsonObject.put("answerText", answerText);
        } catch (Exception e) {
            e.printStackTrace();
        }
        jsonArray.put(jsonObject);
        return jsonArray.toString();
    }

    //关闭窗口
    private void dismiss() {
        if (bottomView != null) {
            bottomView.dismissBottomView();
        }
    }
} 