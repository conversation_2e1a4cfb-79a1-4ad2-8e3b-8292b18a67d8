package com.hpbr.bosszhipin.module.contacts;

import android.content.Context;

import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.config.RequestMethod;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.BuildUrlResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import androidx.annotation.NonNull;

/**
 * create by guofeng
 * date on 2022/1/29
 */

public class PriorityNotifyHttp {


    private final Context mContext;


    public PriorityNotifyHttp(Context context) {
        this.mContext = context;
    }

    /**
     * 点击直聘协议
     *
     * @param param
     */
    public void onCLickZpUrl(@NonNull String param, String friendId, String friendSource) {

        long fId = LText.getLong(friendId);
        int fSource = LText.getInt(friendSource);

        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(fId, UserManager.getUserRole().get(), fSource);
        if (contactBean == null) return;

        ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
            @Override
            public void onCheckNoBlockListener() {
                super.onCheckNoBlockListener();

                SimpleApiRequest request = new SimpleApiRequest(BusinessUrlConfig.URL_ZP_BLOCK_REMIND_GEEK,
                        RequestMethod.GET);
                request.addParam("param", param);
                request.setRequestCallback(new ApiRequestCallback<BuildUrlResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        BaseActivity activity = null;
                        if (mContext instanceof BaseActivity) {
                            activity = (BaseActivity) mContext;
                            activity.showProgressDialog();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<BuildUrlResponse> data) {
                        BuildUrlResponse resp = data.resp;
                        String billUrl = resp.billUrl;
                        if (!LText.empty(billUrl)) {
                            new ZPManager(mContext, billUrl).handler();
                        }
                    }

                    @Override
                    public void onComplete() {
                        BaseActivity activity;
                        if (mContext instanceof BaseActivity) {
                            activity = (BaseActivity) mContext;
                            activity.dismissProgressDialog();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
                HttpExecutor.execute(request);
            }
        }, ContactKeyManager.BgSource.GRAY_URL);

    }

    /**
     * 点击聊天更多按钮
     */
    public void onClickMore(ContactBean contact) {

        String securityId = contact.securityId;

        if (LText.empty(securityId)) return;

        ContactKeyManager.getInstance().onCheckListener(contact, new ContactKeyManager.OnContactKeyCheckCallBack2() {
            @Override
            public void onCheckNoBlockListener() {
                super.onCheckNoBlockListener();
                SimpleApiRequest request = new SimpleApiRequest(BusinessUrlConfig.URL_ZP_BLOCK_GEEK_CHAT_ENTER, RequestMethod.GET);
                request.addParam("securityId", securityId);
                request.setRequestCallback(new ApiRequestCallback<BuildUrlResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        BaseActivity activity = null;
                        if (mContext instanceof BaseActivity) {
                            activity = (BaseActivity) mContext;
                            activity.showProgressDialog();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<BuildUrlResponse> data) {
                        BuildUrlResponse resp = data.resp;
                        String billUrl = resp.billUrl;
                        if (!LText.empty(billUrl)) {
                            new ZPManager(mContext, billUrl).handler();
                        }
                    }

                    @Override
                    public void onComplete() {
                        BaseActivity activity = null;
                        if (mContext instanceof BaseActivity) {
                            activity = (BaseActivity) mContext;
                            activity.dismissProgressDialog();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
                HttpExecutor.execute(request);

            }
        }, ContactKeyManager.BgSource.MORE_VIP);

    }
}