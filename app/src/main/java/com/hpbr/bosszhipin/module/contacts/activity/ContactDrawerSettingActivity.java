package com.hpbr.bosszhipin.module.contacts.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactCache;
import com.hpbr.bosszhipin.module.contacts.constant.IntentParamConstant;
import com.hpbr.bosszhipin.module.contacts.manager.RemoveTargetUserManager;
import com.hpbr.bosszhipin.module.contacts.manager.TopUnTopRequestManager;
import com.hpbr.bosszhipin.module.contacts.viewmodel.ContactDrawerSettingViewModel;
import com.hpbr.bosszhipin.module.contacts.views.DrawerSettingEntraceView;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.utils.AppAnalysisUtil;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.DrawerInfoResponse;

import java.util.ArrayList;
import java.util.List;

public class ContactDrawerSettingActivity extends BaseAwareActivity<ContactDrawerSettingViewModel> implements View.OnClickListener {

    private AppTitleView title_view;
    private SimpleDraweeView simpleDraweeView_img;
    private TextView tv_drawer_title;
    private TextView tv_drawer_desc;
    private ImageView iv_pinned_switch_btn;
    private ImageView iv_dont_disturb_switch_btn;
    private ConstraintLayout cl_delete;
    private ConstraintLayout doNotDisturbContainer;
    private long friendId;
    private int jumpFrom;
    private ContactBean contactBean;
    private NestedScrollView nestedScrollView_content;
    private final RemoveTargetUserManager removeTargetUserManager = new RemoveTargetUserManager(this);
    private String drawerName = "";
    /*当前抽屉是否开启了「置顶」，默认为false*/
    private boolean isOpenTop;
    /*当前抽屉是否开启了「免打扰」，默认为false*/
    private boolean isOpenNoDisturb;
    /*切换开关的类型-「置顶」*/
    private final int SWITCH_TYPE_PIN_TOP = 1;
    /*切换开关的类型-「免打扰」*/
    private final int SWITCH_TYPE_NO_DISTURB = 2;

    /**
     * 打开「联系人抽屉」设置页面
     *
     * @param context
     */
    public static void start(@NonNull Context context, long friendId) {
       start(context, friendId, 0);
    }

    public static void start(@NonNull Context context, long friendId, int from) {
        Intent intent = new Intent(context, ContactDrawerSettingActivity.class);
        intent.putExtra(IntentParamConstant.INTENT_FRIEND_ID, friendId);
        intent.putExtra(IntentParamConstant.JUMP_FROM, from);
        AppUtil.startActivity(context, intent);
    }

    @Override
    protected int contentLayout() {
        return R.layout.activity_contact_drawer_setting;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        initParams();
        initView();
        initEventListener();
        initLiveDataObserve();
        initData();
    }

    private void initParams() {
        Intent intent = getIntent();
        if (intent == null) return;
        friendId = intent.getLongExtra(IntentParamConstant.INTENT_FRIEND_ID, 0);
        jumpFrom = intent.getIntExtra(IntentParamConstant.JUMP_FROM, 0);
    }

    private void initView() {
        setStatusBarColor(ContextCompat.getColor(this, R.color.color_FFF5F5F6_FF131314));
        title_view = findViewById(R.id.title_view);
        title_view.setDividerInvisible();
        title_view.setBgAlpha(0);
        nestedScrollView_content = findViewById(R.id.nestedScrollView_content);
        nestedScrollView_content.setVisibility(View.INVISIBLE);
        simpleDraweeView_img = findViewById(R.id.simpleDraweeView_img);
        tv_drawer_title = findViewById(R.id.tv_drawer_title);
        tv_drawer_desc = findViewById(R.id.tv_drawer_desc);
        iv_pinned_switch_btn = findViewById(R.id.iv_pinned_switch_btn);
        iv_dont_disturb_switch_btn = findViewById(R.id.iv_dont_disturb_switch_btn);
        cl_delete = findViewById(R.id.cl_delete);
        doNotDisturbContainer = findViewById(R.id.cl_dont_disturb);
        doNotDisturbContainer.setVisibility(jumpFrom == IntentParamConstant.FROM_SystemSubscriptionActivity ? View.GONE : View.VISIBLE);
    }

    private void initEventListener() {
        title_view.setBackClickListener(v -> onBackPressed());
        iv_pinned_switch_btn.setOnClickListener(this);
        iv_dont_disturb_switch_btn.setOnClickListener(this);
        cl_delete.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int viewId = v.getId();
        if (viewId == R.id.iv_pinned_switch_btn) {/*点击「置顶消息」的开关*/
            handlePinTop();
        } else if (viewId == R.id.iv_dont_disturb_switch_btn) {/*点击「消息免打扰」的开关*/
            if (contactBean == null) return;
            mViewModel.setNoDistrub(contactBean, contactBean.noDisturb == 1 ? 0 : 1);
        } else if (viewId == R.id.cl_delete) {/*点击「删除」按钮*/
            handleDelete();
        }
    }

    private void initLiveDataObserve() {
        /*获取系统抽屉信息成功*/
        mViewModel.getDrawerInfoLiveData.observe(this, drawerInfoResponse -> {
            if (drawerInfoResponse == null) return;
            renderInfo(drawerInfoResponse);
        });
        /*置顶或取消置顶成功*/
        mViewModel.pinnedTopLiveData.observe(this, isTop -> {
            if (isTop == null) return;
            setOpenSwitch(SWITCH_TYPE_PIN_TOP, isTop);
        });
        /*开启或关闭免打扰成功*/
        mViewModel.openNoDisturbLiveData.observe(this, isOpenNoDisturb -> {
            if (isOpenNoDisturb == null) return;
            setOpenSwitch(SWITCH_TYPE_NO_DISTURB, isOpenNoDisturb);
        });
    }

    private void initData() {
        contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
        loadData();
    }

    /**
     * 加载数据
     */
    private void loadData() {
        if (contactBean == null) return;
        mViewModel.getDrawerInfo(String.valueOf(friendId), contactBean.securityId);
    }

    /**
     * 渲染数据
     *
     * @param drawerInfoResponse
     */
    private void renderInfo(@NonNull DrawerInfoResponse drawerInfoResponse) {
        if (contactBean == null) return;
        nestedScrollView_content.setVisibility(View.VISIBLE);

        /*ICON和标题*/
        if (friendId == DrawerSettingEntraceView.DrawerType.STUDY_REMIND) {
            simpleDraweeView_img.setImageResource(R.mipmap.ic_study_service);
            tv_drawer_title.setText("学习服务");
        } else {
            if (contactBean.avatarResource > 0) {
                simpleDraweeView_img.setImageURI(StringUtil.getResouceUri(contactBean.avatarResource));
            } else {
                ViewCommon.setAvatar(simpleDraweeView_img, contactBean.friendDefaultAvatarIndex, contactBean.friendDefaultAvatar);
            }
            tv_drawer_title.setText(contactBean.friendName);
        }

        drawerName = tv_drawer_title.getText() != null ? tv_drawer_title.getText().toString() : "";
        AppAnalysisUtil.dotDrawEntrySettingClick(drawerName);//埋点

        /*简介*/
        String introduce = drawerInfoResponse.drawerInfo != null ? drawerInfoResponse.drawerInfo.introduction : "";
        tv_drawer_desc.setText(introduce);
        tv_drawer_desc.setVisibility(TextUtils.isEmpty(introduce) ? View.GONE : View.VISIBLE);
        /*是否置顶*/
        setOpenSwitch(SWITCH_TYPE_PIN_TOP, contactBean.isTop);
        /*是否开启免打扰*/
        setOpenSwitch(SWITCH_TYPE_NO_DISTURB, contactBean.noDisturb == 1);
    }

    /**
     * 设置开关状态
     *
     * @param switchType
     * @param isOpenSwitch
     */
    private void setOpenSwitch(int switchType, boolean isOpenSwitch) {
        if (switchType == SWITCH_TYPE_PIN_TOP) {/*「置顶」开关切换*/
            iv_pinned_switch_btn.setImageResource(isOpenSwitch ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
            this.isOpenTop = isOpenSwitch;
        } else if (switchType == SWITCH_TYPE_NO_DISTURB) {/*「免打扰」开关切换*/
            iv_dont_disturb_switch_btn.setImageResource(isOpenSwitch ? R.mipmap.ic_online_switch_on : R.mipmap.ic_online_switch_off);
            this.isOpenNoDisturb = isOpenSwitch;
        }
    }

    /**
     * 处理置顶的逻辑
     */
    private void handlePinTop() {
        if (contactBean == null) return;
        TopUnTopRequestManager topRequestManager = new TopUnTopRequestManager(this);
        if (contactBean.isTop) {
            topRequestManager.cancelFriendTop(contactBean, () -> mViewModel.pinnedTopLiveData.postValue(false), true);
        } else {
            topRequestManager.setFriendTop(contactBean, () -> {
                mViewModel.pinnedTopLiveData.postValue(true);
            }, true);
        }
    }

    /**
     * 处理删除的逻辑
     */
    private void handleDelete() {
        AppAnalysisUtil.dotDrawSettingDelete(drawerName);//埋点
        if (friendId == DrawerSettingEntraceView.DrawerType.STUDY_REMIND) {
            if (LList.getCount(studyServiceList) == 0) return;
            for (Long friendId : studyServiceList) {
                if (friendId == null) continue;
                ContactBean contactBeanItem = ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
                if (contactBeanItem == null) continue;
                removeTargetUserManager.removeListener(contactBeanItem, true);
            }
        } else {
            if (contactBean == null) return;
            removeTargetUserManager.removeListener(contactBean, true);
        }
        jumpToMainActivity(this);
        ToastUtils.showText("删除成功");
    }

    /**
     * 关闭当前页面并回到主页面
     *
     * @param context
     */
    private void jumpToMainActivity(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
        AppUtil.startActivity(context, intent, true);
    }

    /**
     * 学习服务的子项
     */
    private final List<Long> studyServiceList = new ArrayList<Long>() {
        private static final long serialVersionUID = 76453514875677546L;

        {
            add(MqttConfig.SYSTEM_TITAN_TAB1_USER_ID);
            add(MqttConfig.SYSTEM_TITAN_TAB2_USER_ID);
            add(MqttConfig.SYSTEM_TITAN_TAB3_USER_ID);
        }
    };

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        AppAnalysisUtil.dotDrawSettingStatus(drawerName, this.isOpenTop, this.isOpenNoDisturb);//埋点
        AppUtil.finishActivity(this);
    }
}