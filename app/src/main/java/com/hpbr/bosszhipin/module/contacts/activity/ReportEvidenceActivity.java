package com.hpbr.bosszhipin.module.contacts.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.TextView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.ImageUploadDialog2;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.util.MultiplyFileUploadHelper;
import com.hpbr.bosszhipin.module.photoselect.PhotoSelectManager;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.UserReportRequest;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * 作者：YaLin
 * 日期：2016/8/8.
 */
public class ReportEvidenceActivity extends BaseActivity {

    private static final int MAX_REASON_TEXT_COUNT = 500;

    private static final int MAX_IMAGES_COUNT = 6;
    public static final int REQ_GALLERY = 100;
    public static final int REQ_CAMERA = 101;

    //举报原因分类：
    public static final int CODE_REASON_OTHER = 2006;

    private EditText mEtContent;

    private TextView mTvTextCount;

    private ImagesAdapter mAdapter;

    private long mUserId;
    private long mReasonCode;
    private long jobId;
    private long expectId;

    private static final String REPORT_SECURITY_ID = Constants.PREFIX + ".REPORT_SECURITY_ID";

//    @Deprecated
//    private long mTargetId;

    private int source = SOURCE_CHAT;

    /***
     * 本地记录的举报入口, source 0:旧版本未知 1:聊天 2:简历 3:职位详情 4:客服（714）5:面试详情页，6:极速处理页, 7:面试安全评价完成触发投诉弹窗
     * 说明：
     *  埋点detail-report-done：举报来源 source 0:旧版本未知 1:聊天 2:简历 3:职位详情 4:客服（714）5:面试详情页，6:极速处理页 7:面试安全评价完成触发投诉弹窗
     *  埋点detail-report-click：举报来源 source 0:旧版本未知 1:聊天 2:简历 3:职位详情 4:客服（714）5:面试详情页，6:极速处理页
     *  接口所需来源为（boss举报来源）：接口传参使用 0：未知 1:聊天 2：简历 3:招聘  4:面试 6 ：智慧石（客服）
     */
    public static final int SOURCE_OTHER = 0;
    public static final int SOURCE_CHAT = 1;//聊天
    public static final int SOURCE_GEEK_RESUME = 2;//简历
    public static final int SOURCE_BOSS_DETAIL = 3;//职位详情
    public static final int SOURCE_CUSTOMER_SERVICE = 4;//客服
    public static final int SOURCE_GEEK_INTERVIEW_FEEDBACK = 5;//面试详情页
    public static final int SOURCE_GEEK_RESUME_QUICK_HANDLER = 6;//简历极速处理页
    public static final int SOURCE_BOSS_DETAIL_SAFETIP = 7;//职位详情
    public static final int SOURCE_BOSS_DETAIL_QUICK_HANDLER = 8;//职位极速处理页
    public static final int SOURCE_GEEK_CHAT_SCREEN = 9;//聊天详情截图举报
    public static final int SOURCE_INTERVIEW_SECURITY_DIALOG = 10;//面试安全评价完成触发投诉弹窗

    //在职位详情页举报入口：1：右上角感叹号 2：温馨提示中的举报
    public static final int SOURCE_GEEK_DETAIL_POSITION_TITLE = 1;
    public static final int SOURCE_GEEK_DETAIL_POSITION_LINK = 2;

    private static final String REPORT_USER_ID = Constants.PREFIX + ".REPORT_USER_ID";
    private static final String REPORT_REASON_CODE = Constants.PREFIX + ".REPORT_REASON_CODE";
    private static final String REPORT_SOURCE = Constants.PREFIX + ".REPORT_SOURCE";
    private String securityId;

    private static final String REPORT_JOB_ID = Constants.PREFIX + ".REPORT_JOB_ID";
    private static final String REPORT_EXPECT_ID = Constants.PREFIX + ".REPORT_EXPECT_ID";

    public static void startActivity(Context context, long reportUserId, long jobId, long expectId, long code, int source, String securityId) {
        Intent intent = getIntent(context, reportUserId, jobId, expectId, code, source, securityId);
        AppUtil.startActivity(context, intent);
    }

    public static void startActivityForResult(Context context, long reportUserId, long jobId, long expectId, long code, int source, int requestCode, String securityId) {
        Intent intent = getIntent(context, reportUserId, jobId, expectId, code, source, securityId);
        AppUtil.startActivityForResult(context, intent, requestCode);
    }

    private static Intent getIntent(Context context, long reportUserId, long jobId, long expectId, long code, int source, String securityId) {
        Intent intent = new Intent(context, ReportEvidenceActivity.class);
        intent.putExtra(REPORT_USER_ID, reportUserId);
        intent.putExtra(REPORT_JOB_ID, jobId);
        intent.putExtra(REPORT_EXPECT_ID, expectId);
        intent.putExtra(REPORT_REASON_CODE, code);
        intent.putExtra(REPORT_SOURCE, source);
        intent.putExtra(REPORT_SECURITY_ID, securityId);
        return intent;
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!getData()) {
            T.ss(R.string.string_data_error);
            AppUtil.finishActivity(this);
            return;
        }
        setContentView(R.layout.activity_report_evidence);
        initViews();
    }


    private boolean getData() {
        Intent intent = getIntent();
        mUserId = intent.getLongExtra(REPORT_USER_ID, 0);
        mReasonCode = intent.getLongExtra(REPORT_REASON_CODE, 0);
        // 5.31新增字段，牛人:jobId boss:expectId
        jobId = intent.getLongExtra(REPORT_JOB_ID, 0);
        expectId = intent.getLongExtra(REPORT_EXPECT_ID, 0);
        source = intent.getIntExtra(REPORT_SOURCE, SOURCE_CHAT);
        securityId = intent.getStringExtra(REPORT_SECURITY_ID);
        return mReasonCode > 0 && mUserId > 0;
    }

    private void initViews() {
        AppTitleView titleView = findViewById(R.id.title_view);
        titleView.setTitle("提交举报证据");
        titleView.setBackClickListener(R.mipmap.ic_action_close_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBack();
            }
        });
        titleView.setAction1ClickListener(R.mipmap.ic_action_commit_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkAndReport();
            }
        });

        TextView tvPrompt = findViewById(R.id.report_evidence_tv_prompt);
        TextView tvCrop = findViewById(R.id.report_evidence_image_crop);
        mEtContent = findViewById(R.id.report_evidence_et_reason);
        mTvTextCount = findViewById(R.id.report_evidence_tv_text_count);
        GridView gvImages = findViewById(R.id.report_evidence_gv_images);

        tvPrompt.setText(Html.fromHtml(getString(R.string.report_evidence_prompt)));
        tvCrop.setText(getString(R.string.report_evidence_images));


        mAdapter = new ImagesAdapter(this, null);
        mAdapter.setOnMoreClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showAddActionViews();
            }
        });
        gvImages.setAdapter(mAdapter);

        mEtContent.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                int length = s.toString().length();
                String text = length + "/" + MAX_REASON_TEXT_COUNT;
                mTvTextCount.setText(text);
                if (length > MAX_REASON_TEXT_COUNT) {
                    mTvTextCount.setTextColor(getResources().getColor(R.color.app_red));
                } else {
                    mTvTextCount.setTextColor(getResources().getColor(R.color.app_green));
                }
            }
        });
        String text = 0 + "/" + MAX_REASON_TEXT_COUNT;
        mTvTextCount.setText(text);
    }


    private void showAddActionViews() {

        ImageUploadDialog2 mImageUploadDialog = new ImageUploadDialog2(this);
        mImageUploadDialog.show(new ImageUploadDialog2.OnClickCallBack() {
            @Override
            public void onCameraClickListener() {

                PhotoSelectManager.jumpForCameraResult(ReportEvidenceActivity.this, cameraFileUri -> {
                    if (mAdapter != null) {
                        mAdapter.addImage(cameraFileUri.toString());
                    }
                });

            }

            @Override
            public void onGalleryClickListener() {
                int maxCount = MAX_IMAGES_COUNT;
                if (mAdapter != null) {
                    maxCount -= LList.getCount(mAdapter.getAdapterList());
                }
                PhotoSelectManager.jumpForGalleryFromReport(ReportEvidenceActivity.this, maxCount, fileList -> {
                    //刷新列表
                    if (mAdapter != null) {
                        if (!LList.isEmpty(fileList)) {
                            List<String> reportImages = new ArrayList<>();
                            for (Uri uri : fileList) {
                                if (uri == null) continue;
                                reportImages.add(uri.toString());
                            }
                            mAdapter.addStringList(reportImages);
                        }
                    }
                });
            }
        });

    }


    private void checkAndReport() {
        if (TextUtils.isEmpty(mEtContent.getText().toString().trim())) {
            T.ss(getString(R.string.input_report_reason));
            return;
        }
        if (mEtContent.getText().toString().trim().length() > MAX_REASON_TEXT_COUNT) {
            T.ss("举报理由最多只能有" + MAX_REASON_TEXT_COUNT + "个字符");
            return;
        }
        List<Uri> uploadList = mAdapter.getAdapterList();
        showProgressDialog(R.string.loading);
        if (LList.isEmpty(uploadList)) {//没有图片只上传文字
            doTextPostRequest("");
        } else {//既有文字又有图片
            doImageAndTextPostRequest(uploadList);
        }
    }

    private void doImageAndTextPostRequest(List<Uri> uploadList) {
        //上传图片
        MultiplyFileUploadHelper uploadFileUtil = new MultiplyFileUploadHelper();
        uploadFileUtil.executeUpload(uploadList, new MultiplyFileUploadHelper.OnUploadCallBack() {
            @Override
            public void onSuccessListener(List<String> successUrlList) {
                int successCount = LList.getCount(successUrlList);
                if (successCount > 0) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < successUrlList.size(); i++) {
                        String url = LList.getElement(successUrlList, i);
                        if (i != 0) sb.append(",");
                        sb.append(url);
                    }
                    doTextPostRequest(sb.toString());
                }
            }

            @Override
            public void onFailedListener(List<Uri> failedList) {
                dismissProgressDialog();
                int failedCount = LList.getCount(failedList);
                if (failedCount > 0) {
                    T.ss("图片上传失败");
                    mAdapter.setFileList(failedList);
                }

            }
        });
    }

    private void doTextPostRequest(String imageUris) {
        UserReportRequest request = new UserReportRequest(new ApiRequestCallback<EmptyResponse>() {
            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                EmptyResponse resp = data.resp;

                if (resp != null) {
                    new DialogUtils.Builder(ReportEvidenceActivity.this)
                            .setTitle("举报成功")
                            .setDesc("我们会尽快核查并及时与您反馈，感谢您对BOSS直聘的支持！")
                            .setSingleButton()
                            .setPositiveAction("好的", new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    setResult(RESULT_OK);
                                    AppUtil.finishActivity(ReportEvidenceActivity.this);
                                }
                            }).build().show();
                }

                AnalyticsFactory.create().action(AnalyticsAction.ACTION_DETAIL_REPORT_DONE)
                        .param("p", String.valueOf(mUserId))
                        .param("p2", String.valueOf(jobId))
                        .param("p3", String.valueOf(expectId))
                        .param("p4", String.valueOf(mReasonCode))
                        .param("p5", mEtContent.getText().toString().trim())
                        .param("p6", String.valueOf(source))
                        .build();
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.reportedId = mUserId + "";
        request.reasonCode = mReasonCode + "";
        request.source = covertSource(source);
        request.content = mEtContent.getText().toString().trim();

        if (!TextUtils.isEmpty(imageUris)) {
            request.imgUrl = imageUris;
        }
        request.targetId = String.valueOf(UserManager.isBossRole() ? expectId : jobId);
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }


    /**
     * 将本地页面跳转的来源转换为接口所需要的source
     * <p>
     * 接口所需来源为（boss举报来源）：接口传参使用 0：未知 1:聊天 2：简历 3:招聘  4:面试 6 ：智慧石（客服）
     */
    private int covertSource(int source) {
        switch (source) {
            case SOURCE_CHAT:
                return 1;
            case SOURCE_GEEK_RESUME:
            case SOURCE_GEEK_RESUME_QUICK_HANDLER:
                return 2;
            case SOURCE_BOSS_DETAIL:
            case SOURCE_BOSS_DETAIL_QUICK_HANDLER:
                return 3;
            case SOURCE_CUSTOMER_SERVICE:
                return 6;
            case SOURCE_GEEK_INTERVIEW_FEEDBACK:
            case SOURCE_INTERVIEW_SECURITY_DIALOG:
                return 4;
            case SOURCE_GEEK_CHAT_SCREEN:
                return 8;
            default:
                return 0;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void onBack() {
        if (isEdit()) {
            editDialog();
        } else {
            AppUtil.finishActivity(this);
        }
    }

    private boolean isEdit() {
        int count = 0;
        if (mAdapter != null) {
            count = mAdapter.getCount();
        }
        return !TextUtils.isEmpty(mEtContent.getText().toString())
                || count > 0;
    }

    private void editDialog() {
        DialogUtils d = new DialogUtils.Builder(this)
                .setDoubleButton()
                .setTitle(R.string.warm_prompt)
                .setDesc(R.string.string_content_has_not_save)
                .setPositiveAction(R.string.string_confirm, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        AppUtil.finishActivity(ReportEvidenceActivity.this);
                    }
                })
                .setNegativeAction(R.string.string_cancel)
                .build();
        d.show();
    }

    private static class ImagesAdapter extends BaseAdapter {


        private static final int TYPE_IMAGE = 0;
        private static final int TYPE_MORE = 1;

        private static final int ITEM_TYPE_COUNT = 2;

        private Context mContext;

        private List<String> filePathList;

        private int mItemSize;

        private View.OnClickListener mOnMoreClickListener;

        private ImagesAdapter(Context context, List<String> mList) {
            mContext = context;
            filePathList = mList;

            int totalWidth = App.get().getDisplayWidth() - 2 * Scale.dip2px(context, 20);
            mItemSize = totalWidth / 3 - Scale.dip2px(context, 4);
        }

        private void setOnMoreClickListener(View.OnClickListener onMoreClickListener) {
            mOnMoreClickListener = onMoreClickListener;
        }

        private void addImage(String path) {
            if (filePathList == null) {
                filePathList = new ArrayList<>();
            }
            if (!LText.empty(path)) {
                filePathList.add(path);
                notifyDataSetChanged();
            }
        }

        private void addStringList(List<String> pathList) {
            if (LList.isEmpty(pathList)) return;
            if (filePathList == null) {
                filePathList = new ArrayList<>();
            }
            filePathList.addAll(pathList);
            notifyDataSetChanged();
        }


        private void setFileList(List<Uri> pathList) {
            if (filePathList == null) {
                filePathList = new ArrayList<>();
            }
            filePathList.clear();
            if (!LList.isEmpty(pathList)) {
                for (Uri uri : pathList) {
                    filePathList.add(uri.toString());
                }
            }
            notifyDataSetChanged();
        }

        private void removeImage(String path) {
            filePathList.remove(path);
            notifyDataSetChanged();
        }

        private List<Uri> getAdapterList() {
            List<Uri> fileList = new ArrayList<>();
            if (filePathList != null) {
                for (String path : filePathList) {
                    fileList.add(Uri.parse(path));
                }
            }
            return fileList;
        }

        @Override
        public int getItemViewType(int position) {
            int imageCount = filePathList == null ? 0 : filePathList.size();
            if (imageCount >= MAX_IMAGES_COUNT) {
                return TYPE_IMAGE;
            }
            if (position == imageCount) {
                return TYPE_MORE;
            }
            return TYPE_IMAGE;
        }

        @Override
        public int getViewTypeCount() {
            return ITEM_TYPE_COUNT;
        }

        @Override
        public int getCount() {
            int imageCount = filePathList == null ? 0 : filePathList.size();
            if (imageCount == MAX_IMAGES_COUNT) {
                return imageCount;
            }
            return imageCount + 1;
        }

        @Override
        public String getItem(int position) {
            if (getItemViewType(position) == TYPE_IMAGE) {
                return filePathList.get(position);
            }
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(final int position, View convertView, ViewGroup parent) {
            if (getItemViewType(position) == TYPE_IMAGE) {
                ImageViewHolder imageViewHolder;
                if (convertView == null) {
                    convertView = LayoutInflater.from(mContext).inflate(R.layout.item_report_evidence_image, parent, false);
                    imageViewHolder = new ImageViewHolder();
                    imageViewHolder.imageView = convertView.findViewById(R.id.report_evidence_item_image);
                    imageViewHolder.deleteView = convertView.findViewById(R.id.report_evidence_item_delete);
                    convertView.setTag(imageViewHolder);
                } else {
                    imageViewHolder = (ImageViewHolder) convertView.getTag();
                }

                final String path = getItem(position);

                imageViewHolder.imageView.setImageURI(Uri.parse(path));
                imageViewHolder.deleteView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        removeImage(path);
                    }
                });

            } else {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.item_report_evidence_more, parent, false);
                convertView.setOnClickListener(mOnMoreClickListener);
            }
            convertView.getLayoutParams().width = mItemSize;
            convertView.getLayoutParams().height = mItemSize;
            return convertView;
        }
    }

    private static class ImageViewHolder {
        SimpleDraweeView imageView;
        View deleteView;
    }


}
