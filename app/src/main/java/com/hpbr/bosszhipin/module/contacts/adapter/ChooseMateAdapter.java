package com.hpbr.bosszhipin.module.contacts.adapter;

import android.content.Context;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;
import com.hpbr.bosszhipin.module.contacts.entity.HighLight;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LText;

/**
 * Created by guofeng
 * on 2017/4/28.
 */

public class ChooseMateAdapter extends LBaseAdapter<CompanyMateBean> {

    public ChooseMateAdapter(Context context) {
        super(context);
    }

    @Override
    public View getView(int position, View convertView, CompanyMateBean item, LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_choose_mate, null);
            holder = new ViewHolder();
            holder.ivAvatar = (SimpleDraweeView) convertView.findViewById(R.id.iv_avatar);
            holder.tvName = (TextView) convertView.findViewById(R.id.tv_name);
            holder.tvPosition = (TextView) convertView.findViewById(R.id.tv_position);
            holder.tvMail = (TextView) convertView.findViewById(R.id.tv_mail);
            holder.tvSplit = (TextView) convertView.findViewById(R.id.tv_split);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (item != null) {
            setText(holder.tvName, item.name, item.nameHighLight);
            setText(holder.tvPosition, item.position, item.positionHighLight);
            holder.ivAvatar.setImageURI(StringUtil.getNetworkUri(item.tiny));
            if (!LText.empty(item.name) && !LText.empty(item.position)) {
                holder.tvSplit.setText("丨");
            } else {
                holder.tvSplit.setText("");
            }
            if (item.isAssociateFriend) {
                setText(holder.tvMail, item.mail, item.mailHighLight);
            } else {
                holder.tvMail.setText(item.unAssociateMate);
            }
        }

        return convertView;
    }

    private void setText(TextView textView, String text, HighLight highLight) {
        if (highLight != null && !LText.empty(text)) {
            int start = highLight.getStart();
            int end = highLight.getEnd();
            if (start >= 0 && end > 0 && start <= text.length() - 1 && end <= text.length()) {
                SpannableString spannableString = new SpannableString(text);
                spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(getContext(), R.color.app_green)), highLight.getStart(), highLight.getEnd(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                textView.setText(spannableString);
                return;
            }
        }
        textView.setText(text);
    }


    static class ViewHolder {
        SimpleDraweeView ivAvatar;
        TextView tvName;
        TextView tvPosition;
        TextView tvMail;
        TextView tvSplit;
    }


}
