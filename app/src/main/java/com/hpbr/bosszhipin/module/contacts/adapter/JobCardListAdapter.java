package com.hpbr.bosszhipin.module.contacts.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.main.viewholder.JobCardHolder;
import com.monch.lbase.adapter.LBaseAdapter;

import net.bosszhipin.api.bean.ServerJobCardBean;

import java.util.List;

import static com.hpbr.bosszhipin.module.main.views.card.JobCardView.CARD_STYLE_DEFAULT;

/**
 * Author: ZhouYou
 * Date: 2018/1/22.
 */
public class JobCardListAdapter extends LBaseAdapter<ServerJobCardBean> {

    private boolean isFromF2;

    public JobCardListAdapter(Context context, List<ServerJobCardBean> data) {
        super(context, data);
    }

    public void setFromF2() {
        isFromF2 = true;
    }

    @Override
    public View getView(int position, View convertView, ServerJobCardBean item, LayoutInflater inflater) {
        JobCardHolder holder = null;
        if (convertView != null && convertView.getTag() != null && convertView.getTag() instanceof JobCardHolder) {
            holder = (JobCardHolder) convertView.getTag();
        }
        if (holder == null) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_position_card, null);
            holder = new JobCardHolder(convertView);
            holder.setCardStyle(CARD_STYLE_DEFAULT);
            convertView.setTag(holder);
        }
        if (item != null) {
            if (isFromF2) {
                holder.setF2ListItem(item);
            } else {
                holder.setData(item);
            }
        }
        return convertView;
    }
}
