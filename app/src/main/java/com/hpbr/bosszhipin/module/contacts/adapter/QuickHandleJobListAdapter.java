package com.hpbr.bosszhipin.module.contacts.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.entity.QuickHandleBean;
import com.hpbr.bosszhipin.module.main.views.card.JobCardView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LDate;
import com.monch.lbase.util.LText;
import com.twl.ui.DotUtils;

import java.util.List;

/**
 * Author: ZhouYou
 * Date: 2018/8/14.
 */
public class QuickHandleJobListAdapter extends LBaseAdapter<QuickHandleBean> {

    private Activity activity;
    private OnJobStartChatListener listener;

    public QuickHandleJobListAdapter(Activity activity, List<QuickHandleBean> data, OnJobStartChatListener listener) {
        super(activity, data);
        this.activity = activity;
        this.listener = listener;
    }

    @Override
    public View getView(int position, View convertView, QuickHandleBean item, LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_quick_handle_job, null);
            holder = new ViewHolder(convertView);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        holder.setData(activity, item);
        holder.clContactMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (item != null) {
                    long friendId = 0;
                    String securityId = "";
                    if (item.contactBean != null) {
                        friendId = item.contactBean.friendId;
                        securityId = item.contactBean.securityId;
                    }
                    long jobId = 0;
                    long expectId = 0;
                    int friendSource = 0;
                    if (item.jobBean != null) {
                        jobId = item.jobBean.jobId;
                        expectId = item.jobBean.expectId;
                        friendSource = item.jobBean.friendSource;
                    }
                    if (listener != null) {
                        listener.onStartChat(friendId, securityId, jobId, expectId, friendSource);
                    }
                }
            }
        });
        return convertView;
    }

    static class ViewHolder {
        JobCardView jobCardView;

        ConstraintLayout clContactMessage;
        MTextView tvContactMessageTime;
        MTextView tvContactMessage;
        MTextView tvContactMessageCount;

        public ViewHolder(View convertView) {
            jobCardView = convertView.findViewById(R.id.job_card_view);
            jobCardView.init();
            clContactMessage = convertView.findViewById(R.id.cl_contact_message);
            tvContactMessageTime = convertView.findViewById(R.id.tv_contact_message_time);
            tvContactMessage = convertView.findViewById(R.id.tv_contact_message);
            tvContactMessageCount = convertView.findViewById(R.id.tv_contact_message_count);
        }

        public void setData(Activity activity, QuickHandleBean item) {
            if (item == null || item.jobBean == null || item.contactBean == null) return;
            jobCardView.setJobQuickHandle(item.jobBean);
            ContactBean contact = item.contactBean;
            String time = createChatTime(contact);
            tvContactMessageTime.setText(time, View.GONE);
            tvContactMessage.setText(contact.lastChatText);
            //https://jira.kanzhun-inc.com/browse/BSA-14305?filter=-1
            //测试要求没有聊天文案 不显示底部控件
            clContactMessage.setVisibility(LText.empty(contact.lastChatText) ? View.GONE : View.VISIBLE);
            int messageCount = contact.noneReadCount;
            DotUtils.showCountDot(activity, tvContactMessageCount, messageCount);
        }
    }

    public interface OnJobStartChatListener {
        void onStartChat(long friendId, String securityId, long jobId, long expectId, int friendSource);
    }

    private static String createChatTime(ContactBean contactBean) {
        long dateTime = 0;
        if (contactBean.lastChatTime > 0) {
            dateTime = contactBean.lastChatTime;
        } else if (contactBean.updateTime > 0) {
            dateTime = contactBean.updateTime;
        }
        if (dateTime > 0) {
            if (LDate.isToday(dateTime)) {
                return LDate.getDate(dateTime, "HH:mm");
            } else if (LDate.isYesterday(dateTime)) {
                return "昨天";
            } else {
                return LDate.getDate(dateTime, "M月d日");
            }
        }
        return "";
    }
}
