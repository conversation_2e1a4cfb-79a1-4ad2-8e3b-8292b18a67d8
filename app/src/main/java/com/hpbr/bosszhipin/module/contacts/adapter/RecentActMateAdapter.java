package com.hpbr.bosszhipin.module.contacts.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.adapter.LBaseAdapter;
import com.monch.lbase.util.LText;

/**
 * Created by guofeng
 * on 2017/6/26.
 */

public class RecentActMateAdapter extends LBaseAdapter<CompanyMateBean> {

    public RecentActMateAdapter(Context context) {
        super(context);
    }

    @Override
    public View getView(int position, View convertView, CompanyMateBean item, LayoutInflater inflater) {
        ViewHolder holder;
        if (convertView == null) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_choose_mate, null);
            holder = new ViewHolder();
            holder.ivAvatar = (SimpleDraweeView) convertView.findViewById(R.id.iv_avatar);
            holder.tvName = (TextView) convertView.findViewById(R.id.tv_name);
            holder.tvPosition = (TextView) convertView.findViewById(R.id.tv_position);
            holder.tvMail = (TextView) convertView.findViewById(R.id.tv_mail);
            holder.tvSplit = (TextView) convertView.findViewById(R.id.tv_split);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (item != null) {
            holder.tvName.setText(item.name);
            holder.tvPosition.setText(item.position);
            holder.ivAvatar.setImageURI(StringUtil.getNetworkUri(item.tiny));
            if (!LText.empty(item.name) && !LText.empty(item.position)) {
                holder.tvSplit.setText("丨");
            } else {
                holder.tvSplit.setText("");
            }
            holder.tvMail.setText(item.mail);
        }
        return convertView;
    }

    static class ViewHolder {
        SimpleDraweeView ivAvatar;
        TextView tvName;
        TextView tvPosition;
        TextView tvMail;
        TextView tvSplit;
    }

}
