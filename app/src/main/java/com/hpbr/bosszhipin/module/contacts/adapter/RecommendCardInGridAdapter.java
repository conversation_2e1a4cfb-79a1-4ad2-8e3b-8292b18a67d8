package com.hpbr.bosszhipin.module.contacts.adapter;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.google.android.flexbox.FlexboxLayout;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.ViewCommon;
import com.hpbr.bosszhipin.module.main.views.F1WordTextSwitcher;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerGeekCardBean;
import net.bosszhipin.api.bean.ServerGeekCardEduBean;
import net.bosszhipin.api.bean.ServerGeekCardWorkBean;
import net.bosszhipin.api.bean.ServerHighlightDescBean;
import net.bosszhipin.api.bean.ServerRecOnlineBean;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import zpui.lib.ui.floatlayout.ZPUIFloatLayout;
import zpui.lib.ui.shadow.layout.ZPUIConstraintLayout;
import zpui.lib.ui.utils.ZPUIDisplayHelper;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

/**
 * Author: zhouyou
 * Date: 2022/4/11
 */
public class RecommendCardInGridAdapter extends BaseRvAdapter<ServerGeekCardBean, BaseViewHolder> {

    private static final float CARD_WIDTH_A = 152f;
    private static final float CARD_HEIGHT_A = 229f;

    public RecommendCardInGridAdapter(@Nullable List<ServerGeekCardBean> data) {
        super(R.layout.item_grid_f2_recommend_for_you_geek_card, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, ServerGeekCardBean bean) {
        ZPUIConstraintLayout clCard = helper.getView(R.id.cl_card);
        clCard.setLayoutParams(getCardParams(clCard));

        MTextView tvGeekName = helper.getView(R.id.tv_geek_name);
        MTextView tvWorkEduDesc = helper.getView(R.id.tv_work_edu_desc);
        FlexboxLayout flWorkEduDesc = helper.getView(R.id.fl_work_edu_desc);
        SimpleDraweeView ivAvatar = helper.getView(R.id.iv_avatar);
        ImageView ivGender = helper.getView(R.id.iv_gender);
        SimpleDraweeView ivOnlineState = helper.getView(R.id.iv_online_state);

        LinearLayout llWorkExp = helper.getView(R.id.ll_work_exp);
        ivAvatar.setImageURI(bean.geekAvatar);
        tvGeekName.setText(bean.geekName);

        ivGender.setImageResource(ViewCommon.getGenderIcon(bean.geekGender));

        ServerRecOnlineBean recOnline = bean.recOnline;
        if (recOnline != null && recOnline.listingOnline != null) {
            int onlineImageHeight = recOnline.listingOnline.heightPx;
            int onlineImageWidth = recOnline.listingOnline.widthPx;
            ConstraintLayout.LayoutParams ivOnlineStateParams = (ConstraintLayout.LayoutParams) ivOnlineState.getLayoutParams();
            if (onlineImageHeight > 0 && onlineImageWidth > 0) {
                ivOnlineStateParams.dimensionRatio = onlineImageWidth + ":" + onlineImageHeight;
            }
            ivOnlineState.setLayoutParams(ivOnlineStateParams);
            ivOnlineState.setImageURI(recOnline.listingOnline.onlineImgUrl);
            ivOnlineState.setVisibility(VISIBLE);
        } else {
            ivOnlineState.setVisibility(GONE);
        }

        if (!TextUtils.isEmpty(bean.subTitle)) {
            //经验简介 用subTitle替换，具体内容有服务端下发
            flWorkEduDesc.setVisibility(GONE);
            tvWorkEduDesc.setText(bean.subTitle, GONE);
        } else {
            // 经验简介
            List<String> list = new ArrayList<>();
            if (!TextUtils.isEmpty(bean.geekWorkYear)) {
                list.add(bean.geekWorkYear);
            }
            if (!TextUtils.isEmpty(bean.geekDegree)) {
                list.add(bean.geekDegree);
            }
            if (!TextUtils.isEmpty(bean.salary)) {
                list.add(bean.salary);
            }
            tvWorkEduDesc.setVisibility(GONE);
            flWorkEduDesc.setVisibility(View.VISIBLE);
            setWorkEduDesc(flWorkEduDesc, list);
        }

        boolean hasExp = false;
        if (!LList.isEmpty(bean.geekWorks)) {
            hasExp = true;
            setWorkExp(llWorkExp, bean.geekWorks);
        } else if (!TextUtils.isEmpty(bean.expectText)) {
            hasExp = true;
            setExpect(llWorkExp, bean.pushCardExpectName);
        } else if (!LList.isEmpty(bean.geekEdus)) {
            hasExp = true;
            setEduExp(llWorkExp, bean.geekEdus);
        }
        llWorkExp.setVisibility(hasExp ? VISIBLE : GONE);
        // 设置标签
        setMatchWords(helper, getMatchWords(bean.hlmatches));

        F1WordTextSwitcher textSwitcher = helper.getView(R.id.text_switcher_panel);
        List<ServerHighlightDescBean> switcherWords = getF1SwitchWords(bean.hlmatches);
        if (LList.isEmpty(switcherWords)) {
            textSwitcher.setVisibility(GONE);
        } else {
            textSwitcher.setVisibility(VISIBLE);
        }
        textSwitcher.start(switcherWords);
    }

    private ConstraintLayout.LayoutParams getCardParams(ZPUIConstraintLayout clCard) {
        ConstraintLayout.LayoutParams cardParams = (ConstraintLayout.LayoutParams) clCard.getLayoutParams();
        int width = ZPUIDisplayHelper.dp2px(mContext, CARD_WIDTH_A);
        int height = ZPUIDisplayHelper.dp2px(mContext, CARD_HEIGHT_A);
        cardParams.width = width;
        cardParams.height = height;
        return cardParams;
    }

    private List<ServerHighlightDescBean> getMatchWords(List<ServerHighlightDescBean> hlmatches) {
        if (!LList.isEmpty(hlmatches)) {
            List<ServerHighlightDescBean> list = new ArrayList<>();
            for (ServerHighlightDescBean item : hlmatches) {
                if (item == null || TextUtils.isEmpty(item.content)) continue;
                if (LList.isEmpty(item.indexList)) {
                    list.add(item);
                }
            }
            return list;
        }
        return null;
    }

    private List<ServerHighlightDescBean> getF1SwitchWords(List<ServerHighlightDescBean> hlmatches) {
        if (!LList.isEmpty(hlmatches)) {
            List<ServerHighlightDescBean> list = new ArrayList<>();
            for (ServerHighlightDescBean item : hlmatches) {
                if (item == null || TextUtils.isEmpty(item.content)) continue;
                if (item.colour > 0 && !LList.isEmpty(item.indexList)) {
                    list.add(item);
                }
            }
            return list;
        }
        return null;
    }

    private List<ServerHighlightDescBean> getF1Words() {
        List<ServerHighlightDescBean> list = new ArrayList<>();
        ServerHighlightDescBean word1 = new ServerHighlightDescBean();
        word1.content = "本周13位BOSS和TA聊过";
        word1.colour = 1;

        ServerHighlightDescBean word2 = new ServerHighlightDescBean();
        word2.content = "刚刚活跃";
        word2.colour = 2;

        ServerHighlightDescBean word3 = new ServerHighlightDescBean();
        word3.content = "回复消息很积极";
        word3.colour = 3;

        list.add(word1);
        list.add(word2);
        list.add(word3);

        return list;
    }

    private void setWorkEduDesc(FlexboxLayout flWorkEduDesc, List<String> list) {
        flWorkEduDesc.removeAllViews();
        if (LList.isEmpty(list)) return;
        int size = list.size();
        int padding = ZPUIDisplayHelper.dp2px(mContext, 7);
        for (int i = 0; i < size; i++) {
            String s = list.get(i);
            View view = LayoutInflater.from(mContext).inflate(R.layout.item_f1_card_tag_705, null);
            View viewDivider = view.findViewById(R.id.view_divider);
            ConstraintLayout clParent = view.findViewById(R.id.cl_parent);
            MTextView tvTag = view.findViewById(R.id.tv_tag);
            tvTag.setText(s);
            viewDivider.setVisibility(i == size - 1 ? GONE : View.VISIBLE);
            flWorkEduDesc.addView(view);
        }
    }

    private void setWorkExp(LinearLayout llExp, @NonNull List<ServerGeekCardWorkBean> workList) {
        llExp.removeAllViews();
        workList = workList.subList(0, 1);
        int size = workList.size();
        for (int i = 0; i < size; i++) {
            ServerGeekCardWorkBean item = workList.get(i);
            if (item == null) continue;
            View view = LayoutInflater.from(mContext).inflate(R.layout.item_f1_exp_in_push_geek_card, null);
            MTextView tvContentTitle = view.findViewById(R.id.tv_content_title);
            MTextView tvContentSubtitle = view.findViewById(R.id.tv_content_subtitle);
            tvContentTitle.setText(item.company);
            tvContentSubtitle.setText(item.positionName == null ? item.title : item.positionName);
            llExp.addView(view);
        }
    }

    private void setExpect(LinearLayout llExp, @NonNull String expectText) {
        llExp.removeAllViews();
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_f1_exp_in_push_geek_card, null);
        MTextView tvContentTitle = view.findViewById(R.id.tv_content_title);
        MTextView tvContentSubtitle = view.findViewById(R.id.tv_content_subtitle);
        tvContentTitle.setText("求职期望：");
        tvContentSubtitle.setText(expectText);
        llExp.addView(view);
    }

    private void setEduExp(LinearLayout llExp, @NonNull List<ServerGeekCardEduBean> eduList) {
        llExp.removeAllViews();
        eduList = eduList.subList(0, 1);
        int size = eduList.size();
        for (int i = 0; i < size; i++) {
            ServerGeekCardEduBean item = eduList.get(i);
            if (item == null) continue;
            View view = LayoutInflater.from(mContext).inflate(R.layout.item_f1_exp_in_push_geek_card, null);
            MTextView tvContentTitle = view.findViewById(R.id.tv_content_title);
            MTextView tvContentSubtitle = view.findViewById(R.id.tv_content_subtitle);
            tvContentTitle.setText(item.school);
            tvContentSubtitle.setText(item.major);
            llExp.addView(view);
        }
    }

    private void setMatchWords(BaseViewHolder helper, List<ServerHighlightDescBean> hlMatches) {
        ZPUIFloatLayout floatLayout = helper.getView(R.id.flow_layout);
        if (LList.isEmpty(hlMatches)) {
            floatLayout.setVisibility(View.GONE);
        } else {
            floatLayout.setVisibility(View.VISIBLE);
            floatLayout.removeAllViews();
            for (ServerHighlightDescBean bean : hlMatches) {
                if (bean == null || TextUtils.isEmpty(bean.content)) continue;
                floatLayout.addView(getTag(bean.content));
            }
        }
    }

    private MTextView getTag(@NonNull String text) {
        int padding6 = ZPUIDisplayHelper.dp2px(mContext, 6);
        int padding3 = ZPUIDisplayHelper.dp2px(mContext, 3);
        MTextView textView = new MTextView(mContext);
        textView.setText(text);
        textView.setPadding(padding6, padding3, padding6, padding3);
        textView.setGravity(Gravity.CENTER);
        textView.setSingleLine();

        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f);
        textView.setTextColor(ContextCompat.getColor(mContext, R.color.text_c6));
        textView.setBackgroundResource(R.drawable.bg_f1_match_word_gray);
        return textView;
    }
}
