package com.hpbr.bosszhipin.module.contacts.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.main.adapter.F2ContactHolder;
import com.monch.lbase.adapter.LBaseAdapter;

import zpui.lib.ui.utils.listener.ZPUIOnTouchHelper;

/**
 * @ClassName ：ReplyMeAdapter
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2021/8/10  3:07 下午
 */
public class ReplyMeAdapter extends LBaseAdapter<ContactBean> {
    public ReplyMeAdapter(Context context) {
        super(context);
    }

    @Override
    public View getView(int position, View convertView, final ContactBean item, LayoutInflater inflater) {
        F2ContactHolder holder;
        if (convertView == null) {
            holder = new F2ContactHolder();
            convertView = holder.initView(getContext());
            convertView.setTag(holder);
        } else {
            holder = (F2ContactHolder) convertView.getTag();
        }
        /*禁用头像的点击操作*/
        holder.setAvatarClickForbidden(true);

        holder.ll_item_root.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.color_FF1D222E));
        holder.mTvName.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFFFFFF));
        holder.mTvPositionAndCompany.setTextColor(ContextCompat.getColor(getContext(), R.color.color_80FFFFFF));
        holder.mTvTime.setTextColor(ContextCompat.getColor(getContext(), R.color.color_80FFFFFF));
        holder.mTvMsg.setTextColor(ContextCompat.getColor(getContext(), R.color.color_FFFFFFFF));

        holder.initValue(getContext(), item, ROLE.BOSS);



        if (onUnfitListTouchListener != null) {

            //根据点击位置弹出pop 使用
            ZPUIOnTouchHelper.registerViewTouch(getContext(), holder.convertView, new ZPUIOnTouchHelper.ZPUISimpleEventListener() {
                @Override
                public void onLongClick(View v, MotionEvent event) {
                    if (onUnfitListTouchListener != null) {
                        onUnfitListTouchListener.onItemLongClick(item, event);
                    }
                }

                @Override
                public void onClick(View v, MotionEvent event) {
                    if (onUnfitListTouchListener != null) {
                        onUnfitListTouchListener.onItemClick(item, event);
                    }
                }
            });
        }

        return convertView;
    }

    public void setOnUnfitListTouchListener(OnUnfitListTouchListener onUnfitListTouchListener) {
        this.onUnfitListTouchListener = onUnfitListTouchListener;
    }



    OnUnfitListTouchListener onUnfitListTouchListener;

    public interface OnUnfitListTouchListener {

        void onItemLongClick(ContactBean contactBean, MotionEvent event);

        void onItemClick(ContactBean contactBean, MotionEvent event);
    }
}
