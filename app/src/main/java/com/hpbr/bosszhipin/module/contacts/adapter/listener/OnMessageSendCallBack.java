package com.hpbr.bosszhipin.module.contacts.adapter.listener;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

/**
 * 发送消息回调接口
 *
 * Created by guofeng on 2018/4/18.
 */
public interface OnMessageSendCallBack {

    /**
     * 消息开始发送
     *
     * @param contactBean
     */
    void onMsgStartSendListener(ChatBean contactBean);

    /**
     * 消息成功发送
     *
     * @param clientTempMessageId
     * @param isSuccess
     */
    void onMsgSendSuccessListener(long clientTempMessageId, boolean isSuccess);

}
