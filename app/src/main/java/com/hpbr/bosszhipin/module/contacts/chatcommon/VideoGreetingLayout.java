package com.hpbr.bosszhipin.module.contacts.chatcommon;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.FastReplayVideoDeleteRequest;
import net.bosszhipin.api.FastReplayVideoLSendRequest;
import net.bosszhipin.api.FastReplayVideoListRequest;
import net.bosszhipin.api.FastReplayVideoListRespone;
import net.bosszhipin.api.SuccessBooleanResponse;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.FastReplayVideoBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_VIDEOHI_RECORDENTER;
import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_VIDEOHI_RECORD_EXPO;

/**
 * Created by guofeng
 * on 2021-01-06.
 */

public class VideoGreetingLayout extends LinearLayout {

    public static final int DIVIDER_COUNT = 4;
    //最多显示3个ITEM
    public static final int MAX_ITEM_COUNT = 3;

    public static final String SP_FIRST_CLICL_RECORD = "SP_FIRST_CLICK_RECORD";

    private LinearLayout mGreetingContainer;

    private MTextView mGreetingText;


    public VideoGreetingLayout(Context context) {
        super(context);
    }

    private String securityId;

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    private void initView(Context context) {
        View view = LayoutInflater.from(context).inflate(R.layout.view_video_greeting_layout, null);
        mGreetingContainer = view.findViewById(R.id.mGreetingContainer);
        mGreetingText = view.findViewById(R.id.mGreetingText);
        addView(view, new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }

    private int padding = Scale.dip2px(App.getAppContext(), 20);

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();

        setGravity(Gravity.CENTER_HORIZONTAL);

        removeAllViews();

        initView(getContext());

        setPadding(0, padding, 0, 0);

        loadList();
    }


    //设置数据
    private void setDataValue() {

        mGreetingContainer.removeAllViews();


        mGreetingContainer.setGravity(Gravity.CENTER_HORIZONTAL);

        mGreetingText.setVisibility(View.GONE);

        if (UserManager.isBossRole()) {
            mGreetingText.setText("视频招呼能让对方更快速直观的了解你，提高你的招聘效率。");
        }
        if (UserManager.isGeekRole()) {
            mGreetingText.setText("视频招呼能让对方更快速直观的了解你，给你带来面试机会。");
        }


        App.get().getMainHandler().post(() -> {

            final double hWRatio = 1.25;

            int marginBottom = Scale.dip2px(App.get(), 50);

            int measuredWidth = mGreetingContainer.getMeasuredWidth();

            int dividerSize = Scale.dip2px(App.get(), 10);

            int remainWidth = measuredWidth - dividerSize * DIVIDER_COUNT;

            int remainHeight = mGreetingContainer.getMeasuredHeight() - marginBottom;

            int itemWidth = remainWidth / MAX_ITEM_COUNT;

            int itemHeight = (int) (itemWidth * hWRatio);

            if (itemHeight > remainHeight) {
                itemWidth = (int) (itemHeight / hWRatio);
                dividerSize = (measuredWidth - (itemWidth * MAX_ITEM_COUNT)) / 4;
            }

            int count = LList.getCount(itemBeanList);

            if (count == 0) {
                View emptyView = LayoutInflater.from(getContext()).inflate(R.layout.item_video_greating_empty, null);
                mGreetingContainer.addView(emptyView, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
                emptyView.findViewById(R.id.mAddVideoLayout).setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        boolean isFirst = SpManager.get().user().getBoolean(SP_FIRST_CLICL_RECORD, true);
                        if (isFirst) {
                            GeekPageRouter.openGreetingTemplate(getContext(), GeekPageRouter.GreetingVideoFrom.fromContactChat);
                            SpManager.get().user().edit().putBoolean(SP_FIRST_CLICL_RECORD, false).apply();
                        } else {
                            jumpToRecordPage();
                        }

                    }
                });
                mGreetingText.setVisibility(View.VISIBLE);
                return;
            }

            if (count == 1) {
                final LayoutParams addVideoParams = new LayoutParams(itemWidth, itemHeight);
                addVideoParams.rightMargin = itemWidth / 2;
                mGreetingContainer.addView(getAddRecordView(), addVideoParams);
                addAllItemView(dividerSize, itemHeight, itemWidth);
                return;
            }

            if (count == 2) {
                final LayoutParams addVideoParams = new LayoutParams(itemWidth, itemHeight);
                addVideoParams.rightMargin = dividerSize;
                mGreetingContainer.addView(getAddRecordView(), addVideoParams);
                addAllItemView(dividerSize, itemHeight, itemWidth);
                return;
            }

            if (count == MAX_ITEM_COUNT) {
                addAllItemView(dividerSize, itemHeight, itemWidth);
            }
        });
    }

    //创建添加录制视频按钮
    private View getAddRecordView() {
        View addVideoView = LayoutInflater.from(getContext()).inflate(R.layout.item_view_add_video, null);
        addVideoView.findViewById(R.id.addVideoLayout).setOnClickListener(v -> jumpToRecordPage());
        AnalyticsFactory.create().action(ACTION_VIDEOHI_RECORD_EXPO).build();
        return addVideoView;
    }

    private void jumpToRecordPage() {
        AnalyticsFactory.create().action(ACTION_VIDEOHI_RECORDENTER).build();
        //跳转到录制界面,返回后重新load数据渲染界面
        Activity currentActivity = ForegroundUtils.get().getTopActivity();
        if (currentActivity == null) return;
        //跳转到录制页面
        GeekPageRouter.openGreetingVideoRecordPage(currentActivity);
    }

    //渲染ITEM数据
    private void addAllItemView(int dividerSize, int itemHeight, int itemWidth) {

        int count = LList.getCount(itemBeanList);

        for (int i = 0; i < itemBeanList.size(); i++) {
            VideoItemBean videoGreatingBean = LList.getElement(itemBeanList, i);
            if (videoGreatingBean == null) continue;

            View itemView = LayoutInflater.from(getContext()).inflate(R.layout.item_view_video_greeting_layout, null);

            final LayoutParams params = new LayoutParams(itemWidth, itemHeight);

            if (i != count - 1) {
                params.rightMargin = dividerSize;
            }

            SimpleDraweeView mCoverView = itemView.findViewById(R.id.mCoverView);
            ImageView mCheckView = itemView.findViewById(R.id.mCheckView);
            ImageView mPlayingView = itemView.findViewById(R.id.mPlayingView);
            mGreetingContainer.addView(itemView, params);

            if (videoGreatingBean.isSample || videoGreatingBean.canPlayVideo()) {
                mPlayingView.setVisibility(View.VISIBLE);
            } else {
                mPlayingView.setVisibility(View.GONE);
            }

            mPlayingView.setOnClickListener(v -> {
                //播放视频
                new ZPManager(getContext(), videoGreatingBean.linkUrl).handler();
            });

            mCoverView.setImageURI(StringUtil.getNetworkUri(videoGreatingBean.coverUrl));

            setCheckViewStatus(mCheckView, videoGreatingBean);

            mCoverView.setOnClickListener(v -> {

                if (!videoGreatingBean.canPlayVideo()) {
                    ToastUtils.showText("仅支持发送审核通过的视频");
                    return;
                }

                //显示 是否 确认发送视频弹窗
                showConfirmSendVideoDialog(videoGreatingBean.encryptId, getSecurityId());

            });

            if (!videoGreatingBean.isSample) {
                mCoverView.setOnLongClickListener(v -> {
                    showDeleteMenu(itemView, videoGreatingBean.encryptId);
                    return false;
                });
            }

        }
    }

    private String getSecurityId() {
        return securityId;
    }

    //显示 是否 确认发送视频弹窗
    private void showConfirmSendVideoDialog(String encryptId, String securityId) {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        new DialogUtils.Builder(topActivity).setTitle("确认向对方发送视频？")
                .setDesc("对方可以在聊天中随时查看")
                .setPositiveAction("发送", v -> {
                    onSendVideoListener(encryptId, securityId);
                })
                .setNegativeAction("取消")
                .setDoubleButton()
                .build()
                .show();
    }


    //发送视频
    private void onSendVideoListener(String encryptId, String securityId) {
        FastReplayVideoLSendRequest request = new FastReplayVideoLSendRequest(new ApiRequestCallback<SuccessBooleanResponse>() {
            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {

            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.encryptId = encryptId;
        request.securityId = securityId;
        HttpExecutor.execute(request);
    }

    //显示删除菜单
    private void showDeleteMenu(View itemView, String encryptId) {
        ImageView imageView = new ImageView(getContext());
        ViewGroup.LayoutParams layoutParams = imageView.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        layoutParams.width = Scale.dip2px(App.getAppContext(), 50);
        layoutParams.height = Scale.dip2px(App.getAppContext(), 42);
        imageView.setLayoutParams(layoutParams);
        imageView.setImageResource(R.mipmap.ic_delete_menu);


        PopupWindow popupWindow = new PopupWindow(imageView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        imageView.setOnClickListener(v -> {
            if (popupWindow.isShowing()) {
                popupWindow.dismiss();
            }
            showDeleteDialog(encryptId);
        });

        int showRawX = (int) (itemView.getX() + (itemView.getWidth() / 2) - (layoutParams.width / 2));
        int showRawY = App.get().getDisplayHeight() - getMeasuredHeight() - (layoutParams.height / 3 * 2);
        popupWindow.setBackgroundDrawable(new ColorDrawable());
        popupWindow.showAtLocation(topActivity.getWindow().getDecorView(), Gravity.NO_GRAVITY, showRawX, showRawY);
    }


    //加载视频集合
    private void loadList() {
        FastReplayVideoListRequest listRequest = new FastReplayVideoListRequest(new ApiRequestCallback<FastReplayVideoListRespone>() {
            @Override
            public void onSuccess(ApiData<FastReplayVideoListRespone> data) {
                FastReplayVideoListRespone resp = data.resp;
                List<FastReplayVideoBean> videoList = resp.videoList;
                //server数据转化为ITEMBEAN
                transferToVideoItemBean(videoList);
                //设置数据刷新UI
                setDataValue();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        HttpExecutor.execute(listRequest);

    }


    private final List<VideoItemBean> itemBeanList = new ArrayList<>(MAX_ITEM_COUNT);

    //服务器下发的
    private void transferToVideoItemBean(List<FastReplayVideoBean> videoList) {
        itemBeanList.clear();


        if (videoList != null) {
            for (FastReplayVideoBean fastReplayVideoBean : videoList) {
                VideoItemBean itemBean = new VideoItemBean();
                itemBean.encryptId = fastReplayVideoBean.encryptId;
                itemBean.fileId = fastReplayVideoBean.fileId;
                itemBean.videoUrl = fastReplayVideoBean.videoUrl;
                itemBean.coverUrl = fastReplayVideoBean.coverUrl;
                itemBean.linkUrl = fastReplayVideoBean.linkUrl;
                itemBean.isSample = false;
                itemBean.status = fastReplayVideoBean.status;
                itemBeanList.add(itemBean);
                //最多可以显示3个ITEM_VIEW
                if (LList.getCount(itemBeanList) >= MAX_ITEM_COUNT) break;
            }
        }


    }

    //显示删除视频弹窗
    private void showDeleteDialog(String encryptId) {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        new DialogUtils.Builder(topActivity).setTitle("确认删除视频？")
                .setDesc("删除后，已发送的视频也将无法被查看")
                .setPositiveAction("确认删除", v -> onDeleteVideoListener(encryptId))
                .setNegativeAction("我再想想")
                .setDoubleButton()
                .build()
                .show();
    }

    //删除视频
    private void onDeleteVideoListener(String encryptId) {
        FastReplayVideoDeleteRequest deleteRequest = new FastReplayVideoDeleteRequest(new ApiRequestCallback<SuccessBooleanResponse>() {

            @Override
            public void onSuccess(ApiData<SuccessBooleanResponse> data) {
                //刷新视频简历列表
                loadList();
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        deleteRequest.encryptId = encryptId;
        HttpExecutor.execute(deleteRequest);
    }


    //刷新图标状态
    private void setCheckViewStatus(ImageView mCheckView, VideoItemBean videoGreatingBean) {
        if (videoGreatingBean != null) {
            if (videoGreatingBean.isSample) {
                mCheckView.setVisibility(View.VISIBLE);
                mCheckView.setImageResource(R.mipmap.ic_sample_video);
            } else {
                if (videoGreatingBean.canPlayVideo()) {//审核通过
                    mCheckView.setVisibility(View.GONE);
                } else if (videoGreatingBean.isCheckFailed()) {//审核失败
                    mCheckView.setVisibility(View.VISIBLE);
                    mCheckView.setImageResource(R.mipmap.ic_video_checked_failed);
                } else {
                    mCheckView.setVisibility(View.VISIBLE);
                    mCheckView.setImageResource(R.mipmap.ic_video_checking);
                }
            }
        }
    }


    public static class VideoItemBean extends BaseServerBean {

        private static final long serialVersionUID = 1L;
        public String encryptId;
        public String fileId;
        public String videoUrl;
        public String coverUrl;
        public String linkUrl;
        public String videoAddTime;
        public int duration;
        public boolean isSample;
        public int status;////'视频状态，0-待审核，1-审核通过，2-审核不通过'

        //只有审核通过的视频才可以播放
        public boolean canPlayVideo() {
            return status == 1;
        }

        //是否审核失败
        public boolean isCheckFailed() {
            return status == 2;
        }

        //是否审核中
        public boolean isChecking() {
            return status == 0;
        }
    }


}
