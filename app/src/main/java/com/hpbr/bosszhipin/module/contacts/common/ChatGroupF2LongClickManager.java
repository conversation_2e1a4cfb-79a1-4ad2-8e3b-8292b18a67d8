package com.hpbr.bosszhipin.module.contacts.common;

import android.content.Context;
import android.view.View;

import com.hpbr.bosszhipin.common.dialog.MultiItemDialog;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.manager.ContactOperationManager;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.GroupUpdateMemberSettingRequest;
import net.bosszhipin.api.HideGroupRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_FRIEND_DELETE;
import static com.hpbr.bosszhipin.module.contacts.manager.ContactOperationManager.FOLLOW;

/**
 * Created by guofeng on 2018/5/2.
 */

public final class ChatGroupF2LongClickManager {

    private ContactOperationManager.RefreshDataCallback refreshDataCallback;


    private Context context;
    private ProgressDialog dialog;

    public ChatGroupF2LongClickManager(Context context, ContactOperationManager.RefreshDataCallback refreshDataCallback) {
        this.context = context;
        this.refreshDataCallback = refreshDataCallback;
        this.dialog = new ProgressDialog(context);
    }

    public void showDialog2(ContactBean bean) {
        if (bean == null) return;
        String[] items = new String[]{bean.isTop ? "取消置顶联系人" : "置顶联系人", "删除好友"};
        show(bean, items);
    }

    public void showDialog(final ContactBean bean) {
        if (bean == null) return;
        String[] items = new String[]{bean.isTop ? "取消置顶" : "置顶群聊", "删除群聊"};
        show(bean, items);
    }

    private void show(ContactBean bean, String[] items) {
        MultiItemDialog d = new MultiItemDialog(context);
        d.show(items, new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                Integer index = (Integer) v.getTag();

                switch (index) {
                    case 0://【关注群聊】或【取消关注】
                        GroupUpdateMemberSettingRequest request = new GroupUpdateMemberSettingRequest(new ApiRequestCallback<EmptyResponse>() {


                            @Override
                            public void onStart() {
                                super.onStart();
                                dialog.show("提交中");
                            }

                            @Override
                            public void onSuccess(ApiData<EmptyResponse> data) {
                                bean.isTop = !bean.isTop;
                                GroupManager.getInstance().updateGroupWatch(bean.id, bean.isTop ? GroupInfoBean.SWITCH_ON : GroupInfoBean.SWITCH_OFF); // 反转选中状态
                                if (refreshDataCallback != null) {
                                    refreshDataCallback.onRefreshData(FOLLOW);
                                }
                            }

                            @Override
                            public void onComplete() {
                                dialog.dismiss();
                            }

                            @Override
                            public void onFailed(ErrorReason reason) {
                                T.ss(reason.getErrReason());
                            }
                        });
                        request.gid = GroupManager.getInstance().getGid(bean.id);
                        request.watch = bean.isTop ? String.valueOf(0) : String.valueOf(1);
                        HttpExecutor.execute(request);
                        break;
                    case 1://删除群聊

                        AnalyticsFactory.create().action(ACTION_FRIEND_DELETE)
                                .param("p2", "1")
                                .param("p3", "0").
                                param("p", "" + bean.friendId).buildSync();
                        HideGroupRequest request1 = new HideGroupRequest(new ApiRequestCallback<SuccessResponse>() {
                            @Override
                            public void onStart() {
                                super.onStart();
                                dialog.show("删除中");
                            }

                            @Override
                            public void onSuccess(ApiData<SuccessResponse> data) {
                                T.ss("删除成功");
                                if (refreshDataCallback != null) {
                                    refreshDataCallback.onDeleteGroupListener(bean.friendId);
                                }
                            }

                            @Override
                            public void onComplete() {
                                dialog.dismiss();
                            }

                            @Override
                            public void onFailed(ErrorReason reason) {
                                T.ss(reason.getErrReason());
                            }
                        });
                        request1.gid = GroupManager.getInstance().getGid(bean.friendId);
                        request1.groupId = bean.friendId;
                        HttpExecutor.execute(request1);

                        break;
                    default:
                        break;
                }

            }
        });
    }

}
