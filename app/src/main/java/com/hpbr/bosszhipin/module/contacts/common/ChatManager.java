package com.hpbr.bosszhipin.module.contacts.common;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.util.SendMessageUtil;
import com.hpbr.bosszhipin.module.contacts.adapter.listener.OnMessageSendCallBack;

/**
 * 聊天工具类
 *
 * <AUTHOR>
 * @version 713
 */
public class ChatManager {

    private SendMessageUtil sendMessageUtil;

    private long friendId;
    private int friendSource;
    private OnMessageSendCallBack mOnMessageSendCallBack;

    public void setFriendId(long friendId) {
        this.friendId = friendId;
    }

    public void setSource(int source) {
        this.friendSource = source;
    }

    public void setOnMessageSendCallBack(OnMessageSendCallBack onMessageSendCallBack) {
        this.mOnMessageSendCallBack = onMessageSendCallBack;
    }

    /**
     * 发送一条文字消息
     *
     * @param text 发送内容
     */
    public void sendTextMessage(final String text) {
        if (sendMessageUtil == null) {
            sendMessageUtil = new SendMessageUtil();
        }
        ContactBean contactBean = getContactBean();
        if (contactBean != null) {
            sendMessageUtil.sentChatTextMessageWithBizCode(contactBean, text, friendSource, 0, 0, mOnMessageSendCallBack);
        }
    }

    private ContactBean getContactBean() {
        return ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), friendSource);
    }


}
