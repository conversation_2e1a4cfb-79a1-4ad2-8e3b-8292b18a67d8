package com.hpbr.bosszhipin.module.contacts.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

/**
 * Created by monch on 15/5/22.
 */
@Table("ChatAdvert")
public class AdvertBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    /**
     * 弹出类型，0为默认，单按钮广告，1为意见反馈/推荐好友广告
     */
    public int type;

    /**
     * 广告图片的URL
     */
    public String photoUrl;

    /**
     * 广告图片是否已下载完成
     */
    public boolean photoDownloadComplete;

    /**
     * 广告的起始时间
     */
    public long startTime;

    /**
     * 广告的结束时间
     */
    public long endTime;

    /**
     * 广告的去向地址
     */
    public String whereUrl;

    /**
     * 标题
     */
    @Deprecated
    public String titleText;

    /**
     * 描述
     */
    public String descText;

    /**
     * 右侧按钮文字
     */
    public String buttonText;

    /**
     * 左侧按钮文字
     */
    public String buttonCancelText;

    /**
     * 取消按钮的去向地址
     */
    public String whereCancelUrl;

    /**
     * 扩展数据，用于埋点数据JSON
     */
    public String extension;

    /**
     * 是否已经显示过
     */
    public boolean isShowed;

}
