package com.hpbr.bosszhipin.module.contacts.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

/**
 * Created by monch on 16/6/13.
 */
@Table("AllDialog")
public class AllDialogBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    public String title;
    public String icon;
    public String text;
    public String leftButtonText;
    public String leftButtonTarget;
    public String rightButtonText;
    public String rightButtonTarget;
    public String extend;

    @Override
    public String toString() {
        return "AllDialogBean{" +
                "title='" + title + '\'' +
                ", icon='" + icon + '\'' +
                ", text='" + text + '\'' +
                ", leftButtonText='" + leftButtonText + '\'' +
                ", leftButtonTarget='" + leftButtonTarget + '\'' +
                ", rightButtonText='" + rightButtonText + '\'' +
                ", rightButtonTarget='" + rightButtonTarget + '\'' +
                ", extend='" + extend + '\'' +
                '}';
    }
}
