package com.hpbr.bosszhipin.module.contacts.entity;

import com.hpbr.bosszhipin.base.BaseEntityAuto;

/**
 * Created by monch on 2017/6/13.
 */

public class ChatDialogTaskBean extends BaseEntityAuto {

    private static final long serialVersionUID = -1;

    public long uid;
    public int role;

    // 9面试弹窗 10面试被投诉并无定位权限弹窗
    public int type;
    public String title;
    public String desc;
    public String leftButton;
    public String leftProtocol;
    public String rightButton;
    public String rightProtocol;
    public long endTime;
    public String pointStatics;

    @Override
    public String toString() {
        return "ChatDialogTaskBean{" +
                "uid=" + uid +
                ", role=" + role +
                ", type=" + type +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                ", leftButton='" + leftButton + '\'' +
                ", leftProtocol='" + leftProtocol + '\'' +
                ", rightButton='" + rightButton + '\'' +
                ", rightProtocol='" + rightProtocol + '\'' +
                ", endTime=" + endTime +
                '}';
    }

}
