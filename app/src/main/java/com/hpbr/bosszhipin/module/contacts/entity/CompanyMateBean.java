package com.hpbr.bosszhipin.module.contacts.entity;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.SpannableString;

import com.google.gson.annotations.SerializedName;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.monch.lbase.orm.db.annotation.Table;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by liuming on 2015/8/18.
 * 公司同事实体类
 */
@Table("CompanyMateBean")
public class CompanyMateBean extends BaseEntity implements Parcelable {

    private static final long serialVersionUID = -1;

    /**
     * 用户Id
     */
    @SerializedName("userId")
    public long userId;
    /**
     * 小头像
     */
    @SerializedName("tiny")
    public String tiny;
    /**
     * 默认头像
     */
    public int headImg;
    /**
     * 大头像
     */
    public String large;
    /**
     * 姓名
     */
    @SerializedName("name")
    public String name;
    /**
     * 同事名称转化拼音后集合(多音字情况)
     */
    public transient List<String> namePinYin = new ArrayList<>();
    /**
     * 同事职称
     */
    @SerializedName("title")
    public String position;
    /**
     * 同事邮箱
     */
    @SerializedName("mail")
    public String mail;
    /**
     * 名称高亮
     */
    public transient HighLight nameHighLight;
    /**
     * 职位高亮
     */
    public transient HighLight positionHighLight;
    /**
     * 邮箱高亮
     */
    public transient HighLight mailHighLight;
    /**
     * 是否是亲密好友
     */
    @SerializedName("relateType")
    public boolean isAssociateFriend;

    //非相关同事搜索提示文案
    public transient SpannableString unAssociateMate;

    //719 add 同事加密id
    public String encryptUserId;

    public String securityId;

    public boolean star;

    @SerializedName("brandName")
    //品牌
    public String brandName;

    protected CompanyMateBean(Parcel in) {
        userId = in.readLong();
        tiny = in.readString();
        headImg = in.readInt();
        large = in.readString();
        name = in.readString();
        namePinYin = in.createStringArrayList();
        position = in.readString();
        brandName = in.readString();
        mail = in.readString();
        isAssociateFriend = in.readByte() != 0;
        star = in.readByte() != 0;
        encryptUserId = in.readString();
    }


    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(userId);
        dest.writeString(tiny);
        dest.writeInt(headImg);
        dest.writeString(large);
        dest.writeString(name);
        dest.writeStringList(namePinYin);
        dest.writeString(position);
        dest.writeString(mail);
        dest.writeString(brandName);
        dest.writeByte((byte) (isAssociateFriend ? 1 : 0));
        dest.writeByte(star ? (byte) 1 : (byte) 0);
        dest.writeString(encryptUserId);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<CompanyMateBean> CREATOR = new Creator<CompanyMateBean>() {
        @Override
        public CompanyMateBean createFromParcel(Parcel in) {
            return new CompanyMateBean(in);
        }

        @Override
        public CompanyMateBean[] newArray(int size) {
            return new CompanyMateBean[size];
        }
    };

    public CompanyMateBean() {
    }

    public void parser(JSONObject jsonObject) {
        userId = jsonObject.optLong("userId");
        tiny = jsonObject.optString("tiny");
        headImg = jsonObject.optInt("headImg");
        large = jsonObject.optString("large");
        name = jsonObject.optString("name");
        position = jsonObject.optString("title");
        mail = jsonObject.optString("mail");
        brandName = jsonObject.optString("brandName");
        isAssociateFriend = jsonObject.optBoolean("relateType");
    }


}