package com.hpbr.bosszhipin.module.contacts.entity;


import com.hpbr.bosszhipin.base.BaseEntity;

import java.io.Serializable;
import java.util.List;

public class CustomerAskReplyBean extends BaseEntity {

    private static final long serialVersionUID = -1;

    public String text;
    public String subTitle;
    public String taskId;
    public int status; //1:已过期
    public int optionType;  //选项类型 1:单选 2:多选
    public List<Option> options;
    public List<ButtonBean> buttons;
    public int optionActionType;

    public static class Option implements Serializable {
        private static final long serialVersionUID = -7977430140982212959L;

        public long optionId;
        public String title;
        public String url;
        public int status; //0:未选中 1:选中
    }
}
