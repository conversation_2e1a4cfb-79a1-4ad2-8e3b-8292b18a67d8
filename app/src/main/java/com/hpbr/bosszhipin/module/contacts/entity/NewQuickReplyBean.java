package com.hpbr.bosszhipin.module.contacts.entity;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.monch.lbase.orm.db.annotation.Table;

import net.bosszhipin.api.bean.ServerUserQuickReplyBean;

import org.json.JSONObject;

/**
 * Created by wa<PERSON><PERSON> on 16/9/26.
 */
@Table("QuckReply")
public class NewQuickReplyBean extends BaseEntityAuto {
    private static final long serialVersionUID = -1;
    public long fastReplyId;
    public String title;
    public String content;
    public boolean isExpand;


    public void parseFromServer(ServerUserQuickReplyBean quickReplyBean) {
        if (quickReplyBean == null) return;
        fastReplyId = quickReplyBean.fastReplyId;
        title = quickReplyBean.title;
        content = quickReplyBean.content;
    }
}
