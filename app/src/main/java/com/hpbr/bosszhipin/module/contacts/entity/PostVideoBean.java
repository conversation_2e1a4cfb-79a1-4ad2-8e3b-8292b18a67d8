package com.hpbr.bosszhipin.module.contacts.entity;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.monch.lbase.orm.db.annotation.Table;

@Table("PostVideoTask")
public class PostVideoBean extends BaseEntityAuto {

    private static final long serialVersionUID = 7403760646101492839L;

    @IntDef({PostVideoType.TYPE_KNOWLEDGE_POINT, PostVideoType.TYPE_ANSWER, PostVideoType.TYPE_DYNAMIC_GEEK, PostVideoType.TYPE_DYNAMIC_BOSS, PostVideoType.TYPE_DYNAMIC_CIRCLE, PostVideoType.TYPE_HOBBY})
    public @interface PostVideoType {
        int TYPE_KNOWLEDGE_POINT = 1;
        int TYPE_ANSWER = 2;
        int TYPE_DYNAMIC_GEEK = 3;
        int TYPE_DYNAMIC_BOSS = 4;
        int TYPE_DYNAMIC_CIRCLE = 5;
        int TYPE_HOBBY = 6;
    }

    @PostVideoType
    public int postVideoType; // 知识点、回答、动态（牛人、BOSS、圈子）

    public String post; // json字符串

    public String localVideoPath; // 本地视频地址（待上传）

    public String videoCoverPath; // 本地视频封面地址（待上传）

    public long time; // 发布时间戳

    public int firstFailed;//0是

    /////////////////////////////////////////////

    public PostVideoBean setPostVideoType(@PostVideoType int postVideoType) {
        this.postVideoType = postVideoType;
        return this;
    }

    public PostVideoBean setTaskId(long taskId) {
        this.id = taskId;
        return this;
    }

    public PostVideoBean setPost(@NonNull String post) {
        this.post = post;
        return this;
    }

    public PostVideoBean setLocalVideoPath(@NonNull String localVideoPath) {
        this.localVideoPath = localVideoPath;
        return this;
    }

    public PostVideoBean setVideoCoverPath(@NonNull String videoCoverPath) {
        this.videoCoverPath = videoCoverPath;
        return this;
    }

    /////////////////////////////////////////////

    @NonNull
    @Override
    public String toString() {
        return "PostVideoBean{" +
                "postVideoType=" + postVideoType +
                ", post='" + post + '\'' +
                ", localVideoPath='" + localVideoPath + '\'' +
                ", videoCoverPath='" + videoCoverPath + '\'' +
                ", id=" + id +
                '}';
    }

    private PostVideoBean() {
        time = System.currentTimeMillis();
    }

    public static PostVideoBean obj() {
        return new PostVideoBean();
    }



    public static  PostVideoBean wrap(com.bszp.kernel.logic.db.entitiy.PostVideoBean videoBean) {
        if (videoBean == null) return null;
        PostVideoBean postVideoBean = PostVideoBean.obj();
        postVideoBean.id = videoBean.id;
        postVideoBean.post = videoBean.post;
        postVideoBean.postVideoType = videoBean.postVideoType;
        postVideoBean.time = videoBean.time;
        postVideoBean.firstFailed = videoBean.firstFailed;
        postVideoBean.localVideoPath = videoBean.localVideoPath;
        postVideoBean.videoCoverPath = videoBean.videoCoverPath;
        return postVideoBean;
    }

    public static  com.bszp.kernel.logic.db.entitiy.PostVideoBean nowrap(PostVideoBean videoBean) {
        if (videoBean == null) return null;
        com.bszp.kernel.logic.db.entitiy.PostVideoBean postVideoBean = new com.bszp.kernel.logic.db.entitiy.PostVideoBean();
        postVideoBean.id = videoBean.id;
        postVideoBean.post = videoBean.post;
        postVideoBean.postVideoType = videoBean.postVideoType;
        postVideoBean.time = videoBean.time;
        postVideoBean.firstFailed = videoBean.firstFailed;
        postVideoBean.localVideoPath = videoBean.localVideoPath;
        postVideoBean.videoCoverPath = videoBean.videoCoverPath;
        return postVideoBean;
    }
}
