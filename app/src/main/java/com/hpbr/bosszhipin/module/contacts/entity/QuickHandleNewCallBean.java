package com.hpbr.bosszhipin.module.contacts.entity;

import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;

import net.bosszhipin.api.bean.ServerGeekCardBean;
import net.bosszhipin.api.bean.ServerQuickHandleResumeBean;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 * 新招呼卡片
 * https://api.weizhipin.com/project/30/interface/api/8590
 * https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=152814181
 * @since 710
 */
public class QuickHandleNewCallBean extends BaseEntity {

    private static final long serialVersionUID = 1489775076400270221L;

    public ServerQuickHandleResumeBean resumeBean;
    /**
     * GEEK 信息
     */
    public ServerGeekCardBean geekCardBean;
    /**
     * 当前联系人
     */
    public ContactBean contactBean;
    /**
     * 最近聊天记录
     */
    public List<ChatBean> chatBeans;



}
