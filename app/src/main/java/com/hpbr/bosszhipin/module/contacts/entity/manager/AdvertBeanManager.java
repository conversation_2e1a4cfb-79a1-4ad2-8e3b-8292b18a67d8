package com.hpbr.bosszhipin.module.contacts.entity.manager;

import android.text.TextUtils;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.contacts.entity.AdvertBean;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.DataBase;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;

/**
 * Created by monch on 15/5/29.
 */
public class AdvertBeanManager {
    public static final String KEY_ = "AdvertBean";

    private AdvertBeanManager() {
    }

    private static final AdvertBeanManager instance = new AdvertBeanManager();

    public static AdvertBeanManager getInstance() {
        return instance;
    }

    /**
     * 保存独占广告数据
     *
     * @param bean
     * @return
     */
    public long save(AdvertBean bean) {
        if (bean == null) return -1;
        SpManager.get().user().edit().putString(KEY_, GsonUtils.toJson(bean)).apply();
        return 1;
//        DataBase db = App.get().db();
//        if (db.queryCount(AdvertBean.class) > 0) {
//            db.deleteAll(AdvertBean.class);
//        }
//        return db.save(bean);
    }

    /**
     * 获取最新的一条独占广告数据
     *
     * @return
     */
    public AdvertBean getAdvertBean() {
        String json_str = SpManager.get().user().getString(KEY_, "");
        if (TextUtils.isEmpty(json_str)) return null;
        AdvertBean bean = GsonUtils.fromJson(json_str, AdvertBean.class);
        long currentTime = System.currentTimeMillis();
        if (bean != null) {
            if (bean.startTime < currentTime && bean.endTime > currentTime) {
                return bean;
            }
        }
        return null;
//        QueryBuilder qb = new QueryBuilder(AdvertBean.class);
//        long currentTime = System.currentTimeMillis();
//        qb.where("isShowed='false' AND startTime<" + currentTime + " AND endTime>" + currentTime, null);
//        qb.appendOrderDescBy("id");
//        List<AdvertBean> list = App.get().db().query(qb);
//        if (list != null && list.size() > 0) {
//            bean = list.get(0);
//        }
//        return bean;
    }

    public int migrate() {
        DataBase db = App.get().db();
        if (db != null) {
            ArrayList<AdvertBean> advertBeans = db.queryAll(AdvertBean.class);
            if (!LList.isEmpty(advertBeans)) {
                for (AdvertBean advertBean : advertBeans) {
                    save(advertBean);
                }
                db.deleteAll(AdvertBean.class);
                return LList.getCount(advertBeans);
            }
        }
        return 0;
    }
}
