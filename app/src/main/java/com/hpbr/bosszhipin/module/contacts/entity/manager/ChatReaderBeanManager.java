package com.hpbr.bosszhipin.module.contacts.entity.manager;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatReaderBean;
import com.monch.lbase.orm.db.assit.QueryBuilder;

import java.util.List;

import message.handler.dao.MessageDaoFactory;

/**
 * Created by monch on 15/6/1.
 */
public class ChatReaderBeanManager {

    private ChatReaderBeanManager() {
    }

    private static ChatReaderBeanManager instance = new ChatReaderBeanManager();

    public static ChatReaderBeanManager getInstance() {
        return instance;
    }

    /**
     * 创建一条已读数据
     *
     * @param friendUserId
     * @return
     */
    public ChatReaderBean create(long friendUserId) {
        final long myUserId = UserManager.getUID();
        final int myRole = UserManager.getUserRole().get();
        String where = "myUserId=" + myUserId + " AND myRole=" + myRole + " AND friendUserId=" + friendUserId;
        App.get().db().delete(ChatReaderBean.class, where);
        long lastMsgId = MessageDaoFactory.getMessageDao()
                .getLastFriendSendChatMsgIdForFriendId(myUserId, myRole, friendUserId);
        if (lastMsgId < 1) return null;
        ChatReaderBean bean = new ChatReaderBean();
        bean.myUserId = UserManager.getUID();
        bean.myRole = UserManager.getUserRole().get();
        bean.friendUserId = friendUserId;
        bean.messageId = lastMsgId;
        bean.readerTime = System.currentTimeMillis();
        bean.sendSuccess = false;
        App.get().db().save(bean);
        return bean;
    }



    public ChatReaderBean createGroup(long groupId) {
        final long myUserId = UserManager.getUID();
        ChatReaderBean bean = new ChatReaderBean();
        bean.myUserId = myUserId;
        bean.myRole = UserManager.getUserRole().get();
        bean.friendUserId = groupId;
        bean.messageId = MessageDaoFactory.getMessageDao().getTargetGroupMaxMessageId(groupId, myUserId);
        bean.readerTime = System.currentTimeMillis();
        bean.sendSuccess = false;
        return bean;
    }


    /**
     * 获取所有已读消息列表
     *
     * @return
     */
    public List<ChatReaderBean> getReaderList() {
        QueryBuilder qb = new QueryBuilder(ChatReaderBean.class);
        qb.where("sendSuccess=? AND myUserId=? AND myRole=?", new Object[]{false, UserManager.getUID(), UserManager.getUserRole().get()});
        return App.get().db().query(qb);
    }

    public List<ChatReaderBean> getReaderList(int friendSource) {
        QueryBuilder qb = new QueryBuilder(ChatReaderBean.class);
        qb.where("sendSuccess=? AND myUserId=? AND myRole=? and friendSource=?", new Object[]{false, UserManager.getUID(), UserManager.getUserRole().get(), friendSource});
        return App.get().db().query(qb);
    }

    /**
     * 根据好友ID和消息ID，删除此条消息
     *
     * @param friendUserId
     * @param messageId
     * @return
     */
    public int removeReader(long friendUserId, long messageId) {
        String where = "myUserId=" + UserManager.getUID() + " AND myRole=" + UserManager.getUserRole().get() + " AND friendUserId=" + friendUserId + " AND messageId=" + messageId;
        return App.get().db().delete(ChatReaderBean.class, where);
    }

}
