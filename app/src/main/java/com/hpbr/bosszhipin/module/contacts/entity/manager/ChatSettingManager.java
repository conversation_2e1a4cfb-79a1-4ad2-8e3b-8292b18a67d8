package com.hpbr.bosszhipin.module.contacts.entity.manager;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatSettingBean;
import com.monch.lbase.orm.db.assit.QueryBuilder;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.ChatSettingBatchRequest;
import net.bosszhipin.api.ChatSettingBatchResponse;
import net.bosszhipin.api.ChatSettingGeekCardRequest;
import net.bosszhipin.api.ChatSettingGeekCardResponse;
import net.bosszhipin.api.ChatSettingJobCardRequest;
import net.bosszhipin.api.ChatSettingJobCardResponse;
import net.bosszhipin.api.GetAllLabelsAndNoteRequest;
import net.bosszhipin.api.GetAllLabelsAndNoteResponse;
import net.bosszhipin.api.GetSettingCompetiveInfoRequest;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;

/**
 * Created by wangtian on 16/9/26.
 */
public class ChatSettingManager {
    private static final String KEY_REJECT_HINT_SHOW = "KEY_REJECT_HINT_SHOW";
    private volatile static ChatSettingManager mInstance;

    private ChatSettingManager() {
    }


    public static ChatSettingManager getInstance() {
        if (mInstance == null) {
            synchronized (ChatSettingManager.class) {
                if (mInstance == null) {
                    mInstance = new ChatSettingManager();
                }
            }
        }
        return mInstance;
    }

    public ChatSettingBean query(ContactBean contactbean) {
        ArrayList<ChatSettingBean> list = App.get().db().query(new QueryBuilder(ChatSettingBean.class).where("key = ?", new Object[]{contactbean.myRole + "_" + contactbean.myId}));
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    public void loadBatchData(ContactBean contactBean, String from, final IChatSettingCardListener<ChatSettingBatchResponse> callback) {

        ChatSettingBatchRequest batchRequest = new ChatSettingBatchRequest(new ApiRequestCallback<ChatSettingBatchResponse>() {


            @Override
            public void onStart() {
                super.onStart();
                if (callback != null) callback.onStart();
            }

            @Override
            public void onSuccess(ApiData<ChatSettingBatchResponse> data) {
                super.onStart();
                ChatSettingBatchResponse resp = data.resp;
                if (resp != null) {
                    callback.onSuccess(resp);
                }
            }

            @Override
            public void handleInChildThread(ApiData<ChatSettingBatchResponse> data) {
                super.handleInChildThread(data);
                ChatSettingBatchResponse resp = data.resp;
                if (resp != null) {
                    callback.handleInChildThread(resp);
                }
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }


            @Override
            public void onFailed(ErrorReason reason) {
                callback.onFailed(reason.getErrCode(), reason.getErrReason());
            }
        });

        ChatSettingJobCardRequest chatSettingJobCardRequest = new ChatSettingJobCardRequest();
        chatSettingJobCardRequest.bossId = contactBean.friendId;
        chatSettingJobCardRequest.jobId = contactBean.jobId;
        chatSettingJobCardRequest.from = from;
        chatSettingJobCardRequest.securityId = contactBean.securityId;

        GetSettingCompetiveInfoRequest settingCompetiveInfoRequest = new GetSettingCompetiveInfoRequest();
        settingCompetiveInfoRequest.securityId = contactBean.securityId;

        batchRequest.chatSettingJobCardRequest = chatSettingJobCardRequest;
        batchRequest.getSettingCompetiveInfoRequest = settingCompetiveInfoRequest;
        batchRequest.execute();


    }


    public void loadJobCardFromServer(ContactBean contactBean, String from, final IChatSettingCardListener<ChatSettingJobCardResponse> callback) {
        ChatSettingJobCardRequest request = new ChatSettingJobCardRequest(new ApiRequestCallback<ChatSettingJobCardResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                if (callback != null) callback.onStart();
            }

            @Override
            public void handleInChildThread(ApiData<ChatSettingJobCardResponse> data) {
                super.handleInChildThread(data);
                ChatSettingJobCardResponse resp = data.resp;
                if (resp != null) {
                    callback.handleInChildThread(resp);
                }
            }

            @Override
            public void onSuccess(ApiData<ChatSettingJobCardResponse> data) {
                ChatSettingJobCardResponse resp = data.resp;
                if (resp != null) {
                    callback.onSuccess(resp);
                }
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onFailed(reason.getErrCode(), reason.getErrReason());
            }
        });
        request.bossId = contactBean.friendId;
        request.jobId = contactBean.jobId;
        request.from = from;
        request.securityId = contactBean.securityId;
        HttpExecutor.execute(request);
    }

    public void loadGeekCardFromServer(ContactBean contactBean, String from, final IChatSettingCardListener<ChatSettingGeekCardResponse> callback) {
        ChatSettingGeekCardRequest request = new ChatSettingGeekCardRequest(new ApiRequestCallback<ChatSettingGeekCardResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                if (callback != null) callback.onStart();
            }

            @Override
            public void handleInChildThread(ApiData<ChatSettingGeekCardResponse> data) {
                super.handleInChildThread(data);
                ChatSettingGeekCardResponse resp = data.resp;
                if (resp != null) {
                    callback.handleInChildThread(resp);
                }
            }

            @Override
            public void onSuccess(ApiData<ChatSettingGeekCardResponse> data) {
                ChatSettingGeekCardResponse resp = data.resp;
                if (resp != null) {
                    callback.onSuccess(resp);
                }
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onFailed(reason.getErrCode(), reason.getErrReason());
            }
        });
        request.geekId = contactBean.friendId;
        request.expectId = contactBean.jobIntentId;
        request.from = from;
        request.securityId = contactBean.securityId;
        HttpExecutor.execute(request);
    }

    public void GetAllLabelsAndNote(ContactBean contactBean, final IChatSettingCardListener<GetAllLabelsAndNoteResponse> callback) {
        GetAllLabelsAndNoteRequest request = new GetAllLabelsAndNoteRequest(new ApiRequestCallback<GetAllLabelsAndNoteResponse>() {
            @Override
            public void onSuccess(ApiData<GetAllLabelsAndNoteResponse> data) {
                GetAllLabelsAndNoteResponse resp = data.resp;
                if (resp != null) {
                    callback.onSuccess(resp);
                }
            }

            @Override
            public void onComplete() {
                callback.onComplete();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                callback.onFailed(reason.getErrCode(), reason.getErrReason());
            }
        });
        request.friendSource = contactBean.friendSource;
        request.friendId = contactBean.friendId;
        HttpExecutor.execute(request);
    }

    public void delete(ContactBean contactbean) {
        App.get().db().delete(ChatSettingBean.class, "key = " + contactbean.myRole + "_" + contactbean.myId);
    }


}
