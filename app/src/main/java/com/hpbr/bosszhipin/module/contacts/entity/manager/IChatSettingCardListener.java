package com.hpbr.bosszhipin.module.contacts.entity.manager;

import net.bosszhipin.base.HttpResponse;

/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 2018/3/6.
 */
public interface IChatSettingCardListener<T extends HttpResponse> {

    default void onStart() {

    }

    void onSuccess(T t);

    void onFailed(int errCode, String errMessage);

    void onComplete();

    default void handleInChildThread(T resp) {

    }
}
