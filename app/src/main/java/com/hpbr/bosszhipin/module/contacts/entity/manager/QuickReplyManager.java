package com.hpbr.bosszhipin.module.contacts.entity.manager;

import android.text.TextUtils;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.bszp.kernel.account.AccountHelper;
import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.eventbus.EventBusMessage;
import com.hpbr.bosszhipin.eventbus.EventBusMessageType;
import com.hpbr.bosszhipin.eventbus.IEventBussMessageListener;
import com.hpbr.bosszhipin.module.contacts.entity.NewQuickReplyBean;
import com.hpbr.bosszhipin.module.contacts.manager.CommonWordManager;
import com.hpbr.bosszhipin.utils.SortUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.api.QuickReplyAddRequest;
import net.bosszhipin.api.QuickReplyAddResponse;
import net.bosszhipin.api.QuickReplyDeleteRequest;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.ServerUserQuickReplyBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Created by wangtian on 16/9/26.
 */
public class QuickReplyManager {
    public static final String TAG = "QuickReplyManager";

    public static final String NEW_HELLO_REPLY_TAG = "NewHelloReply";
    private static volatile List<NewQuickReplyBean> quickReplyBeans;


    private static final int BOSS_ROLE_CHAT_COMMON_MAX = 30;//B端最多30个常用语

    private static final int GEEK_ROLE_CHAT_COMMON_MAX = 15;//C端最多15个常用语

    // CommonReplyLayout2 相关常量
    private static final String SP_COMMON_REPLY = "SP_COMMON_REPLY_FOR_NEW_HELLO";
    private static final int MIN_CLICK_COUNT = 2;
    private static final MutableLiveData<Boolean> observerCommonReply = new MutableLiveData<>();


    private static final MutableLiveData<Boolean> observerQuickReply = new MutableLiveData<>();


    public static void addQuickReplyObserver(LifecycleOwner owner, Observer<Boolean> observer) {
        observerQuickReply.observe(owner, observer);
    }

    public static void addCommonReplyObserver(LifecycleOwner owner, Observer<Boolean> observer) {
        observerCommonReply.observe(owner, observer);
    }



    public static int getNormalChatCommonMax() {
        if (UserManager.isGeekRole()) {
            return GEEK_ROLE_CHAT_COMMON_MAX;
        }
        return BOSS_ROLE_CHAT_COMMON_MAX;
    }

    //是否超过普通常用语最大数量
    public static boolean isBeyondNormalChatCommonMax() {
        return LList.getCount(getCommonList()) >= getNormalChatCommonMax();
    }

    public synchronized static void reload() {
        String jsonString = SpManager.get().user().getString(TAG, "");
        if (!TextUtils.isEmpty(jsonString)) {
            quickReplyBeans = GsonUtils.fromJson(jsonString, new TypeToken<List<NewQuickReplyBean>>() {
            }.getType());
            if (quickReplyBeans == null) quickReplyBeans = new ArrayList<>();
        } else {
            quickReplyBeans = new ArrayList<>();
        }
        observerQuickReply.postValue(true);
    }

    //获得普通的常用语
    public static List<NewQuickReplyBean> getCommonList() {
        if (quickReplyBeans == null) {
            reload();
        }
        return quickReplyBeans;
    }

    public static synchronized void saveQuickReplyRaw(List<ServerUserQuickReplyBean> serverUserQuickReplyBeans) {
        if (LList.isEmpty(serverUserQuickReplyBeans)) {
            return;
        }

        List<NewQuickReplyBean> newQuickReplyBeans = new ArrayList<>();
        for (ServerUserQuickReplyBean item : serverUserQuickReplyBeans) {
            if (item == null) continue;
            if (item.identity == AccountHelper.getIdentity()) {
                NewQuickReplyBean bean = new NewQuickReplyBean();
                bean.parseFromServer(item);
                newQuickReplyBeans.add(bean);
            }
        }

        saveQuickReply(newQuickReplyBeans);
    }

    public static synchronized void saveQuickReply(List<NewQuickReplyBean> quickReplyBeans) {
        SpManager.get().user().edit().putString(TAG, GsonUtils.toJson(quickReplyBeans)).apply();
        reload();
        // 同步更新CommonReplyLayout2的数据
        syncCommonReplyFromQuickReply();
    }

    public void add(final String commonText) {
        NewQuickReplyBean bean = new NewQuickReplyBean();
        bean.content = commonText;
        addOrUpdate(bean, null, false, 1, "");
    }



    public void addFromIntroduce(final String commonText, IEventBussMessageListener listener) {
        NewQuickReplyBean bean = new NewQuickReplyBean();
        bean.content = commonText;
        addOrUpdate(bean, listener, false, 1, "1");
    }

    public void add(final String commonText, IEventBussMessageListener listener) {
        NewQuickReplyBean bean = new NewQuickReplyBean();
        bean.content = commonText;
        addOrUpdate(bean, listener, false, 1, "");
    }


    public void add(final NewQuickReplyBean bean, final IEventBussMessageListener listener, final boolean isSend, int from) {
        addOrUpdate(bean, listener, isSend, from, "");
    }

    public void update(final NewQuickReplyBean bean, final IEventBussMessageListener listener, final boolean isSend, int from) {
        addOrUpdate(bean, listener, isSend, from, "");
    }

    public static NewQuickReplyBean findNewQuickReplyBeanById(long fastReplyId) {
        List<NewQuickReplyBean> commonList = getCommonList();
        if (fastReplyId > 0 && commonList != null) {
            for (NewQuickReplyBean newQuickReplyBean : commonList) {
                if (newQuickReplyBean == null) continue;
                if (fastReplyId == newQuickReplyBean.fastReplyId) {
                    return newQuickReplyBean;
                }
            }
        }
        return null;
    }


    public void delete(final NewQuickReplyBean bean, final IEventBussMessageListener listener) {
        if (bean == null) return;
        QuickReplyDeleteRequest request = new QuickReplyDeleteRequest(new ApiRequestCallback<EmptyResponse>() {
            @Override
            public void onSuccess(ApiData<EmptyResponse> data) {
                EmptyResponse resp = data.resp;

                if (resp != null) {
                    List<NewQuickReplyBean> quickReplyBeans = getCommonList();
                    NewQuickReplyBean deletePosition = null;
                    for (NewQuickReplyBean delete : quickReplyBeans) {
                        if (delete == null) continue;
                        if (delete.fastReplyId == bean.fastReplyId) {
                            deletePosition = delete;
                            break;
                        }
                    }
                    if (deletePosition != null) {
                        quickReplyBeans.remove(deletePosition);
                        saveQuickReply(quickReplyBeans);
                    }
                    EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_REFRESH);
                    if (listener != null) {
                        listener.onResult(message, false, false);
                    }
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_REFRESH, -1, reason.getErrReason());
                if (listener != null) {
                    listener.onResult(message, false, false);
                }
            }
        });

        request.fastReplyId = bean.fastReplyId + "";
        HttpExecutor.execute(request);
    }

    /**
     * 从原add和update方法中抽取出的公共方法
     *
     * @param bean
     * @param listener
     * @param isSend
     * @param from            1聊天对话框点击常用语-新增保存，2聊天对话框点击常用语-编辑保存，3长按聊天消息-添加常用语，4常用语灰条-添加为常用语(606)
     * @param isSelfIntroduce
     */
    private void addOrUpdate(final NewQuickReplyBean bean,
                             final IEventBussMessageListener listener,
                             final boolean isSend, int from, String isSelfIntroduce) {
        boolean isOperationUpdate = bean.fastReplyId > 0;

        if (!isOperationUpdate) {
            //如果已经在常用语里面,直接提示成功
            if (CommonWordManager.getInstance().checkWeatherHasSet(bean.content)) {
                if (listener != null) {
                    if (isSend) {
                        EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_SEND, bean);
                        listener.onResult(message, true, true);
                    } else {
                        EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_REFRESH);
                        listener.onResult(message, true, true);
                    }
                }
                return;
            }
        }

        QuickReplyAddRequest request = new QuickReplyAddRequest(new ApiRequestCallback<QuickReplyAddResponse>() {
            @Override
            public void onSuccess(ApiData<QuickReplyAddResponse> data) {
                QuickReplyAddResponse resp = data.resp;

                if (resp != null) {
                    bean.fastReplyId = resp.fastReplyId;

                    List<NewQuickReplyBean> quickReplyBeans = getCommonList();
                    if (isOperationUpdate) {
                        int index = 0;
                        for (NewQuickReplyBean reset : quickReplyBeans) {
                            if (reset.fastReplyId == bean.fastReplyId) {
                                break;
                            }
                            index++;
                        }
                        if (index >= quickReplyBeans.size()) {
                            quickReplyBeans.add(bean);
                        } else {
                            quickReplyBeans.set(index, bean);
                        }
                    } else {
                        quickReplyBeans.add(0, bean);
                    }

                    saveQuickReply(quickReplyBeans);

                    if (isSend) {
                        EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_SEND, bean);
                        if (listener != null) {
                            listener.onResult(message, false, false);
                        }
                    } else {
                        EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_REFRESH);
                        if (listener != null) {
                            listener.onResult(message, false, false);
                        }
                    }
                }
            }

            @Override
            public void onComplete() {
            }

            @Override
            public void onFailed(ErrorReason reason) {
                EventBusMessage message = EventBusMessage.obtain(EventBusMessageType.QUICK_REPLY_REFRESH, -1, reason.getErrReason());
                if (listener != null) {
                    listener.onResult(message, false, false);
                }
            }
        });

        request.title = "";
        request.content = bean.content;

        if (isOperationUpdate) {
            request.fastReplyId = bean.fastReplyId + "";
        }

        request.actionType = String.valueOf(from);
        request.isSelfIntroduce = isSelfIntroduce;
        request.execute();
    }

    // ===================== CommonReplyLayout2 相关方法 =====================

    /**
     * CommonWordsBean 数据类，对应 CommonReplyLayout2 中的数据结构
     */
    public static class CommonWordsBean extends BaseServerBean {
        private static final long serialVersionUID = -4944612374707708356L;

        private String text;
        private int count;

        private long fastReplyId;
        private long clickTimeSecond;

        private boolean hasRealClick;

        public void setClickTimeSecond(long clickTimeSecond) {
            this.clickTimeSecond = clickTimeSecond;
        }

        public void setText(String text) {
            this.text = text;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public int getCount() {
            return count;
        }

        public String getText() {
            return text;
        }

        public long getFastReplyId() {
            return fastReplyId;
        }

        public void setFastReplyId(long fastReplyId) {
            this.fastReplyId = fastReplyId;
        }

        public long getClickTimeSecond() {
            return clickTimeSecond;
        }

        public boolean isHasRealClick() {
            return hasRealClick;
        }
    }

    /**
     * 保存 CommonReplyLayout2 的常用语数据
     * @param text 常用语文本
     */
    public static void saveCommonWordsText(String text, long fastReplyId) {
        List<CommonWordsBean> commonWords = getCommonWords(false);
        if (commonWords == null) {
            commonWords = new ArrayList<>();
        }

        // 集合如果存在只需要更新
        for (CommonWordsBean commonWord : commonWords) {
            if (fastReplyId == commonWord.getFastReplyId()) {
                commonWord.setCount(commonWord.count + 1);
                commonWord.setClickTimeSecond(System.currentTimeMillis());
                saveCommonWordsText(commonWords);
                return;
            }
        }

        // 添加数据到集合
        CommonWordsBean commonWordsBean = new CommonWordsBean();
        commonWordsBean.setText(text);
        commonWordsBean.setCount(1);
        commonWordsBean.setFastReplyId(fastReplyId);
        commonWordsBean.setClickTimeSecond(System.currentTimeMillis());
        commonWords.add(commonWordsBean);
        
        // 保存数据到sp里面
        saveCommonWordsText(commonWords);
    }

    /**
     * 保存 CommonReplyLayout2 的常用语数据列表
     * @param commonWords 常用语列表
     */
    private static void saveCommonWordsText(List<CommonWordsBean> commonWords) {
        String value = GsonUtils.toJson(commonWords);
        SpManager.get().user().edit().putString(SP_COMMON_REPLY, value).apply();
        observerCommonReply.postValue(true);
    }

    /**
     * 获取 CommonReplyLayout2 的常用语数据
     * @param needSort 是否需要排序
     * @return 常用语列表
     */
    public static List<CommonWordsBean> getCommonWords(boolean needSort) {
        String value = SpManager.get().user().getString(SP_COMMON_REPLY, "");
        if (LText.empty(value)) return null;
        List<CommonWordsBean> commonWordsBeanList = GsonUtils.fromJson(value, new TypeToken<List<CommonWordsBean>>() {
        }.getType());

        if (needSort && commonWordsBeanList != null) {
            // 按照点击时间排序
            sortOrderByTime(commonWordsBeanList);
        }

        // 返回业务层数据
        return commonWordsBeanList;
    }

    /**
     * 排序 CommonReplyLayout2 的常用语数据
     * @param commonWordsBeanList 常用语列表
     */
    private static void sortOrderByTime(List<CommonWordsBean> commonWordsBeanList) {
        // 按照点击次数，时间排序
        SortUtils.sort(commonWordsBeanList, (o1, o2) -> {
            // 点击次数都大于2次 比较时间,最近点击的在前面
            if (o1.count >= MIN_CLICK_COUNT && o2.count >= MIN_CLICK_COUNT) {
                if (o1.clickTimeSecond > o2.clickTimeSecond) {
                    return -1;
                } else if (o1.clickTimeSecond == o2.clickTimeSecond) {
                    return 0;
                }
                return 1;
            }

            // 优先次数多的在前面
            if (o1.count > o2.count) {
                return -1;
            } else if (o1.count < o2.count) {
                return 1;
            }
            return 0;
        });
    }

    /**
     * 从 QuickReplyManager 同步数据到 CommonReplyLayout2
     * 保持两边数据的一致性
     */
    private static synchronized void syncCommonReplyFromQuickReply() {
        List<NewQuickReplyBean> quickReplyList = getCommonList();
        List<CommonWordsBean> commonWordsList = getCommonWords(false);
        
        if (commonWordsList == null) {
            commonWordsList = new ArrayList<>();
        }
        
        if (LList.isEmpty(quickReplyList)) {
            saveCommonWordsText(new ArrayList<>());
            return;
        }
        
        // 创建新的常用语列表，确保顺序与数据一致性
        List<CommonWordsBean> newCommonWordsList = new ArrayList<>();
        
        // 首先复制原有的常用语数据
        for (CommonWordsBean commonWord : commonWordsList) {
            boolean existInQuickReply = false;
            
            // 检查是否在QuickReply中存在
            for (NewQuickReplyBean quickReplyBean : quickReplyList) {
                if (quickReplyBean.fastReplyId == commonWord.getFastReplyId()) {
                    commonWord.text = quickReplyBean.content;
                    existInQuickReply = true;
                    break;
                }
            }
            
            // 如果在QuickReply中存在，保留
            if (existInQuickReply) {
                newCommonWordsList.add(commonWord);
            }
        }
        
        // 然后添加QuickReply中有但CommonWords中没有的数据
        for (NewQuickReplyBean quickReplyBean : quickReplyList) {
            boolean existInCommonWords = false;
            
            // 检查是否已经在新列表中
            for (CommonWordsBean commonWord : newCommonWordsList) {
                if (quickReplyBean.fastReplyId == commonWord.getFastReplyId()) {
                    existInCommonWords = true;
                    break;
                }
            }
            
            // 如果不在新列表中，添加
            if (!existInCommonWords) {
                CommonWordsBean newCommonWord = new CommonWordsBean();
                newCommonWord.setText(quickReplyBean.content);
                newCommonWord.setFastReplyId(quickReplyBean.fastReplyId);
                newCommonWordsList.add(newCommonWord);
            }
        }

        // 前三个 次数为2
        for (int i = 0; i < Math.min(newCommonWordsList.size(), 3); i++) {
            newCommonWordsList.get(i).setCount(MIN_CLICK_COUNT);
        }
        
        // 保存更新后的常用语列表
        saveCommonWordsText(newCommonWordsList);
    }
}
