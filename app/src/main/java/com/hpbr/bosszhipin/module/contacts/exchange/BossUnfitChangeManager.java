package com.hpbr.bosszhipin.module.contacts.exchange;

import android.app.Activity;
import android.text.TextUtils;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.unfit.BossMarkGeekGrayDialog;
import com.hpbr.bosszhipin.module.unfit.F2UnfitManager;
import com.hpbr.bosszhipin.module.unfit.UnfitChooseOtherReasonHelper;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.monch.lbase.util.LList;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.bean.ServerResponseReplayBean;

import java.util.List;

/**
 * create by guofeng
 * date on 2021/3/5
 */

public class BossUnfitChangeManager {

    private static BossUnfitChangeManager instance = new BossUnfitChangeManager();

    private BossUnfitChangeManager() {
        //no instance
    }

    public static BossUnfitChangeManager getInstance() {
        return instance;
    }


    /**
     * boss收藏|取消收藏事件
     *
     * @param contactBean
     * @param callBack
     */
    public void onStartChangeListener(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //已经设置了收藏,取消收藏
        if (contactBean.isStar) {
            cancelStart(pageType, contactBean, callBack);
            return;
        }

        //未设置了收藏,去收藏
        setStar(pageType, contactBean, callBack);
    }

    /**
     * 店长点击不合适
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void onDianZhangUnfitListener(ContactBean contactBean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        if (contactBean.isReject) {

            ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                    params.markType = 1;
                    params.markId = contactBean.friendId;
                    params.pageType = pageType == 7 ? 7 : 6;
                    params.jobId = contactBean.jobId;
                    params.expectId = contactBean.jobIntentId;
                    params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
                    params.securityId = contactBean.securityId;
                    RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
                }
            }, ContactKeyManager.BgSource.UNFIT_SOURCE);

        } else {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (topActivity == null) return;
            F2UnfitManager f2UnfitManager = new F2UnfitManager(topActivity, contactBean);
            f2UnfitManager.setCallBack(item -> {

                ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                    @Override
                    public void onCheckNoBlockListener() {
                        //设置不合适
                        setUnfit(pageType, contactBean, callBack, item);
                    }
                }, ContactKeyManager.BgSource.UNFIT_SOURCE);

            });
            f2UnfitManager.setStarCallBack(() -> {
                //已经收藏了好友,取消收藏
                if (contactBean.isStar) {
                    cancelStart(pageType, contactBean, callBack);
                } else {//没有收藏好友，去收藏好友
                    setStar(pageType, contactBean, callBack);
                }
            });
            f2UnfitManager.getDianZhangUnfitList(pageType);
        }
    }

    /**
     * Boss设置 不合适事件
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void onChangeUnfitListener(ContactBean contactBean, int pageType, int reasonType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //已经设置了不合适
        if (contactBean.isReject) {
            RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
            params.markType = 1;
            params.markId = contactBean.friendId;
            params.pageType = pageType;
            params.jobId = contactBean.jobId;
            params.markReason = reasonType;
            params.expectId = contactBean.jobIntentId;
            params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
            params.securityId = contactBean.securityId;
            RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
            return;
        }

        //没有设置不合适，弹窗 选择设置不合适/收藏
        showUnfitDialog(pageType, contactBean, callBack);
    }


    //Boss直接设置牛人不合适
    public void onBossUnfitListener(ContactBean contactBean, int pageType, int reasonType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //把好友标记为不合适
        RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
        params.markId = contactBean.friendId;
        params.pageType = pageType;
        params.jobId = contactBean.jobId;
        params.expectId = contactBean.jobIntentId;
        params.markReason = (int) reasonType;
        params.markType = 1;
        params.securityId = contactBean.securityId;
        params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
        RejectHttpManager.getInstance().setLock(contactBean, params, callBack);
    }

    /**
     * Boss设置 不合适事件
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void onChangeUnfitListener(ContactBean contactBean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //已经设置了不合适
        if (contactBean.isReject) {
            ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                    params.markType = 1;
                    params.markId = contactBean.friendId;
                    params.pageType = pageType;
                    params.jobId = contactBean.jobId;
                    params.expectId = contactBean.jobIntentId;
                    params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
                    params.securityId = contactBean.securityId;
                    RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
                }
            }, ContactKeyManager.BgSource.UNFIT_SOURCE);
            return;
        }

        //没有设置不合适，弹窗 选择设置不合适/收藏
        showUnfitDialog(pageType, contactBean, callBack);
    }


    /**
     * 设置页面不合适
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void onChangeUnfitListenerChatSetting(ContactBean contactBean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //已经设置了不合适
        if (contactBean.isReject) {
            RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
            params.markType = 1;
            params.markId = contactBean.friendId;
            params.pageType = pageType;
            params.jobId = contactBean.jobId;
            params.expectId = contactBean.jobIntentId;
            params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
            params.securityId = contactBean.securityId;
            RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
            return;
        }


        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        F2UnfitManager f2UnfitManager = new F2UnfitManager(topActivity, contactBean);
        f2UnfitManager.setCallBack(item -> {
            setUnfit(pageType, contactBean, callBack, item);
        });
        f2UnfitManager.setStarCallBack(() -> {
            //已经收藏了好友,取消收藏
            if (contactBean.isStar) {
                cancelStart(pageType, contactBean, callBack);
            } else {//没有收藏好友，去收藏好友
                setStar(pageType, contactBean, callBack);
            }
        });
        f2UnfitManager.getUnfitList(pageType);
    }

    /**
     * B端简历页-标记合适、不合适 - 1117.160【招聘者】拒绝语优化
     */
    public void onBossResumeChangeUnfitListener(ContactBean contactBean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        //已经设置了不合适
        if (contactBean.isReject) {
            RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
            params.markType = 1;
            params.markId = contactBean.friendId;
            params.pageType = pageType;
            params.jobId = contactBean.jobId;
            params.expectId = contactBean.jobIntentId;
            params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
            params.securityId = contactBean.securityId;
            RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
        } else {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (ActivityUtils.isValid(topActivity)) {
                BossMarkGeekGrayDialog dialog = new BossMarkGeekGrayDialog(topActivity, contactBean);
                dialog.showUnfitDialog(pageType, callBack);
            }
        }
    }


    /**
     * //没有设置不合适，弹窗 选择设置不合适/收藏
     *
     * @param pageType
     * @param contactBean
     */
    private void showUnfitDialog(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack callBack) {

        if (contactBean == null) return;

        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        F2UnfitManager f2UnfitManager = new F2UnfitManager(topActivity, contactBean);
        f2UnfitManager.setCallBack(item -> {

            ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    setUnfit(pageType, contactBean, callBack, item);
                }
            }, ContactKeyManager.BgSource.UNFIT_SOURCE);

        });
        f2UnfitManager.setStarCallBack(() -> {
            //已经收藏了好友,取消收藏
            if (contactBean.isStar) {
                cancelStart(pageType, contactBean, callBack);
            } else {//没有收藏好友，去收藏好友
                setStar(pageType, contactBean, callBack);
            }
        });
        f2UnfitManager.getUnfitList(pageType);
    }

    /**
     * //设置不合适
     *
     * @param pageType
     * @param contactBean
     * @param callBack
     * @param item
     */
    private void setUnfit(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack callBack, ServerResponseReplayBean item) {
        if (contactBean == null) return;
        //把好友标记为不合适
        RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
        params.markId = contactBean.friendId;
        params.pageType = pageType;
        params.jobId = contactBean.jobId;
        params.expectId = contactBean.jobIntentId;
        params.markReason = (int) item.reasonType;
        params.markType = 1;
        params.securityId = contactBean.securityId;
        params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;

        //判断是否点击了"其它"
        if (UnfitChooseOtherReasonHelper.getInstance().checkChooseOtherReason(item.reasonType, params, params1 -> RejectHttpManager.getInstance().setLock(contactBean, params1, callBack)))
            return;

        RejectHttpManager.getInstance().setLock(contactBean, params, callBack);
    }

    /**
     * 设置收藏
     *
     * @param pageType
     * @param contactBean
     * @param callBack
     */
    private void setStar(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;

        //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=107254211 必须有职位才可以进行收藏
        List<JobBean> canUseJobList = UserManager.getCanUseJobList(UserManager.getLoginUser());
        if (LList.isEmpty(canUseJobList)) {
            ToastUtils.showText("请先发布一个职位");
            return;
        }

        ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
            @Override
            public void onCheckNoBlockListener() {
                RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                params.markType = 5;
                params.markId = contactBean.friendId;
                params.pageType = pageType;
                params.jobId = contactBean.jobId;
                params.expectId = contactBean.jobIntentId;
                params.securityId = contactBean.securityId;
                params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
                RejectHttpManager.getInstance().setStar(contactBean, params, callBack);
            }
        }, ContactKeyManager.BgSource.STAR_SOURCE);


    }

    /**
     * 取消收藏
     *
     * @param pageType
     * @param contactBean
     * @param callBack
     */
    private void cancelStart(int pageType, ContactBean contactBean, RejectHttpManager.OnLockUnLockCallBack callBack) {
        if (contactBean == null) return;
        RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
        params.markId = contactBean.friendId;
        params.pageType = pageType;
        params.jobId = contactBean.jobId;
        params.expectId = contactBean.jobIntentId;
        params.markReason = 0;
        params.markType = 5;
        params.securityId = contactBean.securityId;
        params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
        RejectHttpManager.getInstance().cancelStar(contactBean, params, callBack);
    }


} 