package com.hpbr.bosszhipin.module.contacts.exchange;

import android.app.Activity;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.common.dialog.GeekFeedBackStyleDialog;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.twl.utils.ActivityUtils;

/**
 * create by guofeng
 * date on 2021/3/5
 * <p>
 * 牛人设置  感兴趣 || 不感兴趣
 */

public class GeekInterestedChangeManager {

    private static GeekInterestedChangeManager instance = new GeekInterestedChangeManager();

    private GeekInterestedChangeManager() {
    }

    public static GeekInterestedChangeManager getInstance() {
        return instance;
    }


    /**
     * @param contactBean
     * @param pageType    { 埋点用的 1 详情页面 2 聊天对话框 3 设置页面 4 快捷回复}
     * @param callBack
     */
    public void changInterestListener(ContactBean contactBean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {

        if (contactBean == null) return;

        // 已经设置了不感兴趣,取消 设置 不感兴趣
        if (contactBean.isReject) {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (topActivity == null) {
                return;
            }

            new DialogUtils.Builder(topActivity).setTitle("取消不感兴趣").setDesc("取消设置不感兴趣后,系统将继续提示来自\"" + contactBean.friendName + "\"的消息").setPositiveAction("确定", new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                    params.markType = 1;
                    params.markId = contactBean.friendId;
                    params.pageType = pageType;
                    params.jobId = contactBean.jobId;
                    params.expectId = contactBean.jobIntentId;
                    params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
                    params.securityId = contactBean.securityId;
                    RejectHttpManager.getInstance().cancelLock(contactBean, params, true, callBack);
                }
            }).setNegativeAction("取消").setDoubleButton().build().show();
            return;
        }
        //设置不感兴趣
        showUnInterestReasonDialog(contactBean, pageType, callBack);
    }


    /**
     * 显示"不感兴趣"原因弹窗
     *
     * @param bean
     * @param pageType
     */
    private void showUnInterestReasonDialog(ContactBean bean, int pageType, RejectHttpManager.OnLockUnLockCallBack callBack) {

        Activity topActivity = ForegroundUtils.get().getTopActivity();

        if (!ActivityUtils.isValid(topActivity)) return;

        // 解决工单反馈问题，先关闭键盘，不然弹窗展示可能错位。
        AppUtil.hideSoftInput(topActivity);
        GeekFeedBackStyleDialog.GeekFeedBackStyleA geekFeedBackStyleA = new GeekFeedBackStyleDialog.GeekFeedBackStyleA(pageType, bean, callBack);
        geekFeedBackStyleA.loadData(bean.securityId);
    }
}