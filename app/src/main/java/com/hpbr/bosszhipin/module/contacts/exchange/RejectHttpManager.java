package com.hpbr.bosszhipin.module.contacts.exchange;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module_boss_export.BossPageRouter;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.ContactAntiRejectRequest;
import net.bosszhipin.api.ContactAntiRejectResponse;
import net.bosszhipin.api.ContactRejectRequest;
import net.bosszhipin.api.ContactRejectResponse;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.api.bean.ServerResponseReplayBean;
import net.bosszhipin.base.ApiRequestCallback;

import zpui.lib.ui.popup.cookie.ZPUICookieBar;

/**
 * create by guofeng
 * date on 2021/3/5
 */

public class RejectHttpManager {

    private static final RejectHttpManager instance = new RejectHttpManager();


    private RejectHttpManager() {
    }

    public static RejectHttpManager getInstance() {
        return instance;
    }


    /**
     * 接口请求需求的参数
     */
    public static class HttpParams extends BaseServerBean {
        private static final long serialVersionUID = 1L;
        public String lid;
        public long jobId;
        public int pageType;
        public long markId;
        public int markType;
        public long expectId;
        public String securityId;
        public String reasonText;
        public String markReasonText;
        public int markReason;
    }


    /**
     * 设置收藏/设置不合适/设置不感兴趣
     *
     * @param params
     */
    public void setLock(ContactBean contactBean, HttpParams params, OnLockUnLockCallBack callBack) {

        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        ProgressDialog progressDialog = getProgressDialog(topActivity);


        ContactRejectRequest request = new ContactRejectRequest(new ApiRequestCallback<ContactRejectResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                try {
                    if (ActivityUtils.isValid(topActivity)) {
                        progressDialog.show();
                    }
                } catch (Exception e) {
                   e.printStackTrace();
                }
            }

            @Override
            public void handleInChildThread(ApiData<ContactRejectResponse> data) {
                super.handleInChildThread(data);
                ContactRejectResponse resp = data.resp;

                if (resp.mark) {
                    contactBean.isStar = false;
                    contactBean.isReject = true;
                    contactBean.rejectReason = resp.markTip;
                } else {
                    contactBean.isStar = false;
                    contactBean.isReject = true;
                    contactBean.rejectReason = "";
                }

                ContactManager.getInstance().clearContactsNoneRead(contactBean.friendId, contactBean.friendSource);
                ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
            }

            @Override
            public void onSuccess(ApiData<ContactRejectResponse> data) {
                //回掉业务层
                if (callBack != null) {
                    callBack.onSuccess(contactBean);
                }
                //发送广播不合适
                notifyFriendReject(App.getAppContext(), contactBean.isReject, contactBean.jobId, contactBean.jobIntentId, contactBean.friendId);
                //刷新联系人列表
                ContactManager.getInstance().refreshContacts();

            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                if (callBack != null) {
                    callBack.onFailed(contactBean);
                }
            }
        });
        request.reasonText = params.reasonText;
        request.markId = params.markId;
        request.pageType = params.pageType;
        request.jobId = params.jobId;
        request.expectId = params.expectId;
        request.markReason = params.markReason;
        request.markReasonText = params.markReasonText;
        request.markType = 1;
        request.securityId = params.securityId;
        request.lid = params.lid;
        HttpExecutor.execute(request);

        reportSecurityIdNUll(params.securityId);
    }


    /**
     * 获得loading对象
     *
     * @param activity
     * @return
     */
    private ProgressDialog getProgressDialog(Activity activity) {
        return new ProgressDialog(activity);
    }


    /**
     * 取消设置不合适/不感兴趣/收藏
     *
     * @param contactBean
     * @param params
     * @param showToast
     * @param callBack
     */
    public void cancelLock(ContactBean contactBean, HttpParams params, boolean showToast, OnLockUnLockCallBack callBack) {

        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        ProgressDialog progressDialog = getProgressDialog(topActivity);


        ContactAntiRejectRequest request = new ContactAntiRejectRequest(new ApiRequestCallback<ContactAntiRejectResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.show();
                }
            }

            @Override
            public void handleInChildThread(ApiData<ContactAntiRejectResponse> data) {
                super.handleInChildThread(data);
                ContactAntiRejectResponse resp = data.resp;
                if (resp != null) {
                    contactBean.isReject = false;
                    contactBean.isStar = false;
                    contactBean.rejectReason = "";
                    /*非好友关系取消不合适（非好友关系H5举报入口标记不合适，取消不合适时ContactBean是本地new出来的）*/
                    ContactBean localContactBean = ContactManager.getInstance().queryContactByFriendId(contactBean.friendId, UserManager.getUserRole().get(), contactBean.friendSource);
                    if (localContactBean != null) {
                        ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
                    }
                }
            }

            @Override
            public void onSuccess(ApiData<ContactAntiRejectResponse> data) {
                //回掉业务层
                if (callBack != null) {
                    callBack.onSuccess(contactBean);
                }

                if (showToast) {
                    if (UserManager.isBossRole()) {
                        ToastUtils.showText("您已取消对该牛人的不合适标记，系统将继续通知TA对你发出的聊天消息");
                    } else {
                        ToastUtils.showText("已取消不感兴趣");
                    }
                }

                //广播通知不合适
                notifyFriendReject(App.getAppContext(), contactBean.isReject, contactBean.jobId, contactBean.jobIntentId, contactBean.friendId);
                //刷新联系人列表
                ContactManager.getInstance().refreshContacts();
            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                if (callBack != null) {
                    callBack.onFailed(contactBean);
                }
            }
        });
        request.markType = 1;
        request.markId = params.markId;
        request.pageType = params.pageType;
        request.jobId = params.jobId;
        request.expectId = params.expectId;
        request.lid = params.lid;
        request.securityId = params.securityId;
        HttpExecutor.execute(request);

    }


    /**
     * 标记/解除好友不合适
     *
     * @param context 上下文
     * @param reject  true 已标记不合适，false 解除不合适
     */
    private void notifyFriendReject(Context context, boolean reject, long jobId, long expectId, long friendId) {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_FRIEND_REJECT_ACTION);
        intent.putExtra(Constants.DATA_BOOLEAN, reject);
        intent.putExtra(Constants.DATA_JOB_ID, jobId);
        intent.putExtra(Constants.DATA_LONG, expectId);
        intent.putExtra(Constants.DATA_LONG2, friendId);
        ReceiverUtils.sendBroadcast(context, intent);
    }


    /**
     * 设置 感兴趣/不合适/收藏 业务层回掉
     */
    public interface OnLockUnLockCallBack {

        void onSuccess(ContactBean contactBean);

        default void onFailed(ContactBean contactBean) {
        }

        default void onClickReasonItem(ServerResponseReplayBean bean) {}
    }


    /**
     * boss对牛人设置 收藏
     */
    public void setStar(ContactBean contactBean, HttpParams params, OnLockUnLockCallBack callBack) {

        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        ProgressDialog progressDialog = getProgressDialog(topActivity);


        ContactRejectRequest request = new ContactRejectRequest(new ApiRequestCallback<ContactRejectResponse>() {
            @Override
            public void onStart() {
                super.onStart();
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.show();
                }
            }

            @Override
            public void handleInChildThread(ApiData<ContactRejectResponse> data) {
                super.handleInChildThread(data);
                //修改数据
                ContactRejectResponse resp = data.resp;
                if (resp != null) {
                    contactBean.isStar = true;
                    ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
                }
            }

            @Override
            public void onSuccess(ApiData<ContactRejectResponse> data) {
                //回掉业务层
                if (callBack != null) {
                    callBack.onSuccess(contactBean);
                }
                showCookie(topActivity);
            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                if (callBack != null) {
                    callBack.onFailed(contactBean);
                }
            }
        });
        request.reasonText = params.reasonText;
        request.markId = params.markId;
        request.pageType = params.pageType;
        request.jobId = params.jobId;
        request.expectId = params.expectId;
        request.markReason = params.markReason;
        request.markType = 5;
        request.securityId = params.securityId;
        request.lid = params.lid;
        HttpExecutor.execute(request);

        reportSecurityIdNUll(params.securityId);

    }


    //限制上报次数
    private int hasReportCount;

    /**
     * 聊天点击收藏/不感兴趣/不合适需要统计securityId为空有多少，后续取消明文，只保留securityId
     *
     * @param securityId
     */
    private void reportSecurityIdNUll(String securityId) {
        if (hasReportCount > 5) return;
        if (LText.empty(securityId)) {
            ApmAnalyzer.create()
                    .action(ApmAnalyticsAction.USER_MARK_MISS_SECURITY_ID)
                    .report();
            hasReportCount++;
        }
    }


    /**
     * boss对牛人设置 取消 收藏
     */
    public void cancelStar(ContactBean contactBean, HttpParams params, OnLockUnLockCallBack callBack) {

        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        ProgressDialog progressDialog = getProgressDialog(topActivity);


        ContactAntiRejectRequest request = new ContactAntiRejectRequest(new ApiRequestCallback<ContactAntiRejectResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.show();
                }
            }

            @Override
            public void handleInChildThread(ApiData<ContactAntiRejectResponse> data) {
                super.handleInChildThread(data);
                ContactAntiRejectResponse resp = data.resp;
                if (resp != null) {
                    //修改数据
                    contactBean.isStar = false;
                    ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
                }
            }

            @Override
            public void onSuccess(ApiData<ContactAntiRejectResponse> data) {
                //回掉业务层
                if (callBack != null) {
                    callBack.onSuccess(contactBean);
                }
                ToastUtils.showText("取消收藏成功");
            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
                if (callBack != null) {
                    callBack.onFailed(contactBean);
                }
            }
        });
        request.markType = 5;
        request.markId = params.markId;
        request.pageType = params.pageType;
        request.jobId = params.jobId;
        request.expectId = params.expectId;
        request.securityId = params.securityId;
        request.lid = params.lid;
        HttpExecutor.execute(request);
    }

    private void showCookie(Activity activity) {

        if (UserManager.isBossRole()) {
            if (ActivityUtils.isValid(activity)) {
                View view = LayoutInflater.from(activity).inflate(R.layout.twl_ui_cookie_collect_action_success, null);

                final ZPUICookieBar cookieBar = new ZPUICookieBar.Builder(activity)
                        .setCustomView(view)
                        .setLayoutGravity(Gravity.BOTTOM)
                        .setDuration(5000)
                        .create();

                view.findViewById(R.id.cl_toast).setOnClickListener(new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        if (cookieBar != null) {
                            cookieBar.dismiss();
                        }
                        AnalyticsFactory.create().action(AnalyticsAction.ACTION_GEEK_LIKE_GUIDE_CLICK)
                                .param("p", 2)
                                .build();
                        BossPageRouter.jumpToBossCollectionGeekGrayActivity(activity);
                    }
                });

                cookieBar.show();

                AnalyticsFactory.create().action(AnalyticsAction.ACTION_GEEK_LIKE_GUIDE_EXPO)
                        .param("p", 2)
                        .build();
            }
        }

    }
} 