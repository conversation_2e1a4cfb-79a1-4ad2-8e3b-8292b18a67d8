package com.hpbr.bosszhipin.module.contacts.exchange;

import static com.hpbr.bosszhipin.module.contacts.constant.IntentParamConstant.FROM_CHAT_SCREEN;

import android.app.Activity;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.live.export.LiveConstants;
import com.hpbr.bosszhipin.live.export.LiveSchoolFinishService;
import com.hpbr.bosszhipin.module.contacts.manager.ContactKeyManager;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.module.main.fragment.contacts.UnfitManager;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.sankuai.waimai.router.Router;

/**
 * create by guofeng
 * date on 2021/3/5
 */

public class UserRejectManager {

    public static final int PAGE_TYPE_FAST_REPLY = 4;
    public static final int PAGE_TYPE_CHAT_SETTING = 3;
    public static final int PAGE_TYPE_CHAT_PAGE = 2;
    public static final int PAGE_TYPE_DETAIL = 1;
    public static final int PAGE_POUOP_TRVERT = 6;
    public static final int PAGE_TYPE_UNFIT = 7;//不合适列表
    public static final int PAGE_TYPE_UNFITREJECT = 9;
    //1001.802【直播】直播投递闭环 - 在简历页点击左下角不合适按钮
    public static final int PAGE_LIVE_DELIVERY_CLOSED_LOOP_1001802 = 10;


    private static UserRejectManager instance = new UserRejectManager();

    public static UserRejectManager getInstance() {
        return instance;
    }

    private UserRejectManager() {
    }


    public void changeUnfitPageMoveListener(ContactBean contactBean,
                                     int pageType,
                                     RejectHttpManager.OnLockUnLockCallBack callBack) {

        bgAction(contactBean, pageType);

        if (UserManager.isGeekRole()) {
            RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
            params.markType = 1;
            params.markId = contactBean.friendId;
            params.pageType = pageType;
            params.jobId = contactBean.jobId;
            params.expectId = contactBean.jobIntentId;
            params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
            params.securityId = contactBean.securityId;
            RejectHttpManager.getInstance().cancelLock(contactBean, params, false, callBack);
            return;
        }

        if (UserManager.isBossRole()) {
            ContactKeyManager.getInstance().onCheckListener(contactBean, new ContactKeyManager.OnContactKeyCheckCallBack2() {
                @Override
                public void onCheckNoBlockListener() {
                    RejectHttpManager.HttpParams params = new RejectHttpManager.HttpParams();
                    params.markType = 1;
                    params.markId = contactBean.friendId;
                    params.pageType = pageType;
                    params.jobId = contactBean.jobId;
                    params.expectId = contactBean.jobIntentId;
                    params.lid = TextUtils.isEmpty(contactBean.lid) ? "" : contactBean.lid;
                    params.securityId = contactBean.securityId;
                    RejectHttpManager.getInstance().cancelLock(contactBean, params, false, callBack);
                }
            }, ContactKeyManager.BgSource.UNFIT_SOURCE);
        }

    }

    /**
     * Boss点击不合适||geek点击不感兴趣
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void changeRejectListener(ContactBean contactBean,
                                     int pageType,
                                     RejectHttpManager.OnLockUnLockCallBack callBack) {

        bgAction(contactBean, pageType);

        if (UserManager.isGeekRole()) {
            GeekInterestedChangeManager.getInstance().changInterestListener(contactBean, pageType, callBack);
            return;
        }

        if (UserManager.isBossRole()) {
            BossUnfitChangeManager.getInstance().onChangeUnfitListener(contactBean, pageType, callBack);
        }

    }

    /**
     * 设置页面不合适
     *
     * @param contactBean
     * @param pageType
     * @param callBack
     */
    public void changeRejectListenerChatSetting(ContactBean contactBean,
                                                int pageType,
                                                RejectHttpManager.OnLockUnLockCallBack callBack) {
        bgAction(contactBean, pageType);
        if (UserManager.isGeekRole()) {
            GeekInterestedChangeManager.getInstance().changInterestListener(contactBean, pageType, callBack);
            return;
        }

        if (UserManager.isBossRole()) {
            BossUnfitChangeManager.getInstance().onChangeUnfitListenerChatSetting(contactBean, pageType, callBack);
        }
    }


    ///////////////////////////////////////////////////////////////////////////
    // 以下是埋点
    ///////////////////////////////////////////////////////////////////////////

    private void bgAction(ContactBean bean, int pageType) {
        //埋点
        if (UserManager.isBossRole() && !bean.isReject && !(pageType == 2)) {
            bgActionEvent(bean, pageType);
        }
    }


    private void bgActionEvent(ContactBean bean, int pageType) {
        AnalyticsFactory analyticsFactory = AnalyticsFactory.create().action("boss-hide");
        analyticsFactory.param("p", String.valueOf(bean.friendId));
        analyticsFactory.param("p2", String.valueOf(bean.jobId));
        analyticsFactory.param("p3", String.valueOf(bean.jobIntentId));
        analyticsFactory.param("p5", String.valueOf(1));
        analyticsFactory.param("p8", String.valueOf(pageType));
        analyticsFactory.build();

    }

    /*标记为不合适后，是否跳转至MainActivity*/
    public boolean checkEnableRejectJumpToMain() {
        return !liveSceneReject();
    }

    /*1001.802【直播】直播投递闭环*//*标记不合适后，直接回到直播列表模块*/
    private boolean liveSceneReject() {
        return UserManager.isBossRole()
                && ForegroundUtils.get().hasActivity(getLiveSchoolService());
    }

    private LiveSchoolFinishService liveSchoolFinishService;

    /*1001.802【直播】直播投递闭环*/
    public Class<?> getLiveSchoolService() {
        Class<?> cls = null;
        if (null == liveSchoolFinishService) {
            liveSchoolFinishService = Router.getService(
                    LiveSchoolFinishService.class, LiveConstants.LIVE_SCHOOL_FINISH_SERVICE);
        }
        if (null != liveSchoolFinishService) {
            cls = liveSchoolFinishService.getSchoolFinishActivity();
        }
        return cls;
    }

    //检测是否有指定要跳转的页面
    private boolean checkJumpToTargetPage(@NonNull ContactBean contactBean) {
        Class<?> newGreetGeekListActivityClass = ForegroundUtils.get().hasNewGreetGeekListActivity();
        if (newGreetGeekListActivityClass != null) {/*是否有新招呼牛人列表页*/
            ForegroundUtils.get().finishUntilActivity(newGreetGeekListActivityClass);
            return true;
        }

        //蓝白页面不处理
        if (contactBean.friendSource == ContactBean.FROM_DIAN_ZHANG) return false;

        Class<?> waitingHandleGreetingMsg = ForegroundUtils.get().hasWaitingHandleGreetingMsg();

        if (waitingHandleGreetingMsg != null) {
            ForegroundUtils.get().finishUntilActivity(waitingHandleGreetingMsg);
            return true;
        }

        Class<?> filterPageActivity = ForegroundUtils.get().hasFilterPage();

        if (filterPageActivity != null) {
            ForegroundUtils.get().finishUntilActivity(filterPageActivity);
            return true;
        }

        return false;
    }

    //聊天页面设置 "不合适"
    public void chatPageUnfitListenerJump(Activity activity, String from, ContactBean contactBean) {

        if (checkJumpToTargetPage(contactBean)) return;

        if (UserRejectManager
                .getInstance().checkEnableRejectJumpToMain()) {

            if (FROM_CHAT_SCREEN.equals(from)) {

                AppUtil.finishActivity(activity);

            } else {

                //关闭当前页面
                AppUtil.finishActivity(activity);

            }

        }
    }

    //聊天设置页面点击 "不合适"
    public void chatSettingUnfitListenerJump(ContactBean contactBean, Activity activity) {

        if (contactBean.isReject) {

            if (checkJumpToTargetPage(contactBean)) return;

            if (UserRejectManager
                    .getInstance().checkEnableRejectJumpToMain()) { //Boss设置不合适,返回f2
                // 跳转到f2广播
                Intent intent = new Intent();
                intent.setAction(Constants.RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION);
                intent.putExtra(Constants.DATA_INT, 2);
                intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                activity.sendBroadcast(intent);
                AppUtil.startActivity(activity, new Intent(activity, MainActivity.class), true, ActivityAnimType.NONE);
            }
        }


    }

    //Boss查看牛人简历详情页面 点击 "不合适"
    public void bossViewGeekResumeUnfitJump(ContactBean contactBean, Activity activity) {
        if (contactBean.isReject) {

            if (checkJumpToTargetPage(contactBean)) return;

            //1001.802【直播】直播投递闭环页面进入牛人详情选择不适合，无需跳转到f2
            if (UserRejectManager.getInstance().checkEnableRejectJumpToMain()) {
                //此处同ChatNewActivity中的不合适逻辑相同
                // 跳转到f2广播
                Intent intent = new Intent();
                intent.setAction(Constants.RECEIVER_CHANGE_MAIN_SELECT_INDEX_ACTION);
                intent.putExtra(Constants.DATA_INT, 2);
                intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                activity.sendBroadcast(intent);
                AppUtil.startActivity(activity, new Intent(activity, MainActivity.class), true, ActivityAnimType.NONE);
            }
        }
    }
}