package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.contacts.filter.processor.TopContactStatisticsProcessor;
import com.hpbr.bosszhipin.module.contacts.filter.processor.UnreadCountStatisticsProcessor;

import java.util.List;

/**
 * 过滤器工厂类
 * 提供常用的过滤器组合工厂方法
 */
public class ContactFilterFactory {

    /**
     * 创建F2ContactList的标准过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createF2ContactListFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new RemoveFilteredFilter())
                .addFilter(new BatchChatBackFilter())
                .addFilter(new RejectFilter())
                .addFilter(new RejectIntentFilter());
    }

    /**
     * 创建SingleContact的标准过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createSingleContactListFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new RemoveFilteredFilter())
                .addFilter(new BatchChatBackFilter())
                .addFilter(new RejectFilter());
    }

    /**
     * 创建置顶联系人的标准过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createTopContactFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new RemoveFilteredFilter())
                .addFilter(new BatchChatBackFilter())
                .addFilter(new RejectFilter())
                .addFilter(new TopContactFilter());
    }

    /**
     * 创建非置顶联系人的标准过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createNonTopContactFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new RemoveFilteredFilter())
                .addFilter(new BatchChatBackFilter())
                .addFilter(new RejectFilter())
                .addFilter(new NonTopContactFilter());
    }

    /**
     * 创建快速处理联系人的过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createFastHandleFilters(boolean includeUnfit) {
        ContactFilterFramework.FilterBuilder builder = ContactFilterFramework.newBuilder()
                .addFilter(new FastHandleFilter());

        if (!includeUnfit) {
            builder.addFilter(new RemoveFilteredFilter())
                    .addFilter(new BatchChatBackFilter())
                    .addFilter(new RejectFilter());
        } else {
            builder.addFilter(new FilteredUserFilter());
        }

        return builder;
    }

    /**
     * 创建Boss收藏联系人的过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createBossStarFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new StarContactFilter());
    }

    /**
     * 单聊联系人
     */
    public static ContactFilterFramework.FilterBuilder createSingleContactFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new RejectFilter())
                .addFilter(new BatchChatBackFilter())
                .addFilter(new RemoveFilteredFilter());
    }

    /**
     * 创建未读消息过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createUnreadMessageFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new UnreadMessageFilter());
    }

    /**
     * 创建未读数量类型过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createUnreadCountTypeFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new UnreadCountTypeFilter());
    }

    /**
     * 创建未读小红点类型过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createUnreadSilentTypeFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new UnreadSilentTypeFilter());
    }


    /**
     * 创建发过消息的联系人过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createSentMessageFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new ContactSentMessageFilter());
    }

    /**
     * 创建获取被过滤用户的过滤器组合
     */
    public static ContactFilterFramework.FilterBuilder createObtainFilteredUserFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new FilteredUserFilter());
    }

    /**
     * 创建无过滤器组合（返回所有用户）
     */
    public static ContactFilterFramework.FilterBuilder createAllUserFilters() {
        return ContactFilterFramework.newBuilder()
                .addFilter(new AllUserFilter());
    }

    // ============== 统计处理器工厂方法 ==============

    /**
     * 创建置顶联系人统计处理器 (替代 getTopCount、getTopNoneReadCount 方法)
     */
    public static TopContactStatisticsProcessor createTopContactStatisticsProcessor() {
        return new TopContactStatisticsProcessor();
    }

    /**
     * 创建未读数量统计处理器 (替代 getContactNoneReadMsgCount、getSingleContactNoneReadMsgCount 等方法)
     */
    public static UnreadCountStatisticsProcessor createUnreadCountStatisticsProcessor() {
        return new UnreadCountStatisticsProcessor();
    }


    // ============== 组合使用示例方法 ==============

    /**
     * 示例：获取置顶联系人数量和未读数量 (替代原有的 getTopCount 和 getTopNoneReadCount 方法)
     *
     * @param allContacts 所有联系人列表
     * @return 置顶联系人统计处理器，包含统计结果
     */
    public static TopContactStatisticsProcessor getTopContactStatistics(List<ContactBean> allContacts) {
        TopContactStatisticsProcessor processor = createTopContactStatisticsProcessor();

        ContactFilterFramework.newBuilder()
                .addFilter(new AllUserFilter()) // 不过滤任何联系人，只做统计
                .addProcessor(processor)
                .apply(allContacts);

        return processor;
    }

    /**
     * 示例：获取联系人未读消息总数 (替代原有的 getContactNoneReadMsgCount 方法)
     *
     * @param allContacts 所有联系人列表
     * @return 未读消息总数
     */
    public static int getContactNoneReadMsgCount(List<ContactBean> allContacts) {
        UnreadCountStatisticsProcessor processor = createUnreadCountStatisticsProcessor();

        ContactFilterFramework.newBuilder()
                .addFilter(new UnreadCountTypeFilter()) // 只统计数量类型的未读消息
                .addProcessor(processor)
                .apply(allContacts);

        return processor.getContactNoneReadMsgCount();
    }

    /**
     * 示例：获取单聊联系人未读消息总数 (替代原有的 getSingleContactNoneReadMsgCount 方法)
     *
     * @param singleContacts 单聊联系人列表
     * @return 单聊未读消息总数
     */
    public static int getSingleContactNoneReadMsgCount(List<ContactBean> singleContacts) {
        UnreadCountStatisticsProcessor processor = createUnreadCountStatisticsProcessor();

        ContactFilterFramework.newBuilder()
                .addFilter(new UnreadMessageFilter()) // 过滤出有未读消息的联系人
                .addProcessor(processor)
                .apply(singleContacts);

        return processor.getTotalUnreadCount();
    }

    /**
     * 示例：获取其他联系人未读消息数量 (替代原有的 getOtherContactNoneReadMsgCount 方法)
     *
     * @param allContacts 所有联系人列表
     * @param excludeFriendUID 要排除的联系人ID
     * @param excludeFriendSource 要排除的联系人来源
     * @return 其他联系人未读消息总数
     */
    public static int getOtherContactNoneReadMsgCount(List<ContactBean> allContacts,
                                                      long excludeFriendUID,
                                                      int excludeFriendSource) {
        UnreadCountStatisticsProcessor processor = createUnreadCountStatisticsProcessor();

        ContactFilterFramework.newBuilder()
                .addFilter(new UnreadCountTypeFilter()) // 过滤出数量类型的未读消息
                .addFilter(contact -> contact.friendId != excludeFriendUID && contact.friendSource != excludeFriendSource) // 排除指定联系人
                .addProcessor(processor)
                .apply(allContacts);

        return processor.getContactNoneReadMsgCount();
    }
} 