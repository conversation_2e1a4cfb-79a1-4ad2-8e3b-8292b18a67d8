package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 只保留发过消息的联系人 (对应 ContactPredicate)
 */
public class ContactSentMessageFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        return contact.isIHaveSendMsgToFriend();
    }

} 