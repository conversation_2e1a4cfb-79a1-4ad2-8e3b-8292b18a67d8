package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 快速处理联系人过滤 (对应 ObtainFastHandleFilter)
 */
public class FastHandleFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        return contact.isCanFastHandler();
    }

} 