package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 非置顶联系人过滤 (对应 RemoveIsTopUserFilter)
 */
public class NonTopContactFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        return !contact.isTop;
    }

} 