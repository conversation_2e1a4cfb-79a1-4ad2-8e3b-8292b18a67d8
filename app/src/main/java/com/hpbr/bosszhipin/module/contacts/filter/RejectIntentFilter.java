package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.main.fragment.manager.RejectDrawContactManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;

import net.bosszhipin.api.RejectShowDrawerResponse;

/**
 * 1226.80【B/C】聊天场景无用信息收纳功能
 * 拒绝意图过滤 (对应 ObtainRejectIntentFilter)
 */
public class RejectIntentFilter implements ContactFilterFramework.ContactFilter {
    private final boolean shouldCheck;

    public RejectIntentFilter() {
        RejectShowDrawerResponse response = RejectDrawContactManager.getInstance().getRejectShowDrawerResponse();
        this.shouldCheck = response != null && response.show && NotifyUtils.isContactRejectDrawer();
    }

    @Override
    public boolean test(ContactBean contact) {
        if (!shouldCheck) {
            return true; // 不需要检查拒绝意图
        }
        return !RejectDrawContactManager.getInstance().isContains(contact);
    }

}