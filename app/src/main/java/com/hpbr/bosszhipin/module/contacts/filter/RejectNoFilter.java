package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 被拒绝的联系人 (对应 BossObtainRejectFilter)
 */
public class RejectNoFilter implements ContactFilterFramework.ContactFilter {

    private final boolean isBossRole;

    public RejectNoFilter() {
        this.isBossRole = UserManager.isBossRole();
    }

    @Override
    public boolean test(ContactBean contact) {
        if (!isBossRole) {
            return true; // 非Boss身份，不需要过滤
        }
        return contact.isReject;
    }
}