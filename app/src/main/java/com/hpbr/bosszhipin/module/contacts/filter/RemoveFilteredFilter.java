package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 获取被过滤的联系人 (对应 RemoveFiltedUserFilter)
 */
public class RemoveFilteredFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        return contact != null && !contact.isFiltered;
    }

} 