package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.contacts.util.ContactUtils;

/**
 * 获取未读数量类型的联系人 (对应 ObtainNoneRTypeCountFilter)
 */
public class UnreadCountTypeFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        if (ContactUtils.isGrayUnRead(contact)) {
            return false;
        }
        return contact.noneReadCount > 0
                && contact.noneReadType == ContactBean.NONE_READ_COUNT;
    }

} 