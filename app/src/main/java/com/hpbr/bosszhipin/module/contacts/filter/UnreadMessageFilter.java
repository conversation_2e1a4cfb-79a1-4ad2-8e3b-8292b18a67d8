package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 未读消息过滤 (对应 ObtainNoneReadFilter)
 */
public class UnreadMessageFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        return contact.noneReadCount > 0;
    }

} 