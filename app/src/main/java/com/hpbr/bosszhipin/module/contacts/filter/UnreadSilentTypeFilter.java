package com.hpbr.bosszhipin.module.contacts.filter;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.contacts.util.ContactUtils;

/**
 * 获取小红点类型的联系人 (对应 ObtainNoneRTypeSilentFilter)
 */
public class UnreadSilentTypeFilter implements ContactFilterFramework.ContactFilter {

    @Override
    public boolean test(ContactBean contact) {
        if (ContactUtils.isGrayUnRead(contact)) {
            return false;
        }
        if (contact.noneReadCount == 0) {
            return false;
        }
        return contact.noneReadType == ContactBean.NONE_READ_SILENT;
    }

} 