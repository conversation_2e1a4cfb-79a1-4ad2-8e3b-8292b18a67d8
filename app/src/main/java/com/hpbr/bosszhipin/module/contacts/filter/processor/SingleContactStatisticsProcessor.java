package com.hpbr.bosszhipin.module.contacts.filter.processor;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.provider.BossJobListProvider;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.contacts.util.ContactUtils;
import com.hpbr.bosszhipin.module.main.entity.JobBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 联系人数据统计
 *
 */
public class SingleContactStatisticsProcessor implements ContactFilterFramework.ContactProcessor {
    private ContactBean latestUnreadContact = null;
    private long latestUnreadTime = 0;
    //    private final List<JobBean> jobBeans = new ArrayList<>();
    private final List<JobBean> jobList = new ArrayList<>(BossJobListProvider.getInstance().getJobListReadOnly());
    private int jobCount;
    private int unReadCount; //未读数
    private int contactCount;

    @Override
    public void process(ContactBean contact) {
        if (!ContactUtils.isSingle(contact)) {
            return;
        }

        // 统计职位数量
        for (JobBean jobBean : jobList) {
            if (contact.jobId > 0 && jobBean.id == contact.jobId) {
                if (jobList.remove(jobBean)) {
                    jobCount++;
                }
                break;
            }
        }

        contactCount++;
        if (contact.noneReadCount > 0) {
            unReadCount++;
            // 找到最近的未读消息联系人
            if (contact.lastChatTime > latestUnreadTime) {
                latestUnreadTime = contact.lastChatTime;
                latestUnreadContact = contact;
            }
        }
    }

    /**
     * 好友未读数
     * @return
     */
    public int getUnReadCount() {
        return unReadCount;
    }

    public int getContactCount() {
        return contactCount;
    }

    /**
     * 检查一周内是否有未读消息
     */
    public boolean isNoneReadMsgTimeMillisOutWeek() {
        if (latestUnreadContact == null) {
            return true; // 没有未读消息
        }

        long preWeekTimeMillis = System.currentTimeMillis() - 1000 * 60 * 60 * 24 * 7;
        return latestUnreadContact.lastChatTime > 0 && latestUnreadContact.lastChatTime < preWeekTimeMillis;
    }

    /**
     * 检查n天内是否有未读消息
     */
    public boolean isNoneReadMsgTimeMillisOutDays(int days) {
        if (latestUnreadContact == null) {
            return true; // 没有未读消息
        }

        long preDaysTimeMillis = System.currentTimeMillis() - 1000L * 60 * 60 * 24 * days;
        return latestUnreadContact.lastChatTime > 0 && latestUnreadContact.lastChatTime < preDaysTimeMillis;
    }

    /**
     * Boss没有未读的被动消息
     * 或者
     * 最近一条未读的被动消息距离当前>48h（只算单聊/不算系统消息/不区分蓝白）
     *
     * @return
     */
    public boolean checkCanRequestPayBanner() {
        //最近一条未读的被动消息距离当前>48h（只算单聊/不算系统消息/不区分蓝白）
        ContactBean recentContact = getLatestUnreadContact();
        if (recentContact != null) {
            return (System.currentTimeMillis() - recentContact.lastChatTime) > 1000 * 60 * 60 * 24 * 2;
        }
        return true;
    }

    /**
     * 获取最后一条未读消息的时间
     */
    public long getLastNoneReadMsgTime() {
        return latestUnreadContact != null ? latestUnreadContact.lastChatTime : 0;
    }

    public ContactBean getLatestUnreadContact() {
        return latestUnreadContact;
    }

    public boolean hasMoreThanTwoContactJob() {
        return jobCount > 1;
    }
}