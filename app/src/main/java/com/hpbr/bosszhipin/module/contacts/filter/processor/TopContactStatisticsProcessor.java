package com.hpbr.bosszhipin.module.contacts.filter.processor;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

/**
 * 置顶联系人统计处理器
 * 替代原有的 getTopCount、getTopNoneReadCount 等方法
 */
public class TopContactStatisticsProcessor implements ContactFilterFramework.ContactProcessor {

    private int topCount = 0;
    private int topUnreadCount = 0;
    private int topUnreadContactCount = 0;

    @Override
    public void process(ContactBean contact) {
        if (contact == null) {
            return;
        }

        if (contact.isTop) {
            // 统计置顶联系人总数
            topCount++;

            // 统计置顶联系人中有未读消息的数量
            if (contact.noneReadCount > 0) {
                topUnreadCount += contact.noneReadCount;
                topUnreadContactCount++;
            }
        }
    }

    /**
     * 获取置顶联系人总数 (替代原 getTopCount 方法)
     */
    public int getTopCount() {
        return topCount;
    }

    /**
     * 获取置顶联系人未读消息总数 (替代原 getTopNoneReadCount 方法)
     */
    public int getTopNoneReadCount() {
        return topUnreadCount;
    }

    /**
     * 获取有未读消息的置顶联系人数量
     */
    public int getTopUnreadContactCount() {
        return topUnreadContactCount;
    }

    /**
     * 重置统计数据
     */
    public void reset() {
        topCount = 0;
        topUnreadCount = 0;
        topUnreadContactCount = 0;
    }
} 