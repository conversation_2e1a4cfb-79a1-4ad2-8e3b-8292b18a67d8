package com.hpbr.bosszhipin.module.contacts.filter.processor;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;
import com.hpbr.bosszhipin.module.contacts.util.ContactUtils;

/**
 * 未读数量统计处理器
 * 用于统计各种类型的未读消息数量
 *
 * 复用原有的 getObtainNoneRTypeCountFilter 逻辑：
 * - 过滤掉灰色未读消息 (ContactUtils.isGrayUnRead)
 * - 只统计 noneReadCount > 0 && noneReadType == NONE_READ_COUNT 的联系人
 *
 * 对应原 F2ContactHelper.getContactNoneReadMsgCount() 方法的逻辑
 */
public class UnreadCountStatisticsProcessor implements ContactFilterFramework.ContactProcessor {

    // 基础统计
    private int totalUnreadCount = 0;
    private int totalContactCount = 0;

    // 未读类型统计 (复用 ObtainNoneRTypeCountFilter 逻辑)
    private int countTypeUnreadCount = 0;  // 对应 getContactNoneReadMsgCount
    private int countTypeContactCount = 0; // 有数量类型未读的联系人数

    // 小红点类型统计 (复用 ObtainNoneRTypeSilentFilter 逻辑)
    private int silentTypeContactCount = 0; // 小红点类型的联系人数

    // 置顶相关统计
    private int topUnreadCount = 0;
    private int topContactCount = 0;

    // 非灰色未读统计 (未被弱化的消息)
    private int normalUnreadCount = 0;
    private int normalUnreadContactCount = 0;

    @Override
    public void process(ContactBean contact) {
        if (contact == null) {
            return;
        }

        // 统计总联系人数
        totalContactCount++;

        // 统计总未读数量
        totalUnreadCount += contact.noneReadCount;

        // 置顶联系人统计
        if (contact.isTop) {
            topContactCount++;
            if (contact.noneReadCount > 0) {
                topUnreadCount += contact.noneReadCount;
            }
        }

        if (contact.noneReadCount > 0) {
            // 判断是否为灰色未读 (一周前且非置顶的消息)
            boolean isGrayUnread = ContactUtils.isGrayUnRead(contact);

            // 统计非灰色未读消息 (正常显示的未读消息)
            if (!isGrayUnread) {
                normalUnreadCount += contact.noneReadCount;
                normalUnreadContactCount++;
            }

            // ===== 复用 ObtainNoneRTypeCountFilter 的逻辑 =====
            // 对应原 getContactNoneReadMsgCount 方法
            if (!isGrayUnread &&
                    contact.noneReadType == ContactBean.NONE_READ_COUNT) {
                countTypeUnreadCount += contact.noneReadCount;
                countTypeContactCount++;
            }

            // ===== 复用 ObtainNoneRTypeSilentFilter 的逻辑 =====
            // 统计小红点类型的联系人数量
            if (!isGrayUnread &&
                    contact.noneReadType == ContactBean.NONE_READ_SILENT) {
                silentTypeContactCount++;
            }

        }
    }

    // ===== 获取统计结果的方法 =====

    /**
     * 获取总未读数量
     */
    public int getTotalUnreadCount() {
        return totalUnreadCount;
    }

    /**
     * 获取总联系人数
     */
    public int getTotalContactCount() {
        return totalContactCount;
    }

    /**
     * 获取置顶联系人未读数量
     */
    public int getTopUnreadCount() {
        return topUnreadCount;
    }

    /**
     * 获取置顶联系人数量
     */
    public int getTopContactCount() {
        return topContactCount;
    }

    /**
     * 获取数量类型未读数量 (对应原 getContactNoneReadMsgCount 方法)
     * 复用 ObtainNoneRTypeCountFilter 的过滤逻辑
     */
    public int getContactNoneReadMsgCount() {
        return countTypeUnreadCount;
    }

    /**
     * 获取有数量类型未读的联系人数量
     */
    public int getCountTypeContactCount() {
        return countTypeContactCount;
    }

    /**
     * 获取小红点类型的联系人数量 (对应原 getContactSilentCount 方法)
     * 复用 ObtainNoneRTypeSilentFilter 的过滤逻辑
     */
    public int getSilentTypeContactCount() {
        return silentTypeContactCount;
    }

    /**
     * 获取非灰色未读数量 (未被弱化的消息)
     */
    public int getNormalUnreadCount() {
        return normalUnreadCount;
    }

    /**
     * 获取有非灰色未读的联系人数量
     */
    public int getNormalUnreadContactCount() {
        return normalUnreadContactCount;
    }


}