package com.hpbr.bosszhipin.module.contacts.filter.transformer;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactSortManager;
import com.hpbr.bosszhipin.module.contacts.ContactFilterFramework;

import java.util.List;

/**
 * 默认排序转换器
 */
public class DefaultSortTransformer implements ContactFilterFramework.ContactTransformer {

    @Override
    public List<ContactBean> transform(List<ContactBean> contacts) {
        return ContactSortManager.executeDefaultSort(contacts);
    }
} 