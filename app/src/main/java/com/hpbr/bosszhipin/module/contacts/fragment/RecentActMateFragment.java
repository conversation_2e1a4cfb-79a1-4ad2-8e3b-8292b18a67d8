package com.hpbr.bosszhipin.module.contacts.fragment;

import static com.hpbr.bosszhipin.module.contacts.util.ChatUtils.REQ_CHOOSE_MATE;

import android.app.Activity;
import android.os.Bundle;
import android.os.Parcelable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.module.contacts.adapter.RecentActMateAdapter;
import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;
import java.util.List;

import zpui.lib.ui.shadow.helper.IZPUILayout;
import zpui.lib.ui.shadow.layout.ZPUILinearLayout;
import zpui.lib.ui.utils.ZPUILayoutHelper;

/**
 * Created by guofeng
 * on 2017/4/28.
 */

public class RecentActMateFragment extends BaseFragment {

    public static final String BUNDLE_MATE_LIST = PackageConfigContants.PACKAGE_NAME + "BUNDLE_MATE_LIST";
    // 近期活跃好友
    protected static final List<CompanyMateBean> recentActMatesList = new ArrayList<>();

    // 点击Item回调接口
    protected OnMateItemClickCallBack onMateItemClickCallBack;

    protected ListView listView;
    protected LinearLayout llEmpty;

    protected View.OnClickListener listener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int i = v.getId();
            if (i == R.id.ll_search) {
                SingleRouter.startChooseMateForResult(activity, REQ_CHOOSE_MATE);
            }
        }
    };

    protected AdapterView.OnItemClickListener onItemClickListener = new AdapterView.OnItemClickListener() {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            CompanyMateBean mateBean = (CompanyMateBean) parent.getItemAtPosition(position);
            if (mateBean == null) {
//                T.ss("数据异常");
                return;
            }
            if (onMateItemClickCallBack != null) {
                onMateItemClickCallBack.onMateListener(mateBean);
            }
        }
    };

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        if (activity instanceof OnMateItemClickCallBack) {
            onMateItemClickCallBack = (
                    OnMateItemClickCallBack) activity;
        }
    }

    public static RecentActMateFragment getInstance(List<CompanyMateBean> allMates) {
        RecentActMateFragment chooseMateFragment = new RecentActMateFragment();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(BUNDLE_MATE_LIST, (ArrayList<? extends Parcelable>) allMates);
        chooseMateFragment.setArguments(bundle);
        return chooseMateFragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return LayoutInflater.from(activity).inflate(R.layout.fragment_recent_act_mate, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        initIntent();
        initAdapter();
    }

    protected void initView(View view) {
        listView = view.findViewById(R.id.listview);
        llEmpty = view.findViewById(R.id.ll_empty);
        listView.setOnItemClickListener(onItemClickListener);

        ZPUILinearLayout llSearch = view.findViewById(R.id.ll_search);
        llSearch.setRadius(Scale.dip2px(activity, 8));
        llSearch.setOnClickListener(listener);
    }

    protected void initIntent() {
        if (getArguments() == null) return;

        List<CompanyMateBean> mateBeanList = getArguments().getParcelableArrayList(BUNDLE_MATE_LIST);
        if (mateBeanList != null) {
            for (CompanyMateBean companyMateBean : mateBeanList) {
                if (companyMateBean.isAssociateFriend) {
                    recentActMatesList.add(companyMateBean);
                }
            }
        }
        View footView = LayoutInflater.from(activity).inflate(R.layout.view_select_mate_footer, null);
        listView.addFooterView(footView);
        MTextView footerTv = footView.findViewById(R.id.footer_tv);
        if (LList.getCount(recentActMatesList) >= 20)
            footerTv.setText("最多展示20位同事，选择更多请使用搜索");
        else
            footerTv.setText("以上是您的全部同事了");
    }

    protected void initAdapter() {
        if (LList.isEmpty(recentActMatesList)) {
            llEmpty.setVisibility(View.VISIBLE);
            return;
        }
        //显示标题,刷新列表
        RecentActMateAdapter adapter = new RecentActMateAdapter(activity);
        listView.setAdapter(adapter);
        adapter.setData(recentActMatesList);
    }

    @Override
    public void onDestroy() {
        recentActMatesList.clear();
        super.onDestroy();
    }

    public interface OnMateItemClickCallBack {
        void onMateListener(CompanyMateBean item);
    }

}
