package com.hpbr.bosszhipin.module.contacts.fragment;

import static com.hpbr.bosszhipin.module.contacts.util.ChatUtils.REQ_CHOOSE_MATE;

import android.os.Bundle;
import android.os.Parcelable;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.module.contacts.entity.CompanyMateBean;

import java.util.ArrayList;
import java.util.List;

/***
 *
 *
 * 原因：https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=58658669
 * 719 改换搜索同事接口
 * 由于完善公司主页信息及工作体验页面中的搜索同事接口变更，
 * 该activity在完善公司主页信息及工作体验中搜索同事时使用
 *
 *
 * change 2022/1/14
 */

public class RecentActMateFragment2 extends RecentActMateFragment {


    public static RecentActMateFragment getInstance(List<CompanyMateBean> allMates) {
        RecentActMateFragment2 chooseMateFragment = new RecentActMateFragment2();
        Bundle bundle = new Bundle();
        bundle.putParcelableArrayList(BUNDLE_MATE_LIST, (ArrayList<? extends Parcelable>) allMates);
        chooseMateFragment.setArguments(bundle);
        return chooseMateFragment;
    }

    protected View.OnClickListener listener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int i = v.getId();
            if (i == R.id.ll_search) {
                SingleRouter.startChooseMate2ForResult(activity, REQ_CHOOSE_MATE);
            }
        }
    };

    @Override
    protected void initView(View view) {
        listView = view.findViewById(R.id.listview);
        llEmpty = view.findViewById(R.id.ll_empty);
        listView.setOnItemClickListener(onItemClickListener);
        view.findViewById(R.id.ll_search).setOnClickListener(RecentActMateFragment2.this.listener);
    }

}
