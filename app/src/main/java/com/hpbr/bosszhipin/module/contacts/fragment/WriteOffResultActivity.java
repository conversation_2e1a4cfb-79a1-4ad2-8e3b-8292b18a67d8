package com.hpbr.bosszhipin.module.contacts.fragment;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.manager.UserManager;

/**
 * 注销申请结果页
 */
public class WriteOffResultActivity extends BaseActivity {

    public static void start(Context context) {
        Intent starter = new Intent(context, WriteOffResultActivity.class);
        if (!(context instanceof Activity)) {
            starter.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        AppUtil.startActivity(context, starter, ActivityAnimType.NONE);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_write_off_result);
        findViewById(R.id.btn_sure).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtil.finishActivity(WriteOffResultActivity.this, ActivityAnimType.NONE);
                // 退出登录
                UserManager.setAccountInvalid(App.getAppContext(), false);
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 禁用返回键
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}