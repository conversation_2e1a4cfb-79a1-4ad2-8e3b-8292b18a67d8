package com.hpbr.bosszhipin.module.contacts.manager;

import com.hpbr.bosszhipin.module.contacts.service.transfer.ChatHistoryTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.ChatTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.ContactTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.IChatHistoryTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.IChatTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.IContactTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.IMqttConnectStatusTransfer;
import com.hpbr.bosszhipin.module.contacts.service.transfer.MqttConnectStatusTransfer;

/**
 * Created by monch on 15/5/25.
 * 消息工厂
 */
public class ChatMessageFactory {

    private ChatMessageFactory(){}

    /**
     * 消息工厂实例
     */
    private static ChatMessageFactory instance = new ChatMessageFactory();

    /**
     * 获取消息工厂唯一实例
     * @return
     */
    public static ChatMessageFactory getInstance() {
        return instance;
    }

    /**
     * 历史消息传输接口实例，默认实现ChatHistoryTransfer
     */
    private IChatHistoryTransfer mChatHistoryTransfer = new ChatHistoryTransfer();

    /**
     * 获取一个历史消息传输接口实例
     * @return
     */
    public IChatHistoryTransfer createChatHistoryTransfer() {
        return mChatHistoryTransfer;
    }

    /**
     * 消息传输接口实例，默认实现ChatTransfer
     */
    private IChatTransfer mChatTransfer = new ChatTransfer();

    /**
     * 获取一个消息传输接口实例
     * @return
     */
    public IChatTransfer createChatTransfer() {
        return mChatTransfer;
    }

    /**
     * MQTT连接状态接口实例，默认实现MqttConnectStatusTransfer
     */
    private IMqttConnectStatusTransfer mMqttConnectStatusTransfer = new MqttConnectStatusTransfer();

    /**
     * 获取一个MQTT连接状态接口实例
     * @return
     */
    public IMqttConnectStatusTransfer createMqttConnectStatusTransfer() {
        return mMqttConnectStatusTransfer;
    }

    /**
     * 联系人消息传输接口实例，默认实现为ContactTransfer
     */
    private IContactTransfer mContactTransfer = new ContactTransfer();

    /**
     * 获取一个联系人消息传输接口实例
     * @return
     */
    public IContactTransfer createContactTransfer() {
        return mContactTransfer;
    }

}
