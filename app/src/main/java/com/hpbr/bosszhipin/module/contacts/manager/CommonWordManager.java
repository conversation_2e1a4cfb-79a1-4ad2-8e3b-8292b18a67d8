package com.hpbr.bosszhipin.module.contacts.manager;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.text.TextUtils;

import androidx.annotation.MainThread;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.module.contacts.entity.NewQuickReplyBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.QuickReplyManager;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;

import java.util.ArrayList;
import java.util.List;

import static android.content.Context.CLIPBOARD_SERVICE;

public final class CommonWordManager {

    /**
     * 聊天缓存内容
     */
    private static final List<CommonWordsIndex> chatWordsCache = new ArrayList<>();

    public static final int MIN_LENGTH = 11;
    public static final int MAX_LENGTH = 200;
    private static CommonWordManager instance;

    public static CommonWordManager getInstance() {
        if (instance == null) {
            instance = new CommonWordManager();
        }
        return instance;
    }


    /**
     * 记录复制版上面内容
     */
    private final List<String> copyBoardText = new ArrayList<>();


    private interface OnLoadClipboardCallBack {
        @MainThread
        void onClipboardTextListener(String text);
    }

    /**
     * 解决小米手机在获得剪切板时候出现anr
     *
     * @param onLoadClipboardCallBack
     */
    private void checkIsClipboardText(OnLoadClipboardCallBack onLoadClipboardCallBack) {
        try {
            AppThreadFactory.POOL.execute(() -> {
                //发送内容是复制版上的内容，直接提示添加常用语灰条
                ClipboardManager cm = (ClipboardManager) App.get().getSystemService(CLIPBOARD_SERVICE);
                ClipData data = cm.getPrimaryClip();
                if (data != null) {
                    ClipData.Item item = data.getItemAt(0);
                    if (item != null) {
                        CharSequence clipText = item.getText();
                        if (clipText != null) {
                            String content = clipText.toString();

                            App.get().getMainHandler().post(() -> {
                                if (onLoadClipboardCallBack != null) {
                                    onLoadClipboardCallBack.onClipboardTextListener(content);
                                }
                            });

                        }
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加到聊天缓存
     *
     * @param text
     */
    public void addToCache(String text, OnSendAddChatCommonCallback callback) {
        try {
            if (TextUtils.isEmpty(text)) return;
            //常用语 最大长度400&&最小长度10
            if (Math.ceil((double) StringUtil.getChineseCount(text) / 2d) > MAX_LENGTH || Math.ceil((double) StringUtil.getChineseCount(text) / 2d) < MIN_LENGTH)
                return;
            //常用语 已经包含
            if (checkWeatherHasSet(text)) return;

            checkIsClipboardText(content -> {
                if (LText.equal(text, content) && !copyBoardText.contains(text)) {
                    copyBoardText.add(text);
                    callback.onSendAddChatCommonListener(true);
                    return;
                }

                int count = chatWordsCache.size();
                for (int i = 0; i < count; i++) {
                    CommonWordsIndex item = chatWordsCache.get(i);
                    if (item.text.equals(text)) {
                        item.count++;
                        if (item.count == VersionAndDatasCommon.getInstance().chatCommonMaxSameWords() && callback != null) {
                            callback.onSendAddChatCommonListener(false);
                        }
                        //把数据最佳到队列末尾
                        chatWordsCache.add(chatWordsCache.remove(i));
                        return;
                    }
                }

                //超过最大缓存容量
                if (chatWordsCache.size() >= VersionAndDatasCommon.getInstance().chatCacheCommonWordMax()) {
                    //删除第一个元素
                    chatWordsCache.remove(0);
                }

                //添加到缓存
                chatWordsCache.add(obtain(text));

            });


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //清除缓存
    public void clear() {
        chatWordsCache.clear();
    }

    public interface OnSendAddChatCommonCallback {
        void onSendAddChatCommonListener(boolean isClipBoard);
    }


    /**
     * 判断当前 内容是否已经设置为常用语
     *
     * @param text
     * @return
     */
    public boolean checkWeatherHasSet(String text) {
        try {
            List<NewQuickReplyBean> quickList = QuickReplyManager.getCommonList();
            if (quickList != null) {

                List<NewQuickReplyBean> result = new ArrayList<>(quickList);

                for (NewQuickReplyBean item : result) {
                    if (item == null) continue;
                    if (LText.equal(item.content, text)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    private CommonWordsIndex obtain(String text) {
        return new CommonWordsIndex(text, 1);
    }


    private static class CommonWordsIndex {
        private String text;
        private int count;

        public CommonWordsIndex(String text, int count) {
            this.text = text;
            this.count = count;
        }
    }

}
