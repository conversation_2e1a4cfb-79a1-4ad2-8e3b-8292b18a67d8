package com.hpbr.bosszhipin.module.contacts.manager;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.AppActivityLifecycleCallbacks;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.chat.export.router.SingleRouter;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.DialogCommonHandler;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.hpbr.bosszhipin.views.chat.ProgressLoading;
import com.monch.lbase.activity.LActivity;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.config.RequestMethod;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.BusinessUrlConfig;
import net.bosszhipin.api.ContactKeyCheckReleaseRespone;
import net.bosszhipin.api.ContactKeyCheckRespone;
import net.bosszhipin.api.ExposerPreCheckResponse;
import net.bosszhipin.api.bean.BlockCheckBean;
import net.bosszhipin.api.bean.BlockCheckZpDataBean;
import net.bosszhipin.api.bean.ExposeButtonBean;
import net.bosszhipin.api.bean.ExposerPreCheckBean;
import net.bosszhipin.api.bean.HUnterCallChatLimitParams;
import net.bosszhipin.api.bean.HunterCallChatLimitBean;
import net.bosszhipin.api.bean.ServerDialogBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * create by guofeng
 * date on 2021/10/28
 * <p>
 * 沟通钥匙管理类
 */
public class ContactKeyManager {


    private ContactKeyManager() {
        addRegisterLifeCycle();
    }


    private static final ContactKeyManager instance = new ContactKeyManager();


    public static ContactKeyManager getInstance() {
        return instance;
    }


    public static class ContactKeyParams extends BaseEntity {

        private static final long serialVersionUID = 2382286676604511591L;

        public String geekId;
        public String expectId;
        public String jobId;
        public String geekSource;
        public String bgSource;
        public String securityId;

        public String extraParams;


        public ContactKeyParams(String geekId, String expectId, String jobId, String geekSource, String bgSource, String securityId) {
            this.geekId = geekId;
            this.expectId = expectId;
            this.jobId = jobId;
            this.geekSource = geekSource;
            this.bgSource = bgSource;
            this.securityId = securityId;
        }
    }


    /**
     * 检测是否有沟通钥匙
     */
    private void check(@NonNull ContactKeyParams params, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {

        if (UserManager.isBossRole()) {
            Activity topActivity = ForegroundUtils.get().getTopActivity();
            if (topActivity == null) return;

            SimpleApiRequest.POST(BusinessUrlConfig.URL_ZPBLOCK_CHECK)
                    .addParam("geekId", params.geekId)
                    .addParam("jobId", params.jobId)
                    .addParam("geekSource", params.geekSource)
                    .addParam("bgSource", params.bgSource)
                    .addParam("securityId", params.securityId)
                    .setRequestCallback(new ApiRequestCallback<ContactKeyCheckRespone>() {

                        @Override
                        public void onStart() {
                            super.onStart();
                            if (ActivityUtils.isValid(topActivity)) {
                                if (topActivity instanceof LActivity) {
                                    ((LActivity) topActivity).showProgressDialogNoFocus();
                                }
                            }
                        }

                        @Override
                        public void onSuccess(ApiData<ContactKeyCheckRespone> data) {
                            ContactKeyCheckRespone resp = data.resp;
                            BlockCheckBean blockCheck = resp.blockCheck;
                            HunterCallChatLimitBean hunterCallChatLimit = resp.hunterCallChatLimit;
                            handleResult(false, params, blockCheck, hunterCallChatLimit, onContactKeyCheckCallBack);

                        }

                        @Override
                        public void onComplete() {
                            if (ActivityUtils.isValid(topActivity)) {
                                if (topActivity instanceof LActivity) {
                                    ((LActivity) topActivity).dismissProgressDialog();
                                }
                            }
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            ToastUtils.showText(reason.getErrReason());
                        }
                    }).execute();
        } else {
            onContactKeyCheckCallBack.onCheckNoBlockListener();
        }
    }

    /**
     * 检测&消耗沟通钥匙
     */
    private void checkAndRelease(@NonNull ContactKeyParams params, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {

        if (UserManager.isBossRole()) {

            if (isNeedCheck(params)) {
                Activity topActivity = ForegroundUtils.get().getTopActivity();
                if (topActivity == null) return;
                SimpleApiRequest checkRequest = new SimpleApiRequest(BusinessUrlConfig.URL_ZPBLOCK_CHECK_AND_RELEASE, RequestMethod.POST);

                checkRequest.setRequestCallback(new ApiRequestCallback<ContactKeyCheckReleaseRespone>() {

                    @Override
                    public void onStart() {
                        super.onStart();
                        if (ActivityUtils.isValid(topActivity)) {
                            if (topActivity instanceof LActivity) {
                                ((LActivity) topActivity).showProgressDialogNoFocus();
                            }
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<ContactKeyCheckReleaseRespone> data) {
                        ContactKeyCheckReleaseRespone resp = data.resp;
                        HunterCallChatLimitBean hunterCallChatLimit = resp.hunterCallChatLimit;
                        BlockCheckBean blockCheckAndRelease = resp.blockCheckAndRelease;
                        handleResult(true, params, blockCheckAndRelease, hunterCallChatLimit, onContactKeyCheckCallBack);


                    }

                    @Override
                    public void onComplete() {
                        if (ActivityUtils.isValid(topActivity)) {
                            if (topActivity instanceof LActivity) {
                                ((LActivity) topActivity).dismissProgressDialog();
                            }
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        ToastUtils.showText(reason.getErrReason());
                    }
                });
                checkRequest.addParam("geekId", params.geekId);
                checkRequest.addParam("expectId", params.expectId);
                checkRequest.addParam("jobId", params.jobId);
                checkRequest.addParam("geekSource", params.geekSource);
                checkRequest.addParam("bgSource", params.bgSource);
                checkRequest.addParam("securityId", params.securityId);
                checkRequest.addParam("extraParams", params.extraParams);
                HttpExecutor.execute(checkRequest);
            } else {
                onContactKeyCheckCallBack.onCheckNoBlockListener();
            }

        } else {
            onContactKeyCheckCallBack.onCheckNoBlockListener();
        }

    }


    //https://wiki.kanzhun-inc.com/pages/viewpage.action?pageId=187609692
    //停止曝光的行业限定职位，如果账号内有职位权益、沟通权益，使用沟通权益回复牛人时，弹框提示是否解锁曝光
    private void exposePreCheck(Activity activity,
                                String jobId,
                                String securityId,
                                OnExposerPreCheckCallBack callBack) {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity == null) return;

        final ProgressLoading progressDialog = new ProgressLoading(topActivity);

        SimpleApiRequest get = SimpleApiRequest.GET(BusinessUrlConfig.URL_ZPBLOCK_CHAT_EXPOSURE_PRE_CHECK);
        get.setRequestCallback(new ApiRequestCallback<ExposerPreCheckResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                progressDialog.show();
            }

            @Override
            public void onSuccess(ApiData<ExposerPreCheckResponse> data) {
                ExposerPreCheckResponse resp = data.resp;
                ExposerPreCheckBean dialog = resp.dialog;
                if (dialog != null) {
                    String title = dialog.title;
                    String content = dialog.content;
                    List<ExposeButtonBean> buttonList = dialog.buttonList;
                    int count = LList.getCount(buttonList);

                    if (!LText.empty(title)
                            && !LText.empty(content)
                            && count > 0) {

                        if (count == 1) {
                            ExposeButtonBean element = LList.getElement(buttonList, 0);
                            new DialogUtils.Builder(activity)
                                    .setTitle(title)
                                    .setDesc(content)
                                    .setPositiveAction(element != null ? element.text : "", new OnClickNoFastListener() {
                                        @Override
                                        public void onNoFastClick(View v) {
                                            if (callBack != null) {
                                                callBack.onCheckComplete(element != null ? element.params : "");
                                            }
                                        }
                                    })
                                    .setSingleButton()
                                    .build()
                                    .show();
                        } else {
                            ExposeButtonBean leftButton = LList.getElement(buttonList, 0);
                            ExposeButtonBean rightButton = LList.getElement(buttonList, 1);
                            new DialogUtils.Builder(activity)
                                    .setTitle(title)
                                    .setDesc(content)
                                    .setPositiveAction(rightButton != null ? rightButton.text : "", new OnClickNoFastListener() {
                                        @Override
                                        public void onNoFastClick(View v) {
                                            if (callBack != null) {
                                                callBack.onCheckComplete(rightButton != null ? rightButton.params : "");
                                            }
                                        }
                                    })
                                    .setNegativeAction(leftButton != null ? leftButton.text : "", new OnClickNoFastListener() {
                                        @Override
                                        public void onNoFastClick(View v) {
                                            if (callBack != null) {
                                                callBack.onCheckComplete(leftButton != null ? leftButton.params : "");
                                            }
                                        }
                                    })
                                    .setDoubleButton()
                                    .build()
                                    .show();
                        }

                    } else {
                        if (callBack != null) {
                            callBack.onCheckComplete("");
                        }
                    }
                } else {
                    if (callBack != null) {
                        callBack.onCheckComplete("");
                    }
                }

            }

            @Override
            public void onComplete() {
                if (ActivityUtils.isValid(topActivity)) {
                    progressDialog.dismiss();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                if (callBack != null) {
                    callBack.onCheckComplete("");
                }
            }
        });
        get.addParam("jobId", jobId);
        get.addParam("securityId", securityId);
        HttpExecutor.execute(get);
    }

    public interface OnExposerPreCheckCallBack {

        void onCheckComplete(String param);
    }


    /**
     * 添加监控
     */
    private void addRegisterLifeCycle() {

        App.get().registerActivityLifecycleCallbacks(new AppActivityLifecycleCallbacks(App.get()) {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                super.onActivityCreated(activity, savedInstanceState);
                if (SingleRouter.checkIsChatActivityBossUser(activity)
                        || SingleRouter.checkIsChatActivityDZUser(activity)) {
                    //清空缓存数据
                    whiteName.clear();
                }
            }
        });

    }

    /**
     * 第一次接口检测好友信息不阻断,把好友信息放到集合之后
     */
    private final Set<String> whiteName = new HashSet<>();


    /**
     * 获得数据的唯一KEY
     *
     * @param params
     * @return
     */
    private String getParamsKey(@NonNull ContactKeyParams params) {
        return params.geekId + "_" + params.geekSource;
    }

    public void clear() {
        whiteName.clear();
    }

    /**
     * 是否需要检测好友是否需要阻断
     *
     * @param params
     * @return
     */
    private boolean isNeedCheck(@NonNull ContactKeyParams params) {
        String key = getParamsKey(params);
        return !whiteName.contains(key);
    }

    /**
     * 处理接口返回数据
     *
     * @param
     */
    private void handleResult(boolean isCheckAndRelease, @NonNull ContactKeyParams params,
                              @Nullable BlockCheckBean blockCheckBean,
                              @Nullable HunterCallChatLimitBean hunterCallChatLimitBean,
                              @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {


        boolean isLimit = false;

        if (hunterCallChatLimitBean != null) {
            HUnterCallChatLimitParams zpData = hunterCallChatLimitBean.zpData;
            if (zpData != null && hunterCallChatLimitBean.code == 0) {
                int canSendMsg = zpData.canSendMsg;
                String tip = zpData.tip;
                isLimit = zpData.limit;
                if (canSendMsg != 1 && !LText.empty(tip)) {
                    ToastUtils.showText(tip);
                    return;
                }
            }
        }

        if (blockCheckBean != null) {
            int code = blockCheckBean.code;
            BlockCheckZpDataBean zpData = blockCheckBean.zpData;
            if (code == 0 && zpData != null) {

                String jumpUrl = zpData.jumpUrl;
                int status = zpData.status;
                String tip = zpData.tip;

                switch (status) {
                    case 0:
                        //无阻断
                        onContactKeyCheckCallBack.onCheckNoBlockListener();
                        //记录下来该好友不需要阻断
                        String key = getParamsKey(params);
                        //checkAndRelease接口需要添加到白名单
                        if (!isLimit && isCheckAndRelease) {
                            whiteName.add(key);
                        }
                        break;
                    case 1:
                        // 阻断到购买页
                        Activity topActivity = ForegroundUtils.get().getTopActivity();
                        if (!LText.empty(jumpUrl) && topActivity != null) {

                            //跳转到商业购买
                            new ZPManager(topActivity, jumpUrl).handler();

                            //购买成功后出现弹窗提示
                            LiveBus.with(ChannelConstants.CHAT_KEY_PURCHASE_SUCCESS,
                                    ServerDialogBean.class).observe((LifecycleOwner) topActivity, dialog -> {

                                if (dialog != null) {
                                    DialogCommonHandler handler = new DialogCommonHandler();
                                    handler.handle(topActivity, dialog, new DialogCommonHandler.OnActionTypeHandleListener() {

                                        @Override
                                        public void onActionType21() {

                                        }

                                        @Override
                                        public void onActionType22(String url) {
                                            if (ActivityUtils.isValid(topActivity)) { //购买成功，准备激活
                                                new ZPManager(topActivity, url).handler();
                                            }
                                        }
                                    });
                                }


                            });

                        }
                        onContactKeyCheckCallBack.onCheckBlockUrlListener(jumpUrl);
                        break;
                    case 2:
                        //阻断TOAST
                        ToastUtils.showText(tip);
                        onContactKeyCheckCallBack.onCheckBlockToastListener(tip);
                        break;
                }

            }

        }


    }

    /**
     * 检测沟通钥匙回掉事件
     */
    public interface OnContactKeyCheckCallBack {

        //检测有阻断Toast
        void onCheckBlockToastListener(String msg);

        //检测有阻断跳转到购买url
        void onCheckBlockUrlListener(String url);

        //检测没有阻断事件
        void onCheckNoBlockListener();

    }


    public static class OnContactKeyCheckCallBack2 implements OnContactKeyCheckCallBack {

        @Override
        public void onCheckBlockToastListener(String msg) {

        }

        @Override
        public void onCheckBlockUrlListener(String url) {

        }

        @Override
        public void onCheckNoBlockListener() {

        }
    }


    /**
     * 输入框消息
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onInputSendMsgListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        //只处理BOSS身份
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.INPUT_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }

    /**
     * 输入框消息
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void bossQuickReplay(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        //只处理BOSS身份
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.new_GREETING_FAST_REPLAY, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }

    /**
     * 输入框上方气泡消息（开聊语气泡+NLP识别气泡）
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onNlpClickListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        //只处理BOSS身份
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.NLP_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }

    /**
     * 表情消息
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onEmotionSendListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        //只处理BOSS身份
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.GIF_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }


    public void onPhotoSendListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        //只处理BOSS身份
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.PHOTO_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }


    /**
     * 检测接口
     * <p>
     * + 图片/语音/更换职位/发送地址
     * + 短信通知————单独处理，点击icon先判断是否有沟通钥匙，直接出弹窗
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     * @param bgParams
     */
    public void onCheckListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack, String bgParams) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), bgParams, contactBean.securityId);
        check(contactKeyParams, onContactKeyCheckCallBack);
    }


    /**
     * 发送图片
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onSendPhoneListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.PHOTO_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }


    /**
     * 发送语音
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onSendAudioListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.AUDIO_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }

    /**
     * 更换职位
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onChangePositionListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), ContactKeyManager.BgSource.CHANGE_JOB_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }

    /**
     * 发送地址
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onSendLocationListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), BgSource.SEND_LOCATION_SOURCE, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }


    /**
     * 用户点击发送失败的消息
     *
     * @param contactBean
     * @param onContactKeyCheckCallBack
     */
    public void onSendFailedMessageListener(ContactBean contactBean, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (ActivityUtils.isValid(topActivity)) {

            if (UserManager.isBossRole()) {
                exposePreCheck(topActivity, String.valueOf(contactBean.jobId), contactBean.securityId, new OnExposerPreCheckCallBack() {
                    @Override
                    public void onCheckComplete(String param) {

                        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId),
                                String.valueOf(contactBean.friendSource), BgSource.FAILED_SOURCE, contactBean.securityId);
                        contactKeyParams.extraParams = param;


                        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
                    }
                });
            } else {
                onContactKeyCheckCallBack.onCheckNoBlockListener();
            }
        }

    }

    /**
     * 发送消息，传入来源
     */
    public void onSendMessageWithSourceListener(ContactBean contactBean, String bgSource, @NonNull OnContactKeyCheckCallBack onContactKeyCheckCallBack) {
        if (contactBean == null) return;
        ContactKeyParams contactKeyParams = new ContactKeyParams(String.valueOf(contactBean.friendId)
                , String.valueOf(contactBean.jobIntentId), String.valueOf(contactBean.jobId), String.valueOf(contactBean.friendSource), bgSource, contactBean.securityId);
        checkAndRelease(contactKeyParams, onContactKeyCheckCallBack);
    }


    // TODO: 2021/10/29

    /**
     * 1.输入框消息  onInputSendMsgListener
     * 2.点击自定义或系统表情 ChatNewActivity#onGifClick
     * 3.NLP点击添加 onNlpClickListener
     * 4.不合适 onUnfitCheckAndRelease
     * 5.+ 图片————触发购买 ChatNewActivity#onCameraClickListener#onGalleryClickListener
     * 6.+ 语音————触发购买  ChatNewActivity#onAudioClickListener
     * 7.+ 更换职位————触发购买 ChatNewActivity#TYPE_CHANGE_JOB
     * 8.+ 发送地址————触发购买 ChatNewActivity#TYPE_SEND_LOCATION
     * 9.+ 短信通知————触发购买 ChatNewActivity#TYPE_SMS_NOTIFY
     * 10 发送失败的消息 ChatCommon#onClickSendFailViewListener
     * 11.主动发起求简历————触发购买 BossExchangeViewLayout#OnBossCallBackImp#onClickExchangeResumeListener
     * 主动发起换电话/微信————触发购买  BossExchangeViewLayout#OnBossCallBackImp#onClickExchangeWeiXinListener
     * 主动发起面试TA————触发购买  BossExchangeViewLayout#OnBossCallBackImp#onClickExchangeInterviewListener
     * 12.点击牛人发起的交换卡片（电话/微信/简历-同意/拒绝）————触发购买  OnDialogViewButtonClickListener
     */


    /**
     * 埋点用来源
     * 1. 输入框消息
     * 3. 输入框上方气泡消息(开聊语气泡+NLP识别气泡)
     * 4. 表情消息(非emoji)
     * 5. 不合适消息
     * 6. 发送图片
     * 7. 发送语音
     * 8. 更换职位
     * 9. 发送地址
     * 10. 短信通知
     * 11. 点击牛人发起的交换卡片
     * 12. 聊天页面上方按钮
     * 13. 点击发送失败的icon
     * 17. 灰条协议 VIP特权
     * 18. 更多点击IP特权
     */
    public interface BgSource {
        String INPUT_SOURCE = "1";
        String NLP_SOURCE = "3";
        String GIF_SOURCE = "4";
        String UNFIT_SOURCE = "5";
        String PHOTO_SOURCE = "6";
        String AUDIO_SOURCE = "7";
        String CHANGE_JOB_SOURCE = "8";
        String SEND_LOCATION_SOURCE = "9";
        String MESSAGE_NOTICE_SOURCE = "10";
        String DIALOG_SOURCE = "11";
        String EXCHANGE_SOURCE = "12";
        String FAILED_SOURCE = "13";
        String STAR_SOURCE = "14";
        String GRAY_URL = "16";
        String MORE_VIP = "17";
        String new_GREETING_FAST_REPLAY = "18";
        String new_GREETING_UNFIT = "19";

        String BOSS_RECRUIT_SOURCE = "20";

        String BOSS_QUICK_REPLY = "21"; //1301.110 蓝领B被开聊快捷回复功能优化v2
        String CHAT_HELPER_RESTART = "26"; //1309.165【招聘者、PC同步】AI沟通助手逻辑综合优化


        String NONE = "0";
    }
}