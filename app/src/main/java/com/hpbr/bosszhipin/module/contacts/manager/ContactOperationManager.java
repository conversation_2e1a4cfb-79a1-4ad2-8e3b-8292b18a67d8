package com.hpbr.bosszhipin.module.contacts.manager;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_FRIEND_DELETE;

import android.content.Context;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.dialog.MultiItemDialog;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.common.ChatGroupF2LongClickManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.DeleteContactRequest;
import net.bosszhipin.api.SetGeekFitFilterRequest;
import net.bosszhipin.api.SetTopContactRequest;
import net.bosszhipin.api.SetTopContactResponse;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.HttpResponse;

import message.handler.dao.MessageDaoFactory;


public class ContactOperationManager {

    private RefreshDataCallback callback;

    public static final int DELETE = 1;
    public static final int FOLLOW = 0;
    public static final int SET_FIT_VIP = 2;//筛选不符合要求牛人 设置为符合要求
    public static final int SET_GEEK_FIT = 3;//取消牛人不合适
    public static final int SET_GEEK_STAR = 4;//设置牛人星标

    public ContactOperationManager(RefreshDataCallback callback) {
        this.callback = callback;
    }

    public void showDialog(Context context, final ContactBean bean) {
        if (bean == null) return;
        if (bean.isGroup()) {//群聊
            new ChatGroupF2LongClickManager(context, callback).showDialog(bean);
        } else {//单聊
            String[] items = new String[]{bean.isTop ? context.getString(R.string.contact_cancel_top) :
                    context.getString(R.string.contact_top), "删除好友"};
            MultiItemDialog d = new MultiItemDialog(context);
            d.show(items, new OnADialogItemClickListener(context, this, bean, callback));
        }
    }

    public void showDialogVip(Context context, final ContactBean bean) {
        if (bean == null) return;
        String[] items = new String[]{"设为符合", "删除"};
        MultiItemDialog d = new MultiItemDialog(context);
        d.show(items, new OnVipInformityDialogItemClickListener(context, this, bean, callback));
    }

    public void showDialogHighGeek(Context context, final ContactBean bean) {
        if (bean == null) return;
        String[] items = new String[]{"删除"};
        MultiItemDialog d = new MultiItemDialog(context);
        d.show(items, new OnHighGeekDialogItemClickListener(context, this, bean, callback));
    }


    public void deleteFriend(final Context context, final ContactBean bean, int type) {


        if (bean == null || bean.friendId <= 0) {
            T.ss("删除好友失败，请稍后再试");
            return;
        }
        AnalyticsFactory.create().action(ACTION_FRIEND_DELETE).param("p2", "0").
                param("p", "" + bean.friendId).
                param("p3", type + "")
                .buildSync();

        callback.showProgress("正在删除");
        DeleteContactRequest request = new DeleteContactRequest(new ApiRequestCallback<SuccessResponse>() {
            @Override
            public void handleInChildThread(ApiData<SuccessResponse> data) {
                TLog.info("chat", "====delete Friend: %s", bean.friendId);
                ContactManager.getInstance().deleteContact(bean);
                MessageDaoFactory.getMessageDao().removeChatList(bean.myId, bean.myRole, bean.friendId, bean.friendSource);
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
                callback.onRefreshData(DELETE);
                T.ss("删除成功");
                ContactManager.getInstance().refreshContacts();
            }

            @Override
            public void onComplete() {
                callback.dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.friendId = bean.friendId + "";
        request.friendSource = bean.friendSource;
        HttpExecutor.execute(request);
    }

    private static class OnADialogItemClickListener implements View.OnClickListener {

        private Context context;
        private ContactOperationManager manager;
        private ContactBean bean;
        private RefreshDataCallback callback;

        private OnADialogItemClickListener(Context context, ContactOperationManager manager, ContactBean bean, RefreshDataCallback callback) {
            this.context = context;
            this.manager = manager;
            this.bean = bean;
            this.callback = callback;
        }

        @Override
        public void onClick(View v) {
            int index = (Integer) v.getTag();
            if (callback != null) {
                callback.onClick(index);
            }
            switch (index) {
                case 0:
                    if (bean.isTop) {
                        // 这里为取消置顶
                        manager.removeTopFriend(bean);
                    } else {
                        // 这里是置顶聊天
                        manager.addTopFriend(context, bean);
                    }
                    break;
                case 1:
                    manager.deleteFriend(context, bean, 0);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 置顶好友
     *
     * @param bean
     */
    public void addTopFriend(final Context context, final ContactBean bean) {
        if (bean == null) return;

        int count = LList.getCount(F2ContactHelper.getInstance().getF2TopList(true));

        if (count >= 60) {
//        if (LList.getCount(F2ContactDataManager.getInstance().getExpandTopContactList()) >= 60) {
            T.ss("您置顶的人数已达60人上限，请先取消置顶");
            return;
        }
        callback.showProgress("正在置顶");
        SetTopContactRequest setTopContactRequest = new SetTopContactRequest(new ApiRequestCallback<SetTopContactResponse>() {
            @Override
            public void onSuccess(ApiData<SetTopContactResponse> data) {
                T.ss("置顶成功");
                bean.isTop = true;
                ContactManager.getInstance().updateTopFriend(bean);
                callback.onRefreshData(FOLLOW);
            }

            @Override
            public void onComplete() {
                callback.dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        setTopContactRequest.friendId = String.valueOf(bean.friendId);
        setTopContactRequest.isTop = "1";
        setTopContactRequest.friendSource = bean.friendSource;
        setTopContactRequest.securityId = bean.securityId;
        HttpExecutor.execute(setTopContactRequest);
    }

    /**
     * 取消置顶好友
     *
     * @param bean
     */
    public void removeTopFriend(final ContactBean bean) {
        if (bean == null) return;

        callback.showProgress("正在取消置顶");
        SetTopContactRequest setTopContactRequest = new SetTopContactRequest(new ApiRequestCallback<SetTopContactResponse>() {
            @Override
            public void onSuccess(ApiData<SetTopContactResponse> data) {
                T.ss("取消置顶成功");
                bean.isTop = false;
                ContactManager.getInstance().updateTopFriend(bean);
                callback.onRefreshData(FOLLOW);
            }

            @Override
            public void onComplete() {
                callback.dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        setTopContactRequest.friendId = String.valueOf(bean.friendId);
        setTopContactRequest.isTop = "0";
        setTopContactRequest.friendSource = bean.friendSource;
        setTopContactRequest.securityId = bean.securityId;
        HttpExecutor.execute(setTopContactRequest);
    }

    //Vip筛选不符合要求牛人或者Boss长按弹框
    private static class OnVipInformityDialogItemClickListener implements View.OnClickListener {

        private Context context;
        private ContactOperationManager manager;
        private ContactBean bean;
        private RefreshDataCallback callback;

        private OnVipInformityDialogItemClickListener(Context context, ContactOperationManager manager, ContactBean bean, RefreshDataCallback callback) {
            this.context = context;
            this.manager = manager;
            this.bean = bean;
            this.callback = callback;
        }

        @Override
        public void onClick(View v) {
            int index = (Integer) v.getTag();
            if (callback != null) {
                callback.onClick(index);
            }
            switch (index) {
                case 0:
                    manager.setVipGeekFit(context, bean);
                    break;
                case 1:
                    manager.deleteFriend(context, bean, 0);
                    break;
                default:
                    break;
            }
        }
    }


    private static class OnHighGeekDialogItemClickListener implements View.OnClickListener {

        private Context context;
        private ContactOperationManager manager;
        private ContactBean bean;
        private RefreshDataCallback callback;

        private OnHighGeekDialogItemClickListener(Context context, ContactOperationManager manager, ContactBean bean, RefreshDataCallback callback) {
            this.context = context;
            this.manager = manager;
            this.bean = bean;
            this.callback = callback;
        }

        @Override
        public void onClick(View v) {
            int index = (Integer) v.getTag();
            if (callback != null) {
                callback.onClick(index);
            }
            switch (index) {
                case 0:
                    manager.deleteFriend(context, bean, 0);
                    break;
                default:
                    break;
            }
        }
    }

    public void setVipGeekFit(final Context context, final ContactBean bean) {


        if (bean == null || bean.friendId <= 0) {
            T.ss("设为符合失败，请稍后再试");
            return;
        }
        callback.showProgress("正在设置");
        SetGeekFitFilterRequest request = new SetGeekFitFilterRequest(new ApiRequestCallback<HttpResponse>() {
            @Override
            public void handleInChildThread(ApiData<HttpResponse> data) {
                super.handleInChildThread(data);
                bean.isFiltered = false;
                ContactManager.getInstance().updateIsFiltered(bean);
            }

            @Override
            public void onSuccess(ApiData<HttpResponse> data) {
                callback.onRefreshData(SET_FIT_VIP);
                T.ss("设置成功");
                ContactManager.getInstance().refreshContacts();
            }

            @Override
            public void onComplete() {
                callback.dismissProgress();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.friendId = bean.friendId + "";
        HttpExecutor.execute(request);
    }


    public interface RefreshDataCallback {

        default void onClick(int position) {
        }

        default void showProgress(String text) {
        }

        default void dismissProgress() {
        }

        /**
         * 0 关注 1 删除 2设置符合条件牛人
         *
         * @param type
         */
        default void onRefreshData(int type) {
        }

        /**
         * 删除群聊
         *
         * @param groupId
         */
        default void onDeleteGroupListener(long groupId) {

        }

        ;
    }

}
