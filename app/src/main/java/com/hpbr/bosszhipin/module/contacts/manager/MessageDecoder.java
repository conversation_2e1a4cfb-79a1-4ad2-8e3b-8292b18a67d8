package com.hpbr.bosszhipin.module.contacts.manager;

import android.text.TextUtils;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.HighLight;
import com.hpbr.bosszhipin.module.contacts.entity.HistoryChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.InterviewShare;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatArticleBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatAtBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatCompanyDescBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatCustomerFlingList;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDataSync;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDataSyncProfile;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogButtonBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatFrameBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGifImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGradeCardBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGradeItemBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGroupAntelopeInfoCardBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGroupSync;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatHyperLinkBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatIQResponseBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatInterviewBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatJobBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatJobShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageReadBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageSyncBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatNotifyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatOrderBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatOrderDetailItemBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatProtocol;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatRedEnvelopeBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatResumeBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatResumeShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserExperience;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserInfoModel;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatVideoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ReplyItemListBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by monch on 15/5/1.
 * 解码
 */
public class MessageDecoder {

    private static MessageDecoder instance = new MessageDecoder();

    private MessageDecoder() {
    }

    public synchronized static MessageDecoder getInstance() {
        return instance;
    }

    /**
     * 将ChatProtocoo解码为ChatBean数组
     *
     * @param protocol
     * @return
     */
    public LinkedList<ChatBean> builderMessage(byte[] bytes) throws InvalidProtocolBufferException {
        ChatProtocol.TechwolfChatProtocol protocol = ChatProtocol.TechwolfChatProtocol.parseFrom(bytes);
        return MessageDecoder.getInstance().builderMessage(protocol);
    }

    /**
     * 将ChatProtocoo解码为ChatBean数组
     *
     * @param protocol
     * @return
     */
    public LinkedList<ChatBean> builderMessage(ChatProtocol.TechwolfChatProtocol protocol) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_MESSAGE) return null;
        LinkedList<ChatBean> list = new LinkedList<>();
        int count = protocol.getMessagesCount();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfMessage message = protocol.getMessages(i);
            if (message == null) continue;
            ChatBean bean = new ChatBean();
            bean.msgType = MqttConfig.CHAT_TYPE_MESSAGE;
            bean.version = protocol.getVersion();
            bean.message = changeChatMessageBean(message);
            bean.messageSendTime = 0;
            bean.status = 1;
            if (bean.message == null) continue;
            bean.msgId = bean.message.id;
            bean.sortMsgId = bean.message.id;
            bean.time = bean.message.time;
            bean.clientTempMessageId = bean.message.clientTempMessageId;
            bean.domain = protocol.getDomain();
            list.add(bean);
        }
        return list;
    }

    /**
     * 将ChatProtocoo解码为ChatBean数组
     *
     * @param protocol
     * @return
     */
    public LinkedList<HistoryChatBean> builderHistoryMessage(ChatProtocol.TechwolfChatProtocol protocol) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_MESSAGE) return null;
        LinkedList<HistoryChatBean> list = new LinkedList<>();
        int count = protocol.getMessagesCount();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfMessage message = protocol.getMessages(i);
            if (message == null) continue;
            HistoryChatBean bean = new HistoryChatBean();
            bean.msgType = MqttConfig.CHAT_TYPE_MESSAGE;
            bean.version = protocol.getVersion();
            bean.message = changeChatMessageBean(message);
            bean.messageSendTime = 0;
            bean.status = 1;
            if (bean.message == null) continue;
            bean.msgId = bean.message.id;
            bean.sortMsgId = bean.message.id;
            bean.time = bean.message.time;
            bean.clientTempMessageId = bean.message.clientTempMessageId;
            bean.domain = protocol.getDomain();
            list.add(bean);
        }
        return list;
    }

    /**
     * 将ChatProtocol解码为ChatBean
     *
     * @param protocol
     * @return
     */
    public LinkedList<ChatBean> builderIqResponse(ChatProtocol.TechwolfChatProtocol protocol, ChatBean chatBean) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_IQ_RESPONSE || chatBean == null)
            return null;
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ_RESPONSE;
        chatBean.version = protocol.getVersion();
        chatBean.iqResponse = changeChatIQResponseBean(protocol.getIqResponse());
        if (chatBean.iqResponse == null) return null;
        if (chatBean.iqResponse.results == null || chatBean.iqResponse.results.size() <= 0)
            return null;
        ChatProtocol.TechwolfChatProtocol.Builder chatProtocolBuilder = ChatProtocol.TechwolfChatProtocol.newBuilder();
        chatProtocolBuilder.setType(MqttConfig.CHAT_TYPE_MESSAGE);
        chatProtocolBuilder.setVersion(protocol.getVersion());
        int messageCount = protocol.getMessagesCount();
        for (int i = 0; i < messageCount; i++) {
            chatProtocolBuilder.addMessages(protocol.getMessages(i));
        }
        return MessageDecoder.getInstance().builderMessage(chatProtocolBuilder.build());
    }

    /**
     * 将ChatProtocol解码为ChatBean列表
     *
     * @param protocol
     * @return
     */
    public LinkedList<ChatBean> builderMessageSync(ChatProtocol.TechwolfChatProtocol protocol) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE)
            return null;
        LinkedList<ChatBean> list = new LinkedList<>();
        int count = protocol.getMessageSyncCount();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfMessageSync messageSync = protocol.getMessageSync(i);
            if (messageSync == null) continue;
            ChatBean bean = new ChatBean();
            bean.msgType = MqttConfig.CHAT_TYPE_SEND_MESSAGE_ID_RESPONSE;
            bean.version = protocol.getVersion();
            bean.domain = protocol.getDomain();
            bean.messageSync = changeMessageSyncBean(messageSync);
            if (bean.messageSync == null) continue;
            list.add(bean);
        }
        return list;
    }

    public ChatBean builderMessageRead(ChatProtocol.TechwolfChatProtocol protocol) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ)
            return null;
        ChatBean bean = new ChatBean();
        bean.msgType = MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ;
        bean.version = protocol.getVersion();
        bean.domain = protocol.getDomain();
        int count = protocol.getMessageReadCount();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfMessageRead messageRead = protocol.getMessageRead(i);
            if (messageRead == null) continue;
            if (bean.messageRead == null) {
                bean.messageRead = new ArrayList<>();
            }
            bean.messageRead.add(changeMessageReadBean(messageRead));
        }
        return bean;
    }

    public ChatBean builderMessageDataSync(ChatProtocol.TechwolfChatProtocol protocol) {
        if (protocol == null || protocol.getType() != MqttConfig.CHAT_TYPE_DATA_SYNC)
            return null;
        ChatProtocol.TechwolfDataSync dataSync = protocol.getDataSync();
        if (dataSync == null) return null;
        ChatBean bean = new ChatBean();
        bean.msgType = MqttConfig.CHAT_TYPE_DATA_SYNC;
        bean.version = protocol.getVersion();
        bean.messageDataSync = changeMessageDataSyncBean(dataSync);
        if (bean.messageDataSync == null) return null;
        return bean;
    }


    /**
     * 将 ChatProtocol.TechwolfMessage 转换为 ChatMessageBean
     *
     * @param protocol
     * @return
     */
    private ChatMessageBean changeChatMessageBean(ChatProtocol.TechwolfMessage protocol) {
        if (protocol == null) return null;
        ChatMessageBean bean = new ChatMessageBean();
        bean.id = protocol.getMid();
        bean.fromUser = changeChatUserBean(protocol.getFrom());
        bean.toUser = changeChatUserBean(protocol.getTo());
        bean.type = protocol.getType();
        bean.time = protocol.getTime();
        bean.messageBody = changeChatMessageBodyBean(protocol.getBody());
        bean.encryptedBody = changeChatEntryBodyBean(protocol.getEncryptedBody());
        bean.pushText = protocol.getPushText();
        bean.soundUri = protocol.getPushSound() == 2 ?
                "android.resource://com.hpbr.bosszhipin/raw/gold_sound" : null;
        bean.taskId = protocol.getTaskId();
        bean.securityId = protocol.getSecurityId();
        bean.isOffline = protocol.getOffline();
        bean.clientTempMessageId = protocol.getCmid();
        bean.status = protocol.getStatus();
        bean.unCount = protocol.getUncount();
        bean.flag = protocol.getFlag();
        bean.bizId = protocol.getBizId();
        bean.bizType = protocol.getBizType();
        bean.quoteId = protocol.getQuoteId();
        return bean;
    }


    /**
     * 将 ChatProtocol.TechwolfUser 转换为 ChatUserBean
     *
     * @param protocol
     * @return
     */
    private ChatUserBean changeChatUserBean(ChatProtocol.TechwolfUser protocol) {
        if (protocol == null) return null;
        ChatUserBean bean = new ChatUserBean();
        bean.id = protocol.getUid();
        //和服务器沟通，系统id之前是1000，后来修改成899，很早很早之前的逻辑了
        if (bean.id == 1000) bean.id = MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
        bean.name = protocol.getName();
        bean.avatar = protocol.getAvatar();
        bean.friendSource = protocol.getSource();
//        bean.company = protocol.getCompany();
//        bean.headDefaultImageIndex = protocol.getHeadImg();
        return bean;
    }

    private ChatUserInfoModel changeChatUserInfoModel(ChatProtocol.TechwolfUser protocol) {
        if (protocol == null) return null;
        ChatUserInfoModel bean = new ChatUserInfoModel();
        bean.id = protocol.getUid();
        if (bean.id == 1000) bean.id = MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
        bean.name = protocol.getName();
        bean.avatar = protocol.getAvatar();
        bean.company = protocol.getCompany();
        bean.headDefaultImageIndex = protocol.getHeadImg();
        return bean;
    }

    private byte[] changeChatEntryBodyBean(ByteString encryptedBody) {

        return encryptedBody.toByteArray();
    }

    /**
     * 将 ChatProtocol.TechwolfMessageBody 转换为 ChatMessageBodyBean
     *
     * @param protocol
     * @return
     */
    public ChatMessageBodyBean changeChatMessageBodyBean(ChatProtocol.TechwolfMessageBody protocol) {
        if (protocol == null) return null;
        ChatMessageBodyBean bean = new ChatMessageBodyBean();
        bean.type = protocol.getType();
        bean.templateId = protocol.getTemplateId();
        bean.title = protocol.getHeadTitle();
        bean.style = protocol.getStyle();
        switch (bean.type) {
            case 1:
                bean.text = protocol.getText();
                bean.extend = protocol.getExtend();
                bean.atBean = changeAtBean(protocol.getAtInfo());
                break;
            case 2:
                bean.sound = changeChatSoundBean(protocol.getSound());
                bean.text = protocol.getText();
                break;
            case 3:
                bean.image = changeChatImageBean(protocol.getImage());
                break;
            case 4:
                bean.action = changeChatActionBean(protocol.getAction());
                break;
            case 5:
                if (protocol.getArticlesCount() > 0) {
                    bean.article = changeChatArticleBean(protocol.getArticles(0));
                }
                break;
            case 6:
                bean.notify = changeChatNotifyBean(protocol.getNotify());
                break;
            case 7:
                bean.dialog = changeChatDialogBean(protocol.getDialog());
                break;
            case 8:
                bean.job = changeChatJobBean(protocol.getJobDesc());
                break;
            case 9:
                bean.resume = changeChatResumeBean(protocol.getResume());
                break;
            case 10:
                bean.redEnvelope = changeChatRedEnvelopeBean(protocol.getRedEnvelope());
                break;
            case 11:
                bean.orderBean = changeChatOrderBean(protocol.getOrderDetail());
                break;
            case 12:
                bean.hyperLinkBean = changeChatHyperLinkBean(protocol.getHyperLink());
                break;
            case 13:
                bean.videoBean = changeVideoMessage(protocol.getVideo());
                break;
            case 14:
                bean.interviewBean = changeInterviewMessage(protocol.getInterview());
                break;
            case 15:
                bean.articleList = changeAirticleListMessage(protocol.getArticlesList());
                break;
            case 16:
                bean.articleList = changeAirticleListMessage(protocol.getArticlesList());
                break;
            case 17:
                bean.text = protocol.getText();
                break;
            case 18:
                bean.jobShareBean = changeJobShareMessage(protocol.getJobShare());
                break;
            case 19:
                bean.resumeShareBean = changResumeShareMessage(protocol.getResumeShare());
                break;
            case 20:
                bean.gifImageBean = changeGifImageMessage(protocol.getSticker());
                break;
            case 22:
                bean.chatShareBean = changeShareHistoryMessage(protocol.getChatShare());
                break;
            case 23:
                bean.interviewShare = changeInterviewShareMessage(protocol.getInterviewShare());
                break;
            case 24:
                bean.flingList = changeFlingerListMessage(protocol.getListCard());
                break;
            case 25:
                bean.gradeCardBean = changeGradeCardMessage(protocol.getStarRate());
                break;
            case 26:
                bean.frameBean = changeFrameMessage(protocol.getFrame());
                break;
            case 27:
                bean.multiplyImage = new ArrayList<>();
                ChatProtocol.TechwolfMultiImage multiImage = protocol.getMultiImage();
                int imagesCount = multiImage.getImagesCount();
                for (int i = 0; i < imagesCount; i++) {
                    ChatProtocol.TechwolfImageInfo images = multiImage.getImages(i);
                    ChatImageInfoBean item = new ChatImageInfoBean();
                    item.url = images.getUrl();
                    item.width = images.getWidth();
                    item.height = images.getHeight();
                    bean.multiplyImage.add(item);
                }
            case 28:
                bean.companyDescBean = changeCompanyDescMessage(protocol.getComDesc());
                break;
            case 29:
                bean.antelopeInfoBean = changeAntelopeInfoMessage(protocol.getUserCard());
                break;
            default:
                break;
        }
        return bean;
    }


    private ChatGroupAntelopeInfoCardBean changeAntelopeInfoMessage(ChatProtocol.TechwolfUserCard userCard) {
        ChatGroupAntelopeInfoCardBean anteInfoBean = new ChatGroupAntelopeInfoCardBean();
        anteInfoBean.name = userCard.getName();
        anteInfoBean.tiny = userCard.getTiny();
        anteInfoBean.gender = userCard.getGender();
        anteInfoBean.comment = userCard.getComment();
        anteInfoBean.description = userCard.getDescription();
        anteInfoBean.labels = new ArrayList<>();

        int labelCount = userCard.getLabelsCount();
        for (int i = 0; i < labelCount; i++) {
            String label = userCard.getLabels(i);
            anteInfoBean.labels.add(label);
        }

        anteInfoBean.url = userCard.getUrl();

        anteInfoBean.extend = userCard.getExtend();

        return anteInfoBean;
    }


    private ChatCompanyDescBean changeCompanyDescMessage(ChatProtocol.TechwolfComDesc comDesc) {
        ChatCompanyDescBean companyDescBean = new ChatCompanyDescBean();
        companyDescBean.picUrl = comDesc.getPicUrl();
        companyDescBean.name = comDesc.getName();
        companyDescBean.stage = comDesc.getStage();
        companyDescBean.scale = comDesc.getScale();
        companyDescBean.url = comDesc.getUrl();
        companyDescBean.industry = comDesc.getIndustry();
        companyDescBean.welfareLabels = new ArrayList<>();

        int welfareLabelsCount = comDesc.getWelfareLabelsCount();
        for (int i = 0; i < welfareLabelsCount; i++) {
            String welfareLabels = comDesc.getWelfareLabels(i);
            companyDescBean.welfareLabels.add(welfareLabels);
        }

        companyDescBean.introduce = comDesc.getIntroduce();

        companyDescBean.extend = comDesc.getExtend();

        return companyDescBean;
    }

    private ChatFrameBean changeFrameMessage(ChatProtocol.TechwolfFrame frame) {
        ChatFrameBean chatFrameBean = new ChatFrameBean();
        chatFrameBean.href = frame.getHref();
        return chatFrameBean;
    }

    private ChatGradeCardBean changeGradeCardMessage(ChatProtocol.TechwolfStarRate starRate) {
        ChatGradeCardBean gradeCardBean = new ChatGradeCardBean();
        gradeCardBean.title = starRate.getTitle();
        gradeCardBean.rateStatus = starRate.getRateStatus();
        gradeCardBean.operateBean = changeGradeItemMessage(starRate.getRateStar());
        gradeCardBean.optionList = changeGradeListMessage(starRate.getStarsList());
        gradeCardBean.submitOption = changeChatDialogButtonBean(starRate.getSubmitButton());
        return gradeCardBean;
    }

    private List<ChatGradeItemBean> changeGradeListMessage(List<ChatProtocol.TechwolfStar> starsList) {
        if (starsList == null) return null;
        List<ChatGradeItemBean> result = new ArrayList<>();
        int count = starsList.size();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfStar techwolfStar = starsList.get(i);
            result.add(changeGradeItemMessage(techwolfStar));
        }
        return result;
    }

    private ChatGradeItemBean changeGradeItemMessage(ChatProtocol.TechwolfStar rateStar) {
        if (rateStar == null) return null;
        ChatGradeItemBean itemBean = new ChatGradeItemBean();
        itemBean.starId = rateStar.getStarId();
        itemBean.starDesc = rateStar.getStarDesc();
        itemBean.optionList = changeItemOptionList(rateStar.getOptionsList());
        return itemBean;
    }

    private List<String> changeItemOptionList(List<ChatProtocol.TechwolfListItem> optionsList) {
        List<String> result = new ArrayList<>();
        int count = LList.getCount(optionsList);
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfListItem techwolfListItem = optionsList.get(i);
            result.add(techwolfListItem.getTitle());
        }
        return result;
    }


    /**
     * @param listCard
     * @return
     */
    private ChatCustomerFlingList changeFlingerListMessage(ChatProtocol.TechwolfListCard listCard) {
        if (listCard == null) return null;
        ChatCustomerFlingList customerFlingList = new ChatCustomerFlingList();
        customerFlingList.title = listCard.getTitle();
        customerFlingList.pageSize = listCard.getPageSize();
        customerFlingList.items = changeCustomerFlingItem(listCard.getItemsList());
        customerFlingList.urls = getUrlFromCustomerFlingItem(listCard.getItemsList());
        customerFlingList.extend = listCard.getExtend();
        customerFlingList.newItems = changeCustomerFlingItem2(listCard.getItemsList());
        //兼容历史版本
        customerFlingList.icons = changeCustomerFlingIcon(listCard.getItemsList());
        return customerFlingList;
    }


    private List<Integer> changeCustomerFlingIcon(List<ChatProtocol.TechwolfListItem> itemsList) {
        if (itemsList == null) return null;
        List<Integer> result = new ArrayList<>();
        int count = itemsList.size();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfListItem techwolfListItem = itemsList.get(i);
            result.add(techwolfListItem.getIcon());
        }
        return result;
    }


    private List<ReplyItemListBean> changeCustomerFlingItem2(List<ChatProtocol.TechwolfListItem> itemsList) {
        if (itemsList == null) return null;
        List<ReplyItemListBean> result = new ArrayList<>();
        int count = itemsList.size();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfListItem techwolfListItem = itemsList.get(i);
            ReplyItemListBean itemListBean = new ReplyItemListBean();
            itemListBean.title = techwolfListItem.getTitle();
            itemListBean.text = techwolfListItem.getText();
            itemListBean.url = techwolfListItem.getUrl();
            result.add(itemListBean);
        }
        return result;
    }

    private List<String> changeCustomerFlingItem(List<ChatProtocol.TechwolfListItem> itemsList) {
        if (itemsList == null) return null;
        List<String> result = new ArrayList<>();
        int count = itemsList.size();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfListItem techwolfListItem = itemsList.get(i);
            result.add(techwolfListItem.getTitle());
        }
        return result;
    }

    private List<String> getUrlFromCustomerFlingItem(List<ChatProtocol.TechwolfListItem> itemsList) {
        if (itemsList == null) return null;
        List<String> result = new ArrayList<>();
        int count = itemsList.size();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfListItem techwolfListItem = itemsList.get(i);
            result.add(techwolfListItem.getUrl());
        }
        return result;
    }

    private InterviewShare changeInterviewShareMessage(ChatProtocol.TechwolfInterviewShare protocol) {
        InterviewShare interviewShare = new InterviewShare();
        interviewShare.interviewId = protocol.getInterviewId();
        interviewShare.title = protocol.getTitle();
        interviewShare.bottomText = protocol.getBottomText();
        interviewShare.url = protocol.getUrl();
        interviewShare.interviewTime = protocol.getInterviewTime();
        interviewShare.interviewAddress = protocol.getInterviewAddress();
        interviewShare.jobName = protocol.getJobName();
        interviewShare.user = changeChatUserBean(protocol.getUser());
        return interviewShare;
    }

    private ChatShareBean changeShareHistoryMessage(ChatProtocol.TechwolfChatShare protocol) {
        if (protocol == null) return null;
        ChatShareBean shareBean = new ChatShareBean();
        shareBean.shareId = protocol.getShareId();
        shareBean.title = protocol.getTitle();
        List<String> records = new ArrayList<>();
        int recordsCount = protocol.getRecordsCount();
        for (int i = 0; i < recordsCount; i++) {
            records.add(protocol.getRecords(i));
        }
        shareBean.records = records;
        shareBean.bottomText = protocol.getBottomText();
        shareBean.url = protocol.getUrl();
        shareBean.from = changeChatUserBean(protocol.getFrom());
        shareBean.to = changeChatUserBean(protocol.getTo());
        shareBean.user = changeChatUserBean(protocol.getUser());
        return shareBean;
    }

    private ChatGifImageBean changeGifImageMessage(ChatProtocol.TechwolfSticker protocol) {
        if (protocol == null) return null;
        ChatGifImageBean chatGifImageBean = new ChatGifImageBean();
        chatGifImageBean.image = changeChatImageBean(protocol.getImage());
        chatGifImageBean.emotionId = protocol.getSid();
        chatGifImageBean.format = protocol.getFormat();
        chatGifImageBean.packageId = protocol.getPackId();
        chatGifImageBean.name = protocol.getName();
        chatGifImageBean.encSid = protocol.getEncSid();
        return chatGifImageBean;
    }

    private ChatAtBean changeAtBean(ChatProtocol.AtInfo protocol) {
        if (protocol == null) return null;
        ChatAtBean chatAtBean = null;
        if (protocol.getFlag() > 0) {
            chatAtBean = new ChatAtBean();
            chatAtBean.flag = protocol.getFlag();
            chatAtBean.uids = protocol.getUidsList();
        }
        return chatAtBean;
    }

    private ChatResumeShareBean changResumeShareMessage(ChatProtocol.TechwolfResumeShare protocol) {
        if (protocol == null) return null;
        ChatResumeShareBean resumeShareBean = new ChatResumeShareBean();
        resumeShareBean.user = changeChatUserInfoModel(protocol.getUser());
        resumeShareBean.expectId = protocol.getExpectId();
        resumeShareBean.position = protocol.getPosition();
        resumeShareBean.salary = protocol.getSalary();
        resumeShareBean.location = protocol.getLocation();
        resumeShareBean.applyStatus = protocol.getApplyStatus();
        resumeShareBean.age = protocol.getAge();
        resumeShareBean.experience = protocol.getExperience();
        resumeShareBean.education = protocol.getEducation();
        resumeShareBean.url = protocol.getUrl();
        resumeShareBean.source = protocol.getSource();
        resumeShareBean.lid = protocol.getLid();
        resumeShareBean.isBlurred = protocol.getBlurred();
        if (resumeShareBean.user != null) {
            resumeShareBean.user.sex = protocol.getGender();
        }
        return resumeShareBean;
    }

    /**
     * 将 ChatProtocol.TechwolfSound 转换为 ChatSoundBean
     *
     * @param protocol
     * @return
     */
    private ChatSoundBean changeChatSoundBean(ChatProtocol.TechwolfSound protocol) {
        if (protocol == null) return null;
        ChatSoundBean bean = new ChatSoundBean();
        bean.id = protocol.getSid();
        bean.url = protocol.getUrl();
        bean.duration = protocol.getDuration();
        bean.extend = protocol.getExtend();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfImage 转换为 ChatImageBean
     *
     * @param protocol
     * @return
     */
    private ChatImageBean changeChatImageBean(ChatProtocol.TechwolfImage protocol) {
        if (protocol == null) return null;
        ChatImageBean bean = new ChatImageBean();
        bean.id = protocol.getIid();
        bean.tinyImage = changeChatImageInfoBean(protocol.getTinyImage());
        bean.originImage = changeChatImageInfoBean(protocol.getOriginImage());
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfImageInfo 转换为 ChatImageInfoBean
     *
     * @param protocol
     * @return
     */
    private ChatImageInfoBean changeChatImageInfoBean(ChatProtocol.TechwolfImageInfo protocol) {
        if (protocol == null) return null;
        ChatImageInfoBean bean = new ChatImageInfoBean();
        bean.url = protocol.getUrl();
        bean.width = protocol.getWidth();
        bean.height = protocol.getHeight();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfAction 转换为 ChatActionBean
     *
     * @param protocol
     * @return
     */
    private ChatActionBean changeChatActionBean(ChatProtocol.TechwolfAction protocol) {
        if (protocol == null) return null;
        ChatActionBean bean = new ChatActionBean();
        bean.type = protocol.getAid();
        bean.extend = protocol.getExtend();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfArticle 转换为 ChatArticleBean
     *
     * @param protocol
     * @return
     */
    private ChatArticleBean changeChatArticleBean(ChatProtocol.TechwolfArticle protocol) {
        if (protocol == null) return null;
        ChatArticleBean bean = new ChatArticleBean();
        bean.title = protocol.getTitle();
        bean.extend = protocol.getExtend();
        bean.description = protocol.getDescription();
        bean.photoUrl = protocol.getPicUrl();
        bean.url = protocol.getUrl();
        bean.templateId = protocol.getTemplateId();
        bean.buttonText = protocol.getBottomText();
        bean.timeout = protocol.getTimeout();
        bean.statisticParameters = protocol.getStatisticParameters();
        List<ChatProtocol.TechwolfSlice> highListList = protocol.getHighlightPartsList();
        if (highListList != null) {
            bean.highLightList = new ArrayList<>();
            for (ChatProtocol.TechwolfSlice slice : highListList) {
                HighLight h = new HighLight();
                h.setEnd(slice.getEndIndex());
                h.setStart(slice.getStartIndex());
                bean.highLightList.add(h);
            }
        }

        List<ChatProtocol.TechwolfSlice> DimPartsListList = protocol.getDimPartsList();
        if (DimPartsListList != null) {
            bean.dimParts = new ArrayList<>();
            for (ChatProtocol.TechwolfSlice slice : DimPartsListList) {
                HighLight h = new HighLight();
                h.setEnd(slice.getEndIndex());
                h.setStart(slice.getStartIndex());
                bean.dimParts.add(h);
            }
        }
        bean.subTitle = protocol.getSubTitle();
        return bean;
    }

    private List<ChatArticleBean> changeAirticleListMessage(List<ChatProtocol.TechwolfArticle> protocol) {
        if (protocol == null || protocol.size() == 0) return null;
        List<ChatArticleBean> list = new ArrayList<>();
        for (ChatProtocol.TechwolfArticle article : protocol) {
            list.add(changeChatArticleBean(article));
        }
        return list;
    }

    private ChatJobShareBean changeJobShareMessage(ChatProtocol.TechwolfJobShare protocol) {
        if (protocol == null) return null;
        ChatJobShareBean bean = new ChatJobShareBean();
        bean.user = changeChatUserInfoModel(protocol.getUser());
        bean.bossId = protocol.getBossId();
        bean.jobId = protocol.getJobId();
        bean.position = protocol.getPosition();
        bean.salary = protocol.getSalary();
        bean.location = protocol.getLocation();
        bean.description = protocol.getDescription();
        bean.price = protocol.getPrice();
        bean.company = protocol.getCompany();
        bean.stage = protocol.getStage();
        bean.experience = protocol.getExperience();
        bean.education = protocol.getEducation();
        bean.url = protocol.getUrl();
        bean.lid = protocol.getLid();
        bean.extend = protocol.getExtend();
        return bean;
    }


    /**
     * 将 ChatProtocol.TechwolfNotify 转换为 ChatNotifyBean
     *
     * @param protocol
     * @return
     */
    private ChatNotifyBean changeChatNotifyBean(ChatProtocol.TechwolfNotify protocol) {
        if (protocol == null) return null;
        ChatNotifyBean bean = new ChatNotifyBean();
        bean.text = protocol.getText();
        bean.url = protocol.getUrl();
        bean.title = protocol.getTitle();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfDialog 转换为 ChatDialogBean
     *
     * @param protocol
     * @return
     */
    private ChatDialogBean changeChatDialogBean(ChatProtocol.TechwolfDialog protocol) {
        if (protocol == null) return null;
        ChatDialogBean bean = new ChatDialogBean();
        bean.title = protocol.getTitle();
        bean.text = protocol.getText();
        bean.content = protocol.getContent();
        List<ChatProtocol.TechwolfButton> buttonsProtocol = protocol.getButtonsList();
        if (buttonsProtocol != null && buttonsProtocol.size() > 0) {
            bean.buttons = new ArrayList<>();
            for (ChatProtocol.TechwolfButton buttonProtocol : buttonsProtocol) {
                bean.buttons.add(changeChatDialogButtonBean(buttonProtocol));
            }
        }
        bean.operated = protocol.getOperated();
        bean.clickMore = protocol.getClickMore();
        bean.extend = protocol.getExtend();
        bean.selectedIndex = protocol.getSelectedIndex();
        bean.type = protocol.getType();
        bean.dialogTargetUrl = protocol.getUrl();
        bean.backgroundUrl = protocol.getBackgroundUrl();
        bean.timeout = protocol.getTimeout();
        bean.clickTime = 0;
        bean.statisticParameters = protocol.getStatisticParameters();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfButton 转换为 ChatDialogBean.Button
     *
     * @param protocol
     * @return
     */
    private ChatDialogButtonBean changeChatDialogButtonBean(ChatProtocol.TechwolfButton protocol) {
        if (protocol == null) return null;
        ChatDialogButtonBean bean = new ChatDialogButtonBean();
        bean.text = protocol.getText();
        bean.url = protocol.getUrl();
        bean.templateId = protocol.getTemplateId();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfJobDesc 转换为 ChatJobBean
     *
     * @param protocol
     * @return
     */
    private ChatJobBean changeChatJobBean(ChatProtocol.TechwolfJobDesc protocol) {
        if (protocol == null) return null;
        ChatJobBean bean = new ChatJobBean();
        bean.id = protocol.getJobId();
        bean.title = protocol.getTitle();
        bean.company = protocol.getCompany();
        bean.salary = protocol.getSalary();
        bean.url = protocol.getUrl();
        bean.positionClassName = protocol.getPositionCategory();
        bean.experience = protocol.getExperience();
        bean.education = protocol.getEducation();
        bean.city = protocol.getCity();
        bean.bossPositionName = protocol.getBossTitle();
        bean.bossInfo = changeChatUserInfoModel(protocol.getBoss());
        bean.lid = protocol.getLid();
        bean.stage = protocol.getStage();
        bean.bottomText = protocol.getBottomText();
        bean.jobLabel = protocol.getJobLabel();
        bean.profileFlags = protocol.getIconFlag();
        bean.positionDesc = protocol.getContent();
        bean.skills = new ArrayList<>();
        if (protocol.getLabelsList() != null) {
            bean.skills.addAll(protocol.getLabelsList());
        }

        bean.expectPosition = protocol.getExpectPosition();
        bean.expectId = protocol.getExpectId();
        bean.expectSalary = protocol.getExpectSalary();
        bean.partTimeDesc = protocol.getPartTimeDesc();
        bean.distance = protocol.getDistance();
        bean.latlon = protocol.getLatlon();
        bean.extend = protocol.getExtend();

        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfResume 转换为 ChatResumeBean
     *
     * @param protocol
     * @return
     */
    private ChatResumeBean changeChatResumeBean(ChatProtocol.TechwolfResume protocol) {
        if (protocol == null) return null;
        ChatResumeBean bean = new ChatResumeBean();
        bean.userInfo = changeChatUserInfoModel(protocol.getUser());
        bean.id = protocol.getExpectId();
        bean.description = protocol.getDescription();
        bean.city = protocol.getCity();
        bean.positionName = protocol.getPosition();
        bean.securityId = protocol.getSecurityId();
        int keywordsCount = protocol.getKeywordsCount();
        if (keywordsCount > 0) {
            StringBuffer str = new StringBuffer();
            for (int i = 0; i < keywordsCount; i++) {
                str.append(protocol.getKeywords(i));
                if (i != keywordsCount - 1) {
                    str.append(StringUtil.SPLIT_CHAR_KEYWORD);
                }
            }
            bean.advantage = str.toString();
        }
        bean.lid = protocol.getLid();
        if (bean.userInfo != null) {
            bean.userInfo.sex = protocol.getGender();
        }
        bean.expSalary = protocol.getSalary();
        bean.brandName = protocol.getBrandName();
        bean.workAge = protocol.getWorkYear();
        bean.firstText = protocol.getContent1();
        bean.secondText = protocol.getContent2();
        bean.education = protocol.getEducation();
        bean.thirdText = protocol.getContent3();

        List<String> keywordsList = protocol.getKeywordsList();
        if (keywordsList != null) {
            bean.keyWordsList = new ArrayList<>();
            for (String keyWords : keywordsList) {
                bean.keyWordsList.add(keyWords);
            }
        }


        bean.age = protocol.getAge();
        List<String> labelsList = protocol.getLabelsList();
        if (labelsList != null) {
            bean.labels = new ArrayList<>();
            for (String label : labelsList) {
                bean.labels.add(label);
            }
        }

        List<ChatProtocol.UserExperience> experiencesList = protocol.getExperiencesList();
        if (experiencesList != null) {
            bean.experiences = new ArrayList<>();
            for (ChatProtocol.UserExperience experience : experiencesList) {
                ChatUserExperience userExperience = new ChatUserExperience();
                userExperience.endDate = experience.getEndDate();
                userExperience.occupation = experience.getOccupation();
                userExperience.organization = experience.getOrganization();
                userExperience.startDate = experience.getStartDate();
                userExperience.type = experience.getType();
                bean.experiences.add(userExperience);
            }
        }
        bean.positionCategory = protocol.getPositionCategory();
        bean.jobSalary = protocol.getJobSalary();
        bean.bottomText = protocol.getBottomText();
        bean.applyStatus = protocol.getApplyStatus();
        bean.jobId = protocol.getJobId();
        bean.extend = protocol.getExtend();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfRedEnvelope 转换为 ChatRedEnvelope
     *
     * @param protocol
     * @return
     */
    private ChatRedEnvelopeBean changeChatRedEnvelopeBean(ChatProtocol.TechwolfRedEnvelope protocol) {
        if (protocol == null) return null;
        ChatRedEnvelopeBean bean = new ChatRedEnvelopeBean();
        bean.id = protocol.getRedId();
        bean.text = protocol.getRedText();
        bean.title = protocol.getRedTitle();
        bean.url = protocol.getClickUrl();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfOrderDetail 转换为 ChatOrderBean
     *
     * @param protocol
     * @return
     */
    private ChatOrderBean changeChatOrderBean(ChatProtocol.TechwolfOrderDetail protocol) {
        if (protocol == null) return null;
        ChatOrderBean bean = new ChatOrderBean();
        bean.title = protocol.getTitle();
        bean.datetime = protocol.getSubTitle();
        bean.item = new LinkedList<>();
        int count = protocol.getOrderDetailEntryListCount();
        for (int i = 0; i < count; i++) {
            ChatProtocol.TechwolfOrderDetailEntry entry = protocol.getOrderDetailEntryList(i);
            if (entry == null) continue;
            ChatProtocol.TechwolfOrderDetailItem key = entry.getKey();
            ChatOrderDetailItemBean item = new ChatOrderDetailItemBean();
            if (key != null) {
                item.name = key.getName();
                item.nameTemplate = key.getTemplateId();
            }
            ChatProtocol.TechwolfOrderDetailItem value = entry.getValue();
            if (value != null) {
                item.value = value.getName();
                item.valueTemplate = value.getTemplateId();
            }
            bean.item.add(item);
        }
        bean.url = protocol.getUrl();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfIqResponse 转换为 ChatIQResponseBean
     *
     * @param protocol
     * @return
     */
    private ChatIQResponseBean changeChatIQResponseBean(ChatProtocol.TechwolfIqResponse protocol) {
        if (protocol == null) return null;
        ChatIQResponseBean bean = new ChatIQResponseBean();
        bean.id = protocol.getQid();
        bean.query = protocol.getQuery();
        List<ChatProtocol.TechwolfKVEntry> results = protocol.getResultsList();
        if (results != null && results.size() > 0) {
            for (ChatProtocol.TechwolfKVEntry kvEntry : results) {
                bean.results.put(kvEntry.getKey(), kvEntry.getValue());
            }
        }
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfMessageSync 转换为 ChatMessageSyncBean
     *
     * @param protocol
     * @return
     */
    private ChatMessageSyncBean changeMessageSyncBean(ChatProtocol.TechwolfMessageSync protocol) {
        if (protocol == null) return null;
        ChatMessageSyncBean bean = new ChatMessageSyncBean();
        bean.clientId = protocol.getClientMid();
        bean.serverId = protocol.getServerMid();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfMessageRead 转换为 ChatMessageReadBean
     *
     * @param protocol
     * @return
     */
    private ChatMessageReadBean changeMessageReadBean(ChatProtocol.TechwolfMessageRead protocol) {
        if (protocol == null) return null;
        ChatMessageReadBean bean = new ChatMessageReadBean();
        bean.userId = protocol.getUserId();
        bean.messageId = protocol.getMessageId();
        bean.readTime = protocol.getReadTime();
        bean.sync = protocol.getSync();
        bean.friendSource = protocol.getUserSource();
        return bean;
    }

    /**
     * 将 ChatProtocol.TechwolfDataSync 转换为 ChatDataSync
     *
     * @param protocol
     * @return
     */
    private ChatDataSync changeMessageDataSyncBean(ChatProtocol.TechwolfDataSync protocol) {
        if (protocol == null) return null;
        ChatDataSync dataSync = new ChatDataSync();
        dataSync.type = protocol.getType();
        if (dataSync.type == 2001 || dataSync.type == 2002 || dataSync.type == 2003 || dataSync.type == 2004) {
            ChatGroupSync groupSync = new ChatGroupSync();
            dataSync.groupSync = groupSync;
            ChatProtocol.TechwolfGroupSync techwolfGroupSync = protocol.getGroupSync();
            if (techwolfGroupSync != null) {
                groupSync.gid = techwolfGroupSync.getGid();
                groupSync.encGid = techwolfGroupSync.getEncGid();
                groupSync.version = techwolfGroupSync.getVersion();
                groupSync.extraJson = techwolfGroupSync.getExtraJson();
            }
        } else {
            ChatDataSyncProfile profile = new ChatDataSyncProfile();
            dataSync.syncProfile = profile;
            ChatProtocol.TechwolfUserSync userSyncProtocol = protocol.getUserSync();
            if (userSyncProtocol != null) {
                profile.uid = userSyncProtocol.getUid();
                profile.role = userSyncProtocol.getIdentity();
                profile.extraJson = userSyncProtocol.getExtraJson();
                profile.friendSource = userSyncProtocol.getUserSource();
            }
        }
        return dataSync;
    }

    /**
     * @param protocol
     * @return
     */
    private ChatHyperLinkBean changeChatHyperLinkBean(ChatProtocol.TechwolfHyperLink protocol) {
        if (protocol == null) return null;
        ChatHyperLinkBean bean = new ChatHyperLinkBean();
        bean.text = protocol.getText();
        bean.url = protocol.getUrl();
        bean.templateId = protocol.getHyperLinkType();
        String extraJson = protocol.getExtraJson();
        if (!TextUtils.isEmpty(extraJson)) {
            try {
                JSONObject jsonObject = new JSONObject(extraJson);
                bean.fileSize = jsonObject.optString("fileSizeDesc");
                bean.icon = jsonObject.optString("icon");
                bean.title = jsonObject.optString("title");
                bean.desc = jsonObject.optString("mailTip");
                bean.highlightStart = jsonObject.optInt("highlightStart");
                bean.highlightLength = jsonObject.optInt("highlightLength");
                bean.tips = jsonObject.optString("tips");
                bean.atsType = jsonObject.optInt("atsType");
                bean.resumeRevocable = jsonObject.optInt("resumeRevocable");
                bean.mediaType = jsonObject.optInt("mediaType");
                bean.showUserId = jsonObject.optString("showUserId");
                bean.status = jsonObject.optInt("status");
                bean.fileMd5 = jsonObject.optString("fileMd5");
                bean.fileType = jsonObject.optString("fileType");
            } catch (JSONException e) {
                bean.fileSize = null;
                bean.desc = null;
            }
        }
        bean.extraJson = extraJson;
        return bean;
    }

    private ChatVideoBean changeVideoMessage(ChatProtocol.TechwolfVideo protocol) {
        if (protocol == null) return null;
        ChatVideoBean bean = new ChatVideoBean();
        bean.type = protocol.getType();
        bean.status = protocol.getStatus();
        bean.text = protocol.getText();
        bean.duration = protocol.getDuration();
        return bean;
    }

    private ChatInterviewBean changeInterviewMessage(ChatProtocol.TechwolfInterview protocol) {
        if (protocol == null) return null;
        ChatInterviewBean bean = new ChatInterviewBean();
        bean.condition = protocol.getCondition();
        bean.text = protocol.getText();
        bean.url = protocol.getUrl();
        bean.extend = protocol.getExtend();
        return bean;
    }

}