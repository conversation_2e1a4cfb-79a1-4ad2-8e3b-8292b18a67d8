package com.hpbr.bosszhipin.module.contacts.manager;

import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.HighLight;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatArticleBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatClientInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogButtonBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGifImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatHyperLinkBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatIQBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatInterviewBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatJobBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatJobShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageReadBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatNotifyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatPresenceBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatProtocol;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatRedEnvelopeBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatResumeBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatResumeShareBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserExperience;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserInfoModel;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatVideoBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;

import java.util.List;
import java.util.Map;

/**
 * Created by monch on 15/5/1.
 * 编码
 */
public class MessageEncoder {

    private static MessageEncoder instance = new MessageEncoder();

    private MessageEncoder() {
    }

    public synchronized static MessageEncoder getInstance() {
        return instance;
    }

    /**
     * 将ChatBean编码为ChatProtocol
     *
     * @param bean
     * @return
     */
    public ChatProtocol.TechwolfChatProtocol builder(ChatBean bean) {
        if (bean == null) return null;
        return changeTechwolfChat(bean);
    }

    /**
     * 将类型为Message的ChatBean数组转换为ChatProtocol.TechwolfChatProtocol
     *
     * @param list
     * @return
     */
    public ChatProtocol.TechwolfChatProtocol builderTypeMessage(List<ChatBean> list) {
        if (list == null || list.size() <= 0) return null;
        ChatProtocol.TechwolfChatProtocol.Builder builder = ChatProtocol.TechwolfChatProtocol.newBuilder();
        builder.setType(MqttConfig.CHAT_TYPE_MESSAGE);
        builder.setVersion((MqttConfig.PROTOCOL_VERSION));
        for (ChatBean bean : list) {
            if (bean == null || bean.msgType != MqttConfig.CHAT_TYPE_MESSAGE) continue;
            ChatProtocol.TechwolfMessage message = changeTechwolfMessage(bean.message);
            if (message != null) {
                builder.addMessages(message);
            }
        }
        return builder.build();
    }

    /**
     * 将ChatBean转换为ChatProtocol.TechwolfChatProtocol
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfChatProtocol changeTechwolfChat(ChatBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfChatProtocol.Builder builder = ChatProtocol.TechwolfChatProtocol.newBuilder();
        builder.setType(bean.msgType);
        builder.setVersion(MqttConfig.PROTOCOL_VERSION);
        switch (bean.msgType) {
            case MqttConfig.CHAT_TYPE_MESSAGE:
                ChatProtocol.TechwolfMessage message = changeTechwolfMessage(bean.message);
                if (message != null) {
                    builder.addMessages(message);
                }
                break;
            case MqttConfig.CHAT_TYPE_PRESENCE:
                ChatProtocol.TechwolfPresence presence = changeTechwolfPresence(bean.presence);
                if (presence != null) {
                    builder.setPresence(presence);
                }
                break;
            case MqttConfig.CHAT_TYPE_IQ:
                ChatProtocol.TechwolfIq iq = changeTechwolfIq(bean.iq);
                if (iq != null) {
                    builder.setIq(iq);
                }
                break;
            case MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ:

                if (bean.messageRead != null) {
                    for (ChatMessageReadBean item : bean.messageRead) {
                        ChatProtocol.TechwolfMessageRead messageRead = changeTechwolfMessageRead(item);
                        if (messageRead == null) continue;
                        builder.addMessageRead(messageRead);
                    }
                }


                break;
            default:
                break;
        }
        return builder.build();
    }

    /**
     * 将ChatMessageBean转换为ChatProtocol.TechwolfMessage
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfMessage changeTechwolfMessage(ChatMessageBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfMessage.Builder builder = ChatProtocol.TechwolfMessage.newBuilder();
        ChatProtocol.TechwolfUser fromUser = changeTechwolfUser(bean.fromUser);
        if (fromUser != null) {
            builder.setFrom(fromUser);
        }
        ChatProtocol.TechwolfUser toUser = changeTechwolfUser(bean.toUser);
        if (toUser != null) {
            builder.setTo(toUser);
        }
        if (!LText.empty(bean.bizId)) {
            builder.setBizId(bean.bizId);
        }
        builder.setQuoteId(bean.quoteId);
        builder.setBizType(bean.bizType);
        builder.setType(bean.type);
        builder.setMid(bean.clientTempMessageId);
        builder.setTime(bean.time);
        ChatProtocol.TechwolfMessageBody body = changeTechwolfMessageBody(bean.messageBody);
        if (body != null) {
            builder.setBody(body);
        }
        if (bean.pushText != null) {
            builder.setPushText(bean.pushText);
        }
        builder.setTaskId(bean.taskId);
        builder.setOffline(bean.isOffline);
        builder.setCmid(bean.clientTempMessageId);
        builder.setStatus(bean.status);
        builder.setUncount(bean.unCount);
        return builder.build();
    }

    /**
     * 将ChatUserBean转换为ChatProtocol.TechwolfUser
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfUser changeTechwolfUser(ChatUserBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfUser.Builder builder = ChatProtocol.TechwolfUser.newBuilder();
        if (bean.id == 1000) bean.id = MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
        builder.setUid(bean.id);
        if (!LText.empty(bean.name)) {
            builder.setName(bean.name);
        }
        builder.setSource(bean.friendSource);
        return builder.build();
    }

    private ChatProtocol.TechwolfUser changeTechwolfUser(ChatUserInfoModel bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfUser.Builder builder = ChatProtocol.TechwolfUser.newBuilder();
        if (bean.id == 1000) bean.id = MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;
        builder.setUid(bean.id);
        if (!LText.empty(bean.name)) {
            builder.setName(bean.name);
        }
        if (!LText.empty(bean.avatar)) {
            builder.setAvatar(bean.avatar);
        }
        if (!LText.empty(bean.company)) {
            builder.setCompany(bean.company);
        }
        builder.setHeadImg(bean.headDefaultImageIndex);
        return builder.build();
    }

    /**
     * 将ChatMessageBodyBean转换为ChatProtocol.TechwolfMessageBody
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfMessageBody changeTechwolfMessageBody(ChatMessageBodyBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfMessageBody.Builder builder = ChatProtocol.TechwolfMessageBody.newBuilder();
        builder.setType(bean.type);
        builder.setTemplateId(bean.templateId);
        if (!LText.empty(bean.title)) {
            builder.setHeadTitle(bean.title);
        }
        switch (bean.type) {
            case 1:
                if (!LText.empty(bean.text)) {
                    builder.setText(bean.text);
                    if (bean.atBean != null && !LList.isEmpty(bean.atBean.uids)) {
                        builder.setAtInfo(ChatProtocol.AtInfo.newBuilder().addAllUids(bean.atBean.uids).setFlag(0));
                    }
                }
                if (!LText.empty(bean.extend)) {
                    builder.setExtend(bean.extend);
                }
                break;
            case 2:
                ChatProtocol.TechwolfSound sound = changeTechwolfSound(bean.sound);
                if (sound != null) {
                    builder.setSound(sound);
                }
                if (!LText.empty(bean.text)) {
                    builder.setText(bean.text);
                }
                break;
            case 3:
                ChatProtocol.TechwolfImage image = changeTechwolfImage(bean.image);
                if (image != null) {
                    builder.setImage(image);
                }
                break;
            case 4:
                ChatProtocol.TechwolfAction action = changeTechwolfAction(bean.action);
                if (action != null) {
                    builder.setAction(action);
                }
                break;
            case 5:
                ChatProtocol.TechwolfArticle article = changeTechwolfArticle(bean.article);
                if (article != null) {
                    builder.addArticles(article);
                }
                break;
            case 6:
                ChatProtocol.TechwolfNotify notify = changeTechwolfNotify(bean.notify);
                if (notify != null) {
                    builder.setNotify(notify);
                }
                break;
            case 7:
                ChatProtocol.TechwolfDialog dialog = changeTechwolfDialog(bean.dialog);
                if (dialog != null) {
                    builder.setDialog(dialog);
                }
                break;
            case 8:
                ChatProtocol.TechwolfJobDesc job = changeTechwolfJobDesc(bean.job);
                if (job != null) {
                    builder.setJobDesc(job);
                }

                break;
            case 9:
                ChatProtocol.TechwolfResume resume = changeTechwolfResume(bean.resume);
                if (resume != null) {
                    builder.setResume(resume);
                }
                break;
            case 10:
                ChatProtocol.TechwolfRedEnvelope redEnvelope = changeTechwolfRedEnvelope(bean.redEnvelope);
                if (redEnvelope != null) {
                    builder.setRedEnvelope(redEnvelope);
                }
                break;
            case 12:
                ChatProtocol.TechwolfHyperLink hyperLink = changeChatHyperLink(bean.hyperLinkBean);
                if (hyperLink != null) {
                    builder.setHyperLink(hyperLink);
                }
                break;
            case 13:
                ChatProtocol.TechwolfVideo video = changeVideo(bean.videoBean);
                if (video != null) {
                    builder.setVideo(video);
                }
                break;
            case 14:
                ChatProtocol.TechwolfInterview interview = changeInterview(bean.interviewBean);
                if (interview != null) {
                    builder.setInterview(interview);
                }
                break;
            case 15:
                if (bean.articleList != null) {
                    for (ChatArticleBean articleBean : bean.articleList) {
                        ChatProtocol.TechwolfArticle a = changeTechwolfArticle(articleBean);
                        if (a != null) {
                            builder.addArticles(a);
                        }
                    }
                }
                break;
            case 16:
                if (bean.articleList != null) {
                    for (ChatArticleBean articleBean : bean.articleList) {
                        ChatProtocol.TechwolfArticle a = changeTechwolfArticle(articleBean);
                        if (a != null) {
                            builder.addArticles(a);
                        }
                    }
                }
                break;
            case 17:
                if (!LText.empty(bean.text)) {
                    builder.setText(bean.text);
                }
                break;
            case 18:
                if (bean.jobShareBean != null) {
                    ChatProtocol.TechwolfJobShare jobShare = changeTechJobShare(bean.jobShareBean);
                    if (jobShare != null) {
                        builder.setJobShare(jobShare);
                    }
                }
                break;
            case 19:
                if (bean.resumeShareBean != null) {
                    ChatProtocol.TechwolfResumeShare resumeShare = changeTechResumeShare(bean.resumeShareBean);
                    if (resumeShare != null) {
                        builder.setResumeShare(resumeShare);
                    }
                }
                break;
            case 20:
                if (bean.gifImageBean != null) {
                    ChatProtocol.TechwolfSticker sticker = changeTechSticker(bean.gifImageBean);
                    if (sticker != null) {
                        builder.setSticker(sticker);
                    }
                }
                break;
            case 22:
                if (bean.chatShareBean != null) {
                    ChatProtocol.TechwolfChatShare chatShare = changeChatShare(bean.chatShareBean);
                    if (chatShare != null) {
                        builder.setChatShare(chatShare);
                    }
                }
            case 27:
                if (bean.multiplyImage != null) {
                    ChatProtocol.TechwolfMultiImage multiImage = changeChatMultiply(bean.multiplyImage);
                    if (multiImage != null) {
                        builder.setMultiImage(multiImage);
                    }
                }
                break;
            default:
                break;
        }
        return builder.build();
    }

    private ChatProtocol.TechwolfMultiImage changeChatMultiply(List<ChatImageInfoBean> multiplyImage) {
        if (LList.isEmpty(multiplyImage)) return null;
        ChatProtocol.TechwolfMultiImage.Builder builder = ChatProtocol.TechwolfMultiImage.newBuilder();
        for (int i = 0; i < multiplyImage.size(); i++) {
            ChatImageInfoBean element = LList.getElement(multiplyImage, i);
            if (element == null) continue;
            builder.addImages(changeTechwolfImageInfo(element));
        }
        return builder.build();
    }

    private ChatProtocol.TechwolfChatShare changeChatShare(ChatShareBean chatShareBean) {
        if (chatShareBean == null) return null;
        ChatProtocol.TechwolfChatShare.Builder builder = ChatProtocol.TechwolfChatShare.newBuilder();
        builder.setShareId(chatShareBean.shareId);
        builder.setTitle(chatShareBean.title);
        int size = chatShareBean.records.size();
        for (int i = 0; i < size; i++) {
            builder.setRecords(i, chatShareBean.records.get(i));
        }
        builder.setBottomText(chatShareBean.bottomText);
        builder.setUrl(chatShareBean.url);
        builder.setFrom(changeTechwolfUser(chatShareBean.from));
        builder.setTo(changeTechwolfUser(chatShareBean.to));
        builder.setUser(changeTechwolfUser(chatShareBean.user));
        return builder.build();
    }

    private ChatProtocol.TechwolfSticker changeTechSticker(ChatGifImageBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfSticker.Builder builder = ChatProtocol.TechwolfSticker.newBuilder();
        builder.setImage(changeTechwolfImage(bean.image));
        builder.setPackId(bean.packageId);
        builder.setSid(bean.emotionId);
        if (bean.encSid != null) {
            builder.setEncSid(bean.encSid);
        }
        return builder.build();
    }

    private ChatProtocol.TechwolfResumeShare changeTechResumeShare(ChatResumeShareBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfResumeShare.Builder builder = ChatProtocol.TechwolfResumeShare.newBuilder();
        ChatProtocol.TechwolfUser userInfo = changeTechwolfUser(bean.user);
        builder.setUser(userInfo);
        builder.setExpectId(bean.expectId);
        builder.setBlurred(bean.isBlurred);
        builder.setExpectId(bean.expectId);
        builder.setPosition(bean.position);
        builder.setSalary(bean.salary);
        builder.setLocation(bean.location);
        builder.setApplyStatus(bean.applyStatus);
        builder.setApplyStatus(bean.age);
        builder.setExperience(bean.experience);
        builder.setEducation(bean.education);
        builder.setUrl(bean.url);
        builder.setLid(bean.lid);

        return builder.build();
    }

    private ChatProtocol.TechwolfJobShare changeTechJobShare(ChatJobShareBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfJobShare.Builder builder = ChatProtocol.TechwolfJobShare.newBuilder();
        ChatProtocol.TechwolfUser userInfo = changeTechwolfUser(bean.user);
        builder.setUser(userInfo);
        builder.setJobId(bean.jobId);
        builder.setPosition(bean.position);
        builder.setSalary(bean.salary);
        builder.setLocation(bean.location);
        builder.setCompany(bean.company);
        builder.setStage(bean.stage);
        builder.setExperience(bean.experience);
        builder.setEducation(bean.education);
        builder.setUrl(bean.url);
        builder.setLid(bean.lid);
        return builder.build();
    }

    /**
     * 将ChatSoundBean转换为ChatProtocol.TechwolfSound
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfSound changeTechwolfSound(ChatSoundBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfSound.Builder builder = ChatProtocol.TechwolfSound.newBuilder();
        builder.setSid(bean.id);
        if (!LText.empty(bean.url)) {
            builder.setUrl(bean.url);
        }
        builder.setDuration(bean.duration);
        return builder.build();
    }

    /**
     * 将ChatImageBean转换为ChatProtocol.TechwolfImage
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfImage changeTechwolfImage(ChatImageBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfImage.Builder builder = ChatProtocol.TechwolfImage.newBuilder();
        builder.setIid(bean.id);
        ChatProtocol.TechwolfImageInfo tinyImageInfo = changeTechwolfImageInfo(bean.tinyImage);
        if (tinyImageInfo != null) {
            builder.setTinyImage(tinyImageInfo);
        }
        ChatProtocol.TechwolfImageInfo originImageInfo = changeTechwolfImageInfo(bean.originImage);
        if (originImageInfo != null) {
            builder.setOriginImage(originImageInfo);
        }
        return builder.build();
    }

    /**
     * 将ChatImageInfoBean转换为ChatProtocol.TechwolfImageInfo
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfImageInfo changeTechwolfImageInfo(ChatImageInfoBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfImageInfo.Builder builder = ChatProtocol.TechwolfImageInfo.newBuilder();
        if (!LText.empty(bean.url)) {
            builder.setUrl(bean.url);
        }
        builder.setWidth(bean.width);
        builder.setHeight(bean.height);
        return builder.build();
    }

    /**
     * 将ChatActionBean转换为ChatProtocol.TechwolfAction
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfAction changeTechwolfAction(ChatActionBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfAction.Builder builder = ChatProtocol.TechwolfAction.newBuilder();
        builder.setAid(bean.type);    // 此处的类型，就是动作的ID
        if (!LText.empty(bean.extend)) {
            builder.setExtend(bean.extend);
        }
        return builder.build();
    }

    /**
     * 将ChatArticleBean转换为ChatProtocol.TechwolfArticle
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfArticle changeTechwolfArticle(ChatArticleBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfArticle.Builder builder = ChatProtocol.TechwolfArticle.newBuilder();
        if (!LText.empty(bean.title))
            builder.setTitle(bean.title);
        if (!LText.empty(bean.description))
            builder.setDescription(bean.description);
        if (!LText.empty(bean.photoUrl))
            builder.setPicUrl(bean.photoUrl);
        if (!LText.empty(bean.url))
            builder.setUrl(bean.url);
        builder.setTemplateId(bean.templateId);
        if (!LText.empty(bean.buttonText))
            builder.setBottomText(bean.buttonText);
        if (!LText.empty(bean.statisticParameters))
            builder.setStatisticParameters(bean.statisticParameters);

        if (bean.highLightList != null) {
            for (HighLight highLight : bean.highLightList) {
                builder.addHighlightParts(changeTechwolfSlice(highLight));
            }
        }

        if (bean.dimParts != null) {
            for (HighLight highLight : bean.dimParts) {
                builder.addDimParts(changeTechwolfSlice(highLight));
            }
        }

        if (!LText.empty(bean.subTitle))
            builder.setSubTitle(bean.subTitle);

        return builder.build();
    }

    private ChatProtocol.TechwolfSlice changeTechwolfSlice(HighLight bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfSlice.Builder builder = ChatProtocol.TechwolfSlice.newBuilder();
        builder.setEndIndex(bean.getEnd());
        builder.setStartIndex(bean.getStart());
        return builder.build();
    }

    /**
     * 将ChatNotifyBean转换为ChatProtocol.TechwolfNotify
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfNotify changeTechwolfNotify(ChatNotifyBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfNotify.Builder builder = ChatProtocol.TechwolfNotify.newBuilder();
        if (!LText.empty(bean.text))
            builder.setText(bean.text);
        if (!LText.empty(bean.url))
            builder.setUrl(bean.url);
        return builder.build();
    }

    /**
     * 将ChatDialogBean转换为ChatProtocol.TechwolfDialog
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfDialog changeTechwolfDialog(ChatDialogBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfDialog.Builder builder = ChatProtocol.TechwolfDialog.newBuilder();
        if (!LText.empty(bean.title))
            builder.setTitle(bean.title);
        if (!LText.empty(bean.text))
            builder.setText(bean.text);
        int index = 0;
        for (ChatDialogButtonBean b : bean.buttons) {
            builder.setButtons(index, changeTechwolfButton(b));
            index++;
        }
        builder.setOperated(bean.operated);
        builder.setClickMore(bean.clickMore);
        builder.setType(bean.type);
        builder.setUrl(bean.dialogTargetUrl);
        builder.setBackgroundUrl(bean.backgroundUrl);
        builder.setTimeout(bean.timeout);
        builder.setStatisticParameters(bean.statisticParameters);
        return builder.build();
    }

    /**
     * 将ChatDialogBean.Button转换为ChatProtocol.TechwolfButton
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfButton changeTechwolfButton(ChatDialogButtonBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfButton.Builder builder = ChatProtocol.TechwolfButton.newBuilder();
        if (!LText.empty(bean.text))
            builder.setText(bean.text);
        if (!LText.empty(bean.url))
            builder.setUrl(bean.url);
        builder.setTemplateId(bean.templateId);
        return builder.build();
    }

    /**
     * 将ChatJobBean转换为ChatProtocol.TechwolfJobDesc
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfJobDesc changeTechwolfJobDesc(ChatJobBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfJobDesc.Builder builder = ChatProtocol.TechwolfJobDesc.newBuilder();
        if (!LText.empty(bean.title))
            builder.setTitle(bean.title);
        if (!LText.empty(bean.company))
            builder.setCompany(bean.company);
        if (!LText.empty(bean.salary))
            builder.setSalary(bean.salary);
        if (!LText.empty(bean.url))
            builder.setUrl(bean.url);
        builder.setJobId(bean.id);
        if (!LText.empty(bean.positionClassName))
            builder.setPositionCategory(bean.positionClassName);
        if (!LText.empty(bean.experience))
            builder.setExperience(bean.experience);
        if (!LText.empty(bean.education))
            builder.setEducation(bean.education);
        if (!LText.empty(bean.city))
            builder.setCity(bean.city);
        if (!LText.empty(bean.bossPositionName))
            builder.setBossTitle(bean.bossPositionName);
        if (!LText.empty(bean.lid))
            builder.setLid(bean.lid);
        ChatProtocol.TechwolfUser boss = changeTechwolfUser(bean.bossInfo);
        if (boss != null)
            builder.setBoss(boss);
        if (!LText.empty(bean.bottomText))
            builder.setBottomText(bean.bottomText);
        if (!LText.empty(bean.jobLabel))
            builder.setJobLabel(bean.jobLabel);
        if (!LText.empty(bean.distance)) {
            builder.setDistance(bean.distance);
        }
        if (!LText.empty(bean.latlon)) {
            builder.setLatlon(bean.latlon);
        }
        return builder.build();
    }

    /**
     * 将ChatResumeBean转换为ChatProtocol.TechwolfResume
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfResume changeTechwolfResume(ChatResumeBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfResume.Builder builder = ChatProtocol.TechwolfResume.newBuilder();
        ChatProtocol.TechwolfUser userInfo = changeTechwolfUser(bean.userInfo);
        if (userInfo != null)
            builder.setUser(userInfo);
        builder.setExpectId(bean.id);
        if (!LText.empty(bean.description))
            builder.setDescription(bean.description);
        if (!LText.empty(bean.city))
            builder.setCity(bean.city);
        if (!LText.empty(bean.positionName))
            builder.setPosition(bean.positionName);
        if (!LText.empty(bean.advantage))
            builder.addKeywords(bean.advantage);
        if (!LText.empty(bean.lid))
            builder.setLid(bean.lid);
        if (bean.userInfo != null)
            builder.setGender(bean.userInfo.sex);
        if (!LText.empty(bean.expSalary))
            builder.setSalary(bean.expSalary);
        if (!LText.empty(bean.workAge))
            builder.setWorkYear(bean.workAge);
        if (!LText.empty(bean.firstText))
            builder.setContent1(bean.firstText);
        if (!LText.empty(bean.secondText))
            builder.setContent2(bean.secondText);

        if (!LText.empty(bean.age))
            builder.setAge(bean.age);
        if (bean.labels != null) {
            for (String label : bean.labels) {
                builder.addLabels(label);
            }
        }
        if (bean.experiences != null) {
            for (ChatUserExperience experience : bean.experiences) {
                builder.addExperiences(changeTechwolfUserExperience(experience));
            }
        }
        if (!LText.empty(bean.positionCategory))
            builder.setPositionCategory(bean.positionCategory);
        if (!LText.empty(bean.jobSalary))
            builder.setJobSalary(bean.jobSalary);
        if (!LText.empty(bean.bottomText))
            builder.setBottomText(bean.bottomText);
        if (!LText.empty(bean.applyStatus))
            builder.setApplyStatus(bean.applyStatus);
        if (!LText.empty(bean.jobId))
            builder.setJobId(bean.jobId);

        return builder.build();
    }

    /**
     * 将ChatRedEnvelope转换为ChatProtocol.TechwolfRedEnvelope
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfRedEnvelope changeTechwolfRedEnvelope(ChatRedEnvelopeBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfRedEnvelope.Builder builder = ChatProtocol.TechwolfRedEnvelope.newBuilder();
        builder.setRedId(bean.id);
        if (!LText.empty(bean.text)) {
            builder.setRedText(bean.text);
        }
        if (!LText.empty(bean.title)) {
            builder.setRedTitle(bean.title);
        }
        if (!LText.empty(bean.url)) {
            builder.setClickUrl(bean.url);
        }
        return builder.build();
    }

    /**
     * 将ChatPresenceChat转换为ChatProtocol.TechwolfPresence
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfPresence changeTechwolfPresence(ChatPresenceBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfPresence.Builder builder = ChatProtocol.TechwolfPresence.newBuilder();
        builder.setType(bean.type);
        builder.setUid((int) bean.id);
        ChatProtocol.TechwolfClientInfo clientInfo = changeTechwolfClientInfo(bean.clientInfo);
        if (clientInfo != null)
            builder.setClientInfo(clientInfo);
        ChatProtocol.TechwolfClientTime.Builder builderTime = ChatProtocol.TechwolfClientTime.newBuilder();
        builderTime.setStartTime(bean.startTimer);
        builderTime.setResumeTime(bean.resumeTimer);
        builderTime.setLocateTime(bean.locateTime);
        builder.setClientTime(builderTime);
        builder.setLastMessageId(bean.lastMessageId);
        builder.setLastGroupMessageId(bean.lastGroupMessageId);
        return builder.build();
    }

    /**
     * 将ChatPresenceChat转换为ChatProtocol.TechwolfPresence
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfClientInfo changeTechwolfClientInfo(ChatClientInfoBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfClientInfo.Builder builder = ChatProtocol.TechwolfClientInfo.newBuilder();
        if (!LText.empty(bean.version))
            builder.setVersion(bean.version);
        if (!LText.empty(bean.system))
            builder.setSystem(bean.system);
        if (!LText.empty(bean.systemVersion))
            builder.setSystemVersion(bean.systemVersion);
        if (!LText.empty(bean.model))
            builder.setModel(bean.model);
        if (!LText.empty(bean.uniqid))
            builder.setUniqid(bean.uniqid);
        if (!LText.empty(bean.network))
            builder.setNetwork(bean.network);
        builder.setAppid(bean.appId);
        if (!LText.empty(bean.platform))
            builder.setPlatform(bean.platform);
        if (!LText.empty(bean.channel))
            builder.setChannel(bean.channel);
        if (!LText.empty(bean.ssid))
            builder.setSsid(bean.ssid);
        if (!LText.empty(bean.bssid))
            builder.setBssid(bean.bssid);
        builder.setLongitude(bean.longitude);
        builder.setLatitude(bean.latitude);
        return builder.build();
    }

    /**
     * 将ChatIQBean转换为ChatProtocol.TechwolfIq
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfIq changeTechwolfIq(ChatIQBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfIq.Builder builder = ChatProtocol.TechwolfIq.newBuilder();
        builder.setQid(bean.id);
        if (!LText.empty(bean.query))
            builder.setQuery(bean.query);
        Map<String, String> params = bean.params;
        if (params != null && params.size() > 0) {
            for (String key : params.keySet()) {
                ChatProtocol.TechwolfKVEntry.Builder builderKV = ChatProtocol.TechwolfKVEntry.newBuilder();
                builderKV.setKey(key);
                if (params.containsKey(key)) {
                    builderKV.setValue(params.get(key));
                }
                builder.addParams(builderKV);
            }
        }
        return builder.build();
    }

    /**
     * 将ChatMessageReadBean转换为ChatProtocol.TechwolfMessageRead
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfMessageRead changeTechwolfMessageRead(ChatMessageReadBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfMessageRead.Builder builder = ChatProtocol.TechwolfMessageRead.newBuilder();
        builder.setUserId(bean.userId);
        builder.setMessageId(bean.messageId);
        builder.setReadTime(bean.readTime);
        builder.setSync(bean.sync);
        builder.setUserSource(bean.friendSource);
        return builder.build();
    }

    /**
     * 将ChatHyperLinkBean转化为ChatProtocol.TechwolfHyperLinkOrBuilder
     *
     * @param bean
     * @return
     */
    private ChatProtocol.TechwolfHyperLink changeChatHyperLink(ChatHyperLinkBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfHyperLink.Builder builder = ChatProtocol.TechwolfHyperLink.newBuilder();
        builder.setText(bean.text);
        builder.setHyperLinkType(bean.templateId);
        builder.setUrl(bean.url);
        return builder.build();
    }

    private ChatProtocol.TechwolfVideo changeVideo(ChatVideoBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfVideo.Builder builder = ChatProtocol.TechwolfVideo.newBuilder();
        builder.setType(bean.type);
        builder.setStatus(bean.status);
        builder.setDuration(bean.duration);
        return builder.build();
    }

    private ChatProtocol.TechwolfInterview changeInterview(ChatInterviewBean bean) {
        if (bean == null) return null;
        ChatProtocol.TechwolfInterview.Builder builder = ChatProtocol.TechwolfInterview.newBuilder();
        builder.setCondition(bean.condition);
        if (!LText.empty(bean.text))
            builder.setText(bean.text);
        if (!LText.empty(bean.url))
            builder.setUrl(bean.url);
        if (!LText.empty(bean.extend))
            builder.setExtend(bean.extend);
        return builder.build();
    }

    private ChatProtocol.UserExperience changeTechwolfUserExperience(ChatUserExperience userExperience) {
        if (userExperience == null) return null;
        ChatProtocol.UserExperience.Builder builder = ChatProtocol.UserExperience.newBuilder();
        builder.setEndDate(userExperience.endDate);
        builder.setOrganization(userExperience.organization);
        builder.setOccupation(userExperience.occupation);
        builder.setStartDate(userExperience.startDate);
        builder.setType(userExperience.type);
        return builder.build();
    }

}
