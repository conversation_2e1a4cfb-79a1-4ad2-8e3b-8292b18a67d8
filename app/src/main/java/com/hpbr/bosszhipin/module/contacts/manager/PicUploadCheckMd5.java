package com.hpbr.bosszhipin.module.contacts.manager;


import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.hpbr.bosszhipin.config.URLConfig;
import com.kanzhun.zpcloud.util.StringUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.utils.file.FileUtils;

import net.bosszhipin.api.bean.PicCheckMd5Response;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.Serializable;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * created by yhy
 * date 2022/11/18
 * desc:改造流程就是  在原先直接上传图片之前， 由客户端/pc 先计算一下文件的MD5然后传给服务端，服务端判断该文件是否上传过，如果上传过客户端就直接用服务端返回的url（包含缩略图）即可。
 * 如果接口返回的url为空，那么代表没传过，走原先的上传流程接口。
 */
public class PicUploadCheckMd5 {
    /**
     * 使用 uri
     * @param fileUri
     * @param source
     * @param extraParam
     * @param context
     * @param listener
     */
    public void checkHaveUpload(Uri fileUri, String source, CheckExtraParam extraParam, Context context, CheckPicMd5Listener listener) {

        if (extraParam == null) {
            extraParam = new CheckExtraParam();
        }
        BufferedInputStream bis = null;

        try {
            long startTime = System.currentTimeMillis();
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            bis = new BufferedInputStream(context.getContentResolver().openInputStream(fileUri));
            byte[] buf = new byte[1024];
            int totalLength = 0;
            int len;
            while ((len = bis.read(buf)) != -1) {
                messageDigest.update(buf, 0, len);
                totalLength += len;
            }

            String md5 = StringUtils.toHexString(messageDigest.digest());
            checkMd5(source, extraParam.securityId, extraParam.gid,extraParam.isFile, md5, totalLength, listener);
            long useTime = System.currentTimeMillis() - startTime;
            TLog.info("PicUploadCheckMd5", "md5" + "  " + md5 + " time  "+useTime);
        } catch (Exception e) {
            e.printStackTrace();
            listener.error();
        } finally {
            try {
                if (bis != null) bis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 使用file 路径
     * @param filePath
     * @return
     */
    public void checkHaveUpload(String filePath, String source, CheckExtraParam extraParam, CheckPicMd5Listener listener) {
        if (filePath == null) {
            listener.error();
        } else {
            try {
                File file = new File(filePath);
                if (!FileUtils.isFile(file)) {
                    listener.error();
                } else {
                    FileInputStream fileInputStream = null;

                    MessageDigest messageDigest = MessageDigest.getInstance("MD5");
                    fileInputStream = new FileInputStream(file);
                    byte[] buffer = new byte[1024];
                    int totalLength = 0;
                    int len;
                    while ((len = fileInputStream.read(buffer)) != -1) {
                        messageDigest.update(buffer, 0, len);
                        totalLength += len;
                    }

                    String md5 = StringUtils.toHexString(messageDigest.digest());
                    checkMd5(source, extraParam.securityId, extraParam.gid, extraParam.isFile,md5, totalLength, listener);
                    TLog.info("PicUploadCheckMd5", "md5" + "  " + md5);
                }
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
                listener.error();
            } catch (IOException e) {
                e.printStackTrace();
                listener.error();
            }
        }
    }

    private void checkMd5(String source, String securityId, String gid, boolean isFile,String fileMd5, int fileSize, CheckPicMd5Listener listener) {
        SimpleApiRequest.POST(URLConfig.URL_QUICK_UPLOAD)
                .addParam("source", source)
                .addParam("securityId", securityId)
                .addParam("gid", gid)
                .addParam("fileMd5", fileMd5)
                .addParam("fileSize", fileSize)
                .setRequestCallback(new SimpleApiRequestCallback<PicCheckMd5Response>() {
                    @Override
                    public void onSuccess(ApiData<PicCheckMd5Response> data) {
                        super.onSuccess(data);
                        boolean isNotFileIf = !TextUtils.isEmpty(data.resp.url)
                                && !TextUtils.isEmpty(data.resp.tinyUrl)
                                && data.resp.metadata != null && data.resp.metadata.width != 0
                                && data.resp.metadata.height != 0 && !isFile;
                        boolean isFileIf = !TextUtils.isEmpty(data.resp.url) && isFile;
                        if (isNotFileIf || isFileIf) {
                            listener.success(data.resp);
                        } else {
                            listener.error();
                        }
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        listener.error();
                    }
                }).execute();
    }

    public interface CheckPicMd5Listener {
        void success(PicCheckMd5Response response);

        void error();
    }


    public static class CheckExtraParam implements Serializable {
        private static final long serialVersionUID = -2851201814130314535L;
        private String gid = "";
        private String securityId;
        private boolean isFile = false;

        public CheckExtraParam setGid(String gid) {
            this.gid = gid;
            return this;
        }

        public CheckExtraParam setSecurityId(String securityId) {
            this.securityId = securityId;
            return this;
        }

        public CheckExtraParam setIsFile(boolean isFile) {
            this.isFile = isFile;
            return this;
        }
    }
}