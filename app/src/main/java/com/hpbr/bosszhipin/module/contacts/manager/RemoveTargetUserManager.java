package com.hpbr.bosszhipin.module.contacts.manager;

import android.app.Activity;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2022/9/28
 */

public class RemoveTargetUserManager {

    private ProgressDialog mProgressDialog;

    private final Activity mActivity;

    public RemoveTargetUserManager(Activity mActivity) {
        this.mActivity = mActivity;
    }

    private void showDialog(String value) {
        if (mProgressDialog == null) {
            mProgressDialog = new ProgressDialog(mActivity);
        }
        mProgressDialog.show(value);
    }

    private void dismissDialog() {
        if (!ActivityUtils.isValid(mActivity)) return;
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
        }
    }


    /**
     * 删除联系人操作
     * 系统联系人 friendId小于1000的好友
     * 聚合联系人 通知/学习服务 里面包含 好几个系统的联系人,需要拆分出来挨个删除
     *
     * @param contactBean
     */
    public void removeListener(ContactBean contactBean, boolean isShowProgressDialog) {

        if (isShowProgressDialog) showDialog("删除中");

        long friendId = contactBean.friendId;

        long deleteFriendId = VirtualDeleteManager.getInstance().getDeleteFriendIds(friendId);

        ContactBean handleContact = ContactManager.getInstance().queryContactByFriendId(deleteFriendId, UserManager.getUserRole().get(), ContactBean.FROM_BOSS);
        if (handleContact != null) {
            VirtualDeleteManager.getInstance().saveVirtualDeleteContact(handleContact);
        }

        //刷新联系人
        ContactManager.getInstance().refreshContacts();
        //取消loading
        dismissDialog();
        //Toast提示
        ToastUtils.showText("删除成功");
    }


    public static class VirtualDeleteManager {

        private VirtualDeleteManager() {
            checkInitDeleteFriendIds();
        }

        private final static VirtualDeleteManager instance = new VirtualDeleteManager();

        public static VirtualDeleteManager getInstance() {
            return instance;
        }

        //根据当前帐号+身份生成唯一id
        private String createKey() {
            return "SP_DELETE_FRIEND_IDS" + "_" + UserManager.getUID() + "+" + UserManager.getUserRole().get();
        }

        //存放删除好友id集合
        private final List<Long> deleteFriendIds = new ArrayList<>();


        private long getDeleteFriendIds(long friendId) {

            //直播招聘
            if (friendId == MqttConfig.CHAT_LIVING_RECRUIT) {
                return MqttConfig.CHAT_LIVING_RECRUIT;
            }

            //小直同学
            if (friendId == MqttConfig.CHAT_USER_COMMUNICATIONS_OFFICER) {
                return MqttConfig.CHAT_USER_COMMUNICATIONS_OFFICER;
            }


            //猎头服务
            if (friendId == MqttConfig.HUNTER_SERVICE_IF) {
                return MqttConfig.HUNTER_SERVICE_IF;
            }

            //每日新发现
            if (friendId == MqttConfig.CHAT_GET_CIRCLE1) {
                return MqttConfig.CHAT_GET_CIRCLE1;
            }

            //学习服务
            if (friendId == MqttConfig.CHAT_STUDY_SERVICE
                    || friendId == MqttConfig.SYSTEM_TITAN_TAB3_USER_ID
                    || friendId == MqttConfig.SYSTEM_TITAN_TAB2_USER_ID
                    || friendId == MqttConfig.SYSTEM_TITAN_TAB1_USER_ID

            ) {
                return MqttConfig.SYSTEM_TITAN_TAB2_USER_ID;
            }


            //BOSS安全官
            if (friendId == MqttConfig.CHAT_SECURITY_OFFICER) {
                return MqttConfig.CHAT_SECURITY_OFFICER;
            }

            return friendId;
        }


        //保存需要删除联系人id
        public synchronized void saveVirtualDeleteContact(ContactBean contactBean) {
            if (!deleteFriendIds.contains(contactBean.friendId)) {
                deleteFriendIds.add(contactBean.friendId);

                JSONArray jsonArray = new JSONArray();
                for (Long deleteFriendId : deleteFriendIds) {
                    if (deleteFriendId == null) continue;
                    jsonArray.put(deleteFriendId);
                }

                SpManager.get().user().edit().putString(createKey(), jsonArray.toString()).apply();
            }
        }


        //当前登录的联系人信息
        private String currentLoginUserKey;


        //初始化需要删除的联系人ids,每次到消息列表时候需要刷新下
        private synchronized void checkInitDeleteFriendIds() {

            if (LText.empty(currentLoginUserKey)) {
                //第一次启动 ,需要初始化数据
                currentLoginUserKey = createKey();
                initDeleteFriendIds();
            }

            if (!LText.equal(createKey(), currentLoginUserKey)) {
                //富裕新的数据
                currentLoginUserKey = createKey();
                //更换了帐号,需要初始化数据
                initDeleteFriendIds();
            }

        }

        //初始化联系人数据
        private synchronized void initDeleteFriendIds() {
            deleteFriendIds.clear();
            String deleteObject = SpManager.get().user().getString(createKey(), "");
            if (!LText.empty(deleteObject)) {
                try {
                    JSONArray jsonArray = new JSONArray(deleteObject);
                    for (int i = 0; i < jsonArray.length(); i++) {
                        deleteFriendIds.add(jsonArray.getLong(i));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }


        //当前id是否已经删除
        public synchronized boolean isDeleteUser(@Nullable ContactBean contactBean) {
            if (contactBean != null) {

                long deleteFriendIds = getDeleteFriendIds(contactBean.friendId);
                //检查是否需要重新初始化数据
                checkInitDeleteFriendIds();

                return this.deleteFriendIds.contains(deleteFriendIds);
            }
            return false;
        }

        //检测删除的联系人来了新消息,需要解除删除
        public synchronized void checkDeleteUserHasNewMessage(@Nullable ContactBean contactBean) {

            if (contactBean == null) return;

            //检查是否需要重新初始化数据
            checkInitDeleteFriendIds();

            long transferDeleteFriendId = getDeleteFriendIds(contactBean.friendId);


            //已经被删除
            if (deleteFriendIds.contains(transferDeleteFriendId)) {
                //从缓存删除数据里面移除
                deleteFriendIds.remove(transferDeleteFriendId);
                //保存到SP里面
                JSONArray jsonArray = new JSONArray();
                for (Long deleteFriendId : deleteFriendIds) {
                    if (deleteFriendId == null) continue;
                    jsonArray.put(deleteFriendId);
                }
                SpManager.get().user().edit().putString(createKey(), jsonArray.toString()).apply();
                //刷新F2联系人列表
                ContactManager.getInstance().refreshContacts();
            }
        }
    }


} 