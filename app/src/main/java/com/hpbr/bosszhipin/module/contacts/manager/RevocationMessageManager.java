package com.hpbr.bosszhipin.module.contacts.manager;

import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_CUSTOMER_ID;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.chat.export.router.CustomerRouter;
import com.hpbr.bosszhipin.chat.export.routerservice.CustomerService;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.GroupManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactCache;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.chat.ProgressLoading;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.MessageWithDrawRequest;
import net.bosszhipin.api.SuccessResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.lang.ref.SoftReference;
import java.util.List;

import message.handler.MessageUtils;
import message.handler.dao.MessageDao;
import message.handler.dao.MessageDaoFactory;

/**
 * Created by guofeng
 * on 2018/11/14.
 * 消息撤回
 */

public class RevocationMessageManager {


    public static final int TWO_MINUTES_TIME_SECONDS = 1000 * 60 * 2;
    public static final int TEN_MINUTES_TIME_SECONDS = 1000 * 60 * 10;
    public static final String REVERT_SEND_TEXT = "你撤回了一条消息";

    public static final String REVERT_RESUME = "附件简历请求已撤回";
    public static final String REVERT_RECEIVE_TEXT = "撤回了一条消息";
    public static final String RE_EDIT = "重新编辑";




    private SoftReference<Context> softReference;

    private ProgressLoading progressLoading;

    private OnRevocationMessageCallBack onRevocationMessageCallBack;

    private final MessageDao messageDao = MessageDaoFactory.getMessageDao();

    public RevocationMessageManager(Context mContext, OnRevocationMessageCallBack onRevocationMessageCallBack) {
        softReference = new SoftReference<>(mContext);
        this.onRevocationMessageCallBack = onRevocationMessageCallBack;
        progressLoading = new ProgressLoading(softReference.get());
    }

    /**
     * 判断是否可以撤回
     * 文本、图片、表情（包）、语音消息可撤回；
     *
     * @param chatBean
     * @return
     */
    public static boolean checkCanRevocation(ChatBean chatBean, boolean isDianZhang) {
        if (chatBean == null
                || chatBean.message == null
                || chatBean.message.messageBody == null)
            return false;
        boolean hasSyncMsgId = chatBean.msgId > 0;
        boolean isSendRead = chatBean.status == 1 || chatBean.status == 3;
        boolean textType = chatBean.message.messageBody.type == 1;
        boolean audioType = chatBean.message.messageBody.type == 2;
        boolean imageType = chatBean.message.messageBody.type == 3;
        boolean gifType = chatBean.message.messageBody.type == 20;
        //智慧石 人工才可以撤回
        boolean supportCustomerType = true;
        if ((chatBean.toUserId == CHAT_CUSTOMER_ID ||
                (chatBean.message.toUser != null &&
                        chatBean.message.toUser.id == CHAT_CUSTOMER_ID))) {
            CustomerService customerChatService = CustomerRouter.getChatRouterService();
            long startChatTime = customerChatService != null ? customerChatService.getStartChatTime() : 0L;
            if (chatBean.clientTempMessageId < startChatTime || startChatTime <= 0) {
                supportCustomerType = false;
            }
        }
        return hasSyncMsgId && (isSendRead || textType || audioType || imageType || gifType) && !isDianZhang && supportCustomerType;
    }


    /**
     * 聊天撤回文案
     *
     * @param bean
     * @param textView
     */
    public static void setChatRevocationText(ChatBean bean, MTextView textView, View.OnClickListener listener) {
        if (bean.status == 4) {
            String text = TextUtils.isEmpty(bean.message.revocationText) && bean.message.messageBody != null
                    ? bean.message.messageBody.text
                    : bean.message.revocationText;
            int startIndex;
            if (text != null && (startIndex = text.indexOf(RE_EDIT)) >= 0) {

                //超过10分不显示 重新编辑
                if (System.currentTimeMillis() - bean.time > TEN_MINUTES_TIME_SECONDS) {
                    bean.message.revocationText = text.substring(0, startIndex);
                    textView.setText(bean.message.revocationText);
                    AppThreadFactory.POOL.execute(new Runnable() {
                        @Override
                        public void run() {
                            MessageDaoFactory.getMessageDao().update(bean);
                        }
                    });
                } else {//10分之内显示 重新编辑
                    SpannableString spannableString = new SpannableString(text);
                    spannableString.setSpan(new ClickableSpan() {
                        @Override
                        public void updateDrawState(TextPaint ds) {
                            ds.setUnderlineText(false);
                            ds.setColor(ContextCompat.getColor(App.getAppContext(), R.color.color_BOSS7));
                        }

                        @Override
                        public void onClick(View widget) {
                            if (listener != null) {
                                listener.onClick(widget);
                            }
                        }
                    }, startIndex, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                    textView.setText(spannableString);
                    textView.setHighlightColor(Color.TRANSPARENT);//解决TextView设置ClickSpan后，点击高亮部分出现背景色的问题
                    textView.setMovementMethod(LinkMovementMethod.getInstance());
                }

            } else {
                textView.setText(text);
            }
        }
    }

    /**
     * f2联系人获得文案
     *
     * @param chatBean
     * @return
     */
    public static String getContactLastMessage(ChatBean chatBean) {
        String text = chatBean.message.revocationText;
        if (TextUtils.isEmpty(text)) {
            boolean isMeRevertMessage = chatBean.fromUserId == UserManager.getUID();
            if (isMeRevertMessage) {//我撤回消息
                text = REVERT_SEND_TEXT;
            } else {//好友撤回消息
                if (chatBean.fromUserId == CHAT_CUSTOMER_ID) {//智慧石头 客服名字需要去接口缓存获取,消息里面没有用户名
                    long customerId = LText.getLong(chatBean.message.bizId);
                    CustomerService customerChatService = CustomerRouter.getChatRouterService();
                    String customerName = "直直" + (customerChatService != null ? customerChatService.getCustomerName(customerId) : "");
                    text =  "\"" + customerName + "\"" + REVERT_RECEIVE_TEXT;
                } else {//普通消息 user里面有好友名字
                    String fromUserName = chatBean.message.fromUser.name;
                    if (TextUtils.isEmpty(fromUserName)) { //离线消息 没有 from 和 to
                        long friendId = chatBean.fromUserId == UserManager.getUID() ? chatBean.toUserId : chatBean.fromUserId;
                        ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, AccountHelper.getIdentity(), 0);
                        if (contactBean != null) {
                            fromUserName = contactBean.friendName;
                        }
                    }
                    text ="\"" + fromUserName + "\"" + REVERT_RECEIVE_TEXT;
                }
            }
        }
        int index = text.indexOf(RevocationMessageManager.RE_EDIT);
        if (index != -1) {
            return text.substring(0, index);
        }
        return text;
    }

    /**
     * 接受到回撤消息
     */
    public static void onReceiverMessageRevocation(Context context, MessageDao messageDao, long messageId, boolean isReceived) {
        //根据msgId找到最初的消息
        ChatBean chatBean = messageDao.queryUserChatByMsgId(messageId, UserManager.getUID());

        if (chatBean != null) {
            boolean unReadChat = chatBean.status == 1; //未读
            if (TextUtils.isEmpty(chatBean.message.revocationText)) {
                chatBean.status = 4;
                if (chatBean.domain == 2) {//群聊
                    // 群Id
                    long groupId = chatBean.toUserId;
                    //是否我撤回的消息
                    boolean isMeRevertMessage = chatBean.fromUserId == UserManager.getUID();
                    if (isMeRevertMessage) {//我撤回消息
                        chatBean.message.revocationText = REVERT_SEND_TEXT;
                    } else {//群聊好友撤回消息
                        chatBean.message.revocationText = "\"" + chatBean.message.fromUser.name + "\"" + REVERT_RECEIVE_TEXT;
                    }
                    GroupInfoBean groupBean = GroupManager.getInstance().getGroupInfo(groupId);
                    if (groupBean != null) {
                        //是否更新最后联系人最后一个消息
                        if (shouldUpdateGroupChatLastMessage(chatBean, messageDao, groupId)) {
                            groupBean.lastChatText = chatBean.message.revocationText;
                        }
                        //保存到数据库
                        GroupManager.getInstance().insertOrUpdateAllFileWithFresh(groupBean);
                    }

                    //通知撤回消息id
                    RevocationMessageObserver.getInstance().postRevocationMessage(chatBean.msgId);

                } else {//单聊
                    //好友Id
                    long friendId = chatBean.fromUserId == UserManager.getUID() ? chatBean.toUserId : chatBean.fromUserId;
                    ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(friendId, AccountHelper.getIdentity(), 0);
                    //是否我撤回的消息
                    boolean isMeRevertMessage = chatBean.fromUserId == UserManager.getUID();
                    String lastChatText;
                    if (isMeRevertMessage) {//我撤回消息
                        //撤回文本,可以重新编辑
                        if (chatBean.message.messageBody.type == 1
                                && chatBean.message.messageBody.templateId == 1
                                && !isReceived
                                && (System.currentTimeMillis() - chatBean.time < TEN_MINUTES_TIME_SECONDS)) {
                            chatBean.message.revocationText = REVERT_SEND_TEXT + " " + RE_EDIT;
                        } else {//非文本类型的撤回,不可修改
                            chatBean.message.revocationText = REVERT_SEND_TEXT;
                        }
                        lastChatText = REVERT_SEND_TEXT;
                    } else {//好友撤回消息
                        if (chatBean.fromUserId == CHAT_CUSTOMER_ID) {//智慧石头 客服名字需要去接口缓存获取,消息里面没有用户名
                            long customerId = LText.getLong(chatBean.message.bizId);
                            CustomerService customerChatService = CustomerRouter.getChatRouterService();
                            String customerName = "直直" + (customerChatService != null ? customerChatService.getCustomerName(customerId) : "");
                            lastChatText = chatBean.message.revocationText = "\"" + customerName + "\"" + REVERT_RECEIVE_TEXT;
                        } else {//普通消息 user里面有好友名字
                            String fromUserName = chatBean.message.fromUser.name;
                            if (TextUtils.isEmpty(fromUserName)) { //离线消息 没有 from 和 to
                                if (contactBean != null) {
                                    fromUserName = contactBean.friendName;
                                }
                            }
                            lastChatText = chatBean.message.revocationText = "\"" + fromUserName + "\"" + REVERT_RECEIVE_TEXT;
                        }
                    }

                    //通知撤回消息id
                    RevocationMessageObserver.getInstance().postRevocationMessage(chatBean.msgId);

                    if (contactBean != null) {
                        //是否更新最后联系人最后一个消息
                        if (shouldUpdateSingleChatLastMessage(chatBean, messageDao, friendId)) {
                            contactBean.lastChatText = lastChatText;
                        }
                        if (contactBean.friendId <= 1000) { //系统通知 撤回 还原 最后一条消息
                            contactBean.lastChatText = RevocationMessageManager.getLastChatTextRevert(chatBean, contactBean, unReadChat);
                        }
                        // 保存到数据库
                        ContactManager.getInstance().updateLastText(contactBean);
                        //刷新F2最后一条消息聊天内容
                        ContactManager.getInstance().refreshContacts();
                    }
                }

                AppThreadFactory.POOL.execute(new Runnable() {
                    @Override
                    public void run() {
                        //保存到数据库
                        messageDao.update(chatBean);
                        //刷新聊天页面单聊|群聊
                        final Intent intent = new Intent(Constants.RECEIVER_MESSAGE_REVOCATION);
                        intent.putExtra(Constants.DATA_LONG, messageId);
                        ReceiverUtils.sendBroadcastSystem(context, intent);
                    }
                });
            }
        } else {
            TLog.debug(TAG, "onReceiverMessageRevocation: 没有找到Id= %d 的撤回消息", messageId);
        }
    }

    /**
     * 消息删除 联系人 显示lastText
     *
     * @param chatBean
     * @return
     */
    public static void uploadDelMessageContactLastText(@Nullable ChatBean chatBean) {
        if (chatBean == null) return;
        if (chatBean.fromUserId <= 1000) {
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(chatBean.fromUserId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
            if (contactBean != null) {
                contactBean.lastChatText = RevocationMessageManager.getLastChatTextRevert(chatBean, contactBean, chatBean.status == 1);
                ContactManager.getInstance().updateLastText(contactBean);
            }
        }
    }

    /**
     * 消息回滚 显示lastText
     *
     * @param chatBean
     * @param contactBean
     * @param unReadChat
     * @return
     */
    private static String getLastChatTextRevert(ChatBean chatBean, ContactBean contactBean, boolean unReadChat) {
        //获取该联系人的最近15条数据
        ChatBean lastChatBean = null;
        String messageDescLastText = null;
        List<ChatBean> messageValid = MessageDaoFactory.getMessageDao().getNotifyLastMessageValid(contactBean.friendId);
        if (LList.isNotEmpty(messageValid)) {
            for (ChatBean bean : messageValid) {
                if (bean.msgId > chatBean.msgId) {
                    lastChatBean = bean;
                    break;
                }
                if (bean.msgId == chatBean.msgId) {
                    continue;
                }
                //获取下一个消息的 消息内容
                String messageDescText = MessageUtils.getMessageDescText(bean, null);
                // 防止出现不显示 在F2中的 数据
                if (!TextUtils.isEmpty(messageDescText)) {
                    lastChatBean = bean;
                    break;
                }
            }
        }
        if (lastChatBean != null) {
            messageDescLastText = MessageUtils.getMessageDescText(lastChatBean, contactBean.lastChatText);
            contactBean.lastChatTime = lastChatBean.time;
            contactBean.lastChatText = messageDescLastText;
            if (contactBean.noneReadCount > 0 && unReadChat) {
                contactBean.noneReadCount--;
            }
        } else {
            contactBean.lastChatTime = 0;
            contactBean.updateTime = 0;
            contactBean.noneReadCount = 0;
        }
        ContactManager.getInstance().initContactData(contactBean);
        return messageDescLastText;
    }

    private static final String TAG = "chat";

    public void doRevocationListener(ChatBean chatBean, int domain, boolean isInTwoMinute) {
        if (isInTwoMinute && System.currentTimeMillis() - chatBean.time > TWO_MINUTES_TIME_SECONDS) {
            ToastUtils.showText("消息发送已经超过2分钟，不能撤回");
            return;
        }
        MessageWithDrawRequest request = new MessageWithDrawRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                Context context = softReference.get();
                if (context == null) return;
                if (context instanceof Activity) {
                    if (ActivityUtils.isValid((Activity) context)) {
                        progressLoading.show();
                    }
                }
            }


            @Override
            public void handleInChildThread(ApiData<SuccessResponse> data) {

                //通知撤回消息id
                RevocationMessageObserver.getInstance().postRevocationMessage(chatBean.msgId);

                //以及是撤回消息
                if (chatBean.status == 4) return;

                chatBean.status = 4;

                //撤回文本,可以重新编辑
                if (chatBean.message.messageBody.type == 1 && chatBean.message.messageBody.templateId == 1) {
                    chatBean.message.revocationText = REVERT_SEND_TEXT + " " + RE_EDIT;
                } else {//非文本类型的撤回,不可修改

                    if (chatBean.message.messageBody.type == 12) {
                        chatBean.message.revocationText = REVERT_RESUME;
                    } else {
                        chatBean.message.revocationText = REVERT_SEND_TEXT;
                    }
                }

                if (domain == 2) {//群聊
                    GroupInfoBean groupInfoBean = GroupManager.getInstance().getGroupInfo(chatBean.toUserId);
                    if (groupInfoBean != null) {
                        groupInfoBean.lastChatText = MessageUtils.getGroupChatLastMessage(chatBean, groupInfoBean, messageDao);
                        GroupManager.getInstance().insertOrUpdateAllFile(groupInfoBean);
                    }
                } else {//单聊天
                    ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(chatBean.toUserId, UserManager.getUserRole().get(), ContactCache.DEFAULT_FRIEND_SOURCE);
                    if (contactBean != null) {
                        contactBean.lastChatText = MessageUtils.getSingleChatLastText(chatBean, contactBean, true);
                        ContactManager.getInstance().updateLastText(contactBean);
                        if (LText.empty(contactBean.lastChatText)) {
                            TLog.info(TAG, " RevocationMessageManager lastChatText is empty");
                        }
                    }
                }
                ContactManager.getInstance().refreshContacts();


                messageDao.update(chatBean);
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {

                if (onRevocationMessageCallBack != null) {
                    onRevocationMessageCallBack.onRevocationListener();
                }
            }

            @Override
            public void onComplete() {
                Context context = softReference.get();
                if (context == null) return;
                if (context instanceof Activity) {
                    if (ActivityUtils.isValid((Activity) context)) {
                        progressLoading.dismiss();
                    }
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.messageId = chatBean.msgId;
        request.domain = domain;
        HttpExecutor.execute(request);
    }

    public void doRevocationListener(ChatBean chatBean, int domain) {
        doRevocationListener(chatBean, domain, true);
    }

    public void doRevocationResumePreview(ChatBean chatBean, int domain) {

        MessageWithDrawRequest request = new MessageWithDrawRequest(new ApiRequestCallback<SuccessResponse>() {

            @Override
            public void onStart() {
                super.onStart();
                Context context = softReference.get();
                if (context == null) return;
                if (context instanceof Activity) {
                    if (ActivityUtils.isValid((Activity) context)) {
                        progressLoading.show();
                    }
                }
            }

            @Override
            public void handleInChildThread(ApiData<SuccessResponse> data) {
                //通知撤回消息id
                RevocationMessageObserver.getInstance().postRevocationMessage(chatBean.msgId);
                //撤回简历 服务器下发97消息 去更新ChatBean: 客户端不需要去修改
            }

            @Override
            public void onSuccess(ApiData<SuccessResponse> data) {
            }

            @Override
            public void onComplete() {
                Context context = softReference.get();
                if (context == null) return;
                if (context instanceof Activity) {
                    if (ActivityUtils.isValid((Activity) context)) {
                        progressLoading.dismiss();
                    }
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        request.messageId = chatBean.msgId;
        request.domain = domain;
        HttpExecutor.execute(request);

    }

    private static boolean shouldUpdateGroupChatLastMessage(ChatBean chatBean, MessageDao messageDao, long groupId) {
        long id = messageDao.queryMaxGroupChatId(groupId);
        return chatBean.id >= id;
    }

    private static boolean shouldUpdateSingleChatLastMessage(ChatBean chatBean, MessageDao messageDao, long friendId) {
        long id = messageDao.queryMaxSingleChatId(friendId);
        return chatBean.id >= id;
    }


    /**
     * 撤回消息回掉
     */
    public interface OnRevocationMessageCallBack {
        void onRevocationListener();
    }

}
