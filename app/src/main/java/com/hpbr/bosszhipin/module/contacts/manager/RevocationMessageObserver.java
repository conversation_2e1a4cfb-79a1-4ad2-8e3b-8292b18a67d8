package com.hpbr.bosszhipin.module.contacts.manager;

import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

/**
 * create by guofeng
 * date on 2023/2/23
 * 注册监控撤回消息
 */
public class RevocationMessageObserver {

    private RevocationMessageObserver() {}

    private static final RevocationMessageObserver instance=new RevocationMessageObserver();

    public static RevocationMessageObserver getInstance() {
        return instance;
    }


    private final MutableLiveData<Long> revocationMessageList = new MutableLiveData<>();


    public void registerObserver(LifecycleOwner lifecycle, Observer<Long> observer) {
        revocationMessageList.observe(lifecycle, observer);
    }


    public void postRevocationMessage(long msgId) {
        revocationMessageList.postValue(msgId);
    }


} 