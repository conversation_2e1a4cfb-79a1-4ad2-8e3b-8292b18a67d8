package com.hpbr.bosszhipin.module.contacts.manager;

import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_NOTIFY_ID;
import static com.hpbr.bosszhipin.config.MqttConfig.CHAT_SYSTEM_MESSAGE_USER_ID;

import android.app.Activity;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.monch.lbase.dialog.ProgressDialog;
import com.monch.lbase.util.LList;
import com.monch.lbase.widget.T;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.ActivityUtils;

import net.bosszhipin.api.SetTopContactRequest;
import net.bosszhipin.api.SetTopContactResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * create by guofeng
 * date on 2022/9/28
 */

public class TopUnTopRequestManager {

    private ProgressDialog mProgressDialog;

    private final Activity mActivity;

    public TopUnTopRequestManager(Activity mActivity) {
        this.mActivity = mActivity;
    }

    private void showDialog(String value) {
        if (mProgressDialog == null) {
            mProgressDialog = new ProgressDialog(mActivity);
        }
        mProgressDialog.show(value);
    }

    private void dismissDialog() {
        if (!ActivityUtils.isValid(mActivity)) return;
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
        }
    }


    private long convertFriendId(ContactBean contactBean) {

        long friendId = contactBean.friendId;
        //通知
        if (friendId == CHAT_SYSTEM_MESSAGE_USER_ID || friendId == CHAT_NOTIFY_ID) {
            return CHAT_SYSTEM_MESSAGE_USER_ID;
        }

        //直播招聘
        if (friendId == MqttConfig.CHAT_LIVING_RECRUIT) {
            return MqttConfig.CHAT_LIVING_RECRUIT;
        }

        //猎头服务
        if (friendId == MqttConfig.HUNTER_SERVICE_IF) {
            return MqttConfig.HUNTER_SERVICE_IF;
        }
        //每日新发现
        if (friendId == MqttConfig.CHAT_GET_CIRCLE1) {
            return MqttConfig.CHAT_GET_CIRCLE1;
        }

        //学习服务
        if (friendId == MqttConfig.CHAT_STUDY_SERVICE || friendId == MqttConfig.SYSTEM_TITAN_TAB2_USER_ID) {
            return MqttConfig.SYSTEM_TITAN_TAB2_USER_ID;
        }

        //BOSS安全官
        if (friendId == MqttConfig.CHAT_SECURITY_OFFICER) {
            return MqttConfig.CHAT_SECURITY_OFFICER;
        }

        return friendId;
    }


    public void setFriendTop(ContactBean bean, @Nullable Runnable runnable, boolean isShowProgressDialog) {

        int count = LList.getCount(F2ContactHelper.getInstance().getF2TopList(true));
        if (count >= 60) {
            T.ss("您置顶的人数已达60人上限，请先取消置顶");
            return;
        }

        ContactBean handleContact = getHandleContact(bean);

        if (handleContact == null) return;

        if (isShowProgressDialog) showDialog("正在置顶");

        SetTopContactRequest setTopContactRequest = new SetTopContactRequest(new ApiRequestCallback<SetTopContactResponse>() {
            @Override
            public void handleInChildThread(ApiData<SetTopContactResponse> data) {
                super.handleInChildThread(data);
                handleContact.isTop = true;
                handleContact.updateTime = System.currentTimeMillis();
                ContactManager.getInstance().updateTopFriend(handleContact);
            }

            @Override
            public void onSuccess(ApiData<SetTopContactResponse> data) {
                ContactManager.getInstance().refreshContacts();
                if (runnable != null) runnable.run();
            }

            @Override
            public void onComplete() {
                dismissDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        setTopContactRequest.friendId = String.valueOf(handleContact.friendId);
        setTopContactRequest.isTop = "1";
        setTopContactRequest.friendSource = handleContact.friendSource;
        setTopContactRequest.securityId = handleContact.securityId;
        HttpExecutor.execute(setTopContactRequest);

    }

    private ContactBean getHandleContact(ContactBean bean) {
        long friendId = convertFriendId(bean);
        return ContactManager.getInstance().queryContactByFriendId(friendId, UserManager.getUserRole().get(), ContactBean.FROM_BOSS);
    }

    public void cancelFriendTop(ContactBean bean, @Nullable Runnable runnable, boolean isShowProgressDialog) {

        if (isShowProgressDialog) showDialog("正在取消置顶");

        ContactBean handleContact = getHandleContact(bean);

        if (handleContact == null) return;

        SetTopContactRequest setTopContactRequest = new SetTopContactRequest(new ApiRequestCallback<SetTopContactResponse>() {

            @Override
            public void handleInChildThread(ApiData<SetTopContactResponse> data) {
                super.handleInChildThread(data);
                handleContact.isTop = false;
                ContactManager.getInstance().updateTopFriend(handleContact);
            }

            @Override
            public void onSuccess(ApiData<SetTopContactResponse> data) {
                ContactManager.getInstance().refreshContacts();
                if (runnable != null) runnable.run();
            }

            @Override
            public void onComplete() {
                dismissDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }
        });
        setTopContactRequest.friendId = String.valueOf(handleContact.friendId);
        setTopContactRequest.isTop = "0";
        setTopContactRequest.friendSource = handleContact.friendSource;
        setTopContactRequest.securityId = handleContact.securityId;
        HttpExecutor.execute(setTopContactRequest);

    }

} 