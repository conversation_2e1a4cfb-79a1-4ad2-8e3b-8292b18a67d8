package com.hpbr.bosszhipin.module.contacts.service;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;

import com.hpbr.bosszhipin.common.DownloadPhotoCommon;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.contacts.entity.AdvertBean;
import com.hpbr.bosszhipin.module.contacts.entity.manager.AdvertBeanManager;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by monch on 15/5/22.
 */
public class AdvertService extends Service {
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * 启动独占式广告服务
     *
     * @param context
     */
    public static void startupAdvertService(Context context) {
        AppUtil.startService(context, AdvertService.class);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        new Thread(myRunnable).start();
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isSend = true;
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private Timer timer;

    private AdvertBean advertBean;

    private boolean isSend = false;

    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            switch (msg.what) {
                case 0:
                    // 销毁服务
                    if (timer != null) {
                        timer.cancel();
                        timer = null;
                    }
                    try {
                        stopSelf();
                    } catch (Exception e){
                        L.e(e.getMessage());
                    }
                    break;
                case 1:
                    // 启动计时器，寻找对话框弹出时机
                    timer = new java.util.Timer();
                    long T = 2000;
                    timer.schedule(new MyTimerTask(), T, T);
                    break;
                case 2:
                    // 发送通知，通知MainActivity应该弹出独占广告
                    if (!checkAdvertValid()) {
                        handler.sendEmptyMessage(0);
                        break;
                    } else if (MainActivity.mMainActivityIsTopping
                            && advertBean != null
                            && advertBean.photoDownloadComplete) {
                        isSend = true;
                        Intent intent = new Intent();
                        intent.setAction(Constants.RECEIVER_ADVERT_SHOW_ACTION);
                        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
                        intent.putExtra(Constants.DATA_ENTITY, advertBean);
                        AdvertService.this.sendBroadcast(intent);
                        handler.sendEmptyMessage(0);
                    }
                    break;
                default:
                    break;
            }
            return true;
        }
    });

    private Runnable myRunnable = new Runnable() {
        @Override
        public void run() {
            advertBean = AdvertBeanManager.getInstance().getAdvertBean();
            if (!checkAdvertValid()) {
                handler.sendEmptyMessage(0);
                return;
            }
            loadImage();
            handler.sendEmptyMessage(1);
        }
    };

    private void loadImage() {
        if (!advertBean.photoDownloadComplete) {
            DownloadPhotoCommon downloadPhotoCommon = new DownloadPhotoCommon();
            downloadPhotoCommon.setOnDownloadCallback(new DownloadPhotoCommon.OnDownloadCallback() {
                @Override
                public void onDownloadComplete(Bitmap bitmap) {
                    if (bitmap != null) {
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                if (advertBean == null) return;
                                advertBean.photoDownloadComplete = true;
                                AdvertBeanManager.getInstance().save(advertBean);
                            }
                        }).start();
                    }
                }

                @Override
                public void onDownloadFailed() {
                }
            });
            downloadPhotoCommon.onNewDownloadTask(advertBean.photoUrl);
        }
    }

    private class MyTimerTask extends TimerTask {

        private int count = 0;

        @Override
        public void run() {
            if (isSend) return;
            count++;
            if (!checkAdvertValid()) {
                handler.sendEmptyMessage(0);
                return;
            }
            if (count % 10 == 0) {
                loadImage();
            }
            if (MainActivity.mMainActivityIsTopping && advertBean.photoDownloadComplete) {
                handler.sendEmptyMessage(2);
            }
        }
    }

    private boolean checkAdvertValid() {
        return advertBean != null && !advertBean.isShowed && !LText.empty(advertBean.descText)
                && advertBean.startTime < System.currentTimeMillis()
                && advertBean.endTime > System.currentTimeMillis();
    }

}
