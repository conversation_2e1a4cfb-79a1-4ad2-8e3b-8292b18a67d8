package com.hpbr.bosszhipin.module.contacts.service;

import static com.hpbr.bosszhipin.protocol.ProtocolConstants.Chat.TYPE_OPEN_COMMON_WORDS;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatReaderBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatClientInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatDialogButtonBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGifImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatHyperLinkBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatIQBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatJobBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageReadBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatPresenceBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatVideoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.startup.process.Processer;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.utils.NotifyUtils;
import com.hpbr.bosszhipin.utils.permission.LocationPermissionHelper;
import com.monch.lbase.util.L;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.monch.lbase.util.Scale;
import com.techwolf.lib.tlog.TLog;
import com.twl.mms.ApmAction;
import com.twl.utils.GsonUtils;
import com.twl.utils.NetworkUtils;

import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.api.bean.ServerTranslatedPoiAddressBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import message.handler.MSGSender;
import message.handler.MessageTargetInfo;
import message.handler.dao.MessageDaoFactory;
import message.server.MSGManager;
import message.server.sender.MessageModel;


/**
 * Created by monch on 15/6/10.
 */
public class ChatBeanFactory {


    public static final int SEND_LOCATION = 111;
    public static final int ACCEPT_LOCATION = 112;
    public static final int REJECT_LOCATION = 113;
    // 推荐职位信息消息type，兼容新老页面设置为-1
    public static final int CHAT_GEEK_RECOMMEND_JOB = -1;
    // 给牛人推荐的职位信息消息templateId，仅本地使用不入库。
    public static final int RECOMMEND_JOB_TEMPLATE_ID = 10001;


    private static ChatBeanFactory instance = new ChatBeanFactory();

    public static ChatBeanFactory getInstance() {
        return instance;
    }

    //牛人点击同意或者拒绝 地理位置
    public static void sendAcceptOrRejectLocationToAction(long toUserId,int toUserSource, long msgId,
                                                          long jobId, int actionType, int index, ChatSendCallback callback) {
        //发送地理的action处理业务
        MSGSender sender = new MSGSender();
        sender.sendMapToGeek(toUserId, msgId, jobId, actionType, callback);


        //为了解决牛人 同意||拒绝 地理位置 发送俩个action消息导致同步mid出现问题
        App.get().getMainHandler().postDelayed(() -> {
            // 发送第一个按钮点击事件 用于处理按钮变灰
            ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(toUserId, UserManager.getUserRole().get(), toUserSource);
            if (contactBean == null) {
                return;
            }
            MessageTargetInfo messageTargetInfo = MessageTargetInfo.fromContactBean(contactBean);

            ChatBean sendChatBean = getInstance().createDialogClick(messageTargetInfo, toUserSource, msgId, index);
            MessageModel model = new MessageModel();
            model.setSendChatBean(sendChatBean);
            model.setBackChatBean(sendChatBean);
            model.setMesageTargetInfo(messageTargetInfo);
            model.setCallback(null);
            MSGManager.get().sendMessage(model);
        }, 10);

    }

    /**
     * 创建一条文字消息
     *
     * @param text
     * @return
     */
    public ChatBean createText(MessageTargetInfo targetInfo, String text, int friendSource) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        bean.message.toUser.friendSource = friendSource;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 1;
        messageBodyBean.templateId = 1;
        messageBodyBean.title = "";
        messageBodyBean.text = text;
        return bean;
    }

    /**
     * 创建一条文字消息
     *
     * @param text
     * @return
     */
    public ChatBean createText(MessageTargetInfo targetInfo, String text) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 1;
        messageBodyBean.templateId = 1;
        messageBodyBean.title = "";
        messageBodyBean.text = text;
        return bean;
    }


    /**
     * 创建一条假文本消息
     *
     * @param targetInfo
     * @param type
     * @param templateId
     * @param text
     * @param extend
     * @return
     */
    public ChatBean createFalseText(MessageTargetInfo targetInfo,
                                    int type,
                                    int templateId,
                                    String text, String extend) {
        if (targetInfo == null) return null;
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = 0;
        bean.status = 1;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = type;
        messageBodyBean.templateId = templateId;
        messageBodyBean.text = text;
        messageBodyBean.extend = extend;
        return bean;
    }


    //虚拟电话卡片
    public ChatBean createVirtualPhoneMessage(MessageTargetInfo targetInfo, long friendId, int friendSource, String friendPhone) {
        if (targetInfo == null) return null;
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 7;
        messageBodyBean.dialog = new ChatDialogBean();
        messageBodyBean.dialog.type = 32;
        messageBodyBean.dialog.clickMore = true;
        messageBodyBean.dialog.text = "已成功交换联系方式，临时号码请勿保存";
        messageBodyBean.dialog.buttons = new ArrayList<>();
        ChatDialogButtonBean dialogButtonBean = new ChatDialogButtonBean();
        dialogButtonBean.text = "拨号";
        //bosszp://bosszhipin.app/openwith?type=virtualCall&friendId=1525361&friendSource=0&friendPhone=#hdhhhdhh73733133377663
        dialogButtonBean.url = "bosszp://bosszhipin.app/openwith?type=virtualCall&friendId=" + friendId + "&friendSource=" + friendSource + "&friendPhone=" + friendPhone;
        messageBodyBean.dialog.localResourceId = R.mipmap.ic_exchange_virtual;
        messageBodyBean.dialog.buttons.add(dialogButtonBean);
        return bean;
    }


    /**
     * 创建一条声音消息
     *
     * @param targetInfo
     * @param url
     * @param duration
     * @param friendSource
     * @return
     */
    public ChatBean createSound(MessageTargetInfo targetInfo, String filePath, String url, int duration, int friendSource) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 2;
        messageBodyBean.templateId = 1;
        ChatSoundBean soundBean = getDefaultChatSoundBean();
        messageBodyBean.sound = soundBean;
        messageBean.toUser.friendSource = friendSource;
        soundBean.localUrl = filePath;
        soundBean.url = url;
        soundBean.duration = duration;
        return bean;
    }

    /**
     * 创建一条声音消息
     *
     * @param targetInfo
     * @param url
     * @param duration
     * @param friendSource
     * @return
     */
    public ChatBean createBulletSound(MessageTargetInfo
                                              targetInfo,
                                      String message,
                                      String filePath,
                                      String url,
                                      int duration,
                                      boolean isGroup, int friendSource) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        messageBean.type = isGroup ? 2 : 1;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBean.messageBody.text = message;
        messageBean.toUser.friendSource = friendSource;
        messageBodyBean.type = 2;
        messageBodyBean.templateId = 2;
        ChatSoundBean soundBean = getDefaultChatSoundBean();
        messageBodyBean.sound = soundBean;
        soundBean.localUrl = filePath;
        soundBean.url = url;
        soundBean.duration = duration;
        return bean;
    }


    public ChatBean createMultiplyPhoto(MessageTargetInfo targetInfo, List<ChatImageInfoBean> urlList) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.time = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 27;
        messageBodyBean.templateId = 1;
        messageBodyBean.multiplyImage = new ArrayList<>();
        if (urlList != null) {
            for (ChatImageInfoBean itemBean : urlList) {
                if (itemBean == null) continue;
                ChatImageInfoBean item = new ChatImageInfoBean();
                item.url = itemBean.url;
                item.width = Scale.dip2px(App.getAppContext(), 140);
                item.height = Scale.dip2px(App.getAppContext(), 140);
                messageBodyBean.multiplyImage.add(item);
            }
        }
        return bean;
    }

    /**
     * 创建一条图片消息
     *
     * @param targetInfo
     * @param url
     * @param width
     * @param height
     * @param tinyUrl
     * @param tWidth
     * @param tHeight
     * @return
     */
    public ChatBean createPhoto(MessageTargetInfo targetInfo,
                                String url,
                                int width,
                                int height,
                                String tinyUrl,
                                int tWidth,
                                int tHeight) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.time = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 3;
        messageBodyBean.templateId = 1;
        ChatImageBean imageBean = getDefaultChatImageBean();
        messageBodyBean.image = imageBean;
        imageBean.tinyImage = getDefaultChatImageInfoBean(tinyUrl, tWidth, tHeight);
        imageBean.originImage = getDefaultChatImageInfoBean(url, width, height);
        return bean;
    }


    /**
     * 发送gif图片到服务
     *
     * @param packageId
     * @param emotionId
     * @param origin
     * @param tiny
     * @return
     */
    public ChatBean createGif(MessageTargetInfo targetInfo,String encSid,long packageId, long emotionId, ChatImageInfoBean origin, ChatImageInfoBean tiny, String name) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        bean.time = System.currentTimeMillis();
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 20;
        messageBodyBean.templateId = 1;
        messageBodyBean.gifImageBean = getDefaultChatGifImageBean();
        messageBodyBean.gifImageBean.name = name;
        messageBodyBean.gifImageBean.encSid = encSid;
        messageBodyBean.gifImageBean.packageId = packageId;
        messageBodyBean.gifImageBean.emotionId = emotionId;
        //下面不需要传,,服务区只需要packageId和formatId
        messageBodyBean.gifImageBean.image = getDefaultChatImageBean();
        messageBodyBean.gifImageBean.image.tinyImage = getDefaultChatImageInfoBean(tiny.url, tiny.width, tiny.height);
        messageBodyBean.gifImageBean.image.originImage = getDefaultChatImageInfoBean(origin.url, origin.width, origin.height);
        return bean;
    }


    /**
     * 创建一条事件消息
     *
     * @param targetInfo
     * @param actionType
     * @return
     */
    public ChatBean createAction(MessageTargetInfo targetInfo, int actionType, String extend) {

        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);

        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;

        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = actionType;
        actionBean.extend = extend;
        return bean;
    }


    /**
     * 注：有一些消息没有 messageBean.toUser.friendSource 后续需要补全
     * @param targetInfo
     * @return
     */
    public ChatBean createGrayText(MessageTargetInfo targetInfo, String textValue) {

        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);

        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;

        /*添加friendSource*/
        if (messageBean.toUser != null) {
            ContactBean contactBean = targetInfo.getContactBean();
            if (contactBean != null) {
                messageBean.toUser.friendSource = contactBean.friendSource;
            }
        }

        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBodyBean.text = textValue;

        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 1;
        messageBodyBean.templateId = 3;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = 3;
        return bean;
    }

    /**
     * 创建一条按钮点击事件消息
     *
     * @param targetInfo
     * @param index
     * @return
     */
    public ChatBean createDialogClick(MessageTargetInfo targetInfo, String extend, long msgId, int index) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = 20;


        if (LText.empty(extend)) {
            actionBean.extend = "{\"msg_id\":" + msgId + ",\"action\":" + index + "}";
        } else {
            try {
                JSONObject jsonObject = new JSONObject(extend);
                if (!jsonObject.has("msg_id")) {
                    jsonObject.put("msg_id", msgId);
                }

                if (!jsonObject.has("action")) {
                    jsonObject.put("action", index);
                }

                actionBean.extend = jsonObject.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return bean;
    }

    /**
     * 创建一条按钮点击事件消息
     *
     * @param targetInfo
     * @param index
     * @return
     */
    /**
     * 创建一条按钮点击事件消息
     *
     * @param targetInfo
     * @param index
     * @return
     */
    public ChatBean createDialogClick(MessageTargetInfo targetInfo, int friendSource, long msgId, int index) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBean.toUser.friendSource = friendSource;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = 20;
        actionBean.extend = "{\"msg_id\":" + msgId + ",\"action\":" + index + "}";
        return bean;
    }

    public ChatBean createDialogClick(MessageTargetInfo targetInfo, long msgId, int index) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = 20;
        actionBean.extend = "{\"msg_id\":" + msgId + ",\"action\":" + index + "}";
        return bean;
    }


    /**
     * Boss身份发送 地理位置到牛人端
     *
     * @param messageTargetInfo
     * @param msgId
     * @param jobId
     * @return
     */
    public ChatBean createActionBean(MessageTargetInfo messageTargetInfo, long msgId, long jobId, int actionType) {
        ChatBean bean = getDefaultChatBean(messageTargetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(messageTargetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = actionType;
        actionBean.extend = "{\"msg_id\":" + msgId + ",\"jobId\":" + jobId + "}";
        return bean;
    }

    public ChatBean createLocationActionBean(MessageTargetInfo messageTargetInfo, long msgId, long jobId) {
        ChatBean bean = getDefaultChatBean(messageTargetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(messageTargetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = SEND_LOCATION;
        actionBean.extend = "{\"msg_id\":" + msgId + ",\"jobId\":" + jobId + "}";
        return bean;
    }

    public ChatBean createLocationActionBean(MessageTargetInfo messageTargetInfo, ServerTranslatedPoiAddressBean poiAddressBean) {
        ChatBean bean = getDefaultChatBean(messageTargetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(messageTargetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null || messageBean.toUser == null) return null;
        messageBean.toUser.friendSource = messageTargetInfo.getContactBean().friendSource;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = SEND_LOCATION;
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("longitude", poiAddressBean.poiLongitude);
            jsonObject.put("latitude", poiAddressBean.poiLatitude);
            jsonObject.put("locationDesc", poiAddressBean.poiTitle);
            jsonObject.put("city", poiAddressBean.poiCity);
            jsonObject.put("cityCode", poiAddressBean.poiCityCode);
            jsonObject.put("geoId", poiAddressBean.geoId);

            jsonObject.put("province", poiAddressBean.poiProvince);
            jsonObject.put("street", poiAddressBean.poiStreet);
            jsonObject.put("addressNum", poiAddressBean.snippet);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        actionBean.extend = jsonObject.toString();
        return bean;
    }

    /**
     * 创建一条按钮点击事件消息牛人 同意获取简历
     *
     * @param targetInfo
     * @return
     */
    public ChatBean createGeekAgreeResumeDialogClick(MessageTargetInfo targetInfo, String extendField) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = 38;
        actionBean.extend = extendField;
        return bean;
    }


    /**
     * 创建一条按钮点击事件动作的消息
     *
     * @param actionId
     * @param toUserId
     * @param extend
     * @return
     */
    public ChatBean createDialogClickAction(MessageTargetInfo targetInfo,
                                            int actionId,
                                            long toUserId,
                                            String extend) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        messageBean.toUser.id = toUserId;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 4;
        messageBodyBean.templateId = 1;
        ChatActionBean actionBean = getDefaultChatActionBean();
        messageBodyBean.action = actionBean;
        actionBean.type = actionId;
        actionBean.extend = extend;
        return bean;
    }

    /**
     * 创建一个视频消息
     *
     * @param type
     * @param status
     * @param duration
     * @return
     */
    public ChatBean createVideo(MessageTargetInfo targetInfo, int type, int status, int duration) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 13;
        messageBodyBean.templateId = 1;
        ChatVideoBean videoBean = getDefaultVideoBean();
        messageBodyBean.videoBean = videoBean;
        videoBean.type = type;
        videoBean.status = status;
        videoBean.duration = duration;
        return bean;
    }

    /**
     * 获取一个默认的消息
     *
     * @return
     */
    private ChatBean getDefaultChatBean(MessageTargetInfo targetInfo, int msgType) {
        ChatBean bean = new ChatBean();
        bean.msgId = 0;
        bean.sortMsgId = getSortMessageId();
        bean.domain = 1;
        bean.time = System.currentTimeMillis();
        bean.clientTempMessageId = ChatBeanFactory.getClientMessageId();
        bean.myUserId = UserManager.getUID();
        bean.myRole = UserManager.getUserRole().get();
        bean.fromUserId = UserManager.getUID();
        bean.toUserId = targetInfo.targetId;
        bean.msgType = msgType;
        bean.version = MqttConfig.PROTOCOL_VERSION;
        return bean;
    }


    /**
     * 获取一个默认的聊天消息
     *
     * @param targetInfo
     * @param msgId
     * @return
     */
    private ChatMessageBean getDefaultChatMessageBean(MessageTargetInfo targetInfo, long msgId, long clientTempId) {
        ChatMessageBean messageBean = new ChatMessageBean();
        ChatUserBean fromUser = getSendFromUserBean();
        if (fromUser == null) {
            return null;
        }
        messageBean.fromUser = fromUser;
        ChatUserBean toUser = getSendToUserBean(targetInfo);
        if (toUser == null) {
            return null;
        }
        messageBean.toUser = toUser;
        messageBean.type = 1;
        messageBean.id = msgId;
        messageBean.clientTempMessageId = clientTempId;
        messageBean.time = System.currentTimeMillis();
        messageBean.isOffline = false;
        messageBean.status = 2;
        return messageBean;
    }

    /**
     * 获取一个默认的聊天消息体
     *
     * @return
     */
    private ChatMessageBodyBean getDefaultChatMessageBodyBean() {
        return new ChatMessageBodyBean();
    }

    /**
     * 获取一个默认的声音消息体
     *
     * @return
     */
    private ChatSoundBean getDefaultChatSoundBean() {
        return new ChatSoundBean();
    }

//    /**
//     * 获取一个默认的职位信息
//     *
//     * @return
//     */
//    private ChatJobBean getDefaultChatJobBean() {
//        ChatJobBean chatJobBean = new ChatJobBean();
//        UserBean bean = UserManager.getLoginUser();
//        if (bean != null && bean.bossInfo != null) {
//            chatJobBean.bossInfo = new ChatUserBean();
//            chatJobBean.bossInfo.id = bean.id;
//            chatJobBean.bossInfo.name = bean.name;
//            chatJobBean.company = bean.bossInfo.company;
//            chatJobBean.bossPositionName = bean.bossInfo.positionDesc;
//            chatJobBean.bossInfo.headDefaultImageIndex = bean.bossInfo.headDefaultImageIndex;
//            chatJobBean.bossInfo.avatar = bean.avatar;
//            if (bean.bossInfo.brandList != null && bean.bossInfo.brandList.size() > 0) {
//                chatJobBean.stage = bean.bossInfo.brandList.get(0).stageName;
//            }
//        }
//        return chatJobBean;
//    }

    private ChatGifImageBean getDefaultChatGifImageBean() {
        return new ChatGifImageBean();
    }

    /**
     * 获取一个默认的图片消息体
     *
     * @return
     */
    private ChatImageBean getDefaultChatImageBean() {
        return new ChatImageBean();
    }

    /**
     * 获取一个默认的事件消息
     *
     * @return
     */
    private ChatActionBean getDefaultChatActionBean() {
        return new ChatActionBean();
    }

    /**
     * 获取一个默认的视频消息
     *
     * @return
     */
    private ChatVideoBean getDefaultVideoBean() {
        return new ChatVideoBean();
    }

    /**
     * 获取一个图片信息消息体
     *
     * @param url
     * @param width
     * @param height
     * @return
     */
    public ChatImageInfoBean getDefaultChatImageInfoBean(String url, int width, int height) {
        ChatImageInfoBean imageInfoBean = new ChatImageInfoBean();
        imageInfoBean.url = url;
        imageInfoBean.width = width;
        imageInfoBean.height = height;
        return imageInfoBean;
    }

    /**
     * 获取一个发送方用户信息实例
     *
     * @return
     */
    private ChatUserBean getSendFromUserBean() {
        long id = UserManager.getUID();
        UserBean loginUserBean = UserManager.getLoginUser();
        if (loginUserBean == null) return null;
        ChatUserBean bean = new ChatUserBean();
        bean.id = id;
        bean.name = loginUserBean.name;
//        bean.avatar = loginUserBean.avatar;
//        if (UserManager.getUserRole() == ROLE.BOSS && loginUserBean.bossInfo != null) {
//            bean.company = loginUserBean.bossInfo.company;
//            bean.headDefaultImageIndex = loginUserBean.bossInfo.headDefaultImageIndex;
//        } else if (UserManager.getUserRole() == ROLE.GEEK && loginUserBean.geekInfo != null) {
//            bean.company = "";
//            bean.headDefaultImageIndex = loginUserBean.geekInfo.headDefaultImageIndex;
//        }
        return bean;
    }

    /**
     * 获取一个接收方用户信息实例
     *
     * @return
     */
    private ChatUserBean getSendToUserBean(MessageTargetInfo targetInfo) {
        if (targetInfo == null) return null;
        ChatUserBean bean = new ChatUserBean();
        bean.id = targetInfo.targetId;
//        bean.name = contactBean.friendName;
//        bean.avatar = contactBean.friendDefaultAvatar;
//        bean.headDefaultImageIndex = contactBean.friendDefaultAvatarIndex;
        return bean;
    }

//    /**
//     * 根据聊天消息获取一个联系人
//     *
//     * @param messageBean
//     * @return
//     */
//    public ContactBean getMesageTargetInfo(ChatMessageBean messageBean, long uid, int role) {
//        if (messageBean == null) return null;
//        ChatUserBean fromUser = messageBean.fromUser;
//        ChatUserBean toUser = messageBean.toUser;
//        if (fromUser == null || toUser == null) {
//            return null;
//        }
//        ContactBean contactBean = new ContactBean();
//        contactBean.myId = uid;
//        contactBean.myRole = role;
//        if (fromUser.id == contactBean.myId) {
//            ContactBean temp =
//                    ContactManager
//                            .getInstance()
//                            .queryContactByFriendId(messageBean.toUser.id, contactBean.myRole);
//            if (temp != null) {
//                contactBean = temp;
//            }
//            contactBean.friendId = toUser.id;
//            contactBean.friendName = toUser.name;
//            contactBean.friendDefaultAvatarIndex = toUser.headDefaultImageIndex;
//            contactBean.friendDefaultAvatar = toUser.avatar;
//            if (!TextUtils.isEmpty(toUser.company)) {
//                if (role == ROLE.GEEK.get()) {
//                    contactBean.bossCompanyName = toUser.company;
//                } else {
//                    contactBean.geekPositionName = toUser.company;
//                }
//            }
//        } else if (toUser.id == contactBean.myId) {
//            ContactBean temp = ContactManager
//                    .getInstance()
//                    .queryContactByFriendId(messageBean.fromUser.id, contactBean.myRole);
//            if (temp != null) {
//                contactBean = temp;
//            }
//            else {
//                contactBean.isPassive = true;
//            }
//            contactBean.friendId = fromUser.id;
//            contactBean.friendName = fromUser.name;
//            contactBean.friendDefaultAvatarIndex = fromUser.headDefaultImageIndex;
//            contactBean.friendDefaultAvatar = fromUser.avatar;
//            if (!TextUtils.isEmpty(fromUser.company)) {
//                if (role == ROLE.GEEK.get()) {
//                    contactBean.bossCompanyName = fromUser.company;
//                } else {
//                    contactBean.geekPositionName = fromUser.company;
//                }
//            }
//        } else {
//            contactBean = null;
//        }
//        return contactBean;
//    }

    /**
     * 获取一个上线状态消息实体
     *
     * @return
     */
    public ChatBean createPresenceBean(int type) {
        long uid = UserManager.getUID();   // 这里设置用户ID
        if (uid < 0) {
            return null;
        }
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_PRESENCE; // 2代表presence消息
        ChatPresenceBean presenceBean = new ChatPresenceBean();
        chatBean.presence = presenceBean;
        presenceBean.id = uid;
        presenceBean.type = type;    // 1代表上线消息，4代表切换后台，5代表切换前台
        presenceBean.startTimer = SP.get().getLong(Constants.SP_RECORD_START_TIMER_KEY);
        presenceBean.resumeTimer = SP.get().getLong(Constants.SP_RECORD_RECOVERY_TIMER_KEY);
        presenceBean.locateTime = LocationService.getLocationTime();
        long lastMessageId = MessageDaoFactory.getMaxMessageId();
        long lastGroupMessageId = MessageDaoFactory.getMaxGroupMessageId();
        if (!Processer.isMainProcess()) {//主进程使用maxmsgid，mms使用notifyid(如果notifyid > messageID)
            long temp = NotifyUtils.getNotifyId();
            if (temp > lastMessageId) {
                TLog.info("createPresenceBean", "temp = [%d], lastMessageId = [%d]", temp, lastMessageId);
                lastMessageId = temp;
            }

            if (temp > lastGroupMessageId) {
                TLog.info("createPresenceBean", "temp = [%d], lastGroupMessageId = [%d]", temp, lastGroupMessageId);
                lastGroupMessageId = temp;
            }
        }

        boolean hasPullFlag = (type & MqttConfig.CHAT_TYPE_PRESENCE_PULL) != 0;
        if (hasPullFlag && NetworkUtils.hasNetwork(App.getAppContext())) {
            long repairLastMaxMessageId = getRepairLastMessageId(lastMessageId);
            if (repairLastMaxMessageId > 0) {
                lastMessageId = repairLastMaxMessageId;
            }
        }
        TLog.info("createPresenceBean", "lastMessageId = [%d] [%d]", lastMessageId, lastGroupMessageId);
        presenceBean.lastMessageId = lastMessageId;
        presenceBean.lastGroupMessageId = lastGroupMessageId;
        ChatClientInfoBean clientInfoBean = new ChatClientInfoBean();
        presenceBean.clientInfo = clientInfoBean;
        clientInfoBean.version = MobileUtil.getVersion();
        clientInfoBean.system = "Android";
        clientInfoBean.systemVersion = MobileUtil.getSystemVersion();
        clientInfoBean.model = MobileUtil.getDeviceType();
        String uniqid = MobileUtil.getUniqId(App.get().getContext()); // 设备唯一标识
        if (TextUtils.isEmpty(uniqid)) {
            uniqid = "0";
        }
        clientInfoBean.uniqid = uniqid;
        ConnectivityManager connectManager = (ConnectivityManager) App.get().getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info = null;
        try {
            info = connectManager == null ? null : connectManager.getActiveNetworkInfo();
        } catch (Exception e) {
            L.e(e.getMessage());
        }
        String netType = "unknow";
        if (info != null) {
            if (info.getType() == ConnectivityManager.TYPE_WIFI) {
                netType = "WIFI";
            } else if (info.getType() == ConnectivityManager.TYPE_MOBILE) {
                netType = "3G";
            }
        }
        clientInfoBean.network = netType;
        clientInfoBean.appId = PackageConfigContants.PACKAGE_APP_ID;
        clientInfoBean.platform = "Android";
        clientInfoBean.channel = MobileUtil.getChannel();
        if (LocationPermissionHelper.isLocPer == 1 && AndroidDataStarGray.getInstance().isUploadWifiInfo()) { // 有权限之后才能调用
            clientInfoBean.ssid = NetworkUtils.getWifiSSID(App.get());
            clientInfoBean.bssid = NetworkUtils.getWifiBSSID(App.get());
        }
        clientInfoBean.longitude = LocationService.getLongitude();
        clientInfoBean.latitude = LocationService.getLatitude();
        return chatBean;
    }

    private long getRepairLastMessageId(long lastMessageId) {
        long finalLastMessageId = lastMessageId;
        if (MessageDaoFactory.getRepairMsgId() > 0) {
            finalLastMessageId = Math.min(MessageDaoFactory.getRepairMsgId(), finalLastMessageId);
            TLog.info("createPresenceBean", "【消息储存丢失】修复消息, repairMsgId = [%d]", finalLastMessageId);
        }

        if (MessageDaoFactory.getProcessLostMessageMaxId() > 0) {
            long lostMessageId = MessageDaoFactory.getProcessLostMessageMaxId();
            finalLastMessageId = Math.min(lostMessageId, finalLastMessageId);
            if (finalLastMessageId > 0) {
                ApmAnalyzer.create().action(ApmAction.ACTION_MQTT_MONITOR, ApmAction.TYPE_MONITOR_RECEIVER_LOST_FIX)
                        .p2(String.valueOf(lostMessageId))
                        .report();
            }
            TLog.info("createPresenceBean", "【跨进程丢失】修复消息, repairMsgId = [%d]", finalLastMessageId);
        }

        if (finalLastMessageId <= lastMessageId) {
            MessageDaoFactory.removeRepairMsgId();
        }

        return finalLastMessageId;
    }

    /**
     * 获取一个同步历史消息的实体
     *
     * @param friendUserId 好友ID
     * @param firstMsgId   与好友最早的历史消息的MSG ID
     * @return
     */
    public ChatBean createSyncHistoryMessageBean(long friendUserId, long firstMsgId) {
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = friendUserId;
        iq.query = MqttConfig.CHAT_HISTORY_RESPONSE;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("msg_id", firstMsgId + "");
        params.put("uid", friendUserId + "");
        return chatBean;
    }

    /**
     * 获取一个推荐牛人历史记录的消息的实例
     *
     * @param shareId 推荐牛人的用户ID
     * @param page    页数
     * @return
     */
    public ChatBean createRecommendHistoryMessageBean(long shareId, int page) {
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = shareId;
        iq.query = MqttConfig.CHAT_RECOMMEND_HISTORY_RESPONSE;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("page", page + "");
        params.put("shareId", shareId + "");
        return chatBean;
    }

    /**
     * 获得查询NLP对应的聊天实体bean
     *
     * @param qid          时间戳
     * @param fromId       自己的id
     * @param scene
     * @param toId         好友的id
     * @param messageId    当前如果有聊天,当前聊天的消息id,否则传0
     * @param friendSource
     * @return
     */
    public ChatBean createMessageSuggestBean(long qid,
                                             long fromId,
                                             String scene,
                                             long toId,
                                             long messageId,
                                             int friendSource) {
        int chatType = UserManager.isBossRole() ? 1 : 0;
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = qid;
        iq.query = MqttConfig.CHAT_MESSAGE_SUGGEST;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("action", "query");
        params.put("from_id", String.valueOf(fromId));
        params.put("to_id", String.valueOf(toId));
        params.put("msg_id", String.valueOf(messageId));
        params.put("chat_type", String.valueOf(chatType));
        params.put("friend_source", String.valueOf(friendSource));
        params.put("scene", scene);
        return chatBean;
    }

    /**
     * 通知服务器清空当前NLP消息
     *
     * @param qid          时间戳
     * @param fromId       自己的id
     * @param toId         好友的id
     * @param messageId    每一组NLP下发时候对应一个message_id
     * @param type         1、协议跳转：value为跳转地址
     *                     2、发送文本：value为消息文本
     *                     3、交换电话：value为空
     *                     4、交换微信：value为空
     *                     5、请求简历：value为空
     *                     6、发送简历：value为空
     *                     7、预约面试：value为建议面试时间戳
     * @param friendSource
     * @return
     */
    public ChatBean createCleanMessageSuggestBean(long qid, long fromId,
                                                  long toId, long messageId, String type,
                                                  int friendSource, String resident) {
        int chatType = UserManager.isBossRole() ? 0 : 1;
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = qid;
        iq.query = MqttConfig.CHAT_MESSAGE_SUGGEST;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("action", "accept");
        params.put("from_id", String.valueOf(fromId));
        params.put("to_id", String.valueOf(toId));
        params.put("msg_id", String.valueOf(messageId));
        params.put("chat_type", String.valueOf(chatType));
        params.put("type", String.valueOf(type));
        params.put("friend_source", friendSource + "");
        // 和具体业务有关系，服务器需要根据具体业务决定是否关闭nlp消息
        if (!LText.empty(resident)) {
            params.put("resident", resident);
        }
        return chatBean;
    }

    /**
     * 点击聊天顶部 【交换电话】【微信】【简历】清空NLP
     *
     * @param qid
     * @param fromId
     * @param toId
     * @param type
     * @param friendSource
     * @return
     */

    public ChatBean createCleanNLPMessageBean(long qid, long fromId, long toId, String type, int friendSource) {
        int chatType = UserManager.isBossRole() ? 0 : 1;
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = qid;
        iq.query = MqttConfig.CHAT_MESSAGE_SUGGEST;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("action", "submit");
        params.put("from_id", String.valueOf(fromId));
        params.put("to_id", String.valueOf(toId));
//        params.put("msg_id", String.valueOf(messageId));
        params.put("chat_type", String.valueOf(chatType));
        params.put("type", String.valueOf(type));
        params.put("friend_source", friendSource + "");
        return chatBean;
    }

    /**
     * 用户点击 删除 NLP按钮  ,点击之后 服务器不会向该用户推送nlp消息
     *
     * @param qid
     * @param fromId
     * @param toId
     * @param messageId
     * @param friendSource
     * @return
     */
    public ChatBean createDeleteNlpSuggestBean(long qid, long fromId, long toId, long messageId,
                                               String suggestions, String resident, int friendSource) {
        int chatType = UserManager.isBossRole() ? 0 : 1;
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_IQ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatIQBean iq = new ChatIQBean();
        chatBean.iq = iq;
        iq.id = qid;
        iq.query = MqttConfig.CHAT_MESSAGE_SUGGEST;
        Map<String, String> params = new HashMap<>();
        iq.params = params;
        params.put("action", "delete");
        params.put("from_id", String.valueOf(fromId));
        params.put("to_id", String.valueOf(toId));
        params.put("msg_id", String.valueOf(messageId));
        params.put("chat_type", String.valueOf(chatType));
        params.put("type", suggestions);
        params.put("friend_source", friendSource + "");
        // 和具体业务有关系，服务器需要根据具体业务决定是否关闭nlp消息
        if (!LText.empty(resident)) {
            params.put("resident", resident);
        }
        return chatBean;
    }

    /**
     * 获取一个消息已读的消息实体
     *
     * @param bean
     * @return
     */
    public ChatBean createMessageReadBean(ChatReaderBean bean, int friendSource) {
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatMessageReadBean read = new ChatMessageReadBean();
        if (chatBean.messageRead == null) {
            chatBean.messageRead = new ArrayList<>();
        }
        read.friendSource = friendSource;
        chatBean.messageRead.add(read);
        read.userId = bean.friendUserId;
        read.messageId = bean.messageId;
        read.readTime = bean.readerTime;
        return chatBean;
    }

    /**
     * 获得发送消息实体
     *
     * @param bean
     * @return
     */
    public ChatBean createMessageReadBean(List<ChatReaderBean> bean) {
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        if (chatBean.messageRead == null) {
            chatBean.messageRead = new ArrayList<>();
        }

        for (ChatReaderBean item : bean) {
            ChatMessageReadBean read = new ChatMessageReadBean();
            read.userId = item.friendUserId;
            read.messageId = item.messageId;
            read.readTime = item.readerTime;
            read.friendSource = item.friendSource;
            chatBean.messageRead.add(read);
        }

        return chatBean;
    }


    /**
     * 创建一个添加常用语bean对象
     *
     * @param text
     * @return
     */
    public ChatBean createChatCommonBean(String text) {
        //生成添加常用语提示
        Map<String, String> commonWord = new HashMap<>();
        commonWord.put("text", text);
        String url = ZPManager.UrlHandler.makeProtocol(TYPE_OPEN_COMMON_WORDS, commonWord);
        //创建灰色高亮卡片类型数据
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = 1;
        chatBean.msgId = 0;
        chatBean.sortMsgId = getSortMessageId();
        chatBean.domain = 1;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        chatBean.myUserId = UserManager.getUID();
        chatBean.myRole = UserManager.getUserRole().get();
        chatBean.fromUserId = UserManager.getUID();
        chatBean.clientTempMessageId = ChatBeanFactory.getClientMessageId();
        chatBean.messageSendTime = 0;
        chatBean.status = 1;
        chatBean.message = new ChatMessageBean();
        chatBean.message.messageBody = new ChatMessageBodyBean();
        chatBean.message.messageBody.type = 12;
        chatBean.message.messageBody.hyperLinkBean = new ChatHyperLinkBean();
        chatBean.message.messageBody.hyperLinkBean.templateId = 6;
        chatBean.message.messageBody.hyperLinkBean.text = "以上内容您经常发送，可添加为常用语";
        chatBean.message.messageBody.hyperLinkBean.url = url;
        chatBean.message.messageBody.hyperLinkBean.highlightStart = 11;
        chatBean.message.messageBody.hyperLinkBean.highlightLength = 6;
        return chatBean;
    }

    // 记录上次获取的 cmid 为了解决 同时发送时 cmid 一致问题
    private static long lastClientMessageId = System.currentTimeMillis();

    public static synchronized long getClientMessageId() {
        long clientMessageId = System.currentTimeMillis();
        if (clientMessageId <= lastClientMessageId) {
            return ++lastClientMessageId;
        }
        return lastClientMessageId = clientMessageId;
    }

    private long getSortMessageId() {
        return MessageDaoFactory.getMaxMessageId() + 1;
    }

    public ChatBean createGeekRecommendJobsChatBean(String title, String popTitle, @NonNull List<ServerJobCardBean> jobCardBeanList) {
        String jobListStr = GsonUtils.toJson(jobCardBeanList);
        ChatBean chatBean = new ChatBean();
        if (TextUtils.isEmpty(jobListStr)) {
            TLog.debug("createGeekRecommendJobs", "jobListStr is null");
            return chatBean;
        }

        chatBean.messageSendTime = System.currentTimeMillis();
        chatBean.status = 0;
        chatBean.msgType = MqttConfig.CHAT_TYPE_MESSAGE;
        chatBean.message = new ChatMessageBean();
        chatBean.message.fromUser = getSendFromUserBean();
        chatBean.message.toUser = new ChatUserBean();

        chatBean.message.messageBody = new ChatMessageBodyBean();
        // 在新老2套页面下，templateId用以识别推荐职位本地消息
        chatBean.message.messageBody.templateId = RECOMMEND_JOB_TEMPLATE_ID;
        chatBean.message.messageBody.type = CHAT_GEEK_RECOMMEND_JOB;
        chatBean.message.messageBody.job = new ChatJobBean();
        chatBean.message.messageBody.job.title = title;
        chatBean.message.messageBody.job.bottomText = popTitle;
        chatBean.message.messageBody.job.extend = jobListStr;

        return chatBean;
    }
}
