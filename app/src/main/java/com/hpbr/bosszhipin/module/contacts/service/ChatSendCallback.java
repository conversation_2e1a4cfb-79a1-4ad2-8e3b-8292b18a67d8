package com.hpbr.bosszhipin.module.contacts.service;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

import message.handler.MessageTargetInfo;

/**
 * Created by monch on 15/6/10.
 */
public abstract class ChatSendCallback {

    /**
     * 消息保存到本地的回调
     * @param sendChatBean
     */
    public void onSaveLocation(MessageTargetInfo targetInfo, ChatBean sendChatBean) { }

    /**
     * 消息开始发送的回调
     * @param contactBean
     * @param sendChatBean
     */
    public void onStartSend(ContactBean contactBean, ChatBean sendChatBean) { }

    /**
     * 消息发送完成回调
     *
     * @param success      是否成功
     * @param backChatBean 处理后的消息
     */
    public abstract void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean);

}
