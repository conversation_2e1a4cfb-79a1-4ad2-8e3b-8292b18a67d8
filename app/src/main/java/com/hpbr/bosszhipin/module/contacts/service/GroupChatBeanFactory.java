package com.hpbr.bosszhipin.module.contacts.service;

import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.ChatReaderBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatAtBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatGifImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageBodyBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatMessageReadBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatUserBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;

import java.util.ArrayList;
import java.util.List;

import message.handler.MessageTargetInfo;
import message.handler.dao.MessageDaoFactory;


/**
 * Created by wangtian on 2018/5/3.
 */
public class GroupChatBeanFactory {

    private static GroupChatBeanFactory instance = new GroupChatBeanFactory();

    public static GroupChatBeanFactory getInstance() {
        return instance;
    }

    /**
     * 创建一条群聊文字消息
     *
     * @param text
     * @return
     */
    public ChatBean createText(MessageTargetInfo targetInfo, String text, List<Long> atList) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 1;
        messageBodyBean.templateId = 1;
        messageBodyBean.title = "";
        messageBodyBean.text = text;
        if (atList != null && atList.size() > 0) {
            messageBodyBean.atBean = new ChatAtBean();
            messageBodyBean.atBean.flag = 0;
            messageBodyBean.atBean.uids = atList;
        }
        return bean;
    }

    /**
     * 获取一个默认的消息
     *
     * @return
     */
    private ChatBean getDefaultChatBean(MessageTargetInfo targetInfo, int msgType) {
        ChatBean bean = new ChatBean();
        bean.msgId = 0;
        bean.sortMsgId = getSortMessageId();
        bean.domain = 2;
        bean.clientTempMessageId = ChatBeanFactory.getClientMessageId();
        bean.myUserId = UserManager.getUID();
        bean.myRole = UserManager.getUserRole().get();
        bean.fromUserId = UserManager.getUID();
        bean.toUserId = targetInfo.targetId;
        bean.msgType = msgType;
        bean.version = MqttConfig.PROTOCOL_VERSION;
        return bean;
    }

    /**
     * 获取一个消息已读的消息实体
     *
     * @param bean
     * @return
     */
    public ChatBean createMessageReadBean(ChatReaderBean bean) {
        ChatBean chatBean = new ChatBean();
        chatBean.msgType = MqttConfig.CHAT_TYPE_MESSAGE_HAS_READ;
        chatBean.version = MqttConfig.PROTOCOL_VERSION;
        ChatMessageReadBean read = new ChatMessageReadBean();
        if (chatBean.messageRead == null) {
            chatBean.messageRead = new ArrayList<>();
        }
        chatBean.messageRead.add(read);
        read.userId = bean.friendUserId;
        read.messageId = bean.messageId;
        read.readTime = bean.readerTime;
        chatBean.domain = 2;
        return chatBean;
    }

    /**
     * 获取一个默认的聊天消息
     *
     * @param targetInfo
     * @param msgId
     * @return
     */
    private ChatMessageBean getDefaultChatMessageBean(MessageTargetInfo targetInfo, long msgId, long clientTempId) {
        ChatMessageBean messageBean = new ChatMessageBean();
        ChatUserBean fromUser = getSendFromUserBean();
        if (fromUser == null) {
            return null;
        }
        messageBean.fromUser = fromUser;
        ChatUserBean toUser = getSendToUserBean(targetInfo);
        if (toUser == null) {
            return null;
        }
        messageBean.toUser = toUser;
        messageBean.type = 2;
        messageBean.id = msgId;
        messageBean.clientTempMessageId = clientTempId;
        messageBean.time = System.currentTimeMillis();
        messageBean.isOffline = false;
        messageBean.status = 2;
        return messageBean;
    }


    /**
     * 获取一个默认的聊天消息体
     *
     * @return
     */
    private ChatMessageBodyBean getDefaultChatMessageBodyBean() {
        return new ChatMessageBodyBean();
    }

    /**
     * 获取一个发送方用户信息实例
     *
     * @return
     */
    private ChatUserBean getSendFromUserBean() {
        long id = UserManager.getUID();
        UserBean loginUserBean = UserManager.getLoginUser();
        if (loginUserBean == null) return null;
        ChatUserBean bean = new ChatUserBean();
        bean.id = id;
        return bean;
    }

    /**
     * 获取一个接收方用户信息实例
     *
     * @return
     */
    private ChatUserBean getSendToUserBean(MessageTargetInfo targetInfo) {
        if (targetInfo == null) return null;
        ChatUserBean bean = new ChatUserBean();
        bean.id = targetInfo.targetId;
        return bean;
    }

    /**
     * 获取一个默认的声音消息体
     *
     * @return
     */
    private ChatSoundBean getDefaultChatSoundBean() {
        return new ChatSoundBean();
    }


    /**
     * 创建一条声音消息
     *
     * @param targetInfo
     * @param url
     * @param duration
     * @return
     */
    public ChatBean createSound(MessageTargetInfo targetInfo, String url, String localPath, int duration) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 2;
        messageBodyBean.templateId = 1;
        ChatSoundBean soundBean = getDefaultChatSoundBean();
        messageBodyBean.sound = soundBean;
        soundBean.url = url;
        soundBean.localUrl = localPath;
        soundBean.duration = duration;
        return bean;
    }

    /**
     * 创建一条声音消息
     *
     * @param targetInfo
     * @param url
     * @param duration
     * @return
     */
    public ChatBean createBulletSound(MessageTargetInfo targetInfo, String url, String message, String localPath, int duration) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBean.messageBody.text = message;
        messageBodyBean.type = 2;
        messageBodyBean.templateId = 2;
        ChatSoundBean soundBean = getDefaultChatSoundBean();
        messageBodyBean.sound = soundBean;
        soundBean.url = url;
        soundBean.localUrl = localPath;
        soundBean.duration = duration;
        return bean;
    }


    /**
     * 获取一个默认的图片消息体
     *
     * @return
     */
    private ChatImageBean getDefaultChatImageBean() {
        return new ChatImageBean();
    }

    /**
     * 获取一个图片信息消息体
     *
     * @param url
     * @param width
     * @param height
     * @return
     */
    private ChatImageInfoBean getDefaultChatImageInfoBean(String url, int width, int height) {
        ChatImageInfoBean imageInfoBean = new ChatImageInfoBean();
        imageInfoBean.url = url;
        imageInfoBean.width = width;
        imageInfoBean.height = height;
        return imageInfoBean;
    }

    public ChatBean createPhoto(MessageTargetInfo targetInfo,
                                String url,
                                int width,
                                int height,
                                String tinyUrl,
                                int tWidth,
                                int tHeight) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 3;
        messageBodyBean.templateId = 1;
        ChatImageBean imageBean = getDefaultChatImageBean();
        messageBodyBean.image = imageBean;
        imageBean.tinyImage = getDefaultChatImageInfoBean(tinyUrl, tWidth, tHeight);
        imageBean.originImage = getDefaultChatImageInfoBean(url, width, height);
        return bean;
    }


    /**
     * 发送gif图片到服务
     *
     * @param encSid
     * @param packageId
     * @param emotionId
     * @param origin
     * @param tiny
     * @return
     */
    public ChatBean createGif(MessageTargetInfo targetInfo, String encSid,long packageId, long emotionId, ChatImageInfoBean origin, ChatImageInfoBean tiny, String name) {
        ChatBean bean = getDefaultChatBean(targetInfo, MqttConfig.CHAT_TYPE_MESSAGE);
        bean.messageSendTime = System.currentTimeMillis();
        bean.status = 0;
        ChatMessageBean messageBean = getDefaultChatMessageBean(targetInfo, bean.msgId, bean.clientTempMessageId);
        if (messageBean == null) return null;
        bean.message = messageBean;
        ChatMessageBodyBean messageBodyBean = getDefaultChatMessageBodyBean();
        messageBean.messageBody = messageBodyBean;
        messageBodyBean.type = 20;
        messageBodyBean.templateId = 1;
        messageBodyBean.gifImageBean = getDefaultChatGifImageBean();
        messageBodyBean.gifImageBean.name = name;
        messageBodyBean.gifImageBean.packageId = packageId;
        messageBodyBean.gifImageBean.encSid = encSid;
        messageBodyBean.gifImageBean.emotionId = emotionId;
        messageBodyBean.gifImageBean.image = getDefaultChatImageBean();
        messageBodyBean.gifImageBean.image.tinyImage = getDefaultChatImageInfoBean(tiny.url, tiny.width, tiny.height);
        messageBodyBean.gifImageBean.image.originImage = getDefaultChatImageInfoBean(origin.url, origin.width, origin.height);
        return bean;
    }


    private ChatGifImageBean getDefaultChatGifImageBean() {
        return new ChatGifImageBean();
    }


    private long getSortMessageId() {
        return MessageDaoFactory.getMaxGroupMessageId() + 1;
    }

}
