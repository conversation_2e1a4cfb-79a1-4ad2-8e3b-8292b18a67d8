package com.hpbr.bosszhipin.module.contacts.service.transfer;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

import java.util.List;

/**
 * Created by monch on 15/5/19.
 * 观察者接口
 */
public interface ChatHistoryObserver {

    /**
     * 消息更新回调方法
     * @param responseId
     * @param hasMore
     * @param data
     */
    void onLoadHistoryComplete(long responseId, boolean hasMore, List<ChatBean> data);

}
