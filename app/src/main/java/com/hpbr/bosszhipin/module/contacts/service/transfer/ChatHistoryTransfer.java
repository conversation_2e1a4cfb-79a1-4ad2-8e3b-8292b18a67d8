package com.hpbr.bosszhipin.module.contacts.service.transfer;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by monch on 15/5/19.
 * 历史消息传输主题
 */
public class ChatHistoryTransfer implements IChatHistoryTransfer {

    private Object lock = new Object();

    // 观察者集合
    private List<ChatHistoryObserver> list;
    // 消息队列
    private volatile LinkedList<TransferData> queue;

    public ChatHistoryTransfer() {
        list = new ArrayList<>();
        queue = new LinkedList<>();
    }

    @Override
    public void register(ChatHistoryObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            list.add(observer);
        }
    }

    @Override
    public void unregister(ChatHistoryObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            list.remove(observer);
        }
    }

    @Override
    public void notifyObservers(final long responseId,
                                final boolean hasMore,
                                final List<ChatBean> data) {
        TransferData d = new TransferData();
        d.responseId = responseId;
        d.hasMore = hasMore;
        d.list = data;
        queue.add(d);
        handler.sendEmptyMessage(0);
    }

    /**
     * 处理消息队列的handler
     */
    private Handler handler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            TransferData data = null;
            if (!queue.isEmpty()) {
                data = queue.remove(0);
            }
            if (data != null) {
                handleNotify(data);
            }
            return true;
        }
    });

    /**
     * 具体处理消息的方法
     * @param data
     */
    private void handleNotify(TransferData data) {
        synchronized (lock) {
            for (ChatHistoryObserver o : list) {
                o.onLoadHistoryComplete(data.responseId, data.hasMore, data.list);
            }
        }
    }

    /**
     * 所传输消息的封装
     */
    private static final class TransferData {
        long responseId;
        boolean hasMore;
        List<ChatBean> list;
    }

}
