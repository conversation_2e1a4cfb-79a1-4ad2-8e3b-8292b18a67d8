package com.hpbr.bosszhipin.module.contacts.service.transfer;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

/**
 * Created by monch on 15/5/30.
 * 聊天消息观察者接口
 *
 * 继承以后，使用ChatMessageFactory.getInstance().createChatTransfer()注册
 */
public interface ChatObserver {

    /**
     * 主题更新回调
     * @param contactBean
     * @param chatBean
     * @return 返回true继续向后执行，返回false不再执行
     */
    boolean onNewChatMessage(ChatBean chatBean);

    void onUpdateMsgId(long localMesasgeId,long mid);

    void onRefreshUI();
}
