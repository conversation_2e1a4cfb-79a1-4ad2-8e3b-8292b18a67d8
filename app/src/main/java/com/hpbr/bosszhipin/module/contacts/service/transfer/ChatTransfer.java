package com.hpbr.bosszhipin.module.contacts.service.transfer;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

import java.util.HashSet;
import java.util.Set;


/**
 * Created by wa<PERSON><PERSON> on 2018/4/26.
 * <p>
 * ChatMessageFactory.getInstance().createChatTransfer()
 */
public class ChatTransfer implements IChatTransfer {
    private final Object lock = new Object();
    private Set<ChatObserver> set;

    public ChatTransfer() {
        set = new HashSet<>();
    }

    @Override
    public void register(ChatObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            set.add(observer);
        }
    }

    @Override
    public void unregister(ChatObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            set.remove(observer);
        }
    }

    @Override
    public boolean onNewChatMessage(ChatBean chatBean) {
        synchronized (lock) {
            for (ChatObserver observer : set) {
                if (observer != null){
                    observer.onNewChatMessage(chatBean);
                }
            }
        }
        return true;
    }

    @Override
    public void onUpdateMsgId(long localMesasgeId, long mid) {
        synchronized (lock) {
            for (ChatObserver observer : set) {
                if (observer == null) continue;
                observer.onUpdateMsgId(localMesasgeId, mid);
            }
        }
    }

    @Override
    public void onRefreshUI() {
        synchronized (lock) {
            for (ChatObserver observer : set) {
                if (observer == null) continue;
                observer.onRefreshUI();
            }
        }
    }

    @Override
    public int size() {
        return set == null ? 0 : set.size();
    }
}
