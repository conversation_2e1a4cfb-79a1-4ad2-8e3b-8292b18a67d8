package com.hpbr.bosszhipin.module.contacts.service.transfer;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 15/8/3.
 */
public class ContactTransfer implements IContactTransfer {

    private Object lock = new Object();

    // 观察者集合
    private List<ContactObserver> list;

    public ContactTransfer() {
        list = new ArrayList<>();
    }

    @Override
    public void register(ContactObserver observer) {
        synchronized (lock) {
            list.add(observer);
        }
    }

    @Override
    public void unregister(ContactObserver observer) {
        synchronized (lock) {
            list.remove(observer);
        }
    }

    @Override
    public void notifyObservers() {
        handler.sendEmptyMessage(0);
    }

    private Handler handler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            synchronized (lock) {
                for (ContactObserver observer : list) {
                    observer.onLastMessageStatusChanged();
                }
            }
            return true;
        }
    });

}
