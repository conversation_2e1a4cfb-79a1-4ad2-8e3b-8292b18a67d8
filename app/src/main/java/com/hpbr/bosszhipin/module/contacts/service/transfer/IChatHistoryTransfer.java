package com.hpbr.bosszhipin.module.contacts.service.transfer;

import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;

import java.util.List;

/**
 * Created by monch on 15/5/25.
 * 历史消息传输主题接口
 */
public interface IChatHistoryTransfer {

    /**
     * 注册一个观察者
     * @param observer
     */
    void register(ChatHistoryObserver observer);

    /**
     * 解除注册一个观察者
     * @param observer
     */
    void unregister(ChatHistoryObserver observer);

    /**
     * 数据更新通知
     * @param responseId
     * @param hasMore
     * @param data
     */
    void notifyObservers(long responseId, boolean hasMore, List<ChatBean> data);

}
