package com.hpbr.bosszhipin.module.contacts.service.transfer;


/**
 * Created by monch on 15/5/30.
 * 聊天消息传输主题接口
 */
public interface IChatTransfer extends ChatObserver {

    /**
     * 注册一个观察者
     * @param observer
     */
    void register(ChatObserver observer);

    /**
     * 解除注册一个观察者
     * @param observer
     */
    void unregister(ChatObserver observer);

    default int size(){return 0;}
}
