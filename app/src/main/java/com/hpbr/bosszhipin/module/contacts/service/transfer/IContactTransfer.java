package com.hpbr.bosszhipin.module.contacts.service.transfer;

/**
 * Created by monch on 15/8/3.
 */
public interface IContactTransfer {

    /**
     * 注册一个观察者
     *
     * @param observer
     */
    void register(ContactObserver observer);

    /**
     * 解除注册一个观察者
     *
     * @param observer
     */
    void unregister(ContactObserver observer);

    /**
     * 主题更新通知
     */
    void notifyObservers();

}
