package com.hpbr.bosszhipin.module.contacts.service.transfer;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by monch on 15/5/30.
 */
public class MqttConnectStatusTransfer implements IMqttConnectStatusTransfer {

    private Object lock = new Object();

    // 观察者集合
    private List<MqttConnectStatusObserver> list;
    // 消息队列
    private volatile LinkedList<Integer> queue;

    public MqttConnectStatusTransfer() {
        queue = new LinkedList<>();
        list = new ArrayList<>();
    }

    @Override
    public void register(MqttConnectStatusObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            list.add(observer);
        }
    }

    @Override
    public void unregister(MqttConnectStatusObserver observer) {
        if (observer == null) return;
        synchronized (lock) {
            list.remove(observer);
        }
    }

    @Override
    public void notifyObservers(int status) {
        synchronized (queue) {
            queue.add(Integer.valueOf(status));
        }
        handler.sendEmptyMessage(0);
    }

    private Handler handler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            int status = -1;
            synchronized (queue) {
                if (!queue.isEmpty()) {
                    Integer integer = queue.remove(0);
                    if (integer != null) {
                        status = integer;
                    }
                }
            }
            if (status != -1) {
                handleNotify(status);
            }
            return true;
        }
    });


    /**
     * 具体处理消息的方法
     * @param data
     */
    private void handleNotify(int status) {
        synchronized (lock) {
            for (MqttConnectStatusObserver observer : list) {
                observer.onMqttConnectStatusChanged(status);
            }
        }
    }

}
