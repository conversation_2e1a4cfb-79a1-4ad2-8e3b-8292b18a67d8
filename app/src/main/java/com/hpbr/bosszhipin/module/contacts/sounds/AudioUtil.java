package com.hpbr.bosszhipin.module.contacts.sounds;

import android.content.Context;
import android.media.AudioManager;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/29.
 */

public class AudioUtil {

    /**
     * 请求音频焦点
     *
     * @param context
     * @param focus
     * @return
     */
    public static boolean requestAudioFocus(Context context, boolean focus) {
        boolean bool = false;
        if (context != null) {
            AudioManager am = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (focus) {
                int result = am.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
                bool = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            } else {
                int result = am.abandonAudioFocus(null);
                bool = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            }
        }
        return bool;
    }


}
