package com.hpbr.bosszhipin.module.contacts.sounds;

import android.annotation.SuppressLint;
import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.view.KeyEvent;
import android.view.Window;
import android.view.WindowManager;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;

import com.hpbr.bosszhipin.base.App;
import com.tencent.bugly.crashreport.CrashReport;
import com.twl.ui.ToastUtils;

import java.io.File;
import java.io.FileInputStream;

/**
 * create by guofeng
 * date on 2023/5/12
 */
public class ChatSoundPlayer implements SensorEventListener {

    private  MediaPlayer mMediaPlayer;

    private final AppCompatActivity mActivity;

    private final OnMediaPlayerCallBack2 onMediaPlayerCallBack;

    private final AudioManager mAudioManager;

    private final SensorManager sensorManager;

    private final Sensor sensor;



    public ChatSoundPlayer(AppCompatActivity activity, OnMediaPlayerCallBack2 callBack) {
        mActivity = activity;
        onMediaPlayerCallBack = callBack;

        sensorManager = (SensorManager) App.getAppContext().getSystemService(Context.SENSOR_SERVICE);
        sensor = sensorManager.getDefaultSensor(Sensor.TYPE_PROXIMITY);
        mAudioManager = (AudioManager) App.getAppContext().getSystemService(Context.AUDIO_SERVICE);
        sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_NORMAL);
        registerVolumeChangeListener();
        registerLifeCycle();
    }

    private void registerLifeCycle() {
        mActivity.getLifecycle().addObserver((LifecycleEventObserver) (source, event) -> {
            if (event == Lifecycle.Event.ON_DESTROY) {
                sensorManager.unregisterListener(ChatSoundPlayer.this);
            }
            if (event == Lifecycle.Event.ON_PAUSE) {
                if (isPlaying()) {
                    mMediaPlayer.pause();
                    mMediaPlayer.stop();

                    if (onMediaPlayerCallBack != null) {
                        onMediaPlayerCallBack.onPlayerCompleteListener(currentPlayFile);
                    }
                }
            }
        });
    }

    private void registerVolumeChangeListener() {
        mActivity.getWindow().getDecorView().setOnKeyListener((v, keyCode, event) -> {
            if (keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
                if (isPlaying()) {
                    addVolume();
                    return true;
                }
            } else if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
                if (isPlaying()) {
                    lowerVolume();
                    return true;
                }
            }
            return false;
        });
    }


    //当前播放的声音
    private String currentPlayFile;


    @SuppressLint("twl_utils_file")
    public void play(String fileSound) {

        this.currentPlayFile = fileSound;

        AudioUtil.requestAudioFocus(mActivity, true);

        //暂停正在播放的
        if (isPlaying()) {
            mMediaPlayer.pause();
            mMediaPlayer.stop();
            return;
        }

        mMediaPlayer = new MediaPlayer();

        FileInputStream fis = null;

        try {
            AudioPlayerParameter playParam = new AudioPlayerParameter(AudioManager.STREAM_MUSIC, AudioManager.MODE_NORMAL, true);

            mAudioManager.setMode(playParam.mode);
            mAudioManager.setSpeakerphoneOn(playParam.speakerOn);

            mMediaPlayer.setAudioStreamType(playParam.stream);
            mMediaPlayer.setOnCompletionListener(arg0 -> {

                Window window = mActivity.getWindow();
                window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

                if (onMediaPlayerCallBack != null) {
                    onMediaPlayerCallBack.onPlayerCompleteListener(fileSound);
                }

            });
            mMediaPlayer.setOnErrorListener((mp, what, extra) -> {
                if (onMediaPlayerCallBack != null) {
                    onMediaPlayerCallBack.onPlayerErrorListener(fileSound);
                }
                return false;
            });
            mMediaPlayer.setOnPreparedListener(mp -> {

                //保持录音过程屏幕亮的
                Window window = mActivity.getWindow();
                window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

                if (onMediaPlayerCallBack != null) {
                    onMediaPlayerCallBack.onPreparePlayer(fileSound);
                }

                mp.start();

            });

            File file = new File(currentPlayFile);
            if (!file.exists()) {
                ToastUtils.showText("语音播放失败");
                return;
            }
            fis = new FileInputStream(file);
            mMediaPlayer.setDataSource(fis.getFD());
            mMediaPlayer.prepareAsync();
        } catch (Exception e) {
            ToastUtils.showText("语音播放失败");
            CrashReport.postCatchedException(e);
            return;
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }


    public void release() {
        if (isPlaying()) {
            mMediaPlayer.pause();
            mMediaPlayer.stop();
            /*回掉业务传 刷新播放完毕*/
            if (onMediaPlayerCallBack != null) {
                onMediaPlayerCallBack.onPlayerCompleteListener(currentPlayFile);
            }
        }
    }

    private boolean isPlaying() {
        return mMediaPlayer!=null&&mMediaPlayer.isPlaying();
    }


    //增高播放声音音量
    private void addVolume() {
        if (isPlaying()) {
            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_RAISE,
                    AudioManager.FLAG_SHOW_UI);
        }
    }



    //降低播放声音音量
    private void lowerVolume() {
        if (isPlaying())
            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_LOWER,
                    AudioManager.FLAG_SHOW_UI);
    }


    private boolean mLoudSpeaker;


    //使用扬声器
    private void setNormalPlayMode() {
        if (!mLoudSpeaker) {
            mLoudSpeaker = true;
            if (isPlaying()) {
                play(currentPlayFile);
            }
        }
    }

    //使用听筒
    private void setInCallPlayMode() {
        if (mLoudSpeaker) {
            mLoudSpeaker = false;
            if (isPlaying()) {
                play(currentPlayFile);
            }
        }
    }

    @Override
    public void onSensorChanged(SensorEvent event) {

        if (!isPlaying()) return;

        float f = event.values[0];
        if (f == sensor.getMaximumRange()) {
            // 距离远时
            setNormalPlayMode();
        } else {
            // 距离近时
            setInCallPlayMode();
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {

    }

    public interface OnMediaPlayerCallBack {

        void showDownLoadingAnimation(long mid);

        void onPreparePlayer(String filePath);

        void onPlayerErrorListener(String filePath);

        void onPlayerCompleteListener(String filePath);

    }


    public interface OnMediaPlayerCallBack2 {

        void onPreparePlayer(String filePath);

        void onPlayerErrorListener(String filePath);

        void onPlayerCompleteListener(String filePath);

    }
}