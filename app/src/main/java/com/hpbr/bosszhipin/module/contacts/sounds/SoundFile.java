package com.hpbr.bosszhipin.module.contacts.sounds;

import android.annotation.SuppressLint;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.config.URLConfig;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.gray.DataStarGray;
import com.hpbr.bosszhipin.utils.LiveDataBus;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.utils.MD5;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import net.bosszhipin.api.SignRefreshHttpResponse;
import net.bosszhipin.base.BaseFileDownloadCallback;
import net.bosszhipin.base.FileDownloadRequest;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import java.io.File;
import java.net.URLEncoder;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by monch on 15/4/15.
 */
public class SoundFile {

    public static final int MAX_RETRY_COUNT = 1;

    private final AtomicInteger mDownloadCount = new AtomicInteger(0);

    private long startTime = 0;

    private SoundFile() {
    }

    private static SoundFile instance;

    public static SoundFile getInstance() {
        if (instance == null) {
            instance = new SoundFile();
        }
        return instance;
    }


    public String getFileName(String url) {
        return MD5.convert(url) + ".amr";
    }


    public File createLocalSoundFilePath(final String url) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        return FileUtils.getFileByPath(soundPath, getFileName(url));
    }


    @SuppressLint("twl_utils_file")
    public boolean checkSoundFileExist(final String url) {
        try {
            return createLocalSoundFilePath(url).exists();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url) {
        File file = createLocalSoundFilePath(url);
        addNewTaskDownloadFile(url, file, MAX_RETRY_COUNT);
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url, final File file, int retryCount) {
        if (!file.exists()) {
            HttpExecutor.download(new FileDownloadRequest(url, file.getParent(), file.getName(), new BaseFileDownloadCallback() {
                @Override
                public void onFail(String url, ErrorReason reason) {
                    if ((ErrorReason.ERROR_SERVER_FAILED + " 403").equals(reason.getErrReason()) && retryCount > 0) {
                        SimpleApiRequest.GET(URLConfig.URL_SIGN_REFRESH_UPLOAD)
                                .addParam("url", URLEncoder.encode(url))
                                .setRequestCallback(new SimpleCommonApiRequestCallback<SignRefreshHttpResponse>() {
                                    @Override
                                    public void handleInChildThread(ApiData<SignRefreshHttpResponse> data) {
                                        addNewTaskDownloadFile(data.resp.url, file, retryCount - 1);
                                    }
                                })
                                .execute();
                    }
                }

                @Override
                public void onSuccess(String url, File result) {
                    if(mDownloadCount.get() == 0) {
                        startTime = System.currentTimeMillis();
                    }
                    mDownloadCount.addAndGet(1);

                    // 超过100个文件且耗时小于60秒,上报
                    if (mDownloadCount.get() > 100 && System.currentTimeMillis() - startTime < 60 * 1000) {
                        ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_REPORT_SOUND_DOWNLOAD_TOO_MORE).report();
                        mDownloadCount.set(0);
                    }

                    if (DataStarGray.getInstance().isOpenVoiceToWord()) {
                        LiveDataBus.multi(ChannelConstants.SUCCESS_DOWNLOAD_AUDIO_FILE).postValue(url);
                    }
                }
            }));
        }
    }

    @SuppressLint("twl_utils_file")
    public void addNewTaskDownloadFile(final String url, BaseFileDownloadCallback downloadCallback) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        File file = FileUtils.getFileByPath(soundPath, getFileName(url));

        if (!file.exists()) {
            HttpExecutor.download(new FileDownloadRequest(url, soundPath, getFileName(url), downloadCallback));
        }
    }

    public boolean copyFile(File oldFile, String url) {
        String soundPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
        FileUtils.copy(oldFile, FileUtils.getFileByPath(soundPath, getFileName(url)));
        return true;
    }

}
