package com.hpbr.bosszhipin.module.contacts.sounds;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Message;
import android.view.Window;
import android.view.WindowManager;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatSoundBean;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;
import com.hpbr.bosszhipin.window.FloatWindowManager;
import com.monch.lbase.widget.T;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import static com.hpbr.bosszhipin.config.Constants.AUDIO_PLAY_COMPLETE_ACTION;
import static com.hpbr.bosszhipin.config.Constants.AUDIO_PREPARE_PLAY_ACTION;

/**
 * Created by monch on 15/4/15.
 */
public class SoundPlayer {
    private static final String TAG = "SoundPlayer";
    public static final int MAX_LENGTH = 100;

    private AudioManager mAudioManager;
    private Context mContext;
    private boolean mLoudSpeaker = true;

    private Handler handler = AppThreadFactory.createMainHandler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            setLoop(false);
            start();
            return true;
        }
    });


    private OnMediaPlayerProgressCallback onMediaPlayerProgressListener;


    public void setOnMediaPlayerProgressListener(OnMediaPlayerProgressCallback onMediaPlayerProgressListener) {
        this.onMediaPlayerProgressListener = onMediaPlayerProgressListener;
    }

    /**
     * 播放器
     */
    private MediaPlayer mMediaPlayer;

    private OnPlayerSoundCompleteCallback mOnPlayerSoundCompleteCallback;

    public void setOnPlayerSoundCompleteCallback(OnPlayerSoundCompleteCallback callback) {
        mOnPlayerSoundCompleteCallback = callback;
    }

    private ChatSoundBean currentBean;

    public ChatSoundBean getCurrentBean() {
        return currentBean;
    }

    public SoundPlayer(Context context) {
        mContext = context;
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
    }

    public boolean player(ChatSoundBean bean) {
        mLoudSpeaker = true;
        return innerlayer(bean);
    }


    public boolean innerlayer(ChatSoundBean bean) {
        TLog.debug(TAG, "player() called with: mLoudSpeaker = [%b]", mLoudSpeaker);
        this.currentBean = bean;
        String fileParent = PathUtils.getCacheDirChildPathExternalFirst("sound");
        File fileSound = FileUtils.getFileByPath(fileParent, SoundFile.getInstance().getFileName(bean.url));
        if (fileSound.exists()) {
            player(fileSound.getPath());
            return true;
        } else {
            SoundFile.getInstance().addNewTaskDownloadFile(bean.url);
            T.ss("正在下载语音...");
        }
        return false;
    }


    private void player(final String fileUrl) {
        stop();
        AudioUtil.requestAudioFocus(mContext, true);
        mMediaPlayer = new MediaPlayer();
        setDataSource(fileUrl);
    }

    /**
     * 设置播放数据路径
     *
     * @param path 路径
     */
    public void setDataSource(String path) {
        if (mMediaPlayer != null) {
            FileInputStream fis = null;
            try {
                AudioPlayerParameter playParam = getProperParam();
                mAudioManager.setMode(playParam.mode);
                mAudioManager.setSpeakerphoneOn(playParam.speakerOn);

                mMediaPlayer.setAudioStreamType(playParam.stream);
                mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                    public void onCompletion(MediaPlayer arg0) {
                        //播放完毕 取消屏幕一直亮
                        if (mContext instanceof Activity) {
                            Window window = ((Activity) mContext).getWindow();
                            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                        }

                        if (onMediaPlayerProgressListener != null) {
                            onMediaPlayerProgressListener.onMediaPlayerProgressListener(MAX_LENGTH);
                        }

                        if (null != mOnPlayerSoundCompleteCallback) {
                            mOnPlayerSoundCompleteCallback.onPlayerSoundCompleteCallback(currentBean);
                        }
                        //发送语音播放完毕广播
                        sendAudioPlayCompleteAction();
                        /*使视频小窗取消静音*/
                        FloatWindowManager.getInstance().startResumeService(mContext);
                    }
                });
                mMediaPlayer.setOnErrorListener(new MediaPlayer.OnErrorListener() {
                    public boolean onError(MediaPlayer mp, int what, int extra) {
                        //播放完毕 取消屏幕一直亮
                        if (mContext instanceof Activity) {
                            Window window = ((Activity) mContext).getWindow();
                            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                        }
                        if (onMediaPlayerProgressListener != null) {
                            onMediaPlayerProgressListener.onMediaPlayerProgressListener(MAX_LENGTH);
                        }
                        stop();
                        if (null != mOnPlayerSoundCompleteCallback) {
                            mOnPlayerSoundCompleteCallback.onPlayerSoundCompleteCallback(currentBean);
                        }

                        /*使视频小窗取消静音*/
                        FloatWindowManager.getInstance().startResumeService(mContext);
                        return false;
                    }
                });
                mMediaPlayer.setOnPreparedListener(new MediaPlayer.OnPreparedListener() {
                    @Override
                    public void onPrepared(MediaPlayer mp) {
                        /**
                         * 保持录音过程屏幕亮的
                         */
                        if (mContext instanceof Activity) {
                            Window window = ((Activity) mContext).getWindow();
                            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                        }

                        if (onMediaPlayerProgressListener != null) {
                            onMediaPlayerProgressListener.onMediaPlayerProgressListener(0);
                        }

                        handler.removeMessages(0);
                        Message message = handler.obtainMessage(0);
                        handler.sendMessageDelayed(message, 0);
                        //初始化播放进度回掉
                        initProgressCallBack();
                        //发送语音开始播放广播
                        sendAudioPreparePlayAction();
                        /*使视频小窗静音*/
                        FloatWindowManager.getInstance().startPauseService(mContext);
                    }
                });
                File file = new File(path);
                if (!file.exists()) {
                    T.ss("语音播放失败");
                    return;
                }
                fis = new FileInputStream(file);
                mMediaPlayer.setDataSource(fis.getFD());
                mMediaPlayer.prepareAsync();
            } catch (Exception e) {
                T.ss("语音播放失败");
                CrashReport.postCatchedException(e);
                return;
            } finally {
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                    }
                }
            }
        }
    }

    /**
     * 发送语音开始播放广播
     */
    private void sendAudioPreparePlayAction() {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity != null) {
            Intent intent = new Intent(AUDIO_PREPARE_PLAY_ACTION);
            ReceiverUtils.sendBroadcast(topActivity, intent);
        }
    }

    /**
     * 发送语音播放完毕广播
     */
    private void sendAudioPlayCompleteAction() {
        Activity topActivity = ForegroundUtils.get().getTopActivity();
        if (topActivity != null) {
            Intent intent = new Intent(AUDIO_PLAY_COMPLETE_ACTION);
            ReceiverUtils.sendBroadcast(topActivity, intent);
        }
    }

    /**
     * 监控播放回掉进度
     */
    private void initProgressCallBack() {
        App.get().getMainHandler().post(progressRunnable);
    }


    private Runnable progressRunnable = new Runnable() {
        @Override
        public void run() {
            if (onMediaPlayerProgressListener != null) {
                if (mMediaPlayer != null) {
                    float maxLength = mMediaPlayer.getDuration();
                    float currentLength = mMediaPlayer.getCurrentPosition();
                    int progress = (int) (currentLength / maxLength * MAX_LENGTH);
                    onMediaPlayerProgressListener.onMediaPlayerProgressListener(progress);
                    if (progress != MAX_LENGTH) {
                        App.get().getMainHandler().postDelayed(progressRunnable, 1000);
                    }
                }
            }
        }
    };


    /**
     * 设置循环播放
     *
     * @param b true循环播放
     */
    public void setLoop(boolean b) {
        if (mMediaPlayer != null) {
            mMediaPlayer.setLooping(b);
        }
    }

    /**
     * 开始播放
     */
    private void start() {
        if (mMediaPlayer != null) {
            mMediaPlayer.start();
        }
    }

    /**
     * 停止播放
     */
    public void stop() {
        //删除队列里面等待执行的回掉进度任务
        App.get().getMainHandler().removeCallbacks(progressRunnable);

        if (mMediaPlayer != null) {
            mMediaPlayer.pause();
            mMediaPlayer.stop();
            mMediaPlayer.reset();
            mMediaPlayer.release();
        }
        mMediaPlayer = null;
        modeNormal();
    }

    private void modeNormal() {
        try {
            mAudioManager.setMode(AudioManager.MODE_NORMAL);
            mAudioManager.setSpeakerphoneOn(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用扬声器
     */
    public void setNormalPlayMode() {
        TLog.debug(TAG, "setNormalPlayMode() called: mLoudSpeaker = [%b]", mLoudSpeaker);
        if (!mLoudSpeaker) {
            mLoudSpeaker = true;
            if (isPlaying()) innerlayer(currentBean);
        }
    }

    /**
     * 使用听筒
     */
    public void setInCallPlayMode() {
        TLog.debug(TAG, "setInCallPlayMode() called: mLoudSpeaker = [%b]", mLoudSpeaker);
        if (mLoudSpeaker) {
            mLoudSpeaker = false;
            if (isPlaying()) innerlayer(currentBean);
        }
    }

    /**
     * 是否正在播放
     *
     * @return true正在播放
     */
    public boolean isPlaying() {
        if (mMediaPlayer != null) {
            return mMediaPlayer.isPlaying();
        }
        return false;
    }

    /**
     * 音频长度
     *
     * @return 音频播放长度
     */
    public long duration() {
        if (mMediaPlayer != null) {
            return mMediaPlayer.getDuration();
        }
        return 0;
    }

    /**
     * 增加播放声音音量
     */
    public void addVolume() {
        if (isPlaying())
            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_RAISE,
                    AudioManager.FLAG_SHOW_UI);
    }

    /**
     * 降低播放声音音量
     */
    public void lowerVolume() {
        if (isPlaying())
            mAudioManager.adjustStreamVolume(AudioManager.STREAM_MUSIC,
                    AudioManager.ADJUST_LOWER,
                    AudioManager.FLAG_SHOW_UI);
    }

    /**
     * 加参数适配
     * 未来考虑蓝牙以及其它机型适配
     *
     * @return
     */
    private AudioPlayerParameter getProperParam() {
        if (mLoudSpeaker)
            return new AudioPlayerParameter(AudioManager.STREAM_MUSIC, AudioManager.MODE_NORMAL, true);
        else {
            return new AudioPlayerParameter(AudioManager.STREAM_VOICE_CALL, AudioManager.MODE_IN_COMMUNICATION, false);
        }
    }


    /**
     * 监控当前音频播放进度
     */
    public interface OnMediaPlayerProgressCallback {
        /**
         * 监听播放进度
         *
         * @param progress range from 0-100
         */
        void onMediaPlayerProgressListener(int progress);
    }

}
