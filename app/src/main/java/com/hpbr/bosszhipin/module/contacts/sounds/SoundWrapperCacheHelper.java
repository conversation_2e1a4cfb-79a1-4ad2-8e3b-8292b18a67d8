package com.hpbr.bosszhipin.module.contacts.sounds;

import androidx.annotation.Nullable;

import com.monch.lbase.util.LText;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * create by guofeng
 * date on 2023/5/26
 */

public class SoundWrapperCacheHelper {


    public List<SoundWrapper> cache = new ArrayList<>();



    private @Nullable
    SoundWrapper findInCache(long mid) {
        for (SoundWrapper soundWrapper : cache) {
            if (soundWrapper.mid == mid) {
                return soundWrapper;
            }
        }
        return null;
    }


    private @Nullable
    SoundWrapper findInCache(String filePath) {
        for (SoundWrapper soundWrapper : cache) {
            if (LText.equal(soundWrapper.localFile, filePath)) {
                return soundWrapper;
            }
        }
        return null;
    }


    public int getSoundStatus(long mid, String filePath) {

        SoundWrapper inCache;
        if (mid > 0) {
            inCache = findInCache(mid);
        } else {
            inCache = findInCache(filePath);
        }

        if (inCache != null) {
            return inCache.status;
        }
        return SoundWrapper.DEFAULT_STATUS;
    }


    public void refreshCacheLocalFile(@Nullable String localPath) {

        stopPlayingAudioStatus();
        SoundWrapper inCache = findInCache(localPath);

        if (inCache == null) {
            SoundWrapper defWrapper = createDefWrapper(localPath);
            defWrapper.status = SoundWrapper.DEFAULT_STATUS;
            cache.add(defWrapper);
        } else {
            inCache.localFile = localPath;
            inCache.status = SoundWrapper.DEFAULT_STATUS;
        }

    }

    //检测是否需要下载语音
    public void refreshCacheLocalFile(@Nullable String soundUrl,
                                      long mid) {

        stopPlayingAudioStatus();

        SoundWrapper inCache;
        if (mid > 0) {
            inCache = findInCache(mid);
        } else {
            File localSoundFilePath = SoundFile.getInstance().createLocalSoundFilePath(soundUrl);
            inCache = findInCache(localSoundFilePath.toString());
        }

        if (inCache == null) {
            SoundWrapper defWrapper = createDefWrapper(mid, soundUrl);
            defWrapper.status = SoundWrapper.DEFAULT_STATUS;
            cache.add(defWrapper);
        } else {
            inCache.localFile = SoundFile.getInstance().createLocalSoundFilePath(soundUrl).toString();
            inCache.status = SoundWrapper.DEFAULT_STATUS;
        }
    }

    //停止当前正在播放的语音状态
    private void stopPlayingAudioStatus() {
        for (SoundWrapper soundWrapper : cache) {
            if (soundWrapper.status == SoundWrapper.PLAYING_STATUS) {
                soundWrapper.status = SoundWrapper.DEFAULT_STATUS;
            }
        }
    }



    //准备播放 更新缓存状态
    public void onPreparePlayer(String filePath, long mid) {

        if (mid > 0) {
            SoundWrapper inCache = findInCache(mid);
            if (inCache == null) {
                inCache = createDefWrapper(mid, "");
                cache.add(inCache);
            }
            inCache.status = SoundWrapper.PLAYING_STATUS;
            return;
        }

        if (!LText.empty(filePath)) {
            SoundWrapper inCache = findInCache(filePath);
            if (inCache == null) {
                inCache = createDefWrapper(0, filePath);
                cache.add(inCache);
            }
            inCache.status = SoundWrapper.PLAYING_STATUS;
        }

    }



    //播放完毕 更新缓存状态
    public void onPlayerCompleteListener(String filePath, long mid) {
        SoundWrapper inCache = null;
        if (mid > 0) {
            inCache = findInCache(mid);
        }
        if (inCache == null) {
            inCache = findInCache(filePath);
        }
        if (inCache != null) {
            inCache.status = SoundWrapper.DEFAULT_STATUS;
        }
    }

    //播放异常 更新缓存状态
    public void onPlayerErrorListener(String filePath) {
        SoundWrapper inCache = findInCache(filePath);
        if (inCache != null) {
            inCache.status = SoundWrapper.DEFAULT_STATUS;
        }
    }


    //创建默认包装类
    private SoundWrapper createDefWrapper(long mid,
                                          String urlSound) {
        SoundWrapper wrapper = new SoundWrapper();
        wrapper.mid = mid;
        wrapper.url = urlSound;
        wrapper.status = SoundWrapper.DEFAULT_STATUS;
        if (!LText.empty(urlSound)) {
            wrapper.localFile = SoundFile.getInstance().createLocalSoundFilePath(urlSound).toString();
        }
        return wrapper;
    }

    private SoundWrapper createDefWrapper(
                                          String localPath) {
        SoundWrapper wrapper = new SoundWrapper();
        wrapper.status = SoundWrapper.DEFAULT_STATUS;
        wrapper.localFile =localPath;
        return wrapper;
    }

} 