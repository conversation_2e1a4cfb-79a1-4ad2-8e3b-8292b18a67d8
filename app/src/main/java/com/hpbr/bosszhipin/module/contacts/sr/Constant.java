package com.hpbr.bosszhipin.module.contacts.sr;

import android.media.AudioFormat;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class Constant {
    public final static int PLAY_FREQUENCY = 8000;
    public final static int RECORD_FREQUENCY = 16000;
    public final static int CHANNEL = AudioFormat.CHANNEL_IN_MONO;
    public final static int ENCODING = AudioFormat.ENCODING_PCM_16BIT;
    public final static int CHANNEL_COUNT = 1;
    public final static int BIT_RATE = 12650;

    // 1ms 单声道的byte数组长度 16k
    public static final int MS_DATA_LEN = RECORD_FREQUENCY * 2 /1000;

    // 10ms 单声道的byte数组长度 16k
    public static final int TEN_MS_DATA_LEN = MS_DATA_LEN * 10;

    // 1ms 单声道的byte数组长度 8K
    public static final int PLAY_MS_DATA_LEN = PLAY_FREQUENCY * 2 /1000;

    // 10ms 单声道的byte数组长度
    public static final int PLAY_TEN_MS_DATA_LEN = PLAY_MS_DATA_LEN * 10;



    // To make quieter sounds still show up well on the display, we use +/- 8192 as the amplitude
    // that reaches the top/bottom of the view instead of +/- 32767. Any samples that have
    // magnitude higher than this limit will simply be clipped during drawing.
    public static final float MAX_AMPLITUDE_TO_DRAW = 8192.0f;
    public static final float MIN_AMPLITUDE_TO_DRAW = 100.0f;


    /**
     * 60秒的数据长度
     */
    public static final long SIXTY_DATA_LEN = TEN_MS_DATA_LEN * 100 * 60;
    public static final int DECODE_SPEED = 4;


    /**
     * 1208.602 F1小助手是否点击了关闭
     * 一次启动 关闭之后不再展示 ，下次启动再展示和进程绑定所以静态
     */
    public static  boolean geekF1AssistantEntranceIsClose=false;

    @IntDef({ExchangeType.PHONE, ExchangeType.RESUME, ExchangeType.WECHAT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ExchangeType {
        int RESUME = 1;
        int PHONE = 2;
        int WECHAT = 3;
    }
}
