package com.hpbr.bosszhipin.module.contacts.sr.coder;

import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.util.Log;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;

import static com.hpbr.bosszhipin.module.contacts.sr.Constant.MAX_AMPLITUDE_TO_DRAW;
import static com.hpbr.bosszhipin.module.contacts.sr.Constant.MIN_AMPLITUDE_TO_DRAW;

public class AmrDecodeHelper {
    private static final String TAG = "AmrDecodeHelper";

    public static void getSamples(AudioSummary summary) {
        MediaExtractor extractor = new MediaExtractor();
        MediaFormat format = null;
        int i;

        File inputFile1 = new File(summary.getPath());
        MediaCodec codec = null;
        try {
            extractor.setDataSource(inputFile1.getPath());
            int numTracks = extractor.getTrackCount();
            // find and select the first audio track present in the file.
            for (i = 0; i < numTracks; i++) {
                format = extractor.getTrackFormat(i);
                if (format.getString(MediaFormat.KEY_MIME).startsWith("audio/")) {
                    extractor.selectTrack(i);
                    break;
                }
            }
            if (i == numTracks) {
                throw new RuntimeException("No audio track found in " + inputFile1);
            }
            int channels = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
            // Expected total number of samples per channel.
            long time = format.getLong(MediaFormat.KEY_DURATION);
            Log.d(TAG, "getSamples: time = " + time);
            int defSpeed = summary.init(channels, time);
            codec = MediaCodec.createDecoderByType(format.getString(MediaFormat.KEY_MIME));
            codec.configure(format, null, null, 0);
            codec.start();

            int decodedSamplesSize = 0;  // size of the output buffer containing decoded samples.
            byte[] decodedSamples = null;
            ByteBuffer[] inputBuffers = codec.getInputBuffers();
            ByteBuffer[] outputBuffers = codec.getOutputBuffers();
            int sample_size;
            MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
            long presentation_time;
            boolean done_reading = false;

            while (!summary.mCancel) {
                // read data from file and feed it to the decoder input buffers.
                int inputBufferIndex = codec.dequeueInputBuffer(100);
                if (!done_reading && inputBufferIndex >= 0) {
                    sample_size = extractor.readSampleData(inputBuffers[inputBufferIndex], 0);
                    if (sample_size < 0) {
                        // All samples have been read.
                        codec.queueInputBuffer(
                                inputBufferIndex, 0, 0, -1, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                        done_reading = true;
                    } else {
                        presentation_time = extractor.getSampleTime();
                        codec.queueInputBuffer(inputBufferIndex, 0, sample_size, presentation_time, 0);
                        boolean hasMore = true;
                        int speed = defSpeed;
                        while (hasMore && speed-- > 0) {
                            if (!(hasMore = extractor.advance())) break;
                        }
                    }
                }

                // Get decoded stream from the decoder output buffers.
                int outputBufferIndex = codec.dequeueOutputBuffer(info, 100);
                if (outputBufferIndex >= 0 && info.size > 0) {
                    if (decodedSamplesSize < info.size) {
                        decodedSamplesSize = info.size;
                        decodedSamples = new byte[decodedSamplesSize];
                    }
                    outputBuffers[outputBufferIndex].get(decodedSamples, 0, info.size);
                    outputBuffers[outputBufferIndex].clear();
                    codec.releaseOutputBuffer(outputBufferIndex, false);
                    calculateWaveform(decodedSamples, 0, info.size, summary);
                } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                    outputBuffers = codec.getOutputBuffers();
                } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    // Subsequent data will conform to new format.
                    // We could check that codec.getOutputFormat(), which is the new output format,
                    // is what we expect.
                }
                if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (extractor != null) {
                extractor.release();
            }
            codec.stop();
            codec.release();
            Log.d(TAG, "getSamples: ");
        }
    }


    private static void calculateWaveform(byte[] b, int off, int len, AudioSummary summary) {
        int count = len / summary.getDensity();
        int index = off;
        for (int i = 0; i < count; i++) {

            short amplitude = (short) ((b[index] & 0xff) | (b[index + 1] << 8));
            if (amplitude > (short) MAX_AMPLITUDE_TO_DRAW) {
                amplitude = (short) MAX_AMPLITUDE_TO_DRAW;
            } else if (amplitude < MIN_AMPLITUDE_TO_DRAW){
                amplitude = (short) MIN_AMPLITUDE_TO_DRAW;
            }
            summary.addWaveform(amplitude);
            index += summary.getDensity();
        }

    }


}
