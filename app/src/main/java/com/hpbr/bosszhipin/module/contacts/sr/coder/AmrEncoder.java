package com.hpbr.bosszhipin.module.contacts.sr.coder;

import android.media.MediaCodec;
import android.media.MediaFormat;
import android.os.Build;
import android.os.SystemClock;
import android.util.Log;

import com.hpbr.bosszhipin.module.contacts.sr.Constant;

import java.io.Closeable;
import java.io.IOException;
import java.nio.ByteBuffer;

import okio.BufferedSink;
import okio.BufferedSource;

public class AmrEncoder implements Closeable{
    private static final String TAG = "AmrEncoder";
    private MediaCodec mEncoder;
    MediaCodec.BufferInfo bufferInfo;
    int inputBufferIndex;
    int outputBufferIndex;

    public AmrEncoder() {
        init();
    }

    private void init(){
        try {
            mEncoder = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AMR_NB);
        } catch (IOException e) {
            e.printStackTrace();
        }
        MediaFormat format = new MediaFormat();
        format.setString(MediaFormat.KEY_MIME, MediaFormat.MIMETYPE_AUDIO_AMR_NB);
        format.setInteger(MediaFormat.KEY_SAMPLE_RATE, Constant.PLAY_FREQUENCY);
        format.setInteger(MediaFormat.KEY_CHANNEL_COUNT, Constant.CHANNEL_COUNT);
        format.setInteger(MediaFormat.KEY_BIT_RATE, Constant.BIT_RATE);


        mEncoder.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
    }

    public void start(){
        mEncoder.start();
        bufferInfo = new MediaCodec.BufferInfo();
    }


    public void encode(byte[] buffer1, int offset, int length, BufferedSink bufferedSink) throws IOException {
        long time = SystemClock.elapsedRealtime();
        ByteBuffer inputBuffer;
        ByteBuffer outputBuffer;
        do {
            inputBufferIndex = mEncoder.dequeueInputBuffer(-1);
            if (inputBufferIndex >= 0) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    inputBuffer = mEncoder.getInputBuffer(inputBufferIndex);
                } else {
                    inputBuffer = mEncoder.getInputBuffers()[inputBufferIndex];
                }
                int min = Math.min(length, inputBuffer.limit());
                inputBuffer.clear();
                inputBuffer.put(buffer1, offset, min);
                try {
                    mEncoder.queueInputBuffer(inputBufferIndex, 0, min, 0, 0);
                } catch (MediaCodec.CryptoException e) {
                    e.printStackTrace();
                }
                length -= min;
                offset += min;
            }

            outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);


            while (outputBufferIndex >= 0) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    outputBuffer = mEncoder.getOutputBuffer(outputBufferIndex);
                } else {
                    outputBuffer = mEncoder.getOutputBuffers()[outputBufferIndex];
                }

                outputBuffer.position(bufferInfo.offset);
                outputBuffer.limit(bufferInfo.offset + bufferInfo.size);
                bufferedSink.write(outputBuffer);

                try {
                    mEncoder.releaseOutputBuffer(outputBufferIndex, false);
                    outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

            Log.d(TAG, "encode: " + length + ":" + offset + ", elapsed :" + (SystemClock.elapsedRealtime() - time));
        } while (length > 0);
    }


    public void encode(BufferedSource bufferedSource, BufferedSink bufferedSink) throws IOException {
        long time = SystemClock.elapsedRealtime();
        ByteBuffer inputBuffer;
        ByteBuffer outputBuffer;
        boolean isOver = false;
        do {
            inputBufferIndex = mEncoder.dequeueInputBuffer(-1);
            if (inputBufferIndex >= 0) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    inputBuffer = mEncoder.getInputBuffer(inputBufferIndex);
                } else {
                    inputBuffer = mEncoder.getInputBuffers()[inputBufferIndex];
                }
                inputBuffer.clear();
                int size = bufferedSource.read(inputBuffer);
                isOver = size <= 0;
                if (isOver)  {
                    return;
                }
                try {
                    mEncoder.queueInputBuffer(inputBufferIndex, 0, size, 0, 0);
                } catch (MediaCodec.CryptoException e) {
                    e.printStackTrace();
                }
            }

            outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);


            while (outputBufferIndex >= 0) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    outputBuffer = mEncoder.getOutputBuffer(outputBufferIndex);
                } else {
                    outputBuffer = mEncoder.getOutputBuffers()[outputBufferIndex];
                }

                outputBuffer.position(bufferInfo.offset);
                outputBuffer.limit(bufferInfo.offset + bufferInfo.size);
                bufferedSink.write(outputBuffer);

                try {
                    mEncoder.releaseOutputBuffer(outputBufferIndex, false);
                    outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }

        } while (!isOver);
    }

    @Override
    public void close() throws IOException {
        try {
            mEncoder.stop();
            mEncoder.release();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
