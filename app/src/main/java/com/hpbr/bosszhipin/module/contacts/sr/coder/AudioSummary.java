package com.hpbr.bosszhipin.module.contacts.sr.coder;

import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.module.contacts.sounds.SoundFile;
import com.twl.utils.file.FileUtils;
import com.twl.utils.file.PathUtils;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.concurrent.TimeUnit;

import static com.hpbr.bosszhipin.module.contacts.sr.Constant.DECODE_SPEED;
import static com.hpbr.bosszhipin.module.contacts.sr.Constant.PLAY_TEN_MS_DATA_LEN;

public class AudioSummary {
    private static final short MAX_DATA_LEN = 600;

    private String path;
    private String url;
    private int channelCount;
    private short[] waveform;
    private int offset = 0;
    private long time;
    private int density;
    boolean mDecode = false;
    volatile boolean mCancel = false;
    private int progress;

    public void setProgress(int progress) {
        this.progress = progress;
        if (decodeCallback != null) {
            decodeCallback.onProgressListener();
        }
    }

    public int getProgress() {
        return progress;
    }


    public void setUrl(String url) {
        this.url = url;
    }

    int init(int channels, long time) {
        int speed = DECODE_SPEED;
        channelCount = channels;
        this.time = time / 1000000;
        density = PLAY_TEN_MS_DATA_LEN * channels;

        int dataLen = (int) (time / 10000) / speed;
        while (dataLen > MAX_DATA_LEN) {
            dataLen = (int) (time / 10000) / ++speed;
        }
        waveform = new short[dataLen];

        return speed;
    }

    public String getUrl() {
        return url;
    }


    public int getDensity() {
        return density;
    }

    private SoundFile soundFile = SoundFile.getInstance();

    private DecodeCallback decodeCallback;

    public void setDecodeCallback(DecodeCallback decodeCallback) {
        this.decodeCallback = decodeCallback;
    }


    public void getWaveform() {
        if (decodeCallback != null) {
            if (waveform != null) {
                decodeCallback.onResult(waveform);
            } else {
                //列表播放声音都是采用URL映射soundFile.getFileName(url)路径
                String parentPath = PathUtils.getCacheDirChildPathExternalFirst("sound");
                String name = soundFile.getFileName(getUrl());
                File fileSound = FileUtils.getFileByPath(parentPath,name);
                path = fileSound.toString();
                //文件不存在下载
                if (!fileSound.exists()) {
                    SoundFile.getInstance().addNewTaskDownloadFile(getUrl());
                    return;
                }
                WeakReference<DecodeCallback> callbackWeakReference = new WeakReference<>(decodeCallback);
                AppThreadFactory.POOL.schedule(() -> {
                    ensureDecode();
                    DecodeCallback decodeCallback = callbackWeakReference.get();
                    if (waveform != null) {
                        decodeCallback.onResult(waveform);
                    }
                }, 0, TimeUnit.MILLISECONDS);
            }
        }
    }

    public String getPath() {
        return path;
    }

    public long getTime() {
        ensureDecode();
        return time;
    }

    private void ensureDecode() {
        if (waveform != null) {
            return;
        }
        decode();
    }

    private void decode() {
        if (!mDecode) {
            AmrDecodeHelper.getSamples(this);
            mDecode = true;

        }
    }

    public void addWaveform(short sample) {
        if (waveform != null && offset < waveform.length) {
            waveform[offset++] = sample;
        }
    }

    public void cancel() {
        mCancel = true;
    }

    @Override
    public String toString() {
        return "AudioSummary{" +
                "Path='" + path + '\'' +
                ", channelCount=" + channelCount +
                ", waveform=" + (waveform == null ? "null" : waveform.length) +
                ", time=" + time +
                ", density=" + density +
                ", mDecode=" + mDecode +
                ", mCancel=" + mCancel +
                '}';
    }

    public interface DecodeCallback {

        void onResult(short[] waveform);

        void onProgressListener();
    }

}
