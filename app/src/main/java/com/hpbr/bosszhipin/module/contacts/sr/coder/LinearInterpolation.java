package com.hpbr.bosszhipin.module.contacts.sr.coder;

class LinearInterpolation {
    private float mMultiplier;

    public LinearInterpolation(int oldSampleRate, int newSampleRate) {
        mMultiplier = (float) newSampleRate / oldSampleRate;
    }

    private short[] mCache;

    private short[] getCache(int len) {
        if (mCache == null || mCache.length < len) {
            mCache = new short[len];
        }
        return mCache;
    }

    /**
     * Do interpolation on the samples according to the original and destinated sample rates
     *
     * @param samples original samples
     * @return interpolated samples
     */
    public short[] interpolate(short[] samples) {

        int newLength = Math.round(((float) samples.length * mMultiplier));
        short[] interpolatedSamples = getCache(newLength);

        // interpolate the value by the linear equation y=mx+c
        for (int i = 0; i < newLength; i++) {

            // get the nearest positions for the interpolated point
            float currentPosition = i / mMultiplier;
            int nearestLeftPosition = (int) currentPosition;
            int nearestRightPosition = nearestLeftPosition + 1;
            if (nearestRightPosition >= samples.length) {
                nearestRightPosition = samples.length - 1;
            }

            float slope = samples[nearestRightPosition] - samples[nearestLeftPosition]; // delta x is 1
            float positionFromLeft = currentPosition - nearestLeftPosition;

            interpolatedSamples[i] = (short) (slope * positionFromLeft + samples[nearestLeftPosition]); // y=mx+c
        }

        return interpolatedSamples;
    }
}
