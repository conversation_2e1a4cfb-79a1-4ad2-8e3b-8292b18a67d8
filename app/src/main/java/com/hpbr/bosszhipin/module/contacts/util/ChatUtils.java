package com.hpbr.bosszhipin.module.contacts.util;

import static com.hpbr.bosszhipin.module.contacts.manager.RevocationMessageManager.TWO_MINUTES_TIME_SECONDS;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ImageSpan;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringDef;
import androidx.core.content.ContextCompat;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.chat.export.router.ImportPushMsgRouter;
import com.hpbr.bosszhipin.chat.export.routerservice.IImportPushMsgService;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.constant.CommonConstant;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.InterviewWaitConfirmBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatActionBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatArticleBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.MessageBean;
import com.hpbr.bosszhipin.module.contacts.manager.RevocationMessageManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.utils.ForegroundUtils;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.chatbottom2.emotion.data.InnerEmotionManager;
import com.hpbr.bosszhipin.weiget.CenterImageSpan;
import com.hpbr.bosszhipin.weiget.ChatCenterImageSpan;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.bean.BaseServerBean;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import message.handler.receiver.RSingleMessageHandler;

/**
 * create by guofeng
 * date on 2022/10/19
 */

public class ChatUtils {


    public static final long ALL_JOB_ID = 0;

    public static final int REQ_CHOOSE_MATE = 100;
    // 忽略不统计
    public static final String COME_FROM_NONE = "0";
    // 牛人详情页分享
    public static final String COME_FROM_GEEK_DETAIL_SHARE = "1";
    // 聊天页加号
    public static final String COME_FROM_CHAT_ADD = "2";
    // 牛人详情设置页
    public static final String COME_FROM_CHAT_SETTING = "3";

    public static final int REPORT_CHATDETAIL_CODE = 101;

    public static final int TIMEOUT = 1000 * 60 * 2;

    public static final int OPERATION_RECOMMEND = 2;

    public static final int OPERATION_ALL = 0;

    //历史搜索的key
    public static String getSearchHistoryKey() {
        return "com.hpbr.bosszhipin.SEARCH_HISTORY_RECORD_" + UserManager.getUID() + "_" + UserManager.getUserRole().get();
    }

    /**
     * 是否沟通钥匙的消息
     *
     * @param b
     * @return
     */
    public static boolean isContactKeyMessage(@Nullable ChatBean b) {
        try {
            if (b != null
                    && b.message != null
                    && b.message.messageBody != null
                    && b.message.messageBody.extend != null) {
                String extend = b.message.messageBody.extend;
                if (!extend.isEmpty()) {
                    JSONObject jsonObject = new JSONObject(extend);
                    String value = jsonObject.optString(SendMessageUtil.CONTACT_FAILED_KEY);
                    if (LText.equal(value, SendMessageUtil.CONTACT_FAILED_VALUE)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * @param messageBean
     * @return
     */
    public static boolean isContactKeyMessage(@Nullable MessageBean messageBean) {
        try {
            if (messageBean != null && messageBean.extend != null && !messageBean.extend.isEmpty()) {
                JSONObject jsonObject = new JSONObject(messageBean.extend);
                String value = jsonObject.optString(SendMessageUtil.CONTACT_FAILED_KEY);
                if (LText.equal(value, SendMessageUtil.CONTACT_FAILED_VALUE)) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 添加沟通钥匙标记到消息里面
     *
     * @param b
     */
    public static void addContactKey2Message(@Nullable ChatBean b) {
        if (b != null) {
            try {
                if (b.message != null
                        && b.message.messageBody != null) {

                    String extend = b.message.messageBody.extend;
                    JSONObject jsonObject;
                    if (LText.empty(extend)) {
                        jsonObject = new JSONObject();
                    } else {
                        jsonObject = new JSONObject(extend);
                    }
                    if (!jsonObject.has(SendMessageUtil.CONTACT_FAILED_KEY)) {
                        jsonObject.put(SendMessageUtil.CONTACT_FAILED_KEY, SendMessageUtil.CONTACT_FAILED_VALUE);
                    }
                    b.message.messageBody.extend = jsonObject.toString();
                }

            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

    }

    public static String getChatExtend(ChatBean chatBean) {
        String extend = null;
        if (chatBean == null ||
                chatBean.message == null ||
                chatBean.message.messageBody == null) {
            return extend;
        }
        if (chatBean.message.messageBody.article != null) {
            extend = chatBean.message.messageBody.article.extend;
        } else if (chatBean.message.messageBody.job != null) {
            extend = chatBean.message.messageBody.job.extend;
        } else if (chatBean.message.messageBody.articleList != null) {
            ChatArticleBean element = LList.getElement(chatBean.message.messageBody.articleList, 0);
            if (element != null) {
                extend = element.extend;
            }
        } else if (chatBean.message.messageBody.interviewBean != null) {
            extend = chatBean.message.messageBody.interviewBean.extend;
        } else if (chatBean.message.messageBody.dialog != null) {
            extend = chatBean.message.messageBody.dialog.extend;
        } else if (chatBean.message.messageBody.hyperLinkBean != null) {
            extend = chatBean.message.messageBody.hyperLinkBean.extraJson;
        } else {
            extend = chatBean.message.messageBody.extend;
        }
        return extend;
    }

    @StringDef({COME_FROM_NONE, COME_FROM_GEEK_DETAIL_SHARE, COME_FROM_CHAT_ADD, COME_FROM_CHAT_SETTING})
    public @interface SourceFrom {
    }


    public static final String NONE_READ_MESSAGE = "未读消息";
    public static final String SINGLE_FILTER_PARAM_NAME = "singleFilter";


    public static final String HAS_ANSWER_QUESTION = "已答题";


    public static boolean isClickCommonReply;

    public static boolean isClickIKnow;


    public static final String FIRST_SELECT_FILTER = "FIRST_SELECT_FILTER";

    public static final String UPDATE_CHAT_FILTER = "UPDATE_CHAT_FILTER";

    public static final String UPDATE_CHAT_POSITION = "UPDATE_CHAT_POSITION";


    /**
     * 获得 草稿内容,需要把【表情】转化为 emotion 表情
     *
     * @param text
     * @return
     */
    public static CharSequence getContactDraft(@Nullable String text) {
        if (text != null) {
            if (containEmotion(text)) {
                return obtainEmotionSpannable(text);
            }
        }
        return text;
    }


    private static final String SP_INTERVIEW_CONFIRM_EXTEND = "SP_INTERVIEW_CONFIRM_EXTEND";


    public static void saveGeekInterviewConfirm(String extend, long friendUserId) {
        if (extend == null) return;
        Map<String, InterviewWaitConfirmBean> value;

        String extendValue = SpManager.get().user().getString(SP_INTERVIEW_CONFIRM_EXTEND, "");
        if (extendValue == null) {
            value = new HashMap<>();
        } else {
            value = GsonUtils.fromJson(extendValue, new TypeToken<Map<String, InterviewWaitConfirmBean>>() {
            }.getType());
            if (value == null) {
                value = new HashMap<>();
            }
        }

        value.put(String.valueOf(friendUserId), GsonUtils.fromJson(extend, InterviewWaitConfirmBean.class));

        if (value.size() > 0) {
            SpManager.get().user().edit().putString(SP_INTERVIEW_CONFIRM_EXTEND, GsonUtils.toJson(value)).apply();
        }

        //发送光标
        Intent intent = new Intent(Constants.RECEIVER_REFRESH_CHAT_TOP_STATUSs);
        ReceiverUtils.sendBroadcast(App.getAppContext(), intent);
    }


    public static InterviewWaitConfirmBean getUserWaitConfirmBean(String friendId) {
        String extendValue = SpManager.get().user().getString(SP_INTERVIEW_CONFIRM_EXTEND, "");
        if (extendValue != null) {
            Map<String, InterviewWaitConfirmBean> value = GsonUtils.fromJson(extendValue, new TypeToken<Map<String, InterviewWaitConfirmBean>>() {
            }.getType());
            if (value != null) {
                return value.get(friendId);
            }
        }

        return null;
    }


    /**
     * 获取大于指定 "时间戳" 的联系人
     *
     * @param timeMill 三天前的 "时间戳"
     * @return
     */
    public static List<ContactBean> getInterviewContactList(long timeMill) {
        List<ContactBean> singleContact = F2ContactHelper.getInstance().getSingleContact(false);
        List<ContactBean> result = new ArrayList<>();
        for (ContactBean contactBean : singleContact) {
            if (contactBean.lastChatTime >= timeMill
                    || contactBean.updateTime > timeMill) {
                result.add(contactBean);
            }
        }
        return result;
    }


    /**
     * 是否有标签
     *
     * @param message
     * @return
     */
    public static boolean containEmotion(String message) {
        int cursor = -1;
        while (true) {
            int startIndex = message.indexOf("[", cursor);
            if (startIndex < 0) break;
            int endIndex = message.indexOf("]", startIndex);
            if (endIndex < 0) break;
            cursor = startIndex + 1;
            String s = message.substring(startIndex, endIndex + 1);
            int id = InnerEmotionManager.getInstance().getResourceId(s);
            if (id > 0) {
                return true;
            }
        }
        return false;
    }

    public static SpannableString obtainEmotionSpannable(CharSequence charSequence) {
        SpannableString spannableString = new SpannableString(charSequence);
        List<Index> indexList = checkAllEmotion(spannableString.toString());

        int leftMargin = Scale.dip2px(App.getAppContext(), 1);
        int right = Scale.dip2px(App.getAppContext(), 22) + leftMargin;
        int bottom = Scale.dip2px(App.getAppContext(), 22);
        for (Index index : indexList) {
            Drawable drawable = ContextCompat.getDrawable(App.getAppContext(), index.resourceId);
            if (drawable != null) {
                drawable.setBounds(0, 0, right, bottom);
                ChatCenterImageSpan imageSpan = new ChatCenterImageSpan(drawable);
                imageSpan.setLeftMargin(leftMargin);
                spannableString.setSpan(imageSpan, index.start, index.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return spannableString;
    }


    public static Spannable obtainEmotionSpannable(Spannable spannableString) {

        boolean hasEmotion = containEmotion(spannableString.toString());

        if (hasEmotion) {
            List<Index> indexList = checkAllEmotion(spannableString.toString());
            int leftMargin = Scale.dip2px(App.getAppContext(), 1);
            int right = Scale.dip2px(App.getAppContext(), 22) + leftMargin;
            int bottom = Scale.dip2px(App.getAppContext(), 22);
            for (Index index : indexList) {
                Drawable drawable = ContextCompat.getDrawable(App.getAppContext(), index.resourceId);
                if (drawable != null) {
                    drawable.setBounds(0, 0, right, bottom);
                    ChatCenterImageSpan imageSpan = new ChatCenterImageSpan(drawable);
                    imageSpan.setLeftMargin(leftMargin);
                    spannableString.setSpan(imageSpan, index.start, index.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                }
            }
        }

        return spannableString;
    }


    public static SpannableString obtainEmotionSpannable2(String message, int emotionSize) {
        SpannableString spannableString = new SpannableString(message);
        List<Index> indexList = checkAllEmotion(message);
        int right = Scale.dip2px(App.getAppContext(), emotionSize);
        int bottom = Scale.dip2px(App.getAppContext(), emotionSize);
        for (Index index : indexList) {
            Drawable drawable = ContextCompat.getDrawable(App.getAppContext(), index.resourceId);
            if (drawable != null) {
                drawable.setBounds(0, 0, right, bottom);
                CenterImageSpan imageSpan = new CenterImageSpan(drawable);
                spannableString.setSpan(imageSpan, index.start, index.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return spannableString;
    }


    public static SpannableString obtainEmotionSpannable2(String message) {
        SpannableString spannableString = new SpannableString(message);
        List<Index> indexList = checkAllEmotion(message);
        int right = Scale.dip2px(App.getAppContext(), 16);
        int bottom = Scale.dip2px(App.getAppContext(), 16);
        for (Index index : indexList) {
            Drawable drawable = ContextCompat.getDrawable(App.getAppContext(), index.resourceId);
            if (drawable != null) {
                drawable.setBounds(0, 0, right, bottom);
                ImageSpan imageSpan = new ImageSpan(drawable);
                spannableString.setSpan(imageSpan, index.start, index.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        return spannableString;
    }

    private static class Index extends BaseEntity {

        private static final long serialVersionUID = -1;

        /**
         * 起始位置 include
         */
        private int start;
        /**
         * 结束位置 exclusive
         */
        private int end;
        /**
         * 资源的resId
         */
        @SuppressWarnings("twl_type_id_serialize")
        private int resourceId;
    }

    /**
     * 计算[]字符正在的位置坐标
     *
     * @param message
     * @return
     */
    private static List<Index> checkAllEmotion(String message) {
        List<Index> indexList = new ArrayList<>();
        if (message != null) {
            char[] chars = message.toCharArray();
            int length = chars.length - 1;
            int start = 0;
            int end = 0;
            while (start <= length && end <= length) {
                boolean canAdd = true;
                if (chars[start] == '[') {
                    end = start + 1;
                    while (end <= length) {
                        if (chars[end] == '[') {
                            start = end;
                            canAdd = false;
                            break;
                        }
                        if (chars[end] == ']') {
                            String s = message.substring(start, end + 1);
                            int id = InnerEmotionManager.getInstance().getResourceId(s);
                            if (id > 0) {
                                Index index = new Index();
                                index.start = start;
                                index.end = end + 1;
                                index.resourceId = id;
                                indexList.add(index);
                            }
                            break;
                        } else {
                            end++;
                        }
                    }
                }
                if (canAdd) {
                    start++;
                }
            }
        }
        return indexList;
    }


    private static final String SP_AN_XIN_BAO_JD = "SP_AN_XIN_BAO_JD";


    public static String getAnXInBaoJidFromDb() {
        return SpManager.get().user().getString(SP_AN_XIN_BAO_JD, "");
    }


    /**
     * 从消息解析 axbJid 保存到SP里面，
     * 后续打开
     * {@see DailyMatchPagerActivity}
     * 页面读取参数提交到接口
     *
     * @param articleBean 消息的article对象
     */
    public static void parserAnXinBaoJid(ChatBean chatBean, @Nullable ChatArticleBean articleBean, @Nullable List<ChatArticleBean> articleList) {
        ChatArticleBean tempArticleBean = articleBean;
        if (tempArticleBean == null) {
            tempArticleBean = LList.getElement(articleList, 0);
        }

        if (tempArticleBean == null) return;

        try {
            String extend = tempArticleBean.extend;
            if (LText.empty(extend)) return;

            String decode = URLDecoder.decode(extend);

            JSONObject jsonObject = new JSONObject(decode);


            AnXinBaoParam anXinBaoParam = AnXinBaoParam.getAnXinBaoParam();
            anXinBaoParam.title = jsonObject.optString("title");
            anXinBaoParam.fromName = RSingleMessageHandler.getFriendName(chatBean);

            if (jsonObject.has("axbJid")) {
                anXinBaoParam.axbJid = jsonObject.optString("axbJid");
            }

            /*保存数据到DB*/
            AnXinBaoParam.saveAnXinBaoJdParam(anXinBaoParam);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static class AnXinBaoParam extends BaseServerBean {

        private static final long serialVersionUID = -2707683955337155696L;

        public String axbJid;

        public String title;

        public String fromName;


        public static final String SP_AN_XIN_BAO_KEY = "SP_AN_XIN_BAO_KEY";

        /*获取安心保参数*/
        public static AnXinBaoParam getAnXinBaoParam() {
            AnXinBaoParam anXinBaoParam;
            String value = SpManager.get().user().getString(SP_AN_XIN_BAO_KEY, "");
            if (LText.empty(value)) {
                anXinBaoParam = new AnXinBaoParam();
                anXinBaoParam.axbJid = getAnXInBaoJidFromDb();
            } else {
                anXinBaoParam = GsonUtils.fromJson(value, AnXinBaoParam.class);
            }
            return anXinBaoParam;
        }

        /*保存安心保参数*/
        public static void saveAnXinBaoJdParam(@NonNull AnXinBaoParam anXinBaoParam) {
            SpManager.get().user().edit().putString(SP_AN_XIN_BAO_KEY, GsonUtils.toJson(anXinBaoParam)).apply();
            //1214进入今日速配使用，由于夸包，单独存储id使用
            SpManager.get().user().edit().putString(CommonConstant.SP_AN_XIN_BAO_ID_KEY, anXinBaoParam.axbJid).apply();
        }

    }

    /**
     * 为我的优势 生成标签
     *
     * @param advantageText
     * @param hasHighLight
     * @return
     */
    public static @Nullable SpannableString createAdvantageHighLightTag(@Nullable String advantageText, boolean hasHighLight, @DrawableRes int drawableRes) {

        if (LText.empty(advantageText)) return null;

        if (!hasHighLight) {
            return new SpannableString(advantageText);
        }

        String blackPlaceHolder = " ";
        String advantageValue = blackPlaceHolder + advantageText;

        SpannableString spannableString = new SpannableString(advantageValue);
        Context context = ForegroundUtils.get().getTopActivity() != null ? ForegroundUtils.get().getTopActivity() : App.getAppContext();
        Drawable drawable = ContextCompat.getDrawable(context, drawableRes);
        if (drawable != null) {
            int right = Scale.dip2px(App.getAppContext(), 42);
            int bottom = Scale.dip2px(App.getAppContext(), 18);
            int rightMargin = Scale.dip2px(App.getAppContext(), 4);
            drawable.setBounds(0, 0, right, bottom);
            ChatCenterImageSpan imageSpan = new ChatCenterImageSpan(drawable);
            imageSpan.setRightMargin(rightMargin);
            spannableString.setSpan(imageSpan, 0, blackPlaceHolder.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
        return spannableString;
    }

    public static @Nullable SpannableString createAdvantageHighLightTag(@Nullable String advantageText, boolean hasHighLight) {
        return createAdvantageHighLightTag(advantageText, hasHighLight, R.mipmap.ic_highlight_tag);
    }


    public static @Nullable SpannableString createRecommendReasonTag(@Nullable String recommendReason, @DrawableRes int resourceIcon) {

        String blackPlaceHolder = " ";
        Drawable drawable = ContextCompat.getDrawable(App.getAppContext(), resourceIcon);
        //设置图片高度和字体高度一致,达到文字图片剧中效果
        if (drawable != null) {
            drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        }
        SpannableString spannableString = new SpannableString(blackPlaceHolder + recommendReason);
        ChatCenterImageSpan imageSpan = new ChatCenterImageSpan(drawable);
        int rightMargin = Scale.dip2px(App.getAppContext(), 4);
        imageSpan.setRightMargin(rightMargin);
        spannableString.setSpan(imageSpan, 0, blackPlaceHolder.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spannableString;
    }


    public static void onContactActionReport() {
        AnalyticsFactory
                .create()
                .action(AnalyticsAction.ACTION_F2_EXPOSE_RED_DOT)
                .param("p", "聊天")
                .param("p2", F2ContactHelper.getInstance().checkF2HasSilent() ? 1 : 0)
                .build();
    }

    public static void onInteractActionReport() {
        AnalyticsFactory
                .create()
                .action(AnalyticsAction.ACTION_F2_EXPOSE_RED_DOT)
                .param("p", "互动")
                .param("p2", F2ContactHelper.getInstance().checkF2HasSilent() ? 1 : 0)
                .param("p3", F2ContactHelper.getInstance().createBgActionParams())
                .build();
    }


    @Deprecated
    public static List<ContactBean> getAllQuickHandleContact() {
        List<ContactBean> result = new ArrayList<>();
        List<ContactBean> contactList = F2ContactHelper.getInstance().getSingleContact(false);
        for (ContactBean contactBean : contactList) {

            if (contactBean.isIHaveSendMsgToFriend()) continue;

            if (contactBean.isCanFastHandler()) {
                result.add(contactBean);
            }
        }
        return result;
    }


    public static boolean isAiChatMsgMark(@Nullable ChatBean chatBean) {
        return !LText.empty(parserAiChatMsgMark(chatBean));
    }

    public static String parserAiChatMsgMark(@Nullable ChatBean chatBean) {
        if (chatBean == null) return "";
        if (chatBean.message == null) return "";
        if (chatBean.message.messageBody == null) return "";


        String extend = chatBean.message.messageBody.extend;
        if (!LText.empty(extend)) {

            try {
                return new JSONObject(extend).optString("aiChatMsgMark");
            } catch (Exception e) {
                e.printStackTrace();
            }

        }

        return "";
    }


    /**
     * 检测消息列表是否需要显示 【需要协助】
     *
     * @param chatBean
     * @return
     */
    public static boolean needBossIntervene(@Nullable ChatBean chatBean) {
        if (chatBean == null) return false;
        if (chatBean.message == null) return false;
        if (chatBean.message.messageBody == null) return false;

        if (RSingleMessageHandler.getMessFriendSource(chatBean) == ContactBean.FROM_BOSS) {
            String extend = chatBean.message.messageBody.extend;

            if (!LText.empty(extend)) {
                try {
                    return new JSONObject(extend).optInt("needBossIntervene") == 1;
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }

        return false;
    }

    public static boolean isSupportRevocation(ChatBean chatBean,boolean isDianZhang) {
        return RevocationMessageManager.checkCanRevocation(chatBean, isDianZhang)
                && chatBean.fromUserId == UserManager.getUID()
                && System.currentTimeMillis() - chatBean.time < TWO_MINUTES_TIME_SECONDS;
    }


    public static int AIContactCount=300;

    public static void setAIContactCount(int aiContactCount){
        if (aiContactCount>0) {
            AIContactCount=aiContactCount;
        }
    }

    public static void handleAssistConfirmMsg(ChatBean chatBean) {
        if (chatBean == null || chatBean.message == null || chatBean.message.messageBody == null ){
            return;
        }

        ChatActionBean action = chatBean.message.messageBody.action;
        try {
            JSONObject jsonObject = new JSONObject(action.extend);
            String actionType = jsonObject.optString("actionType");
            JSONObject assistConfirm = jsonObject.optJSONObject("assistConfirm");
            if (TextUtils.equals(actionType, "2") && assistConfirm != null) {
                IImportPushMsgService receiveImportPushMsgManager = ImportPushMsgRouter.getReceiveImportPushMsgManager();
                if (receiveImportPushMsgManager != null) {
                    receiveImportPushMsgManager.receiveNewMessage(chatBean);
                }
            }
        } catch (JSONException e) {
            TLog.error(RSingleMessageHandler.TAG, "parse 129 action extend fails. error = %s", e);
        }
    }
}