package com.hpbr.bosszhipin.module.contacts.util;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.config.MqttConfig;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.contact.ContactData;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactModeManager;
import com.hpbr.bosszhipin.utils.NotifyUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class ContactUtils {

    static long weekTimeInMillis;
    static long weekTimeInMillisExpired;

    /***
     * 1109.61 不重要消息弱化
     * @param contactBean
     * @return
     */
    public static boolean isGrayUnRead(ContactBean contactBean) {
        if (contactBean.isTop) {
            return false;
        }
        return isWeekAgeGray(contactBean);
    }

    public static boolean isWeekAgeGray(ContactBean contactBean) {
        long showTime = contactBean.getShowTime();
        if (showTime <= 0) { //产品要求 没有时间 不算 一周前的
            return false;
        }
//        if (showTime < 1697597339000L){ //测试数据
//            return true;
//        }
        return NotifyUtils.notifyDownplayingMessage() && isWeekAge(showTime);
    }


    /**
     * 判断日期是否在同一周
     *
     * @return
     */
    public static boolean isWeekAge(long time) {
        if (System.currentTimeMillis() > weekTimeInMillisExpired) {
            weekTimeInMillis = getWeekTimeInMillis();
            weekTimeInMillisExpired = weekTimeInMillis + 24 * 60 * 60 * 1000;
        }
        return weekTimeInMillis > time;
    }

    private static long getWeekTimeInMillis() {
        //获取按天 获取上周时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置时分秒等参数为0
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.WEEK_OF_MONTH, -1);
        return calendar.getTimeInMillis();
    }

    @NonNull
    public static List<ContactBean> getAllContactGroupType(int groupType) {
        List<ContactBean> contactList = F2ContactHelper.getInstance().getF2ContactList();
        List<ContactBean> contactBeans = new ArrayList<>();
        for (ContactBean contactBean : contactList) {
            //本地联系人
            if (F2ContactModeManager.getInstance().isLocalContact(contactBean.friendId)) continue;
            //系统联系人
            if (F2ContactModeManager.getInstance().isServerSysContact(contactBean.friendId))
                continue;
            //群
            if (contactBean.isGroup()) continue;
            //分类去掉 不感兴趣 不合适
            if (contactBean.isBlack || contactBean.isReject) continue;
            if (contactBean.getGroupType() == groupType) {
                contactBeans.add(contactBean);
            }
        }
        return contactBeans;
    }


    public static List<ContactBean> getAllQuickHandleContact() {
        List<ContactBean> result = new ArrayList<>();
        ContactData contactData = ContactManager.getInstance().getContactData();
        List<ContactBean> contactList = contactData.getF2ContactList();
        for (ContactBean contactBean : contactList) {
            if (!isSingle(contactBean)) continue;
            if (contactBean.isIHaveSendMsgToFriend()) continue;

            if (contactBean.isCanFastHandler()) {
                result.add(contactBean);
            }
        }
        return result;
    }


    public static boolean isSingle(ContactBean contactBean) {
        if (contactBean == null) return false;
        if (contactBean.friendId == 0) return false;
        if (contactBean.isGroup()) return false;
        if (contactBean.friendId <= 1000) return false;
        if (contactBean.friendId == MqttConfig.CHAT_CUSTOMER_ID) return false;
        if (F2ContactModeManager.getInstance().isLocalContact(contactBean.friendId)) return false;
        return true;
    }

}
