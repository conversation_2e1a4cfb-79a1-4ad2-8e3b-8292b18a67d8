package com.hpbr.bosszhipin.module.contacts.util;

import androidx.annotation.UiThread;
import androidx.annotation.WorkerThread;

import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.error.ErrorReason;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <AUTHOR>
 * 处理基础消息类
 * @since 710
 */
public  class MessageQuickHandleHelper<T>  {

    public interface OnBossQuickHandleListener<T> {

        /**
         * 获取联系人
         *
         * @param data false   不调用
         */
        @WorkerThread
        boolean onSuccessContact(List<ContactBean> data);

        /**
         * 数据
         *
         * @param data
         */
        @UiThread
        void onSuccessData(List<T> data);

        /**
         * 失败
         *
         * @param errorReason
         */
        @UiThread
        void onFailure(ErrorReason errorReason);
    }


}
