package com.hpbr.bosszhipin.module.contacts.util;

import android.net.Uri;

import com.hpbr.bosszhipin.utils.UploadFileUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.FileUploadResponse;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by guofeng
 * on 2017/11/22.
 * 上传多个图片工具类
 */

public class MultiplyFileUploadHelper {

    private int index;

    /**
     * 执行上传
     */
    public void executeUpload(List<Uri> fileList, final OnUploadCallBack completeCallBack) {

        final int count = LList.getCount(fileList);
        if (count == 0) {
            if (completeCallBack != null) {
                completeCallBack.onFailedListener(fileList);
            }
            return;
        }

        index = 0;

        final List<String> successUrlsList = new ArrayList<>();

        final List<Uri> failedFilesList = new ArrayList<>();

        for (final Uri uri : fileList) {
            UploadFileUtil.uploadImage(UploadFileUtil.REPORT, uri, new ApiRequestCallback<FileUploadResponse>() {
                @Override
                public void onSuccess(ApiData<FileUploadResponse> data) {
                    FileUploadResponse response = data.resp;
                    String url = response.url;
                    if (!LText.empty(url)) {
                        successUrlsList.add(url);
                    }
                }

                @Override
                public void onComplete() {
                    ++index;
                    if (index == count && completeCallBack != null) {
                        if (failedFilesList.isEmpty()) {
                            completeCallBack.onSuccessListener(successUrlsList);
                        } else {
                            completeCallBack.onFailedListener(failedFilesList);
                        }
                    }
                }

                @Override
                public void onFailed(ErrorReason reason) {
                    failedFilesList.add(uri);
                }
            });
        }
    }


    public interface OnUploadCallBack {


        void onSuccessListener(List<String> successUrlList);

        void onFailedListener(List<Uri> failedList);

    }

}
