package com.hpbr.bosszhipin.module.contacts.util;

import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.contacts.entity.QuickHandleBean;

import java.util.List;

/**
 * Author: <PERSON><PERSON>ou
 * Date: 2018/8/15.
 */
public interface OnQuickHandleListener {

    /**
     * 展示加载动画
     */
    void showLoadingAnimation();

    /**
     * 隐藏加载动画
     */
    void dismissLoadingAnimation();

    /**
     * 设置标题
     *
     * @param title 标题
     */
    void refreshTitleText(String title,int contactCount);

    /**
     * 刷新卡片模式adapter
     *
     * @param data
     */
    void refreshCardAdapter(List<QuickHandleBean> data, boolean hasMore);

    /**
     * 刷新消息模式adapter
     * @param data
     */
    void refreshMessageAdapter(List<QuickHandleBean> data, boolean hasMore);
}
