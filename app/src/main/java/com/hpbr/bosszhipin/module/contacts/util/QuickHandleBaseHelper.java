package com.hpbr.bosszhipin.module.contacts.util;


import android.app.Activity;
import android.os.Handler;
import android.text.TextUtils;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.ContactSortManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.module.contacts.entity.QuickHandleBean;
import com.hpbr.bosszhipin.module.main.fragment.manager.f2.F2ContactHelper;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.BossGetQuickHandleResumeListRequest;
import net.bosszhipin.api.BossGetQuickHandleResumeListResponse;
import net.bosszhipin.api.GeekGetQuickHandleJobListRequest;
import net.bosszhipin.api.GeekGetQuickHandleJobListResponse;
import net.bosszhipin.api.bean.ServerJobCardBean;
import net.bosszhipin.api.bean.ServerQuickHandleResumeBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.util.ArrayList;
import java.util.List;

import message.handler.MSGSender;
import message.handler.dao.MessageDaoFactory;

/**
 * Author: ZhouYou
 * Date: 2018/8/15.
 * 6.11 【梁瑶】快速处理列表分页加载逻辑由客户端处理
 * <p>
 * 本类统一处理Geek和Boss关于快速处理列表的数据组装以及分页加载
 */
public class QuickHandleBaseHelper {

    private static final int LOADING_CONTACT = 1;
    private static final int REFRESH = 2;

    /**
     * 每页加载的卡片数量
     */
    private static final int LOAD_SIZE = 100;
    /**
     * 页码
     */
    private int pageIndex = 1;
    private int totalPage;

    private Activity activity;
    private OnQuickHandleListener listener;

    private List<ContactBean> contactList = new ArrayList<>();

    private List<QuickHandleBean> sourceData = new ArrayList<>();

    public QuickHandleBaseHelper(Activity activity, OnQuickHandleListener listener) {
        this.activity = activity;
        this.listener = listener;
    }

    /**
     * 读取联系人
     */
    public void loadContact() {
        Runnable runnable = () -> {
            List<ContactBean> contactList = F2ContactHelper.getInstance().getFastHandleContact();
//            List<ContactBean> contactList = FastHandleChatContactManager.loadContactList();
            handler.obtainMessage(LOADING_CONTACT, contactList).sendToTarget();
        };
        if (!AppThreadFactory.POOL.isShutdown()) {
            AppThreadFactory.POOL.submit(runnable);
        }
    }

    private Handler handler = new Handler(msg -> {
        if (msg.what == LOADING_CONTACT) {
            List<ContactBean> list = (List<ContactBean>) msg.obj;
            if (list == null || list.isEmpty()) {
                ToastUtils.showText(R.string.fast_handle_empty_boss_prompt);
                AppUtil.finishActivity(activity);
            } else {
                contactList.addAll(list); // 获取所有的未读联系人
                // 1. 设置标题
                int size = list.size();
                if (UserManager.isBossRole()) {
                    int bossNotReadCount = F2ContactHelper.getInstance().getSingleContactNoneReadMsgCount();
//                    int bossNotReadCount = F2TabMessageCountManager.getInstance().getSingleContactNoneReadCount();
                    ;
                    listener.refreshTitleText(activity.getString(R.string.string_fast_handle_list_boss_title, bossNotReadCount), LList.getCount(contactList));
                } else
                    listener.refreshTitleText(activity.getString(R.string.string_fast_handle_list_title, size), LList.getCount(contactList));
                // 2. 获取总页数
                totalPage = (int) Math.ceil(size * 1.0f / LOAD_SIZE);
                // 3. 获取第一页数据
                getData(true);
            }
        } else if (msg.what == REFRESH) {
            listener.refreshCardAdapter(sourceData, pageIndex < totalPage);
            listener.refreshMessageAdapter(sourceData, pageIndex < totalPage);
            if (!LList.isEmpty(contactList)) {
                int size = 0;
                for (ContactBean contactBean : contactList) {
                    if (contactBean == null) continue;
                    if (contactBean.noneReadCount > 0) {
                        size++;
                    }
                }
                if (size <= 0) {
                    if (UserManager.isBossRole())
                        listener.refreshTitleText("未读消息", LList.getCount(contactList));
                    else
                        listener.refreshTitleText("极速处理", LList.getCount(contactList));
                } else {
                    //boss身份 只有最近沟通 已去掉了 快速处理，牛人身份  有 极速处理
                    if (UserManager.isBossRole()) {
                        int bossNotReadCount = F2ContactHelper.getInstance().getSingleContactNoneReadMsgCount();
//                        int bossNotReadCount = F2TabMessageCountManager.getInstance().getSingleContactNoneReadCount();

                        listener.refreshTitleText(activity.getString(R.string.string_fast_handle_list_boss_title, bossNotReadCount), LList.getCount(contactList));
                    } else
                        listener.refreshTitleText(activity.getString(R.string.string_fast_handle_list_title, size), LList.getCount(contactList));

                }
            }
        }
        return true;
    });

    /**
     * 页码增加
     */
    private void incrementPageIndex() {
        pageIndex++;
    }

    private static final String TAG = "QuickHandleBaseHelper";



    private String getSubListSecurityIds(int startIndex, int endIndex) {

        String result = "";
        if (startIndex < endIndex) {
            List<ContactBean> pageList = contactList.subList(startIndex, endIndex);
            StringBuilder stringBuilder = new StringBuilder();
            for (ContactBean contactBean : pageList) {
                if (contactBean == null) continue;
                if (LText.empty(contactBean.securityId)) continue;
                stringBuilder.append(contactBean.securityId);
                stringBuilder.append(";");
            }
            result = stringBuilder.toString();
        }

        if (LText.empty(result)) {
            String value = startIndex + "_" + endIndex + "_" + LList.getCount(contactList);
            TLog.info(TAG, "params is empty : %s", value);
        }

        return result;
    }

    /**
     * 获取请求参数
     *
     * @return
     */
    private String getRequestParamsString() {
        int size = contactList.size();
        if (size > 0) {
            if (size > LOAD_SIZE) {
                int index = pageIndex - 1;
                //(0,99)(100,199)(200,299)(300,399)...(500,520)
                int startIndex = index * LOAD_SIZE;
                int tempIndex = startIndex + LOAD_SIZE;
                int endIndex;
                if (tempIndex > size) {
                    endIndex = size;
                } else {
                    endIndex = tempIndex;
                }
                return getSubListSecurityIds(startIndex, endIndex);
            } else {
                int startIndex = 0;
                int endIndex = size;
                return getSubListSecurityIds(startIndex, endIndex);
            }
        }
        return "";
    }


    private void getData(boolean isFirstLoad) {
        String params = getRequestParamsString();

        //检测securityId是否为空
        checkSecurityIdEmpty(params);

        if (LText.empty(params)) return;

        if (UserManager.isBossRole()) {
            getResumeList(params, isFirstLoad);
        } else {
            getJobList(params, isFirstLoad);
        }
    }

    private static boolean hasSendNoSource;

    /**
     * 检测securityId是否为空
     *
     * @param params
     */
    private void checkSecurityIdEmpty(String params) {
        if (LText.empty(params)) {
            if (!hasSendNoSource) {
                ApmAnalyzer.create()
                        .action(ApmAnalyticsAction.ACTION_MISSING_SECURITY_ID, "quickFastHandle")
                        .param("p2", LList.getCount(contactList) + "")
                        .report();
            }
            hasSendNoSource = true;
        }
    }

    /**
     * 获取数据
     */
    public void getData() {
        getData(false);
    }

    /**
     * Boss的快速处理，获取简历列表
     *
     * @param params 请求参数
     */
    private void getResumeList(String params, boolean isFirstLoad) {
        BossGetQuickHandleResumeListRequest request = new BossGetQuickHandleResumeListRequest(new ApiRequestCallback<BossGetQuickHandleResumeListResponse>() {

            @Override
            public void onStart() {
                if (isFirstLoad) {
                    listener.showLoadingAnimation();
                }
            }

            @Override
            public void handleInChildThread(ApiData<BossGetQuickHandleResumeListResponse> data) {
                BossGetQuickHandleResumeListResponse resp = data.resp;
                if (resp != null) {
                    // 组装数据
                    List<QuickHandleBean> list = transfer2ResumeContact(resp.result);
                    data.setExtendParam("QuickHandleBean", list);
                }
            }

            @Override
            public void onSuccess(ApiData<BossGetQuickHandleResumeListResponse> data) {
                incrementPageIndex(); // 本页加载成功后，pageIndex++

                List<QuickHandleBean> list = (List<QuickHandleBean>) data.getExtendParam("QuickHandleBean");
                if (!LList.isEmpty(list)) {
                    sourceData.addAll(list);
                }
                listener.refreshCardAdapter(sourceData, pageIndex < totalPage);
                //刷新卡片模式adpter
                listener.refreshMessageAdapter(sourceData, pageIndex < totalPage);
            }

            @Override
            public void onComplete() {
                if (isFirstLoad) {
                    listener.dismissLoadingAnimation();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityIds = TextUtils.isEmpty(params) ? "" : params;
        HttpExecutor.execute(request);
    }

    /**
     * 牛人的快速处理，获取职位列表
     *
     * @param params 请求参数
     */
    private void getJobList(String params, boolean isFirstLoad) {
        GeekGetQuickHandleJobListRequest request = new GeekGetQuickHandleJobListRequest(new ApiRequestCallback<GeekGetQuickHandleJobListResponse>() {

            @Override
            public void onStart() {
                if (isFirstLoad) {
                    listener.showLoadingAnimation();
                }
            }

            @Override
            public void handleInChildThread(ApiData<GeekGetQuickHandleJobListResponse> data) {
                GeekGetQuickHandleJobListResponse resp = data.resp;
                if (resp != null) {
                    // 组装数据
                    List<QuickHandleBean> list = transfer2JobContact(resp.result);
                    data.setExtendParam("QuickHandleBean", list);
                }
            }

            @Override
            public void onSuccess(ApiData<GeekGetQuickHandleJobListResponse> data) {
                incrementPageIndex(); // 本页加载成功后，pageIndex++
                List<QuickHandleBean> list = (List<QuickHandleBean>) data.getExtendParam("QuickHandleBean");
                if (!LList.isEmpty(list)) {
                    sourceData.addAll(list);
                }
                listener.refreshCardAdapter(sourceData, pageIndex < totalPage);
            }

            @Override
            public void onComplete() {
                if (isFirstLoad) {
                    listener.dismissLoadingAnimation();
                }
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        request.securityIds = TextUtils.isEmpty(params) ? "" : params;
        HttpExecutor.execute(request);
    }

    private List<QuickHandleBean> transfer2JobContact(List<ServerJobCardBean> list) {
        List<QuickHandleBean> data = new ArrayList<>();
        if (!LList.isEmpty(list)) {
            for (ServerJobCardBean item : list) {
                if (item == null) continue;
                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(item.bossId, UserManager.getUserRole().get(), item.friendSource);
                QuickHandleBean bean = new QuickHandleBean();
                bean.jobBean = item;
                bean.contactBean = contactBean;
                data.add(bean);
            }
        }
        return data;
    }

    private List<QuickHandleBean> transfer2ResumeContact(List<ServerQuickHandleResumeBean> list) {
        List<QuickHandleBean> data = new ArrayList<>();
        if (!LList.isEmpty(list)) {
            for (ServerQuickHandleResumeBean item : list) {
                if (item == null) continue;
                ContactBean contactBean = ContactManager.getInstance().queryContactByFriendId(item.geekId, UserManager.getUserRole().get(), item.friendSource);
                QuickHandleBean bean = new QuickHandleBean();
                bean.resumeBean = item;
                bean.contactBean = contactBean;
                data.add(bean);
            }
        }
        return ContactSortManager.executeQuickHandleDefaultSort(data);
    }

    /**
     * 设置全部已读
     *
     * @param from 当前模式：1消息模式；2卡片模式
     */
    public void setReadAll(int from) {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_QUICK_PROCESS_READ_ALL)
                .param("p", from > 0 ? String.valueOf(from) : "")
                .param("p4", System.currentTimeMillis() + ".quick-process-name-card-list")
                .build();
        if (UserManager.isBossRole() && !LList.isEmpty(contactList)) {
            int size = 0;
            for (ContactBean contactBean : contactList) {
                if (contactBean == null) continue;
                if (contactBean.noneReadCount > 0) {
                    size++;
                }
            }
            if (size == 0) {
                ToastUtils.showText("已全部标为已读");
            }
        }
        if (listener != null) {
            listener.showLoadingAnimation();
        }
        cleanAllNoneReadContactMessage(() -> {
            handler.sendEmptyMessageDelayed(REFRESH, 300);
            handler.post(() -> {
                if (listener != null) {
                    listener.dismissLoadingAnimation();
                }
            });
        });
    }

    public void refresh() {
        handler.sendEmptyMessageDelayed(REFRESH, 300);
    }


    /**
     * 清空本地所有未读消息
     */
    public static void cleanAllNoneReadContactMessage(Runnable runnable) {
        //异步数据库查询是否有加密消息,加密消息不通知服务端已读
        App.getDbExecutor().execute(new Runnable() {
            @Override
            public void run() {
                final List<Long> bossIds = new ArrayList<>();
                final List<Long> dianZhangIds = new ArrayList<>();

                List<ContactBean> contactList = F2ContactHelper.getInstance().getSingleContact(true);

                for (ContactBean contact : contactList) {
                    //有未读 && 没有加密消息
                    if (contact.noneReadCount > 0) {
                        boolean hasLockMessage = MessageDaoFactory.getMessageDao().hasEncryptMessage(contact.friendId);
                        if (!hasLockMessage) {
                            //Boss联系人
                            if (contact.friendSource == ContactBean.FROM_BOSS) {
                                bossIds.add(contact.friendId);
                            } else {
                                // 店长联系人
                                dianZhangIds.add(contact.friendId);
                            }
                        }
                    }
                    contact.noneReadCount = 0;
                    //更新数据库
                    ContactManager.getInstance().insertOrUpdateAllField(contact, UserManager.getUserRole().get());
                }
                if (runnable != null) {
                    runnable.run();
                }
                //没有消息发送
                if (LList.isEmpty(bossIds) && LList.isEmpty(dianZhangIds)) return;

                //刷新F2列表
                ContactManager.getInstance().refreshContacts();
                //通知服务器 Boss已读
                MSGSender.sendMessageReaderMessage(bossIds, ContactBean.FROM_BOSS);
                //通知服务器 店长已读
                MSGSender.sendMessageReaderMessage(dianZhangIds, ContactBean.FROM_DIAN_ZHANG);
            }
        });
    }

}
