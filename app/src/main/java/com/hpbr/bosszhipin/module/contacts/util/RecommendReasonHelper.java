package com.hpbr.bosszhipin.module.contacts.util;

import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.chat.export.constant.ChatAnalyticsAction;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.BizSpUtil;
import com.hpbr.bosszhipin.utils.MapUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;

import net.bosszhipin.api.GetRecommendReasonResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: dubojun
 * date: 2024/12/30
 * description:
 **/
public class RecommendReasonHelper {
    private static final String TAG = "RecommendReasonHelper";

    public static final int MAX_LIMIT = 100;
    public static final String SEPARATOR = ",";


    private RecommendReasonHelper() {
    }

    private static class Singleton {
        public static final RecommendReasonHelper sInstance = new RecommendReasonHelper();
    }

    public static RecommendReasonHelper get() {
        return Singleton.sInstance;
    }

    public void requestRecommendReason() {
        if (UserManager.isBossRole()) {
            return;
        }

        if (UserManager.isGeekStudent()) {
            return;
        }


        List<ContactBean> newGreetContacts = ContactUtils.getAllContactGroupType(ContactBean.GroupType.NEW_GREETING);
        if (newGreetContacts.size() > MAX_LIMIT) {
            newGreetContacts = newGreetContacts.subList(0, MAX_LIMIT);
        }
        Map<Long, ContactBean> contactMap = handleTargetContacts(newGreetContacts);
        if (MapUtils.isEmpty(contactMap)) {
            return;
        }
        List<ContactBean> targetContacts = new ArrayList<>(contactMap.values());
        String securityIds = StringUtil.connectTextWithCharNew(",", targetContacts, contactBean -> contactBean.securityId);
        if (LText.empty(securityIds)) {
            TLog.info(TAG, "securityIds is null, do not request recommend reason");
            return;
        }

        SimpleApiRequest.POST(ChatUrlConfig.URL_GET_RECOMMEND_REASON)
                .addParam("securityIds", securityIds)
                .setRequestCallback(new SimpleApiRequestCallback<GetRecommendReasonResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<GetRecommendReasonResponse> data) {
                        if (data == null || data.resp == null || LList.isEmpty(data.resp.recommendData)) {
                            return;
                        }

                        for (GetRecommendReasonResponse.RecommendReason reason : data.resp.recommendData) {
                            if (reason == null || !reason.enableDisplay) {
                                continue;
                            }

                            ContactBean contactBean = contactMap.get(reason.friendId);
                            if (contactBean != null && contactBean.friendSource == reason.friendSource) {
                                contactBean.recommendReason = reason.reason;
                                if (contactBean.getGroupType() == ContactBean.GroupType.NEW_GREETING) {
                                    AnalyticsFactory.create().action(ChatAnalyticsAction.ACTION_GEEK_CONNECT_RECOMMENDATION_REASON_SHOW)
                                            .param("p2", contactBean.friendId)
                                            .param("p3", contactBean.jobId)
                                            .param("p4", reason.reason)
                                            .buildSync();
                                }
                            }
                        }

                        BizSpUtil.setHasReqRecommendReasonContacts(StringUtil.connectTextWithCharNew(SEPARATOR, data.resp.recommendData, recommendReason -> String.valueOf(recommendReason.friendId)));
                    }

                    @Override
                    public void onSuccess(ApiData<GetRecommendReasonResponse> data) {
                        if (data == null || data.resp == null || LList.isEmpty(data.resp.recommendData)) {
                            return;
                        }

                        ContactManager.getInstance().refreshContacts();
                    }
                })
                .execute();
    }

    private Map<Long, ContactBean> handleTargetContacts(List<ContactBean> contactBeans) {
        HashMap<Long, ContactBean> map = new HashMap<>();
        if (LList.isEmpty(contactBeans)) {
            return map;
        }

        // 上次请求过的不再请求
        String friendIds = BizSpUtil.getRecommendReasonContacts();
        List<String> friendIdList = new ArrayList<>();
        if (!LText.empty(friendIds)) {
            String[] friendIdArr = friendIds.split(SEPARATOR);
            Collections.addAll(friendIdList, friendIdArr);
        }

        for (ContactBean contactBean : contactBeans) {
            if (contactBean != null && !friendIdList.contains(String.valueOf(contactBean.friendId))) {
                map.put(contactBean.friendId, contactBean);
            }
        }
        return map;
    }
}
