package com.hpbr.bosszhipin.module.contacts.util;

import static com.hpbr.bosszhipin.event.ApmAnalyticsAction.ACTION_CHAT_BOSS_JOB_ID_ZERO;

import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.techwolf.lib.tlog.TLog;

/**
 * create by guofeng
 * date on 2022/10/18
 */

public class ReportUtil {


    private static boolean hasReport = false;


    private static final String TAG = "ChatBaseActivity";

    /**
     * 检查 上报 业务场景传jobId=0
     *
     * @param jobId
     */
    public static void reportNoJobId(long jobId, String className, String method) {
        if (UserManager.isBossRole()) {
            if (jobId <= 0) {

                ApmAnalyzer.create()
                        .action(ACTION_CHAT_BOSS_JOB_ID_ZERO)
                        .param("p2", className)
                        .param("p3", method)
                        .report();

                TLog.info(TAG, "ChatBossJobIdZero " + className + "=" + method);
            }
        }
    }
}