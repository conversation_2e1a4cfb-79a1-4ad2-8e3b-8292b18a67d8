package com.hpbr.bosszhipin.module.contacts.util;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.db.entry.GroupInfoBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.contacts.adapter.listener.OnMessageSendCallBack;
import com.hpbr.bosszhipin.module.contacts.entity.ChatBean;
import com.hpbr.bosszhipin.module.contacts.entity.protobuf.ChatImageInfoBean;
import com.hpbr.bosszhipin.module.contacts.service.ChatBeanFactory;
import com.hpbr.bosszhipin.module.contacts.service.ChatSendCallback;
import com.monch.lbase.widget.T;

import java.lang.ref.WeakReference;
import java.util.List;

import message.handler.MSGSender;
import message.handler.MessageTargetInfo;
import message.handler.MessageUtils;
import message.handler.dao.MessageDaoFactory;

/**
 * Created by guofeng on 2018/4/18.
 */

public final class SendMessageUtil {

    private MSGSender chatSendCommon = new MSGSender();

    /**
     * 单聊发送消息
     *
     * @param contactBean
     * @param text
     * @param onMessageSendCallBack
     */
    public void sentGeekGuessAskChatTextMessage(ContactBean contactBean, String text, int friendSource, final OnMessageSendCallBack onMessageSendCallBack) {
        ChatBean bean = chatSendCommon.sendTextMessage(MessageTargetInfo.fromContactBean(contactBean), text, friendSource, new ChatSendCallback() {
            @Override
            public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                if (onMessageSendCallBack != null) {
                    onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                }
            }
        }, 107);

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }

    public interface OnSaveFailedMessageCallBack {
        void onSaveMessageSuccessListener(ChatBean chatBean);
    }

    public static String CONTACT_FAILED_KEY = "contactFailedKey";

    public static String CONTACT_FAILED_VALUE = "未发送成功";



    public void sendContactKeyFailedChatTextMessage(ContactBean contactBean, String text, int friendSource,
                                                    final OnSaveFailedMessageCallBack onSaveFailedMessageCallBack) {
        ChatBean sendChatBean = ChatBeanFactory.getInstance().createText(MessageTargetInfo.fromContactBean(contactBean), text, friendSource);
        if (sendChatBean == null) return;
        sendChatBean.status = 2;
        sendChatBean.time = System.currentTimeMillis();

        ChatUtils.addContactKey2Message(sendChatBean);

        AppThreadFactory.POOL.execute(new Runnable() {
            @Override
            public void run() {

                //更新最后一个消息的文案
                contactBean.lastChatText = MessageUtils.getSingleChatLastText(sendChatBean, contactBean, false);
                contactBean.lastChatStatus = 2;
                contactBean.lastChatTime = System.currentTimeMillis();
                contactBean.updateTime = contactBean.lastChatTime;
                contactBean.setFriendStageNew(ContactBean.STAGE_HAVE_SEND_TO_FRIEND);
                ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());

                //保存一个失败的消息到数据库
                MessageDaoFactory.getMessageDao().saveChat(sendChatBean);
                //同步刷新UI
                App.get().getMainHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (onSaveFailedMessageCallBack != null) {
                            onSaveFailedMessageCallBack.onSaveMessageSuccessListener(sendChatBean);
                        }
                    }
                });
            }
        });

    }


    public void sendFailedAudioMessage(ContactBean contactBean,MessageTargetInfo targetInfo,
                                       String filePath, String url,
                                       int duration, int friendSource,OnSaveFailedMessageCallBack onSaveFailedMessageCallBack){
        ChatBean sendChatBean = ChatBeanFactory
                .getInstance().createSound(targetInfo, filePath, url, duration, friendSource);
        if (sendChatBean == null) return;
        sendChatBean.status = 2;
        sendChatBean.time = System.currentTimeMillis();

        if (onSaveFailedMessageCallBack != null) {
            onSaveFailedMessageCallBack.onSaveMessageSuccessListener(sendChatBean);
        }
        AppThreadFactory.POOL.execute(() -> {
            //更新最后一个消息的文案
            contactBean.lastChatText = MessageUtils.getSingleChatLastText(sendChatBean, contactBean, false);
            contactBean.lastChatStatus = 2;
            contactBean.lastChatTime = System.currentTimeMillis();
            contactBean.updateTime = contactBean.lastChatTime;
            ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
            //保存一个失败的消息到数据库
            MessageDaoFactory.getMessageDao().saveChat(sendChatBean);
        });
    }


    public void sendFailedImageMessage(ContactBean contactBean,
                                       MessageTargetInfo targetInfo,
                                       String url,
                                       int width,
                                       int height,
                                       String tinyUrl,
                                       int tinyWidth,
                                       int tinyHeight,OnSaveFailedMessageCallBack onSaveFailedMessageCallBack){
        ChatBean sendChatBean = ChatBeanFactory
                .getInstance().createPhoto(targetInfo,
                        url,
                        width,
                        height,
                        tinyUrl,
                        tinyWidth,
                        tinyHeight);
        if (sendChatBean == null) return;
        sendChatBean.status = 2;
        sendChatBean.time = System.currentTimeMillis();
        if (onSaveFailedMessageCallBack != null) {
            onSaveFailedMessageCallBack.onSaveMessageSuccessListener(sendChatBean);
        }
        AppThreadFactory.POOL.execute(() -> {
            //更新最后一个消息的文案
            contactBean.lastChatText = MessageUtils.getSingleChatLastText(sendChatBean, contactBean, false);
            contactBean.lastChatStatus = 2;
            contactBean.lastChatTime = System.currentTimeMillis();
            contactBean.updateTime = contactBean.lastChatTime;
            ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
            //保存一个失败的消息到数据库
            MessageDaoFactory.getMessageDao().saveChat(sendChatBean);
        });
    }


    public void sendFailedGifMessage(ContactBean contactBean,
                                     MessageTargetInfo targetInfo,
                                     String encSid,
                                     long packageId,
                                     long emotionId,
                                     ChatImageInfoBean origin,
                                     ChatImageInfoBean tiny,
                                     String name,OnSaveFailedMessageCallBack onSaveFailedMessageCallBack){
        ChatBean sendChatBean = ChatBeanFactory
                .getInstance().createGif(targetInfo,
                        encSid,
                        packageId,
                        emotionId,
                        origin,
                        tiny, name);

        if (sendChatBean == null) return;
        sendChatBean.status = 2;
        sendChatBean.time = System.currentTimeMillis();
        if (onSaveFailedMessageCallBack != null) {
            onSaveFailedMessageCallBack.onSaveMessageSuccessListener(sendChatBean);
        }
        AppThreadFactory.POOL.execute(() -> {
            //更新最后一个消息的文案
            contactBean.lastChatText = MessageUtils.getSingleChatLastText(sendChatBean, contactBean, false);
            contactBean.lastChatStatus = 2;
            contactBean.lastChatTime = System.currentTimeMillis();
            contactBean.updateTime = contactBean.lastChatTime;
            ContactManager.getInstance().insertOrUpdateAllField(contactBean, UserManager.getUserRole().get());
            //保存一个失败的消息到数据库
            MessageDaoFactory.getMessageDao().saveChat(sendChatBean);
        });
    }

    //https://zhishu.zhipin.com/wiki/yQ9VfCvSr6M bizType=21050090
    //https://zhishu.zhipin.com/wiki/ixOtowTjacj bizType=21050091
    //https://zhishu.zhipin.com/wiki/90IsiggvMWY bizType=21050092
    public static final int HELL_QUERY_BIZ_TYPE = 21050090;
    public static final int HANDLE_GREETING_BIZ_TYPE = 21050091;
    public static final int AI_RECRUIT_BIZ_TYPE = 21050092;

    public static final int COMMON_PHRASES_TYPE = 105;

    public void sentChatTextMessage(ContactBean contactBean,
                                    String text,
                                    String bizId, int bizType, String extend,
                                    final OnMessageSendCallBack onMessageSendCallBack) {


        ChatBean bean = chatSendCommon.sendTextMessage(MessageTargetInfo.fromContactBean(contactBean),
                text, contactBean.friendSource, bizId, bizType, extend, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        if (onMessageSendCallBack != null) {
                            onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }
                    }
                });

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }

    public void sentChatTextMessageWithBizIDBizId(ContactBean contactBean,
                                               String text,
                                               int friendSource,
                                               String bizId,int bizType,
                                               final OnMessageSendCallBack onMessageSendCallBack) {


        ChatBean bean = chatSendCommon.sendTextMessageBizIdBizType(MessageTargetInfo.fromContactBean(contactBean),
                text, friendSource, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        if (onMessageSendCallBack != null) {
                            onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }
                    }
                }, bizId,bizType);

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }

    /**
     * 单聊发送消息
     *
     * @param contactBean
     * @param text
     * @param quoteId
     * @param onMessageSendCallBack
     */
    public void sentChatTextMessageWithBizCode(ContactBean contactBean,
                                               String text,
                                               int friendSource,
                                               int bizCode,
                                               long quoteId,
                                               final OnMessageSendCallBack onMessageSendCallBack) {


        ChatBean bean = chatSendCommon.sendTextMessageWithBizCode(MessageTargetInfo.fromContactBean(contactBean),
                text, friendSource, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        if (onMessageSendCallBack != null) {
                            onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }
                    }
                }, bizCode, quoteId);

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }


    public void sendChatTextMessage(ContactBean contactBean, String text, int bizType, String extend, final OnMessageSendCallBack onMessageSendCallBack) {
        sendChatTextMessage(contactBean, text, null, bizType, extend, onMessageSendCallBack);
    }

    /**
     * 单聊发送消息
     *
     * @param contactBean
     * @param text
     * @param bizId
     * @param bizType
     * @param onMessageSendCallBack
     * @param extend
     */
    public void sendChatTextMessage(ContactBean contactBean, String text, String bizId, int bizType, String extend, final OnMessageSendCallBack onMessageSendCallBack) {
        //解决内存泄露
        final WeakReference<OnMessageSendCallBack> callBackWeakReference = new WeakReference<>(onMessageSendCallBack);

        ChatBean bean = chatSendCommon.sendTextMessage(MessageTargetInfo.fromContactBean(contactBean),
                text, contactBean.friendSource, bizId, bizType, extend, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {

                        OnMessageSendCallBack callBack = callBackWeakReference.get();
                        if (callBack != null) {
                            callBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }

                    }
                });

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }

    /**
     * 单聊发送消息
     *
     * @param contactBean
     * @param text
     * @param taskId
     * @param onMessageSendCallBack
     * @param extend
     */
    public void sendChatTextMessage(ContactBean contactBean, String text, long taskId,
                                    final OnMessageSendCallBack onMessageSendCallBack,
                                    String extend) {
        //解决内存泄露
        final WeakReference<OnMessageSendCallBack> callBackWeakReference = new WeakReference<>(onMessageSendCallBack);
        final int friendSource = contactBean != null ? contactBean.friendSource : 0;
        ChatBean bean = chatSendCommon.sendTextMessage(MessageTargetInfo.fromContactBean(contactBean),
                text, friendSource, new ChatSendCallback() {
                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {

                        OnMessageSendCallBack callBack = callBackWeakReference.get();
                        if (callBack != null) {
                            callBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }

                    }
                }, taskId, extend);

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }
    }


    public void sendGroupChatTextMessage(GroupInfoBean groupInfoBean,
                                         String text,
                                         List<Long> atList, long quoteId,ChatBean quoteChatBean,
                                         final OnMessageSendCallBack onMessageSendCallBack) {


        ChatBean bean = chatSendCommon.sendGroupTextMessage(
                MessageTargetInfo.fromGroupInfoBean(groupInfoBean),
                text,
                atList, quoteId,
                new ChatSendCallback() {
                    @Override
                    public void onSaveLocation(MessageTargetInfo targetInfo, ChatBean sendChatBean) {
                        super.onSaveLocation(targetInfo, sendChatBean);
                    }

                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        if (onMessageSendCallBack != null) {
                            onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }
                    }
                });

        bean.message.quoteChatBean = quoteChatBean;

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }

    }

    /**
     * 群聊发送消息
     *
     * @param groupInfoBean
     * @param text
     * @param onMessageSendCallBack
     */
    public void sendGroupChatTextMessage(GroupInfoBean groupInfoBean,
                                         String text,
                                         List<Long> atList,
                                         final OnMessageSendCallBack onMessageSendCallBack) {


        ChatBean bean = chatSendCommon.sendGroupTextMessage(
                MessageTargetInfo.fromGroupInfoBean(groupInfoBean),
                text,
                atList, 0,
                new ChatSendCallback() {
                    @Override
                    public void onSaveLocation(MessageTargetInfo targetInfo, ChatBean sendChatBean) {
                        super.onSaveLocation(targetInfo, sendChatBean);
                    }

                    @Override
                    public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                        if (onMessageSendCallBack != null) {
                            onMessageSendCallBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                        }
                    }
                });

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }
        if (onMessageSendCallBack != null) {
            onMessageSendCallBack.onMsgStartSendListener(bean);
        }

    }

    /**
     * 发送一条图片消息
     *
     * @param url          图片URL
     * @param width        图片的宽
     * @param height       图片的高
     * @param tinyUrl      缩略图URL
     * @param tinyWidth    缩略图的宽
     * @param tinyHeight   缩略图的高
     * @param friendSource
     */
    public void sendImageMessage(ContactBean contactBean,
                                 String url, int width,
                                 int height, String tinyUrl,
                                 int tinyWidth,
                                 int tinyHeight, int friendSource, final OnMessageSendCallBack callBack) {

        ChatBean bean = chatSendCommon.sendPhotoMessage(MessageTargetInfo.fromContactBean(contactBean), url, width, height, tinyUrl, tinyWidth, tinyHeight, friendSource, new ChatSendCallback() {
            @Override
            public void onSaveLocation(MessageTargetInfo targetInfo, ChatBean sendChatBean) {
                super.onSaveLocation(targetInfo, sendChatBean);
            }

            @Override
            public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                if (callBack != null) {
                    callBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                }
            }
        });

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }

        if (callBack != null) {
            callBack.onMsgStartSendListener(bean);
        }
    }

    /**
     * 发送一条图片消息
     *
     * @param url          图片URL
     * @param width        图片的宽
     * @param height       图片的高
     * @param tinyUrl      缩略图URL
     * @param tinyWidth    缩略图的宽
     * @param tinyHeight   缩略图的高
     * @param friendSource
     */
    public void sendGroupImageMessage(GroupInfoBean groupInfoBean,
                                      String url, int width,
                                      int height,
                                      String tinyUrl,
                                      int tinyWidth,
                                      int tinyHeight,
                                      int friendSource, final OnMessageSendCallBack callBack) {

        ChatBean bean = chatSendCommon.sendGroupPhotoMessage(MessageTargetInfo.fromGroupInfoBean(groupInfoBean), url, width, height, tinyUrl, tinyWidth, tinyHeight, friendSource, new ChatSendCallback() {
            @Override
            public void onComplete(boolean success, MessageTargetInfo targetInfo, ChatBean backChatBean) {
                if (callBack != null) {
                    callBack.onMsgSendSuccessListener(backChatBean.clientTempMessageId, success);
                }
            }
        });

        if (bean == null) {
            T.ss("发送消息失败");
            return;
        }

        if (callBack != null) {
            callBack.onMsgStartSendListener(bean);
        }
    }
}
