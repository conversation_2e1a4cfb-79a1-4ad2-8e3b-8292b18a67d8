package com.hpbr.bosszhipin.module.contacts.viewmodel;

import android.app.Application;

import com.bszp.kernel.utils.SingleLiveEvent;
import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.BaseViewModel;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.data.manager.ContactManager;
import com.twl.http.ApiData;

import net.bosszhipin.api.DrawerInfoResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

/**
 * @ClassName ：ContactDrawerSettingViewModel
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/9/21  5:54 PM
 */
public class ContactDrawerSettingViewModel extends BaseViewModel {

    /*获取系统抽屉信息成功*/
    public MutableLiveData<DrawerInfoResponse> getDrawerInfoLiveData = new SingleLiveEvent();
    /*置顶或取消置顶成功*/
    public MutableLiveData<Boolean> pinnedTopLiveData = new SingleLiveEvent();
    /*开启或关闭免打扰成功*/
    public MutableLiveData<Boolean> openNoDisturbLiveData = new SingleLiveEvent();

    public ContactDrawerSettingViewModel(@NonNull Application application) {
        super(application);
    }

    /**
     * 获取系统抽屉信息
     *
     * @param drawerId
     * @param securityId
     */
    public void getDrawerInfo(String drawerId, String securityId) {
        SimpleApiRequest.GET(ChatUrlConfig.URL_FRIEND_GET_DRAWER_INFO)
                .addParam("drawerId", drawerId)
                .addParam("securityId", securityId)
                .setRequestCallback(new SimpleApiRequestCallback<DrawerInfoResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void onSuccess(ApiData<DrawerInfoResponse> data) {
                        if (data == null || data.resp == null) return;
                        getDrawerInfoLiveData.postValue(data.resp);
                    }

                    @Override
                    public void onComplete() {
                        super.onComplete();
                        hideProgress();
                    }
                }).execute();
    }

    /**
     * 免打扰系统通知抽屉
     *
     * @param noDisturb 1开启免打扰 0 关闭免打扰
     */
    public void setNoDistrub(ContactBean contactBean, int noDisturb) {
        SimpleApiRequest.POST(ChatUrlConfig.URL_FRIEND_SET_NO_DISTURB)
                .addParam("friendId", contactBean.friendId)
                .addParam("securityId", contactBean.securityId)
                .addParam("noDisturb", String.valueOf(noDisturb))/*免打扰状态 1开启 0 未开启 */
                .setRequestCallback(new SimpleCommonApiRequestCallback<DrawerInfoResponse>() {
                    @Override
                    public void onStart() {
                        super.onStart();
                        showProgress();
                    }

                    @Override
                    public void handleInChildThread(ApiData<DrawerInfoResponse> data) {
                        super.handleInChildThread(data);
                        contactBean.noDisturb = noDisturb;
                        ContactManager.getInstance().updateNoDisturb(contactBean);
                    }

                    @Override
                    public void onSuccess(ApiData<DrawerInfoResponse> data) {
                        super.onSuccess(data);
                        openNoDisturbLiveData.postValue(noDisturb == 1);
                    }

                    @Override
                    public void onComplete() {
                        hideProgress();
                    }
                }).execute();
    }


}
