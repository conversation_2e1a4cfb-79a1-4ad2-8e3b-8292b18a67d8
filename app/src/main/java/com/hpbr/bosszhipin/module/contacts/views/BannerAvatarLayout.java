package com.hpbr.bosszhipin.module.contacts.views;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.facebook.drawee.generic.GenericDraweeHierarchy;
import com.facebook.drawee.generic.GenericDraweeHierarchyBuilder;
import com.facebook.drawee.generic.RoundingParams;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.List;

import zpui.lib.ui.utils.ZPUIDisplayHelper;

/**
 * create by guofeng
 * date on 2023/7/20
 */

public class BannerAvatarLayout extends LinearLayout {


    public BannerAvatarLayout(Context context) {
        super(context);
        initConfig();
    }

    public BannerAvatarLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initConfig();
    }

    public BannerAvatarLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initConfig();
    }

    private void initConfig() {
        setOrientation(HORIZONTAL);
        setGravity(Gravity.CENTER_VERTICAL);
    }

    //每个头像的宽高
    private static final int ITEM_SIZE = Scale.dip2px(App.get(), 24);

    //每个头像被遮挡的区域
    private static final int COVER_SIZE = Scale.dip2px(App.get(), 5);


    //刷新当前布局
    public void refreshLayout(@Nullable List<String> avatarList) {
        removeAllViews();
        if (avatarList == null || avatarList.isEmpty()) return;
        for (int i = 0; i < avatarList.size(); i++) {
            String url = LList.getElement(avatarList, i);
            addImageView(url, i);
        }
        addArrowRightView();
    }

    //添加头像View
    private void addImageView(String url, int index) {
        SimpleDraweeView simpleDraweeView = new SimpleDraweeView(getContext());
        simpleDraweeView.setImageURI(url);

        RoundingParams roundingParams = new RoundingParams();
        roundingParams.setRoundAsCircle(true);
        GenericDraweeHierarchyBuilder builder = new GenericDraweeHierarchyBuilder(getContext().getResources());
        GenericDraweeHierarchy hierarchy = builder.build();
        hierarchy.setRoundingParams(roundingParams);
        simpleDraweeView.setHierarchy(hierarchy);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.width = ITEM_SIZE;
        params.height = ITEM_SIZE;
        if (index != 0) {
            params.leftMargin = -COVER_SIZE;
        }
        simpleDraweeView.setLayoutParams(params);
        addView(simpleDraweeView);
    }



    //添加向右的箭头
    private void addArrowRightView() {
        ImageView imageView = new ImageView(getContext());
        imageView.setImageResource(R.mipmap.ic_arrow_black);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Scale.dip2px(App.get(), 6);
        addView(imageView, params);
    }


}