package com.hpbr.bosszhipin.module.contacts.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.IntDef;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.contacts.activity.ContactDrawerSettingActivity;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @ClassName ：DrawerSettingEntraceView
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2022/9/21  9:54 PM
 */
public class DrawerSettingEntraceView extends ConstraintLayout {

    private Context context;
    private long drawerType;
    private ImageView iv_setting;

    public DrawerSettingEntraceView(@NonNull Context context) {
        this(context, null);
    }

    public DrawerSettingEntraceView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DrawerSettingEntraceView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public DrawerSettingEntraceView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        this.context = context;
        initAttrs(context, attrs);
        init();
    }

    private void initAttrs(Context context, @Nullable AttributeSet attrs) {
        TypedArray mTypeArray = context.obtainStyledAttributes(attrs, R.styleable.drawer_setting_entrance);
        drawerType = mTypeArray.getInteger(R.styleable.drawer_setting_entrance_drawerType, 0);
        mTypeArray.recycle();
    }

    private void init() {
        initView();
        initEventListener();
    }

    private void initView() {
        View view = View.inflate(context, R.layout.layout_drawer_setting_entrance_view, this);
        iv_setting = view.findViewById(R.id.iv_setting);
        setVisibility(drawerType != 0 ? View.VISIBLE : View.GONE);
    }

    private void initEventListener() {
        iv_setting.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                if (drawerType == 0) return;
                if (drawerType == DrawerSettingEntraceView.DrawerType.SHORT_VIDEO) {
                    //如果短视频 需要埋点
                    AnalyticsFactory
                            .create()
                            .action(AnalyticsAction.ACTION_GET_VIDEO_ASSIST_SETTING_CLICK)
                            .build();
                }
                ContactDrawerSettingActivity.start(context, drawerType);
            }
        });
    }

    /**
     * 设置抽屉类型
     *
     * @param drawerType
     * @return
     */
    public DrawerSettingEntraceView setDrawerType(@DrawerType long drawerType) {
        this.drawerType = drawerType;
        setVisibility(drawerType != 0 ? View.VISIBLE : View.GONE);
        return this;
    }

    /**
     * 抽屉类型
     */
    @IntDef({DrawerType.LIVING_RECRUIT, DrawerType.TAI_TAN_STAR, DrawerType.SECURITY_OFFICER,
            DrawerType.HUNTER_SERVICE, DrawerType.EVERY_DAY_FIND, DrawerType.STUDY_REMIND, DrawerType.USER_COMMUNICATIONS_OFFICER,
            DrawerType.SHORT_VIDEO})
    @Retention(RetentionPolicy.SOURCE)
    public @interface DrawerType {
        /*直播招聘*/
        int LIVING_RECRUIT = 890;
        /*直课堂*/
        int TAI_TAN_STAR = 888;
        /*BOSS安全官*/
        int SECURITY_OFFICER = 887;
        /*猎头服务*/
        int HUNTER_SERVICE = 885;
        /*每日新发现*/
        int EVERY_DAY_FIND = 896;
        /*学习服务*/
        int STUDY_REMIND = 881;
        /*小直同学*/
        int USER_COMMUNICATIONS_OFFICER = 879;
        /*小直同学*/
        int SHORT_VIDEO = 876;
    }


}
