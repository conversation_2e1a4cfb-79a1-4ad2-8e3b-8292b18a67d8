package com.hpbr.bosszhipin.module.contacts.views;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.contacts.entity.AllDialogBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * 作者：YaLin
 * 日期：2016/6/6.
 */
public class GuideDialogHelper extends BaseActivity {
    private Dialog dialog;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        AllDialogBean bean = (AllDialogBean) getIntent().getSerializableExtra(Constants.DATA_ENTITY);
        if (bean == null) {
            finishActivity();
            return;
        }
        showAlertDialog(bean);
    }

    @Override
    protected void onDestroy() {
        dismissDialog();
        super.onDestroy();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        AllDialogBean bean = (AllDialogBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
        if (bean == null) {
            finishActivity();
            return;
        }
        dismissDialog();
        showAlertDialog(bean);
    }

    private void dismissDialog() {
        if (dialog != null) {
            dialog.setOnDismissListener(null);
            dialog.dismiss();
        }
    }

    private void showAlertDialog(final AllDialogBean bean) {
        dialog = new Dialog(this, R.style.Guide_Dialog_Transparent);
        LayoutInflater inflater = LayoutInflater.from(this);
        View dialogView = inflater.inflate(R.layout.view_dialog, null);
        MTextView title = (MTextView) dialogView.findViewById(R.id.tv_title);
        SimpleDraweeView icon = (SimpleDraweeView) dialogView.findViewById(R.id.iv_icon);
        MTextView text = (MTextView) dialogView.findViewById(R.id.tv_text);
        MTextView left = (MTextView) dialogView.findViewById(R.id.tv_left_button);
        MTextView right = (MTextView) dialogView.findViewById(R.id.tv_right_button);
        title.setText(bean.title);
        icon.setImageURI(StringUtil.getNetworkUri(bean.icon));
        text.setText(bean.text);
        left.setText(bean.leftButtonText);
        right.setText(bean.rightButtonText);
        dialogView.findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishActivity();
            }
        });
        left.setOnClickListener(new HandlerManagerListener(this, bean.leftButtonTarget, dialog));
        right.setOnClickListener(new HandlerManagerListener(this, bean.rightButtonTarget, dialog));
        dialog.setCancelable(false);
        dialog.addContentView(dialogView, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                finishActivity();
            }
        });
        dialog.show();
    }

    private static class HandlerManagerListener implements View.OnClickListener {

        private GuideDialogHelper activity;
        private String target;

        private Dialog dialog;

        HandlerManagerListener(GuideDialogHelper activity, String target, Dialog dialog) {
            this.activity = activity;
            this.target = target;
            this.dialog = dialog;
        }

        @Override
        public void onClick(View v) {
            if (dialog != null) {
                dialog.setOnDismissListener(null);
                dialog.dismiss();
            }
            ZPManager manager = new ZPManager(activity, target);
            manager.handler();
            activity.finishActivity();
        }
    }

    private void finishActivity() {
        AppUtil.finishActivity(this, ActivityAnimType.NONE);
    }

    public static class Builder {
        private Context mActivity;
        private AllDialogBean bean;

        public Builder(@NonNull Activity activity) {
            mActivity = activity;
        }

        public Builder data(@NonNull AllDialogBean bean) {
            this.bean = bean;
            return this;
        }

        public void show() {
            if (mActivity == null || bean == null) return;
            if (mActivity instanceof Activity && ((Activity) mActivity).isFinishing()) return;
            Intent intent = new Intent(mActivity, GuideDialogHelper.class);
            intent.putExtra(Constants.DATA_ENTITY, bean);
            AppUtil.startActivity(mActivity, intent, false, ActivityAnimType.NONE);
        }
    }
}
