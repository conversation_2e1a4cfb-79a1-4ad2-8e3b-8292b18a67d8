package com.hpbr.bosszhipin.module.contacts.views.boss;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.views.MTextView;

import static com.hpbr.bosszhipin.event.AnalyticsAction.ACTION_CHAT_VIRTUAL_PHONE_OPEN;

/**
 * create by guofeng
 * date on 2020/9/2
 */
public class VirtualPhoneExchangeDialog {

    private Context context;

    public VirtualPhoneExchangeDialog(Context context) {
        this.context = context;
    }

    private Dialog dialog;

    private String title;

    private String switchContent;

    private String content;

    public void setTitle(String title) {
        this.title = title;
    }

    public void setSwitchTitle(String switchContent) {
        this.switchContent = switchContent;
    }

    public void setContent(String content) {
        this.content = content;
    }


    //点击button事件
    private OnViewClickCallBack onViewClickCallBack;

    public void setOnViewClickCallBack(OnViewClickCallBack onViewClickCallBack) {
        this.onViewClickCallBack = onViewClickCallBack;
    }

    //是否打开了开关
    private boolean isOpenSwitch = false;

    //是否能编辑
    private boolean isEditSwitch = false;

    public void setEditSwitch(boolean editSwitch) {
        isEditSwitch = editSwitch;
    }

    public void setIsOpenSwitch(boolean isOpen) {
        isOpenSwitch = isOpen;
    }

    private ContactBean contactBean;

    public void setContactBean(ContactBean contactBean) {
        this.contactBean = contactBean;
    }

    public void show() {

        View view = LayoutInflater.from(context).inflate(R.layout.view_virtual_phone_exchange, null);

        ImageView mSwitchView = view.findViewById(R.id.mSwitchView);
        MTextView mCancelView = view.findViewById(R.id.mCancelView);
        MTextView mSureView = view.findViewById(R.id.mSureView);

        MTextView mTitleView = view.findViewById(R.id.mTitleView);
        MTextView mDescView = view.findViewById(R.id.mDescView);
        mTitleView.setText(title);


        mCancelView.setOnClickListener(v -> {

            dismiss();

            if (onViewClickCallBack != null) {
                onViewClickCallBack.onCancelClickListener();
            }

        });

        mSureView.setOnClickListener(v -> {
            dismiss();

            if (onViewClickCallBack != null) {
                onViewClickCallBack.onSureClickListener(isOpenSwitch);
            }

            AnalyticsFactory
                    .create()
                    .action(ACTION_CHAT_VIRTUAL_PHONE_OPEN)
                    .secId(contactBean.securityId)
                    .param("p2", isOpenSwitch ? 1 : 0)
                    .build();
        });
        mSwitchView.setImageResource(R.mipmap.ic_virtual_exchange_switch_open);
        mSwitchView.setOnClickListener(v -> {
            isOpenSwitch = !isOpenSwitch;
            checkSwitchStatus(mSwitchView, mDescView);

        });
        checkSwitchStatus(mSwitchView, mDescView);


        dialog = new Dialog(context, com.twl.ui.R.style.twl_ui_common_dialog);
        dialog.setCanceledOnTouchOutside(false);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        dialog.addContentView(view, lp);

        Window window = dialog.getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.dimAmount = 0.6f;
            params.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE;
            window.setAttributes(params);
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            }
        }

        dialog.show();
    }

    //检查开关是否打开
    private void checkSwitchStatus(ImageView mSwitchView, MTextView mDescView) {
        if (isEditSwitch) {
            if (isOpenSwitch) {
                mSwitchView.setImageResource(R.mipmap.ic_virtual_exchange_switch_open);
                mDescView.setText(switchContent);
            } else {
                mDescView.setText(content);
                mSwitchView.setImageResource(R.mipmap.ic_virtual_exchange_switch_close);
            }
        } else {
            mSwitchView.setImageResource(R.mipmap.ic_virtual_exchange_switch_enable);
            if (isOpenSwitch) {
                mDescView.setText(switchContent);
            } else {
                mDescView.setText(content);
            }
        }
    }


    //关闭窗口
    private void dismiss() {
        if (dialog != null) {
            dialog.dismiss();
        }
    }


    //点击按钮回掉
    public interface OnViewClickCallBack {

        void onCancelClickListener();

        void onSureClickListener(boolean isSwitchOpen);
    }

}