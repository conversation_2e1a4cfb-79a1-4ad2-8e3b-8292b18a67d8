package com.hpbr.bosszhipin.module.contacts.views.boss.manager;

import android.app.Activity;
import android.content.Intent;

import com.hpbr.bosszhipin.utils.ReceiverUtils;

/**
 * create by guofeng
 * date on 2020-06-10
 */
public class GeekLurkModeManager {


    public static final String ACTION_OPEN_LURK_MODE = "ACTION_OPEN_LURK_MODE";
    public static final String ACTION_CLOSE_LURK_MODE = "ACTION_CLOSE_LURK_MODE";



    public static void sendOpenLurkModeAction(Activity activity) {
        Intent intent = new Intent();
        intent.setAction(ACTION_OPEN_LURK_MODE);
        ReceiverUtils.sendBroadcast(activity, intent);
    }


    public static void sendCLoseLurkModeAction(Activity activity) {
        Intent intent = new Intent();
        intent.setAction(ACTION_CLOSE_LURK_MODE);
        ReceiverUtils.sendBroadcast(activity, intent);
    }
}
