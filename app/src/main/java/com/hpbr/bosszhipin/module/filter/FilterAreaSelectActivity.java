package com.hpbr.bosszhipin.module.filter;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.basedata.core.ExceptionConstants;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.commend.activity.search.CitySelectActivity;
import com.hpbr.bosszhipin.module.commend.activity.search.CitySelectIntentParams;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.entity.DistanceLocationBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.fragment.geek.GFindFilterPresenter;
import com.hpbr.bosszhipin.module.main.views.filter.bossf1.DistrictSelectionPanelView;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.module_geek_export.GeekConsts;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.service.LocationService;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.UiUtils;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetGeekBusinessDistrictResponse;
import net.bosszhipin.base.HttpResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleApiRequestCallback;

import java.util.List;
import com.hpbr.bosszhipin.utils.GeekF1GrayUtils;

/**
 * 筛选栏-地址选择页面
 *
 * <AUTHOR>
 * @version 7.05
 */
public class FilterAreaSelectActivity extends BaseActivity {
    private static final String TAG="FilterAreaSelectAct";
    public static final int REQUEST_SWITCH_CITY = 0x01;
    public static final int REQUEST_SELECT_AREA = 0x02;
    protected static final String INTENT_EXPECT_JOB = "intent_expect_job";
    protected static final String INTENT_JOB = "intent_job";
    protected static final String INTENT_LOCATION = "intent_location";
    public static final String INTENT_FROMCITYSELECT = "intent_fromcityselect";
    /**
     * geek 从搜索页进来的，其他不传值时，认为是从F1进来。
     */
    public static final int INTENT_GEEK_FROM_F1_GEEK = 0; //从职场人牛人F1进入 - 普通期望
    public static final int INTENT_GEEK_FROM_SEARCH = 1; //从搜索进入
    public static final int INTENT_GEEK_FROM_MAP_FIND_LIST = 2; // 从地图找工作列表进入
    public static final int INTENT_GEEK_FROM_MAP_FIND_TOP_BAR = 3; // 从地图找工作
    public static final int INTENT_GEEK_FROM_PLAN_EIGHT = 4; // 从方案八开口进入
    public static final int INTENT_GEEK_FROM_F1_STUDENT = 5; // 从学生 F1进入

    public static final int INTENT_GEEK_FROM_F1_STUDENT_MIX = 10; // 从学生 F1进入
    public static final int INTENT_GEEK_FROM_F1_GEEK_MIXED = 6; //从职场人牛人F1进入 - 混合期望
    public static final int INTENT_GEEK_FROM_SIMILAR_COMPANY = 7; //从相似公司进来
    public static final int INTENT_GEEK_FROM_INDUSTRY_BOTTOM = 8; //从行业主页底部弹层进入
    public static final int INTENT_GEEK_FROM_SELECT_EXPECT_LOCATION = 9; //从选择期望地点

    public static final String INTENT_GEEK_FROM = "intent_geek_from";
    public static final String INTENT_DISTRICT_DATA = "intent_district_data";
    public static final String INTENT_SUBWAY_DATA = "intent_subway_data";
    public static final String INTENT_DISTANCE_DATA = "intent_distance_data";
    public static final String INTENT_SWITCH_CITY = "intent_switch_city";
    public static final String INTENT_LOCATION_ID = "intent_location_id";
    public static final String INTENT_LOCATION_NAME = "intent_location_name";
    public static final String INTENT_IS_NO_SUBLIST = "intent_is_no_sublist";

    public static final String INTENT_DISTRICT_HISTORY_OFTEN_SELECTED_SIZE = "intent_district_history_often_selected_size";

    public static final String INTENT_DISTRICT_DATA_ALL = "intent_district_data_all";
    public static final String INTENT_SUBWAY_DATA_ALL = "intent_subway_data_all";
    public static final String INTENT_DISTANCE_DATA_ALL = "intent_distance_data_all";

    public static final String INTENT_IS_SHOW_NEAR_BY = "intent_is_show_near_by";
    public static final String INTENT_IS_SHOW_SUBWAY = "intent_is_show_subway";


    private JobBean mCurJobForBoss;
    private JobIntentBean mCurExpectJobForGeek;
    /**
     * 定位城市ID
     */
    private long mLocationId;

    private final boolean isMultiType = true;
    /**
     * 定位城市名字
     */
    private String mLocationName;
    private AppTitleView mTitleView;
    private LinearLayout llHistory;
    private RecyclerView rvHistory;
    private DistrictSelectionPanelView mDistrictSelectionPanelView;

    private LevelBean mSelectedDistrictData;
    private LevelBean mSelectedSubwayData;
    private DistanceLocationBean mSelectedDistanceLocationData;

    private LevelBean mDistrictData;
    private DistanceLocationBean mDistanceLocationData;

    private LevelBean districtOrigin;
    /**
     * 来自哪里，如果是搜索页面过来的，需要隐藏"切换城市"按钮
     */
    private int mFrom;

    /**
     * 是否显示附近（距离）选项
     */
    private final boolean isShowDistanceSelection = true;
    /**
     * 是否显示附近（地铁）选项
     */
    private boolean isShowSubway = true;

    /**
     * 牛人进入该页面的统一入口
     *
     * @param activity             页面Activity
     * @param curExpectJob         当前期望职位
     * @param districtData         商圈数据
     * @param subwayData           地铁数据
     * @param distanceLocationData 距离数据
     */
    public static void startGeekFilter(Activity activity,
                                       JobIntentBean curExpectJob,
                                       LevelBean districtData,
                                       LevelBean subwayData,
                                       DistanceLocationBean distanceLocationData,
                                       int from) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterAreaSelectActivity.class);
        intent.putExtra(INTENT_EXPECT_JOB, curExpectJob);
        intent.putExtra(INTENT_DISTRICT_DATA, districtData);
        intent.putExtra(INTENT_SUBWAY_DATA, subwayData);
        intent.putExtra(INTENT_DISTANCE_DATA, distanceLocationData);
        intent.putExtra(INTENT_GEEK_FROM, from);
        AppUtil.startActivityForResult(activity, intent, REQUEST_SELECT_AREA, ActivityAnimType.UP_GLIDE);
    }

    public static void startGeekFilter(Activity activity,
                                       JobIntentBean curExpectJob,
                                       LevelBean districtData,
                                       LevelBean subwayData,
                                       DistanceLocationBean distanceLocationData,
                                       int from,
                                       long locationId,
                                       String locationName, boolean isShowDistanceSelection, boolean isShowSubway) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterAreaSelectActivity.class);
        intent.putExtra(INTENT_EXPECT_JOB, curExpectJob);
        intent.putExtra(INTENT_DISTRICT_DATA, districtData);
        intent.putExtra(INTENT_SUBWAY_DATA, subwayData);
        intent.putExtra(INTENT_DISTANCE_DATA, distanceLocationData);
        intent.putExtra(INTENT_GEEK_FROM, from);
        intent.putExtra(INTENT_LOCATION_ID, locationId);
        intent.putExtra(INTENT_LOCATION_NAME, locationName);
        intent.putExtra(INTENT_IS_SHOW_NEAR_BY, isShowDistanceSelection);
        intent.putExtra(INTENT_IS_SHOW_SUBWAY, isShowSubway);
        AppUtil.startActivityForResult(activity, intent, REQUEST_SELECT_AREA, ActivityAnimType.UP_GLIDE);
    }

    /**
     * boss进入该页面的统一入口
     *
     * @param activity                调用
     * @param isShowDistanceSelection 是否显示附近（距离）选项 (false:不显示)
     */
    public static void startBossFilter(Activity activity,
                                       JobBean selectedCurJob,
                                       LevelBean selectedDistrictData,
                                       DistanceLocationBean selectedDistanceLocationData,
                                       LevelBean districtData,
                                       DistanceLocationBean distanceLocationData,
                                       boolean isShowDistanceSelection) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterAreaSelectActivity.class);
        intent.putExtra(INTENT_JOB, selectedCurJob);
        intent.putExtra(INTENT_DISTRICT_DATA, selectedDistrictData);
        intent.putExtra(INTENT_DISTANCE_DATA, selectedDistanceLocationData);
        intent.putExtra(INTENT_DISTRICT_DATA_ALL, districtData);
        intent.putExtra(INTENT_DISTANCE_DATA_ALL, distanceLocationData);
        intent.putExtra(INTENT_IS_SHOW_NEAR_BY, isShowDistanceSelection);

        AppUtil.startActivityForResult(activity, intent, REQUEST_SELECT_AREA, ActivityAnimType.UP_GLIDE);
    }


    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_filter_page_area);
        initReceiver();
        initData();
        initView();
    }

    private void initReceiver() {
        ReceiverUtils.register(this, mReceiver, Constants.RECEIVER_CHANGE_LOCATION);
    }

    private String  handleRightTitle(){
        TextView tvBtnAction = mTitleView.getTvBtnAction();
        if (tvBtnAction!=null){
            Drawable drawable = ResourcesCompat.getDrawable(getResources(),R.mipmap.geek_icon_filter_area_right,null);
            int dpw = (int) UiUtils.convertDpToPixel(12, this);
            int dph = (int) UiUtils.convertDpToPixel(12, this);
            int dp4 = (int) UiUtils.convertDpToPixel(4, this);
            if (drawable!=null){
                drawable.setBounds(0, 0, dpw, dph);
                tvBtnAction.setCompoundDrawables(drawable, null,null , null);
                tvBtnAction.setCompoundDrawablePadding(dp4);
            }
            mTitleView.setActionButtonColorBlue();
        }
       return "筛选城市";
    }

    private void initView() {
        mTitleView = findViewById(R.id.title_view);
        rvHistory = findViewById(R.id.rvHistory);
        llHistory = findViewById(R.id.llHistory);
        mTitleView.setDividerInvisible();
        mTitleView.setBackClickListener(R.mipmap.ic_action_close_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
        if (UserManager.isGeekRole() && !isFromGeekPlan8() && !isFromSelectExpectLocation()) {
            // 只有牛人角色 并且是F1进来 才会显示
            String rightTitle;
            if (GeekF1GrayUtils.isChangeCityExperiment() &&(isGeekF1()||isStudentF1())){
                rightTitle=handleRightTitle();
            }else if ((GeekF1GrayUtils.isChangeExpectCityGroup4Experiment() &&isGeekSearch())){
                rightTitle=handleRightTitle();
            }else {
                rightTitle="切换城市";
            }
            mTitleView.setActionButtonListener(rightTitle, new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    if (isGeekMapJob()) {
                        switchCityForMapJob();
                    } else {
                        switchCity();
                    }
                }
            });
        }
        mDistrictSelectionPanelView = findViewById(R.id.districtSelectionPanelView);
        mDistrictSelectionPanelView.setSupportMultiSelect(supportMultiSelectMode() || isFromSelectExpectLocation());
        mDistrictSelectionPanelView.setVisibility(View.INVISIBLE);
        if (isGeekSearch() || isGeekF1() || isStudentF1() || isFromCompany() || isFromSelectExpectLocation()) {
            requestDistrictData();
        } else {
            flushViewData();
        }
        mDistrictSelectionPanelView.setOnTabClickListener(switchIndex -> {
            if (isGeekF1()|| isStudentF1()){
                AnalyticsFactory.create().action("geek-Filter-screening-click").param("p", switchIndex==DistrictSelectionPanelView.INDEX_BUSINESS_DISTRICT?"1":"2").build();
            }
        });

        if (isFromSelectExpectLocation()) {
            mDistrictSelectionPanelView.setLeftListVisibility(View.GONE);
        }
    }


    private void flushViewData() {

        if (districtOrigin == null) {
            districtOrigin = setupBusinessDistrictData();
        }

        if (isGeekSearch() && (districtOrigin == null || LList.getCount(districtOrigin.subLevelModeList) == 0)) {//C端搜索 && 没有子层级数据列表，此时返回到原来页面
            Intent intent = new Intent();
            intent.putExtra(INTENT_IS_NO_SUBLIST, true);
            setResult(Activity.RESULT_OK, intent);
            AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.NONE);
            return;
        }

        mDistrictSelectionPanelView.setVisibility(View.VISIBLE);
        // 这里注意一下，关于附近的筛选只支持boss身份
        mDistrictSelectionPanelView.setAreaData(districtOrigin, setupSubwayData(), setupDistanceLocationData(), mCurJobForBoss);

        mDistrictSelectionPanelView.setListener(new DistrictSelectionPanelView.OnLocationSelectedListener() {

            @Override
            public void onLocationSelected(LevelBean locationBean, LevelBean subwayBean, DistanceLocationBean distanceLocationBean) {
                if (isFromSelectExpectLocation()) {
                    saveBusinessDistrict(new SimpleApiRequestCallback<HttpResponse>() {
                        @Override
                        public void onComplete() {
                            confirm(locationBean, subwayBean, distanceLocationBean);
                        }
                    });
                } else {
                    confirm(locationBean, subwayBean, distanceLocationBean);
                }
            }

            @Override
            public void onLocationSelected(LevelBean locationBean, LevelBean subwayBean, DistanceLocationBean distanceLocationBean, int num) {
                mSelectedDistrictData = locationBean;
                mSelectedSubwayData = subwayBean;
                mSelectedDistanceLocationData = distanceLocationBean;
                handleTitle();
            }
        });


        mDistrictSelectionPanelView.setSelectedAreaItem(mSelectedDistrictData, mSelectedSubwayData, mSelectedDistanceLocationData);
        handleTitle();
    }


    private void confirm(LevelBean locationBean, LevelBean subwayBean, DistanceLocationBean distanceLocationBean) {
        mSelectedDistrictData = locationBean;
        mSelectedSubwayData = subwayBean;
        mSelectedDistanceLocationData = distanceLocationBean;
        Intent intent = new Intent();
        intent.putExtra(INTENT_DISTRICT_DATA, mSelectedDistrictData);
        intent.putExtra(INTENT_SUBWAY_DATA, mSelectedSubwayData);
        intent.putExtra(INTENT_DISTANCE_DATA, mSelectedDistanceLocationData);
        setResult(Activity.RESULT_OK, intent);
        intent.putExtra(INTENT_GEEK_FROM, mFrom);
        AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.UP_GLIDE);
    }




    /**
     * 从地址中间页进入 直接保存商圈
     *
     * @param callback
     */
    private void saveBusinessDistrict(SimpleApiRequestCallback<HttpResponse> callback) {
        long cityCode = 0;
        if (mCurExpectJobForGeek != null) {
            cityCode = mCurExpectJobForGeek.locationIndex;
        }
        if (mSelectedDistrictData != null) {
            cityCode = mSelectedDistrictData.code;
        }
        SimpleApiRequest.POST(GeekUrlConfig.URL_GET_F1_DISTRICT_SAVE)
                .addParam("cityCode", cityCode)
                .addParam("multiBusinessDistrict", getSelectBusinessDistrict())
                .addParam("additionalSave", 0)
                .addParam("source", 0)
                .setRequestCallback(callback)
                .execute();
    }

    private String getSelectBusinessDistrict() {
        if (mSelectedDistrictData == null) {
            return "";
        }
        JsonArray array = new JsonArray();
        if (!LList.isEmpty(mSelectedDistrictData.subLevelModeList)) {
            for (LevelBean levelBean : mSelectedDistrictData.subLevelModeList) {
                JsonObject object = new JsonObject();
                try {
                    if (LList.getCount(levelBean.subLevelModeList) == 1 && levelBean.subLevelModeList.get(0).code == levelBean.code) {
                        object.addProperty(String.valueOf(levelBean.code), "");
                    } else {
                        object.addProperty(String.valueOf(levelBean.code), getDistrictString(levelBean.subLevelModeList));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                array.add(object);
            }
            return array.toString().replace("[", "").replace("]", "");
        }
        return "";
    }

    private String getDistrictString(List<LevelBean> list) {

        if (!LList.isEmpty(list)) {
            return StringUtil.connectTextWithChar("_", list, new StringUtil.ValueProvider<LevelBean>() {

                @Override
                public String getValue(@NonNull LevelBean bean) {
                    return String.valueOf(bean.code);
                }
            });
        }
        return "";
    }


    /**
     * 如果是牛人搜索页进来
     *
     * @return
     */
    private boolean isGeekSearch() {
        return mFrom == INTENT_GEEK_FROM_SEARCH;
    }

    private boolean isGeekF1() {
        return mFrom == INTENT_GEEK_FROM_F1_GEEK || mFrom == INTENT_GEEK_FROM_F1_GEEK_MIXED;
    }

    private boolean isStudentF1() {
        return mFrom == INTENT_GEEK_FROM_F1_STUDENT || mFrom == INTENT_GEEK_FROM_F1_STUDENT_MIX;
    }

    private boolean isFromGeekPlan8() {
        return mFrom == INTENT_GEEK_FROM_PLAN_EIGHT;
    }

    private boolean isFromSelectExpectLocation() {
        return mFrom == INTENT_GEEK_FROM_SELECT_EXPECT_LOCATION;
    }

    private boolean isFromCompany() {
        return mFrom == INTENT_GEEK_FROM_SIMILAR_COMPANY || mFrom == INTENT_GEEK_FROM_INDUSTRY_BOTTOM;
    }

    /**
     * 来源903地图找工作
     *
     * @return
     */
    private boolean isGeekMapJob() {
        return mFrom == INTENT_GEEK_FROM_MAP_FIND_LIST || mFrom == INTENT_GEEK_FROM_MAP_FIND_TOP_BAR;
    }


    private void initData() {
        Intent intent = getIntent();
        if (UserManager.isBossRole()) {
            mCurJobForBoss = (JobBean) intent.getSerializableExtra(INTENT_JOB);
        } else {
            mCurExpectJobForGeek = (JobIntentBean) intent.getSerializableExtra(INTENT_EXPECT_JOB);
        }
        mSelectedDistrictData = (LevelBean) intent.getSerializableExtra(INTENT_DISTRICT_DATA);
        mSelectedSubwayData = (LevelBean) intent.getSerializableExtra(INTENT_SUBWAY_DATA);
        mSelectedDistanceLocationData = (DistanceLocationBean) intent.getSerializableExtra(INTENT_DISTANCE_DATA);

        mDistrictData = (LevelBean) intent.getSerializableExtra(INTENT_DISTRICT_DATA_ALL);
        mDistanceLocationData = (DistanceLocationBean) intent.getSerializableExtra(INTENT_DISTANCE_DATA_ALL);
        //vip提示条
//        topBarResponseData = GeekVipManager.getInstance().getGeekVipData();
        mFrom = intent.getIntExtra(INTENT_GEEK_FROM, 0);
        mLocationId = intent.getLongExtra(INTENT_LOCATION_ID, 0);
        mLocationName = intent.getStringExtra(INTENT_LOCATION_NAME);

        //是否显示附近（距离）选项

        isShowSubway = intent.getBooleanExtra(INTENT_IS_SHOW_SUBWAY, true);
    }

    private void switchCity() {
        int sortType=1;
        if (mCurExpectJobForGeek != null) {
            sortType = mCurExpectJobForGeek.sortType;
            AnalyticsFactory.create().action("change-city")
                    .param("p", String.valueOf(mCurExpectJobForGeek.locationIndex))
                    .param("p2", String.valueOf(mCurExpectJobForGeek.positionClassIndex))
                    .build();
        } else if (mCurJobForBoss != null) {
            AnalyticsFactory.create().action("change-city")
                    .param("p", String.valueOf(mCurJobForBoss.locationIndex))
                    .param("p2", String.valueOf(mCurJobForBoss.positionClassIndex))
                    .build();
        }


        CitySelectActivity.startActivityForResult(
                this,
                CitySelectIntentParams.obj()
                        .setShowAll(false)
                        .setSourceFrom(getCityPageSoureParam(mFrom))
                        .setSupportRecommend(true)
                        .setSortType(sortType)
                        .setShowSearchBox(true)
                        .setUpGlide(true)
        );
//        LocationSelectionActivity.startActivitySwitchCity(this);
    }


    // 903地图找工作切换热门城市
    private void switchCityForMapJob() {
        GeekPageRouter.openChooseHotCityForResult(this);
    }

    private void handleTitle() {
        GFindFilterPresenter findFilterPresenter = new GFindFilterPresenter();
        findFilterPresenter.setSubway(mSelectedSubwayData);
        findFilterPresenter.setBusinessDistrict(mSelectedDistrictData);
        findFilterPresenter.setDistanceLocation(mSelectedDistanceLocationData);

        String areaName = (String) findFilterPresenter.getNameAndCount()[0];
        int count = (int) findFilterPresenter.getNameAndCount()[1];

        if (UserManager.isBossRole()) {
            if (TextUtils.isEmpty(areaName) && mCurJobForBoss != null) {
                areaName = mCurJobForBoss.locationName;
            }
        } else {
            if (TextUtils.isEmpty(areaName) && mCurExpectJobForGeek != null) {
                areaName = mCurExpectJobForGeek.locationName;
            }
            // 如果mLocationName不为空，则说明是有传递进来
            if (!TextUtils.isEmpty(mLocationName)) {
                areaName = mLocationName;
            }
        }

        String stringArea = TextUtils.isEmpty(areaName) ? "默认" : areaName;
        if (count <= 0) {
            mTitleView.setTitle(stringArea);
        } else {
            mTitleView.setTitle(Html.fromHtml(getString(R.string.string_title_with_color, stringArea, count)));
        }
    }

    /**
     * 获取商圈信息-附近
     *
     * @return
     */
    private DistanceLocationBean setupDistanceLocationData() {
        return null;
    }

    /**
     * 获取商圈信息
     *
     * @return
     */
    private LevelBean setupBusinessDistrictData() {
        if (mDistrictData != null) {
            return mDistrictData;
        } else {
            long requestCityCode = 0;
            if (mSelectedDistrictData != null) {
                requestCityCode = mSelectedDistrictData.code;
            }
            if (requestCityCode <= 0) {
                if (UserManager.isBossRole()) {
                    requestCityCode = mCurJobForBoss == null ? 0 : mCurJobForBoss.locationIndex;
                } else {
                    if (mLocationId != 0) {
                        // 优先考虑传递进来的locationId
                        requestCityCode = mLocationId;
                    } else {
                        requestCityCode = mCurExpectJobForGeek == null ? 0 : mCurExpectJobForGeek.locationIndex;
                    }
                }
            }
            LevelBean businessDistrict = VersionAndDatasCommon.getInstance().getBusinessDistrictOnSpecifiedCity(requestCityCode);
            if (businessDistrict == null) {
                // 防止在基础数据中发生找不到的情况出现
                businessDistrict = new LevelBean();
                businessDistrict.code = requestCityCode;
                if (!TextUtils.isEmpty(mLocationName)) {
                    businessDistrict.name = mLocationName;
                } else {
                    businessDistrict.name = (mCurExpectJobForGeek == null ? "" : mCurExpectJobForGeek.locationName);
                }
            }
            return businessDistrict;
        }
    }

    private void requestDistrictData() {

        if (mCurExpectJobForGeek == null) {
            return;
        }

        String encryptExpectId = mCurExpectJobForGeek.encryptExpectId;
        String position = mCurExpectJobForGeek.positionClassIndexString;
        long cityCode = mCurExpectJobForGeek.locationIndex;
        int sortType =mCurExpectJobForGeek.sortType;
        if (mSelectedDistrictData != null) {
            cityCode = mSelectedDistrictData.code;
        }

        // 请求
        SimpleApiRequest.GET(GeekUrlConfig.URL_ZPGEEK_APP_BUSINESSDISTRICT)
                .addParam("city", cityCode)
                .addParam("position", isGeekSearch() || isFromCompany() ? "" : position)//从搜索过来的不传 position和 encryptExpectId
                .addParam("encryptExpectId", isGeekSearch() || isFromCompany() ? "" : encryptExpectId)
                .addParam("scene", (isGeekF1() || isStudentF1()) ? "1" : isGeekSearch()?"3":"0")/*@since 1002   调用场景，0:默认， 1:F1，默认值为0*/
                .addParam("sortType", (isGeekF1() || isStudentF1()) ? String.valueOf(sortType) : "")
                .setRequestCallback(new SimpleApiRequestCallback<GetGeekBusinessDistrictResponse>() {
                    @Override
                    public void handleInChildThread(ApiData<GetGeekBusinessDistrictResponse> data) {
                        super.handleInChildThread(data);
                        if (data != null && data.resp != null && data.resp.businessDistrict != null) {
                            districtOrigin = data.resp.businessDistrict;

                            if (TextUtils.isEmpty(districtOrigin.name) || LList.isEmpty(districtOrigin.subLevelModeList)) {
                                ApmAnalyzer.create().action(ExceptionConstants.ACTION_BASEDATA_EXCEPTION, ExceptionConstants.BASEDATA_DISTRICT_NETERROR)
                                        .param("p3", String.valueOf(districtOrigin.code))
                                        .param("p2", districtOrigin.name)
                                        .param("p4", String.valueOf(LList.getCount(districtOrigin.subLevelModeList)))
                                        .report();
                            }

                        } else {
                            districtOrigin = setupBusinessDistrictData();
                        }
                    }

                    @Override
                    public void onSuccess(ApiData<GetGeekBusinessDistrictResponse> data) {
                        super.onSuccess(data);
                        flushViewData();
                    }

                    @Override
                    public void handleErrorInChildThread(ErrorReason reason) {
                        super.handleErrorInChildThread(reason);
                        districtOrigin = setupBusinessDistrictData();
                    }

                    @Override
                    public void onFailed(ErrorReason reason) {
                        super.onFailed(reason);
                        flushViewData();
                    }
                })
                .execute();
    }


    /**
     * 获取地铁信息
     *
     * @return
     */
    private LevelBean setupSubwayData() {
        if (UserManager.isBossRole() || !isShowSubway) {
            return null;
        }

        long requestCityCode = 0;
        if (mSelectedDistrictData != null) {
            // 地铁的数据由商圈中的城市信息来决定，因此这里要先通过商圈信息来获取城市码
            requestCityCode = mSelectedDistrictData.code;
        }
        if (requestCityCode <= 0) {
            if (mLocationId != 0) {
                requestCityCode = mLocationId;
            } else {
                requestCityCode = mCurExpectJobForGeek == null ? 0 : mCurExpectJobForGeek.locationIndex;
            }
        }
        return VersionAndDatasCommon.getInstance().getSubwayOnSpecifiedCity(requestCityCode);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == CitySelectActivity.REQ_CITY_SELECT) {
                if (data != null) {
                    if (data.hasExtra(CitySelectActivity.RESULT_PARAM_TO_IS_FINISH)){
                        boolean isFinish=   data.getBooleanExtra(CitySelectActivity.RESULT_PARAM_TO_IS_FINISH,false);
                        if (isFinish){
                            Intent intent = getIntent();
                            intent.putExtra(CitySelectActivity.RESULT_PARAM_TO_IS_FINISH, true);
                            setResult(RESULT_OK, intent);
                            AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.UP_GLIDE);
                            return;
                        }
                    }

                  if (data.hasExtra(Constants.DATA_ENTITY)){
                      LevelBean city = (LevelBean) data.getSerializableExtra(Constants.DATA_ENTITY);
                      if (city == null) {
                          return;
                      }
                      LevelBean firstLevelBean = new LevelBean(city.code, city.name);
                      if (city.isCountyLevelCity() || city.isCountyOrDistrict()) {
                          firstLevelBean = new LevelBean(city.parentCode, city.parentName);
                          LevelBean secondLevelBean = new LevelBean(city.code, city.name);
                          secondLevelBean.cityType = 3;
                          firstLevelBean.subLevelModeList.add(secondLevelBean);
                      } else {
                          firstLevelBean = new LevelBean(city.code, city.name);
                          if (!LList.isEmpty(city.subLevelModeList)) { // 包含县级市 搜索返回
                              LevelBean countryBean = LList.getElement(city.subLevelModeList, 0);
                              if (countryBean != null && (countryBean.isCountyLevelCity() || countryBean.isCountyOrDistrict())) {
//                            bean = new LevelBean(countryBean.code, countryBean.name);
                                  firstLevelBean.subLevelModeList.add(countryBean);
                              }
                          }
                      }

                      Intent intent = new Intent();
                      intent.putExtra(INTENT_DISTRICT_DATA, firstLevelBean);
                      intent.putExtra(INTENT_FROMCITYSELECT, true);
                      setResult(Activity.RESULT_OK, intent);
                      AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.UP_GLIDE);
                  }
                }
            }

            if (requestCode == GeekConsts.RequestCode.RC_CHOOSE_HOT_CITY) {
                if (data != null && data.hasExtra(GeekConsts.IntentConsts.KEY_HOT_CITY)) {
                    LevelBean city = (LevelBean) data.getSerializableExtra(GeekConsts.IntentConsts.KEY_HOT_CITY);
                    Intent intent = new Intent();
                    intent.putExtra(GeekConsts.IntentConsts.KEY_HOT_CITY, city);
                    intent.putExtra(INTENT_GEEK_FROM, mFrom);
                    intent.putExtra(INTENT_FROMCITYSELECT, true);
                    setResult(Activity.RESULT_OK, intent);
                    AppUtil.finishActivity(FilterAreaSelectActivity.this, ActivityAnimType.UP_GLIDE);
                }
            }
        }
    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            // 在筛选框内切换当前定位城市
            if (TextUtils.equals(action, Constants.RECEIVER_CHANGE_LOCATION)) {
                LocationService.LocationBean location = (LocationService.LocationBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
                if (location != null) {
                    mDistrictSelectionPanelView.setSelectLocation(location);
                }
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ReceiverUtils.unregister(this, mReceiver);
    }


    /*支持多选的场景*/
    public boolean supportMultiSelectMode() {


        UserBean userBean = UserManager.getLoginUser();

        /*身份判断 - 目前不支持学生*/
        boolean isStudent = UserManager.isGeekStudent();


        boolean supportScene = isGeekF1() || isGeekSearch() || isFromCompany();

        return !isStudent  && supportScene;
    }


    public int getCityPageSoureParam(int sourceFrom) {
        if(sourceFrom==INTENT_GEEK_FROM_F1_GEEK){
            return  CitySelectActivity.FROM_GEEK_F1_FILTER;
        }else if (sourceFrom ==INTENT_GEEK_FROM_F1_GEEK_MIXED){
            return  CitySelectActivity.FROM_GEEK_F1_MIXED_FILTER;
        } else if ( sourceFrom == INTENT_GEEK_FROM_F1_STUDENT_MIX) {
            return CitySelectActivity.FROM_F1_MIXED_EXPECT;
        } else if (sourceFrom == INTENT_GEEK_FROM_F1_STUDENT) {
            return CitySelectActivity.FROM_F1_SINGE_EXPECT;
        } else if (sourceFrom == INTENT_GEEK_FROM_SEARCH) {
            return CitySelectActivity.FROM_SEARCH_PAGE;
        } else {
            return 0;
        }
    }
}
