package com.hpbr.bosszhipin.module.filter;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ScrollView;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.hpbr.bosszhipin.module.commend.view.AdvancedExperienceFilterView;
import com.hpbr.bosszhipin.module.commend.view.ContactIntentionExpFilterView;
import com.hpbr.bosszhipin.module.main.entity.OtherFilterParam;
import com.hpbr.bosszhipin.module.main.views.filter.BaseFilterRuleNewView;
import com.hpbr.bosszhipin.module.main.views.filter.GeekBaseFilterRuleView;
import com.hpbr.bosszhipin.module.main.views.filter.GeekFilterRuleView;
import com.hpbr.bosszhipin.module.main.views.filter.GeekSearchFilterRuleView;
import com.hpbr.bosszhipin.module.main.views.filter.geekf1.GeekF1ServerFilterView;
import com.hpbr.bosszhipin.module.main.views.filter.geekf4.GeekF4CompanyFilterView;
import com.hpbr.bosszhipin.module.main.views.filter.geekf4.StageFilterView;
import com.hpbr.bosszhipin.module_geek_export.GeekPageRouter;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.module_geeksearch_export.GeekSearchConstant;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;

import net.bosszhipin.api.bean.BaseNlpFilterItemBean;
import net.bosszhipin.api.bean.CodeNameFlagBean;
import net.bosszhipin.api.bean.GeekServerFilterOptionBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;


/**
 * 筛选栏-筛选tab 落地页
 * 此页面有多个入口，Geek首页，搜索页 ，参考{@link PageType}
 *
 * <AUTHOR>
 * @version 7.05
 */
public class FilterFiltrateSelectActivity extends BaseActivity {

    public static final int REQUEST_CODE_FILTER = 1000;
    public static final int REQUEST_CODE_FILTER_HUNTER_SEARCH = 1002;
    public static final int REQUEST_CODE_FILTER_HUNTER_RECOMMEND = 1001;
    public static final String INTENT_CONDITION_LIST = "intent_condition_list";
    public static final String INTENT_PAGE_TYPE = "intent_page_type";
    public static final String INTENT_EXPECT_TYPE = "intent_expect_type";
    public static final String INTENT_EXPECT_JOB_TYPE = "INTENT_EXPECT_JOB_TYPE";
    public static final String INTENT_IS_NO_COMPLETE_INFO_SCENE = "INTENT_IS_NO_COMPLETE_INFO_SCENE";
    private static final String VIP_AVALIABLE = "vip_avaliable";
    private static final String INTENT_CHATTED_JOB = "intent_chatted_job";

    public static final String INTENT_ANCHOR_TYPE = "intent_anchor_type";
    public static final String SEARCH_INPUT_QUERY = "search_input_query";
    public static final String INTTENT_EXPECT_ID = "intent_expect_id";
    public static final String INTTENT_ENCRYPT_EXPECT_ID = "intent_encrypt_expect_id";
    public static final int ANCHOR_TYPE_EDU = 1;
    public static final int ANCHOR_TYPE_WORK_YEAR = 2;
    public static final int ANCHOR_TYPE_MORE = 3;

    private BaseFilterRuleNewView mBossView;
    private GeekBaseFilterRuleView mGeekView;
    private AppTitleView mTitleView;
    private long expectId = 0;

    private FrameLayout mLyContainer;
    private ArrayList<FilterBean> mSelectedConditions;
    private int mSelectedCount;
    private int mPageType;
    /**
     * 全职还是兼职   用于在 812 筛选中 默认的选中全职或者兼职
     */
    private int mPositionJiobType;
    /*是否是 「登录未完善场景」*/
    private boolean isNoCompleteInfoScene;
    private String encryptExpectId;
    private int chattedJob;
    private String searchInputQuery;

    @IntDef({PageType.PAGE_TYPE_GEEK_FIND,
            PageType.PAGE_TYPE_GEEK_SEARCH_POSITION,
            PageType.PAGE_TYPE_GEEK_SEARCH_BRAND,
            PageType.PAGE_TYPE_GEEK_COMPANY,
            PageType.PAGE_TYPE_BOSS_FIND,
            PageType.PAGE_TYPE_BOSS_ADVANCE_SEARCH,
            PageType.PAGE_TYPE_GEEK_FIND_JOB_BY_MAP,
            PageType.PAGE_TYPE_GEEK_STUDENT_FIND,
            PageType.PAGE_TYPE_COMPANY_RECOMMEND,
            PageType.PAGE_TYPE_GEEK_SEARCH_SUBSCRIBE,
            PageType.PAGE_TYPE_STU_SEARCH_SUBSCRIBE,
            PageType.PAGE_TYPE_BOSS_CONTACT_INTENTION})
    public @interface PageType {
        int PAGE_TYPE_GEEK_FIND = 1;   // 牛人首页-职位列表
        int PAGE_TYPE_GEEK_SEARCH_POSITION = 2; // 牛人搜索页-按职位
        int PAGE_TYPE_GEEK_SEARCH_BRAND = 3; // 牛人搜索页-按公司
        int PAGE_TYPE_GEEK_COMPANY = 4; // 牛人首页-公司列表 714 之前用
        int PAGE_TYPE_BOSS_FIND = 5; // boss首页-牛人列表
        int PAGE_TYPE_BOSS_ADVANCE_SEARCH = 6; //boss-高搜
        int PAGE_TYPE_GEEK_FIND_JOB_BY_MAP = 7; // 牛人地图找工作
        int PAGE_TYPE_GEEK_STUDENT_FIND = 8;
        int PAGE_TYPE_COMPANY_RECOMMEND = 10;// 公司-推荐
        int PAGE_TYPE_GEEK_SEARCH_SUBSCRIBE = 11; // 搜索订阅
        int PAGE_TYPE_STU_SEARCH_SUBSCRIBE = 18; // 学生搜索订阅
        int PAGE_TYPE_BOSS_CONTACT_INTENTION = 19; // 1203.501 意向订单
    }

    /**
     * 启动该页面
     *
     * @param activity           页面activity
     * @param requestCode        请求码
     * @param selectedFilterBean 已选中项
     * @param pageType           从哪个页面进来的
     */
    public static void start(Activity activity, int requestCode, ArrayList<FilterBean> selectedFilterBean, @PageType int pageType, long expectId, String encryptId, int positionType) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterFiltrateSelectActivity.class);
        intent.putExtra(INTENT_PAGE_TYPE, pageType);
        intent.putExtra(INTTENT_EXPECT_ID, expectId);
        intent.putExtra(INTTENT_ENCRYPT_EXPECT_ID, encryptId);
        intent.putExtra(INTENT_EXPECT_JOB_TYPE, positionType);
        if (!LList.isEmpty(selectedFilterBean)) {
            Entity entity = new Entity();
            entity.selectedFilterBean = selectedFilterBean;
            intent.putExtra(INTENT_CONDITION_LIST, entity);
        }
        AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 启动该页面
     * 带锚点信息
     *
     * @param activity           页面activity
     * @param requestCode        请求码
     * @param selectedFilterBean 已选中项
     * @param pageType           从哪个页面进来的
     */
    public static void start(Activity activity, int requestCode, ArrayList<FilterBean> selectedFilterBean, @PageType int pageType, int anchorType) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterFiltrateSelectActivity.class);
        intent.putExtra(INTENT_PAGE_TYPE, pageType);
        intent.putExtra(INTENT_ANCHOR_TYPE, anchorType);
        if (!LList.isEmpty(selectedFilterBean)) {
            Entity entity = new Entity();
            entity.selectedFilterBean = selectedFilterBean;
            intent.putExtra(INTENT_CONDITION_LIST, entity);
        }
        AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 启动该页面
     *
     * @param activity           页面activity
     * @param requestCode        请求码
     * @param selectedFilterBean 已选中项
     * @param pageType           从哪个页面进来的
     * @param intent             外部Intent
     */
    public static void start(Activity activity, int requestCode, ArrayList<FilterBean> selectedFilterBean, @PageType int pageType, Intent intent) {
        intent.setClass(activity, FilterFiltrateSelectActivity.class);
        intent.putExtra(INTENT_PAGE_TYPE, pageType);
        if (!LList.isEmpty(selectedFilterBean)) {
            Entity entity = new Entity();
            entity.selectedFilterBean = selectedFilterBean;
            intent.putExtra(INTENT_CONDITION_LIST, entity);
        }
        AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 启动该页面
     *
     * @param activity              activity
     * @param requestCode           请求码
     * @param selectedFilterBean    已选中项
     * @param pageType              从哪个页面进来的
     * @param isNoCompleteInfoScene 是否是 「登录未完善场景」
     */
    public static void start(Activity activity, int requestCode, ArrayList<FilterBean> selectedFilterBean, int chattedJob, @PageType int pageType, boolean isNoCompleteInfoScene,String query) {
        if (activity == null) return;
        Intent intent = new Intent();
        intent.setClass(activity, FilterFiltrateSelectActivity.class);
        intent.putExtra(INTENT_PAGE_TYPE, pageType);
        intent.putExtra(INTENT_CHATTED_JOB, chattedJob);
        if (!LList.isEmpty(selectedFilterBean)) {
            Entity entity = new Entity();
            entity.selectedFilterBean = selectedFilterBean;
            intent.putExtra(INTENT_CONDITION_LIST, entity);
        }
        intent.putExtra(INTENT_IS_NO_COMPLETE_INFO_SCENE, isNoCompleteInfoScene);
        intent.putExtra(SEARCH_INPUT_QUERY, query);
        activity.startActivityForResult(intent, requestCode);
        activity.overridePendingTransition(R.anim.activity_new_enter_up_glide, R.anim.activity_old_exit_up_glide);
    }

    /**
     * 学生党筛选
     *
     * @param activity           页面activity
     * @param requestCode        请求码
     * @param selectedFilterBean 已选中项
     * @param expectType         期望类型
     * @param pageType           从哪个页面进来的
     */
    public static void start(Activity activity, int requestCode, ArrayList<FilterBean> selectedFilterBean, CodeNameFlagBean expectType, @PageType int pageType) {
        Intent intent = new Intent();
        intent.setClass(activity, FilterFiltrateSelectActivity.class);
        intent.putExtra(INTENT_PAGE_TYPE, pageType);
        intent.putExtra(INTENT_EXPECT_TYPE, expectType);
        if (!LList.isEmpty(selectedFilterBean)) {
            Entity entity = new Entity();
            entity.selectedFilterBean = selectedFilterBean;
            intent.putExtra(INTENT_CONDITION_LIST, entity);
        }
        AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_filter_page_filtrate);
        initData();
        initView();
    }

    private void initData() {
        mPositionJiobType = getIntent().getIntExtra(INTENT_EXPECT_JOB_TYPE, 0);
        mPageType = getIntent().getIntExtra(INTENT_PAGE_TYPE, 0);
        mAnchorType = getIntent().getIntExtra(INTENT_ANCHOR_TYPE, 0);
        expectId = getIntent().getLongExtra(INTTENT_EXPECT_ID, 0L);
        encryptExpectId = getIntent().getStringExtra(INTTENT_ENCRYPT_EXPECT_ID);
        searchInputQuery = getIntent().getStringExtra(SEARCH_INPUT_QUERY);
        chattedJob = getIntent().getIntExtra(INTENT_CHATTED_JOB, GeekSearchConstant.DEFAULT_CHATTED_JOB);
        Entity entity = (Entity) getIntent().getSerializableExtra(INTENT_CONDITION_LIST);
        if (entity != null) {
            mSelectedConditions = entity.selectedFilterBean;
        }
        isNoCompleteInfoScene = getIntent().getBooleanExtra(INTENT_IS_NO_COMPLETE_INFO_SCENE, false);
    }

    private void initView() {
        mTitleView = findViewById(R.id.title_view);
        mTitleView.setDividerInvisible();
        mTitleView.setBackClickListener(R.mipmap.ic_action_close_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtil.finishActivity(FilterFiltrateSelectActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
        mTitleView.setTitle(getFilterTitleString(0));

        mLyContainer = findViewById(R.id.ly_container);
        // 根据不同的页面加载不同的View即可，数据包含在View中
        if (mPageType == PageType.PAGE_TYPE_GEEK_FIND) {
//            mView = new GeekF1DemandFilterView(this);
            mGeekView = new GeekF1ServerFilterView(this);
            mGeekView.initFilterData(0, 0, expectId, encryptExpectId, mPositionJiobType,"");
        } else if (mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_POSITION // F1 - 搜索 - 职位 - 筛选
                || mPageType == PageType.PAGE_TYPE_GEEK_FIND_JOB_BY_MAP // 牛人地图找工作 - 筛选
                || mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_SUBSCRIBE) { // 牛人搜索订阅
            mGeekView = new GeekSearchFilterRuleView(this);
            mGeekView.setChooseFilterOrder(mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_SUBSCRIBE)
                    .setShowOtherSetting(mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_POSITION, chattedJob)
                    .initFilterData(isNoCompleteInfoScene ? 3 :  mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_SUBSCRIBE?18:1, 0, 0, "", mPositionJiobType,searchInputQuery);
        } else if (mPageType == PageType.PAGE_TYPE_GEEK_SEARCH_BRAND) { //// F1 - 搜索 - 公司 - 筛选
            mGeekView = new GeekSearchFilterRuleView(this);
            mGeekView.initFilterData(2, 0, 0, "", mPositionJiobType,"");
        } else if (mPageType == PageType.PAGE_TYPE_GEEK_COMPANY) {
            mBossView = new StageFilterView(this);
        } else if (mPageType == PageType.PAGE_TYPE_BOSS_ADVANCE_SEARCH) {
            mBossView = new AdvancedExperienceFilterView(this);
        } else if (mPageType == PageType.PAGE_TYPE_BOSS_CONTACT_INTENTION) {
            mBossView = new ContactIntentionExpFilterView(this);
        } else if (mPageType == PageType.PAGE_TYPE_GEEK_STUDENT_FIND) {
            CodeNameFlagBean expectType = (CodeNameFlagBean) getIntent().getSerializableExtra(INTENT_EXPECT_TYPE);
            mGeekView = new GeekF1ServerFilterView(this);
            if (expectType != null) {
                mGeekView.initFilterData(0, expectType.code, expectId, encryptExpectId, mPositionJiobType,"");
            }
//            mBossView = new GeekStudentF1DemandFilterView(this);
//            ((GeekStudentF1DemandFilterView) mBossView).setData(mSelectedConditions, expectType);
        } else if (mPageType == PageType.PAGE_TYPE_COMPANY_RECOMMEND) {
            mBossView = new GeekF4CompanyFilterView(this, getIntent());
        } else if (mPageType == PageType.PAGE_TYPE_STU_SEARCH_SUBSCRIBE) { // 1201.607 学生 搜索订阅
            mGeekView = new GeekSearchFilterRuleView(this);
            mGeekView.setChooseFilterOrder(mPageType == PageType.PAGE_TYPE_STU_SEARCH_SUBSCRIBE)
                    .setShowOtherSetting(mPageType == PageType.PAGE_TYPE_STU_SEARCH_SUBSCRIBE, chattedJob)
                    .initFilterData(mPageType, 0, 0, "", mPositionJiobType,"");
        }

        if (mBossView != null) {
            mBossView.setSelectedItems(mSelectedConditions);
            mLyContainer.addView(mBossView);
            mBossView.setConfirmListener(new BaseFilterRuleNewView.OnConfirmListener() {
                @Override
                public void onConfirm(ArrayList<FilterBean> selectedFilterBean) {
                    Entity entity = new Entity();
                    entity.selectedFilterBean = selectedFilterBean;
                    entity.selectedCount = mSelectedCount;
                    // 1307.92371【高搜】区分来源埋点：将搜索类型返回到高搜界面
                    entity.anchorType = mAnchorType;

                    Intent intent = new Intent();
                    intent.putExtra(INTENT_CONDITION_LIST, entity);
                    setResult(Activity.RESULT_OK, intent);
                    AppUtil.finishActivity(FilterFiltrateSelectActivity.this, ActivityAnimType.UP_GLIDE);
                }

                @Override
                public void onSelectedConditionCount(int count) {
                    mSelectedCount = count;
                    mTitleView.setTitle(getFilterTitleString(count));
                }
            });
            // 触发一次
            mBossView.setConfirmText();
        } else if (mGeekView != null) {
//            mGeekView.setSelectedItems(mSelectedConditions);
            mGeekView.diliverSelectedItems(mSelectedConditions);
            mLyContainer.addView(mGeekView);
            mGeekView.setConfirmListener(new GeekFilterRuleView.OnConfirmListener() {
                @Override
                public void onConfirm(ArrayList<FilterBean> selectedFilterBean, @Nullable OtherFilterParam otherFilterParam) {
                    Entity entity = new Entity();
                    entity.selectedFilterBean = selectedFilterBean;
                    entity.selectedCount = mSelectedCount;
                    // 1307.92371【高搜】区分来源埋点：将搜索类型返回到高搜界面
                    entity.anchorType = mAnchorType;
                    if (otherFilterParam != null) {
                        entity.isOpenRecentTalkJobSwitch = otherFilterParam.isOpenRecentTalkJobSwitch;
                        entity.originSearchFilters = otherFilterParam.originSearchFilters;
                    }

                    Intent intent = new Intent();
                    intent.putExtra(INTENT_CONDITION_LIST, entity);
                    setResult(Activity.RESULT_OK, intent);
                    AppUtil.finishActivity(FilterFiltrateSelectActivity.this, ActivityAnimType.UP_GLIDE);
                }

                @Override
                public void onSelectedConditionCount(int count) {
                    mSelectedCount = count;
                    mTitleView.setTitle(getFilterTitleString(count));
                }
            });
            // 触发一次
            mGeekView.setConfirmText();
        }

        srcollToTargetView();
    }

    private int mAnchorType = 0;//锚点。学历1、工作经历2、更多3

    /**
     * 滚动至锚点位置
     * 学历1、工作经历2、更多3
     */
    private void srcollToTargetView() {
        if (mPageType != PageType.PAGE_TYPE_BOSS_ADVANCE_SEARCH || mAnchorType <= 0) {
            return;
        }
        View cl_options = findViewById(R.id.cl_options);
        if (cl_options == null || !(cl_options instanceof ScrollView)) {
            return;
        }

        ScrollView scrollView = (ScrollView) cl_options;
        scrollView.post(new Runnable() {
            @Override
            public void run() {
                View targetView = null;
                View tv_degree = findViewById(R.id.tv_degree);
                View tv_work_year = findViewById(R.id.tv_work_year);
                View tv_work_salary = findViewById(R.id.tv_work_salary);
                if (mAnchorType == ANCHOR_TYPE_EDU) {
                    targetView = tv_degree;
                } else if (mAnchorType == ANCHOR_TYPE_WORK_YEAR) {
                    targetView = tv_work_year;
                } else if (mAnchorType == ANCHOR_TYPE_MORE) {
                    targetView = tv_work_salary;
                }
                if (targetView == null) return;

                //计算滚动距离
                int[] startLocation = new int[2];
                int[] endLocation = new int[2];
                scrollView.getLocationInWindow(startLocation); //获取在当前窗口内的绝对坐标
                targetView.getLocationOnScreen(endLocation);//获取在整个屏幕内的绝对坐标
                int top = endLocation[1] - startLocation[1];
                scrollView.scrollTo(0, top);
            }
        });

    }

    private CharSequence getFilterTitleString(int count) {
        CharSequence cs;
        if (count <= 0) {
            cs = "筛选";
        } else {
            cs = Html.fromHtml(getString(R.string.string_title_with_color, "筛选", count));
        }
        return cs;
    }

    public static class Entity implements Serializable {
        private static final long serialVersionUID = -1081127131052837124L;
        public ArrayList<FilterBean> selectedFilterBean;
        public long expectId;
        public int selectedCount;
        /*「显示近30天内沟通过的职位」开关是否打开*/
        public boolean isOpenRecentTalkJobSwitch;
        public String encFilterId;

        // 1307.92371【高搜】区分来源埋点：记录从高搜界面打开的筛选类型
        public int anchorType;
        public    List<BaseNlpFilterItemBean> originSearchFilters;
    }

    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode==RESULT_OK&&data!=null){
            if (requestCode== GeekPageRouter.REQUEST_CODE_INDUSTRY){
                try {
                    List<GeekServerFilterOptionBean> industrySelectList = (List<GeekServerFilterOptionBean>) data.getSerializableExtra(GeekPageRouter.GeekFilterParam.INTENT_CONDITION_LIST_FROM_SEARCH);
                    if (mGeekView != null) {
                        mGeekView.setIndustrySelectList(industrySelectList);
                    }
                }catch (Exception e){
                    TLog.info("onActivityResult",e.getMessage());
                }

            }
        }
    }
}
