package com.hpbr.bosszhipin.module.filter;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.main.entity.DistanceLocationBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.main.fragment.geek.GFindFilterPresenter;
import com.hpbr.bosszhipin.module.main.views.filter.bossf1.SubwaySelectionView;
import com.hpbr.bosszhipin.module.my.entity.JobIntentBean;
import com.hpbr.bosszhipin.module.my.entity.LevelBean;
import com.hpbr.bosszhipin.net.GeekUrlConfig;
import com.hpbr.bosszhipin.utils.ReceiverUtils;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.monch.lbase.util.LList;
import com.techwolf.lib.tlog.TLog;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.EmptyResponse;
import net.bosszhipin.base.SimpleApiRequest;
import net.bosszhipin.base.SimpleCommonApiRequestCallback;

/**
 * 筛选栏-F1附近-地址选择-选择地铁站
 *
 * <AUTHOR>
 * @version 10.01
 */
public class NearByFilterSubwayActivity extends BaseActivity {
    private static final String TAG="NearByFilterSubway";

    public static final int REQUEST_SELECT_AREA = 0x03;

    protected static final String INTENT_EXPECT_JOB = "intent_expect_job";
    protected static final String INTENT_JOB = "intent_job";
    public static final String INTENT_GEEK_FROM = "intent_geek_from";
    public static final String INTENT_DISTRICT_DATA = "intent_district_data";
    public static final String INTENT_SUBWAY_DATA = "intent_subway_data";
    public static final String INTENT_DISTANCE_DATA = "intent_distance_data";
    public static final String INTENT_LOCATION_ID = "intent_location_id";
    public static final String INTENT_LOCATION_NAME = "intent_location_name";


    private JobBean mCurJobForBoss;
    private JobIntentBean mCurExpectJobForGeek;
    /**
     * 定位城市ID
     */
    private long mLocationId;

    /**
     * 定位城市名字
     */
    private String mLocationName;
    private AppTitleView mTitleView;
    private SubwaySelectionView mSubwayPanelView;

    private LevelBean mSelectedSubwayData;
    /**
     * 来自哪里，如果是搜索页面过来的，需要隐藏"切换城市"按钮
     */
    private int mFrom;

    /**
     * 牛人进入该页面的统一入口
     *
     * @param activity     页面Activity
     * @param curExpectJob 当前期望职位
     * @param subwayData   地铁数据
     */
    public static void startNearbyFilter(Activity activity,
                                         JobIntentBean curExpectJob,
                                         LevelBean subwayData,
                                         long cityCode,
                                         String cityName,
                                         int from) {
        Intent intent = new Intent();
        intent.setClass(activity, NearByFilterSubwayActivity.class);
        intent.putExtra(INTENT_EXPECT_JOB, curExpectJob);
        intent.putExtra(INTENT_SUBWAY_DATA, subwayData);
        intent.putExtra(INTENT_LOCATION_ID, cityCode);
        intent.putExtra(INTENT_LOCATION_NAME, cityName);
        intent.putExtra(INTENT_GEEK_FROM, from);
        AppUtil.startActivityForResult(activity, intent, REQUEST_SELECT_AREA, ActivityAnimType.UP_GLIDE);
    }


    @Override
    protected boolean shouldUseLightStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_nearby_subway);
        initReceiver();
        initData();
        initView();
    }

    private void initReceiver() {
        ReceiverUtils.register(this, mReceiver, Constants.RECEIVER_CHANGE_LOCATION);
    }

    private void initView() {
        mTitleView = findViewById(R.id.title_view);
        mTitleView.setDividerInvisible();
        mTitleView.setBackClickListener(R.mipmap.ic_action_close_black, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppUtil.finishActivity(NearByFilterSubwayActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
        mSubwayPanelView = findViewById(R.id.districtSelectionPanelView);
        mSubwayPanelView.setSupportMultiSelect(true);
        flushViewData();
    }

    private void flushViewData() {


        // 这里注意一下，关于附近的筛选只支持boss身份
        mSubwayPanelView.setAreaData(null, setupSubwayData(), null, mCurJobForBoss);

        mSubwayPanelView.setSelectedAreaItem(null, mSelectedSubwayData, null);
        mSubwayPanelView.setListener(new SubwaySelectionView.OnLocationSelectedListener() {

            @Override
            public void onLocationSelected(LevelBean locationBean, LevelBean subwayBean, DistanceLocationBean distanceLocationBean) {
                mSelectedSubwayData = subwayBean;
                saveDataRequest();
            }

            @Override
            public void onLocationSelected(LevelBean locationBean, LevelBean subwayBean, DistanceLocationBean distanceLocationBean, int num) {
                mSelectedSubwayData = subwayBean;
                handleTitle();
            }
        });
        handleTitle();
    }

    private void saveDataRequest() {
        if (mSelectedSubwayData != null) {
            SimpleApiRequest.POST(GeekUrlConfig.URL_GEEK_SAVE_SUBWAY)
                    .addParam("cityCode", String.valueOf(mSelectedSubwayData.code))/*订阅id（当该参数的值传空的时候，为取消全部搜索订阅）*/
                    .addParam("subwayStationIds", createSubwayParams())
                    .setRequestCallback( new SimpleCommonApiRequestCallback<EmptyResponse>()  {
                        @Override
                        public void onSuccess(ApiData<EmptyResponse> data) {

                        }

                        @Override
                        public void onComplete() {
                            Intent intent = new Intent();
                            intent.putExtra(INTENT_SUBWAY_DATA, mSelectedSubwayData);
                            intent.putExtra(INTENT_GEEK_FROM, mFrom);
                            setResult(Activity.RESULT_OK, intent);
                            AppUtil.finishActivity(NearByFilterSubwayActivity.this, ActivityAnimType.UP_GLIDE);
                        }

                        @Override
                        public void onFailed(ErrorReason reason) {
                            TLog.info(TAG,reason.getErrReason());
                        }
                    })
                    .execute();

            AnalyticsFactory.create().action("userinfo-job-sub-save").param("p", createSubwayParams()).build();
        }

    }

    private String createSubwayParams() {
        String subwayStationIds = "";
        if (mSelectedSubwayData != null && !LList.isEmpty(mSelectedSubwayData.subLevelModeList)) {
            for (LevelBean levelBean : mSelectedSubwayData.subLevelModeList) {
                if (!LList.isEmpty(levelBean.subLevelModeList)) {
                    for (LevelBean stationId : levelBean.subLevelModeList) {
                        subwayStationIds = StringUtil.connectTextWithChar(",", subwayStationIds, String.valueOf(stationId.code));
                    }

                }
            }
        }
        return subwayStationIds;
    }


    private void initData() {
        Intent intent = getIntent();
        if (UserManager.isBossRole()) {
            mCurJobForBoss = (JobBean) intent.getSerializableExtra(INTENT_JOB);
        } else {
            mCurExpectJobForGeek = (JobIntentBean) intent.getSerializableExtra(INTENT_EXPECT_JOB);
        }
        mSelectedSubwayData = (LevelBean) intent.getSerializableExtra(INTENT_SUBWAY_DATA);
        mFrom = intent.getIntExtra(INTENT_GEEK_FROM, 0);
        mLocationId = intent.getLongExtra(INTENT_LOCATION_ID, 0);
        mLocationName = intent.getStringExtra(INTENT_LOCATION_NAME);
    }


    private void handleTitle() {
        GFindFilterPresenter findFilterPresenter = new GFindFilterPresenter();
        findFilterPresenter.setSubway(mSelectedSubwayData);

        String areaName = (String) findFilterPresenter.getNameAndCount()[0];
        int count = (int) findFilterPresenter.getNameAndCount()[1];

        if (UserManager.isBossRole()) {
            if (TextUtils.isEmpty(areaName) && mCurJobForBoss != null) {
                areaName = mCurJobForBoss.locationName;
            }
        } else {
            if (TextUtils.isEmpty(areaName) && mCurExpectJobForGeek != null) {
                areaName = mCurExpectJobForGeek.locationName;
            }
            // 如果mLocationName不为空，则说明是有传递进来
            if (!TextUtils.isEmpty(mLocationName)) {
                areaName = mLocationName;
            }
        }

        String stringArea = TextUtils.isEmpty(areaName) ? "默认" : areaName;
        if (count <= 0) {
            mTitleView.setTitle(stringArea);
        } else {
            mTitleView.setTitle(Html.fromHtml(getString(R.string.string_title_with_color, stringArea, count)));
        }
    }

    /**
     * 获取商圈信息-附近
     *
     * @return
     */
    private LevelBean setupSubwayData() {
        if (UserManager.isBossRole()) {
            return null;
        }

        long requestCityCode = 0;
//        if (mSelectedDistrictData != null) {
//            // 地铁的数据由商圈中的城市信息来决定，因此这里要先通过商圈信息来获取城市码
//            requestCityCode = mSelectedDistrictData.code;
//        }
        if (requestCityCode <= 0) {
            if (mLocationId != 0) {
                requestCityCode = mLocationId;
            } else {
                requestCityCode = mCurExpectJobForGeek == null ? 0 : mCurExpectJobForGeek.locationIndex;
            }
        }
        return VersionAndDatasCommon.getInstance().getSubwayOnSpecifiedCity(requestCityCode);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ReceiverUtils.unregister(this, mReceiver);
    }


}
