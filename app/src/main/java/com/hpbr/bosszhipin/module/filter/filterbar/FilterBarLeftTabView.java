package com.hpbr.bosszhipin.module.filter.filterbar;

import android.content.Context;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.MTextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

/**
 * 筛选栏左侧tab，目前只有一个TextView，但是为了以后扩展，单独做一个View
 *
 * <AUTHOR>
 * @version 7.05
 */
public class FilterBarLeftTabView extends FrameLayout {
    private MTextView tvTabLabel;
    //    private ZPUIBadgeView badgeView;
    private ImageView ivRedDot;
    private SimpleDraweeView sdvIcon;

    public FilterBarLeftTabView(Context context) {
        this(context, null);
    }

    public FilterBarLeftTabView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterBarLeftTabView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        inflate(context, R.layout.layout_filterbar_left_tab, this);
        tvTabLabel = findViewById(R.id.tv_tab_label);
        ivRedDot = findViewById(R.id.iv_red_dot);
        sdvIcon = findViewById(R.id.sdvIcon);
        sdvIcon.setVisibility(GONE);
//        badgeView = ZPUIBadgeUtils.createBadgeIcon(context, tvTabLabel, false);
//        badgeView.setGravityOffset(4, 3, true);
//        badgeView.stroke(Color.WHITE, 1, true);
    }

    public void setText(String text, boolean isSelected) {
        tvTabLabel.setText(text);
        tvTabLabel.setSelected(isSelected);
        tvTabLabel.setTypeface(isSelected ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
    }

    public void setSdvIcon(String iconUrl){
        if (!TextUtils.isEmpty(iconUrl)){
            sdvIcon.setVisibility(VISIBLE);
            sdvIcon.setImageURI(iconUrl);
        }else {
            sdvIcon.setVisibility(GONE);
        }
    }

    public void updateSelected(boolean isSelected) {
        if (null == tvTabLabel) return;
        tvTabLabel.setSelected(isSelected);
        tvTabLabel.setTypeface(isSelected ? Typeface.DEFAULT_BOLD : Typeface.DEFAULT);
    }

//    public void updateBadgeNumber(int badgeNumber) {
//        badgeView.setBadgeNumber(badgeNumber);
//    }

    public void setRedDotVisible(int visibility) {
        ivRedDot.setVisibility(visibility);
    }

    public void setTextColor(Context context, int color) {
        if (context != null && tvTabLabel!=null) {
            tvTabLabel.setTextColor(ContextCompat.getColor(context, color));
        }
    }

    public void setTextColor( int color) {
        if ( tvTabLabel!=null ) {
            tvTabLabel.setTextColor(color);
        }
    }

}