package com.hpbr.bosszhipin.module.filter.filterbar;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.StringDef;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.utils.UiUtils;
import com.monch.lbase.util.Scale;

/**
 * 筛选栏右侧tab
 *
 * <AUTHOR>
 * @version 7.05
 */
public class FilterBarRightTabView extends FrameLayout {
    private final Context mContext;
    private TextView tvTabLabel;
    /**
     * 红点标签
     */
    private View mCornerMarkView;

    /**
     * VIP标签
     */
    private View mVipView;


    /**
     * VIP标签
     */
    private View mIconVipView;

    /**
     * VIP专属
     */
    private View mVipBelong;

    /**
     * VIP限免标签
     */
    private View mFreeVipView;

    private ImageView ivRedDot;

    /**
     * "..."
     */
    static final char[] ELLIPSIS_NORMAL = {'\u2026'};
    private static final String ELLIPSIS_STRING = new String(ELLIPSIS_NORMAL);
    /**
     * 前面的文字最多显示4个，中间使用...
     */
    private int maxLength = 4;
    /**
     * 标签是否不使用背景色
     */
    private boolean clearBg = false;
    private boolean limitTextLength = true;

    public FilterBarRightTabView(Context context) {
        this(context, null);
    }

    public FilterBarRightTabView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterBarRightTabView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init(context);
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    private void init(Context context) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.layout_filterbar_right_tab, this);
        tvTabLabel = view.findViewById(R.id.tv_tab_label);

        mCornerMarkView = view.findViewById(R.id.img_corner_mark);
        mVipView = view.findViewById(R.id.img_vip);
        mFreeVipView = view.findViewById(R.id.img_free_vip);
        mIconVipView = view.findViewById(R.id.img_vip_tag);
        mVipBelong = view.findViewById(R.id.mVipBelong);
        ivRedDot = view.findViewById(R.id.iv_red_dot);
    }

    /**
     * 设置VIP限免标签
     *
     * @param visibility
     */
    public void setFreeVIPVisible(int visibility) {
        mFreeVipView.setVisibility(visibility);
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvTabLabel.getLayoutParams();
        if (visibility == View.VISIBLE) {
            layoutParams.rightMargin = Scale.dip2px(mContext, 4);
        }
    }

    /**
     * 设置VIP标签是否展示
     *
     * @param visibility
     */
    public void setVIPMarkVisible(int visibility) {
        mVipView.setVisibility(visibility);
        ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) tvTabLabel.getLayoutParams();
        if (visibility == View.VISIBLE) {
            layoutParams.rightMargin = Scale.dip2px(mContext, 4);
        }
    }

    /**
     * 设置VIP专属是否显示
     *
     * @param Visiblity
     */
    public void setVIPBelongVisible(int Visiblity) {
        mVipBelong.setVisibility(Visiblity);
    }

    /**
     * 设置VIP图标标签
     *
     * @param visibility
     */
    public void setVIPIconMarkVisible(int visibility) {
        mIconVipView.setVisibility(visibility);
    }


    public TextView getTvLabel() {
        return tvTabLabel;
    }

    public void setText(String text, int num, boolean isSelected) {
        if (TextUtils.isEmpty(text)) return;
        // 处理一下字数限制，
        StringBuilder builder = new StringBuilder();
        if (text.length() > maxLength && limitTextLength) {
            builder.append(text, 0, 2);
            builder.append(ELLIPSIS_STRING);
            builder.append(text, text.length() - 2, text.length());
        } else {
            builder.append(text);
        }

        if (num > 0) {
            builder.append("·" + num);
        }
        text = builder.toString();

        tvTabLabel.setText(text);
        if (isSelected) {
            tvTabLabel.setTextColor(ContextCompat.getColor(mContext, R.color.app_green_dark));
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_selected_bg);
            mCornerMarkView.setBackgroundResource(R.mipmap.ic_filter_bar_right_mark_selected);
        } else {
            tvTabLabel.setTextColor(ContextCompat.getColor(mContext, R.color.color_GY8));
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_unselect_bg);
            mCornerMarkView.setBackgroundResource(R.mipmap.ic_filter_bar_right_mark_unselect);
        }
        if (clearBg) {
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_white);
        }

        mCornerMarkView.setSelected(isSelected);
    }


    /**
     * 新增：支持中英文混排的省略（左/中/右）
     *
     * @param text          原始文本
     * @param num           数字
     * @param isSelected    是否选中
     * @param ellipsizeType 省略类型："middle"/"start"/"end"
     */
    public void setTextEllipsize(String text, int num, boolean isSelected, @EllipsizeType String ellipsizeType) {
        if (TextUtils.isEmpty(text))
            return;
        StringBuilder builder = new StringBuilder();
        int maxLen = maxLength * 2; // maxLength=4时，最多4个中文或8个英文
        String ellipsis = ELLIPSIS_STRING;
        if (StringUtil.getLength(text) > maxLen && limitTextLength) {
            switch (ellipsizeType) {
                case EllipsizeType.START: {
                    // 左边省略
                    String end = StringUtil.subStringReverse(text, maxLen);
                    builder.append(ellipsis).append(end);
                    break;
                }
                case EllipsizeType.MIDDLE: {
                    // 中间省略
                    int half = maxLen / 2;
                    String start = StringUtil.subString(text, half);
                    String end = StringUtil.subStringReverse(text,
                            maxLen - half);
                    builder.append(start).append(ellipsis).append(end);
                    break;
                }
                case EllipsizeType.END:
                default: {
                    // 右边省略
                    String start = StringUtil.subString(text, maxLen);
                    builder.append(start).append(ellipsis);
                    break;
                }
            }
        } else {
            builder.append(text);
        }
        if (num > 0) {
            builder.append("·").append(num);
        }
        text = builder.toString();
        tvTabLabel.setText(text);
        if (isSelected) {
            tvTabLabel.setTextColor(ContextCompat.getColor(mContext, R.color.app_green_dark));
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_selected_bg);
            mCornerMarkView.setBackgroundResource(R.mipmap.ic_filter_bar_right_mark_selected);
        } else {
            tvTabLabel.setTextColor(ContextCompat.getColor(mContext, R.color.color_GY8));
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_unselect_bg);
            mCornerMarkView.setBackgroundResource(R.mipmap.ic_filter_bar_right_mark_unselect);
        }
        if (clearBg) {
            tvTabLabel.setBackgroundResource(R.drawable.shape_filter_bar_right_white);
        }
        mCornerMarkView.setSelected(isSelected);
    }

    public void setLimitMaxLength(boolean limit) {
        limitTextLength = limit;
    }

    public void setClearBg(boolean clearBg) {
        this.clearBg = clearBg;
    }

    /**
     * 设置Icon
     */
    public void setLeftIcon(boolean isSHowLeftIcon, int leftIconResId, int leftIconWidth, int leftIconHight, int leftIconPadding) {
        setLeftDrawable(isSHowLeftIcon ? getResources().getDrawable(leftIconResId) : null, leftIconWidth, leftIconHight, leftIconPadding);
    }

    public void setLeftDrawable(Drawable drawable, int leftIconWidth, int leftIconHight, int leftIconPadding) {
        if (null != drawable) {
            int dpw = (int) UiUtils.convertDpToPixel(leftIconWidth, getContext());
            int dph = (int) UiUtils.convertDpToPixel(leftIconHight, getContext());
            int dppad = (int) UiUtils.convertDpToPixel(leftIconPadding, getContext());
            tvTabLabel.setCompoundDrawablePadding(dppad);
            drawable.setBounds(0, 0, dpw, dph);
            tvTabLabel.setCompoundDrawables(drawable, null, null, null);
        } else {
            tvTabLabel.setCompoundDrawables(null, null, null, null);
        }
    }

    /**
     * 设置VIP图标标签
     *
     * @param visibility
     */
    public void setRedDotVisible(int visibility) {
        ivRedDot.setVisibility(visibility);
    }

    @StringDef({
            EllipsizeType.START,
            EllipsizeType.MIDDLE,
            EllipsizeType.END,
    })
    public @interface EllipsizeType {
        String START = "start";
        String MIDDLE = "middle";
        String END = "end";
    }
}
