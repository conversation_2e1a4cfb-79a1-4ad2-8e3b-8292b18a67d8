package com.hpbr.bosszhipin.module.filter.filterbar;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.Scale;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 在顶部标题栏下方的筛选栏
 *
 * <p>
 * 左边是按钮
 * 右边是筛选项
 *
 * <AUTHOR>
 * @version 7.05
 */
public class FilterBarView extends RelativeLayout {
    private Context mContext;
    private LinearLayout mLyLeft;
    private LinearLayout mLyRight;
    private View mShadowView;

    private LeftTab mCurrentSelectedLeftTab;

    private OnLeftTabSelectListener mOnLeftTabSelectListener;
    private OnRightTabSelectListener mOnRightTabSelectListener;
    private List<LeftTab> mLeftTabList = new ArrayList<>();
    private List<RightTab> mRightTabList = new ArrayList<>();
    //背景色改为白色，清除背景色
    private boolean clearBg = false;
    //是否限制文字长度
    private boolean limitTextLength = true;

    public void setLimitTextLength(boolean limitTextLength) {
        this.limitTextLength = limitTextLength;
    }

    private View view_filter_bar_bottom_line;


    public FilterBarView(Context context) {
        this(context, null);
    }

    public FilterBarView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public FilterBarView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }

    private void init() {
        inflate(mContext, R.layout.layout_filter_bar, this);
        mLyLeft = findViewById(R.id.ly_left);
        mLyRight = findViewById(R.id.ly_right);
        mShadowView = findViewById(R.id.view_shadow);
        view_filter_bar_bottom_line = findViewById(R.id.view_filter_bar_bottom_line);
    }

    /**
     * 设置底部分隔线是否显示
     *
     * @param isVisible
     */
    public void setBottomLineVisible(boolean isVisible) {
        view_filter_bar_bottom_line.setVisibility(isVisible ? View.VISIBLE : View.GONE);
    }

    public void setOnLeftTabSelectListener(OnLeftTabSelectListener onLeftTabSelectListener) {
        this.mOnLeftTabSelectListener = onLeftTabSelectListener;
    }

    public void setOnRightTabSelectListener(OnRightTabSelectListener onRightTabSelectListener) {
        this.mOnRightTabSelectListener = onRightTabSelectListener;
    }

    public void setRightTabGravity(int rightTabGravity) {
        mLyRight.setGravity(rightTabGravity);
        if (rightTabGravity == Gravity.START) {
            mShadowView.setVisibility(View.GONE);
            RelativeLayout.LayoutParams params = (LayoutParams) mLyRight.getLayoutParams();
            params.leftMargin = 0;
            mLyRight.setLayoutParams(params);
        }
    }

    /**
     * 移除某一个左tab
     *
     * @param tag 要移除的tag
     */
    public void removeLeftTab(String tag) {
        Iterator<LeftTab> iterator = mLeftTabList.iterator();
        while (iterator.hasNext()) {
            LeftTab leftTab = iterator.next();
            if (TextUtils.equals(tag, leftTab.tag)) {
                iterator.remove();
            }
        }
        updateLeftTabView();
    }

    /**
     * 移除某一个右tab
     *
     * @param tag 要移除的tag
     */
    public void removeRightTab(String tag) {
        Iterator<RightTab> iterator = mRightTabList.iterator();
        while (iterator.hasNext()) {
            RightTab rightTab = iterator.next();
            if (TextUtils.equals(tag, rightTab.tag)) {
                iterator.remove();
            }
        }
        updateRightTabView();
    }

    /**
     * 根据tag查询Tab
     *
     * @param tag
     * @return 如果包含此tag的tab，则返回。如果不包含，则返回null
     */
    public RightTab getRightTabByTag(String tag) {
        for (RightTab rightTab : mRightTabList) {
            if (TextUtils.equals(tag, rightTab.tag)) {
                return rightTab;
            }
        }
        return null;
    }

    public FilterBarLeftTabView getLeftTabViewByTag(String tag) {
        FilterBarLeftTabView tabView = null;
        try {
            for (int i = 0; i < mLeftTabList.size(); i++) {
                if (TextUtils.equals(tag, mLeftTabList.get(i).tag)) {
                    tabView = (FilterBarLeftTabView) mLyLeft.getChildAt(i);
                    break;
                }
            }
        } catch (Exception ignored) {

        }
        return tabView;
    }

    public FilterBarRightTabView getRightTabViewByTag(String tag) {
        FilterBarRightTabView tabView = null;
        try {
            for (int i = 0; i < mRightTabList.size(); i++) {
                if (TextUtils.equals(tag, mRightTabList.get(i).tag)) {
                    tabView = (FilterBarRightTabView) mLyRight.getChildAt(i);
                    break;
                }
            }
        } catch (Exception ignored) {

        }
        return tabView;
    }

    /**
     * 根据tag查询Tab
     *
     * @param tag
     * @return 如果包含此tag的tab，则返回。如果不包含，则返回null
     */
    public LeftTab getLeftTabByTag(String tag) {
        for (LeftTab leftTab : mLeftTabList) {
            if (TextUtils.equals(tag, leftTab.tag)) {
                return leftTab;
            }
        }
        return null;
    }

    public void notifyLeftDataSetChanged() {
        updateLeftTabView();
    }

    public void notifyRightDataSetChanged() {
        updateRightTabView();
    }

    public void clearLeftTab() {
        if (mLyLeft != null) {
            mLeftTabList.clear();
            mLyLeft.removeAllViews();
        }
    }

    public void clearRightTab() {
        if (mLyRight != null) {
            mRightTabList.clear();
            mLyRight.removeAllViews();
        }
    }

    /**
     * 添加一个左侧Tab
     *
     * @param leftTab
     */
    public void addLeftTab(LeftTab leftTab) {
        if (leftTab == null) return;
        addLeftTab(leftTab, mLeftTabList.size());
    }

    /**
     * 添加到相应位置一个左侧Tab（目前暂无使用，private可以改为public）
     *
     * @param leftTab  要添加的Tab数据
     * @param position 索引位置
     */
    public void addLeftTab(LeftTab leftTab, int position) {
        addLeftTabInternal(leftTab, position);
    }

    /**
     * 获取左边tab的总数
     *
     * @return
     */
    public int getLeftTabTotalSize() {
        return mLeftTabList.size();
    }


    public void addRightTab(RightTab rightTab) {
        addRightTabInternal(rightTab, mRightTabList.size());
    }

    /**
     * 添加右边的栏目
     *
     * @param rightTab
     * @param position 顺序，从0开始 1/2/3...
     */
    public void addRightTab(RightTab rightTab, int position) {
        addRightTabInternal(rightTab, position);
    }

    /**
     * 添加左侧tab列表
     *
     * @param leftTabList
     */
    public void addLeftTabList(List<LeftTab> leftTabList) {
        if (LList.isEmpty(leftTabList)) {
            mLyLeft.setVisibility(View.GONE);
            return;
        }
        mLeftTabList = leftTabList;
        updateLeftTabView();
    }

    private void addLeftTabInternal(LeftTab tab, final int position) {
        if (tab != null && !TextUtils.isEmpty(tab.text)) {
            mLeftTabList.add(position, tab);
            updateLeftTabView();
        }
    }

    private void addRightTabInternal(RightTab tab, final int position) {
        if (tab != null && !TextUtils.isEmpty(tab.text)) {
            mRightTabList.add(position, tab);
            updateRightTabView();
        }
    }

    private void updateLeftTabView() {
        mLyLeft.removeAllViews();
        for (int i = 0; i < mLeftTabList.size(); i++) {
            final int position = i;
            LeftTab tab = mLeftTabList.get(i);
            FilterBarLeftTabView filterBarLeftTab = new FilterBarLeftTabView(mContext);
            filterBarLeftTab.setText(tab.text, tab.isSelected);
            filterBarLeftTab.setRedDotVisible(tab.isShowRedDot ? View.VISIBLE : View.GONE);
            filterBarLeftTab.setSdvIcon(tab.iconUrl);
            filterBarLeftTab.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    mCurrentSelectedLeftTab = tab;
                    if (mOnLeftTabSelectListener != null) {
                        if (tab.delayOptionSelect) {
                            mOnLeftTabSelectListener.onItemSelected(filterBarLeftTab, tab.tag, true);
                        } else {
                            mOnLeftTabSelectListener.onItemSelected(filterBarLeftTab, tab.tag, false);
                            updateTextStyle(position);
                        }
                    }
                }
            });
            mLyLeft.addView(filterBarLeftTab);
        }
    }

    public LeftTab getCurrentSelectedTab() {
        return mCurrentSelectedLeftTab;
    }

    private void updateTextStyle(int position) {
        for (int i = 0; i < mLeftTabList.size(); i++) {
            LeftTab leftTab = mLeftTabList.get(i);
            if (position == i) {
                leftTab.isSelected = true;
            } else {
                leftTab.isSelected = false;
            }
        }
        updateLeftTabView();
    }

    private void updateRightTabView() {
        mLyRight.removeAllViews();
        for (int i = 0; i < mRightTabList.size(); i++) {
            RightTab tab = mRightTabList.get(i);
            FilterBarRightTabView filterBarRightTab = new FilterBarRightTabView(mContext);
            filterBarRightTab.setMaxLength(5);
            filterBarRightTab.setClearBg(clearBg);
            filterBarRightTab.setLimitMaxLength(limitTextLength);
            if (i > 0) {
                // 添加间距
                filterBarRightTab.setPadding(Scale.dip2px(mContext, 8), 0, 0, 0);
            }
            filterBarRightTab.setVIPMarkVisible(tab.isShowVIP ? View.VISIBLE : View.GONE);
            filterBarRightTab.setFreeVIPVisible(tab.isShowFreeVIP ? View.VISIBLE : View.GONE);
            filterBarRightTab.setVIPIconMarkVisible(tab.isShowVIPIcon ? View.VISIBLE : View.GONE);
            filterBarRightTab.setRedDotVisible(tab.isShowRedDot ? View.VISIBLE : View.GONE);
            filterBarRightTab.setText(tab.text, tab.num, tab.isSelected);
            filterBarRightTab.setLeftIcon(tab.isSHowLeftIcon, tab.leftIconResId, tab.leftIconWidth, tab.leftIconHight, tab.leftIconPadding);
            filterBarRightTab.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    if (mOnRightTabSelectListener != null) {
                        mOnRightTabSelectListener.onItemSelected(filterBarRightTab, tab.tag);
                    }
                }
            });
            mLyRight.addView(filterBarRightTab);
        }
    }

    /**
     * 选中左侧Tab
     *
     * @param index 索引值
     */
    public void setLeftSelectedItem(int index) {
        if (LList.getElement(mLeftTabList, index) == null) return;
        for (int i = 0; i < mLeftTabList.size(); i++) {
            LeftTab tab = mLeftTabList.get(i);
            tab.isSelected = i == index;
        }
        updateLeftTabView();
        mLyLeft.getChildAt(index).performClick();
    }

    public void setLeftSelectedItemUI(int index) {
        if (LList.getElement(mLeftTabList, index) == null) return;
        for (int i = 0; i < mLeftTabList.size(); i++) {
            LeftTab tab = mLeftTabList.get(i);
            tab.isSelected = i == index;
        }
        updateLeftTabView();
//        mLyLeft.getChildAt(index).performClick();
    }

    public void setLeftSelectedItemVoidClick(int index) {
        if (LList.getElement(mLeftTabList, index) == null) return;
        for (int i = 0; i < mLeftTabList.size(); i++) {
            LeftTab tab = mLeftTabList.get(i);
            tab.isSelected = i == index;
        }
        updateLeftTabView();
    }

    /**
     * 选中左侧tab，根据tag
     *
     * @param tag
     */
    public void setLeftSelectItemByTag(String tag) {
        int index = -1;
        int size = mLeftTabList.size();
        for (int i = 0; i < size; i++) {
            LeftTab tab = mLeftTabList.get(i);
            if (tab == null) continue;
            if (TextUtils.equals(tag, tab.tag)) {
                index = i;
                break;
            }
        }
        if (index >= 0) {
            setLeftSelectedItem(index);
        }
    }


    public View getLeftSelectItemByTag(String tag) {
        int index = -1;
        int size = mLeftTabList.size();
        for (int i = 0; i < size; i++) {
            LeftTab tab = mLeftTabList.get(i);
            if (tab == null) continue;
            if (TextUtils.equals(tag, tab.tag)) {
                index = i;
                break;
            }
        }
        if (index >= 0 &&  mLyLeft!=null && index<mLyLeft.getChildCount())  {
           return mLyLeft.getChildAt(index);
        }
        return null;
    }

    /**
     * 选中左侧tab，根据tag
     *
     * @param tag
     */
    public void setLeftSelectItemByTagVoidClick(String tag) {
        int index = -1;
        int size = mLeftTabList.size();
        for (int i = 0; i < size; i++) {
            LeftTab tab = mLeftTabList.get(i);
            if (tab == null) continue;
            if (TextUtils.equals(tag, tab.tag)) {
                index = i;
                break;
            }
        }
        if (index >= 0) {
            setLeftSelectedItemVoidClick(index);
        }
    }

    /**
     * 更新左侧Tab
     *
     * @param index 索引值
     */
    public void updateLeftSelectedItem(int index) {
        if (LList.getElement(mLeftTabList, index) == null) return;
        for (int i = 0; i < mLeftTabList.size(); i++) {
            LeftTab tab = mLeftTabList.get(i);
            tab.isSelected = i == index;
        }
        updateLeftTabView();
    }

    /**
     * 更新选中左侧tab，根据tag
     */
    public void updateSelectItemByTag(String tag) {
        int index = -1;
        int size = mLeftTabList.size();
        for (int i = 0; i < size; i++) {
            LeftTab tab = mLeftTabList.get(i);
            if (tab == null) continue;
            if (TextUtils.equals(tag, tab.tag)) {
                index = i;
                break;
            }
        }
        if (index >= 0) {
            updateLeftSelectedItem(index);
        }
    }

    public static class LeftTab {
        public String text;
        public String tag;
        public boolean isSelected;
        public boolean isShowRedDot;
        public boolean delayOptionSelect;
        public int badgeNumber;
        public String iconUrl;

        public LeftTab(String text, String tag) {
            this.text = text;
            this.tag = tag;
        }

        public LeftTab(String text, String tag, String iconUrl) {
            this.text = text;
            this.tag = tag;
            this.iconUrl = iconUrl;
        }


    }

    public static class RightTab {
        public String text;
        public String tag;
        public int num;
        public boolean isShowRedDot;
        public boolean isSelected;
        public int position;
        public boolean isShowNew;
        public boolean isShowVIP;
        public boolean isShowVIPIcon;
        public boolean isShowFreeVIP;
        public boolean isSHowLeftIcon;
        public int leftIconResId;

        public int leftIconWidth = 14;//dp
        public int leftIconHight = 14;//dp
        public int leftIconPadding = 2;//dp

        public RightTab(String text, String tag) {
            this(text, 0);
            this.tag = tag;
        }

        public RightTab(String text, String tag, boolean isShowRedDot) {
            this(text, 0);
            this.tag = tag;
            this.isShowRedDot = isShowRedDot;
        }

        public RightTab(String text, int num) {
            this(text, num, false);
        }

        public RightTab(String text, int num, boolean isShowRedDot) {
            this(text, num, isShowRedDot, false);
        }

        public RightTab(String text, int num, boolean isShowRedDot, boolean isSelected) {
            this(text, num, isShowRedDot, isSelected, false);
        }

        public RightTab(String text, int num, boolean isShowRedDot, boolean isSelected, boolean isShowNew) {
            this.text = text;
            this.num = num;
            this.isShowRedDot = isShowRedDot;
            this.isSelected = isSelected;
            this.isShowNew = isShowNew;
        }
    }

    public interface OnLeftTabSelectListener {
        void onItemSelected(FilterBarLeftTabView selectTab, String tag, boolean delayOptionSelect); // 是否延迟选中
    }

    public interface OnRightTabSelectListener {
        void onItemSelected(FilterBarRightTabView selectTab, String tag);
    }
}
