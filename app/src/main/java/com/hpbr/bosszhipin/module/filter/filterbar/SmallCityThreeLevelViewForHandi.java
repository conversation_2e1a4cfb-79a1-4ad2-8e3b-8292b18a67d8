package com.hpbr.bosszhipin.module.filter.filterbar;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.AnimationSet;
import android.view.animation.TranslateAnimation;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.recycleview.BaseRvAdapter;
import com.hpbr.bosszhipin.recycleview.BaseViewHolder;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LList;
import com.twl.utils.DisplayHelper;

import net.bosszhipin.api.bean.ServerThreeFilterBean;

import java.util.List;
import java.util.Locale;

/**
 * fanfan
 */
public class SmallCityThreeLevelViewForHandi extends RelativeLayout {

    private Context mContext;
    private RecyclerView rcvFristLevel;
    private RecyclerView rcvSencondLevel;
    private MTextView mtvGuide;
    private RecyclerView rcvThirdLevel;
    private RelativeLayout rlContainer;

    private List<ServerThreeFilterBean> filterBeanList;

    private FirstLevelAdapter firstLevelAdapter;
    private SecondLevelAdapter secondLevelAdapter;
    private ThirdAdapter thirdLevelAdapter;

    private ServerThreeFilterBean selectFirstFilter;

    private ThreeFilterCallback threeFilterCallback;

    public SmallCityThreeLevelViewForHandi(Context context) {
        this(context, null);
    }

    public SmallCityThreeLevelViewForHandi(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SmallCityThreeLevelViewForHandi(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }

    private void init() {
        inflate(mContext, R.layout.layout_three_filter_city_handi, this);
        rcvFristLevel = findViewById(R.id.rcv_first);
        rcvSencondLevel = findViewById(R.id.rcv_second);
        rcvThirdLevel = findViewById(R.id.rcv_third);
        mtvGuide = findViewById(R.id.mtv_second_guide);
        rlContainer = findViewById(R.id.rl_contianer);

    }

    public void setData(List<ServerThreeFilterBean> filterList, ThreeFilterCallback callback) {

        clearData();

        this.filterBeanList = filterList;
        this.threeFilterCallback = callback;
        ServerThreeFilterBean filterBean = LList.getElement(filterList, 0);
        if (filterBean != null) {
            filterBean.selected = true;
        }
        firstLevelAdapter = new FirstLevelAdapter(filterList);
        rcvFristLevel.setAdapter(firstLevelAdapter);
        setMinimumHeight(DisplayHelper.dp2px(mContext,0));

    }

    public void clearData() {
        selectFirstFilter = null;
        rcvSencondLevel.setVisibility(GONE);
        rcvThirdLevel.setVisibility(GONE);
        mtvGuide.setVisibility(GONE);
    }



    public class FirstLevelAdapter extends BaseRvAdapter<ServerThreeFilterBean, BaseViewHolder> {


        public FirstLevelAdapter(List<ServerThreeFilterBean> firstList) {
            super(R.layout.item_small_city_filter);
            this.mData = firstList;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerThreeFilterBean item) {

            RelativeLayout rlOutLine = helper.getView(R.id.rl_icon);
            MTextView mtvName = helper.getView(R.id.mtv_name);
            MTextView mtvCount = helper.getView(R.id.mtv_count);
            SimpleDraweeView sdvIcon = helper.getView(R.id.sdv_icon);


            mtvName.setText(item.name);
            if (item.count <= 0) {
                mtvCount.setVisibility(GONE);
            } else if (item.count >= 500) {
                mtvCount.setVisibility(VISIBLE);
                mtvCount.setText("500+");
            } else {
                mtvCount.setVisibility(VISIBLE);
                mtvCount.setText(String.valueOf(item.count));
            }

            rlOutLine.setBackgroundResource(item.selected ? R.drawable.bg_btn_e3f5f5_radius_corner : R.drawable.bg_btn_f5f5f5_radius_corner);
            mtvName.setTextColor(ContextCompat.getColor(mContext, item.selected ? R.color.color_FF0D9EA3 : R.color.color_FF292929_FF9E9EA1));

            sdvIcon.setImageURI(item.selected ? item.selectedIcon : item.icon);

            helper.itemView.setOnClickListener(new OnClickNoFastListener(OnClickNoFastListener.DELAY_SORT_DURATION) {
                @Override
                public void onNoFastClick(View v) {


                    clearCheckSelect(mData);
                    item.selected = true;
                    selectFirstFilter = item;

                    if (!TextUtils.isEmpty(item.code) && !LList.isEmpty(item.subLevelModelList)) {
                        rlContainer.setVisibility(VISIBLE);
                        rcvSencondLevel.setVisibility(VISIBLE);
                        rcvSencondLevel.scrollToPosition(0);
                        rcvThirdLevel.setVisibility(GONE);
                        mtvGuide.setVisibility(GONE);
                        setMinimumHeight(DisplayHelper.dp2px(mContext,40));

                        if (!LList.isEmpty(item.subLevelModelList)) {
                            ServerThreeFilterBean filterBean = LList.getElement(item.subLevelModelList, 0);
                            if (filterBean != null) {
                                filterBean.selected = true;
                            }
                            if (secondLevelAdapter == null) {
                                secondLevelAdapter = new SecondLevelAdapter(item.subLevelModelList);
                                rcvSencondLevel.setAdapter(secondLevelAdapter);
                            } else {
                                secondLevelAdapter.setNewData(item.subLevelModelList);
                            }
                        }
                    } else {
                        setMinimumHeight(DisplayHelper.dp2px(mContext,0));
                        rcvSencondLevel.setVisibility(GONE);
                        rcvThirdLevel.setVisibility(GONE);
                        mtvGuide.setVisibility(GONE);
                        rlContainer.setVisibility(GONE);
                    }

                    notifyDataSetChanged();

                    if (threeFilterCallback != null) {
                        threeFilterCallback.onfilterChange(selectFirstFilter);
                    }


                }
            });
        }

    }


    class SecondLevelAdapter extends BaseRvAdapter<ServerThreeFilterBean, BaseViewHolder> {


        public SecondLevelAdapter(List<ServerThreeFilterBean> sencodList) {
            super(R.layout.item_smallcity_option);
            this.mData = sencodList;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerThreeFilterBean item) {

            MTextView mtvName = helper.getView(R.id.mtv_second_guide);


            if (item.count <= 0) {
                mtvName.setText(item.name);
            } else if (item.count >= 500) {
                mtvName.setText(String.format(Locale.getDefault(), "%s · %s", item.name, "500+"));
            } else {
                mtvName.setText(String.format(Locale.getDefault(), "%s · %d", item.name, item.count));
            }

            mtvName.setBackgroundResource(item.selected ? R.drawable.bg_btn_e3f5f5_radius_corner : R.drawable.bg_btn_f5f5f5_radius_corner);
            mtvName.setTextColor(ContextCompat.getColor(mContext, item.selected ? R.color.color_FF0D9EA3: R.color.color_FF292929_FF9E9EA1));


            helper.itemView.setOnClickListener(new OnClickNoFastListener(OnClickNoFastListener.DELAY_SORT_DURATION) {
                @Override
                public void onNoFastClick(View v) {


                    clearCheckSelect(mData);
                    item.selected = true;
                    notifyDataSetChanged();
                    if (!TextUtils.isEmpty(item.code) && !LList.isEmpty(item.subLevelModelList)) {

                        mtvGuide.setVisibility(VISIBLE);
                        mtvGuide.setText(item.name);

                        hideSecondRcv();
                        showThirdRcv();
                        rcvThirdLevel.scrollToPosition(0);

                        if (!LList.isEmpty(item.subLevelModelList)) {
                            if (thirdLevelAdapter == null) {
                                thirdLevelAdapter = new ThirdAdapter(item.subLevelModelList);
                                rcvThirdLevel.setAdapter(thirdLevelAdapter);
                            } else {
                                thirdLevelAdapter.setNewData(item.subLevelModelList);
                            }
                        }
                        mtvGuide.setOnClickListener(new OnClickNoFastListener() {
                            @Override
                            public void onNoFastClick(View v) {

                                clearCheckSelect(item.subLevelModelList);

                                mtvGuide.setVisibility(GONE);

                                showSecondRv();
                                hideThirdRcv();

                                if(threeFilterCallback != null) {
                                    threeFilterCallback.onfilterChange(selectFirstFilter);
                                }

                            }
                        });
                    } else {
                        rcvSencondLevel.setVisibility(VISIBLE);
                        rcvThirdLevel.setVisibility(GONE);
                        mtvGuide.setVisibility(GONE);

                    }

                    if (threeFilterCallback != null) {
                        threeFilterCallback.onfilterChange(selectFirstFilter);
                    }


                }
            });

        }
    }

    class ThirdAdapter extends BaseRvAdapter<ServerThreeFilterBean, BaseViewHolder> {


        public ThirdAdapter(List<ServerThreeFilterBean> sencodList) {
            super(R.layout.item_smallcity_option);
            this.mData = sencodList;
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, ServerThreeFilterBean item) {

            MTextView mtvName = helper.getView(R.id.mtv_second_guide);


            if (item.count <= 0) {
                mtvName.setText(item.name);
            } else if (item.count >= 500) {
                mtvName.setText(String.format(Locale.getDefault(), "%s · %s", item.name, "500+"));
            } else {
                mtvName.setText(String.format(Locale.getDefault(), "%s · %d", item.name, item.count));
            }

            mtvName.setBackgroundResource(item.selected ? R.drawable.bg_btn_e3f5f5_radius_corner : R.drawable.bg_btn_f5f5f5_radius_corner);
            mtvName.setTextColor(ContextCompat.getColor(mContext, item.selected ? R.color.color_FF0D9EA3 : R.color.color_FF292929_FF9E9EA1));


            helper.itemView.setOnClickListener(new OnClickNoFastListener(OnClickNoFastListener.DELAY_SORT_DURATION) {
                @Override
                public void onNoFastClick(View v) {
                    clearCheckSelect(mData);
                    item.selected = true;
                    notifyDataSetChanged();

                    if (threeFilterCallback != null) {
                        threeFilterCallback.onfilterChange(selectFirstFilter);
                    }

                }

            });


        }
    }

    public static void clearCheckSelect(List<ServerThreeFilterBean> mData) {
        if (!LList.isEmpty(mData)) {
            for (ServerThreeFilterBean bean : mData) {
                if (bean.selected) {
                    bean.selected = false;
                    if (!LList.isEmpty(bean.subLevelModelList)) {
                        clearCheckSelect(bean.subLevelModelList);
                    }
                }
            }
        }
    }

    public interface ThreeFilterCallback {
        void onfilterChange(ServerThreeFilterBean bean);
    }


    private void hideSecondRcv() {

        AnimationSet setAnimation = new AnimationSet(true);
        setAnimation.setDuration(300);

        TranslateAnimation tr = new TranslateAnimation(1f,-200,0,0);
        tr.setDuration(200);


        AlphaAnimation al = new AlphaAnimation(1,0);
        al.setDuration(100);



        setAnimation.addAnimation(tr);
        setAnimation.addAnimation(al);


        rcvSencondLevel.startAnimation(setAnimation);

        rcvSencondLevel.setVisibility(GONE);

    }

    private void showSecondRv() {

        rcvSencondLevel.setVisibility(VISIBLE);

        AnimationSet setAnimation = new AnimationSet(true);
        setAnimation.setDuration(300);

        TranslateAnimation tr = new TranslateAnimation(-200,1f,0,0);
        tr.setDuration(200);


        AlphaAnimation al = new AlphaAnimation(0,1);
        al.setDuration(100);



        setAnimation.addAnimation(tr);
        setAnimation.addAnimation(al);


        rcvSencondLevel.startAnimation(setAnimation);



    }

    private void showThirdRcv() {


        rcvThirdLevel.setVisibility(VISIBLE);

        AnimationSet setAnimation = new AnimationSet(true);
        setAnimation.setDuration(500);

        AlphaAnimation al = new AlphaAnimation(0,1);
        al.setDuration(300);

        TranslateAnimation tr = new TranslateAnimation(200,1f,0,0);
        tr.setDuration(100);

        setAnimation.addAnimation(al);
        setAnimation.addAnimation(tr);

        rcvThirdLevel.startAnimation(setAnimation);

    }

    private void hideThirdRcv() {

        AlphaAnimation al = new AlphaAnimation(1,0);
        al.setDuration(200);
        rcvThirdLevel.startAnimation(al);
        rcvThirdLevel.setVisibility(GONE);
    }
}