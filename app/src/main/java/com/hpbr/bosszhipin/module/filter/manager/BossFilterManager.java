package com.hpbr.bosszhipin.module.filter.manager;

import com.hpbr.bosszhipin.common.VersionAndDatasCommon;
import com.hpbr.bosszhipin.module.commend.entity.FilterBean;
import com.monch.lbase.util.LList;

import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/6/4
 */
public class BossFilterManager {

    private static BossFilterManager instance = new BossFilterManager();

    // 薪资
    private FilterBean filterSalary;
    // 工作经验
    private FilterBean filterExp;
    // 求职状态
    private FilterBean filterIntention;
    // 学历
    private FilterBean filterDegree;
    // 院校
    private FilterBean filterSchool;
    // 年龄
    private FilterBean filterAge;
    // 跳槽频率
    private FilterBean filterChangeJobFrequency;
    // 是否与同事交换简历
    private FilterBean filterExchangeResumeWithColleague;
    // 名企
    private FilterBean filterBigCompany;
    // 求职意向
    private FilterBean filterIntentionStrength;
    // 拨打电话
    private FilterBean filterCallPhone;
    // 性别
    private FilterBean filterCallGender;
    // 性别
    private FilterBean filterRecentNotView;
    // 跃度筛选
    private FilterBean filterActivation;

    public static BossFilterManager getInstance() {
        return instance;
    }

    /**
     * 获取指定code下的数据
     *
     * @param code
     * @return
     */
    private FilterBean getSpecifiedFilter(long code) {
        FilterBean filterBean = null;
        List<FilterBean> list = VersionAndDatasCommon.getInstance().getBossFilterList();
        if (!LList.isEmpty(list)) {
            for (FilterBean item : list) {
                if (item == null) continue;
                if (item.code == code) {
                    filterBean = item;
                    break;
                }
            }
        }
        return filterBean;
    }

    /**
     * 获取薪资
     *
     * @return
     */
    public FilterBean getFilterSalary() {
        if (filterSalary == null) {
            filterSalary = getSpecifiedFilter(IFilterKey.SALARY);
        }
        return filterSalary;
    }

    /**
     * 获取经验
     *
     * @return
     */
    public FilterBean getFilterExp() {
        if (filterExp == null) {
            filterExp = getSpecifiedFilter(IFilterKey.EXPERIENCE);
        }
        return filterExp;
    }

    /**
     * 获取求职状态
     *
     * @return
     */
    public FilterBean getFilterIntention() {
        if (filterIntention == null) {
            filterIntention = getSpecifiedFilter(IFilterKey.INTENTION);
        }
        return filterIntention;
    }

    /**
     * 获取学历
     *
     * @return
     */
    public FilterBean getFilterDegree() {
        if (filterDegree == null) {
            filterDegree = getSpecifiedFilter(IFilterKey.DEGREE);
        }
        return filterDegree;
    }

    /**
     * 获取学校
     *
     * @return
     */
    public FilterBean getFilterSchool() {
        if (filterSchool == null) {
            filterSchool = getSpecifiedFilter(IFilterKey.SCHOOL);
        }
        return filterSchool;
    }

    /**
     * 获取跳槽频率
     *
     * @return
     */
    public FilterBean getFilterChangeJobFrequency() {
        if (filterChangeJobFrequency == null) {
            filterChangeJobFrequency = getSpecifiedFilter(IFilterKey.SWITCH_JOB_FREQUENCY);
        }
        return filterChangeJobFrequency;
    }

    /**
     * 获取与同事交换简历
     *
     * @return
     */
    public FilterBean getFilterExchangeResumeWithColleague() {
        if (filterExchangeResumeWithColleague == null) {
            filterExchangeResumeWithColleague = getSpecifiedFilter(IFilterKey.EXCHANGE_RESUME_WITH_COLLEAGUE);
        }
        return filterExchangeResumeWithColleague;
    }

    /**
     * 获取名企
     *
     * @return
     */
    public FilterBean getFilterBigCompany() {
        if (filterBigCompany == null) {
            filterBigCompany = getSpecifiedFilter(IFilterKey.BIG_COMPANY);
        }
        return filterBigCompany;
    }

    /**
     * 获取求职意向
     *
     * @return
     */
    public FilterBean getFilterIntentionStrength() {
        if (filterIntentionStrength == null) {
            filterIntentionStrength = getSpecifiedFilter(IFilterKey.INTENTION_STRENGTH);
        }
        return filterIntentionStrength;
    }

    /**
     * 获取电话拨打
     *
     * @return
     */
    public FilterBean getFilterCallPhone() {
        if (filterCallPhone == null) {
            filterCallPhone = getSpecifiedFilter(IFilterKey.CALL_PHONE);
        }
        return filterCallPhone;
    }

    /**
     * 获取年龄
     *
     * @return
     */
    public FilterBean getFilterAge() {
        if (filterAge == null) {
            filterAge = new FilterBean();
            filterAge.paramName = "age";
            filterAge.name = "年龄";
            filterAge.subFilterConfigModel.clear();
            for (int i = 16; i < 36; i++) {
                filterAge.subFilterConfigModel.add(new FilterBean(i, i + "岁"));
            }
            filterAge.subFilterConfigModel.add(new FilterBean(-1, "36及以上"));
        }
        return filterAge;
    }

    /**
     * 获取性别
     */
    public FilterBean getFilterGender() {
        if (filterCallGender == null) {
            filterCallGender = getSpecifiedFilter(IFilterKey.CALL_GENDER);
        }
        return filterCallGender;
    }

    /**
     * 获取最近没有看过
     */
    public FilterBean getFilterRecentNotView() {
        if (filterRecentNotView == null) {
            filterRecentNotView = getSpecifiedFilter(IFilterKey.RECENT_NOT_VIEW);
        }
        return filterRecentNotView;
    }

    /**
     * 获取活跃度筛选
     */
    public FilterBean getFilterActivation() {
        if (filterActivation == null) {
            filterActivation = getSpecifiedFilter(IFilterKey.ACTIVATION);
        }
        return filterActivation;
    }

    public interface IFilterKey {
        long SALARY = 400;
        long DEGREE = 200;
        long EXPERIENCE = 100;
        long INTENTION = 700;
        long SCHOOL = 1100;
        long SWITCH_JOB_FREQUENCY = 1200;
        long EXCHANGE_RESUME_WITH_COLLEAGUE = 1300;
        long BIG_COMPANY = 1400;
        long INTENTION_STRENGTH = 1500;
        long CALL_GENDER = 2200;
        long RECENT_NOT_VIEW = 2300;
        long CALL_PHONE = 2400;
        long ACTIVATION = 2500;
    }
}
