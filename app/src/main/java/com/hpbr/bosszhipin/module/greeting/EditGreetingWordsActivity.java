package com.hpbr.bosszhipin.module.greeting;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.base.BaseActivity2;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.utils.InputUtils;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.Scale;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;
import com.twl.ui.popup.XGravity;
import com.twl.ui.popup.YGravity;
import com.twl.ui.popup.ZPUIPopup;

import net.bosszhipin.api.GreetingUpdate2Response;
import net.bosszhipin.api.GreetingUpdateRequest2;
import net.bosszhipin.api.bean.ServerGreetingWordBean;
import net.bosszhipin.api.bean.ServerJDChatTemplateBean;
import net.bosszhipin.base.ApiRequestCallback;

import java.io.Serializable;

/**
 * create by guofeng
 * date on 2023/6/6
 */
public class EditGreetingWordsActivity extends BaseActivity2 {


    public static final String SP_GPT_RESULT_KEY = "SP_GPT_RESULT_KEY";
    private MTextView mCountView;


    private final InputUtils inputUtils = new InputUtils(this, 100);
    private EditText mInputText;



    public static Intent createIntent(Context context, String demo, long templateId) {
        Intent starter = new Intent(context, EditGreetingWordsActivity.class);
        starter.putExtra(Constants.DATA_STRING, demo);
        starter.putExtra(Constants.DATA_LONG, templateId);
        return starter;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_greeting_words);

        mInputText = findViewById(R.id.mInputText);
        mCountView = findViewById(R.id.mCountView);
        AppTitleView mAppTitleView = findViewById(R.id.mAppTitleView);
        mAppTitleView.setTitle("编辑招呼语");
        mAppTitleView.setBackClickListener();


        mAppTitleView.setActionButtonLeftListener("确定", new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {

                AnalyticsFactory
                        .create()
                        .action(AnalyticsAction.ACTION_GREETING_EDIT_COMPLETE_CLICK)
                        .param("p", LText.equal(getTemplateText(), mInputText.getText().toString()) ? "1" : "2")
                        .param("p2", mInputText.getText().toString())
                        .build();

                boolean inputLargerThanMaxLength = inputUtils.isInputLargerThanMaxLength(mInputText.getText().toString());
                if (inputLargerThanMaxLength) {
                    ToastUtils.showText("最多输入100字，请删除后重试");
                    return;
                }
                updateGreetingWords();
            }
        });


        mInputText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                inputUtils.checkInputCount(mCountView, s.toString());

                TextView tvBtnActionLeft = mAppTitleView.getTvBtnActionLeft();
                if (LText.empty(s.toString())) {
                    tvBtnActionLeft.setAlpha(0.5f);
                    tvBtnActionLeft.setEnabled(false);
                } else {
                    tvBtnActionLeft.setAlpha(1);
                    tvBtnActionLeft.setEnabled(true);
                }


            }
        });

        mInputText.setText(getTemplateText());

        mInputText.setSelection(mInputText.getText().length());
    }



    //更新招呼语
    private void updateGreetingWords() {
        GreetingUpdateRequest2 greetingUpdateRequest2 = new GreetingUpdateRequest2(new ApiRequestCallback<GreetingUpdate2Response>() {

            @Override
            public void onStart() {
                super.onStart();
                showProgressDialog();
            }

            @Override
            public void onSuccess(ApiData<GreetingUpdate2Response> data) {

                String value = mInputText.getText().toString();
                Intent intent = new Intent();
                intent.putExtra(Constants.DATA_STRING, value);
                setResult(Activity.RESULT_OK, intent);

                AppUtil.finishActivity(EditGreetingWordsActivity.this);
            }

            @Override
            public void onComplete() {
                dismissProgressDialog();
            }

            @Override
            public void onFailed(ErrorReason reason) {
                ToastUtils.showText(reason.getErrReason());
            }
        });
        greetingUpdateRequest2.templateId = getTemplateId();
        greetingUpdateRequest2.template = mInputText.getText().toString();
        greetingUpdateRequest2.customType = "2";
        HttpExecutor.execute(greetingUpdateRequest2);

    }

    @Nullable
    private String getTemplateText() {
        return getIntent().getStringExtra(Constants.DATA_STRING);
    }

    private long getTemplateId() {
        return getIntent().getLongExtra(Constants.DATA_LONG, 0);
    }


}