package com.hpbr.bosszhipin.module.greeting;

import com.hpbr.bosszhipin.ChatUrlConfig;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GeekEditGreetingResponse;
import net.bosszhipin.api.bean.BaseServerBean;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

import androidx.annotation.NonNull;

/**
 * create by guofeng
 * date on 2023/9/20
 */

public class GreetingGptLoopRequest extends ApiRequestCallback<GeekEditGreetingResponse> {


    private final OnGptCallBack onGptCallBack;

    public GreetingGptLoopRequest(OnGptCallBack onGptCallBack) {
        this.onGptCallBack = onGptCallBack;
    }


    public void execute() {
        /*需要调用接口生成GPT文案*/
        SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_GREETING_GET_GPT);
        get.addParam("reGenerate", 1)//是否用户主动请求重新生成 0-否 1-是
                .setRequestCallback(this).execute();
    }


    public boolean isCreating() {
        return loopRequest.pollingMaxFeq() > 0;
    }

    private final GreetingLoopRequest loopRequest = new GreetingLoopRequest();

    public void onClear() {
        loopRequest.onClear();
    }

    @Override
    public void onSuccess(ApiData<GeekEditGreetingResponse> data) {


        GeekEditGreetingResponse resp = data.resp;
        String gptGreeting = resp.suggestGreeting;
        //0 - 成功 1 - 生成中 2 - 生成失败
        int suggestStatus = resp.suggestStatus;
        //轮询延时时间
        int pollingDelaySec = resp.pollingDelaySec;
        //最大轮训次数
        int pollingMaxFeq = resp.pollingMaxFeq;


        if (suggestStatus == GeekEditGreetingResponse.GPT_IN_CREATING) {
            //生成中 轮询调用接口
            loopRequest.setDelay(pollingDelaySec);
            loopRequest.setOnGptCallBack(onGptCallBack);
            loopRequest.setPollingMaxFeq(pollingMaxFeq);
            loopRequest.execute();
        } else {
            //生成 成功|失败 返回结果
            if (onGptCallBack != null) {
                GptInfo gptInfo = createInfo(suggestStatus, gptGreeting);
                onGptCallBack.onGptCallBackListener(gptInfo);
            }
        }

    }

    @Override
    public void onComplete() {

    }

    @Override
    public void onStart() {
        super.onStart();
        if (onGptCallBack != null) {
            onGptCallBack.onGptStartCallBackListener();
        }
    }

    @Override
    public void onFailed(ErrorReason reason) {
        ToastUtils.showText(reason.toString());
        //生成失败 返回结果
        GptInfo failInfo = createInfo(2, "");
        if (onGptCallBack != null) {
            onGptCallBack.onGptCallBackListener(failInfo);
        }
    }


    public static GptInfo createInfo(int suggestStatus,
                                     String suggestGreeting) {
        GptInfo gptInfo = new GptInfo();
        gptInfo.suggestStatus = suggestStatus;
        gptInfo.suggestGreeting = suggestGreeting;
        return gptInfo;
    }

    public static class GptInfo extends BaseServerBean {

        private static final long serialVersionUID = -5718621125747910778L;
        public int suggestStatus;//0 - 成功 1 - 生成中 2 - 生成失败

        public String suggestGreeting;
    }

    public interface OnGptCallBack {

        void onGptStartCallBackListener();

        void onGptCallBackListener(@NonNull GptInfo gptInfo);
    }


} 