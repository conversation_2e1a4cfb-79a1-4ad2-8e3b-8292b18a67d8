package com.hpbr.bosszhipin.module.greeting;

import com.hpbr.bosszhipin.ChatUrlConfig;
import com.hpbr.bosszhipin.base.App;
import com.twl.http.ApiData;
import com.twl.http.error.ErrorReason;
import com.twl.ui.ToastUtils;

import net.bosszhipin.api.GeekEditGreetingResponse;
import net.bosszhipin.base.ApiRequestCallback;
import net.bosszhipin.base.SimpleApiRequest;

/**
 * create by guofeng
 * date on 2023/9/20
 */

public class GreetingLoopRequest extends ApiRequestCallback<GeekEditGreetingResponse> {


    private GreetingGptLoopRequest.OnGptCallBack onGptCallBack;

    public void setOnGptCallBack(GreetingGptLoopRequest.OnGptCallBack onGptCallBack) {
        this.onGptCallBack = onGptCallBack;
    }

    private int pollingMaxFeq;

    public void setPollingMaxFeq(int pollingMaxFeq) {
        this.pollingMaxFeq = pollingMaxFeq;
    }


    private int delay;


    public void setDelay(int delay) {
        this.delay = delay;
    }

    private boolean isDestroy;

    public void onClear() {
        isDestroy = true;
    }


    public void execute() {
        SimpleApiRequest get = SimpleApiRequest.GET(ChatUrlConfig.URL_GREETING_GET_GPT);
        get.addParam("reGenerate", "0")
                .setRequestCallback(this).execute();
    }

    public int pollingMaxFeq() {
        return pollingMaxFeq;
    }


    public static final String TAG = "GptLoopRequest";


    @Override
    public void onSuccess(ApiData<GeekEditGreetingResponse> data) {

        if (isDestroy) return;

        GeekEditGreetingResponse resp = data.resp;
        int gptStatus = resp.suggestStatus;
        /*生成成功|失败调用回掉*/
        if (gptStatus == GeekEditGreetingResponse.GPT_CREATE_SUCCESS
                || gptStatus == GeekEditGreetingResponse.GPT_FAILED) {
            GreetingGptLoopRequest.GptInfo gptInfo = GreetingGptLoopRequest.createInfo(gptStatus,
                    resp.suggestGreeting);
            if (onGptCallBack != null) {
                onGptCallBack.onGptCallBackListener(gptInfo);
            }
            pollingMaxFeq=0;
        } else {
            if (pollingMaxFeq >= 1) {
                pollingMaxFeq--;
                /*生成中继续调用接口去loading*/
                App.get().getMainHandler().postDelayed(() -> {
                    if (isDestroy) return;
                    execute();
                }, delay * 1000);
            } else {
                /*次数用完回掉失败结束*/
                GreetingGptLoopRequest.GptInfo gptInfo = GreetingGptLoopRequest.createInfo(2,
                        resp.suggestGreeting);
                if (onGptCallBack != null) {
                    onGptCallBack.onGptCallBackListener(gptInfo);
                }
            }
        }
    }

    @Override
    public void onComplete() {

    }

    @Override
    public void onFailed(ErrorReason reason) {

        ToastUtils.showText(reason.getErrReason());
        /*生成失败调用回掉*/
        GreetingGptLoopRequest.GptInfo failInfo = GreetingGptLoopRequest.createInfo(GeekEditGreetingResponse.GPT_FAILED,
                "");
        if (onGptCallBack != null) {
            onGptCallBack.onGptCallBackListener(failInfo);
        }
    }
}