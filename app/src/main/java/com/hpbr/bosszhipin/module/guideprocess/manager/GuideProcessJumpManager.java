package com.hpbr.bosszhipin.module.guideprocess.manager;

import androidx.annotation.IntDef;
import androidx.annotation.Nullable;

import com.hpbr.bosszhipin.data.manager.UserManager;
import com.monch.lbase.util.LList;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;

/**
 * @ClassName ：GuideProcessJumpManager
 * @Description ：
 * <AUTHOR> SheYi
 * @Date ：2023/4/14  4:05 PM
 */
public class GuideProcessJumpManager {


    /**
     * 根据服务端返回数据，判断应该跳转的流程类型
     *
     * @param supplementList // 1-补充工作经历，3-更新个人优势，4-学生切换身份，5-职场人补充开始工作时间，6-补充求职状态，7-补充生日，8-更新简历状态
     * @return
     */
    public static @GuideProcessType int getGuideProcessType(ArrayList<Integer> supplementList) {
        if (LList.getCount(supplementList) == 0) return GuideProcessType.GUIDE_PROCESS_TYPE_NONE;
        if (UserManager.isGeekStudent() && supplementList.contains(4)) {/*学生身份&&集合中返回了 4（学生切换身份）*/
            return GuideProcessType.GUIDE_PROCESS_TYPE_STUDENT;
        } else if (supplementList.contains(1)) {/*集合中返回了 1（补充工作经历）*/
            return GuideProcessType.GUIDE_PROCESS_TYPE_WORK_EXP;
        } else if (supplementList.contains(3)) {/*集合中返回了 3（更新个人优势）*/
            return GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR;
        } else if (isHaveFlowBackCompleteProcess(supplementList)) {
            return GuideProcessType.GUIDE_PROCESS_TYPE_FLOW_BACK_COMPLETE;
        }

        return GuideProcessType.GUIDE_PROCESS_TYPE_NONE;
    }

    /**
     * 是否有「回流」完善引导（包含：5（补充开始工作时间）||  6（补充求职状态）||  7（补充生日） || 8（更新简历状态））
     *
     * @param supplementList
     * @return
     */
    public static boolean isHaveFlowBackCompleteProcess(@Nullable ArrayList<Integer> supplementList) {
        if (supplementList == null) return false;
        if (LList.getCount(supplementList) == 0) return false;
        return supplementList.contains(FlowBackPage.PAGE_BEGIN_WORK_TIME) || supplementList.contains(FlowBackPage.PAGE_WORK_STATUS)
                || supplementList.contains(FlowBackPage.PAGE_BIRTHDAY) || supplementList.contains(FlowBackPage.PAGE_RESUME_SHOW_STATUS)
                || supplementList.contains(FlowBackPage.PAGE_PHONE_ASSISTANT);
    }


    /**
     * 获取「回流」完善引导的页面列表
     *
     * @param supplementList
     * @return
     */
    public static ArrayList<Integer> getFlowBackPageCodeList(@Nullable ArrayList<Integer> supplementList) {
        ArrayList<Integer> list = new ArrayList<>();
        if (LList.getCount(supplementList) == 0) return list;
        for (Integer pageCode : supplementList) {
            if (pageCode == FlowBackPage.PAGE_BEGIN_WORK_TIME || pageCode == FlowBackPage.PAGE_WORK_STATUS
                    || pageCode == FlowBackPage.PAGE_BIRTHDAY || pageCode == FlowBackPage.PAGE_RESUME_SHOW_STATUS
                    || pageCode == FlowBackPage.PAGE_PHONE_ASSISTANT) {
                list.add(pageCode);
            }
        }
        return list;
    }


    /**
     * 流程类型
     */
    @IntDef({GuideProcessType.GUIDE_PROCESS_TYPE_NONE, GuideProcessType.GUIDE_PROCESS_TYPE_WORK_EXP, GuideProcessType.GUIDE_PROCESS_TYPE_BLUE_COLLAR, GuideProcessType.GUIDE_PROCESS_TYPE_STUDENT,
            GuideProcessType.GUIDE_PROCESS_TYPE_FLOW_BACK_COMPLETE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface GuideProcessType {
        /*未知*/
        int GUIDE_PROCESS_TYPE_NONE = 0;
        /*「工作经历」*/
        int GUIDE_PROCESS_TYPE_WORK_EXP = 1;
        /*「蓝领结构化」个人优势引导*/
        int GUIDE_PROCESS_TYPE_BLUE_COLLAR = 2;
        /*「学生」引导*/
        int GUIDE_PROCESS_TYPE_STUDENT = 3;
        /*「回流」完善引导*/
        int GUIDE_PROCESS_TYPE_FLOW_BACK_COMPLETE = 4;
    }

    /**
     * 页面类型(这里的值不是随便定义，对应 zpgeek/cvapp/useractive/query 接口中的  extraMap -> supplementList)
     */
    @IntDef({PageType.WORK_EXP, PageType.BLUE_COLLAR, PageType.STUDENT, PageType.FILL_BEGIN_WORK_TIME,
            PageType.FILL_WORK_STATUS, PageType.FILL_BIRTHDAY, PageType.FILL_RESUME_SHOW_STATUS, PageType.FILL_PHONE_ASSISTANT})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PageType {
        /*「工作经历」*/
        int WORK_EXP = 1;
        /*「蓝领结构化」个人优势引导*/
        int BLUE_COLLAR = 3;
        /*「学生」引导*/
        int STUDENT = 4;
        /*职场人补充开始工作时间*/
        int FILL_BEGIN_WORK_TIME = 5;
        /*补充求职状态*/
        int FILL_WORK_STATUS = 6;
        /*补充生日*/
        int FILL_BIRTHDAY = 7;
        /*更新简历状态*/
        int FILL_RESUME_SHOW_STATUS = 8;
        /*电话助手*/
        int FILL_PHONE_ASSISTANT = 9;
    }

    /**
     * 回流流程的页面
     * 注：这里的值，是服务端返回的值；不能随便定义
     */
    @IntDef({FlowBackPage.PAGE_WORK_STATUS, FlowBackPage.PAGE_BIRTHDAY, FlowBackPage.PAGE_BEGIN_WORK_TIME, FlowBackPage.PAGE_RESUME_SHOW_STATUS,
            FlowBackPage.PAGE_PHONE_ASSISTANT})
    public @interface FlowBackPage {

        /*参加工作时间*/
        int PAGE_BEGIN_WORK_TIME = 5;
        /*求职状态*/
        int PAGE_WORK_STATUS = 6;
        /*出生年月*/
        int PAGE_BIRTHDAY = 7;
        /*更新简历显示状态*/
        int PAGE_RESUME_SHOW_STATUS = 8;
        /*电话助手*/
        int PAGE_PHONE_ASSISTANT = 9;

    }


}
