package com.hpbr.bosszhipin.module.imageviewer;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.ViewPager;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.ChannelConstants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.module.webview.DownLoadUtil;
import com.hpbr.bosszhipin.utils.LiveBus;
import com.hpbr.bosszhipin.utils.permission.PermissionHelper;
import com.hpbr.bosszhipin.views.BottomView;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;
import com.twl.utils.ActivityUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Locale;

/**
 * Created by zhangxiangdong on 2018/3/6.
 */
public class ImagePreviewActivity extends AppCompatActivity implements ImagePreviewFragment.Callback {

    private OverScrollReportHandler reportHandler;
    private ViewPager picturesViewPager;
    private MTextView indicatorText;
    private ArrayList<Image> pictureList;

    private BottomView bottomView;
    private View view;
    private boolean enableSaveFunc;

    private boolean enableIndicator;

    private long preEnterTimeInMillis;
    private boolean enablePoint;
    private String textFrom;
    private String contentId;
    private boolean isSwitch;

    private int selectPosition;
    private String sessionId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //region https://stackoverflow.com/a/28041425/3094830
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);

        getWindow().setStatusBarColor(Color.TRANSPARENT);

        getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS, WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        //endregion

        setContentView(R.layout.activity_picture_preview);

        //noinspection unchecked
        pictureList = (ArrayList<Image>) getIntent().getSerializableExtra(SharedConsts.KEY_PICTURE_LIST);
        ExtraParams params = (ExtraParams) getIntent().getSerializableExtra(SharedConsts.KEY_ANIMATION_PARAMS);
        enableSaveFunc = getIntent().getBooleanExtra(SharedConsts.KEY_ENABLE_SAVE_BUTTON, false);
        enableIndicator = getIntent().getBooleanExtra(SharedConsts.KEY_ENABLE_INDICATOR, false);
        enablePoint = getIntent().getBooleanExtra(SharedConsts.KEY_ENABLE_POINT, false);
        textFrom = getIntent().getStringExtra(SharedConsts.KEY_TEXT_FROM);
        contentId = getIntent().getStringExtra(SharedConsts.KEY_CONTENT_ID);
        sessionId = getIntent().getStringExtra(SharedConsts.KEY_SESSION_ID);

        picturesViewPager = findViewById(R.id.vp_pictures);
        picturesViewPager.setAdapter(new ImagesPagerAdapter(getSupportFragmentManager(), pictureList));
        final int initialIndex = params != null ? params.initialIndex : 0;
        picturesViewPager.setCurrentItem(initialIndex);
        picturesViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                triggerReportPageDuration();
                triggerRecordPageDuration();
                isSwitch = true;
                selectPosition = position;
                setCurrentIndicator(position);
                actionPost(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }

            private void actionPost(int position) {
                LiveBus.with(ChannelConstants.GALLERY_VIEWER, Integer.class).postValue(position); // 可用于所有场景下滑动时的埋点行为
            }
        });

        reportHandler = new OverScrollReportHandler(picturesViewPager);

        indicatorText = findViewById(R.id.indicatorText);

        if (enableIndicator) {
            indicatorText.setVisibility(View.VISIBLE);
        }
        setCurrentIndicator(initialIndex);

        MTextView tipsText = findViewById(R.id.tipsText);
        tipsText.setText(getIntent().getStringExtra(SharedConsts.KEY_COPYRIGHT_TIPS_TEXT), View.GONE);
    }

    private void setCurrentIndicator(int index /* 从0开始计 */) {
        if (pictureList == null || !enableIndicator) return;
        SpannableStringBuilder spannable = new SpannableStringBuilder(
                String.format(Locale.getDefault(), "%d/%d", index + 1, pictureList.size()));
        spannable.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.app_green))
                , 0, String.valueOf(index + 1).length(), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        indicatorText.setText(spannable);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        reportHandler.captureTouchEvent(ev);
        return super.dispatchTouchEvent(ev);
    }

    @Override
    protected void onResume() {
        super.onResume();
        triggerRecordPageDuration();
    }

    @Override
    protected void onPause() {
        super.onPause();
        triggerReportPageDuration();
    }

    @Override
    public void finish() {
        Intent data = new Intent();
        data.putExtra(SharedConsts.KEY_CURRENT_INDEX, picturesViewPager.getCurrentItem());
        setResult(RESULT_OK, data);
        super.finish();
    }

    public static void launch(Activity activity, Intent intent) {
        AppUtil.startActivity(activity, intent, ActivityAnimType.ALPHA_FAST);
    }

    @Override
    public void onSaveImage(@NonNull Image image) {
        if (enableSaveFunc) {
            showBottom();
        }
    }

    public void showBottom() {
        initDialog();
        if (!ActivityUtils.isValid(this)) return;
        if (bottomView != null) {
            bottomView.dismissBottomView();
            bottomView = null;
        }
        bottomView = new BottomView(this, R.style.BottomViewTheme_Defalut, view);
        bottomView.setAnimation(R.style.BottomToTopAnim);
        bottomView.showBottomView(true);
    }

    @SuppressLint("InflateParams")
    private void initDialog() {
        view = LayoutInflater.from(this).inflate(R.layout.bottom_save_image_view, null);
        view.findViewById(R.id.tv_cancel).setOnClickListener(view1 -> bottomView.dismissBottomView());
        view.findViewById(R.id.tv_save_image).setOnClickListener(view1 -> {
            bottomView.dismissBottomView();

            PermissionHelper.getStorageHelper(ImagePreviewActivity.this)
                    .setPermissionCallback((yes, permission) -> {
                        if (yes) {
                            Image image = LList.getElement(pictureList, picturesViewPager.getCurrentItem());
                            if (image != null) {
                                DownLoadUtil.downLoadImageToSdcard(image.getUrl());
                            }
                        } else {
                            T.ss("请开启磁盘读写（存储空间）权限");
                        }
                    })
                    .requestPermission();
        });
    }

    public void triggerRecordPageDuration() {
        preEnterTimeInMillis = System.currentTimeMillis();
    }

    public void triggerReportPageDuration() {
        long pageStayDuration = (System.currentTimeMillis() - preEnterTimeInMillis);
        if (!enablePoint) return;
        if (LText.empty(contentId)) return;
        if (LText.empty(textFrom)) return;
        Image item = LList.getElement(pictureList, selectPosition);
        if (item == null) return;
        String url;
        if (LText.empty(item.getTinyUrl())) {
            url = item.getUrl();
        } else {
            url = item.getTinyUrl();
        }
        DecimalFormat df = new DecimalFormat("#####0.00");
        AnalyticsFactory factory = AnalyticsFactory
                .create()
                .action(AnalyticsAction.ACTION_PICTURE_DETAIL_DETAIL)
                .param("p", textFrom)
                .param("p2", isSwitch ? 2 : 1)
                .param("p3", df.format(pageStayDuration * 1.00f / 1000))
                .param("p4", contentId)
                .param("p5", url)
                .param("p6", sessionId);
        factory.debug();
        factory.build();
    }

}
