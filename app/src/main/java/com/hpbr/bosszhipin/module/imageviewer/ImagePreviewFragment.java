package com.hpbr.bosszhipin.module.imageviewer;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.filippudak.ProgressPieView.ProgressPieView;
import com.github.piasy.biv.indicator.progresspie.ProgressPieIndicator;
import com.github.piasy.biv.view.BigImageView;
import com.github.piasy.biv.view.FrescoImageViewFactory;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.monch.lbase.util.LText;
import com.monch.lbase.widget.T;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/6/15 16:31.
 */
public class ImagePreviewFragment extends BaseFragment {

    public interface Callback {
        void onSaveImage(@NonNull Image image);
    }

    private Callback callback;

    private static final String KEY_IMAGE = "key_image";

    public static ImagePreviewFragment newInstance(Image image) {
        Bundle args = new Bundle();
        args.putSerializable(KEY_IMAGE, image);
        ImagePreviewFragment fragment = new ImagePreviewFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof Callback) {
            callback = (Callback) context;
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_picture_preview, container, false);
    }

    @Override
    public void onResume() {
        super.onResume();
        AnalyticsFactory.create().action("Preferred-pic-slid").param("p", "inner").build();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if (getArguments() == null) {
            previewFailed();
            return;
        }

        final Image image = (Image) getArguments().getSerializable(KEY_IMAGE);
        if (image == null) {
            previewFailed();
            return;
        }

        String imageUrl = image.getUrl();
        if (imageUrl == null) {
            previewFailed();
            return;
        }

        BigImageView bigImageView = view.findViewById(R.id.bigImageView);
        bigImageView.setImageViewFactory(new FrescoImageViewFactory());
        bigImageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                handleExist();
            }
        });
        bigImageView.setProgressIndicator(new ProgressPieIndicator() {
            private ProgressPieView mProgressPieView;

            @Override
            public View getView(BigImageView parent) {
                mProgressPieView = (ProgressPieView) LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.custom_ui_progress_pie_indicator, parent, false);
                return mProgressPieView;
            }

            @Override
            public void onProgress(int progress) {
                if (progress < 0 || progress > 100 || mProgressPieView == null) {
                    return;
                }
                mProgressPieView.setProgress(progress);
            }
        });

        String tinyImageUrl = image.getTinyUrl();
        try {
            if (!LText.empty(tinyImageUrl)) {
                bigImageView.showImage(Uri.parse(tinyImageUrl), Uri.parse(imageUrl));
            } else {
                bigImageView.showImage(Uri.parse(imageUrl));
            }
        } catch (Exception ignored) {

        }

        bigImageView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                if (callback != null) { // 长按保存图片
                    callback.onSaveImage(image);
                }
                return true;
            }
        });

        view.setFocusableInTouchMode(true);
        view.requestFocus();
        view.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP) {
                    handleExist();
                    return true;
                }
                return false;
            }
        });
    }

    private void previewFailed() {
        handleExist();
        T.ss("参数错误，暂无法预览");
    }

    private void handleExist() {
        finishPage();
    }

    private void finishPage() {
        AppUtil.finishActivity(activity, ActivityAnimType.ALPHA_FAST);
    }

}
