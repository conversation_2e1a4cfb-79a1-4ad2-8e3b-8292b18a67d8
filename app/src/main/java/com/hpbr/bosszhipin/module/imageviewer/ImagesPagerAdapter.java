package com.hpbr.bosszhipin.module.imageviewer;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import com.monch.lbase.util.LList;

import java.util.List;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/6/15 16:31.
 */
@SuppressWarnings("WeakerAccess")
class ImagesPagerAdapter extends FragmentPagerAdapter {

    private List<Image> pictureList;

    public ImagesPagerAdapter(@NonNull FragmentManager fm, @NonNull List<Image> pictureList) {
        super(fm);
        this.pictureList = pictureList;
    }

    @Override
    public Fragment getItem(int position) {
        return ImagePreviewFragment.newInstance(pictureList.get(position));
    }

    @Override
    public int getCount() {
        return LList.getCount(pictureList);
    }
}
