package com.hpbr.bosszhipin.module.imageviewer;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import java.util.ArrayList;

import static com.hpbr.bosszhipin.module.imageviewer.SharedConsts.*;

/**
 * Created by <PERSON><PERSON>xiang<PERSON> on 2018/6/15 16:34.
 */
@SuppressWarnings("WeakerAccess")
public final class IntentBuilder {

    @NonNull
    private final Activity sourceActivity;
    @Nullable
    private ArrayList<Image> pictureList;
    @Nullable
    private ExtraParams params;
    private boolean enableSaveButton;
    private boolean enableIndicator;
    //是否需要埋点
    private boolean enablePoint;
    //来源
    public String textFrom;
    //来源id
    public String contentId;
    //来源id
    public String sessionId;
    @Nullable
    private String copyrightTipsText;

    public IntentBuilder(@NonNull Activity sourceActivity) {
        this.sourceActivity = sourceActivity;
    }

    public static IntentBuilder with(@NonNull Activity sourceActivity) {
        return new IntentBuilder(sourceActivity);
    }

    public IntentBuilder image(@NonNull Image image) {
        if (pictureList == null) {
            pictureList = new ArrayList<>();
        }
        pictureList.add(image);
        return this;
    }

    public IntentBuilder images(@Nullable ArrayList<Image> images) {
        if (images == null) return this;

        if (this.pictureList != null) {
            this.pictureList.addAll(images);
        } else {
            this.pictureList = images;
        }

        return this;
    }

    public IntentBuilder params(@Nullable ExtraParams params) {
        this.params = params;
        return this;
    }

    public String getSessionId() {
        return sessionId;
    }

    public IntentBuilder setSessionId(String sessionId) {
        this.sessionId = sessionId;
        return this;
    }

    public IntentBuilder enableSaveButton(boolean enableSaveButton) {
        this.enableSaveButton = enableSaveButton;
        return this;
    }

    public IntentBuilder enableIndicator(boolean enableIndicator) {
        this.enableIndicator = enableIndicator;
        return this;
    }

    public IntentBuilder setCopyrightTipsText(@Nullable String copyrightTipsText) {
        this.copyrightTipsText = copyrightTipsText;
        return this;
    }

    public IntentBuilder setEnablePoint(boolean enablePoint) {
        this.enablePoint = enablePoint;
        return this;
    }


    public IntentBuilder setTextFrom(String textFrom) {
        this.textFrom = textFrom;
        return this;
    }

    public IntentBuilder setContentId(String contentId) {
        this.contentId = contentId;
        return this;
    }

    public Intent build() {
        Intent launchIntent = new Intent(sourceActivity, ImagePreviewActivity.class);
        launchIntent.putExtra(KEY_PICTURE_LIST, pictureList);
        launchIntent.putExtra(KEY_ANIMATION_PARAMS, params);
        launchIntent.putExtra(KEY_ENABLE_SAVE_BUTTON, enableSaveButton);
        launchIntent.putExtra(KEY_ENABLE_INDICATOR, enableIndicator);
        launchIntent.putExtra(KEY_COPYRIGHT_TIPS_TEXT, copyrightTipsText);
        launchIntent.putExtra(KEY_ENABLE_POINT, enablePoint);
        launchIntent.putExtra(KEY_TEXT_FROM, textFrom);
        launchIntent.putExtra(KEY_CONTENT_ID, contentId);
        launchIntent.putExtra(KEY_SESSION_ID, sessionId);
        return launchIntent;
    }

}
