package com.hpbr.bosszhipin.module.imageviewer;

import androidx.viewpager.widget.ViewPager;
import android.view.MotionEvent;

import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/6/15 16:31.
 */
@SuppressWarnings("WeakerAccess")
class OverScrollReportHandler {

    private final ViewPager viewPager;

    private float lastMotionX;
    private float lastMotionY;
    // 标识第一个页面继续向右滑动是否已上报
    private boolean isFirstPageOverScrollReported;

    public OverScrollReportHandler(ViewPager viewPager) {
        this.viewPager = viewPager;
    }

    public void captureTouchEvent(MotionEvent ev) {
        float x = ev.getX();
        float y = ev.getY();
        switch (ev.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                lastMotionX = x;
                lastMotionY = y;
                break;
            case MotionEvent.ACTION_MOVE:
                final float dx = x - lastMotionX;
                final float dy = y - lastMotionY;
                handleMotionReport(dx, dy);
                lastMotionX = x;
                lastMotionY = y;
                break;
        }
    }

    private void handleMotionReport(float dx, float dy) {
        if (dx > 0 && Math.abs(dx) > Math.abs(dy)) { // 向右滑动页面
            if (viewPager.getCurrentItem() == 0 // 已经处于第一个页面
                    && viewPager.getAdapter().getCount() > 1 // 超过一个页面
                    ) {
                if (!isFirstPageOverScrollReported) {
                    reportOverScroll();
                    isFirstPageOverScrollReported = true;
                }
            }
        }
    }

    private void reportOverScroll() {
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_SLIDE_PICTURE).build();
    }

}
