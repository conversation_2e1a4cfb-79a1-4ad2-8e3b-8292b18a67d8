package com.hpbr.bosszhipin.module.interview;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.LinearLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewComplainCardBean;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * Created by monch on 2017/6/13.
 */

public class InterviewComplainCardActivity extends BaseActivity implements View.OnClickListener {

    private InterviewComplainCardBean bean;
    private LinearLayout mainLl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        bean = (InterviewComplainCardBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
        if (bean == null) {
            finish();
            return;
        }
        setContentView(R.layout.activity_interview_complain_card);
        getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        initView(bean);
    }

    private void initView(InterviewComplainCardBean bean) {

        mainLl = findViewById(R.id.main_ll);
        MTextView title = findViewById(R.id.tv_title_text);
        MTextView subTitle = findViewById(R.id.sub_title_tv);
        SimpleDraweeView complainBg = findViewById(R.id.complain_bg);
        title.setText(bean.title);
        subTitle.setText(bean.subTitle);
        complainBg.setImageURI(bean.picUrl);

        MTextView cancelBtn = findViewById(R.id.tv_cancel);
        cancelBtn.setText(bean.cancelText);
        cancelBtn.setOnClickListener(this);

        MTextView commitBtn = findViewById(R.id.btn_commit);
        commitBtn.setText(bean.clickText);
        commitBtn.setOnClickListener(this);
        commitBtn.setTag(bean.clickUrl);

        Animation animation = AnimationUtils.loadAnimation(this, R.anim.scale_up);
        findViewById(R.id.main_ll).startAnimation(animation);
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });


        findViewById(R.id.root_view).setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_UP)
                    if (event.getX() < mainLl.getX() || event.getX() > mainLl.getX() + mainLl.getWidth() || event.getY() < mainLl.getY() || event.getY() > mainLl.getY() + mainLl.getHeight())
                        close();
                return true;
            }
        });
    }

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.tv_cancel) {
            close();

        } else if (i == R.id.btn_commit) {
            String url = (String) v.getTag();
            if (!TextUtils.isEmpty(url)) {
                new ZPManager(this, url).handler();

                close();
            }

        } else {
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            close();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void close() {
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }


}
