package com.hpbr.bosszhipin.module.interview;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCardBean;
import com.hpbr.bosszhipin.module.interview.fragment.InterviewReceiveCardFragment;
import com.hpbr.bosszhipin.module.interview.fragment.OfferReceiverCardFragment;

import java.util.Map;

/**
 * Created by monch on 2017/6/13.
 */

public class InterviewReceiveCardActivity extends BaseActivity {

    private InterviewCardBean bean;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        bean = (InterviewCardBean) getIntent().getSerializableExtra(Constants.DATA_ENTITY);
        if (bean == null) {
            bean = new InterviewCardBean();
        }
        setContentView(R.layout.activity_interview_receive_card);
        bgAction();
        initFragment();
        makeStatusBarTransparent();
    }


    private void initFragment() {
        //录用通知
        if (isOffer()) {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.root_view, OfferReceiverCardFragment.getInstance(bean))
                    .commitAllowingStateLoss();
        } else {
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.root_view, InterviewReceiveCardFragment.getInstance(bean))
                    .commitAllowingStateLoss();
        }
    }





    //是否录用通知
    private boolean isOffer() {
        return bean.source == 1;
    }



    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }


    private String getInterviewid() {
        if (bean != null) {
            if (!TextUtils.isEmpty(bean.buttonUrl)) {
                Map<String, String> params = ZPManager.UrlHandler
                        .getParams(bean.buttonUrl);
                if (!params.isEmpty() && params.containsKey("interviewid")) {
                    return params.get("interviewid");
                }
            }
        }
        return "";
    }

    //埋点
    private void bgAction() {
        if (!isOffer()) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_INTERVIEW_CARD_SHOW)
                    .param("p", String.valueOf(bean.friendId))
                    .param("p2", String.valueOf(bean.jobId))
                    .param("p3", getInterviewid())
                    .param("p4", bean.bizType)//1 普通聊天 2 道具（8.18蓝领牛炸Pro）
                    .build();
        }
    }


}
