package com.hpbr.bosszhipin.module.interview;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareActivity;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.interview.entity.InterviewComplainCardBean;
import com.hpbr.bosszhipin.module.interview.fragment.NewCollectionMedalFragment;
import com.hpbr.bosszhipin.module.interview.fragment.NewCollectionPictureFragment;
import com.hpbr.bosszhipin.module.interview.viewmodel.NewCollectionViewModel;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/08/18 18:00
 * 解锁新勋章、单图（最佳招聘者）
 */
public class NewCollectionActivity extends BaseAwareActivity<NewCollectionViewModel> {

    private InterviewComplainCardBean bean;

    public static void startActivity(Context context, InterviewComplainCardBean card) {
        Intent intent = new Intent(context, NewCollectionActivity.class);
        intent.putExtra(Constants.DATA_ENTITY, card);
        AppUtil.startActivity(context, intent, ActivityAnimType.SCALE);
    }


    @Override
    protected int contentLayout() {
        return R.layout.activity_new_collection;
    }

    @Override
    protected void onAfterCreate(Bundle savedInstanceState) {
        ImmersiveUtils.immersiveStyleBar(getThis(), false, false);
        transparentNavBar(getThis());
        getParamsBean();
        if (bean == null) {
            close();
            return;
        }
        mViewModel.setBean(bean);

        initView();
        initLiveDataObserve();
        initData();
    }


    private void initView() {

    }

    private void initLiveDataObserve() {


    }

    private void initData() {
        if (InterviewComplainCardBean.isPictureMedal(bean)) {
            initFragment(NewCollectionPictureFragment.getInstance(null));
        } else {
            initFragment(NewCollectionMedalFragment.getInstance(null));
        }
    }


    protected void initFragment(Fragment fragment) {
        if (isFinishing()) {
            return;
        }
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.ll_container, fragment);
        transaction.commitAllowingStateLoss();
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            close();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void close() {
        AppUtil.finishActivity(NewCollectionActivity.this, ActivityAnimType.SCALE);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
    }


    private void getParamsBean() {
        Intent intent = getIntent();
        if (intent != null) {
            Serializable serializableExtra = intent.getSerializableExtra(Constants.DATA_ENTITY);
            if (serializableExtra instanceof InterviewComplainCardBean) {
                bean = (InterviewComplainCardBean) serializableExtra;
            }
        }
    }

    public static void transparentNavBar(@NonNull final Activity activity) {
        transparentNavBar(activity.getWindow());
    }

    public static void transparentNavBar(final Window window) {
        if (window == null) return;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.setNavigationBarContrastEnforced(false);
        }
        window.setNavigationBarColor(Color.TRANSPARENT);
        View decorView = window.getDecorView();
        int vis = decorView.getSystemUiVisibility();
        int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
        decorView.setSystemUiVisibility(vis | option);
    }


}
