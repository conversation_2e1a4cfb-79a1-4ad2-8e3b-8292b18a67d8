package com.hpbr.bosszhipin.module.interview.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.collection.SparseArrayCompat;

import com.hpbr.bosszhipin.base.BaseFragment;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: luhao
 * Date: 2019/4/25
 */
public class CommonPagerAdapter extends FragmentPagerAdapter {

    private List<Fragment> mFragments = new ArrayList<>();
    private FragmentManager fm;

    private SparseArrayCompat<String> mFragmentPositionMap = new SparseArrayCompat<>();
    private SparseArrayCompat<String> mFragmentPositionMapAfterUpdate = new SparseArrayCompat<>();

    public CommonPagerAdapter(FragmentManager fm, List<Fragment> fragments) {
        super(fm);
        this.fm = fm;
        mFragments.clear();
        if (!LList.isEmpty(fragments)) mFragments.addAll(fragments);

        setFragmentPositionMap();
        setFragmentPositionMapForUpdate();

    }

    /**
     * 保存更新之前的位置信息，用<hashCode, position>的键值对结构来保存
     */
    private void setFragmentPositionMap() {
        mFragmentPositionMap.clear();
        for (int i = 0; i < mFragments.size(); i++) {
            mFragmentPositionMap.put(Long.valueOf(getItemId(i)).intValue(), String.valueOf(i));
        }
    }

    /**
     * 保存更新之后的位置信息，用<hashCode, position>的键值对结构来保存
     */
    private void setFragmentPositionMapForUpdate() {
        mFragmentPositionMapAfterUpdate.clear();
        for (int i = 0; i < mFragments.size(); i++) {
            mFragmentPositionMapAfterUpdate.put(Long.valueOf(getItemId(i)).intValue(), String.valueOf(i));
        }
    }

    @Override
    public int getItemPosition(@NonNull Object object) {
        int hashCode = object.hashCode();
        String position = mFragmentPositionMapAfterUpdate.get(hashCode);
        if (position == null) {
            return POSITION_NONE;
        } else {
            int size = mFragmentPositionMap.size();
            for (int i = 0; i < size; i++) {
                int key = mFragmentPositionMap.keyAt(i);
                if (key == hashCode) {
                    String index = mFragmentPositionMap.get(key);
                    if (position.equals(index)) {
                        return POSITION_UNCHANGED;
                    } else {
                        return POSITION_NONE;
                    }
                }
            }
        }
        return POSITION_UNCHANGED;
    }

    /**
     * @param position    旧Fragment的位置
     * @param newFragment 新Fragment
     */
    public void replaceFragment(int position, Fragment newFragment) {
        Fragment oldFragment = LList.getElement(mFragments, position);
        removeFragmentInternal(oldFragment);
        mFragments.set(position, newFragment);
        notifyItemChanged();
    }
    /**
     * 移除指定的Fragment
     * @param fragment 目标Fragment
     */
    public void removeFragment(BaseFragment fragment) {
        //先从List中移除
        mFragments.remove(fragment);
        //然后从Transaction移除
        removeFragmentInternal(fragment);
        //最后刷新Adapter
        notifyItemChanged();
    }
    /**
     * 移除指定位置的Fragment，同 {@link #removeFragment(BaseFragment fragment)}
     * @param position
     */
    public void removeFragment(int position) {
        Fragment fragment = LList.getElement(mFragments, position);
        if (fragment != null) {
            //然后从List中移除
            mFragments.remove(fragment);
            //先从Transaction移除
            removeFragmentInternal(fragment);
            //最后刷新Adapter
            notifyItemChanged();
        }
    }
    /**
     * 添加Fragment
     * @param fragment 目标Fragment
     */
    public void addFragment(Fragment fragment) {
        LList.addElement(mFragments, fragment);
        notifyItemChanged();
    }
    /**
     * 在指定位置插入一个Fragment
     * @param position 插入位置
     * @param fragment 目标Fragment
     */
    public void insertFragment(int position, Fragment fragment) {
        LList.addElement(mFragments, fragment, position);
        notifyItemChanged();
    }
    private void notifyItemChanged() {
        //刷新之前重新收集位置信息
        try {
            setFragmentPositionMapForUpdate();
            notifyDataSetChanged();
            setFragmentPositionMap();
        } catch (Exception e) {
//            e.printStackTrace();
        }
    }
    /**
     * 从Transaction移除Fragment
     * @param fragment 目标Fragment
     */
    private void removeFragmentInternal(Fragment fragment) {
        FragmentTransaction transaction = fm.beginTransaction();
        transaction.remove(fragment);
        transaction.commitNow();
    }

    @Override
    public Fragment getItem(int position) {
        return LList.getElement(mFragments, position);
    }

    @Override
    public long getItemId(int position) {
        return getItem(position).hashCode();
    }

    @Override
    public int getCount() {
        return LList.getCount(mFragments);
    }
}
