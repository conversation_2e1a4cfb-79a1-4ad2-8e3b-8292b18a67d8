package com.hpbr.bosszhipin.module.interview.entity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.interview.InterviewReceiveCardActivity;
import com.hpbr.bosszhipin.module.launcher.LauncherPopActivity;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 2017/6/15.
 */
@Table("InterviewCardTable")
public class InterviewCardBean extends BaseEntityAuto {

    private static final long serialVersionUID = -1;
    private static final String KEY = "InterviewCard";

    public long uid;
    public int role;
    public long friendId;

    /* 以下 是服务器 返回 的*/
    public long jobId;
    public String title;
    public String brandName;
    public String brandLogo;
    public String bossAvatar;
    public String jobName;
    public String jobSalary;
    public String buttonText;
    public String buttonUrl;
    public boolean isHunter;// 613版本增加字段，标识是否为猎头面试
    public String bizType;//埋点 1 普通聊天 2 道具（8.18蓝领牛炸Pro）
    public String bossTitle;
    public String bossName;
    public long appointmentTime;
    public long appointmentEndTime;
    public int videoInterview;
    public int meetingType;
    public int directAccept; // 1120.40 1：直接接受面试，0:不能直接接受面试

    /**
     * @since 716
     */
    public String cancelButtonText; //:取消文本的文案
    public String cancelButtonUrl;  //:取消的跳转链接

    public int source;

    public InterviewCardBean() {
    }

    public InterviewCardBean(long uid, int role, long friendId, long jobId, String title,
                             String brandName, String brandLogo, String bossAvatar, String jobName,
                             String jobSalary, String buttonText, String buttonUrl,
                             boolean isHunter, String cancelButtonText, String cancelButtonUrl) {
        this.uid = uid;
        this.role = role;
        this.friendId = friendId;
        this.jobId = jobId;
        this.title = title;
        this.brandName = brandName;
        this.brandLogo = brandLogo;
        this.bossAvatar = bossAvatar;
        this.jobName = jobName;
        this.jobSalary = jobSalary;
        this.buttonText = buttonText;
        this.buttonUrl = buttonUrl;

        this.isHunter = isHunter;

        this.cancelButtonText = cancelButtonText;
        this.cancelButtonUrl = cancelButtonUrl;
    }

    public static void handler(final InterviewCardBean bean) {
        final Context c = App.get().getContext();
        if (App.get().isForeground() && c instanceof Activity &&
                !(c instanceof WelcomeActivity) && !(c instanceof LauncherPopActivity)) {
            // App处于前台
            AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Intent intent = new Intent(c, InterviewReceiveCardActivity.class);
                    intent.putExtra(Constants.DATA_ENTITY, bean);
                    AppUtil.startActivity(c, intent, ActivityAnimType.SCALE);
                }
            }, 2000);
        } else {
            saveInterviewCard(bean);
        }
    }

    private static void saveInterviewCard(InterviewCardBean bean) {
        String json_str = SpManager.get().user().getString(KEY, "");
        List<InterviewCardBean> list = null;
        if (!TextUtils.isEmpty(json_str)) {
            list = new ArrayList<>(GsonUtils.fromJson(json_str, new TypeToken<List<InterviewCardBean>>() {
            }.getType()));
        }
        if (list == null) list = new ArrayList<>();
        list.add(bean);
        SpManager.get().user().edit().putString(KEY, GsonUtils.toJson(list)).apply();
    }

    public static void inApplication() {
        AppThreadFactory.POOL.submit(new Runnable() {
            @Override
            public void run() {
                String json_str = SpManager.get().user().getString(KEY, "");
                if (TextUtils.isEmpty(json_str)) return;
                List<InterviewCardBean> list  = GsonUtils.fromJson(json_str, new TypeToken<List<InterviewCardBean>>(){}.getType());
                if (list == null || list.isEmpty()) return;
                for (InterviewCardBean interviewCardBean : list) {
                    InterviewCardBean.handler(interviewCardBean);
                }
                SpManager.get().user().edit().remove(KEY).apply();
            }
        });
    }

}
