package com.hpbr.bosszhipin.module.interview.entity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.module.interview.InterviewComplainCardActivity;
import com.hpbr.bosszhipin.module.interview.NewCollectionActivity;
import com.hpbr.bosszhipin.module.launcher.LauncherPopActivity;
import com.hpbr.bosszhipin.module.launcher.WelcomeActivity;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import net.bosszhipin.api.bean.ServerButtonBean;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luhao on 2019/9/21.
 */
@Table("InterviewComplainCardTable")
public class InterviewComplainCardBean extends BaseEntityAuto {
    private static final String KEY = "InterviewComplainCard";

    private static final long serialVersionUID = -1;

    public long uid;
    public int role;
    public long friendId;
    public String picUrl;

    public int picHeight;
    public int picWidth;
    public String url;

    public String title;
    public String subTitle;
    public String topTitle;
    public String topSubtitle;
    public int medalMsgFlag;
    public int bzbMedalFlag;//1是付费勋章，0是普通勋章，3残障勋章，4最佳招聘官
    public int medalType;
    public String cancelText;
    public String cancelUrl;
    public String clickText;
    public String clickUrl;

    public InterviewComplainCardBean(long uid, int role, long friendId) {
        this.uid = uid;
        this.role = role;
        this.friendId = friendId;
    }

    public static InterviewComplainCardBean newInstance(long uid, int role, long friendId, @NonNull PushMessageDialogBean dialogBean) {
        InterviewComplainCardBean bean = new InterviewComplainCardBean(uid, role, friendId);
        bean.uid = uid;
        bean.role = role;
        bean.friendId = friendId;

        bean.picUrl = dialogBean.picUrl;
        bean.picHeight = dialogBean.picHeight;
        bean.picWidth = dialogBean.picWidth;
        bean.url = dialogBean.url;

        bean.title = dialogBean.title;
        bean.subTitle = dialogBean.subTitle;
        bean.topTitle = dialogBean.topTitle;
        bean.topSubtitle = dialogBean.topSubtitle;
        bean.medalMsgFlag = dialogBean.medalMsgFlag;
        bean.bzbMedalFlag = dialogBean.bzbMedalFlag;
        bean.medalType = dialogBean.medalType;
        ServerButtonBean cancelButton = LList.getElement(dialogBean.buttonList, 0);
        if (cancelButton != null) {
            bean.cancelText = cancelButton.text;
            bean.cancelUrl = cancelButton.url;
        }

        ServerButtonBean clickButton = LList.getElement(dialogBean.buttonList, 1);
        if (clickButton != null) {
            bean.clickText = clickButton.text;
            bean.clickUrl = clickButton.url;
        }
        return bean;
    }

    public static void handler(final @NonNull InterviewComplainCardBean bean) {
        final Context c = App.get().getContext();
        if (App.get().isForeground() && c instanceof Activity &&
                !(c instanceof WelcomeActivity) && !(c instanceof LauncherPopActivity)) {
            // App处于前台
            AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (bean.medalMsgFlag == 1) {
                        /**新勋章 */
                        NewCollectionActivity.startActivity(c, bean);
                    } else {
                        Intent intent = new Intent(c, InterviewComplainCardActivity.class);
                        intent.putExtra(Constants.DATA_ENTITY, bean);
                        AppUtil.startActivity(c, intent, ActivityAnimType.SCALE);
                    }

                }
            }, 2000);
        } else {
            saveInterviewComplainCard(bean);
        }
    }

    private static void saveInterviewComplainCard(@NonNull InterviewComplainCardBean bean) {
        String json_str = SpManager.get().user().getString(KEY, "");
        List<InterviewComplainCardBean> list = null;
        if (!TextUtils.isEmpty(json_str)) {
            list = new ArrayList<>(GsonUtils.fromJson(json_str, new TypeToken<List<InterviewComplainCardBean>>() {
            }.getType()));
        }
        if (list == null) list = new ArrayList<>();
        list.add(bean);
        SpManager.get().user().edit().putString(KEY, GsonUtils.toJson(list)).apply();
    }

    public static void inApplication() {
        AppThreadFactory.POOL.submit(new Runnable() {
            @Override
            public void run() {
                String json_str = SpManager.get().user().getString(KEY, "");
                if (TextUtils.isEmpty(json_str)) return;
                List<InterviewComplainCardBean> list = GsonUtils.fromJson(json_str, new TypeToken<List<InterviewComplainCardBean>>() {
                }.getType());
                if (list == null || list.isEmpty()) return;
                for (InterviewComplainCardBean interviewCardBean : list) {
                    InterviewComplainCardBean.handler(interviewCardBean);
                }
                SpManager.get().user().edit().remove(KEY).apply();
            }
        });
    }


    /**
     * 普通勋章
     */
    public static boolean isCommonMedal(InterviewComplainCardBean bean) {
        return bean != null && bean.bzbMedalFlag == 0;
    }

    /**
     * 付费勋章
     */
    public static boolean isPaidMedal(InterviewComplainCardBean bean) {
        return bean != null && bean.bzbMedalFlag == 1;
    }

    /**
     * 残障勋章
     */
    public static boolean isHandicappedMedal(InterviewComplainCardBean bean) {
        return bean != null && bean.bzbMedalFlag == 3;
    }

    /**
     * 最佳招聘官
     */
    public static boolean isPictureMedal(InterviewComplainCardBean bean) {
        return bean != null && bean.bzbMedalFlag == 4;
    }


}
