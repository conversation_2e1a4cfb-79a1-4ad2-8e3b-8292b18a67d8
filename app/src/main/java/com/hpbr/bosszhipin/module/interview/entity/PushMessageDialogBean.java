package com.hpbr.bosszhipin.module.interview.entity;

import android.app.Activity;
import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.common.app.AppThreadFactory;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.utils.AppStatusUtil;
import com.monch.lbase.util.LList;

import net.bosszhipin.api.bean.ServerButtonBean;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: zhouyou
 * Date: 2020/7/15
 */
public class PushMessageDialogBean extends BaseEntity {

    private static final long serialVersionUID = 4357827619544223204L;
    public long uid;
    public int role;
    public long friendId;
    public String picUrl;
    public int picHeight;
    public int picWidth;
    public String url;
    public String title;
    public String subTitle;
    public String topTitle;
    public String topSubtitle;
    public int medalMsgFlag;
    public int bzbMedalFlag;//1是付费勋章，0是普通勋章，3残障勋章，4最佳招聘官员
    public int medalType;
    public List<ServerButtonBean> buttonList;
    public List<String> highlightWords;

    public static PushMessageDialogBean parse(@NonNull JSONObject jsonObject) {
        PushMessageDialogBean bean = new PushMessageDialogBean();
        String picUrl = jsonObject.optString("picUrl");
        String url = jsonObject.optString("url");
        int picHeight = jsonObject.optInt("picHeight");
        int picWidth = jsonObject.optInt("picWidth");
        String title = jsonObject.optString("title");
        String subTitle = jsonObject.optString("subTitle");
        String topTitle = jsonObject.optString("topTitle");
        String topSubtitle = jsonObject.optString("topSubtitle");
        int medalMsgFlag = jsonObject.optInt("medalMsgFlag");
        int bzbMedalFlag = jsonObject.optInt("bzbMedalFlag");
        int medalType = jsonObject.optInt("medalType");
        JSONArray jaHighlightWords = jsonObject.optJSONArray("highlightWords");
        if (jaHighlightWords != null && jaHighlightWords.length() > 0) {
            bean.highlightWords = new ArrayList<>();
            int length = jaHighlightWords.length();
            for (int i = 0; i < length; i++) {
                String s = jaHighlightWords.optString(i);
                bean.highlightWords.add(s);
            }
        }

        bean.picUrl = picUrl;
        bean.url = url;
        bean.picHeight = picHeight;
        bean.picWidth = picWidth;
        bean.title = title;
        bean.subTitle = subTitle;
        bean.topTitle = topTitle;
        bean.topSubtitle = topSubtitle;
        bean.medalMsgFlag = medalMsgFlag;
        bean.bzbMedalFlag = bzbMedalFlag;
        bean.medalType = medalType;

        List<ServerButtonBean> buttonList = new ArrayList<>();
        JSONObject cancelButton = jsonObject.optJSONObject("cancelButton");
        if (cancelButton != null) {
            buttonList.add(getButton(cancelButton));
        }
        JSONObject clickButton = jsonObject.optJSONObject("clickButton");
        if (clickButton != null) {
            buttonList.add(getButton(clickButton));
        }
        bean.buttonList = buttonList;
        return bean;
    }

    private static ServerButtonBean getButton(@NonNull JSONObject joButton) {
        String text = joButton.optString("text");
        String url = joButton.optString("url");
        ServerButtonBean button = new ServerButtonBean();
        button.text = text;
        button.url = url;
        return button;
    }

    /**
     * 展示公共弹窗
     *
     * @param bean
     */
    public static void showDialog(final @NonNull PushMessageDialogBean bean) {
        if (isAppForeground()) {
            // App处于前台
            AppThreadFactory.getMainHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    Context context = App.get().getContext();
                    if (context instanceof Activity) {
                        Activity activity = (Activity) context;
                        dispatch(activity, bean);
                    }
                }
            }, 2000);
        }
    }

    private static void dispatch(@NonNull Activity activity, @NonNull PushMessageDialogBean bean) {
        SpannableStringBuilder ssb = null;
        if (!TextUtils.isEmpty(bean.subTitle)) {
            ssb = new SpannableStringBuilder(bean.subTitle);
            if (!LList.isEmpty(bean.highlightWords)) {
                for (String word : bean.highlightWords) {
                    if (TextUtils.isEmpty(word)) continue;
                    if (bean.subTitle.contains(word)) {
                        int startIndex = bean.subTitle.indexOf(word);
                        int endIndex = startIndex + word.length();
                        ssb.setSpan(new ForegroundColorSpan(ContextCompat.getColor(activity, R.color.app_green_dark)), startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                    }
                }
            }
        }
        if (LList.getCount(bean.buttonList) == 1) {
            DialogUtils.Builder builder = new DialogUtils.Builder(activity);
            builder.setSingleButton();
            builder.setTitle(bean.title);
            builder.setDesc(ssb);
            ServerButtonBean button = LList.getElement(bean.buttonList, 0);
            if (button != null) {
                builder.setPositiveAction(button.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        new ZPManager(activity, button.url).handler();
                    }
                });
            }
            DialogUtils d = builder.build();
            d.show();
        } else if (LList.getCount(bean.buttonList) == 2) {
            DialogUtils.Builder builder = new DialogUtils.Builder(activity);
            builder.setDoubleButton();
            builder.setTitle(bean.title);
            builder.setDesc(ssb);

            ServerButtonBean button0 = LList.getElement(bean.buttonList, 0);
            ServerButtonBean button1 = LList.getElement(bean.buttonList, 1);

            if (button0 != null) {
                builder.setNegativeAction(button0.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        new ZPManager(activity, button0.url).handler();
                    }
                });
            }

            if (button1 != null) {
                builder.setPositiveAction(button1.text, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        new ZPManager(activity, button1.url).handler();
                    }
                });
            }
            DialogUtils d = builder.build();
            d.show();
        }
    }

    private static boolean isAppForeground() {
        return (AppStatusUtil.getAppStatus() == 1 ||
                AppStatusUtil.isAppForegroundAndScreenOn());
    }
}
