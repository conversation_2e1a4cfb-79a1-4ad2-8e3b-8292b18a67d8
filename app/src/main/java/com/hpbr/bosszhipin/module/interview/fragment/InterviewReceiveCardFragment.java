package com.hpbr.bosszhipin.module.interview.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.event.AnalyticsAction;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCardBean;
import com.hpbr.bosszhipin.module.interview.entity.InterviewParams;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * 1012.40 调整面试邀请卡片样式
 * create by guofeng
 * date on 2020-05-14
 */
public class InterviewReceiveCardFragment extends BaseFragment {

    private InterviewCardBean interviewCardBean;

    private ConstraintLayout mParentLayout;

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        Bundle arguments = getArguments();
        if (arguments != null) {
            interviewCardBean = (InterviewCardBean) arguments.getSerializable(Constants.DATA_ENTITY);
        }
    }

    public static InterviewReceiveCardFragment getInstance(InterviewCardBean bean) {
        InterviewReceiveCardFragment instance = new InterviewReceiveCardFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_ENTITY, bean);
        instance.setArguments(bundle);
        return instance;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragmen_interview_receive_card, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }


    private void initView(View view) {
        mParentLayout = view.findViewById(R.id.cl_parent_layout);
        MTextView brandName = view.findViewById(R.id.tv_brand_name);
        MTextView typeTitle = view.findViewById(R.id.tv_invitation_title);
        SimpleDraweeView brandLogo = view.findViewById(R.id.iv_invitation_logo);
        MTextView bossView = view.findViewById(R.id.tv_boss_content);
        MTextView jobView = view.findViewById(R.id.tv_job_content);
        MTextView timeView = view.findViewById(R.id.tv_time_content);
        ZPUIRoundButton btnConfirm = view.findViewById(R.id.btn_confirm);
        TextView tvTODetailView = view.findViewById(R.id.tv_go_to_detail);
        ImageView closeView = view.findViewById(R.id.iv_close);
        brandName.setText(interviewCardBean.brandName);
        typeTitle.setText(interviewCardBean.title);
        if (!TextUtils.isEmpty(interviewCardBean.brandLogo)) {
            brandLogo.setImageURI(StringUtil.getNetworkUri(interviewCardBean.brandLogo));
        } else {
            brandLogo.setImageURI(StringUtil.getResouceUri(R.mipmap.ic_company_logo_default));
        }
        bossView.setText(String.format("%s · %s", interviewCardBean.bossName, interviewCardBean.bossTitle));
        jobView.setText(String.format("%s  %s", interviewCardBean.jobName, interviewCardBean.jobSalary));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        timeView.setText(format.format(new Date(interviewCardBean.appointmentTime)));

        if (interviewCardBean.directAccept == 1) {
            tvTODetailView.setVisibility(View.VISIBLE);
            btnConfirm.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    handleGotoDetail(1);
                }
            });
            tvTODetailView.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    handleGotoDetail(0);
                }
            });
        } else {
            tvTODetailView.setVisibility(View.INVISIBLE);
            tvTODetailView.setOnClickListener(null);

            btnConfirm.setText("立即查看");

            btnConfirm.setOnClickListener(new OnClickNoFastListener() {
                @Override
                public void onNoFastClick(View v) {
                    handleGotoDetail(0);
                }
            });
        }


        closeView.setOnClickListener(new OnClickNoFastListener() {
            @Override
            public void onNoFastClick(View v) {
                close(true);
            }
        });

        Animation animation = AnimationUtils.loadAnimation(activity, R.anim.scale_up);
        mParentLayout.startAnimation(animation);
        view.findViewById(R.id.root_view).setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_UP)
                if (event.getX() < mParentLayout.getX() || event.getX() > mParentLayout.getX() + mParentLayout.getWidth() || event.getY() < mParentLayout.getY() || event.getY() > mParentLayout.getY() + mParentLayout.getHeight())
                    close(false);
            return true;
        });
    }

    private void handleGotoDetail(int directAccept) {
        boolean offer = interviewCardBean.source == 1;
        AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_INTERVIEW_CARD)
                .param("p", String.valueOf(interviewCardBean.friendId))
                .param("p2", String.valueOf(interviewCardBean.jobId))
                .param("p3", getInterviewid())
                .param("p4", directAccept == 1 ? 2 : 1)
                .param("p5", offer ? "1" : "0")//: 0 面试弹窗 1 录用通知弹窗
                .build();

        String url = interviewCardBean.buttonUrl;
        if (!TextUtils.isEmpty(url)) {
            url = url + "&videoInterview=" + interviewCardBean.videoInterview + "&appointmentTime=" + interviewCardBean.appointmentTime + "&jumpFrom=" + InterviewParams.JUMP_FROM_INTERVIEW_RECEIVE_CARD;
            if (directAccept == 1) {
                url += "&directAccept=" + interviewCardBean.directAccept;
            }
            new ZPManager(activity, url).handler();
            close(false);
        }
    }


    private void close(boolean needAction) {
        if (needAction) {
            AnalyticsFactory.create().action(AnalyticsAction.ACTION_CHAT_INTERVIEW_CARD)
                    .param("p", String.valueOf(interviewCardBean.friendId))
                    .param("p2", String.valueOf(interviewCardBean.jobId))
                    .param("p3", getInterviewid())
                    .param("p4", 0)
                    .param("p5", 0)
                    .build();
        }
        AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
    }


    private String getInterviewid() {
        if (!TextUtils.isEmpty(interviewCardBean.buttonUrl)) {
            Map<String, String> params = ZPManager.UrlHandler
                    .getParams(interviewCardBean.buttonUrl);
            if (!params.isEmpty() && params.containsKey("interviewid")) {
                return params.get("interviewid");
            }
        }
        return "";
    }
}
