package com.hpbr.bosszhipin.module.interview.fragment;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.LottieDrawable;
import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.event.AnalyticsFactory;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewComplainCardBean;
import com.hpbr.bosszhipin.module.interview.viewmodel.NewCollectionViewModel;
import com.hpbr.bosszhipin.views.MTextView;

/**
 * <AUTHOR>
 * @date 2023/08/21 10:14
 * 新勋章
 */
public class NewCollectionMedalFragment extends BaseAwareFragment<NewCollectionViewModel> implements View.OnClickListener {


    public String defaultAsset = "lottie/lottie_new_medal.json";
    private InterviewComplainCardBean bean;//bzbMedalFlag; 1是付费勋章，0是普通勋章，3残障勋章，4最佳招聘官
    private MTextView mPageTitle;
    private int lottieStartNum = 1;

    private ConstraintLayout mClRoot;
    private SimpleDraweeView mIvMedal;
    private MTextView mTitle;
    private MTextView mSubTitle;
    private MTextView mTvYes;
    private MTextView mTvNo;

    private LottieAnimationView mLottieView;
    private LottieAnimationView mLottieView2;
    private LottieAnimationView mLottieView3;
    private CountDownTimer mTimer;


    public static NewCollectionMedalFragment getInstance(Bundle bundle) {
        NewCollectionMedalFragment fragment = new NewCollectionMedalFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        super.createViewModel(provider);
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(NewCollectionViewModel.class);
    }


    @Override
    protected int getLayoutResId() {
        return R.layout.fragment_new_collection_medal;
    }

    @Override
    protected void initViews(View view) {
        bean = mViewModel.bean;
        if (bean == null) {
            close();
            return;
        }
        initView(view);
        initData(bean);
    }


    @Override
    public void onResume() {
        super.onResume();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
    }


    private void initView(View view) {
        mClRoot = view.findViewById(R.id.cl_root_view);
        mIvMedal = view.findViewById(R.id.iv_medal);
        mPageTitle = view.findViewById(R.id.tv_page_title);
        mTitle = view.findViewById(R.id.tv_medal_title);
        mSubTitle = view.findViewById(R.id.tv_medal_sub_title);
        mTvYes = view.findViewById(R.id.tv_page_yes);
        mTvNo = view.findViewById(R.id.tv_page_no);

        mLottieView = view.findViewById(R.id.lottie_view);
        mLottieView2 = view.findViewById(R.id.lottie_view2);
        mLottieView3 = view.findViewById(R.id.lottie_view3);

        mClRoot.setOnClickListener(this);
        mTvYes.setOnClickListener(this);
        mTvNo.setOnClickListener(this);

    }

    private String getTopTitle() {
        if (bean.bzbMedalFlag == 3) {
            return bean.topTitle;
        }
        return "恭喜获得BOSS荣誉勋章";
    }

    private void initData(@NonNull InterviewComplainCardBean bean) {
        mClRoot.setBackground(activity.getDrawable(R.drawable.bg_gradient_black_vertical3));
        mPageTitle.setTextColor(ContextCompat.getColor(activity, R.color.color_FFFFFFFF));
        mPageTitle.setText(getTopTitle());

        mTvYes.setBackground(activity.getDrawable(R.drawable.selector_bg_green_circle_button));
        mTvYes.setTextColor(ContextCompat.getColor(activity, R.color.color_FFFFFFFF));

        mIvMedal.setImageURI(bean.picUrl);
        mTitle.setText(bean.title);
        mSubTitle.setText(bean.subTitle);

        mTvYes.setText(bean.clickText);
        mTvYes.setTag(bean.clickUrl);

        mTvNo.setText(bean.cancelText);
        mTvNo.setTag(bean.cancelUrl);

        mLottieView.setAnimation(defaultAsset);
        mLottieView2.setAnimation(defaultAsset);
        mLottieView3.setAnimation(defaultAsset);
        mLottieView.setRepeatMode(LottieDrawable.RESTART);
        mLottieView2.setRepeatMode(LottieDrawable.RESTART);
        mLottieView3.setRepeatMode(LottieDrawable.RESTART);
        mLottieView.setRepeatCount(-1);
        mLottieView2.setRepeatCount(-1);
        mLottieView3.setRepeatCount(-1);

        mTimer = new CountDownTimer(4500, 1500) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (lottieStartNum <= 3) {
                    switch (lottieStartNum) {
                        case 1:
                            mLottieView.playAnimation();
                            break;
                        case 2:
                            mLottieView2.playAnimation();
                            break;
                        case 3:
                            mLottieView3.playAnimation();
                            break;
                        default:
                            break;
                    }
                    lottieStartNum++;
                }

            }

            @Override
            public void onFinish() {

            }
        };
        mTimer.start();
    }


    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.tv_page_no) {
            String url = (String) v.getTag();
            if (!TextUtils.isEmpty(url)) {
                new ZPManager(activity, url).handler();
            }
            close();
        } else if (i == R.id.tv_page_yes) {
            String url = (String) v.getTag();
            if (!TextUtils.isEmpty(url)) {
                new ZPManager(activity, url).handler();
                close();
                AnalyticsFactory.create().action("biz-item-medal-light").param("p", String.valueOf(bean.medalType)).build();
            }
        } else if (i == R.id.cl_root_view) {
//            close();
        }
    }

    private void close() {
        AppUtil.finishActivity(activity, ActivityAnimType.SCALE);
    }


}
