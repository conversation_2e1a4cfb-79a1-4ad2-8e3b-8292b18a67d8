package com.hpbr.bosszhipin.module.interview.fragment;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseAwareFragment;
import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewComplainCardBean;
import com.hpbr.bosszhipin.module.interview.viewmodel.NewCollectionViewModel;
import com.hpbr.bosszhipin.utils.ClickProtectedUtil;

/**
 * <AUTHOR>
 * @date 2023/08/21 10:14
 * 新勋章-最佳招聘官
 */
public class NewCollectionPictureFragment extends BaseAwareFragment<NewCollectionViewModel> implements View.OnClickListener {


    private ConstraintLayout clRoot;
    private SimpleDraweeView sdvPic;
    private ImageView ivClose;

    private InterviewComplainCardBean bean;

    public static NewCollectionPictureFragment getInstance(Bundle bundle) {
        NewCollectionPictureFragment fragment = new NewCollectionPictureFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected void createViewModel(ViewModelProvider provider) {
        super.createViewModel(provider);
        mViewModel = new ViewModelProvider((FragmentActivity) activity).get(NewCollectionViewModel.class);
    }


    @Override
    protected int getLayoutResId() {
        return R.layout.fragment_new_collection_pic;
    }

    @Override
    protected void initViews(View view) {

        bean = mViewModel.bean;
        if (!InterviewComplainCardBean.isPictureMedal(bean)) {
            close();
            return;
        }

        initView(view);
        initEventListener();
        initLiveDataObserve();
        setData();
    }


    @Override
    public void onResume() {
        super.onResume();
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }


    private void initView(View view) {
        clRoot = view.findViewById(R.id.cl_root);
        sdvPic = view.findViewById(R.id.sdv_pic);
        ivClose = view.findViewById(R.id.iv_close);

    }


    private void initEventListener() {
        sdvPic.setOnClickListener(this);
        ivClose.setOnClickListener(this);

    }

    private void setData() {
        ConstraintSet set = new ConstraintSet();
        set.clone(clRoot);
        set.setDimensionRatio(R.id.sdv_pic, bean.picWidth > 0 && bean.picHeight > 0 ? TextUtils.concat("h,", String.valueOf(bean.picWidth), ":", String.valueOf(bean.picHeight)).toString() : "h,300:427");
        set.applyTo(clRoot);
        sdvPic.setImageURI(bean.picUrl);

    }


    @Override
    public void onClick(View v) {
        if (ClickProtectedUtil.blockShortClickEvent()) return;
        int id = v.getId();
        if (id == R.id.iv_close) {
            close();
        } else if (id == R.id.sdv_pic) {
            new ZPManager(activity, bean.url).handler();
            close();
        }
    }

    private void initLiveDataObserve() {

    }

    private void close() {
        AppUtil.finishActivity(activity, ActivityAnimType.SCALE);
    }


}
