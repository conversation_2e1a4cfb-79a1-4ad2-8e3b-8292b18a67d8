package com.hpbr.bosszhipin.module.interview.fragment;

import android.app.Activity;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseFragment;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.interview.entity.InterviewCardBean;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LText;

/**
 * create by guofeng
 * date on 2020-05-14
 */
public class OfferReceiverCardFragment extends BaseFragment implements View.OnClickListener {


    private InterviewCardBean interviewCardBean;

    @Override
    public void onAttach(Activity activity) {
        super.onAttach(activity);
        Bundle arguments = getArguments();
        if (arguments != null) {
            interviewCardBean = (InterviewCardBean) arguments.getSerializable(Constants.DATA_ENTITY);
        }
    }

    public static OfferReceiverCardFragment getInstance(InterviewCardBean bean) {
        OfferReceiverCardFragment instance = new OfferReceiverCardFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constants.DATA_ENTITY, bean);
        instance.setArguments(bundle);
        return instance;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragmen_offer_receive_card, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
    }

    private void initView(View view) {
        SimpleDraweeView mAvatar = view.findViewById(R.id.mAvatar);
        MTextView mTitle = view.findViewById(R.id.mTitle);
        MTextView mDesc = view.findViewById(R.id.mDesc);
        MTextView mCommit = view.findViewById(R.id.mCommit);
        MTextView mCancel = view.findViewById(R.id.mCancel);
        mTitle.setText(interviewCardBean.brandName);
        mDesc.setText(interviewCardBean.title);
        mAvatar.setImageURI(StringUtil.getNetworkUri(interviewCardBean.brandLogo));


        mCommit.setVisibility(LText.empty(interviewCardBean.buttonText) ? View.GONE : View.VISIBLE);
        mCommit.setOnClickListener(this);
        mCommit.setTag(interviewCardBean.buttonUrl);
        mCommit.setText(interviewCardBean.buttonText);


        mCancel.setOnClickListener(this);
        mCancel.setVisibility(LText.empty(interviewCardBean.cancelButtonText) ? View.GONE : View.VISIBLE);
        mCancel.setTag(interviewCardBean.cancelButtonUrl);
        mCancel.setText(interviewCardBean.cancelButtonText);
    }


    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.mCommit) {
            String buttonUrl = interviewCardBean.buttonUrl;
            new ZPManager(activity, buttonUrl).handler();
            AppUtil.finishActivity(activity);
        }
        if (id == R.id.mCancel) {
            String cancelButtonUrl = interviewCardBean.cancelButtonUrl;
            new ZPManager(activity, cancelButtonUrl).handler();
            AppUtil.finishActivity(activity);
        }
    }
}
