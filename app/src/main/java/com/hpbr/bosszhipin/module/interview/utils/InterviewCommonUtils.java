package com.hpbr.bosszhipin.module.interview.utils;

import android.app.Activity;
import android.content.Intent;

import com.hpbr.bosszhipin.common.app.ActivityAnimType;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.data.db.entry.ContactBean;
import com.hpbr.bosszhipin.module.company.activity.MultiAddressActivity;
import com.hpbr.bosszhipin.module.interview.entity.InterviewContactBean;
import com.hpbr.bosszhipin.module.main.entity.JobBean;
import com.hpbr.bosszhipin.module.my.activity.boss.location.SelectWorkLocationActivity;
import com.hpbr.bosszhipin.utils.CloneUtils;
import com.hpbr.bosszhipin.utils.permission.PermissionConstants;
import com.techwolf.lib.tlog.TLog;
import com.twl.ui.ToastUtils;
import com.twl.utils.ActivityUtils;

/**
 * 面试外部通用工具类
 *
 * <AUTHOR>
 * @since 2022/9/28
 */
public class InterviewCommonUtils {
    private static final String TAG = "InterviewCommonUtils";
    /**
     * 构建面试创建所需ContactBean对象
     */
    public static InterviewContactBean createContactBean(ContactBean contactBean) {
        if(contactBean == null) {
            return null;
        }
        InterviewContactBean bean = new InterviewContactBean();
        bean.setFriendId(contactBean.friendId);
        bean.setFriendSource(contactBean.friendSource);
        bean.setFriendName(contactBean.friendName);
        bean.setJobIntentId(contactBean.jobIntentId);
        bean.setJobId(contactBean.jobId);
        bean.setSecurityId(contactBean.securityId);
        return bean;
    }

    public static void selectLocation(Activity activity, JobBean jobBean, int requestCode) {
        if (ActivityUtils.isInvalid(activity)) {
            return;
        }

        if (jobBean == null) {
            ToastUtils.showText("沟通职位不在线，无法使用");
            TLog.info(TAG, "没有找到有效的职位数据");
            return;
        }

        if (jobBean.isMultiAddress()) {//职位存在多地址
            MultiAddressActivity.startForB(activity, String.valueOf(jobBean.id), String.valueOf(jobBean.locationIndex), requestCode);
        } else {
            JobBean cloneJobBean = CloneUtils.cloneObject(jobBean);//里面会修改职位的经纬度
            if (cloneJobBean != null) {
                Intent intent = SelectWorkLocationActivity.createIntent(activity, cloneJobBean, true, "确定", PermissionConstants.SCENE_CHAT_INTERVIEW_LOCATION);
                intent.putExtra("from", SelectWorkLocationActivity.SELECT_WORK_CHAT_SOURCE);
                AppUtil.startActivityForResult(activity, intent, requestCode, ActivityAnimType.UP_GLIDE);
            }
        }
    }
}
