package com.hpbr.bosszhipin.module.interview.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.interview.interfaces.OnCalenderItemClickCallBack;
import com.hpbr.bosszhipin.utils.DateUtil;
import com.hpbr.bosszhipin.views.MTextView;
import com.monch.lbase.util.LList;

import java.util.ArrayList;
import java.util.List;

public class HorizontalInterviewCalenderView extends LinearLayout {

    private RecyclerView recyclerView;
    private InterViewAdapter adapter;

    public HorizontalInterviewCalenderView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public HorizontalInterviewCalenderView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        recyclerView = new RecyclerView(getContext());

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        recyclerView.setLayoutManager(linearLayoutManager);
        addView(recyclerView, new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
    }

    public RecyclerView getRecyclerView() {
        return recyclerView;
    }

    //点击日历,通知外部刷新数据
    private OnCalenderItemClickCallBack onItemSelectCallBack;

    public void setOnItemSelectCallBack(OnCalenderItemClickCallBack onItemSelectCallBack) {
        this.onItemSelectCallBack = onItemSelectCallBack;
    }

    //设置数据源头
    public List<InfoBean> sourceData;

    public void setSourceData(List<InfoBean> sourceData) {
        this.sourceData = sourceData;
    }

    //显示控件
    public void showLayout() {

        adapter = new InterViewAdapter();
        adapter.setOnItemSelectCallBack(onItemSelectCallBack);
        adapter.addAll(sourceData);
        recyclerView.setAdapter(adapter);
    }

    public void updateCalender(int data8) {
        if (adapter == null || LList.isEmpty(sourceData)) return;

        for (int i = 0; i < sourceData.size(); i++) {
            if (data8 == sourceData.get(i).date8 && i != adapter.selectIndex) {
                adapter.selectIndex = i;
                adapter.notifyDataSetChanged();
                //LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                int offset = computePositionOffset(i);
                recyclerView.smoothScrollToPosition(offset);
            }
        }
    }

    private class InterViewAdapter extends RecyclerView.Adapter<ViewHolder> {

        private int selectIndex = -1;

        private final List<InfoBean> SOURCE_DATA = new ArrayList<>();

        private OnCalenderItemClickCallBack onItemSelectCallBack;

        private void setOnItemSelectCallBack(OnCalenderItemClickCallBack onItemSelectCallBack) {
            this.onItemSelectCallBack = onItemSelectCallBack;
        }

        public void addAll(List<InfoBean> data) {
            if (LList.isEmpty(data)) return;
            selectIndex = -1;
            SOURCE_DATA.clear();
            SOURCE_DATA.addAll(data);
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

            View convertView = LayoutInflater.from(getContext()).inflate(R.layout.view_item_interview_day, null);

            return new ViewHolder(convertView);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

            InfoBean infoBean = LList.getElement(SOURCE_DATA, position);
            if (infoBean == null) return;
            holder.weekTv.setText(String.valueOf(DateUtil.dateToWeek(String.valueOf(infoBean.date8))));
            if (DateUtil.isToday(infoBean.date8)) {
                holder.tvDay.setText("今天");
                holder.tvDay.setTextSize(12);
            } else {
                holder.tvDay.setText(String.valueOf(DateUtil.getDayFromDate8(String.valueOf(infoBean.date8))));
                holder.tvDay.setTextSize(17);
            }
            holder.status.setVisibility(infoBean.interviewCount > 0 ? VISIBLE : INVISIBLE);

            if (infoBean.interviewCount > 0) {
                holder.itemView.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (onItemSelectCallBack != null) {
                            onItemSelectCallBack.onCalenderItemClickListener(infoBean.date8);
                        }
                        selectIndex = holder.getAdapterPosition();
                        notifyDataSetChanged();
                    }
                });
                if (selectIndex == -1)
                    selectIndex = position;
            } else {
                holder.itemView.setOnClickListener(null);
            }
            holder.tvDay.setTextColor(position == selectIndex ? ContextCompat.getColor(App.get().getContext(), R.color.color_FFFFFFFF) : ContextCompat.getColor(App.get().getContext(), R.color.text_c6));
            holder.tvDay.setBackgroundResource(position == selectIndex ? R.drawable.bg_green_circle : 0);

            if (infoBean.interviewCount == 0)
                holder.tvDay.setTextColor(ContextCompat.getColor(App.get().getContext(), R.color.text_c3));
        }

        @Override
        public int getItemCount() {
            return LList.getCount(SOURCE_DATA);
        }
    }

    private static class ViewHolder extends RecyclerView.ViewHolder {

        private MTextView weekTv;
        private MTextView tvDay;
        private View status;

        public ViewHolder(View itemView) {
            super(itemView);
            weekTv = itemView.findViewById(R.id.week_day_tv);
            tvDay = itemView.findViewById(R.id.tv_day);
            status = itemView.findViewById(R.id.view_status);
        }
    }


    public static class InfoBean {

        private int date8;
        private int interviewCount;

        public InfoBean(int date, int interviewCount) {
            this.date8 = date;
            this.interviewCount = interviewCount;
        }


        public int getInterviewCount() {
            return interviewCount;
        }
    }

    public int computePositionOffset(int position) {
        int offsetPosition = 0;

        int firstPosition = recyclerView.getChildLayoutPosition(recyclerView.getChildAt(0));
        int lastPosition = recyclerView.getChildLayoutPosition(recyclerView.getChildAt(recyclerView.getChildCount() - 1));
        //获取firstPosition，lastPosition的第二种方法
//        LinearLayoutManager manager = (LinearLayoutManager) mRecyclerView.getLayoutManager();
//        int firstPosition = manager.findFirstVisibleItemPosition();
//        int lastPosition = manager.findLastVisibleItemPosition();

        //最大的position
        int maxPosition = adapter.getItemCount() - 1;
        //position的偏移量
        int offset = (lastPosition - firstPosition) / 2;

        if (firstPosition > position) { //第一种情况
            offsetPosition = position - offset;
        } else if (lastPosition < position) { //第二种情况
            offsetPosition = position + offset;
        } else { //第三种情况
            if (lastPosition - position > position - firstPosition) {//第三种情况中position在中心点偏上
                offsetPosition = position - offset;
            } else {//第三种情况中position在中心点偏下
                offsetPosition = position + offset;
            }
        }
        //偏移过的offsetPosition越界
        if (offsetPosition < 0) {
            offsetPosition = 0;
        } else if (offsetPosition > maxPosition) {
            offsetPosition = maxPosition;
        }
        return offsetPosition;
    }

}
