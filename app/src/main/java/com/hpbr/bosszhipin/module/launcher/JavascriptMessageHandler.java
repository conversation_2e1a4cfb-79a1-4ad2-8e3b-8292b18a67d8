package com.hpbr.bosszhipin.module.launcher;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.module.webview.bean.ZpPersonalizationMessage;
import com.hpbr.bosszhipin.module.webview.jsi.PostMessage;
import com.hpbr.bosszhipin.utils.PersonalizedUtils;
import com.techwolf.lib.tlog.TLog;
import com.twl.utils.GsonUtils;

import org.json.JSONException;
import org.json.JSONObject;


public class JavascriptMessageHandler {

    public static final String TAG = "JavascriptMessage";
    public static final String ACTION_SYNC_PRIVACY_PERSONALIZATION_SETTINGS = "syncPrivacyPersonalizationSettings";
    public static final String ACTION_GET_PRIVACY_PERSONALIZATION_SETTINGS = "getPrivacyPersonalizationSettings";

    static final int WHAT_POST_MESSAGE = 33;
    static final int WHAT_POST_BZ_MESSAGE = 45;
    private boolean isInterceptJsMethodCall = false;

    private WebView webView;

    private final Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            if (msg.what == WHAT_POST_BZ_MESSAGE){
                if (msg.obj instanceof String) {
                    String json = (String) msg.obj ;
                    doBZPostMessage(json);
                }
            }
            return false;
        }
    });

    protected JavascriptMessageHandler() {
    }



    protected void setWebView(WebView webView) {
        this.webView = webView;
    }

    protected void setInterceptJsMethodCall(boolean interceptJsMethodCall) {
        isInterceptJsMethodCall = interceptJsMethodCall;
    }
//
//    @JavascriptInterface
//    public void postMessage(String json) {
//        if (isInterceptJsMethodCall) return;
//        if (handler == null) return;
//        Message msg = handler.obtainMessage();
//        msg.what = WHAT_POST_MESSAGE;
//        msg.obj = json;
//        handler.sendMessage(msg);
//    }

    /**
     * 新版本 js 交互
     */
    @JavascriptInterface
    public void BZPostMessage(String json) {
        if (isInterceptJsMethodCall) return;
        if (handler == null) return;
        Message msg = handler.obtainMessage();
        msg.what =  WHAT_POST_BZ_MESSAGE;
        msg.obj = json;
        handler.sendMessage(msg);
    }

    /**
     * 处理前端传输的JSON
     *
     * @param json {@link PostMessage}
     */
    public void doBZPostMessage(@NonNull String json) {
        TLog.info("postBZMessage", "postBZMessage() called with: json = [ %s ]", json);
        try {
            ZpPersonalizationMessage postMessage = parseMessage(json);
            if (postMessage == null){
                TLog.error(TAG,"内部错误，请稍后重试：%s",json);
                return;
            }
            if (TextUtils.equals(ACTION_SYNC_PRIVACY_PERSONALIZATION_SETTINGS, postMessage.name)) {
                PersonalizedUtils.updatePersonalizedValue(postMessage.jsonVal);
            } else if (TextUtils.equals(ACTION_GET_PRIVACY_PERSONALIZATION_SETTINGS, postMessage.name)) {
                String personalizedValue = PersonalizedUtils.getPersonalizedValue();
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("jsonVal", personalizedValue);
                    loadJavascript(postMessage.callbackName, 0, "", jsonObject);
                } catch (JSONException e) {
                    loadJavascript(postMessage.callbackName, 0, "", jsonObject);
                }
            }

        } catch (Throwable e) {
            TLog.error(TAG,e,"内部错误，请稍后重试：%s",json);
        }
    }

    private ZpPersonalizationMessage parseMessage(String json){
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(json);
            String actionName = jsonObject.optString("name");
            String callbackName = jsonObject.optString("callbackName");
            JSONObject params = jsonObject.optJSONObject("params");
            ZpPersonalizationMessage postMessage = GsonUtils.fromJson(params.toString(), ZpPersonalizationMessage.class);
            if (postMessage!=null) {
                postMessage.name = actionName;
                postMessage.callbackName = callbackName;
            }
            return postMessage;
        } catch (Exception e) {
            TLog.error(TAG,e,"内部错误，请稍后重试：parseMessage = %s",json);
        }

        return null;
    }

    protected void loadJavascript(String callbackName, int code, String message, JSONObject zpData) {
        App.get().getMainHandler().post(new Runnable() {
            @Override
            public void run() {
                if (webView != null) {
                    if (zpData == null) return;
                    JSONObject object = new JSONObject();
                    try {
                        object.put("code", code);
                        object.put("message", message);
                        object.put("zpData", zpData);
                        StringBuffer jsBuffer = new StringBuffer();
                        jsBuffer.append("javascript:");
                        jsBuffer.append(callbackName);
                        jsBuffer.append("(");
                        jsBuffer.append(object.toString());
                        jsBuffer.append(")");
                        if (jsBuffer != null) {
                            TLog.info(TAG, "run() called : %s", jsBuffer.toString());
                        }
                        webView.loadUrl(jsBuffer.toString());
                    } catch (Exception ignored) {

                    }

                }
            }
        });
    }

}
