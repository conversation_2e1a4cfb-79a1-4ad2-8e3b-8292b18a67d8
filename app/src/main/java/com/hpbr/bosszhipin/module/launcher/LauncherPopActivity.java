package com.hpbr.bosszhipin.module.launcher;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.widget.FrameLayout;

import com.facebook.drawee.view.SimpleDraweeView;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.base.BaseActivity;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.launcher.entity.ScreenAdvertBean;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.bosszhipin.startup.pipeline.StartupPipeline;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.bosszhipin.utils.StringUtil;
import com.monch.lbase.util.LText;
import com.twl.ui.ToastUtils;

import zpui.lib.ui.shadow.roundwidget.ZPUIRoundButton;

/**
 * Created by zhouyou on 16-5-12.
 * 全屏广告
 */
public class LauncherPopActivity extends BaseActivity implements View.OnClickListener {

    private static final int COUNT_DOWN_SECOND = 3;
    private int mCurrentTime;

    private ScreenAdvertBean bean;
    private boolean isJump = false;

    private ZPUIRoundButton btnNext;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Intent intent = getIntent();
        bean = (ScreenAdvertBean) intent.getSerializableExtra(Constants.DATA_ENTITY);
        if (bean == null) {
            StartupPipeline.openNextPage(this, intent, null);
            return;
        }
        mCurrentTime = bean.showTime <= 0 ? COUNT_DOWN_SECOND : bean.showTime;
        setContentView(R.layout.activity_launcher_pop);

        ScreenUtil.makeActivityFullScreen(getWindow());

        SimpleDraweeView ivPopView = findViewById(R.id.iv_pop_view);
        ivPopView.setOnClickListener(this);
        FrameLayout flJumpOver = findViewById(R.id.fl_jump_over);
        flJumpOver.setOnClickListener(this);

        btnNext = findViewById(R.id.btn_next);


        ivPopView.setImageURI(StringUtil.getNetworkUri(bean.photo));
        handler.sendEmptyMessage(0);
    }

    private final Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            btnNext.setText("跳过 " + mCurrentTime-- + "s");
            if (mCurrentTime < 0) {
                if (!isJump) {
                    StartupPipeline.openNextPage(LauncherPopActivity.this, getIntent(), null);
                    isJump = true;
                }
            } else {
                handler.sendEmptyMessageDelayed(0, 1000);
            }
            return true;
        }
    });

    @Override
    public void onClick(View v) {
        int i = v.getId();
        if (i == R.id.iv_pop_view) {
            if (!LText.empty(bean.target)) {
                mCurrentTime = 0;
                handler.removeMessages(0);
                if (UserManager.isCurrentLoginStatus()) {
                    isJump = true;
                    Intent intent = new Intent(this, MainActivity.class);
                    intent.putExtra(Constants.DATA_ZP_URL, bean.target);
                    AppUtil.startActivity(this, intent, true);
                } else {
                    handler.sendEmptyMessage(0);
                    ToastUtils.showText("您需要登录才能查看"); // 测试非要加和IOS同步
                }
            }

        } else if (i == R.id.fl_jump_over) {
            mCurrentTime = 0;
            handler.removeMessages(0);
            handler.sendEmptyMessage(0);

        } else {
        }
    }
}
