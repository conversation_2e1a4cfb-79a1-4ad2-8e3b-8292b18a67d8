package com.hpbr.bosszhipin.module.launcher;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.SslErrorHandler;
import android.webkit.WebBackForwardList;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.appcompat.app.AppCompatActivity;

import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.app.AppUtil;
import com.hpbr.bosszhipin.common.dialog.DialogUtils;
import com.hpbr.bosszhipin.config.PackageConfigContants;
import com.hpbr.bosszhipin.gray.AndroidDataStarGray;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.login.util.SecurityFrameworkManager;
import com.hpbr.bosszhipin.module.webview.WebViewDownloadListener;
import com.hpbr.bosszhipin.module.webview.WebViewUrlCheck;
import com.hpbr.bosszhipin.utils.ImmersiveUtils;
import com.hpbr.bosszhipin.utils.MobileUtil;
import com.hpbr.bosszhipin.views.AppTitleView;
import com.hpbr.bosszhipin.views.OnClickNoFastListener;
import com.monch.lbase.util.LText;
import com.twl.utils.ActivityUtils;

public class ProtocolWebviewActivity extends AppCompatActivity {
    public static final String URL_KEY = "url_key";

    private final JavascriptMessageHandler javascriptMessageHandler = new JavascriptMessageHandler();
    private WebView mWebView;
    private String mCurrentUrl;
    public static void startActivity(Context context, String url) {
        Intent intent = new Intent();
        intent.setClass(context, ProtocolWebviewActivity.class);
        if (!(context instanceof Activity)) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        intent.putExtra(URL_KEY, url);
        AppUtil.startActivity(context, intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ImmersiveUtils.useWhileStatusBar(this, getWindow(), false);
        String url = getIntent().getStringExtra(URL_KEY);
        setContentView(R.layout.activity_protocol_webview);
        AppTitleView appTitleView = findViewById(R.id.title_view);
        appTitleView.setBackClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!goBack()) {
                    AppUtil.finishActivity(ProtocolWebviewActivity.this);
                }
            }
        });

        WebView webView = findViewById(R.id.webview);
        initWebview(webView, appTitleView);
        javascriptMessageHandler.setInterceptJsMethodCall(WebViewUrlCheck.isExternalUrl(url));
        webView.loadUrl(url);
        mWebView = webView;
    }

    private void initWebview(WebView webView, AppTitleView appTitleView) {
        WebSettings settings = webView.getSettings();
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setBuiltInZoomControls(true);
        settings.setTextZoom(100);
        settings.setJavaScriptEnabled(true);
        settings.setDisplayZoomControls(false);
        settings.setDomStorageEnabled(true);
        settings.setPluginState(WebSettings.PluginState.ON);

        String userAgent = settings.getUserAgentString();
        String bossUserAgent = "BossZhipin/" + MobileUtil.getVersion() + "/" + PackageConfigContants.PACKAGE_APP_ID;
        if (!LText.empty(userAgent)) {
            if (!userAgent.contains(bossUserAgent)) {
                userAgent = userAgent + " " + bossUserAgent;
            }
        } else {
            userAgent = bossUserAgent;
        }
        settings.setUserAgentString(userAgent);

        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onReceivedTitle(WebView view, String title) {
                appTitleView.setTitle(title);
            }
        });
        javascriptMessageHandler.setWebView(webView);
        webView.addJavascriptInterface(javascriptMessageHandler, "wst");
        webView.removeJavascriptInterface("searchBoxJavaBridge_");
        webView.setDownloadListener(new WebViewDownloadListener(ProtocolWebviewActivity.this));
        webView.setWebViewClient(new WebViewClientListener(this));
    }

    @Override
    protected void onDestroy() {
        if (mWebView != null) {
            try {
                mWebView.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
                mWebView.clearHistory();

                ((ViewGroup) mWebView.getParent()).removeView(mWebView);
                mWebView.destroy();
                mWebView = null;
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
        super.onDestroy();
    }

    public class WebViewClientListener extends WebViewClient {

        private final Activity activity;

        public WebViewClientListener(Activity activity) {
            this.activity = activity;
        }

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            Uri uri = request.getUrl();
            if (uri != null) {
                return load(uri.toString());
            }
            return true;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            if (LText.empty(url)) return;
            mCurrentUrl = url;
        }

        private boolean load(String url) {
            if (!TextUtils.isEmpty(url)) {
                final ZPManager manager = new ZPManager(activity, url);
                if (manager.isZPUrl() || manager.isSystemUrl()) {
                    manager.handler();
                    if (manager.needCloseWebViewAfterHandleZPUrl()) {
                        AppUtil.finishActivity(activity);
                    }
                    return true;
                }
                javascriptMessageHandler.setInterceptJsMethodCall(WebViewUrlCheck.isExternalUrl(url));
            }
            return false;
        }

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            showSSLErrorDialog(handler,view);
        }


        private void showSSLErrorDialog(SslErrorHandler sslErrorHandler, WebView webView) {
            DialogUtils.Builder builder = new DialogUtils.Builder(ProtocolWebviewActivity.this);
            builder.setDesc(DEFAULT_ERROR_TIPS)
                    .setCancelable(false)
                    .setTitleIsNullGone(true)
                    .setSingleButton()
                    .setPositiveAction("确定", new OnClickNoFastListener() {
                    @Override
                    public void onNoFastClick(View v) {
                        sslErrorHandler.cancel();
                        finish();
                    }
            });
            if (ActivityUtils.isValid(ProtocolWebviewActivity.this)) {
                builder.build().show();
            }
        }

        private static final String DEFAULT_ERROR_TIPS = "当前网络环境存在问题，无法正常加载网页，您可以按以下步骤排查解决：\n1.若当前是WIFI环境，可切换到流量环境(4G/5G)重试；\n2.若当前正使用VPN，可关闭VPN后重试；\n3.若路由器当前DNS指向陌生IP地址，可更换为可信的公共DNS后重试。";

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode != KeyEvent.KEYCODE_BACK) {
            return super.onKeyDown(keyCode, event);
        }

        if (goBack()) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    public boolean goBack() {
        WebBackForwardList list = mWebView.copyBackForwardList();
        if (list == null) {
            return false;
        }
        final int start = list.getCurrentIndex();
        int stepBack = -1;
        while (start + stepBack >= 0) {
            String hist = list.getItemAtIndex(start + stepBack).getUrl();
            boolean isAnchorUrl = false;
            if (!LText.empty(hist) && hist.endsWith("#")) {
                hist = hist.substring(0, hist.length() - 1);
                isAnchorUrl = true;
            }
            if (!LText.empty(mCurrentUrl) && mCurrentUrl.endsWith("#")) {
                isAnchorUrl = true;
                mCurrentUrl = mCurrentUrl.substring(0, mCurrentUrl.length() - 1);
            }
            if ((isAnchorUrl && LText.equal(mCurrentUrl, hist))) {
                stepBack -= 1;
            } else {
                mCurrentUrl = hist;
                break;
            }
        }
        if (mWebView.canGoBackOrForward(stepBack)) {
            mWebView.goBackOrForward(stepBack);
            return true;
        }
        return false;
    }
}
