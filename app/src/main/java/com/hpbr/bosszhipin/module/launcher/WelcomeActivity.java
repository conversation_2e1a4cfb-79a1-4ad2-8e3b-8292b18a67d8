package com.hpbr.bosszhipin.module.launcher;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.bszp.kernel.account.AccountHelper;
import com.hpbr.apm.event.ApmAnalyzer;
import com.hpbr.bosszhipin.app.R;
import com.hpbr.bosszhipin.common.AppFirstUseDialogUtil;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.event.ApmAnalyticsAction;
import com.hpbr.bosszhipin.manager.MemoryManager;
import com.hpbr.bosszhipin.manager.ZPManager;
import com.hpbr.bosszhipin.module.main.fragment.manager.BarGroupManager;
import com.hpbr.bosszhipin.push.PushSdkManager;
import com.hpbr.bosszhipin.startup.pipeline.StartupPipeline;
import com.hpbr.bosszhipin.startup.process.PrivacyDelayInitializer;
import com.hpbr.bosszhipin.utils.BuildInfoHelper;
import com.hpbr.bosszhipin.utils.ScreenUtil;
import com.hpbr.utils.platform.BuildInfoUtils;
import com.monch.lbase.util.LText;
import com.monch.lbase.util.SP;
import com.techwolf.lib.tlog.TLog;

public class WelcomeActivity extends AppCompatActivity {
    private static final String TAG = "WelcomeActivity";

    private boolean isInited = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        /**
         * 有些机型（荣耀 magic2）点击Home键，后台回到app，重启WelcomeActivity bug
         */
        if (!isTaskRoot()
                && (getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0
                && getIntent().hasCategory(Intent.CATEGORY_LAUNCHER)
                && TextUtils.equals(getIntent().getAction(), Intent.ACTION_MAIN)) {
            //结束activity
            finish();
            return;
        }

        overridePendingTransition(0, 0);
        setContentView(R.layout.activity_welcome);
//        Log.d("PatchInstall", "Patch has installed!"); // tinker验证

        ScreenUtil.makeActivityFullScreen(getWindow());

        //点击Push消息 清空本地未读数据-通知服务器清空未读
        final Intent intent = getIntent();
        if (intent != null) {
            String sFromId = intent.getStringExtra(Constants.MAIN_FROM_ID_KEY);
            String url = intent.getStringExtra(Constants.NOTIFY_STRING_URL);
            long lFriendId = LText.getLong(sFromId);
            //跳转到F3不清理未读数据
            if (lFriendId > 0 && !ZPManager.PUSH_SYSTEM_USER_CENTER.equals(url)) {
                BarGroupManager.clearNoneReadBarItemBubble(lFriendId);
            }
        }
        PushSdkManager.getInstance().firstActivityShow(this);
        UserManager.updateToken2();

        //上报可用内存
        MemoryManager.reportMemoryApm();
        showBuildInfo();

    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus && !isInited) {
            isInited = true;
            if (isShowLaunchAuthenticationPop()) {
                showAppWelcomeLaunchDialog();
            } else {
                onProcess();
            }
        }
    }

//    /**
//     * 申请权限
//     */
//    private void applyForPermissionAuthorize(@NonNull List<String> permissions) {
//        new PermissionManager(this)
//                .requestPermission(permissions.toArray(new String[permissions.size()]), new PermissionManager.OnCommonPermissionCallBack() {
//                    @Override
//                    public void onRequestPermissionsResult(boolean hasPermission, boolean shouldShowAllRequestPermissionRationale) {
//                        // 请求权限回调，更新数据
//                        onProcess();
//                    }
//                });
//    }

    /**
     * 是否展示App欢迎页启动弹框
     *
     * @return
     */
    private boolean isShowLaunchAuthenticationPop() {
        return PrivacyDelayInitializer.getInstance().isDelay();
    }

    /**
     * 展示App欢迎页启动弹框
     */
    private void showAppWelcomeLaunchDialog() {
        AppFirstUseDialogUtil util = new AppFirstUseDialogUtil(this, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                AnalyticsFactory.create().action(AnalyticsAction.ACTION_ACTIVE_LOGIN_AGREEMENT_HWPA)
//                        .param("p", 1)
//                        .build();
                PrivacyDelayInitializer.getInstance().delayInit();
                checkSpCorruption();
                onProcess();
            }
        });
        //游客模式
        util.setTouristListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TLog.info("WelcomeActivity","tourist");
                CommonConfigManager.getInstance().setTourist(true);
//                PrivacyDelayInitializer.getInstance().delayInit();
                onProcess();
            }
        });
        util.showFirstUseAppDialog();
    }

    //检查SP 是否损坏 delayInit 必须初始化之后才能上报
    private static void checkSpCorruption() {
        if (AccountHelper.getAccount() != null || !TextUtils.isEmpty(AccountHelper.getLastPhone())) {
            ApmAnalyzer.create().action(ApmAnalyticsAction.ACTION_SQ_LITE_UPDATE, ApmAnalyticsAction.TYPE_SP_CORRUPTION).p3(SP.dump()).debug().report();
        }
    }

    private void onProcess() {
        StartupPipeline pipeline = new StartupPipeline(WelcomeActivity.this);
        pipeline.start();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        return keyCode == KeyEvent.KEYCODE_BACK || super.onKeyDown(keyCode, event);
    }

    private void showBuildInfo(){
        TextView tv_build_info = findViewById(R.id.tv_build_info);
        if (tv_build_info == null) {
            dumpViewHierarchy();
            return;
        }
        if (BuildInfoUtils.isDebug()){
            tv_build_info.setVisibility(View.VISIBLE);
            tv_build_info.setText(BuildInfoHelper.getSimpleInfo(this));
        }else {
            tv_build_info.setVisibility(View.GONE);
        }
    }

    private void dumpViewHierarchy() {
        try {
            View decorView = getWindow().getDecorView();
            StringBuilder sb = new StringBuilder();
            dumpViewHierarchyInternal(decorView, sb, 0);
            String dumpInfo = sb.toString();
            TLog.error(TAG, "View hierarchy: %s", dumpInfo);
            ApmAnalyzer.create()
                    .action("action_temp", "welcome_null")
                    .p2(dumpInfo)
                    .report();
        } catch (Exception e) {
            TLog.error(TAG, e, "dumpViewHierarchy error");
        }
    }

    private void dumpViewHierarchyInternal(View view, StringBuilder sb, int depth) {
        for (int i = 0; i < depth; i++) sb.append("  ");
        sb.append(view.getClass().getSimpleName())
                .append(" ").append(view.getId() != View.NO_ID ? getResources().getResourceEntryName(view.getId()) : "no-id")
                .append("\n");

        if (view instanceof ViewGroup) {
            ViewGroup vg = (ViewGroup) view;
            for (int i = 0; i < vg.getChildCount(); i++) {
                dumpViewHierarchyInternal(vg.getChildAt(i), sb, depth + 1);
            }
        }
    }

}
