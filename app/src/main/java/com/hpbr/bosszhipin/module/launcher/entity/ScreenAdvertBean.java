package com.hpbr.bosszhipin.module.launcher.entity;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.BaseEntity;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by monch on 16/5/12.
 */
@Table("ScreenAdvert")
public class ScreenAdvertBean extends BaseEntity {

    public static final String KEY = "screenAdvert";
    private static final long serialVersionUID = -1;

    /**
     * 图片地址
     */
    public String photo;

    /**
     * 目标地址
     */
    public String target;

    /**
     * 启始时间
     */
    public long startTime;

    /**
     * 结束时间
     */
    public long endTime;

    /**
     * 优先级
     */
    public int priority;

    /**
     * 开屏广告时间
     */
    public int showTime;

    @Override
    public String toString() {
        return "ScreenAdvertBean{" +
                "photo='" + photo + '\'' +
                ", target='" + target + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", priority=" + priority +
                ", showTime=" + showTime +
                '}';
    }

    private static final Object LOCK = new Object();

    public static void clearDatabase() {
        synchronized (LOCK) {
            SpManager.get().global().edit().remove(KEY).apply();
        }
    }

    public static void saveDatabase(ScreenAdvertBean bean) {
        synchronized (LOCK) {
            String json_str = SpManager.get().global().getString(KEY, "");
            List<ScreenAdvertBean> list = null;
            if (!TextUtils.isEmpty(json_str)) {
                list = new ArrayList<>(GsonUtils.fromJson(json_str, new TypeToken<List<ScreenAdvertBean>>() {
                }.getType()));
            }
            if (list == null) list = new ArrayList<>();
            list.add(bean);
            SpManager.get().global().edit().putString(KEY, GsonUtils.toJson(list)).apply();
        }
    }

    public static List<ScreenAdvertBean> queryAllDatabase() {
        synchronized (LOCK) {
            String json_str = SpManager.get().global().getString(KEY, "");
            if (TextUtils.isEmpty(json_str)) return null;
            List<ScreenAdvertBean> advertBeans = GsonUtils.fromJson(json_str, new TypeToken<List<ScreenAdvertBean>>() {
            }.getType());
            if (LList.isEmpty(advertBeans)) {
                return advertBeans;
            }
            Collections.sort(advertBeans, new Comparator<ScreenAdvertBean>() {
                @Override
                public int compare(ScreenAdvertBean o1, ScreenAdvertBean o2) {
                    if (o2.priority == o1.priority) {
                        return (int) (o2.id - o1.id);
                    }
                    return o2.priority - o1.priority;
                }
            });
            return advertBeans;
        }
    }

}
