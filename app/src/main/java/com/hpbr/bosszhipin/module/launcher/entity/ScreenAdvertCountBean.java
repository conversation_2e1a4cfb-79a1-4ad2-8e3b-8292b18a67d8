package com.hpbr.bosszhipin.module.launcher.entity;

import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.hpbr.bosszhipin.base.BaseEntityAuto;
import com.hpbr.bosszhipin.utils.io.sp.SpManager;
import com.monch.lbase.orm.db.annotation.Table;
import com.monch.lbase.util.LList;
import com.twl.utils.GsonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by monch on 16/5/23.
 */
@Table("ScreenAdvertCount")
public class ScreenAdvertCountBean extends BaseEntityAuto {

    public static final String KEY = "screenAdvertCount";
    private static final long serialVersionUID = -1;

    public long uid;
    public long aid;
    public long startTime;
    public long endTime;
    // 展示类型，1为每日展示，0为按总次数展示
    public int showType;
    // 展示次数
    public int showCount;
    // 已经展示的次数
    public int showOffCount;
    // 展示日期，当showType为1时，此值生效
    // 此值记录当日日期，用于标记当天与showOffCount的关系
    // 当showType为0时，此值为-1
    public long showDate;

    private static final Object LOCK = new Object();

    @Override
    public String toString() {
        return "ScreenAdvertCountBean{" +
                "uid=" + uid +
                ", aid=" + aid +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", showType=" + showType +
                ", showCount=" + showCount +
                ", showOffCount=" + showOffCount +
                ", showDate=" + showDate +
                '}';
    }

    public static void clearDatabase() {
        synchronized (LOCK) {
            SpManager.get().global().edit().remove(KEY).apply();
        }
    }

    public static ScreenAdvertCountBean queryDatabase(long id) {
        synchronized (LOCK) {
            List<ScreenAdvertCountBean> screenAdvertCountBeans = queryAllDatabase();
            if (LList.isEmpty(screenAdvertCountBeans)) return null;
            for (ScreenAdvertCountBean screenAdvertCountBean : screenAdvertCountBeans) {
                if (screenAdvertCountBean != null && (screenAdvertCountBean.id == id || screenAdvertCountBean.aid == id)) {
                    return screenAdvertCountBean;
                }
            }
        }
        return null;
    }

    public static void saveDatabase(ScreenAdvertCountBean bean) {
        synchronized (LOCK) {
            String json_str = SpManager.get().global().getString(KEY, "");
            List<ScreenAdvertCountBean> list = null;
            if (!TextUtils.isEmpty(json_str)) {
                List<ScreenAdvertCountBean> screenAdvertCountBeans = GsonUtils.fromJson(json_str, new TypeToken<List<ScreenAdvertCountBean>>() {
                }.getType());
                if (screenAdvertCountBeans != null) {
                    list = new ArrayList<>(screenAdvertCountBeans);
                }
            }
            if (list == null) list = new ArrayList<>();
            int index = -1;
            for (int i = 0; i < list.size(); i++) {
                ScreenAdvertCountBean screenAdvertCountBean = list.get(i);
                if (screenAdvertCountBean != null && screenAdvertCountBean.aid == bean.aid) {
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                list.set(index, bean);
            } else {
                list.add(bean);
            }
            SpManager.get().global().edit().putString(KEY, GsonUtils.toJson(list)).apply();
        }
    }

    public static List<ScreenAdvertCountBean> queryAllDatabase() {
        synchronized (LOCK) {
            String json_str = SpManager.get().global().getString(KEY, "");
            if (TextUtils.isEmpty(json_str))  return null;
            return GsonUtils.fromJson(json_str, new TypeToken<List<ScreenAdvertCountBean>>() {
            }.getType());
        }
    }

}
