package com.hpbr.bosszhipin.module.login.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.common.JobExpectListCheckUtils;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.login.util.UserInfoUtil;
import com.hpbr.bosszhipin.module_geek_export.GeekF1Constant;
import com.monch.lbase.util.L;
import com.techwolf.lib.tlog.TLog;

import java.lang.ref.WeakReference;

/**
 * 调用地方：
 * 1、切换前台
 * 2、登录状态发生变更(主界面创建)
 **/
public class RefreshUserInfoService extends Service {

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (UserManager.isCurrentLoginStatus() && UserManager.getUID() >= 0) {
            getUserInfo();
        }
        return super.onStartCommand(intent, flags, startId);
    }

    private void getUserInfo() {
        UserInfoUtil uiu = new UserInfoUtil();
        uiu.setOnGetUserInfoCallback(new OnGetUserInfoCallbackImpl(this));
        uiu.requestUserInfo();
    }

    private void destroy() {
        stopSelf();
    }

    private static class OnGetUserInfoCallbackImpl implements UserInfoUtil.OnGetUserInfoCallback {

        @NonNull
        private final WeakReference<RefreshUserInfoService> swr;

        public OnGetUserInfoCallbackImpl(@NonNull RefreshUserInfoService refreshUserInfoService) {
            this.swr = new WeakReference<>(refreshUserInfoService);
        }

        @Override
        public void onGetUserInfoCompleteCallback() {
            L.i("获取用户数据完成");
        }

        @Override
        public void onGetUserInfoCallback(boolean success, String errorMsg) {
            if (success) {
                L.i("获取用户数据成功");

                if (!UserManager.isBossRole()) {
                    TLog.info(GeekF1Constant.SEND_EXPECT_LIST_CHANGE_BROADCAST,"RefreshUserInfoService onGetUserInfoCallback");
                    JobExpectListCheckUtils.sendExpectListChangeBroadcast();
                }
            } else {
                L.i("获取用户数据失败：" + errorMsg);
            }

            RefreshUserInfoService service = swr.get();
            if (service != null) {
                service.destroy();
            }
        }

    }

}
