package com.hpbr.bosszhipin.module.login.service;

import com.bszp.kernel.user.UserData;
import com.bszp.kernel.user.UserLifecycle;
import com.sankuai.waimai.router.annotation.RouterService;
import com.techwolf.lib.tlog.TLog;

@RouterService(interfaces = UserLifecycle.class, key = "userOtherInfo")
public class RefreshUserOtherInfoService implements UserLifecycle {
    @Override
    public void onUserDataInit(UserData userData) {
        TLog.info("UserLifecycle", "userOtherInfo onUserDataInit");
    }

    @Override
    public void onUserDataRelease() {
        TLog.info("UserLifecycle", "onUserDataRelease");
    }
}
