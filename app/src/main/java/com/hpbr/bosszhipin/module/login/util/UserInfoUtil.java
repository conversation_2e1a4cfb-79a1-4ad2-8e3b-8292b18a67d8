package com.hpbr.bosszhipin.module.login.util;

import android.content.Intent;

import com.bszp.kernel.user.UserHelper;
import com.bszp.kernel.user.UserInfoCallback;
import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.common.pub.entity.ROLE;
import com.hpbr.bosszhipin.config.Constants;
import com.hpbr.bosszhipin.config.custom.CommonConfigManager;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.hpbr.bosszhipin.module.main.activity.MainActivity;
import com.hpbr.utils.platform.Utils;
import com.twl.http.ApiData;
import com.twl.http.HttpExecutor;
import com.twl.http.error.ErrorReason;

import net.bosszhipin.api.GetUserSecurityRequest;
import net.bosszhipin.api.GetUserSecurityResponse;
import net.bosszhipin.api.UserBeanResponse;
import net.bosszhipin.base.ApiRequestCallback;

/**
 * Created by monch on 15/5/13.
 */
public class UserInfoUtil {

    public interface OnGetUserInfoCallback {
        void onGetUserInfoCompleteCallback();

        void onGetUserInfoCallback(boolean success, String errorMsg);
    }

    private OnGetUserInfoCallback callback;

    private boolean isCheckSecurityFramework = true;

    public void setCheckSecurityFramework(boolean isCheckSecurityFramework) {
        this.isCheckSecurityFramework = isCheckSecurityFramework;
    }

    public void setOnGetUserInfoCallback(OnGetUserInfoCallback callback) {
        this.callback = callback;
    }


    public void requestUserInfo() {
        requestUserInfo(false);
    }

    /**
     * @param strict true  batch#detail 抛出接口错误
     *               false  忽略 batch#detail错误 不覆盖数据
     */
    public void requestUserInfo(boolean strict) {
        CommonConfigManager.getInstance().refreshUserConfig();

        if (UserManager.getUID() < 0 && callback != null) {
            callback.onGetUserInfoCallback(false, "请登录后再获取用户信息");
            return;
        }

        UserHelper.refreshUserInfo(new UserCallback(strict));
    }

    public void requestUserOtherInfo() {
        if (UserManager.isBossRole()) {
            BossUserDetailUtils.refreshBossUserOtherInfo();
        } else {
            GeekUserDetailUtils.refreshGeekUserOtherInfo();
        }
    }

    /**
     * 刷新完成后的操作
     */
    private void refreshComplete() {
        if (!UserManager.isCurrentLoginStatus()) return;
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return;
        if (UserManager.getUserRole() == ROLE.BOSS) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return;
            sendBroadcast();
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return;
            sendBroadcast();
        }
    }

    private void sendBroadcast() {
        Intent intent = new Intent();
        intent.setAction(Constants.RECEIVER_REFRESH_USER_CENTER_ACTION);
        intent.setFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES);
        App.get().getApplicationContext().sendBroadcast(intent);
    }

    class UserCallback extends UserInfoCallback {

        boolean strict;

        public UserCallback(boolean strict) {
            this.strict = strict;
        }

        @Override
        public boolean isStrict() {
            return strict;
        }

        @Override
        public void handleInChildThread(ApiData<UserBeanResponse> data) {
//            long maxMessageId = MessageDaoFactory.getMessageDao().getLastMessageId(UserManager.getUID(), identity);
//            long maxGroupMessageId = MessageDaoFactory.getMessageDao().getLastGroupMessageId(UserManager.getUID());
//            MessageDaoFactory.saveMaxMessageId(true, maxMessageId);
//            MessageDaoFactory.saveMaxGroupMessageId(true, maxGroupMessageId);
            requestUserOtherInfo();
        }

        @Override
        public void onSuccess(ApiData<UserBeanResponse> data) {
            if (callback != null) {
                callback.onGetUserInfoCallback(true, null);
            }
            refreshComplete();
            if (isCheckSecurityFramework) {
                SecurityFrameworkManager.getInstance().check();
            }
        }

        @Override
        public void onFailed(ErrorReason reason) {
            if (callback != null) {
                callback.onGetUserInfoCallback(false, reason.getErrReason());
            }
            refreshComplete();
        }

        @Override
        public void onComplete() {
            if (callback != null) {
                callback.onGetUserInfoCompleteCallback();
            }
        }

    }

    public static final int FROM_POST = 1;
    public static final int FROM_JS_CALL = 0;
    public void refreshSecurityStatus(int from) {
        GetUserSecurityRequest request = new GetUserSecurityRequest(new ApiRequestCallback<GetUserSecurityResponse>() {
            @Override
            public void onSuccess(ApiData<GetUserSecurityResponse> data) {
                UserManager.setSecurityUrl(data.resp.securityUrl);
                if (from == FROM_POST){
                    MainActivity.gotoMain(Utils.getTopActivityOrApp());
                }else{
                    SecurityFrameworkManager.getInstance().check();
                }
            }

            @Override
            public void onComplete() {

            }

            @Override
            public void onFailed(ErrorReason reason) {

            }
        });
        HttpExecutor.execute(request);
    }


}
