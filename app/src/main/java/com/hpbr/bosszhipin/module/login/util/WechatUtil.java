package com.hpbr.bosszhipin.module.login.util;

import android.content.Context;

import androidx.annotation.NonNull;

import com.hpbr.bosszhipin.base.App;
import com.hpbr.bosszhipin.config.OtherConfig;
import com.hpbr.bosszhipin.data.manager.UserManager;
import com.hpbr.bosszhipin.module.login.entity.BossInfoBean;
import com.hpbr.bosszhipin.module.login.entity.GeekInfoBean;
import com.hpbr.bosszhipin.module.login.entity.UserBean;
import com.techwolf.lib.tlog.TLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mm.opensdk.constants.Build;
import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.twl.ui.ToastUtils;

/**
 * Created by z<PERSON>xiang<PERSON> on 2018/7/19 20:53.
 */
public class WechatUtil {
    private static final  String TAG = "WechatUtil";

    public static boolean isWeixinInstall() {
        IWXAPI api = WXAPIFactory.createWXAPI(App.getAppContext(), OtherConfig.WE_CHAT_APP_ID, true);
        api.registerApp(OtherConfig.WE_CHAT_APP_ID);
        return api.isWXAppInstalled();
    }

    public static void requestLoginWithWechat(Context context, @NonNull String tag) {
        try {
            TLog.info(TAG, "requestLoginWithWechat() called with: tag = [" + tag + "]");
            IWXAPI api = WXAPIFactory.createWXAPI(context, OtherConfig.WE_CHAT_APP_ID, true);
            api.registerApp(OtherConfig.WE_CHAT_APP_ID);

            if (!api.isWXAppInstalled()) {
                TLog.info(TAG, "WX not install");
                ToastUtils.showText("未检测到您的微信客户端");
                return;
            }

            SendAuth.Req req = new SendAuth.Req();
            req.scope = "snsapi_userinfo";
            req.state = tag;
            api.sendReq(req);
        } catch (Exception e) {
            CrashReport.postCatchedException(e.getCause());
            ToastUtils.showText("微信登录异常");
        }
    }

    public static void queryWechatBindInfo(@NonNull WechatBindInfo info) {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return;

        boolean bindWeiXin;
        String thirdUserId;
        String nickname;
        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return;

            bindWeiXin = bossInfo.bindWeiXin;
            thirdUserId = bossInfo.thirdUserId;
            nickname = bossInfo.wxNickname;
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return;

            bindWeiXin = geekInfo.bindWeiXin;
            thirdUserId = geekInfo.thirdUserId;
            nickname = geekInfo.wxNickname;
        }
        info.setBindWeiXin(bindWeiXin);
        info.setThirdUserId(thirdUserId);
        info.setNickname(nickname);
    }

    /**
     * 拉起微信客服
     * https://developers.weixin.qq.com/doc/oplatform/Mobile_App/APP_launch_wechat_customer_service.html
     *
     * @param companyId 企业id
     * @param url 客服URL
     */
    public static boolean openWechatCustomerServiceChat(Context context, String companyId, String url) {
        IWXAPI api = WXAPIFactory.createWXAPI(context, OtherConfig.WE_CHAT_APP_ID);
        if (api.getWXAppSupportAPI() >= Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {
            WXOpenCustomerServiceChat.Req req = new WXOpenCustomerServiceChat.Req();
            // 企业ID
            req.corpId = companyId;
            // 客服URL
            req.url = url;
            return api.sendReq(req);
        }
        return false;
    }

    public static void saveBindInfo(boolean bindWeiXin, String thirdUserId, String wxNickname) {
        UserBean loginUser = UserManager.getLoginUser();
        if (loginUser == null) return;

        if (UserManager.isBossRole()) {
            BossInfoBean bossInfo = loginUser.bossInfo;
            if (bossInfo == null) return;

            bossInfo.bindWeiXin = bindWeiXin;
            bossInfo.thirdUserId = thirdUserId;
            bossInfo.wxNickname = wxNickname;
        } else {
            GeekInfoBean geekInfo = loginUser.geekInfo;
            if (geekInfo == null) return;

            geekInfo.bindWeiXin = bindWeiXin;
            geekInfo.thirdUserId = thirdUserId;
            geekInfo.wxNickname = wxNickname;
        }
    }

    @SuppressWarnings("WeakerAccess")
    public static class WechatBindInfo {

        private boolean bindWeiXin; // true表示微信已绑定
        private String thirdUserId; // 微信的OpenId
        private String nickname; // 微信昵称

        public boolean isBindWeiXin() {
            return bindWeiXin;
        }

        public void setBindWeiXin(boolean bindWeiXin) {
            this.bindWeiXin = bindWeiXin;
        }

        public String getThirdUserId() {
            return thirdUserId;
        }

        public void setThirdUserId(String thirdUserId) {
            this.thirdUserId = thirdUserId;
        }

        public String getNickname() {
            return nickname;
        }

        public void setNickname(String nickname) {
            this.nickname = nickname;
        }
    }

}
